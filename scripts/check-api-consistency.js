/**
 * 接口一致性检查脚本
 * 用于检查前端和shared包中的接口定义是否一致
 */
const fs = require('fs');
const path = require('path');

// 读取shared模块的接口定义（如果存在）
const sharedTypesPath = path.resolve(__dirname, '../packages/shared/src/types/demo.ts');
const frontendTypesPath = path.resolve(__dirname, '../apps/frontend/src/pages/demo/types.ts');

console.log('开始检查API一致性...');

// 检查shared模块是否存在
if (!fs.existsSync(sharedTypesPath)) {
  console.log('shared模块类型定义不存在，跳过检查');
  console.log('提示：这是正常现象，因为项目已经移除了对shared模块的依赖');
  process.exit(0);
}

// 检查前端类型定义是否存在
if (!fs.existsSync(frontendTypesPath)) {
  console.error('前端类型定义不存在，请先创建');
  process.exit(1);
}

// 读取文件内容
const sharedTypes = fs.readFileSync(sharedTypesPath, 'utf8');
const frontendTypes = fs.readFileSync(frontendTypesPath, 'utf8');

// 提取接口字段
const extractFields = (content) => {
  const interfaces = {};
  
  // 匹配接口定义
  const interfaceRegex = /interface\s+(\w+)\s*{([^}]*)}/g;
  let match;
  
  while ((match = interfaceRegex.exec(content)) !== null) {
    const interfaceName = match[1];
    const interfaceBody = match[2];
    
    // 提取字段
    const fields = [];
    const fieldRegex = /(\w+)(\?)?:\s*([^;\n]+)/g;
    let fieldMatch;
    
    while ((fieldMatch = fieldRegex.exec(interfaceBody)) !== null) {
      fields.push({
        name: fieldMatch[1],
        optional: !!fieldMatch[2],
        type: fieldMatch[3].trim()
      });
    }
    
    interfaces[interfaceName] = fields;
  }
  
  return interfaces;
};

// 提取API路径
const extractApiPaths = (content) => {
  const apiPathsRegex = /API_PATHS\s*=\s*{([^}]*)}/;
  const match = apiPathsRegex.exec(content);
  
  if (!match) return {};
  
  const apiPathsBody = match[1];
  const paths = {};
  const pathRegex = /(\w+):\s*['"]([^'"]+)['"]/g;
  let pathMatch;
  
  while ((pathMatch = pathRegex.exec(apiPathsBody)) !== null) {
    paths[pathMatch[1]] = pathMatch[2];
  }
  
  return paths;
};

// 提取接口和API路径
const sharedInterfaces = extractFields(sharedTypes);
const frontendInterfaces = extractFields(frontendTypes);
const sharedApiPaths = extractApiPaths(sharedTypes);
const frontendApiPaths = extractApiPaths(frontendTypes);

// 检查接口一致性
let hasInconsistency = false;

// 检查接口字段
Object.keys(sharedInterfaces).forEach(interfaceName => {
  if (!frontendInterfaces[interfaceName]) {
    console.warn(`警告: 前端缺少接口 ${interfaceName}`);
    hasInconsistency = true;
    return;
  }
  
  const sharedFields = sharedInterfaces[interfaceName];
  const frontendFields = frontendInterfaces[interfaceName];
  
  // 检查字段是否缺失
  sharedFields.forEach(sharedField => {
    const frontendField = frontendFields.find(f => f.name === sharedField.name);
    
    if (!frontendField) {
      console.warn(`警告: 接口 ${interfaceName} 中前端缺少字段 ${sharedField.name}: ${sharedField.type}`);
      hasInconsistency = true;
    } else if (frontendField.type !== sharedField.type) {
      console.warn(`警告: 接口 ${interfaceName} 中字段 ${sharedField.name} 类型不一致`);
      console.warn(`  shared: ${sharedField.type}`);
      console.warn(`  frontend: ${frontendField.type}`);
      hasInconsistency = true;
    }
  });
  
  // 检查前端是否有多余字段
  frontendFields.forEach(frontendField => {
    const sharedField = sharedFields.find(f => f.name === frontendField.name);
    
    if (!sharedField) {
      console.warn(`警告: 接口 ${interfaceName} 中前端有多余字段 ${frontendField.name}: ${frontendField.type}`);
      hasInconsistency = true;
    }
  });
});

// 检查API路径
Object.keys(sharedApiPaths).forEach(pathKey => {
  if (!frontendApiPaths[pathKey]) {
    console.warn(`警告: 前端缺少API路径 ${pathKey}: ${sharedApiPaths[pathKey]}`);
    hasInconsistency = true;
  } else if (frontendApiPaths[pathKey] !== sharedApiPaths[pathKey]) {
    console.warn(`警告: API路径 ${pathKey} 不一致`);
    console.warn(`  shared: ${sharedApiPaths[pathKey]}`);
    console.warn(`  frontend: ${frontendApiPaths[pathKey]}`);
    hasInconsistency = true;
  }
});

// 检查前端是否有多余API路径
Object.keys(frontendApiPaths).forEach(pathKey => {
  if (!sharedApiPaths[pathKey]) {
    console.log(`信息: 前端有额外API路径 ${pathKey}: ${frontendApiPaths[pathKey]}`);
    // 这不是错误，只是信息提示，不设置hasInconsistency标志
  }
});

if (hasInconsistency) {
  console.warn('API一致性检查发现问题，请检查上述警告');
} else {
  console.log('API一致性检查通过，前端和shared包中的接口定义一致');
}

// 退出码为0，不阻止构建流程
process.exit(0); 