你在 Vue3 和 TypeScript 方面拥有丰富的专业知识，并对这些技术的最佳实践和优化有着深入的理解。

你在 Vue3、TypeScript 和 Java Spring Boot 方面拥有丰富的专业知识，并对这些技术的最佳实践和优化有着深入的理解。

## 工程介绍
- 前端：pnpm+vite+vue3+TypeScript+elementPlus
- 后端：Spring Boot+MDP框架（美团内部框架）
- 前端pages目录下是各个页面的入口，src/pages 下面是页面入口对应的组件代码
- 每个页面可以有自己的路由，前端路由使用浏览器hash模式
- 项目是一个前后端分离的系统，作为履约平台技术部AI相关的门户平台型网站
- 前端采用多页面应用（MPA）架构，每个模块既可以作为主应用的一部分，也可以作为独立页面访问

## 代码结构简介

```
banma_business_oms/
├── apps/                    # 应用目录
│   ├── frontend/            # 前端应用
│   │   ├── src/             # 前端源代码
│   │   │   ├── assets/      # 静态资源
│   │   │   │   ├── images/  # 图片资源
│   │   │   │   └── styles/  # 样式文件
│   │   │   ├── pages/       # 功能页面（页面隔离）
│   │   │   │   ├── home/    # 首页模块
│   │   │   │   │   ├── views/    # 模块视图
│   │   │   │   │   ├── router.ts # 模块路由
│   │   │   │   │   └── entry.ts  # 模块入口
│   │   │   │   └── demo/    # 示例模块
│   │   │   │       ├── views/    # 模块视图
│   │   │   │       ├── request/  # 模块API请求
│   │   │   │       ├── router.ts # 模块路由
│   │   │   │       ├── types.ts  # 模块类型定义
│   │   │   │       └── entry.ts  # 模块入口
│   │   │   ├── shared/      # 共享资源
│   │   │   │   ├── components/ # 共享组件
│   │   │   │   │   ├── layout/  # 布局组件
│   │   │   │   │   └── NotFound.vue # 404页面组件
│   │   │   │   ├── constants/ # 常量定义
│   │   │   │   ├── hooks/     # 自定义钩子
│   │   │   │   └── utils/     # 工具函数
│   │   │   │       ├── httpRequest.ts # HTTP请求工具
│   │   │   │       └── page-entry.ts  # 页面入口工具
│   │   │   ├── router/      # 主路由配置
│   │   │   ├── types/       # 类型定义
│   │   │   ├── App.vue      # 主应用组件
│   │   │   └── main.ts      # 入口文件
│   │   ├── pages/           # 页面入口
│   │   │   ├── home/        # 首页独立HTML
│   │   │   └── demo/        # 示例页面独立HTML
│   │   ├── mock/            # 模拟数据
│   │   ├── index.html       # 主应用HTML入口
│   │   ├── vite.config.ts   # Vite 配置
│   │   └── ...              # 其他前端资源
│   └── backend/             # 后端应用
│       └── ...              # 后端资源
├── packages/                # 共享包
│   └── ...                  # 共享库和工具
└── ...                      # 配置文件
```

### 上下文

```
- 前端路径: `apps/frontend/src/{{module}}/views/{{name}}.vue`
- 后端路径: `apps/backend/src/main/java/com/sankuai/meituan/banma/business/oms/controller{{name}}Controller.java`
- OpenAPI（弱依赖）: `packages/shared/src/openapi/*,packages/shared/src/types/*`
```

### 详细代码文档介绍请参考
- [前端文档](./apps/frontend/README.md)
- [后端文档](./apps/backend/README.md)

## 后端代码规范及注意事项

### 代码风格
- 使用标准的Java代码风格，遵循Spring Boot最佳实践
- 控制器类使用@RestController和@RequestMapping注解
- 服务类使用@Service注解
- 使用CommonResult封装API响应
- 方法和类添加适当的Javadoc注释

### 命名规范
- 类名使用PascalCase（如DemoController）
- 方法名使用camelCase（如queryScenePage）
- 变量名使用camelCase（如sceneName）
- 常量使用UPPER_SNAKE_CASE（如MOCK_SCENE_DATA）

### API设计
- RESTful API设计，使用适当的HTTP方法（GET、POST等）
- API路径前缀为/api/businessoms/
- 使用@RequestBody接收JSON请求体
- 使用@RequestParam接收查询参数
- 返回CommonResult包装的响应

### 模块化设计
- 每个功能模块应有自己的控制器、服务和数据访问层
- 模块应遵循单一职责原则，确保代码的内聚性
- 相关的功能应组织在同一个包下，保持目录结构清晰
- 新增模块时，应在适当的包下创建完整的目录结构
- 模块API应有统一的URL前缀，如`/api/businessoms/{module}/`
- 跨模块调用优先通过服务层接口进行，避免直接依赖实现类

### 服务层(Service)设计规范
- 一般服务接口命名格式：`{模块名}Service`，如`UserService`
- 不要使用service接口+Impl模式，直接使用@service标注`{模块名}Service`为实现类
- 服务层只负责业务逻辑编排，不直接调用外部系统接口，而是通过适配器层进行调用
- 服务方法应处理业务异常，并转换为适当的业务错误码

### 适配器层(Adaptor)设计规范
- 适配器层负责封装所有与外部系统、接口、服务的调用
- 服务层中涉及到调用外部接口/方法/调用等，都必须封装到适配器层中
- 适配器类命名格式：`{模块名}{功能}Adaptor`，如`UserAPIAdaptor`，且该类即为实现类，不要使用接口+impl模式
- 适配器方法应处理所有外部调用的异常，并转换为系统内部可处理的异常
- 适配器方法应当做好参数校验、结果转换和日志记录

### 数据处理
- 使用Stream API进行集合操作
- 适当处理空值和异常情况
- 使用适当的数据类型和转换

## 前端代码规范及注意事项

### Code Style and Structure
- 编写干净、可维护且技术准确性高的 Vue3 代码。
- 使用 TypeScript 编写代码，尽量使用一些常见的语法，降低代码的复杂性。
- 优先使用迭代和模块化来避免代码重复。
- 为函数添加注释，注释应该解释为什么而不是如何。
- 请默认使用element-plus（for vue3）组件库，除非用户指定了其他组件。
- 访问对象和数组时，要优先使用可选链做容错，尽量使用解构赋值，减少代码的复杂性。
- 尽量使用函数式编程，减少副作用。
- 组件名使用PascalCase命名
- 变量和函数使用camelCase命名
- 常量使用UPPER_SNAKE_CASE命名
- 路由组件使用异步加载
- 大型组件拆分为小组件
- 使用`computed`和`watch`优化计算和副作用
- 避免不必要的组件重渲染

### 模块化设计规范
- 每个功能模块应有独立的目录结构，包含视图、路由、类型定义和入口文件
- 建议：模块内组件应遵循统一的命名规范，使用模块名作为前缀，如`UserList.vue`, `UserDetail.vue`。若模块名过长，则使用缩写；若确定不会重复，也可不使用模块名前缀
- 模块路由应使用统一的URL前缀，如`/user`，并在子路由中继续保持层级清晰
- 模块内部状态应封装在当前模块内，避免直接依赖其他模块
- 模块API请求应集中管理，放在模块下的`request`目录中
- 跨模块共享的组件、工具和类型定义应放在`shared`目录下
- 新增模块时，应参考现有模块的结构，保持项目整体风格一致

### 前端技术栈
- 核心框架：Vue 3.5.13
- 开发语言：TypeScript 4.8.4
- UI组件库：Element Plus 2.9.5
- 构建工具：Vite 5.1.4
- 包管理器：pnpm 9.15.1
- 路由管理：Vue Router 4.5.0
- 样式处理：SCSS 1.85.1
- CSS框架：Tailwind CSS 3.4.17
- HTTP客户端：Axios 1.7.9

### 多页面应用架构特性
- 主应用和独立页面共享同一套代码，但有不同的入口
- 独立页面不包含主应用的布局和菜单
- 独立页面可以有自己的路由配置和样式
- 每个功能模块（如 home、demo）都是独立的，包含自己的组件、路由和视图
- 模块之间相互隔离，便于将来迁移
- 所有模块共用一套页面框架（菜单、导航等）
- 共享组件、工具函数、钩子和常量放在 shared 目录下

### element-plus（for vue3） Specifics
- element-plus组件库已经全局引入，不要在页面内单独引入组件，直接使用即可
- 图标组件也已全局注册，可直接使用

### CSS Specifics
- 在文件中编写样式，不要将样式分离到 css 文件中, 要使用'scss'来编写样式代码。
- 不要在样式中使用"/deep/"。
- 文字类型按钮 <el-button type='primary'>
- el-form 不需要设置默认label-width
- 给<el-button>设置样式时，必须定义新的class类名，然后再设置样式
- 按钮间保持一定间距
- <el-table-column> 默认不设置width属性
- 使用 `@use` 替代已弃用的 `@import`
- 优先使用CSS变量实现主题和样式复用
- 组件样式使用`scoped`属性隔离
- 全局样式放在`src/assets/styles`目录下

### Naming Conventions
- 使用**camelCase**来命名组件文件（例如，components/myComponent.vue）。
- 使用描述性的变量名（例如，`isLoading`, `hasError`）。
- 使用camelCase来命名变量和函数（例如，`isFetchingData`, `handleUserInput`）。

### Fetching Data
- 接口请求请使用工程内置的@utils/httpRequest.ts文件内提供的方法，不要使用axios。
- 所有本项目后端API请求路径都应使用 `/api/businessoms/` 前缀，其他后端请求`/api/xxx/`
- 例如：`/api/businessoms/user/list`、`/api/aiassistant/demo/create`
- 引用方法如下:

```
import httpRequest from '@src/utils/httpRequest'
```

- httpRequest内存在的方法有：

```
rawRequestGet
rawRequestPostAsQuery
rawRequestPostAsJson
rawRequestPostAsForm
```

- 调用案例：
- 请使用文件内的提供的方法，不要自己创造不存在的方法。
- 方法返回数据说明

```
1. 返回的数据就是接口的原始数据
2. 返回的都是json类型数据
3. 返回数据统一格式为
{
   code: number,
   message: string,
   data: any
}
当code为0时，表示请求成功，data为业务数据
当code为非0时，表示请求失败，message为错误信息
```

### 如何开发新页面

> **重要提示**：在开发新页面时，必须严格遵循项目的前端代码结构规范。确保新增文件放在正确的目录位置，保持一致的命名风格，并按照项目的模块化设计原则组织代码。新开发的页面或组件应当与现有代码保持一致的风格、结构和实现方式，以确保项目的整体一致性和可维护性。

#### 新增模块（独立功能模块）
1. 在 `src/pages` 下创建新的页面目录，例如 `user`
2. 在页面目录下创建目录结构：
   ```
   src/pages/user/
   ├── views/        # 模块视图组件
   │   └── index.vue # 模块主视图
   ├── router.ts     # 模块路由配置
   ├── types.ts      # 模块类型定义（可选）
   └── entry.ts      # 模块独立入口
   ```
3. 创建视图组件 `views/index.vue`
4. 创建路由配置 `router.ts`，定义该模块的路由：
   ```typescript
   import { RouteRecordRaw } from 'vue-router';

   const userRoutes: Array<RouteRecordRaw> = [
     {
       path: '/user',
       name: 'User',
       component: () => import('./views/index.vue'),
       meta: {
         title: '用户管理',
         icon: 'user'
       }
     }
   ];

   export default userRoutes;
   ```
5. 在 `src/router/index.ts` 中导入并注册模块路由
6. 添加菜单项
7. 如果需要将模块作为独立页面访问：
   - 在 `pages` 目录下创建HTML入口文件：
     ```
     pages/user/
     └── index.html  # 独立页面HTML入口
     ```
   - 创建入口文件 `src/pages/user/entry.ts`：
     ```typescript
     import { createPageApp } from '../../shared/utils/page-entry';
     import router from './router';

     createPageApp(router);
     ```

#### 在现有模块新增页面
1. 确定要新增页面的所属模块，例如在 `demo` 模块中添加新页面
2. 在模块的 `views` 目录下创建新页面组件，例如 `detail.vue`：
   ```
   src/pages/demo/views/
   ├── index.vue   # 已有页面
   └── detail.vue  # 新增页面
   ```
3. 在该模块的 `router.ts` 中添加新页面的路由配置：
   ```typescript
   const demoRoutes: Array<RouteRecordRaw> = [
     {
       path: '/demo',
       name: 'Demo',
       component: () => import('./views/index.vue'),
       meta: { title: '示例页面' }
     },
     {
       path: '/demo/detail',
       name: 'DemoDetail',
       component: () => import('./views/detail.vue'),
       meta: { title: '示例详情' }
     }
   ];
   ```
4. 不需要修改主路由配置，因为模块路由已经在主路由中注册
5. 如需在菜单中显示新页面，则更新菜单配置
6. 如需从已有页面跳转到新页面，使用路由导航：
   ```vue
   <template>
     <router-link to="/demo/detail">查看详情</router-link>
     <!-- 或者使用编程式导航 -->
     <el-button @click="$router.push('/demo/detail')">查看详情</el-button>
   </template>
   ```

### 多页面应用架构特性与使用注意事项
- 同一模块内的页面共享模块级别的状态和工具
- 不同模块间应避免直接依赖，通过共享库或事件通信
- 独立页面访问时不会加载主应用布局，需单独处理导航和样式
- 独立页面和主应用中的模块可以共享同一套业务逻辑
- 每个模块独立编译，提高了构建效率和代码隔离性
- 在开发环境下，访问独立页面的URL格式为：`http://oms.banma.dev.sankuai.com:3000/pages/{module}/`
- 在开发独立页面时，需要同时考虑在主应用中的集成和独立访问两种场景


记住！
- 不要修改现有实现，除非我在查询中明确要求您这样做
- 我没有提供的东西不要自己臆想，比如接口url
- 保持良好的代码风格，增加或修改代码时，要考虑代码的复用性，尽量减少重复的代码，提高代码的复用率
- 增加或修改代码时考虑代码的健壮性、可维护性、可读性，遇到复杂低效的代码时，可以进行适当的有必要的重构以保持代码简洁高效