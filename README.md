# 履约平台技术部AI门户 (banma_business_oms)

## 项目简介

履约平台技术部AI门户是一个基于现代化技术栈构建的全栈应用，作为履约平台技术部AI相关的门户平台型网站，包含AI产品、AI助手、AI学习资料或分享、AI coding、AI资讯动态等内容。项目采用前后端分离架构，前端基于Vue3+element，后端基于springboot。

## 项目结构
- 当前工程是一个前后端分离的项目
- 前端：pnpm+vite+vue3+TypeScript+elementPlus
- 后端：Spring Boot+MDP框架（美团内部框架）
- 前端pages目录下是各个页面的入口，src/pages 下面是页面入口对应的组件代码
- 每个页面可以有自己的路由，前端路由使用浏览器hash模式

```
banma_business_oms/
├── apps/                    # 应用目录
│   ├── frontend/            # 前端应用
│   │   ├── src/             # 前端源代码
│   │   ├── pages/           # 页面入口
│   │   └── ...              # 其他前端资源
│   └── backend/             # 后端应用
│       └── ...              # 后端资源
├── packages/                # 共享包
│   └── ...                  # 共享库和工具
└── ...                      # 配置文件
```
```
#
## 上下文
- 前端路径: `apps/frontend/src/{{module}}/views/{{name}}.vue`
- 后端路径: `apps/backend/src/main/java/com/sankuai/meituan/banma/business/oms/controller{{name}}Controller.java`
- OpenAPI（弱依赖）: `packages/shared/src/openapi/*,packages/shared/src/types/*`
```

## 主要模块

- **前端应用**：基于Vue3 + TypeScript + Element Plus构建的模块化SPA应用
- **后端应用**：基于springboot 提供API服务和业务逻辑处理

## 快速开始

### 安装依赖

```bash
# 安装项目依赖
pnpm install
```

### 启动开发环境

```bash
# 启动前端开发服务器
cd apps/frontend
pnpm dev

# 启动后端开发服务器
cd apps/backend
pnpm dev
```

## 详细文档

- [前端文档./apps/frontend/README.md](https://dev.sankuai.com/code/repo-detail/bm/banma_business_oms/file/detail?path=apps%2Ffrontend%2FREADME.md)
- [后端文档./apps/backend/README.md](https://dev.sankuai.com/code/repo-detail/bm/banma_business_oms/file/detail?path=apps%2Fbackend%2FREADME.md)

## 开发规范

项目遵循严格的开发规范，包括代码风格、提交规范和测试要求。详情请参考各模块的文档。

## 接口一致性检查

为了确保前端和shared包中的接口定义保持一致，我们添加了接口一致性检查脚本。该脚本会在前端构建前自动运行，检查前端和shared包中的接口定义是否一致。

### 手动运行检查

```bash
pnpm check-api
```

### 检查内容

- 接口字段是否一致
- 接口字段类型是否一致
- API路径是否一致

### 注意事项

- 如果shared包不存在，检查会自动跳过
- 检查发现问题会输出警告，但不会阻止构建流程
- 建议在开发过程中定期运行检查，确保接口定义一致
