/**
 * Demo模块的OpenAPI定义
 */
import { SceneQueryItem, SceneQueryParams, SceneQueryResult } from '../types/demo';

// API路径常量
export const API_PATHS = {
  SCENE_QUERY_PAGE: '/api/businessoms/demo/scene/page',
  SCENE_QUERY_DETAIL: '/api/businessoms/demo/scene/detail'
};

// API请求/响应类型定义
export namespace DemoApi {
  // 分页查询
  export namespace SceneQueryPage {
    export type Request = SceneQueryParams;
    export type Response = SceneQueryResult;
  }

  // 详情查询
  export namespace SceneQueryDetail {
    export interface Request {
      id: number;
    }
    export type Response = SceneQueryItem;
  }
} 