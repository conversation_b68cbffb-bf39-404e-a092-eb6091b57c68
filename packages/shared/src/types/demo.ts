/**
 * Demo模块的共享类型定义
 */

// API路径定义
export const API_PATHS = {
  SCENE_QUERY_PAGE: '/api/businessoms/demo/scene/page',
  SCENE_QUERY_DETAIL: '/api/businessoms/demo/scene/detail',
  getDemoList: '/api/businessoms/demo/list',
  getDemoDetail: '/api/businessoms/demo/detail',
  createDemo: '/api/businessoms/demo/create',
  updateDemo: '/api/businessoms/demo/update',
  deleteDemo: '/api/businessoms/demo/delete'
};

// 聚合查询场景实体
export interface SceneQueryItem {
  id: number;
  sceneName: string;
  sceneDescription: string;
  sceneLevel: number;
  appkeys: string;
  fieldCodes: string;
  administrator: string;
  estimateQps: number;
  actualQps: number;
  opName: string;
  opMis: string;
  valid: number;
  ctime: number;
  utime: number;
}

// 分页查询参数
export interface SceneQueryParams {
  sceneName?: string;
  sceneLevel?: number;
  valid?: number;
  pageNum: number;
  pageSize: number;
}

// 分页查询结果
export interface SceneQueryResult {
  total: number;
  list: SceneQueryItem[];
}

// API响应结构
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// Demo模块接口定义
export interface DemoListParams {
  page: number;
  pageSize: number;
  keyword?: string;
}

export interface DemoDetailParams {
  id: number;
}

export interface DemoCreateParams {
  name: string;
  description: string;
  status: number;
}

export interface DemoUpdateParams {
  id: number;
  name?: string;
  description?: string;
  status?: number;
}

export interface DemoDeleteParams {
  id: number;
}

export interface DemoItem {
  id: number;
  name: string;
  description: string;
  status: number;
  createTime: string;
  updateTime: string;
}

export interface DemoDetail extends DemoItem {
  creatorId: number;
  creatorName: string;
  updaterId: number;
  updaterName: string;
}

export interface DemoListResponse {
  list: DemoItem[];
  total: number;
  page: number;
  pageSize: number;
}

export interface DemoDetailResponse {
  data: DemoDetail;
} 