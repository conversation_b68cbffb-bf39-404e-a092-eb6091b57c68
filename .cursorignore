# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
# 通用忽略规则
# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.specstory

# 编辑器和IDE文件
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.project
.classpath
.settings/
*.swp
*.swo
*~

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 依赖目录
node_modules/
jspm_packages/
bower_components/

# 构建输出
dist/
build/
out/
target/
.next/
.nuxt/
.output/

# 环境变量和配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local
*.env.js
config.local.js

# 缓存目录
.npm
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.cache/
.parcel-cache/
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Java相关
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
replay_pid*

# Maven相关
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# 前端构建文件
/dist
/tmp
/coverage
/public/build/

# 临时文件
tmp/
temp/
.temp/

# 本地配置文件
*.local

# 测试覆盖率报告
coverage/
.nyc_output/

# 其他
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local 

.cursorindexingignore
mock/
# SpecStory explanation file
.specstory/.what-is-this.md
# SpecStory explanation file
.specstory/.what-is-this.md


apps/frontend/node_modules/
