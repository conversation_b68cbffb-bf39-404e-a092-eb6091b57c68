/* 主题变量 */
:root {
  /* 主题色 */
  --primary-color: #1890ff;
  --primary-color-light: #40a9ff;
  --primary-color-dark: #096dd9;
  --primary-color-bg: #e6f7ff;
  
  /* 功能色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #f5222d;
  --info-color: #1890ff;
  
  /* 中性色 */
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  --text-color-placeholder: #c0c4cc;
  
  /* 边框色 */
  --border-color-base: #dcdfe6;
  --border-color-light: #e4e7ed;
  --border-color-lighter: #ebeef5;
  --border-color-extra-light: #f2f6fc;
  
  /* 背景色 */
  --background-color-base: #f5f7fa;
  --background-color-light: #fafafa;
  
  /* 菜单色系 - 更浅色调 */
  --menu-bg-color: #ffffff;
  --menu-text-color: #606266;
  --menu-active-text-color: var(--primary-color);
  --menu-hover-bg-color: #f5f7fa;
  --menu-active-bg-color: #e6f7ff;
  
  /* 头部导航色系 */
  --header-bg-color: #ffffff;
  --header-text-color: #606266;
  --header-hover-bg-color: #f5f7fa;
  
  /* 阴影 */
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.08), 0 0 6px rgba(0, 0, 0, 0.02);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  --box-shadow-dark: 0 2px 16px 0 rgba(0, 0, 0, 0.12);
  
  /* 字体 */
  --font-size-extra-large: 20px;
  --font-size-large: 18px;
  --font-size-medium: 16px;
  --font-size-base: 14px;
  --font-size-small: 13px;
  --font-size-extra-small: 12px;
  
  /* 圆角 */
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-round: 20px;
  --border-radius-circle: 50%;
  
  /* 间距 */
  --spacing-mini: 4px;
  --spacing-small: 8px;
  --spacing-base: 12px;
  --spacing-medium: 16px;
  --spacing-large: 24px;
  --spacing-extra-large: 32px;
  
  /* 动画 */
  --transition-duration: 0.3s;
  --transition-timing-function: ease-in-out;
}

/* 暗色主题变量 - 预留 */
html.dark {
  --primary-color: #1890ff;
  --primary-color-light: #40a9ff;
  --primary-color-dark: #096dd9;
  --primary-color-bg: rgba(24, 144, 255, 0.1);
  
  --text-color-primary: #f0f0f0;
  --text-color-regular: #d0d0d0;
  --text-color-secondary: #a0a0a0;
  --text-color-placeholder: #707070;
  
  --border-color-base: #434343;
  --border-color-light: #4f4f4f;
  --border-color-lighter: #5a5a5a;
  --border-color-extra-light: #646464;
  
  --background-color-base: #141414;
  --background-color-light: #1f1f1f;
  
  --menu-bg-color: #1f1f1f;
  --menu-text-color: #d0d0d0;
  --menu-active-text-color: var(--primary-color);
  --menu-hover-bg-color: #2a2a2a;
  --menu-active-bg-color: rgba(24, 144, 255, 0.1);
  
  --header-bg-color: #1f1f1f;
  --header-text-color: #d0d0d0;
  --header-hover-bg-color: #2a2a2a;
  
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 6px rgba(0, 0, 0, 0.2);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  --box-shadow-dark: 0 2px 16px 0 rgba(0, 0, 0, 0.4);
}

// 主题变量
$primary-color: #1890ff;
$primary-light-color: #e6f7ff;
$primary-hover-color: #40a9ff;
$primary-active-color: #096dd9;

$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$info-color: #1890ff;

$text-color: #303133;
$text-color-secondary: #606266;
$text-color-placeholder: #909399;

$border-color: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

$background-color: #f5f7fa;
$background-color-light: #f8f9fc;
$background-color-hover: #eef1f8;

// 菜单样式
.el-menu {
  background-color: $background-color-light !important;
  border-right: none !important;
}

.el-menu-item {
  &:hover, &:focus {
    background-color: $background-color-hover !important;
  }
  
  &.is-active {
    background-color: $primary-light-color !important;
    color: $primary-color !important;
    border-right: 3px solid $primary-color !important;
  }
}

.el-sub-menu__title {
  &:hover, &:focus {
    background-color: $background-color-hover !important;
  }
}

// 卡片样式
.el-card {
  border-radius: 4px;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid $border-color-lighter;
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 表格样式
.el-table {
  th.el-table__cell {
    background-color: $background-color-light;
    font-weight: 500;
  }
  
  .el-table__row {
    &:hover > td.el-table__cell {
      background-color: $background-color-hover;
    }
  }
}

// 分页样式
.el-pagination {
  .el-pagination__sizes .el-input .el-input__inner,
  .btn-prev,
  .btn-next,
  .el-pager li {
    background-color: #fff;
    border: 1px solid $border-color-light;
  }
  
  .el-pager li.is-active {
    background-color: $primary-color;
    color: #fff;
    border-color: $primary-color;
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    font-weight: 500;
  }
}

// 输入框样式
.el-input {
  .el-input__inner {
    &:hover {
      border-color: $primary-hover-color;
    }
    
    &:focus {
      border-color: $primary-color;
    }
  }
}

// 下拉菜单样式
.el-dropdown-menu {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 标签样式
.el-tag {
  border-radius: 2px;
  
  &--primary {
    background-color: $primary-light-color;
    border-color: $primary-light-color;
    color: $primary-color;
  }
}

// 弹窗样式
.el-dialog {
  border-radius: 4px;
  
  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid $border-color-lighter;
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid $border-color-lighter;
  }
} 