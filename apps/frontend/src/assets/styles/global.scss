/* 引入主题样式 */
@use './theme.scss';

/* 引入Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式变量 */
:root {
  /* 主题色 */
  --primary-color: #1890ff;
  --primary-color-light: #40a9ff;
  --primary-color-dark: #096dd9;
  --primary-color-bg: #e6f7ff;
  
  /* 功能色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #f5222d;
  --info-color: #1890ff;
  
  /* 中性色 */
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  --text-color-placeholder: #c0c4cc;
  
  /* 边框色 */
  --border-color-base: #dcdfe6;
  --border-color-light: #e4e7ed;
  --border-color-lighter: #ebeef5;
  --border-color-extra-light: #f2f6fc;
  
  /* 背景色 */
  --background-color-base: #f5f7fa;
  --background-color-light: #fafafa;
  
  /* 阴影 */
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --box-shadow-dark: 0 2px 16px 0 rgba(0, 0, 0, 0.2);
  
  /* 字体 */
  --font-size-extra-large: 20px;
  --font-size-large: 18px;
  --font-size-medium: 16px;
  --font-size-base: 14px;
  --font-size-small: 13px;
  --font-size-extra-small: 12px;
  
  /* 圆角 */
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-round: 20px;
  --border-radius-circle: 50%;
  
  /* 间距 */
  --spacing-mini: 4px;
  --spacing-small: 8px;
  --spacing-base: 12px;
  --spacing-medium: 16px;
  --spacing-large: 24px;
  --spacing-extra-large: 32px;
  
  /* 动画 */
  --transition-duration: 0.3s;
  --transition-timing-function: ease-in-out;
}

/* 全局基础样式 */
html, body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  font-size: var(--font-size-base);
  color: var(--text-color-primary);
  background-color: var(--background-color-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-color-light);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局相关 */
.page-container {
  padding: var(--spacing-large);
}

.page-header {
  margin-bottom: var(--spacing-large);
  
  .page-title {
    font-size: var(--font-size-large);
    font-weight: 500;
    color: var(--text-color-primary);
    margin: 0 0 var(--spacing-small) 0;
  }
  
  .page-desc {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    margin: 0;
  }
}

/* Flex布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.p-0 { padding: 0; }

.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.m-1 { margin: var(--spacing-mini); }
.m-2 { margin: var(--spacing-small); }
.m-3 { margin: var(--spacing-base); }
.m-4 { margin: var(--spacing-medium); }
.m-5 { margin: var(--spacing-large); }
.m-6 { margin: var(--spacing-extra-large); }

.mt-1 { margin-top: var(--spacing-mini); }
.mr-1 { margin-right: var(--spacing-mini); }
.mb-1 { margin-bottom: var(--spacing-mini); }
.ml-1 { margin-left: var(--spacing-mini); }

.mt-2 { margin-top: var(--spacing-small); }
.mr-2 { margin-right: var(--spacing-small); }
.mb-2 { margin-bottom: var(--spacing-small); }
.ml-2 { margin-left: var(--spacing-small); }

.mt-3 { margin-top: var(--spacing-base); }
.mr-3 { margin-right: var(--spacing-base); }
.mb-3 { margin-bottom: var(--spacing-base); }
.ml-3 { margin-left: var(--spacing-base); }

.mt-4 { margin-top: var(--spacing-medium); }
.mr-4 { margin-right: var(--spacing-medium); }
.mb-4 { margin-bottom: var(--spacing-medium); }
.ml-4 { margin-left: var(--spacing-medium); }

.mt-5 { margin-top: var(--spacing-large); }
.mr-5 { margin-right: var(--spacing-large); }
.mb-5 { margin-bottom: var(--spacing-large); }
.ml-5 { margin-left: var(--spacing-large); }

.p-1 { padding: var(--spacing-mini); }
.p-2 { padding: var(--spacing-small); }
.p-3 { padding: var(--spacing-base); }
.p-4 { padding: var(--spacing-medium); }
.p-5 { padding: var(--spacing-large); }
.p-6 { padding: var(--spacing-extra-large); }

.pt-1 { padding-top: var(--spacing-mini); }
.pr-1 { padding-right: var(--spacing-mini); }
.pb-1 { padding-bottom: var(--spacing-mini); }
.pl-1 { padding-left: var(--spacing-mini); }

.pt-2 { padding-top: var(--spacing-small); }
.pr-2 { padding-right: var(--spacing-small); }
.pb-2 { padding-bottom: var(--spacing-small); }
.pl-2 { padding-left: var(--spacing-small); }

.pt-3 { padding-top: var(--spacing-base); }
.pr-3 { padding-right: var(--spacing-base); }
.pb-3 { padding-bottom: var(--spacing-base); }
.pl-3 { padding-left: var(--spacing-base); }

.pt-4 { padding-top: var(--spacing-medium); }
.pr-4 { padding-right: var(--spacing-medium); }
.pb-4 { padding-bottom: var(--spacing-medium); }
.pl-4 { padding-left: var(--spacing-medium); }

.pt-5 { padding-top: var(--spacing-large); }
.pr-5 { padding-right: var(--spacing-large); }
.pb-5 { padding-bottom: var(--spacing-large); }
.pl-5 { padding-left: var(--spacing-large); }

/* 文本工具类 */
.text-primary { color: var(--text-color-primary); }
.text-regular { color: var(--text-color-regular); }
.text-secondary { color: var(--text-color-secondary); }
.text-placeholder { color: var(--text-color-placeholder); }

.text-primary-color { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-xs { font-size: var(--font-size-extra-small); }
.text-sm { font-size: var(--font-size-small); }
.text-base { font-size: var(--font-size-base); }
.text-md { font-size: var(--font-size-medium); }
.text-lg { font-size: var(--font-size-large); }
.text-xl { font-size: var(--font-size-extra-large); }

.font-bold { font-weight: bold; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: normal; }

.line-clamp-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.line-clamp-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 边框和阴影 */
.border { border: 1px solid var(--border-color-base); }
.border-t { border-top: 1px solid var(--border-color-base); }
.border-r { border-right: 1px solid var(--border-color-base); }
.border-b { border-bottom: 1px solid var(--border-color-base); }
.border-l { border-left: 1px solid var(--border-color-base); }

.rounded { border-radius: var(--border-radius-base); }
.rounded-sm { border-radius: var(--border-radius-small); }
.rounded-lg { border-radius: var(--border-radius-round); }
.rounded-full { border-radius: var(--border-radius-circle); }

.shadow { box-shadow: var(--box-shadow-base); }
.shadow-sm { box-shadow: var(--box-shadow-light); }
.shadow-lg { box-shadow: var(--box-shadow-dark); }

/* 卡片样式优化 */
.el-card {
  border-radius: var(--border-radius-base);
  border: none;
  box-shadow: var(--box-shadow-light) !important;
  margin-bottom: var(--spacing-large);
  transition: box-shadow var(--transition-duration) var(--transition-timing-function);
  
  &:hover {
    box-shadow: var(--box-shadow-base) !important;
  }
  
  .el-card__header {
    padding: var(--spacing-base) var(--spacing-large);
    border-bottom: 1px solid var(--border-color-lighter);
    font-weight: 500;
  }
  
  .el-card__body {
    padding: var(--spacing-large);
  }
}

/* 表格样式优化 */
.el-table {
  th.el-table__cell {
    background-color: var(--background-color-base);
    color: var(--text-color-regular);
    font-weight: 500;
  }
  
  .el-table__row:hover > td.el-table__cell {
    background-color: var(--primary-color-bg);
  }
}

/* 按钮样式优化 */
.el-button {
  &.is-link {
    padding: 0 var(--spacing-mini);
  }
  
  &.el-button--danger.is-link {
    color: var(--danger-color);
  }
  
  & + .el-button {
    margin-left: var(--spacing-small);
  }
}

/* 表单样式优化 */
.el-form {
  .el-form-item__label {
    font-weight: 500;
  }
}

/* 分页样式优化 */
.el-pagination {
  padding: var(--spacing-large) 0;
  justify-content: flex-end;
}

/* 弹窗样式优化 */
.el-dialog {
  border-radius: var(--border-radius-base);
  overflow: hidden;
  
  .el-dialog__header {
    padding: var(--spacing-base) var(--spacing-large);
    margin-right: 0;
    border-bottom: 1px solid var(--border-color-lighter);
    
    .el-dialog__title {
      font-size: var(--font-size-medium);
      font-weight: 500;
    }
  }
  
  .el-dialog__body {
    padding: var(--spacing-large);
  }
  
  .el-dialog__footer {
    padding: var(--spacing-base) var(--spacing-large);
    border-top: 1px solid var(--border-color-lighter);
  }
}

/* 标签样式优化 */
.el-tag {
  border-radius: var(--border-radius-base);
}

/* 下拉菜单样式优化 */
.el-dropdown-menu {
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-light);
  
  .el-dropdown-menu__item {
    line-height: 36px;
    
    &:hover {
      background-color: var(--primary-color-bg);
      color: var(--primary-color);
    }
  }
}

/* 工具类 */
.cursor-pointer {
  cursor: pointer;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

.z-100 {
  z-index: 100;
}

.z-max {
  z-index: 9999;
}

/* 动画 */
.transition {
  transition: all var(--transition-duration) var(--transition-timing-function);
}

.transition-fast {
  transition: all 0.15s var(--transition-timing-function);
}

.transition-slow {
  transition: all 0.5s var(--transition-timing-function);
}

.hover-scale {
  transition: transform var(--transition-duration) var(--transition-timing-function);
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-shadow {
  transition: box-shadow var(--transition-duration) var(--transition-timing-function);
  
  &:hover {
    box-shadow: var(--box-shadow-base);
  }
}

.hover-primary {
  transition: color var(--transition-duration) var(--transition-timing-function);
  
  &:hover {
    color: var(--primary-color);
  }
} 

.el-drawer__header {
  margin: 12px 0px 0px 12px !important;
  padding: 6px !important;
  font-weight: bold !important;
}