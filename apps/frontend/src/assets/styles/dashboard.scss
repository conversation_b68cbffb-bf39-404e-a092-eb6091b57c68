/* 数据看板样式 */

/* 数据概览卡片 */
.overview-card {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-light);
  padding: var(--spacing-medium);
  height: 100%;
  
  .overview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-small);
    
    .overview-title {
      font-size: var(--font-size-base);
      color: var(--text-color-secondary);
      margin: 0;
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: var(--spacing-small);
        font-size: 16px;
      }
    }
    
    .overview-extra {
      color: var(--text-color-secondary);
      font-size: var(--font-size-small);
      cursor: pointer;
      
      &:hover {
        color: var(--primary-color);
      }
    }
  }
  
  .overview-value {
    font-size: 28px;
    font-weight: 500;
    color: var(--text-color-primary);
    margin: var(--spacing-small) 0;
  }
  
  .overview-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: var(--spacing-small);
    
    .trend-value {
      display: flex;
      align-items: center;
      font-size: var(--font-size-small);
      
      &.up {
        color: var(--success-color);
      }
      
      &.down {
        color: var(--danger-color);
      }
      
      .el-icon {
        margin-right: 4px;
      }
    }
    
    .trend-label {
      font-size: var(--font-size-small);
      color: var(--text-color-secondary);
    }
  }
}

/* 图表容器 */
.chart-card {
  background-color: #fff;
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-light);
  padding: var(--spacing-medium);
  height: 100%;
  
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-medium);
    
    .chart-title {
      font-size: var(--font-size-medium);
      font-weight: 500;
      color: var(--text-color-primary);
      margin: 0;
    }
    
    .chart-actions {
      display: flex;
      align-items: center;
      
      .action-item {
        margin-left: var(--spacing-small);
        cursor: pointer;
        color: var(--text-color-secondary);
        
        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }
  
  .chart-content {
    height: 300px;
  }
  
  .chart-footer {
    margin-top: var(--spacing-medium);
    display: flex;
    justify-content: space-between;
    
    .footer-item {
      text-align: center;
      
      .item-value {
        font-size: var(--font-size-medium);
        font-weight: 500;
        color: var(--text-color-primary);
      }
      
      .item-label {
        font-size: var(--font-size-small);
        color: var(--text-color-secondary);
        margin-top: 4px;
      }
    }
  }
}

/* 排行榜 */
.ranking-list {
  .ranking-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-small) 0;
    border-bottom: 1px solid var(--border-color-lighter);
    
    &:last-child {
      border-bottom: none;
    }
    
    .ranking-index {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-small);
      margin-right: var(--spacing-small);
      
      &.top-1, &.top-2, &.top-3 {
        color: #fff;
        border-radius: var(--border-radius-circle);
      }
      
      &.top-1 {
        background-color: #f5222d;
      }
      
      &.top-2 {
        background-color: #fa8c16;
      }
      
      &.top-3 {
        background-color: #faad14;
      }
    }
    
    .ranking-content {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .ranking-title {
        font-size: var(--font-size-base);
        color: var(--text-color-primary);
      }
      
      .ranking-value {
        font-size: var(--font-size-base);
        font-weight: 500;
        color: var(--text-color-primary);
      }
    }
  }
}

/* 数据表格 */
.data-table {
  .el-table {
    .cell {
      padding: 8px 12px;
    }
  }
  
  .table-footer {
    margin-top: var(--spacing-medium);
    display: flex;
    justify-content: flex-end;
  }
}

/* 数据过滤器 */
.data-filter {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-medium);
  
  .filter-item {
    margin-right: var(--spacing-medium);
    
    &:last-child {
      margin-right: 0;
    }
  }
  
  .filter-label {
    margin-right: var(--spacing-small);
    font-size: var(--font-size-base);
    color: var(--text-color-regular);
  }
}

/* 数据网格布局 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: var(--spacing-medium);
  margin-bottom: var(--spacing-large);
  
  @media (max-width: 1400px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
  
  .grid-item {
    &.span-2 {
      grid-column: span 2;
    }
    
    &.span-3 {
      grid-column: span 3;
    }
    
    &.span-4 {
      grid-column: span 4;
    }
  }
} 