/* 布局样式 */

/* 基础布局 */
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

/* 头部导航 */
.app-header {
  height: 60px;
  background-color: var(--header-bg-color);
  box-shadow: var(--box-shadow-base);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-medium);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  
  .header-left {
    display: flex;
    align-items: center;
    
    .logo {
      display: flex;
      align-items: center;
      
      .logo-image {
        height: 32px;
        margin-right: var(--spacing-small);
      }
      
      .logo-title {
        font-size: var(--font-size-large);
        font-weight: 500;
        margin: 0;
        color: var(--text-color-primary);
        background-image: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    
    .menu-toggle {
      margin-left: var(--spacing-large);
      padding: var(--spacing-small);
      cursor: pointer;
      border-radius: var(--border-radius-base);
      color: var(--text-color-regular);
      
      &:hover {
        background-color: var(--header-hover-bg-color);
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    
    .header-actions {
      display: flex;
      align-items: center;
      
      .action-item {
        padding: var(--spacing-small);
        margin: 0 var(--spacing-mini);
        cursor: pointer;
        border-radius: var(--border-radius-base);
        color: var(--text-color-regular);
        
        &:hover {
          background-color: var(--header-hover-bg-color);
        }
      }
    }
    
    .header-divider {
      height: 20px;
      margin: 0 var(--spacing-base);
    }
    
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: var(--spacing-mini) var(--spacing-small);
      border-radius: var(--border-radius-base);
      
      &:hover {
        background-color: var(--header-hover-bg-color);
      }
      
      .username {
        margin: 0 var(--spacing-small);
        font-size: var(--font-size-base);
        color: var(--text-color-primary);
      }
    }
  }
}

/* 主体容器 */
.app-container {
  display: flex;
  flex: 1;
  margin-top: 60px;
}

/* 侧边栏 */
.app-sidebar {
  width: 220px;
  height: calc(100vh - 60px);
  background-color: var(--menu-bg-color);
  box-shadow: var(--box-shadow-light);
  position: fixed;
  left: 0;
  top: 60px;
  bottom: 0;
  z-index: 999;
  transition: width var(--transition-duration);
  
  &.collapsed {
    width: 64px;
  }
  
  .el-menu {
    border-right: none;
    
    &:not(.el-menu--collapse) {
      width: 220px;
    }
    
    .el-menu-item {
      height: 50px;
      line-height: 50px;
      
      &.is-active {
        background-color: var(--menu-active-bg-color);
        color: var(--menu-active-text-color);
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background-color: var(--primary-color);
        }
      }
      
      &:hover {
        background-color: var(--menu-hover-bg-color);
      }
    }
    
    .el-sub-menu__title {
      height: 50px;
      line-height: 50px;
      
      &:hover {
        background-color: var(--menu-hover-bg-color);
      }
    }
  }
}

/* 主内容区 */
.app-main {
  flex: 1;
  padding: var(--spacing-large);
  margin-left: 220px;
  transition: margin-left var(--transition-duration);
  min-height: calc(100vh - 60px);
  background-color: var(--background-color-base);
  
  &.expanded {
    margin-left: 64px;
  }
  
  .page-container {
    min-height: calc(100vh - 100px);
    background-color: transparent;
    border: none;
    box-shadow: none;
  }
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-duration) ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式布局 */
@media (max-width: 992px) {
  .app-sidebar {
    transform: translateX(-100%);
    
    &.mobile-show {
      transform: translateX(0);
    }
  }
  
  .app-main {
    margin-left: 0;
  }
  
  .app-header {
    .header-left {
      .logo-title {
        display: none;
      }
    }
  }
} 