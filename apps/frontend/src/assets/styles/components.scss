/* 通用组件样式 */

/* 页面标题样式 */
.page-title-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  padding: 16px 24px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  position: relative;
  display: inline-flex;
  align-items: center;
  margin: 0;
  
  .title-icon {
    margin-right: 10px;
    font-size: 22px;
    color: var(--primary-color);
  }
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: var(--primary-color);
    border-radius: 2px;
    margin-right: 10px;
  }
}

/* 卡片样式 */
.app-card {
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-light);
  background-color: #fff;
  margin-bottom: var(--spacing-medium);
  
  .app-card-header {
    padding: var(--spacing-medium);
    border-bottom: 1px solid var(--border-color-lighter);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .app-card-title {
      font-size: var(--font-size-medium);
      font-weight: 500;
      color: var(--text-color-primary);
      margin: 0;
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: var(--spacing-small);
      }
    }
    
    .app-card-extra {
      display: flex;
      align-items: center;
    }
  }
  
  .app-card-body {
    padding: var(--spacing-medium);
  }
  
  .app-card-footer {
    padding: var(--spacing-medium);
    border-top: 1px solid var(--border-color-lighter);
    display: flex;
    justify-content: flex-end;
  }
}

/* 数据展示卡片 */
.data-card {
  height: 100%;
  
  .data-card-value {
    font-size: 28px;
    font-weight: 500;
    color: var(--text-color-primary);
    margin: var(--spacing-small) 0;
  }
  
  .data-card-title {
    font-size: var(--font-size-base);
    color: var(--text-color-secondary);
    margin-bottom: var(--spacing-small);
  }
  
  .data-card-footer {
    margin-top: var(--spacing-small);
    display: flex;
    align-items: center;
    font-size: var(--font-size-small);
    
    .data-trend {
      display: flex;
      align-items: center;
      
      &.up {
        color: var(--success-color);
      }
      
      &.down {
        color: var(--danger-color);
      }
      
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

/* 表格工具栏 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-medium);
  
  .toolbar-left {
    display: flex;
    align-items: center;
    
    .el-button {
      margin-right: var(--spacing-small);
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    
    .el-input {
      width: 240px;
    }
  }
}

/* 表格样式 */
.app-table {
  width: 100%;
  
  .table-operations {
    .el-button {
      padding: 4px 8px;
      font-size: var(--font-size-small);
      margin-left: 0;
      margin-right: var(--spacing-small);
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
}

/* 表单样式 */
.app-form {
  .form-footer {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-large);
    
    .el-button {
      min-width: 100px;
      margin: 0 var(--spacing-small);
    }
  }
}

/* 搜索表单 */
.search-form {
  background-color: #fff;
  padding: var(--spacing-medium);
  border-radius: var(--border-radius-base);
  margin-bottom: var(--spacing-medium);
  
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: var(--spacing-small);
    
    .el-form-item {
      margin-right: var(--spacing-medium);
      margin-bottom: var(--spacing-small);
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
  
  .form-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--spacing-small);
    
    .el-button {
      margin-left: var(--spacing-small);
    }
  }
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-medium);
}

/* 详情页样式 */
.detail-container {
  .detail-header {
    margin-bottom: var(--spacing-medium);
    
    .detail-title {
      font-size: var(--font-size-large);
      font-weight: 500;
      color: var(--text-color-primary);
      margin-bottom: var(--spacing-small);
    }
    
    .detail-meta {
      display: flex;
      align-items: center;
      color: var(--text-color-secondary);
      font-size: var(--font-size-small);
      
      .meta-item {
        display: flex;
        align-items: center;
        margin-right: var(--spacing-medium);
        
        .el-icon {
          margin-right: var(--spacing-mini);
        }
      }
    }
  }
  
  .detail-content {
    background-color: #fff;
    padding: var(--spacing-medium);
    border-radius: var(--border-radius-base);
  }
}

/* 标签页样式 */
.app-tabs {
  .el-tabs__header {
    margin-bottom: var(--spacing-medium);
  }
}

/* 弹窗样式 */
.app-dialog {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    
    .el-button {
      margin-left: var(--spacing-small);
    }
  }
}

/* 状态标签 */
.status-tag {
  &.success {
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(82, 196, 26, 0.2);
  }
  
  &.warning {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(250, 173, 20, 0.2);
  }
  
  &.danger {
    background-color: rgba(245, 34, 45, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(245, 34, 45, 0.2);
  }
  
  &.info {
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(24, 144, 255, 0.2);
  }
} 