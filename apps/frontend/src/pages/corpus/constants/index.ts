// 来源映射
export const SOURCE_MAP = {
  0: 'TT/大象群',
  1: '其他',
  2: '手动新增',
  3: '学城',
  4: '合并'
} as const

// 平台来源映射
export const PLATFORM_MAP = {
  1: '知识库语料助手机器人',
  2: '语料处理平台管理端',
  3: '语料合并',
  4: '大象群监控',
  5: '学城问答上传'
} as const;

// 表格列配置
export const TABLE_COLUMNS = [
  { prop: 'ticketId', label: '语料ID', width: 100 },
  { prop: 'title', label: '标题', minWidth: 120, showOverflowTooltip: true },
  { prop: 'content', label: '内容', minWidth: 200, showOverflowTooltip: false },
  { prop: 'source', label: '来源', width: 100 },
  { prop: 'misId', label: '创建人', width: 100 },
  { prop: 'createTime', label: '创建时间', width: 160 },
  { prop: 'updateTime', label: '更新时间', width: 160 }
] as const;

// TT列表列配置
export const TT_COLUMNS = [
  { prop: 'ticketId', label: 'TT ID', width: 120 },
  { prop: 'name', label: 'TT标题', minWidth: 200, showOverflowTooltip: true },
  { prop: 'assigned', label: 'TT处理人', width: 120, minWidth: 120, showOverflowTooltip: true },
  { prop: 'createdAt', label: 'TT创建时间', width: 160, minWidth: 160, showOverflowTooltip: true },
  { prop: 'updatedAt', label: 'TT更新时间', width: 160, minWidth: 160, showOverflowTooltip: true },
  { prop: 'kbStatus', label: '是否入库', width: 100 },
  { prop: 'kbUpdateUser', label: '入库人', width: 120, minWidth: 120, showOverflowTooltip: true },
  { prop: 'kbTimestamp', label: '语料更新时间', width: 160 }
] as const

// 检索对比列配置
export const COMPARE_COLUMNS = [
  { prop: 'ticketId', label: 'TASK-ID', width: 150 },
  { prop: 'score', label: '相似度', width: 100, sortable: true },
  { prop: 'title', label: '标题', minWidth: 200, showOverflowTooltip: true },
  { prop: 'content', label: '内容', minWidth: 300, showOverflowTooltip: false },
  { prop: 'updateTime', label: '更新时间', width: 160 }
] as const

// 轮询配置
export const POLL_CONFIG = {
  MAX_RETRIES: 100,
  INTERVAL: 1000,
  INITIAL_DELAY: 1000
} as const 