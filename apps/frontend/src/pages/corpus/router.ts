import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/corpus/index',
    name: 'Corpus',
    component: () => import('./views/pages/index.vue'),
    meta: {
      title: '语料管理系统',
      icon: 'Document'
    }
  },
  {
    path: '/corpus/kmwiki',
    name: 'KmWiki',
    component: () => import('./views/pages/kmwiki/km.vue'),
    meta: {
      title: '知识库Wiki',
      icon: 'Collection'
    }
  },
  {
    path: '/corpus/review/detail',
    name: 'CorpusReviewDetail',
    component: () => import('./views/pages/review/detail.vue'),
    meta: {
      title: '语料审核详情'
    }
  },
  {
    path: '/corpus/stats',
    name: 'CorpusStats',
    component: () => import('./views/pages/stats/index.vue'),
    meta: {
      title: '语料统计分析',
      icon: 'DataAnalysis'
    }
  },
  {
    path: '/corpus/operation',
    name: 'CorpusOperation',
    component: () => import('./views/pages/operation/index.vue'),
    meta: {
      title: '语料服务运营状态',
      icon: 'TrendCharts'
    }
  },
  {
    path: '/corpus/monitor',
    name: 'CorpusMonitor',
    component: () => import('./views/pages/monitor/index.vue'),
    meta: {
      title: '大象群聊咨询问题监控收集',
      icon: 'ChatDotRound'
    }
  },
  {
    path: '/corpus/monitor/statistics',
    name: 'MonitorStatistics',
    component: () => import('./views/pages/monitor/pages/Statistics.vue'),
    meta: {
      title: '监控统计详情'
    }
  }
];

export default routes;
