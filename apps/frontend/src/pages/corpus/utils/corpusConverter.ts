import { ElMessage } from 'element-plus'
import httpRequest from '../../../utils/httpRequest'
import { TTItem } from '../types'
import { formatTitle } from './format'

// 自定义API响应类型
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
  message?: string;
}

/**
 * 处理TT转换为语料的方法
 * @param params 转换所需参数
 * @returns 返回一个Promise，携带转换结果
 */
export const handleConvertToCorpus = async (params: {
  row: TTItem;
  currentMisId: string;
  currentEmpId: string;
  currentTeam: number;
  loadingPanel: { value: boolean };
  loadingPanelTitle: { value: string };
  loadingPanelInstance: any;
  taskMissingInfo: { value: string[] };
  convertDialogVisible: { value: boolean };
  convertFormData: { 
    value: {
      title: string;
      content: string;
      originalContent: string;
      taskId: string;
      ticketId: string;
      rgId: number;
      tagsIds: string;
      taskMissingInfo?: string[];
    }
  };
  onNotify?: (message: {
    title: string;
    content: string;
    type: 'success' | 'warning' | 'info' | 'error';
  }) => void;
}) => {
  const { 
    row, 
    currentMisId, 
    currentEmpId, 
    currentTeam, 
    loadingPanel, 
    loadingPanelTitle, 
    loadingPanelInstance,
    taskMissingInfo,
    convertDialogVisible,
    convertFormData,
    onNotify
  } = params;
  
  // 发送通知的帮助函数
  const notify = (message: {
    title: string;
    content: string;
    type: 'success' | 'warning' | 'info' | 'error';
  }) => {
    if (onNotify) {
      onNotify(message);
    }
  };
  
  if (!currentMisId) {
    ElMessage.error('未获取到用户信息，请刷新页面重试')
    return false
  }

  if (!currentEmpId) {
    ElMessage.error('未获取到用户empId，请刷新页面重试')
    return false
  }

  // 设置加载面板标题
  loadingPanelTitle.value = '请求处理中'
  // 显示加载进度面板
  loadingPanel.value = true

  try {
    // 构造请求参数
    const requestParams = {
      empId: currentEmpId,
      misId: currentMisId,
      ticketId: row.ticketId
    }

    const queryString = Object.entries(requestParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&')
    const response = (await httpRequest.rawRequestGet(`/ticket/addTTContentByTTId?${queryString}`, null)) as unknown as ApiResponse<string>

    if (response?.code === 0 && response?.data) {
      const taskId = response.data
      
      // 发送任务创建成功通知
      notify({
        title: '语料转换任务已创建',
        content: `TT ID: ${row.ticketId} 的转换任务已创建，处理中...`,
        type: 'info'
      });

      // 第一阶段进度 - 开始转换
      setTimeout(() => {
        loadingPanelInstance.value?.setProgress(15)
      }, 1000)

      // 开始轮询任务状态
      let retryCount = 0
      const maxRetries = 100 // 最多轮询100次
      const pollInterval = 1000 // 每秒轮询一次

      // 第二阶段进度 - 开始模型处理
      setTimeout(() => {
        loadingPanelInstance.value?.setProgress(45)
      }, 1000)

      const pollTask = async () => {
        if (retryCount >= maxRetries) {
          loadingPanel.value = false
          ElMessage.error('任务处理超时，请稍后重试')
          
          // 发送超时通知
          notify({
            title: '语料转换任务超时',
            content: `TT ID: ${row.ticketId} 的转换任务处理超时，请稍后在任务列表中查看`,
            type: 'warning'
          });
          
          return
        }

        try {
          const pollResponse = (await httpRequest.rawRequestGet('/review/queryModelOutputByTaskId', {
            taskId
          })) as unknown as ApiResponse<{
            taskStatus: number;
            taskMessage?: string;
            title: string;
            content: string;
            taskId: string;
            taskMissingInfo?: string[];
            tagsIds?: string;
          }>
          

          // 根据轮询次数增加进度
          const currentProgress = 45 + Math.min((retryCount / maxRetries) * 45, 45)
          loadingPanelInstance.value?.setProgress(currentProgress)

          if (pollResponse?.code === 0) {
            if (pollResponse.data.taskStatus === 1) {
              // 任务成功，显示转换对话框
              loadingPanelInstance.value?.complete()
              setTimeout(() => {
                loadingPanel.value = false
                convertFormData.value = {
                  title: formatTitle(pollResponse.data.title),
                  content: pollResponse.data.content,
                  originalContent: pollResponse.data.content, // 保存原始内容
                  taskId: pollResponse.data.taskId,
                  ticketId: row.ticketId,
                  rgId: currentTeam,
                  tagsIds: pollResponse.data.tagsIds || '',
                  taskMissingInfo: pollResponse.data.taskMissingInfo || []
                }
                // 将API返回的taskMissingInfo赋值给taskMissingInfo变量
                taskMissingInfo.value = pollResponse.data.taskMissingInfo || []
                convertDialogVisible.value = true
                
                // 发送成功通知
                notify({
                  title: '语料转换任务已完成',
                  content: `TT ID: ${row.ticketId} 的转换任务处理完成`,
                  type: 'success'
                });
              }, 500)
              return true
            } else if (pollResponse.data.taskStatus === 2) {
              // 任务失败
              loadingPanel.value = false
              const errorMsg = pollResponse.data.taskMessage || '未知错误';
              ElMessage.error(`转换失败: ${errorMsg}`)
              
              // 发送失败通知
              notify({
                title: '语料转换任务失败',
                content: `TT ID: ${row.ticketId} 的转换失败: ${errorMsg}`,
                type: 'error'
              });
              
              return false
            }
          }

          // 继续轮询
          retryCount++
          setTimeout(pollTask, pollInterval)
        } catch (error: any) {
          loadingPanel.value = false
          console.error('轮询出错:', error)
          ElMessage.error(`轮询失败: ${error.message || '未知错误'}`)
          
          // 发送错误通知
          notify({
            title: '语料转换轮询失败',
            content: `TT ID: ${row.ticketId} 的转换轮询失败: ${error.message || '未知错误'}`,
            type: 'error'
          });
          
          return false
        }
      }

      // 开始轮询
      setTimeout(pollTask, pollInterval)
      return true
    } else {
      loadingPanel.value = false
      const errorMsg = response?.msg || '未知错误';
      ElMessage.error(`创建转换任务失败: ${errorMsg}`)
      
      // 发送创建任务失败通知
      notify({
        title: '创建语料转换任务失败',
        content: `TT ID: ${row.ticketId} 的转换任务创建失败: ${errorMsg}`,
        type: 'error'
      });
      
      return false
    }
  } catch (error: any) {
    loadingPanel.value = false
    console.error('转换语料出错:', error)
    ElMessage.error(`转换失败: ${error.message || '未知错误'}`)
    
    // 发送错误通知
    notify({
      title: '语料转换发生错误',
      content: `TT ID: ${row.ticketId} 的转换发生错误: ${error.message || '未知错误'}`,
      type: 'error'
    });
    
    return false
  }
} 