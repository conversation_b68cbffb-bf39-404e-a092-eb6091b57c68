/**
 * Monaco Editor 加载器工具
 * 用于动态加载 Monaco Editor 并配置语言支持
 */
import * as monaco from 'monaco-editor'

// 配置 Monaco Editor 主题
export function configureMonacoTheme() {
  // 定义自定义主题 (可选)
  monaco.editor.defineTheme('customMarkdown', {
    base: 'vs',
    inherit: true,
    rules: [
      { token: 'header', foreground: '0076ff', fontStyle: 'bold' },
      { token: 'emphasis', fontStyle: 'italic' },
      { token: 'strong', fontStyle: 'bold' },
      { token: 'link', foreground: '0366d6' },
      { token: 'code', foreground: '24292e', background: 'f6f8fa' }
    ],
    colors: {
      'editor.foreground': '#24292e',
      'editor.background': '#ffffff',
      'editor.lineHighlightBackground': '#f6f8fa'
    }
  })
}

// 配置 Markdown 语言支持和提示
export function configureMarkdownLanguage() {
  // 检查是否已经注册了Markdown的补全提供程序
  const existingProviders = monaco.languages.CompletionItemProvider
    ? monaco.languages.CompletionItemProvider._entries.some(
        (entry: any) => entry._languages && entry._languages.includes('markdown')
      )
    : false;

  if (existingProviders) {
    return;
  }

  // 注册Markdown代码提示提供器
  monaco.languages.registerCompletionItemProvider('markdown', {
    provideCompletionItems: function(model, position) {
      const word = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn
      };

      // 定义Markdown语法提示
      const suggestions = [
        // 标题
        {
          label: '# 一级标题',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '# ${1:一级标题}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入一级标题',
          range: range
        },
        {
          label: '## 二级标题',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '## ${1:二级标题}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入二级标题',
          range: range
        },
        {
          label: '### 三级标题',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '### ${1:三级标题}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入三级标题',
          range: range
        },
        
        // 文本格式
        {
          label: '**粗体**',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '**${1:粗体文本}**',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入粗体文本',
          range: range
        },
        {
          label: '*斜体*',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '*${1:斜体文本}*',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入斜体文本',
          range: range
        },
        {
          label: '~~删除线~~',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '~~${1:删除线文本}~~',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入删除线文本',
          range: range
        },
        
        // 列表
        {
          label: '- 无序列表',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '- ${1:列表项1}\n- ${2:列表项2}\n- ${3:列表项3}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入无序列表',
          range: range
        },
        {
          label: '1. 有序列表',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '1. ${1:列表项1}\n2. ${2:列表项2}\n3. ${3:列表项3}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入有序列表',
          range: range
        },
        {
          label: '- [ ] 任务列表',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '- [ ] ${1:未完成任务}\n- [x] ${2:已完成任务}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入任务列表',
          range: range
        },
        
        // 链接和图片
        {
          label: '[链接](url)',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '[${1:链接文本}](${2:https://example.com})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入链接',
          range: range
        },
        {
          label: '![图片](url)',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '![${1:图片描述}](${2:https://example.com/image.jpg})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入图片',
          range: range
        },
        
        // 代码
        {
          label: '`行内代码`',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '`${1:行内代码}`',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入行内代码',
          range: range
        },
        {
          label: '```代码块```',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '```${1:language}\n${2:代码内容}\n```',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入代码块',
          range: range
        },
        
        // 表格
        {
          label: '表格',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '| ${1:列1} | ${2:列2} | ${3:列3} |\n| --- | --- | --- |\n| ${4:内容1} | ${5:内容2} | ${6:内容3} |',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入简单表格',
          range: range
        },
        
        // 引用
        {
          label: '> 引用',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '> ${1:引用内容}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '插入引用',
          range: range
        },
        
        // 分隔线
        {
          label: '---',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '---\n',
          documentation: '插入分隔线',
          range: range
        }
      ];

      return { suggestions: suggestions };
    }
  });

  // 添加Markdown语法的触发字符
  monaco.languages.setLanguageConfiguration('markdown', {
    wordPattern: /[-\w]+/,
    comments: {
      blockComment: ['<!--', '-->']
    },
    brackets: [
      ['{', '}'],
      ['[', ']'],
      ['(', ')']
    ],
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '<', close: '>', notIn: ['string'] },
      { open: '`', close: '`', notIn: ['string'] },
      { open: '**', close: '**', notIn: ['string', 'comment'] },
      { open: '*', close: '*', notIn: ['string', 'comment'] },
      { open: '_', close: '_', notIn: ['string', 'comment'] }
    ],
    folding: {
      markers: {
        start: new RegExp("^\\s*<!--\\s*#?region\\b.*-->"),
        end: new RegExp("^\\s*<!--\\s*#?endregion\\b.*-->")
      }
    }
  });
}

// 配置编辑器布局
export function configureEditorLayout() {
  // 添加全局样式以确保编辑器填充其容器
  const style = document.createElement('style')
  style.textContent = `
    .monaco-editor, 
    .monaco-editor .overflow-guard,
    .monaco-editor-background,
    .monaco-editor .inputarea.ime-input {
      width: 100% !important;
    }
    
    .monaco-editor,
    .monaco-editor-background,
    .monaco-editor .inputarea.ime-input {
      padding: 0 !important;
    }
    
    .monaco-editor .margin {
      margin-left: 0 !important;
    }
  `
  document.head.appendChild(style)
}

// 添加键盘快捷键支持Markdown格式
export function configureMarkdownKeyboardShortcuts(editor: monaco.editor.IStandaloneCodeEditor) {
  if (!editor) return;
  
  // 添加常用的Markdown格式化快捷键
  editor.addAction({
    id: 'markdown-bold',
    label: '粗体',
    keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyB],
    run: function(ed: monaco.editor.ICodeEditor) {
      const selection = ed.getSelection();
      if (!selection) return;
      
      const selectedText = ed.getModel()?.getValueInRange(selection) || '';
      const range = selection;
      
      if (selectedText.length > 0) {
        ed.executeEdits('', [
          { range: range, text: `**${selectedText}**` }
        ]);
      } else {
        const position = ed.getPosition();
        if (!position) return;
        
        ed.executeEdits('', [
          { range: range, text: '**粗体文本**' }
        ]);
        // 光标定位到"粗体文本"中间
        ed.setPosition({ lineNumber: position.lineNumber, column: position.column + 2 });
      }
    }
  });
  
  editor.addAction({
    id: 'markdown-italic',
    label: '斜体',
    keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyI],
    run: function(ed: monaco.editor.ICodeEditor) {
      const selection = ed.getSelection();
      if (!selection) return;
      
      const selectedText = ed.getModel()?.getValueInRange(selection) || '';
      const range = selection;
      
      if (selectedText.length > 0) {
        ed.executeEdits('', [
          { range: range, text: `*${selectedText}*` }
        ]);
      } else {
        const position = ed.getPosition();
        if (!position) return;
        
        ed.executeEdits('', [
          { range: range, text: '*斜体文本*' }
        ]);
        // 光标定位到"斜体文本"中间
        ed.setPosition({ lineNumber: position.lineNumber, column: position.column + 1 });
      }
    }
  });
  
  editor.addAction({
    id: 'markdown-link',
    label: '插入链接',
    keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyK],
    run: function(ed: monaco.editor.ICodeEditor) {
      const selection = ed.getSelection();
      if (!selection) return;
      
      const selectedText = ed.getModel()?.getValueInRange(selection) || '';
      const range = selection;
      
      if (selectedText.length > 0) {
        ed.executeEdits('', [
          { range: range, text: `[${selectedText}](url)` }
        ]);
      } else {
        const position = ed.getPosition();
        if (!position) return;
        
        ed.executeEdits('', [
          { range: range, text: '[链接文本](url)' }
        ]);
        // 光标定位到"链接文本"中间
        ed.setPosition({ lineNumber: position.lineNumber, column: position.column + 1 });
      }
    }
  });
}

// 初始化 Monaco Editor
export function initMonacoEditor() {
  configureMonacoTheme()
  configureMarkdownLanguage()
  configureEditorLayout()
  
  // 使用 MutationObserver 监听 DOM 变化，确保编辑器正确调整大小
  window.addEventListener('resize', () => {
    const editors = document.querySelectorAll('.monaco-editor')
    editors.forEach((editor) => {
      const editorInstance = (editor as any)._core?.editor
      if (editorInstance && typeof editorInstance.layout === 'function') {
        editorInstance.layout()
      }
    })
  })
}

export default {
  initMonacoEditor,
  configureMonacoTheme,
  configureMarkdownLanguage,
  configureEditorLayout,
  configureMarkdownKeyboardShortcuts
} 