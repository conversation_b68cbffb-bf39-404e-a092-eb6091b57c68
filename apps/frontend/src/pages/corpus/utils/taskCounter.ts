import httpRequest from '../../../utils/httpRequest'
import type { AxiosResponse } from 'axios'

interface TaskCountParams {
  rgId: number;
  misId: string;
}

interface TaskCountResponse {
  code: number;
  message: string;
  data: number;
}

export const REFRESH_INTERVAL = 3000; // 3秒刷新一次

// 添加节流函数，避免频繁请求
let lastRequestTime = 0;
const THROTTLE_DELAY = 2000; // 2秒内不重复请求

export async function fetchPendingTaskCount({ rgId, misId }: TaskCountParams): Promise<number> {
  const now = Date.now();
  if (now - lastRequestTime < THROTTLE_DELAY) {
    return -1; // 返回-1表示被节流，调用方不更新计数
  }
  
  try {
    lastRequestTime = now;
    const response = await httpRequest.rawRequestGet('/review/countModelOutputsWithAskStatusZero', {
      rgId,
      misId,
    });

    // 类型安全的方式处理响应
    const data = response as unknown as TaskCountResponse;
    if (data.code === 0) {
      return data.data;
    }
    console.warn('获取待处理任务数量失败:', data.message);
    return 0;
  } catch (error) {
    console.error('获取待处理任务数量出错:', error);
    return 0;
  }
} 