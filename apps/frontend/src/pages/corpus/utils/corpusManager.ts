import { ElMessage, ElMessageBox } from 'element-plus'
import httpRequest from '../../../utils/httpRequest'
import { CorpusItem } from '../types'

// 自定义API响应类型
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
  message?: string;
}

/**
 * 批量删除语料方法
 * @param params 删除所需参数
 * @returns 返回一个Promise，携带删除结果
 */
export const batchDeleteCorpus = async (params: {
  selectedRows: CorpusItem[];
  currentMisId: string;
  currentTeam: number;
  refreshCallback: () => Promise<void>;
}): Promise<boolean> => {
  const { selectedRows, currentMisId, currentTeam, refreshCallback } = params;
  
  if (!selectedRows.length) {
    ElMessage.warning('请选择要删除的语料')
    return false
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.length} 条语料吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 获取选中行的ticketId列表
    const ticketIds = selectedRows.map(row => row.ticketId)
    
    // 构造URL参数
    const queryParams = `misId=${currentMisId}&rgId=${currentTeam}&ticketIds=${ticketIds.join(',')}`
    
    const response = (await httpRequest.rawRequestPostAsJson(
      `/corpus/deleteCorpusByTicketIds?${queryParams}`,
      null // POST请求体为空，参数都在URL中
    )) as unknown as ApiResponse<any>
    

    if (response?.code === 0) {
      ElMessage.success('批量删除成功')
      // 刷新列表
      await refreshCallback()
      return true
    } else {
      ElMessage.error(`批量删除失败: ${response?.msg || '未知错误'}`)
      return false
    }
  } catch (error: any) {
    // 检查取消操作的各种可能格式
    if (error !== 'cancel' && error?.toString() !== 'Error: cancel' && error?.message !== 'cancel') {
      console.error('批量删除语料出错:', error)
      ElMessage.error(`批量删除失败: ${error.message || '未知错误'}`)
    }
    // 取消操作不显示错误提示
    return false
  }
}

/**
 * 删除单个语料
 * @param params 删除所需参数
 * @returns 返回一个Promise，携带删除结果
 */
export const deleteCorpus = async (params: {
  ticketId: string;
  currentMisId: string;
  currentTeam: number;
  refreshCallback: () => Promise<void>;
}): Promise<boolean> => {
  const { ticketId, currentMisId, currentTeam, refreshCallback } = params;

  try {
    await ElMessageBox.confirm(
      '确定要删除该语料吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 构造请求参数
    const requestParams = {
      misId: currentMisId,
      rgId: currentTeam,
      ticketIds: ticketId // 单条删除，直接使用当前行的ticketId
    }
    
    
    // 构造URL参数
    const queryParams = `misId=${requestParams.misId}&rgId=${requestParams.rgId}&ticketIds=${requestParams.ticketIds}`
    
    // 发送删除请求
    const response = (await httpRequest.rawRequestPostAsJson(
      `/corpus/deleteCorpusByTicketIds?${queryParams}`,
      null
    )) as unknown as ApiResponse<any>
    
    if (response?.code === 0) {
      ElMessage.success('删除成功')
      // 刷新列表
      await refreshCallback()
      return true
    } else {
      ElMessage.error(`删除失败: ${response?.msg || '未知错误'}`)
      return false
    }
  } catch (error: any) {
    // 检查是否是预期的API错误，而不是取消操作
    if (error?.code !== 'CANCEL' && error !== 'cancel' && error?.message !== 'cancel' && error?.toString() !== 'Error: cancel') {
      console.error('删除出错:', error)
      ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
    }
    return false
  }
}

/**
 * 从检索结果中删除语料
 * @param params 删除所需参数
 * @returns 返回一个Promise，携带删除结果
 */
export const deleteCompareCorpus = async (params: {
  selectedRows: CorpusItem[];
  currentMisId: string;
  currentTeam: number;
  compareResults: { value: CorpusItem[] };
  clearSelection: () => void;
  refreshMainList?: () => Promise<void>;
}): Promise<boolean> => {
  const { selectedRows, currentMisId, currentTeam, compareResults, clearSelection, refreshMainList } = params;
  
  if (!selectedRows.length) {
    ElMessage.warning('请选择要删除的语料')
    return false
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.length} 条语料吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 获取选中行的ticketId列表
    const ticketIds = selectedRows.map(row => row.ticketId)
    
    // URL参数
    const requestParams = {
      misId: currentMisId,
      rgId: currentTeam,
      ticketIds: ticketIds.join(',') // 将数组转换为逗号分隔的字符串
    }
    
    
    // 构造URL参数
    const queryParams = `misId=${requestParams.misId}&rgId=${requestParams.rgId}&ticketIds=${requestParams.ticketIds}`
    
    // 发送删除请求
    const response = (await httpRequest.rawRequestPostAsJson(
      `/corpus/deleteCorpusByTicketIds?${queryParams}`,
      null
    )) as unknown as ApiResponse<any>
    
    if (response?.code === 0) {
      ElMessage.success('删除成功')
      
      // 从检索结果中移除已删除的项
      compareResults.value = compareResults.value.filter(
        item => !ticketIds.includes(item.ticketId)
      )
      
      // 清空选中项
      clearSelection()
      
      // 如果提供了刷新主列表的回调，则执行
      if (refreshMainList) {
        await refreshMainList()
      }
      
      return true
    } else {
      ElMessage.error(`删除失败: ${response?.msg || '未知错误'}`)
      return false
    }
  } catch (error: any) {
    if (error !== 'cancel' && error?.toString() !== 'Error: cancel' && error?.message !== 'cancel') {
      console.error('删除出错:', error)
      ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
    }
    return false
  }
}

/**
 * 检索相似语料
 * @param params 检索相关参数
 * @returns 返回处理结果
 */
export const searchSimilarCorpus = async (params: {
  content: string;
  currentTeam: number;
  ticketId: string;
  loadingPanelInstance?: any;
  SOURCE_MAP: Record<string, string>;
  formatTitle: (title: string) => string;
  formatDateTime: (datetime: string) => string;
}): Promise<{
  success: boolean;
  data: any[];
  error?: string;
}> => {
  const { content, currentTeam, ticketId, loadingPanelInstance, SOURCE_MAP, formatTitle, formatDateTime } = params;
  
  // 最大重试次数
  const maxRetries = 3;
  let currentRetry = 0;
  let contentResults: any[] = [];

  try {
    // 第一阶段进度 - 准备检索
    setTimeout(() => {
      loadingPanelInstance?.setProgress(15);
    }, 500);
    
    // 如果内容超过1000字符，进行分段处理
    const contentLength = content.length;
    if (contentLength > 1000) {
      // 第二阶段进度 - 开始分段处理
      setTimeout(() => {
        loadingPanelInstance?.setProgress(30);
      }, 500);
      
      // 将内容分成多个段落，每段不超过1000字符
      const segments: string[] = [];
      let start = 0;
      while (start < contentLength) {
        let end = start + 1000;
        // 尝试在句号、问号或感叹号处断开
        while (end < contentLength && end < start + 1200 && !'.?!。？！'.includes(content[end - 1])) {
          end++;
        }
        if (end >= contentLength || end >= start + 1200) {
          end = start + 1000;
        }
        segments.push(content.substring(start, end));
        start = end;
      }

      // 分别处理每个段落
      const allResults: any[] = [];
      for (let i = 0; i < segments.length; i++) {
        // 更新进度条
        const segmentProgress = 30 + Math.min((i / segments.length) * 60, 60);
        loadingPanelInstance?.setProgress(segmentProgress);
        
        const requestData = {
          rgId: currentTeam,
          query: segments[i]
        };

        const response = await httpRequest.rawRequestPostAsJson(
          '/review/querySimilarContentWithScore',
          requestData,
          {
            timeout: 30000 // 每段30秒超时
          }
        ) as unknown as ApiResponse<{data: any[]}>;

        if (response?.code === 0 && response.data?.data) {
          allResults.push(...response.data.data);
        }

        // 等待1秒再处理下一段
        if (i < segments.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 合并结果并按相似度排序
      contentResults = allResults
        .reduce((unique: any[], item: any) => {
          const exists = unique.find((u: any) => u.ticketId === item.ticketId);
          if (!exists) {
            unique.push(item);
          } else if (item.score > exists.score) {
            exists.score = item.score;
          }
          return unique;
        }, [])
        // 过滤掉与选中行ticketId相同的结果项
        .filter((item: any) => item.ticketId !== ticketId)
        .sort((a: any, b: any) => b.score - a.score)
        .map((item: any) => ({
          ...item,
          selected: false, // 确保新的检索结果项的选中状态为 false
          score: (item.score * 100).toFixed(0) + '%',
          type: item.type === null ? '系统故障' : item.type,
          source: SOURCE_MAP[item.source] || '其他',
          title: formatTitle(item.title),
          createTime: formatDateTime(item.createTime),
          updateTime: formatDateTime(item.updateTime)
        }));
    } else {
      // 内容较短，直接处理
      // 第二阶段进度 - 开始检索处理
      setTimeout(() => {
        loadingPanelInstance?.setProgress(45);
      }, 500);
      
      const requestData = {
        rgId: currentTeam,
        query: content
      };

      const response = await httpRequest.rawRequestPostAsJson(
        '/review/querySimilarContentWithScore',
        requestData,
        {
          timeout: 30000 // 30秒超时
        }
      ) as unknown as ApiResponse<{data: any[]}>;

      if (response?.code === 0) {
        contentResults = (response.data?.data || [])
          // 过滤掉与选中行ticketId相同的结果项
          .filter((item: any) => item.ticketId !== ticketId)
          .map((item: any) => ({
            ...item,
            selected: false, // 确保新的检索结果项的选中状态为 false
            score: (item.score * 100).toFixed(0) + '%',
            type: item.type === null ? '系统故障' : item.type,
            source: SOURCE_MAP[item.source] || '其他',
            title: formatTitle(item.title),
            createTime: formatDateTime(item.createTime),
            updateTime: formatDateTime(item.updateTime)
          })) || [];
      } else {
        throw new Error(response?.msg || '检索失败');
      }
    }

    // 完成进度
    loadingPanelInstance?.complete();
    
    return {
      success: true,
      data: contentResults
    };
  } catch (error: any) {
    console.error('检索对比出错:', error);
    
    // 如果是超时错误并且还有重试机会
    if ((error.code === 'ECONNABORTED' || error.message?.includes('timeout')) && currentRetry < maxRetries) {
      currentRetry++;
      // 更新进度条文本
      loadingPanelInstance?.setProgress(30);
      await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试
      return await searchSimilarCorpus(params);
    }
    
    // 如果是超时错误但已无重试机会
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return {
        success: false,
        data: [],
        error: '检索超时，请减少文本内容后重试'
      };
    } else {
      return {
        success: false,
        data: [],
        error: `检索失败: ${error.message || '未知错误'}`
      };
    }
  }
}

/**
 * 合并选中的语料
 * @param params 合并语料所需参数
 * @returns 返回合并结果
 */
export const mergeSelectedCorpus = async (params: {
  compareSource: 'main' | 'convert';
  selectedRows?: any[];
  selectedCompareRows: any[];
  currentMisId: string;
  currentTeam: number;
  convertFormData?: any;
  loadingPanelInstance?: any;
  formatTitle: (title: string) => string;
}): Promise<{
  success: boolean;
  data?: {
    title: string;
    content: string;
    taskId: string;
    ticketId: string;
    corpusIdList: string[];
    tagsIds?: string;
  };
  error?: string;
}> => {
  const { 
    compareSource, 
    selectedRows, 
    selectedCompareRows, 
    currentMisId, 
    currentTeam, 
    convertFormData,
    loadingPanelInstance,
    formatTitle
  } = params;

  try {
    // 构造请求体数据
    const requestData = {
      triggerSource: compareSource === 'convert' ? 2 : 1, // 转换语料审核为2，首页为1
      corpusTextList: compareSource === 'convert' ? [convertFormData?.content] : [], // 转换语料审核时使用编辑内容
      corpusIdList: compareSource === 'main' 
        ? [selectedRows?.[0]?.ticketId, ...selectedCompareRows.map(row => row.ticketId)] // 首页模式：包含首页选中的语料
        : selectedCompareRows.map(row => row.ticketId), // 转换模式：只包含检索结果中选中的语料
      misId: currentMisId,
      rgId: currentTeam,
      ticketId: compareSource === 'convert' ? convertFormData?.ticketId : '' // 转换语料审核时使用返回的ticketId
    };

    // 第一阶段进度 - 准备合并
    setTimeout(() => {
      loadingPanelInstance?.setProgress(15);
    }, 1000);


    // 发送合并请求
    const response = await httpRequest.rawRequestPostAsJson(
      '/corpus/createMergeCorpusTask',
      requestData
    ) as unknown as ApiResponse<string>;

    if (response?.code === 0) {
      const taskId = response.data;
      
      // 轮询任务状态
      let retryCount = 0;
      const maxRetries = 100; // 最多轮询100次
      const pollInterval = 1000; // 每秒轮询一次
      
      return new Promise((resolve, reject) => {
        const pollTask = async () => {
          if (retryCount >= maxRetries) {
            reject(new Error('任务处理超时，请稍后重试'));
            return;
          }
          
          try {
            const pollResponse = await httpRequest.rawRequestGet('/review/queryModelOutputByTaskId', {
              taskId
            }) as unknown as ApiResponse<any>;
            
            
            // 根据轮询次数增加进度，确保进度值始终增加
            const currentProgress = 45 + Math.min((retryCount / maxRetries) * 45, 45);
            loadingPanelInstance?.setProgress(currentProgress);
            
            if (pollResponse?.code === 0) {
              if (pollResponse.data.taskStatus === 1) {
                // 任务成功，返回结果
                loadingPanelInstance?.complete();
                
                // 返回成功结果
                resolve({
                  success: true,
                  data: {
                    title: formatTitle(pollResponse.data.title),
                    content: pollResponse.data.content,
                    taskId: pollResponse.data.taskId,
                    ticketId: pollResponse.data.ticketId,
                    corpusIdList: compareSource === 'main'
                      ? [selectedRows?.[0]?.ticketId, ...selectedCompareRows.map(row => row.ticketId)]
                      : selectedCompareRows.map(row => row.ticketId),
                    tagsIds: pollResponse.data.tagsIds || ''
                  }
                });
                return;
              } else if (pollResponse.data.taskStatus === 2) {
                // 任务失败
                reject(new Error(pollResponse.data.taskMessage || '合并失败，请重试'));
                return;
              }
            }
            
            // 继续轮询
            retryCount++;
            setTimeout(pollTask, pollInterval);
          } catch (error: any) {
            console.error('轮询出错:', error);
            reject(new Error(error.message || '轮询失败'));
          }
        };
        
        // 开始轮询
        setTimeout(pollTask, pollInterval);
      });
    } else {
      throw new Error(response?.msg || '创建合并任务失败');
    }
  } catch (error: any) {
    console.error('合并语料出错:', error);
    return {
      success: false,
      error: error.message || '合并语料失败，请重试'
    };
  }
} 