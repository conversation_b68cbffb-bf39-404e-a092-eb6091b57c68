/**
 * 防抖函数
 * @param fn 需要防抖的函数
 * @param delay 延迟时间，默认300ms
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(fn: T, delay = 300): (...args: Parameters<T>) => void {
  let timer: number | null = null
  let isExecuting = false // 添加执行中标识
  
  return function(this: any, ...args: Parameters<T>): void {
    // 如果正在执行，直接返回
    if (isExecuting) {
      return
    }
    
    if (timer) {
      clearTimeout(timer)
    }
    
    timer = setTimeout(() => {
      isExecuting = true // 标记开始执行
      
      try {
        // 使用Promise处理异步函数
        const result = fn.apply(this, args)
        
        // 如果是Promise，等待执行完成
        if (result instanceof Promise) {
          result
            .then(() => {
              isExecuting = false // 执行完成
              timer = null
            })
            .catch(() => {
              isExecuting = false // 执行出错也要重置状态
              timer = null
            })
        } else {
          // 非Promise函数，直接标记完成
          isExecuting = false
          timer = null
        }
      } catch (error) {
        // 捕获同步函数异常
        isExecuting = false
        timer = null
        throw error
      }
    }, delay) as unknown as number
  }
}

export default debounce 