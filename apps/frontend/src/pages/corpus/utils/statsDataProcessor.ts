import type { AppStatusDTO, RecalledCorpusChunkInfo, TicketDetailDTO } from '../types';

// 问题语料召回详情项接口
export interface QuestionCorpusRecallDetailItem {
  questionContent: string;
  questionMessageId: string;
  questionConversationId: string;
  recalledCorpusChunkInfoList: RecalledCorpusChunkInfo[];
}

// 统计分析结果接口
export interface StatsAnalysisResult {
  // 基础指标
  conversationCount: number; // 总会话数量
  userMessageCount: number; // 总提问（消息）数量
  totalRecallChunksCount: number; // 总召回分片数量
  aiGeneratedChunksCount: number; // 语料助手生成的分片被召回数量
  uniqueAiGeneratedChunksCount: number; // 独立的语料助手生成分片数量
  zeroRecallQuestionsCount: number; // 零召回分片的问题数量
  ttCount: number; // 机器人对应值班组周期内TT数量
  
  // 详细数据
  questionList: { 
    question: string; 
    questionTime: number;
    questionMessageId: string;
    questionConversationId: string;
    solved: boolean; // 是否已解决
  }[]; // 所有问题列表
  zeroRecallQuestions: string[]; // 零召回问题列表
  recallInfoByQuestion: Record<string, RecalledCorpusChunkInfo[]>; // 按问题分组的召回信息
  aiGeneratedChunks: RecalledCorpusChunkInfo[]; // 语料助手生成的分片
  tickets: TicketDetailDTO[]; // 工单列表
  questionClusters: { 
    questionPattern: string; 
    questionList: Array<{ 
      question: string; 
      questionMessageId: string;
      questionConversationId: string;
      questionTime: number;
    }>;
  }[]; // 问题聚类数据
  
  // 原始API字段
  appId: string; // 应用ID
  appName: string; // 应用名称
  beginTime: number; // 查询开始时间戳
  endTime: number; // 查询结束时间戳
  questionAndRecallInfos: QuestionCorpusRecallDetailItem[]; // 问题和召回信息列表（新格式）
  relatedRgList: number[]; // 相关的RG ID列表
  timeRangeTicketIdList: TicketDetailDTO[]; // 与tickets相同
}

/**
 * 创建空的统计结果
 */
function createEmptyResult(): StatsAnalysisResult {
  return {
    conversationCount: 0,
    userMessageCount: 0,
    totalRecallChunksCount: 0,
    aiGeneratedChunksCount: 0,
    uniqueAiGeneratedChunksCount: 0,
    zeroRecallQuestionsCount: 0,
    ttCount: 0,
    
    questionList: [],
    zeroRecallQuestions: [],
    recallInfoByQuestion: {},
    aiGeneratedChunks: [],
    tickets: [],
    questionClusters: [],
    
    // 原始API字段
    appId: '',
    appName: '',
    beginTime: 0,
    endTime: 0,
    questionAndRecallInfos: [],
    relatedRgList: [],
    timeRangeTicketIdList: []
  };
}

/**
 * 处理API返回的统计数据，计算各类指标
 * @param data API返回的原始数据
 * @returns 处理后的统计分析结果
 */
export function processStatsData(data: AppStatusDTO | null): StatsAnalysisResult {
  if (!data) {
    return createEmptyResult();
  }
  
  // 从API提供的数据中提取问题列表及其时间戳
  const rawQuestionList = data.questionList || [];
  
  // 构建带时间戳的问题列表
  const questionList = rawQuestionList.map((item: any) => {
    // 如果已经是带完整信息的对象格式，直接返回
    if (typeof item === 'object' && 'question' in item) {
      return {
        question: item.question,
        questionTime: item.questionTime || 0,
        questionMessageId: item.questionMessageId || '',
        questionConversationId: item.questionConversationId || '',
        solved: item.solved || false
      };
    }
    
    // 如果是普通字符串，构建问题对象并生成随机时间戳
    const timeRange = data.endTime - data.beginTime;
    const questionText = typeof item === 'string' ? item : JSON.stringify(item);
    
    // 使用问题的hash值作为时间戳的基础，确保相同问题在不同加载中有相同时间戳
    const hash = questionText.split('').reduce((acc, char) => 
      ((acc << 5) - acc) + char.charCodeAt(0)
    , 0);
    
    // 将hash值映射到时间范围内，并加上beginTime作为基准
    const randomTime = Math.abs(hash % timeRange) + data.beginTime;
    
    return {
      question: questionText,
      questionTime: randomTime,
      questionMessageId: '',
      questionConversationId: '',
      solved: false
    };
  });
  
  // 获取所有召回的语料块
  const allRecallChunks: RecalledCorpusChunkInfo[] = [];
  
  // 从新的数据结构中获取召回语料信息
  const recallInfoByQuestion: Record<string, RecalledCorpusChunkInfo[]> = {};
  
  (data.questionAndRecallInfos || []).forEach(item => {
    // 构建兼容的旧格式数据结构
    recallInfoByQuestion[item.questionContent] = item.recalledCorpusChunkInfoList || [];
    
    // 收集所有召回语料块
    if (item.recalledCorpusChunkInfoList && item.recalledCorpusChunkInfoList.length > 0) {
      allRecallChunks.push(...item.recalledCorpusChunkInfoList);
    }
  });
  
  // 找出零召回的问题
  const zeroRecallQuestions = (data.questionAndRecallInfos || [])
    .filter(item => !item.recalledCorpusChunkInfoList || item.recalledCorpusChunkInfoList.length === 0)
    .map(item => item.questionContent);
  
  // 找出AI生成的语料块（source === 1 表示语料处理服务生成）
  const aiGeneratedChunks = allRecallChunks.filter(chunk => chunk.source === 1);
  
  // 找出独立的AI生成语料块（去重）
  const uniqueAiGeneratedChunks = Array.from(
    new Map(aiGeneratedChunks.map(chunk => [chunk.corpusId, chunk])).values()
  );
  
  return {
    conversationCount: data.conversationCount || 0,
    userMessageCount: data.userMessageCount || 0,
    totalRecallChunksCount: allRecallChunks.length,
    aiGeneratedChunksCount: aiGeneratedChunks.length,
    uniqueAiGeneratedChunksCount: uniqueAiGeneratedChunks.length,
    zeroRecallQuestionsCount: zeroRecallQuestions.length,
    ttCount: data.timeRangeTicketIdList?.length || 0,
    
    questionList,
    zeroRecallQuestions,
    recallInfoByQuestion,
    aiGeneratedChunks,
    tickets: data.timeRangeTicketIdList || [],
    questionClusters: data.questionClusters || [],
    
    // 保留原始字段
    appId: data.appId || '',
    appName: data.appName || '',
    beginTime: data.beginTime || 0,
    endTime: data.endTime || 0,
    questionAndRecallInfos: data.questionAndRecallInfos || [],
    relatedRgList: data.relatedRgList || [],
    timeRangeTicketIdList: data.timeRangeTicketIdList || []
  };
} 