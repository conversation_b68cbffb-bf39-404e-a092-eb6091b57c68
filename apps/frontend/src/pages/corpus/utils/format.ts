/**
 * 格式化日期时间
 * @param dateTimeString 日期时间字符串
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (dateTimeString: string): string => {
  if (!dateTimeString) return ''
  const date = new Date(dateTimeString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 格式化标题
 * @param title 原始标题
 * @returns 格式化后的标题
 */
export const formatTitle = (title: string): string => {
  if (!title) return ''
  // 移除【m-xxxxx】或【数字】格式的前缀
  return title.replace(/^【[a-zA-Z0-9-]+】/, '')
}

/**
 * 获取相似度样式类
 * @param score 相似度分数
 * @returns 样式类名
 */
export const getSimilarityClass = (score: string): string => {
  const value = parseInt(score)
  if (value >= 80) return 'high'
  if (value >= 60) return 'medium'
  return 'low'
} 