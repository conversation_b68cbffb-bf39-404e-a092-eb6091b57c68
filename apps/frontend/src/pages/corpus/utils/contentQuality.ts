import httpRequest from '../../../utils/httpRequest'

interface ContentQualityResponse {
  score: number;
  suggestions: string[];
}

/**
 * 获取内容质量评分
 * @param content 待评估的内容
 * @returns 内容质量评分结果
 */
export async function getContentQualityAssessment(content: string): Promise<ContentQualityResponse | null> {
  if (!content || content.trim().length === 0) {
    console.warn('内容为空，不发送评分请求')
    return null
  }

  // 如果内容过长，截取一部分，避免请求过大
  const MAX_LENGTH = 5000
  const requestContent = content.length > MAX_LENGTH ? content.substring(0, MAX_LENGTH) : content
  

  try {
    console.time('质量评分请求耗时')
    const response = await httpRequest.rawRequestPostAsJson(
      '/review/getContentQualityAssessment',
      { query: requestContent }
    )
    console.timeEnd('质量评分请求耗时')

    // 转换为安全的类型
    const data = response as any;
    
    if (data?.code === 0 && data?.data) {
      // 确保返回的数据符合预期格式
      const result: ContentQualityResponse = {
        score: typeof data.data.score === 'number' ? data.data.score : parseInt(data.data.score) || 0,
        suggestions: Array.isArray(data.data.suggestions) ? data.data.suggestions : []
      }
      
      return result
    }
    
    // 如果没有正确的响应，创建一个模拟的评分
    if (content.length > 100) {
      console.warn('获取内容质量评分失败，但内容足够长，返回默认评分')
      return {
        score: 70,
        suggestions: ['无法获取评分，但内容已填写。建议检查格式并确保包含必要信息。']
      }
    }
    
    console.warn('获取内容质量评分失败:', data?.message || '未知错误')
    return null
  } catch (error) {
    console.error('获取内容质量评分出错:', error)
    
    // 如果内容足够长，至少返回一个默认评分
    if (content.length > 100) {
      return {
        score: 70,
        suggestions: ['评分服务暂时不可用。建议检查格式并确保包含必要信息。']
      }
    }
    
    return null
  }
} 