<template>
  <div class="knowledge-upload-container">
    <!-- 标题区域 -->
    <div class="title-row">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="handleBackToIndex" type="text" size="large">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
      </div>

      <h1 class="page-title">
        <!-- 文档列表模式 -->
        <template v-if="activeUploadType === 'document'">
          背景知识列表
        </template>
        
        <!-- QA模式的标题 -->
        <template v-else-if="activeUploadType === 'qa'">
          {{ qaActiveStep === 1 ? 'FAQ知识上传' : (qaActiveStep === 2 ? 'FAQ文档切分' : '大模型转语料') }}
        </template>
        
        <!-- 网站模式的标题 -->
        <template v-else-if="activeUploadType === 'website'">
          {{ websiteActiveStep === 1 ? '背景知识上传' : '背景知识处理' }}
        </template>
      </h1>
    </div>

    <!-- 上传方式选择器 - 始终显示 -->
    <StepsSelector v-model:activeStep="activeUploadType" class="steps-selector" />

    <!-- 上传方式按钮 - 仅在FAQ或网站的第一步显示
    <div v-if="isInitialStep && activeUploadType !== 'document'">
      <UploadMethods v-model="activeUploadType" />
    </div> -->

    <!-- QA模式的步骤指示器 -->
    <StepsIndicator 
      v-if="activeUploadType === 'qa'" 
      :activeStep="qaActiveStep" 
      :titles="['FAQ知识上传', 'FAQ文档切分', '大模型转语料']"
      class="steps-container"
    />
    
    <!-- 网站模式的步骤指示器 -->
    <StepsIndicator 
      v-else-if="activeUploadType === 'website'" 
      :activeStep="websiteActiveStep" 
      :totalSteps="2"
      :titles="['背景知识上传', '背景知识处理']"
      class="steps-container"
    />

    <!-- FAQ上传流程 -->
    <template v-if="activeUploadType === 'qa'">
      <!--FAQ步骤1：上传知识 -->
      <template v-if="qaActiveStep === 1">
        <div class="upload-form-container qa-upload-container">
          <QaUpload 
            ref="qaUploadRef" 
            :initialUrl="parsedQaUrl"
            :initialContentId="contentId"
            :initialIsTable="documentIsTable"
            :rgId="rgIdFromRoute"
            :misId="misIdFromRoute"
            @parse-status-change="handleQaParseStatusChange"
            @next-step="handleQaNextStep"
          />
          <TipsSection />
        </div>
      </template>

      <!-- FAQ步骤2：处理知识 -->
      <template v-else-if="qaActiveStep === 2">
        <div class="knowledge-process-layout">
          <!-- 左侧：知识处理区域 -->
          <div class="knowledge-process-area">
            <KnowledgeProcess 
              :contentId="contentId"
              :corpusData="corpusData"
              :rgId="rgIdFromRoute || 1"
              :misId="misIdFromRoute"
              @update-corpus-data="handleCorpusDataUpdate"
              @open-tag-management="handleOpenTagManagement"
            />
          </div>
          
          <!-- 右侧：知识预览区域 -->
          <div class="knowledge-preview-area">
            <KnowledgePreview 
              title="FAQ"
              :segments="knowledgeSegments"
              :initialSelectedSegments="selectedKnowledgeSegments"
              @select-segment="handleSelectSegment"
              @selected-segments-change="handleSelectedSegmentsChange"
              @prev-step="handleQaPrevStep"
              @next-step="handleQaPreviewNextStep"
            />
          </div>
        </div>
      </template>

      <!--FAQ步骤3：学习知识 -->
      <template v-else-if="qaActiveStep === 3">
        <div class="qa-step-container">
          <KnowledgeLearn 
            :selectedSegments="selectedKnowledgeSegments"
            :contentId="contentId"
            :rgId="rgIdFromRoute || 1"
            :misId="misIdFromRoute"
            @prev-step="handleQaPrevStep"
          />
        </div>
      </template>
    </template>

    <!-- 网站上传流程 -->
    <template v-else-if="activeUploadType === 'website'">
      <!-- 网站步骤1：上传知识 -->
      <template v-if="websiteActiveStep === 1">
        <div class="upload-form-container">
          <WebsiteUpload 
            ref="websiteUploadRef"
            :savedWebsites="savedWebsites"
            @add-website="handleAddWebsite" 
            @batch-import="handleBatchImport" 
            @next-step="handleWebsiteNextStep"
          />
          <TipsSection />
        </div>
      </template>

      <!-- 网站步骤2：处理知识 -->
      <template v-else-if="websiteActiveStep === 2">
        <div class="knowledge-process-layout">
          <div class="website-processing-container">
            <h3>网站知识处理</h3>
            
            <div class="website-data-list">
              <div v-if="savedWebsites.length === 0" class="empty-data">
                暂无数据，请返回上一步添加网址
              </div>
              <template v-else>
                <div class="data-summary">
                  <span>已添加 {{ savedWebsites.length }} 条网址数据，请确认后导入</span>
                </div>
                
                <el-table :data="savedWebsites" style="width: 100%" border stripe>
                  <el-table-column type="index" label="序号" width="70" align="center" />
                  <el-table-column prop="website" label="网址" min-width="250" show-overflow-tooltip />
                  <el-table-column prop="name" label="知识名称" min-width="150" show-overflow-tooltip />
                  <el-table-column label="自动更新" width="100" align="center">
                    <template #default="scope">
                      <el-tag :type="scope.row.autoUpdate === 1 ? 'success' : 'info'">
                        {{ scope.row.autoUpdate === 1 ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </div>
            
            <!-- 操作按钮 -->
            <div class="website-process-actions">
              <el-button @click="handleWebsitePrevStep" type="default">上一步</el-button>
              <el-button 
                type="primary" 
                :disabled="savedWebsites.length === 0"
                @click="handleConfirmImport"
                :loading="importLoading"
              >
                确认导入
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </template>
    
    <!-- 学城列表 -->
    <template v-else-if="activeUploadType === 'document'">
      <div class="document-list-layout document-list-container">
        <DocumentList
          :key="`document-list-${rgIdFromRoute}-${misIdFromRoute}`"
          :rgId="rgIdFromRoute || 1"
          :misId="misIdFromRoute"
          @switch-to-website="handleSwitchToWebsiteUpload"
        />
      </div>
    </template>

    <!-- 添加失败URL列表对话框 -->
    <el-dialog
      v-model="failedUrlsDialogVisible"
      title="导入失败的网站"
      width="50%"
    >
      <div class="failed-urls-container">
        <p class="failed-notice">以下网站导入失败，请检查网址是否有效：</p>
        
        <el-table :data="failedUrlsList" border stripe style="width: 100%">
          <el-table-column prop="name" label="网站名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="url" label="网址" min-width="300" show-overflow-tooltip />
        </el-table>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="failedUrlsDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleRetryFailedUrls">重试导入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import httpRequest from '@/utils/httpRequest'
import { API_PATHS } from '@/pages/corpus/request/api'
import { getCurrentUser } from '@/shared/services/userService'
import StepsIndicator from '@/pages/corpus/views/components/kmwiki/StepsIndicator.vue'
import UploadMethods from '@/pages/corpus/views/components/kmwiki/UploadMethods.vue'
import WebsiteUpload from '@/pages/corpus/views/components/kmwiki/WebsiteUpload.vue'
import QaUpload from '@/pages/corpus/views/components/kmwiki/QaUpload.vue'
import TipsSection from '@/pages/corpus/views/components/kmwiki/TipsSection.vue'
import KnowledgeProcess from '@/pages/corpus/views/components/kmwiki/KnowledgeProcess.vue'
import KnowledgePreview from '@/pages/corpus/views/components/kmwiki/KnowledgePreview.vue'
import KnowledgeLearn from '@/pages/corpus/views/components/kmwiki/KnowledgeLearn.vue'
import DocumentList from '@/pages/corpus/views/components/kmwiki/DocumentList.vue'
import StepsSelector from '@/pages/corpus/views/components/kmwiki/StepsSelector.vue'

// 定义语料列表数据类型
interface CorpusItem {
  ticketId: string;
  title: string;
  content: string;
  type: number;
  source: number;
  misId: string;
  createTime: string;
  updateTime: string;
  contentId: number;
  tagsIds?: string; // 标签ID字符串，逗号分隔
  tagsname?: string; // 新增：标签名称，逗号分隔
}

interface CorpusData {
  list: CorpusItem[];
  total: number;
  totalPage: number;
  pageSize: number;
  currentPage: number;
}

// 当前活动上传类型
const activeUploadType = ref('qa')

//FAQ模式活动步骤
const qaActiveStep = ref(1)

// 网站模式活动步骤
const websiteActiveStep = ref(1)

// 添加documentIsTable变量保存QA上传组件中的文档类型勾选状态
const documentIsTable = ref(false)

// 判断是否处于初始步骤（第一步）
const isInitialStep = computed(() => {
  return (activeUploadType.value === 'qa' && qaActiveStep.value === 1) || 
         (activeUploadType.value === 'website' && websiteActiveStep.value === 1)
})

//FAQ上传组件引用
const qaUploadRef = ref(null)

// Website上传组件引用
const websiteUploadRef = ref(null)

// FAQ URL解析状态
const isQaUrlParseSuccess = ref(false)

// 保存已解析的QA URL
const parsedQaUrl = ref('')

// 保存已添加的网址列表
const savedWebsites = ref<Array<{ website: string; name: string; autoUpdate?: number; contentId?: number | null }>>([])

// 获取路由参数
const route = useRoute()
const router = useRouter()
const rgIdFromRoute = ref<number | null>(null)
const misIdFromRoute = ref<string>('')

// 定义知识片段数据
const knowledgeSegments = ref<string[]>([])

// 选中的知识片段
const selectedKnowledgeSegments = ref<string[]>([])

// 存储内容ID
const contentId = ref<number | null>(null)

// 语料列表数据
const corpusData = ref<CorpusData>({
  list: [],
  total: 0,
  totalPage: 1,
  pageSize: 1000,
  currentPage: 1
})

// 导入加载状态
const importLoading = ref(false)

// 在组件挂载时获取参数
onMounted(() => {
  // 从路由查询参数中获取rgId和misId
  if (route.query.rgId) {
    rgIdFromRoute.value = Number(route.query.rgId)
  } else {
    console.warn('km.vue - 未从路由获取到rgId参数')
  }
  
  if (route.query.misId) {
    misIdFromRoute.value = String(route.query.misId)
  } else {
    console.warn('km.vue - 未从路由获取到misId参数')
  }
  
})

// 处FAQ URL解析状态变化
const handleQaParseStatusChange = (success: boolean) => {
  isQaUrlParseSuccess.value = success
}

// 添加网址
const handleAddWebsite = (data: { website: string; name: string }) => {
  // 保存到全局状态，用于返回时恢复
  if (!savedWebsites.value.some(item => item.website === data.website)) {
    savedWebsites.value.push(data)
  }
  
  ElMessage.success('添加网址成功')
}

// 学城目录批量导入
const handleBatchImport = () => {
  ElMessage.info('批量导入功能开发中')
}

// ==================== FAQ流程相关方法 ====================

// 处理QA组件下一步按钮点击 (第一步到第二步)
const handleQaNextStep = async () => {
  
  if (qaActiveStep.value === 1) {
    try {
      // 获取FAQ内容ID
      if (qaUploadRef.value) {
        const contentIdFromQa = qaUploadRef.value.contentId
        
        if (!contentIdFromQa) {
          ElMessage.warning('内容ID无效，请重新解析URL')
          return
        }
        
        // 存储内容ID
        contentId.value = contentIdFromQa
        
        // 保存已解析的URL，用于返回时恢复状态
        if (qaUploadRef.value.formData && qaUploadRef.value.formData.website) {
          parsedQaUrl.value = qaUploadRef.value.formData.website
        }
        
        // 保存文档类型勾选框的值，用于返回时恢复状态
        documentIsTable.value = qaUploadRef.value.isTable
        
        // 使用路由传递的参数或者通过API获取用户信息
        let misId = misIdFromRoute.value
        let rgId = rgIdFromRoute.value
        
        // 如果没有从路由获取到参数，则尝试获取用户信息
        if (!misId || !rgId) {
          const userInfo = await getCurrentUser()
          if (!userInfo) {
            ElMessage.error('获取用户信息失败')
            return
          }
          
          misId = misId || userInfo.login
          // 如果没有从路由获取到rgId，则使用默认值1
          rgId = rgId || 1
        }
        
        // 获取是否为表格内容的标记
        const isTable = qaUploadRef.value.isTable ? 1 : 0
        
        // 发送请求获取知识拆分数据
        const kmSplitResponse = await httpRequest.rawRequestGet(API_PATHS.GET_KM_SPLIT_BY_CONTENT_ID, { 
          contentId: contentIdFromQa,
          isTable: isTable, // 添加isTable参数
          rgId,
          misId
        })
        
        
        if (kmSplitResponse.code === 0 && kmSplitResponse.data) {
          // 存储拆分数据到knowledgeSegments
          knowledgeSegments.value = kmSplitResponse.data.data || []
          
          // 获取语料列表数据
          try {
            // 构建查询语料列表的请求参数
            const corpusParams = {
              rgId,
              misId,
              contentId: contentIdFromQa,
              pageNum: 1,
              pageSize: 1000
            }
            
            // 发送请求获取语料列表数据
            const corpusResponse = await httpRequest.rawRequestGet(API_PATHS.QUERY_CORPUS_LIST_BY_CONTENT_ID, corpusParams)
            
            if (corpusResponse.code === 0) {
              const responseData = corpusResponse.data
              
              if (responseData && typeof responseData === 'object') {
                corpusData.value = {
                  list: Array.isArray(responseData.list) ? responseData.list : [],
                  total: responseData.total || 0,
                  totalPage: responseData.totalPage || 1,
                  pageSize: responseData.pageSize || 1000,
                  currentPage: responseData.currentPage || 1
                }
              } else {
                ElMessage.warning('语料列表数据结构异常')
                return
              }
            } else {
              ElMessage.error(`获取语料列表数据失败: ${corpusResponse.message}`)
              return
            }
          } catch (corpusError) {
            ElMessage.error('获取语料列表数据时发生错误')
            return
          }
          
          // 切换到FAQ第二步
          qaActiveStep.value++
          ElMessage.success('进入FAQ知识处理阶段')
        } else {
          ElMessage.error(`获取知识拆分数据失败: ${kmSplitResponse.message}`)
        }
      }
    } catch (error) {
      console.error('处理FAQ下一步时出错:', error)
      ElMessage.error('获取知识拆分数据时发生错误，请重试')
    }
  }
}

// 处理FAQ预览下一步 (第二步到第三步)
const handleQaPreviewNextStep = () => {
  
  // 如果没有选中任何段落，提示用户
  if (selectedKnowledgeSegments.value.length === 0) {
    ElMessage.warning('请至少选择一个知识段落')
    return
  }
  
  // 存储选中的段落用于下一步显示
  const selectedSegmentsForLearning = [...selectedKnowledgeSegments.value]
  
  // 带着选中的段落进FAQ第三步
  qaActiveStep.value++
  ElMessage.success(`已选择 ${selectedSegmentsForLearning.length} 个知识段落进入学习阶段`)
}

// 处理FAQ流程上一步
const handleQaPrevStep = () => {
  
  if (qaActiveStep.value > 1) {
    // 在从第三步返回第二步时，保留已选择的段落信息
    if (qaActiveStep.value === 3) {
      // 不需要额外操作，因为selectedKnowledgeSegments会自动保留
    }
    
    // 从第二步返回第一步，此时需要保留URL解析状态和内容ID
    if (qaActiveStep.value === 2) {
      // documentIsTable值已在进入第二步时保存，无需重新保存
      // contentId和isQaUrlParseSuccess会自动保留
    }
    
    // 更新步骤
    qaActiveStep.value--
  }
}

// ==================== 网站流程相关方法 ====================

// 处理网站上传下一步
const handleWebsiteNextStep = (data: {
  websites: Array<{ website: string; name: string; autoUpdate: number; contentId?: number | null }>,
  rgId: number | null,
  misId: string
}) => {
  
  // 保存网站数据
  if (data && data.websites && Array.isArray(data.websites)) {
    savedWebsites.value = [...data.websites]
    
    // 保存rgId和misId
    if (data.rgId !== null) rgIdFromRoute.value = data.rgId
    if (data.misId) misIdFromRoute.value = data.misId
  } else {
    // 兼容旧的处理方式，如果websiteUploadRef.value.addedWebsites存在
    if (websiteUploadRef.value && websiteUploadRef.value.addedWebsites) {
      savedWebsites.value = [...websiteUploadRef.value.addedWebsites]
    }
  }
  
  // 切换到网站第二步
  websiteActiveStep.value++
  ElMessage.success('进入网站知识处理阶段')
}

// 处理网站流程上一步
const handleWebsitePrevStep = () => {
  
  if (websiteActiveStep.value > 1) {
    // 在从网站处理阶段返回到上传阶段时，保留已添加的网址信息
    if (websiteActiveStep.value === 2) {
      // 网站信息会自动保留在WebsiteUpload组件中
    }
    
    // 更新步骤
    websiteActiveStep.value--
  }
}

// ==================== 通用方法 ====================

// 处理段落选择
const handleSelectSegment = (segment: string, index: number) => {
}

// 处理选中段落变化
const handleSelectedSegmentsChange = (segments: string[]) => {
  selectedKnowledgeSegments.value = segments
}

// 获取段落路径
const getSegmentPath = (segment: string): string => {
  // 如果段落包含路径信息（形如 "QA/二、无效客资/Q:用户称其没有留资行为"）
  const pathMatch = segment.match(/^([^/]+\/[^/]+\/[^:]+)/)
  if (pathMatch) {
    return pathMatch[1]
  }
  
  // 如果是简单的类别（如 "QA"）
  const simplePathMatch = segment.match(/^([^/\n]+)/)
  if (simplePathMatch) {
    return simplePathMatch[1]
  }
  
  return ''
}

// 获取段落内容（去除路径信息）
const getSegmentContent = (segment: string): string => {
  // 如果段落包含路径和内容（如 "QA/二、无效客资/Q:用户称其没有留资行为\n使用查看用户..."）
  const contentMatch = segment.match(/^[^/]+\/[^/]+\/[^:]+:(.+)$/s)
  if (contentMatch) {
    return contentMatch[1].trim()
  }
  
  // 如果段落包含简单的"类别/内容"格式
  const simpleContentMatch = segment.match(/^[^/\n]+\/(.+)$/s)
  if (simpleContentMatch) {
    return simpleContentMatch[1].trim()
  }
  
  // 如果没有匹配到格式，返回原文
  return segment
}

// 在学习阶段显示完整内容不带省略号
const getFullSegmentContent = (segment: string): string => {
  const content = getSegmentContent(segment)
  return content // 不截断，显示完整内容
}

// 处理语料列表数据更新
const handleCorpusDataUpdate = (updatedData: CorpusData) => {
  corpusData.value = updatedData
}

// 处理从文档列表切换到网站知识上传
const handleSwitchToWebsiteUpload = () => {
  
  // 重置网站上传数据
  savedWebsites.value = []
  
  // 切换到网站上传模式
  activeUploadType.value = 'website'
  websiteActiveStep.value = 1
}

// 处理确认导入
const handleConfirmImport = async () => {
  if (savedWebsites.value.length === 0) {
    ElMessage.warning('请先添加网址数据')
    return
  }
  
  try {
    importLoading.value = true
    
    // 获取有效的rgId和misId
    const rgId = rgIdFromRoute.value || 1
    const misId = misIdFromRoute.value || ''
    
    // 按照后端AddDocumentForm的结构准备请求数据
    const nameList = savedWebsites.value.map(item => item.name)
    const urlList = savedWebsites.value.map(item => item.website)
    const autoUpdateList = savedWebsites.value.map(item => item.autoUpdate || 0)
    
    // 准备导入数据
    const formData = {
      nameList,
      urlList,
      autoUpdateList
    }
    
    
    // 发送请求导入网站数据，将rgId和misId作为URL参数
    const response = await httpRequest.rawRequestPostAsJson(
      `${API_PATHS.ADD_BATCH_DOCUMENT}?rgId=${rgId}&misId=${misId}`, 
      formData
    )
    
    if (response.code === 0) {
      // 检查是否存在失败的URL列表
      if (response.data && response.data.failUrlList && response.data.failUrlList.length > 0) {
        // 有导入失败的项，显示弹窗
        showFailedUrlsDialog(response.data.failUrlList, nameList, urlList)
        
        // 仍然显示部分成功的消息
        ElMessage.warning('部分网站导入成功，部分导入失败')
        
        // 清空已成功导入的网站
        const failedUrls = new Set(response.data.failUrlList)
        savedWebsites.value = savedWebsites.value.filter(site => failedUrls.has(site.website))
        
        // 不跳转，留在当前页面让用户查看失败项
      } else {
        // 全部导入成功
        ElMessage.success(response.msg || '网站知识导入成功')
        
        // 清空保存的网站数据
        savedWebsites.value = []
        
        // 跳转到文档列表页面
        activeUploadType.value = 'document'
      }
    } else {
      // 导入失败
      ElMessage.error(response.msg || '导入失败，请稍后重试')
    }
  } catch (error) {
    console.error('导入网站知识失败:', error)
    ElMessage.error('导入失败，请稍后重试')
  } finally {
    importLoading.value = false
  }
}

// 添加显示失败URL列表的对话框函数
const failedUrlsDialogVisible = ref(false)
const failedUrlsList = ref([])

const showFailedUrlsDialog = (failUrls: string[], nameList: string[], urlList: string[]) => {
  // 构建失败项的详细信息列表
  failedUrlsList.value = failUrls.map(failUrl => {
    // 查找失败URL在原始urlList中的索引
    const index = urlList.indexOf(failUrl)
    return {
      name: index !== -1 ? nameList[index] : '未知',
      url: failUrl
    }
  })
  
  // 显示对话框
  failedUrlsDialogVisible.value = true
}

// 处理重试导入失败URL的逻辑
const handleRetryFailedUrls = () => {
  // 关闭对话框
  failedUrlsDialogVisible.value = false
  
  // 已经在savedWebsites中保留了失败的项，直接提示用户可以重新导入
  ElMessage.info('已准备好失败的URL，您可以再次点击"确认导入"按钮重试')
}

// 处理打开标签管理的事件
const handleOpenTagManagement = () => {
  // 跳转到主页面并通过查询参数指示打开标签管理
  router.push({
    path: '/corpus/index',
    query: {
      rgId: rgIdFromRoute.value,
      openTagManagement: 'true'
    }
  })
}

// 返回到index页面
const handleBackToIndex = () => {
  router.push({
    path: '/corpus/index',
    query: {
      rgId: rgIdFromRoute.value
    }
  })
}
</script>

<style lang="scss" scoped>
.knowledge-upload-container {
  max-width: 1200px;
  min-width: 320px;
  margin: 0 auto;
  padding: 20px;
  height: calc(100vh - 40px);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  overflow: hidden;
  
  .title-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    min-height: 40px;
    height: 40px;
    flex-shrink: 0;
    
    .back-button {
      margin-right: 12px;
      
      .el-button {
        display: flex;
        align-items: center;
        font-size: 15px;
        font-weight: 500;
        color: #409EFF;
        padding-left: 0;
        
        .el-icon {
          margin-right: 4px;
          font-size: 18px;
        }
        
        &:hover {
          color: #66b1ff;
          background: transparent;
        }
      }
    }
    
    .page-title {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin: 0;
      height: 36px;
      line-height: 36px;
      overflow: hidden;
      display: flex;
      align-items: center;
    }
  }
  
  .steps-selector {
    margin-bottom: 10px;
  }
  
  .steps-container {
    height: 60px;
    min-height: 60px;
    flex-shrink: 0;
  }
  
  .qa-upload-container {
    margin-top: 10px;
  }
  
  .upload-form-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    width: 100%;
    box-sizing: border-box;
    min-width: 385px;
  }
  
  .knowledge-process-layout {
    display: flex;
    gap: 20px;
    height: calc(100% - 150px);
    overflow: hidden;
    
    .knowledge-process-area, .knowledge-preview-area {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }
    
    .knowledge-process-area {
      flex: 4;
      max-width: 40%;
    }
    
    .knowledge-preview-area {
      flex: 6;
      max-width: 60%;
    }
  }

  .knowledge-learn-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
    height: 100%;
    min-height: 500px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .selected-knowledge-header {
      margin-bottom: 20px;
      position: sticky;
      top: 0;
      background-color: #fff;
      padding: 5px 0;
      z-index: 10;
      border-bottom: 1px solid #f0f2f5;
      padding-bottom: 10px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        color: #303133;
      }
    }
    
    .selected-knowledge-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
      flex: 1;
      overflow-y: auto;
      padding-right: 5px;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
      
      .knowledge-item {
        padding: 20px;
        background-color: #f5f7fa;
        border-radius: 8px;
        border: 1px solid #e4e7ed;
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .knowledge-item-header {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          
          .knowledge-item-number {
            background-color: #f0f2f5;
            color: #606266;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 14px;
            margin-right: 10px;
            font-weight: 500;
          }
          
          .knowledge-item-type {
            background-color: #e6f7ff;
            color: #1890ff;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 14px;
            margin-right: 10px;
            font-weight: 500;
          }
          
          .knowledge-item-chars {
            margin-left: auto;
            color: #909399;
            font-size: 13px;
            background-color: #f5f7fa;
            padding: 4px 8px;
            border-radius: 4px;
          }
        }
        
        .knowledge-item-path {
          color: #409EFF;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 12px;
          background-color: rgba(64, 158, 255, 0.1);
          padding: 6px 10px;
          border-radius: 4px;
          display: inline-block;
          border-left: 3px solid #409EFF;
        }
        
        .knowledge-item-content {
          color: #303133;
          font-size: 15px;
          line-height: 1.8;
          white-space: pre-wrap;
          background-color: #fff;
          padding: 15px;
          border-radius: 6px;
          border: 1px solid #ebeef5;
          overflow-x: auto;
        }
      }
    }
    
    .learn-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 25px;
      padding-top: 15px;
      border-top: 1px solid #f0f2f5;
    }
  }

  .website-processing-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
    height: 100%;
    overflow: hidden;
    
    h3 {
      font-size: 20px;
      color: #303133;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .website-data-list {
      margin-bottom: 30px;
      width: 100%;
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .empty-data {
        text-align: center;
        color: #909399;
        padding: 40px 0;
        background-color: #f5f7fa;
        border-radius: 4px;
      }
      
      .data-summary {
        margin-bottom: 15px;
        font-size: 14px;
        color: #606266;
      }
    }
    
    .website-process-actions {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 20px;
    }
  }

  .document-list-container {
    margin-top: 10px;
  }

  .document-list-layout {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
    
    :deep(.document-list-container) {
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 100%;
      overflow: hidden;
      
      .el-table {
        flex: 1;
        overflow: auto;
      }
    }
  }

  // 添加失败URL对话框的样式
  .failed-urls-container {
    padding: 10px 0;
    
    .failed-notice {
      color: #f56c6c;
      font-weight: 500;
      margin-bottom: 15px;
      font-size: 15px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
  }
}

@media (max-width: 500px) {
  .knowledge-upload-container {
    padding: 15px;
    
    .page-title {
      font-size: 20px;
      margin-bottom: 15px;
    }
    
    .upload-form-container {
      min-width: 320px;
      padding: 15px;
    }
  }
}

// FAQ步骤3的学习组件容器样式
.qa-step-container {
  height: calc(100% - 150px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
</style>
