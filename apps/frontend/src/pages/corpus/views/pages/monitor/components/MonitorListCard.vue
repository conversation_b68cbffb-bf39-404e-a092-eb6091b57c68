<template>
  <el-card class="monitor-card">
    <template #header>
      <div class="card-header">
        <span>群聊问题监控</span>
        <el-button type="primary" size="small" @click="$emit('add')">
          <el-icon><Plus /></el-icon>新增监控
        </el-button>
      </div>
    </template>
    
    <el-table :data="monitorList" v-loading="loading" border stripe>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="groupName" label="群聊名称" min-width="180" />
      <el-table-column prop="keyword" label="关键词" min-width="150" />
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '监控中' : '已停止' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="$emit('view', scope.row)">查看问题</el-button>
          <el-button link type="success" size="small" @click="$emit('toggle-status', scope.row)">
            {{ scope.row.status === 1 ? '停止监控' : '开始监控' }}
          </el-button>
          <el-button link type="danger" size="small" @click="$emit('delete', scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { MonitorItem } from '../types';

const props = defineProps<{
  monitorList: MonitorItem[];
  loading: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
}>();

const emit = defineEmits<{
  (e: 'size-change', size: number): void;
  (e: 'page-change', page: number): void;
  (e: 'add'): void;
  (e: 'view', item: MonitorItem): void;
  (e: 'toggle-status', item: MonitorItem): void;
  (e: 'delete', item: MonitorItem): void;
}>();

// 处理分页变化
const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleCurrentChange = (page: number) => {
  emit('page-change', page);
};
</script>

<style scoped lang="scss">
.monitor-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    span {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 