<template>
  <div class="statistics-page">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" icon="ArrowLeft">返回</el-button>
        <h2>监控统计信息</h2>
      </div>
      <div class="header-actions">
        <div class="header-info" v-if="currentMonitorTask">
          <span class="label">监控组：</span>
          <span class="value">{{ currentMonitorTask.monitoringGroupName }}</span>
          <el-tag v-if="currentMonitorTask.disabled" type="danger" effect="dark" size="small">已禁用</el-tag>
          <el-tag v-else type="success" effect="dark" size="small">正常</el-tag>
        </div>
        <el-button 
          type="primary" 
          @click="fetchStatistics" 
          class="stats-button"
          :icon="Histogram"
        >
          查询统计
        </el-button>
      </div>
    </div>

    <div class="time-range-selector">
      <span class="label">时间范围：</span>
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :shortcuts="dateShortcuts"
        :disabledDate="disabledDate"
        @change="handleDateRangeChange"
        style="width: 320px;"
      />
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-cards" v-loading="loading">
      <MonitorStats 
        ref="monitorStatsRef"
        :monitor-group-id="monitorGroupId"
        :time-range="inquiryTimeRange"
        :loading="loading"
        :question-types="currentMonitorTask?.questionTypes || []"
        :unanswered-count="unansweredTotal"
        @view-details="handleStatsViewDetails"
      />
    </div>

    <!-- 趋势图区域 -->
    <div class="trend-chart-container" v-loading="loading">
      <InquiryTrendChart
        ref="trendChartRef"
        :monitor-group-id="monitorGroupId"
        :start-time="inquiryTimeRange[0]"
        :end-time="inquiryTimeRange[1]"
      />
    </div>

    <!-- 数据可视化区域 -->
    <div class="charts-container">
      <!-- 问题类型饼图和群组柱状图区域 -->
      <div class="charts-row">
        <QuestionSolvingStatusChart
          ref="typeChartRef"
          :monitor-group-id="monitorGroupId"
          :start-time="inquiryTimeRange[0]"
          :end-time="inquiryTimeRange[1]"
        />
        <GroupBarChart
          ref="groupChartRef"
          :monitor-group-id="monitorGroupId"
          :start-time="inquiryTimeRange[0]"
          :end-time="inquiryTimeRange[1]"
        />
      </div>
    </div>

    <!-- 新增：总咨询数据详情对话框 -->
    <InquiryDetailDialog
      v-model="inquiryDetailDialogVisible"
      :inquiry-list="inquiryDetailList"
      :question-types="currentMonitorTask?.questionTypes || []"
      :loading="inquiryDetailLoading"
      :dx-group-statistics="dxGroupStatistics"
      :total="inquiryDetailTotal"
      :page-size="inquiryDetailPageSize"
      :current-page="inquiryDetailCurrentPage"
      @page-change="handleInquiryDetailPageChange"
    />
    
    <!-- 新增：问题类型统计详情对话框 -->
    <QuestionTypeDetailDialog
      v-model="questionTypeDialogVisible"
      :question-type-statistics="questionTypeStatistics"
      :loading="questionTypeLoading"
    />
    
    <!-- 新增：咨询人统计详情对话框 -->
    <InquirerDetailDialog
      v-model="inquirerDialogVisible"
      :inquirer-statistics="inquirerStatistics"
      :loading="inquirerLoading"
    />
    
    <!-- 未回答咨询列表对话框 -->
    <UnansweredQuestionDialog
      v-model="unansweredDialogVisible"
      :unanswered-list="unansweredList"
      :question-types="currentMonitorTask?.questionTypes || []"
      :loading="unansweredListLoading"
      :dx-group-statistics="dxGroupStatistics"
      :total="unansweredTotal"
      :page-size="unansweredPageSize"
      :current-page="unansweredCurrentPage"
      @page-change="handleUnansweredPageChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ArrowLeft, Histogram } from '@element-plus/icons-vue';
import { MonitorTask, MonitoringGroupItem, InquiryDetail } from '../types';
import { queryMonitoringGroups, queryUnansweredInquiries, queryInquiriesByTimeRangeWithoutExcel } from '../request';
import { getCurrentUser } from '@/shared/services/userService';
import MonitorStats from '../components/MonitorStats.vue';
import InquiryTrendChart from '../components/InquiryTrendChart.vue';
import QuestionSolvingStatusChart from '../components/QuestionSolvingStatusChart.vue';
import GroupBarChart from '../components/GroupBarChart.vue';
import UnansweredQuestionDialog from '../components/UnansweredQuestionDialog.vue';
import InquiryDetailDialog from '../components/InquiryDetailDialog.vue';
import QuestionTypeDetailDialog from '../components/QuestionTypeDetailDialog.vue';
import InquirerDetailDialog from '../components/InquirerDetailDialog.vue';

// 获取路由参数
const route = useRoute();
const router = useRouter();
const monitorGroupId = ref<number | null>(Number(route.query.id) || null);

// 从URL参数中获取时间范围
const urlStartTime = route.query.startTime ? Number(route.query.startTime) : null;
const urlEndTime = route.query.endTime ? Number(route.query.endTime) : null;

// 返回按钮点击事件
const goBack = () => {
  // 添加日志以便调试
  console.log('返回操作信息:', {
    monitorGroupId: monitorGroupId.value,
    startTime: inquiryTimeRange.value[0],
    endTime: inquiryTimeRange.value[1]
  });
  
  // 在本地存储中设置返回标记和时间戳
  localStorage.setItem('monitor_auto_query', 'true');
  localStorage.setItem('monitor_auto_query_timestamp', String(Date.now()));
  localStorage.setItem('monitor_groupId', String(monitorGroupId.value));
  
  // 返回主页时，携带原来的监控组ID
  router.push({ 
    path: '/corpus/monitor',
    query: { 
      groupId: monitorGroupId.value // 保持选中的监控组
    }
  });
};

// 当前用户MIS ID
const currentMisId = ref<string>('');

// 当前选中的监控任务
const currentMonitorTask = ref<MonitorTask | null>(null);

// 统计组件引用
const monitorStatsRef = ref();
const trendChartRef = ref();
const typeChartRef = ref();
const groupChartRef = ref();

// 时间范围相关
const timeRange = ref([
  urlStartTime ? new Date(urlStartTime) : new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000), // 使用URL参数或默认7天前
  urlEndTime ? new Date(urlEndTime) : new Date() // 使用URL参数或默认今天
]);
const inquiryTimeRange = ref<[number, number]>([
  urlStartTime || new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 7 * 24 * 60 * 60 * 1000, // 使用URL参数或默认7天前
  urlEndTime || new Date(new Date().setHours(23, 59, 59, 999)).getTime() // 使用URL参数或默认今天
]);

// 统计相关loading状态
const statisticsLoading = ref(false);
const inquiryDetailLoading = ref(false);
const unansweredListLoading = ref(false);

// 页面loading为三者之一为true时
const loading = computed(() => statisticsLoading.value || inquiryDetailLoading.value || unansweredListLoading.value);

// 未回答咨询列表相关
const unansweredDialogVisible = ref(false);
const unansweredList = ref<InquiryDetail[]>([]);
const unansweredCount = ref(0);
const dxGroupStatistics = ref<Array<{groupId: number, groupName: string, count: number}>>([]);

// 新增：总咨询数量详情弹窗相关状态
const inquiryDetailDialogVisible = ref(false);
const inquiryDetailList = ref<InquiryDetail[]>([]);
const inquiryDetailCurrentPage = ref(1);
const inquiryDetailPageSize = ref(5);
const inquiryDetailTotal = ref(0);

const questionTypeDialogVisible = ref(false);
const questionTypeStatistics = ref<Array<{typeName: string, count: number, typeId: number}>>([]);
const questionTypeLoading = ref(false);

const inquirerDialogVisible = ref(false);
const inquirerStatistics = ref<Array<{misId: string, orgId: string, orgName: string | null, count: number}>>([]);
const inquirerLoading = ref(false);

// 存储API响应数据
const responseData = ref<any>(null);
const reloading = ref(false);

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 日期禁用函数 - 限制最大选择范围为90天
const disabledDate = (time: Date) => {
  // 如果没有选择开始日期，则只禁用未来日期
  if (!timeRange.value || !timeRange.value[0]) {
    return time.getTime() > Date.now();
  }
  
  const startTime = timeRange.value[0].getTime();
  const endTime = time.getTime();
  
  // 禁用与所选开始日期相差超过90天的日期和未来日期
  // 根据用户选择的是开始日期还是结束日期来判断
  if (endTime < startTime) {
    // 如果选择的是开始日期，限制不能选择距离结束日期超过90天的日期
    if (timeRange.value[1]) {
      const maxRange = 90 * 24 * 60 * 60 * 1000;
      return timeRange.value[1].getTime() - endTime > maxRange;
    }
    return false;
  } else {
    // 如果选择的是结束日期，限制不能选择距离开始日期超过90天的日期或未来日期
    const maxRange = 90 * 24 * 60 * 60 * 1000;
    return endTime - startTime > maxRange || endTime > Date.now();
  }
};

// 分页相关状态
const unansweredCurrentPage = ref(1);
const unansweredPageSize = ref(5);
const unansweredTotal = ref(0);

// 获取未回答咨询分页数据
const fetchUnansweredList = async (page = 1, size = 10) => {
  unansweredListLoading.value = true;
  try {
    const params = {
      startTime: inquiryTimeRange.value[0],
      endTime: inquiryTimeRange.value[1],
      pageNum: page,
      pageSize: size,
      monitorGroupIds: [monitorGroupId.value]
    };
    const res = await queryUnansweredInquiries(params);
    if (res.code === 0 && res.data) {
      unansweredList.value = res.data.list || [];
      unansweredTotal.value = res.data.total || 0;
      unansweredCount.value = res.data.unansweredCount || 0;
      dxGroupStatistics.value = res.data.dxGroupStatistics || [];
    } else {
      unansweredList.value = [];
      unansweredTotal.value = 0;
      unansweredCount.value = 0;
    }
  } finally {
    unansweredListLoading.value = false;
  }
};

// 打开未回答咨询列表对话框
const openUnansweredDialog = () => {
  unansweredDialogVisible.value = true;
  fetchUnansweredList(unansweredCurrentPage.value, unansweredPageSize.value);
};

// 分页切换
const handleUnansweredPageChange = (page, size) => {
  unansweredCurrentPage.value = page;
  unansweredPageSize.value = size;
  fetchUnansweredList(page, size);
};

// 初始化函数
const init = async () => {
  try {
    // 获取当前用户信息
    const userInfo = await getCurrentUser();
    currentMisId.value = userInfo.login;
    
    // 加载监控组信息
    if (monitorGroupId.value) {
      await fetchMonitorTaskDetail(monitorGroupId.value);
    } else {
      ElMessage.warning('未指定监控组ID，请返回主页重新选择');
    }
  } catch (error) {
    console.error('初始化失败:', error);
    ElMessage.error('初始化失败，请刷新页面重试');
  }
};

// 获取监控任务详情
const fetchMonitorTaskDetail = async (groupId: number) => {
  statisticsLoading.value = true;
  try {
    const response = await queryMonitoringGroups({ misId: currentMisId.value });
    
    if (response?.code === 0 && response.success) {
      // 从API响应中找到指定ID的监控组
      const groups: MonitoringGroupItem[] = response.data?.monitoringGroups || [];
      const selectedGroup = groups.find(item => item.monitoringGroupId === groupId);
      
      if (selectedGroup) {
        // 使用API返回的问题类型数组
        const questionTypes = selectedGroup.questionTypes || [];
        
        // 构造监控任务对象
        currentMonitorTask.value = {
          monitoringGroupId: selectedGroup.monitoringGroupId,
          monitoringGroupName: selectedGroup.monitoringGroupName,
          monitoringGroupDesc: selectedGroup.monitoringGroupDesc,
          monitoringGroupOwner: selectedGroup.monitoringGroupOwnerArray || [],
          dxGroupIds: selectedGroup.dxGroupIdArray || [],
          monitoredOrgIds: selectedGroup.monitoredOrgIdArray || [],
          monitoredMisIds: selectedGroup.monitoredMisIdArray || [],
          keywords: selectedGroup.keywordsArray || [],
          questionTypes: questionTypes,
          monitoringTimeRangeType: selectedGroup.monitoringTimeRangeType,
          disabled: selectedGroup.status === 1 // 1表示禁用
        };
        
        // 获取统计数据
        fetchStatistics();
      } else {
        ElMessage.warning('未找到指定的监控组，请返回主页重新选择');
      }
    } else {
      ElMessage.error(response?.msg || '获取监控组信息失败');
    }
  } catch (error) {
    console.error('获取监控组信息失败:', error);
    ElMessage.error('获取监控组信息失败');
  } finally {
    statisticsLoading.value = false;
  }
};

// 日期范围变化处理
const handleDateRangeChange = (val: [Date, Date] | null) => {
  if (val) {
    // 设置开始时间为选中日期的0点
    const startDate = new Date(val[0]);
    startDate.setHours(0, 0, 0, 0);
    
    // 设置结束时间为选中日期的23:59:59.999
    const endDate = new Date(val[1]);
    endDate.setHours(23, 59, 59, 999);
    
    // 更新时间范围状态
    inquiryTimeRange.value = [startDate.getTime(), endDate.getTime()];
    
    // 使用setTimeout确保状态更新后再请求数据，避免重复触发
    setTimeout(() => {
      // 显式控制只获取一次统计数据
      fetchStatistics(true); // 传递true表示这是一次重置查询
    }, 0);
  }
};

// 查询统计数据
const fetchStatistics = async (resetReload: boolean = false) => {
  if (!monitorGroupId.value) {
    ElMessage.warning('请选择监控群组');
    return;
  }
  loading.value = true;
  try {
    const [statisticsRes, inquiryDetailRes, unansweredRes] = await Promise.all([
      monitorStatsRef.value?.getQueryStatisticsData(
        String(monitorGroupId.value),
        String(inquiryTimeRange.value[0]),
        String(inquiryTimeRange.value[1])
      ),
      queryInquiriesByTimeRangeWithoutExcel({
        startTime: inquiryTimeRange.value[0],
        endTime: inquiryTimeRange.value[1],
        pageNum: inquiryDetailCurrentPage.value,
        pageSize: inquiryDetailPageSize.value,
        monitorGroupIds: [monitorGroupId.value]
      }),
      queryUnansweredInquiries({
        startTime: inquiryTimeRange.value[0],
        endTime: inquiryTimeRange.value[1],
        pageNum: 1,
        pageSize: unansweredPageSize.value,
        monitorGroupIds: [monitorGroupId.value]
      })
    ]);

    // 处理统计数据
    if (statisticsRes && statisticsRes.data) {
      // 不再使用统计数据refreshWithData方法更新趋势图
      typeChartRef.value?.refreshWithData(statisticsRes.data);
      groupChartRef.value?.refreshWithData(statisticsRes.data);
      responseData.value = statisticsRes;
    }

    // 处理总咨询分页数据
    if (inquiryDetailRes.code === 0 && inquiryDetailRes.data) {
      inquiryDetailList.value = inquiryDetailRes.data.list || [];
      inquiryDetailTotal.value = inquiryDetailRes.data.total || 0;
    } else {
      inquiryDetailList.value = [];
      inquiryDetailTotal.value = 0;
    }

    // 处理未回答分页数据
    if (unansweredRes.code === 0 && unansweredRes.data) {
      unansweredList.value = unansweredRes.data.list || [];
      unansweredTotal.value = unansweredRes.data.total || 0;
      unansweredCount.value = unansweredRes.data.unansweredCount || 0;
      dxGroupStatistics.value = unansweredRes.data.dxGroupStatistics || [];
    } else {
      unansweredList.value = [];
      unansweredTotal.value = 0;
      unansweredCount.value = 0;
    }

    // 使用两个数据源更新趋势图
    if (inquiryDetailRes.code === 0 && unansweredRes.code === 0) {
      trendChartRef.value?.refreshWithTwoDataSources(
        inquiryDetailRes.data,
        unansweredRes.data
      );
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 分页查询总咨询详情
const fetchInquiryDetailList = async (page = 1, size = 5) => {
  inquiryDetailLoading.value = true;
  try {
    const params = {
      startTime: inquiryTimeRange.value[0],
      endTime: inquiryTimeRange.value[1],
      pageNum: page,
      pageSize: size,
      monitorGroupIds: [monitorGroupId.value]
    };
    const res = await queryInquiriesByTimeRangeWithoutExcel(params);
    if (res.code === 0 && res.data) {
      inquiryDetailList.value = res.data.list || [];
      inquiryDetailTotal.value = res.data.total || 0;
    } else {
      inquiryDetailList.value = [];
      inquiryDetailTotal.value = 0;
    }
  } finally {
    inquiryDetailLoading.value = false;
  }
};

const handleInquiryDetailPageChange = (page, size) => {
  inquiryDetailCurrentPage.value = page;
  inquiryDetailPageSize.value = size;
  fetchInquiryDetailList(page, size);
};

// 处理统计卡片详情点击
const handleStatsViewDetails = (eventData: { type: string, data: any }) => {
  switch (eventData.type) {
    case 'totalInquiries':
      inquiryDetailDialogVisible.value = true;
      break;
    case 'questionTypeCount':
      if (eventData.data.questionTypeCount > 0 && eventData.data.questionTypeStatistics && eventData.data.questionTypeStatistics.length > 0) {
        questionTypeStatistics.value = eventData.data.questionTypeStatistics;
        questionTypeDialogVisible.value = true;
      } else {
      ElMessage.info(`问题类型数量: ${eventData.data.questionTypeCount} 种`);
      }
      break;
    case 'totalInquirers':
      if (eventData.data.totalInquirers > 0 && eventData.data.inquirerStatistics && eventData.data.inquirerStatistics.length > 0) {
        inquirerStatistics.value = eventData.data.inquirerStatistics;
        inquirerDialogVisible.value = true;
      } else {
      ElMessage.info(`总咨询人数: ${eventData.data.totalInquirers} 人`);
      }
      break;
    case 'unansweredCount':
      // 直接用响应式变量
      if (unansweredTotal.value > 0 && unansweredList.value.length > 0) {
        unansweredDialogVisible.value = true;
      } else {
        ElMessage.info(`未回答咨询数量: ${unansweredTotal.value} 条`);
      }
      break;
    default:
      break;
  }
};

// 组件挂载后执行初始化
onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
.statistics-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 64px);
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 16px;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 500;
        color: #303133;
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .stats-button {
        background: linear-gradient(90deg, #409EFF, #1677FF);
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
        }
      }
    }
    
    .header-info {
      display: flex;
      align-items: center;
      
      .label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
      }
      
      .value {
        color: #303133;
        font-weight: 500;
        margin-right: 8px;
      }
    }
  }
  
  .time-range-selector {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    background-color: #fff;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    width: max-content;
    margin-left: auto;
    
    .label {
      margin-right: 12px;
      font-weight: 500;
      color: #606266;
    }
  }
  
  .stats-cards {
    margin-bottom: 24px;
  }
  
  .trend-chart-container {
    // 无需再设置margin-bottom，因为组件内部已经设置了
  }
  
  .charts-container {
    width: 100%;
  }
  
  .charts-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    gap: 24px;
  }
}
</style> 