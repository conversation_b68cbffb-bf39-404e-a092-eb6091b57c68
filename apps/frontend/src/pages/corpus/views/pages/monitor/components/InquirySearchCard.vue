<template>
  <el-card class="inquiry-card">
    <div class="search-area">
      <div class="search-title">咨询问题时间范围查询</div>
      <div class="search-form">
        <div class="time-group">
          <span class="time-label">时间范围</span>
          <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="x"
            :shortcuts="dateShortcuts"
            :disabled-date="disabledDate"
            @change="handleDateRangeChange"
            style="width: 350px;"
          />
        </div>
        <div class="button-group">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>查询
          </el-button>
          <el-tooltip
            content="生成并下载Excel报表"
            placement="top"
            effect="light"
          >
            <el-button type="success" :loading="loading" @click="handleGenerateAndDownload">
              <el-icon><Download /></el-icon>生成并导出Excel
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>
    
    <el-table :data="inquiryList" v-loading="loading" border stripe>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="questionerMisId" label="提问者" width="120" />
      <el-table-column prop="summarizedQuestion" label="问题摘要" min-width="220" show-overflow-tooltip />
      <el-table-column prop="messageCts" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.messageCts) }}
        </template>
      </el-table-column>
      <el-table-column prop="dxGroupName" label="来源群" width="150" show-overflow-tooltip>
        <template #default="scope">
          <el-link 
            type="primary" 
            :underline="false"
            @click="openGroupChat(scope.row.dxGroupId)"
          >
            {{ scope.row.dxGroupName || `未知群组(${scope.row.dxGroupId})` }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="questionType" label="问题类型" width="100">
        <template #default="scope">
          {{ getQuestionTypeName(scope.row.questionType) }}
        </template>
      </el-table-column>
      <el-table-column label="回应状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.replyStatus === 0 ? 'danger' : 'success'" size="small">
            {{ scope.row.replyStatus === 0 ? '无回应' : '有回应' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="showDetail(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
        background
      />
    </div>
    
    <!-- 问题详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="问题详情"
      width="800px"
      destroy-on-close
    >
      <div v-if="selectedInquiry" class="detail-container">
        <div class="detail-item">
          <div class="label">ID:</div>
          <div class="value">{{ selectedInquiry.id }}</div>
        </div>
        <div class="detail-item">
          <div class="label">提问者:</div>
          <div class="value">{{ selectedInquiry.questionerMisId }}</div>
        </div>
        <div class="detail-item">
          <div class="label">创建时间:</div>
          <div class="value">{{ formatDate(selectedInquiry.messageCts) }}</div>
        </div>
        <div class="detail-item">
          <div class="label">问题类型:</div>
          <div class="value">{{ getQuestionTypeName(selectedInquiry.questionType) }}</div>
        </div>
        <div class="detail-item">
          <div class="label">来源群:</div>
          <div class="value">
            <el-link 
              type="primary" 
              :underline="false"
              @click="openGroupChat(selectedInquiry.dxGroupId)"
            >
              {{ selectedInquiry.dxGroupName || `未知群组(${selectedInquiry.dxGroupId})` }}
            </el-link>
          </div>
        </div>
        <div class="detail-item">
          <div class="label">回应状态:</div>
          <div class="value">
            <el-tag :type="selectedInquiry.replyStatus === 0 ? 'danger' : 'success'" size="small">
              {{ selectedInquiry.replyStatus === 0 ? '无回应' : '有回应' }}
            </el-tag>
          </div>
        </div>
        <div class="detail-item full-width">
          <div class="label">原始消息:</div>
          <div class="value message-content">
            <pre class="formatted-text">{{ selectedInquiry.rawQuestionMsg }}</pre>
          </div>
        </div>
        <div class="detail-item full-width">
          <div class="label">问题摘要:</div>
          <div class="value message-content">
            <pre class="formatted-text">{{ selectedInquiry.summarizedQuestion }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, inject, PropType, defineExpose } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Download } from '@element-plus/icons-vue';
import { InquiryDetail, InquiryQueryParams, QuestionType } from '../types';
import { queryInquiriesByTimeRange, queryInquiriesByTimeRangeWithoutExcel } from '../request'; 
import { isDev, isTest } from '@/shared/utils/env-helper';

// 定义组件属性和事件
const props = defineProps({
  currentMonitorGroupId: {
    type: Number,
    default: 0
  },
  questionTypes: {
    type: Array as PropType<QuestionType[]>,
    default: () => []
  }
});

const emit = defineEmits([
  'update:inquiry-list',
  'time-range-change',
  'update-statistics',
  'hide-statistics'
]);

// 时间范围和搜索参数
const timeRange = ref<[number, number]>([
  new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 7 * 24 * 60 * 60 * 1000, // 7天前的0点
  new Date(new Date().setHours(23, 59, 59, 999)).getTime() // 今天的23:59:59
]);
const searchParams = reactive<InquiryQueryParams>({
  startTime: '',
  endTime: '',
  pageNum: 1,
  pageSize: 10,
  monitorGroupIds: []
});

// 查询结果数据
const inquiryList = ref<InquiryDetail[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const excelDownloadLink = ref<string>('');

// 详情对话框
const detailDialogVisible = ref(false);
const selectedInquiry = ref<InquiryDetail | null>(null);

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近1天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24);
      return [start, end];
    },
  },
  {
    text: '最近1周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近1个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

// 问题类型映射
const QUESTION_TYPE_MAP = computed<Record<number, string>>(() => {
  const map: Record<number, string> = {};
  
  // 如果有传入问题类型，则使用传入的问题类型
  if (props.questionTypes && props.questionTypes.length > 0) {
    props.questionTypes.forEach(type => {
      // 使用questionTypeId或id作为键
      const typeId = type.questionTypeId || type.id || 0;
      if (typeId > 0) {
        map[typeId] = type.typeName;
      }
    });
  }
  
  // 如果映射为空，则使用默认映射
  if (Object.keys(map).length === 0) {
    map[1] = '配送问题';
    map[2] = '商品问题';
    map[3] = '退款问题';
    map[4] = '其他问题';
  }
  
  return map;
});

// 获取问题类型名称
const getQuestionTypeName = (type: number): string => {
  return QUESTION_TYPE_MAP.value[type] || '未知类型';
};

// 格式化日期
const formatDate = (timestamp: number): string => {
  if (!timestamp) return '';
  
  try {
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return String(timestamp);
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  } catch (e) {
    return String(timestamp);
  }
};

// 一键生成并下载Excel
const handleGenerateAndDownload = async () => {
  if (!timeRange.value || timeRange.value.length !== 2) {
    ElMessage.warning('请选择时间范围');
    return;
  }
  
  // 更新搜索参数
  searchParams.startTime = String(timeRange.value[0]);
  searchParams.endTime = String(timeRange.value[1]);
  searchParams.pageNum = currentPage.value;
  searchParams.pageSize = pageSize.value;
  
  // 添加当前监控组ID
  if (props.currentMonitorGroupId) {
    searchParams.monitorGroupIds = [props.currentMonitorGroupId];
  }
  
  loading.value = true;
  excelDownloadLink.value = '';
  
  try {
    // 使用生成Excel的接口
    const res = await queryInquiriesByTimeRange(searchParams);
    
    if (res?.code === 0) {
      inquiryList.value = res.data?.list || [];
      total.value = res.data?.total || 0;
      
      // 检查下载链接是否存在且不为null (可能是空字符串)
      if (res.data && res.data.excelDownloadLink) {
        excelDownloadLink.value = res.data.excelDownloadLink;
        
        // 自动下载Excel
        const link = document.createElement('a');
        link.href = excelDownloadLink.value;
        link.target = '_blank';
        link.download = `咨询问题数据_${new Date().getTime()}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        if (total.value > 0) {
          ElMessage.success('Excel生成成功，下载已开始');
        } else {
          ElMessage.warning('未查询到数据，已生成空的Excel文件');
        }
      } else {
        // 接口返回成功但没有下载链接
        if (total.value > 0) {
          ElMessage.warning('查询成功，但Excel链接生成失败');
        } else {
          ElMessage.warning('未查询到数据');
        }
      }
      
      // 通知父组件
      emit('update:inquiry-list', inquiryList.value);
    } else {
      ElMessage.error(res?.msg || '操作失败');
      inquiryList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('生成Excel失败:', error);
    ElMessage.error('生成Excel失败，请稍后重试');
    inquiryList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 查询咨询问题
const handleSearch = async () => {
  if (!props.currentMonitorGroupId) {
    ElMessage.warning('请先选择监控组');
    return;
  }
  // 只在点击查询时重置页码
  if (currentPage.value !== 1) currentPage.value = 1;
  // 设置查询参数
  searchParams.startTime = timeRange.value[0]?.toString() || '';
  searchParams.endTime = timeRange.value[1]?.toString() || '';
  searchParams.pageNum = currentPage.value;
  searchParams.pageSize = pageSize.value;
  searchParams.monitorGroupIds = [props.currentMonitorGroupId];
  await fetchInquiryList();
  
  
  // 根据查询结果决定是否显示统计信息
  if (inquiryList.value.length > 0) {
    // 有数据时，触发统计更新事件
    emit('update-statistics', {
      startTime: searchParams.startTime,
      endTime: searchParams.endTime,
      monitorGroupIds: searchParams.monitorGroupIds
    });
  } else {
    // 没有数据时，触发隐藏统计事件
    emit('hide-statistics');
  }
  
};

// 查询咨询问题列表
const fetchInquiryList = async () => {
  loading.value = true;
  excelDownloadLink.value = '';
  
  try {
    // 使用不生成Excel的接口
    const res = await queryInquiriesByTimeRangeWithoutExcel(searchParams);
    
    if (res?.code === 0 && res.data) {
      inquiryList.value = res.data.list || [];
      total.value = res.data.total || 0;
      
      if (total.value > 0) {
        ElMessage.success('查询成功');
      } else {
        ElMessage.warning('未查询到数据');
      }
      
      // 通知父组件
      emit('update:inquiry-list', inquiryList.value);
    } else {
      ElMessage.error(res?.msg || '查询失败');
      inquiryList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('查询咨询问题失败:', error);
    ElMessage.error('查询失败，请稍后重试');
    inquiryList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  searchParams.pageSize = size;
  searchParams.pageNum = 1;
  fetchInquiryList();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  searchParams.pageNum = page;
  fetchInquiryList();
};

// 显示问题详情
const showDetail = (item: InquiryDetail) => {
  selectedInquiry.value = item;
  detailDialogVisible.value = true;
};

// 日期范围变化处理
const handleDateRangeChange = (val: [number, number] | null) => {
  if (val) {
    // 设置开始时间为选中日期的0点
    const startDate = new Date(val[0]);
    startDate.setHours(0, 0, 0, 0);
    
    // 设置结束时间为选中日期的23:59:59.999
    const endDate = new Date(val[1]);
    endDate.setHours(23, 59, 59, 999);
    
    timeRange.value = [startDate.getTime(), endDate.getTime()];
    
    // 不再触发时间范围变化事件，而是仅在查询按钮点击时触发
    // emit('time-range-change', timeRange.value);
  }
};

// 打开群组聊天
const openGroupChat = (groupId: number) => {
  if (!groupId) {
    ElMessage.warning('无效的群组ID');
    return;
  }
  
  // 根据环境决定使用哪个URL（三元运算符写法）
  const baseUrl = isDev() || isTest() 
    ? 'http://xm-web.it.test.sankuai.com/bridge/chat'
    : 'https://x.sankuai.com/bridge/chat';
  
  // 构建完整URL
  const url = `${baseUrl}?gid=${groupId}`;
  
  // 在新窗口中打开URL
  window.open(url, '_blank');
};

// 暴露方法给父组件
defineExpose({
  /**
   * 执行查询操作
   * 父组件可以通过ref调用此方法触发查询
   */
  executeQuery: () => {
    console.log('executeQuery called:', {
      timeRange: timeRange.value,
      startDate: new Date(timeRange.value[0]).toLocaleString(),
      endDate: new Date(timeRange.value[1]).toLocaleString(),
      currentMonitorGroupId: props.currentMonitorGroupId
    });
    handleSearch();
  }
});
</script>

<style scoped lang="scss">
.inquiry-card {
  margin-bottom: 20px;
  background-color: #fff;

  .search-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .search-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 15px;
      color: #303133;
    }
    
    .search-form {
      display: flex;
      align-items: center;
      width: 50%;
      justify-content: space-between;
      
      .time-group {
        display: flex;
        align-items: center;
        margin-left: 20px;
      }
      
      .time-label {
        margin-right: 10px;
        white-space: nowrap;
        min-width: 60px;
        display: inline-block;
        color: #606266;
      }
      
      .button-group {
        display: flex;
        gap: 10px;
        align-items: center;
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .detail-container {
    display: flex;
    flex-wrap: wrap;
    
    .detail-item {
      display: flex;
      margin-bottom: 16px;
      width: 50%;
      
      &.full-width {
        width: 100%;
      }
      
      .label {
        width: 80px;
        font-weight: bold;
        color: #606266;
      }
      
      .value {
        flex: 1;
        
        &.message-content {
          background-color: #f5f7fa;
          padding: 12px;
          border-radius: 4px;
          border: 1px solid #e4e7ed;
          max-height: 300px;
          overflow: auto;

          .formatted-text {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-all;
            font-family: inherit;
            font-size: inherit;
            line-height: 1.5;
            width: 100%;
            overflow-x: auto;
          }
        }
      }
    }
  }
}
</style> 