<template>
  <div class="group-bar-chart">
    <div class="chart-header">
      <h3 class="chart-title">群组消息分布</h3>
      <el-tooltip content="展示会话量最多的群组及其数量" placement="top">
        <el-icon><QuestionFilled /></el-icon>
      </el-tooltip>
    </div>
    <div ref="chartRef" class="chart-container" v-loading="loading"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { queryStatistics } from '../request';

// 注册 ECharts 组件
echarts.use([
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent,
  CanvasRenderer
]);

const props = defineProps<{
  monitorGroupId: number | null;
  startTime: number;
  endTime: number;
}>();

const chartRef = ref<HTMLElement | null>(null);
const loading = ref(false);
let chart: echarts.ECharts | null = null;

// 用于存储群组数据
const chartData = ref({
  groupIds: [] as string[],
  counts: [] as number[]
});

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', handleResize);
});

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  
  // 设置基本配置
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#eee',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      },
      padding: [10, 12],
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowBlur: 10
    },
    grid: {
      left: '3%',
      right: '8%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      },
      axisLabel: {
        margin: 10,
        showMaxLabel: true
      },
      max: function(value) {
        return Math.ceil(value.max * 1.1); // 扩大10%的空间
      }
    },
    yAxis: {
      type: 'category',
      data: ['暂无数据'],
      axisTick: {
        show: false
      },
      axisLabel: {
        formatter: function(value) {
          // 群ID可能很长，限制显示长度
          if (value.length > 14) {
            return value.substring(0, 14) + '...';
          }
          return value;
        },
        margin: 16,
        width: 120,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '会话数量',
        type: 'bar',
        barWidth: '60%',
        data: [0],
        itemStyle: {
          color: function(params) {
            // 为每个柱子设置不同的颜色
            const colorList = ['#5B8FF9', '#61DDAA', '#65789B', '#F6BD16', '#7262FD'];
            return colorList[params.dataIndex % colorList.length];
          },
          borderRadius: [0, 4, 4, 0]
        },
        label: {
          show: true,
          position: 'right',
          color: '#666',
          formatter: function(params) {
            return params.value;
          },
          padding: [0, 0, 0, 5]
        }
      }
    ]
  };
  
  chart.setOption(option);
};

// 更新图表
const updateChart = () => {
  if (chart) {
    chart.setOption({
      yAxis: {
        data: chartData.value.groupIds
      },
      series: [
        {
          data: chartData.value.counts
        }
      ]
    });
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

// 获取群组数据
const fetchGroupData = async () => {
  if (!props.monitorGroupId || !props.startTime || !props.endTime) {
    console.warn('柱状图数据获取条件不满足', props);
    return;
  }
  
  loading.value = true;
  
  try {
    const response = await queryStatistics({
      startTime: String(props.startTime),
      endTime: String(props.endTime),
      monitorGroupIds: [String(props.monitorGroupId)]
    });
    
    if (response && response.code === 0 && response.success && response.data) {
      // 从API响应中提取群组数据
      if (response.data.dxGroupStatistics && response.data.dxGroupStatistics.length > 0) {
        const groupIds: string[] = [];
        const counts: number[] = [];
        
        // 按数量排序并取前5个
        const sortedStats = [...response.data.dxGroupStatistics]
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);
        
        sortedStats.forEach(item => {
          // 使用群组名称（如果有）+ 群组ID
          const groupName = item.groupName ? 
            `${item.groupName}` : 
            `群ID: ${item.groupId}`;
          groupIds.push(groupName);
          counts.push(item.count);
        });
        
        // 反转数组以便在柱状图中从上到下显示（从大到小）
        groupIds.reverse();
        counts.reverse();
        
        // 更新图表数据
        chartData.value = {
          groupIds,
          counts
        };
      } else {
        // 如果没有数据，显示空状态
        chartData.value = {
          groupIds: ['暂无数据'],
          counts: [0]
        };
      }
      
      // 更新图表
      updateChart();
    } else {
      console.error('获取群组数据失败', response);
      ElMessage.error(response?.msg || '获取群组数据失败');
    }
  } catch (error) {
    console.error('获取群组数据异常:', error);
    ElMessage.error('获取群组数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 监听属性变化，重新获取数据
watch(
  () => [props.monitorGroupId, props.startTime, props.endTime],
  () => {
    // 不再主动请求数据，由父组件通过refreshWithData方法提供数据
    // fetchGroupData(); // 注释掉自动触发的请求
  },
  { deep: true }
);

// 使用预先获取的数据刷新图表
const refreshWithData = async (data: any) => {
  if (!data) {
    console.warn('群组图表没有收到有效数据');
    return;
  }
  
  loading.value = true;
  
  try {
    // 从传入的数据中提取群组数据
    if (data.dxGroupStatistics && data.dxGroupStatistics.length > 0) {
      const groupIds: string[] = [];
      const counts: number[] = [];
      
      // 按数量排序并取前5个
      const sortedStats = [...data.dxGroupStatistics]
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
      
      sortedStats.forEach(item => {
        // 使用群组名称（如果有）+ 群组ID
        const groupName = item.groupName ? 
          `${item.groupName}` : 
          `群ID: ${item.groupId}`;
        groupIds.push(groupName);
        counts.push(item.count);
      });
      
      // 反转数组以便在柱状图中从上到下显示（从大到小）
      groupIds.reverse();
      counts.reverse();
      
      // 更新图表数据
      chartData.value = {
        groupIds,
        counts
      };
    } else {
      // 如果没有数据，显示空状态
      chartData.value = {
        groupIds: ['暂无数据'],
        counts: [0]
      };
    }
    
    // 更新图表
    updateChart();
  } catch (error) {
    console.error('群组图表使用共享数据更新失败:', error);
  } finally {
    loading.value = false;
  }
};

// 暴露刷新方法
defineExpose({
  refresh: fetchGroupData,
  refreshWithData
});
</script>

<style scoped lang="scss">
.group-bar-chart {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px 16px 24px 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-top: 20px;
  width: calc(50% - 12px);
  
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .chart-title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
      margin: 0;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: linear-gradient(to bottom, #5B8FF9, #85a5ff);
        border-radius: 2px;
      }
    }
    
    .el-icon {
      color: #909399;
      cursor: pointer;
      font-size: 18px;
      
      &:hover {
        color: #5B8FF9;
      }
    }
  }
  
  .chart-container {
    width: 100%;
    height: 320px;
    border-radius: 4px;
    overflow: hidden;
  }
}
</style> 