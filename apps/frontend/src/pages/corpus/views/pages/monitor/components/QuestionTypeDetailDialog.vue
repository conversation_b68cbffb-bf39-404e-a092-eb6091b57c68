<template>
  <div class="question-type-detail-dialog">
    <el-dialog
      v-model="dialogVisible"
      title="问题类型统计"
      width="60%"
    >
      <div class="question-type-chart">
        <el-row :gutter="20">
          <el-col :span="12">
            <div ref="pieChartRef" class="pie-chart"></div>
          </el-col>
          <el-col :span="12">
            <el-table
              :data="questionTypeStatistics"
              style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              v-loading="loading"
            >
              <el-table-column type="index" width="50" label="#" />
              <el-table-column label="问题类型" prop="typeName" min-width="120" />
              <el-table-column label="数量" prop="count" width="80">
                <template #default="{ row }">
                  <span class="count-text">{{ row.count }}</span>
                </template>
              </el-table-column>
              <el-table-column label="占比" width="100">
                <template #default="{ row }">
                  <el-progress 
                    :percentage="calculatePercentage(row.count)" 
                    :color="getColorForIndex(row.typeId)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, onMounted, watch, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册 ECharts 组件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
]);

// 定义props
const props = defineProps<{
  modelValue: boolean;
  questionTypeStatistics: Array<{
    typeName: string;
    count: number;
    typeId: number;
  }>;
  loading?: boolean;
}>();

// 定义事件
const emit = defineEmits(['update:modelValue']);

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 图表相关
const pieChartRef = ref<HTMLElement | null>(null);
let pieChart: echarts.ECharts | null = null;

// 计算总数量
const totalCount = computed(() => {
  return props.questionTypeStatistics.reduce((sum, item) => sum + item.count, 0);
});

// 计算百分比
const calculatePercentage = (count: number): number => {
  if (totalCount.value === 0) return 0;
  return Math.round((count / totalCount.value) * 100);
};

// 获取颜色
const getColorForIndex = (index: number): string => {
  const colors = [
    '#5B8FF9', '#8FD460', '#F6BD16', '#5D7092', '#E8684A', 
    '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3'
  ];
  return colors[index % colors.length];
};

// 初始化图表
const initChart = () => {
  if (!pieChartRef.value) return;
  
  pieChart = echarts.init(pieChartRef.value);
  updateChart();
  
  window.addEventListener('resize', handleResize);
};

// 更新图表
const updateChart = () => {
  if (!pieChart) return;
  
  const seriesData = props.questionTypeStatistics.map((item, index) => ({
    value: item.count,
    name: item.typeName,
    itemStyle: { color: getColorForIndex(item.typeId) }
  }));
  
  pieChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: props.questionTypeStatistics.map(item => item.typeName)
    },
    series: [
      {
        name: '问题类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: seriesData
      }
    ]
  });
};

// 处理窗口大小变化
const handleResize = () => {
  if (pieChart) {
    pieChart.resize();
  }
};

// 监听数据变化
watch(
  () => props.questionTypeStatistics,
  () => {
    if (pieChart) {
      updateChart();
    }
  },
  { deep: true }
);

// 监听弹窗显示状态
watch(
  () => dialogVisible.value,
  (val) => {
    if (val) {
      // 延迟初始化图表，确保DOM已经渲染
      setTimeout(() => {
        if (!pieChart && pieChartRef.value) {
          initChart();
        } else if (pieChart) {
          pieChart.resize();
          updateChart();
        }
      }, 100);
    }
  }
);

onMounted(() => {
  if (dialogVisible.value) {
    // 延迟初始化图表，确保DOM已经渲染
    setTimeout(() => {
      initChart();
    }, 100);
  }
});

onBeforeUnmount(() => {
  if (pieChart) {
    pieChart.dispose();
    pieChart = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped lang="scss">
.question-type-detail-dialog {
  .pie-chart {
    width: 100%;
    height: 300px;
  }
  
  .count-text {
    font-weight: 600;
    color: #303133;
  }
}
</style> 