// 咨询问题查询参数接口
export interface InquiryQueryParams {
  startTime: string;
  endTime: string;
  pageNum: number;
  pageSize: number;
  /**
   * 监控组ID列表
   */
  monitorGroupIds?: number[];
}

// 咨询问题明细接口
export interface InquiryDetail {
  id: number;
  monitorGroupId: number;
  questionerMisId: string;
  questionerEmpId: string | null;
  questionerOrgId: string;
  questionerOrgName: string | null;
  rawQuestionMsg: string;
  summarizedQuestion: string;
  questionType: number;
  replyStatus: number; // 回应状态：0-有回应，1-无回应
  fromMessageId: number;
  messageCts: number;
  dxGroupId: number;
  dxGroupName?: string; // 来源群名称
  ctime: number;
  utime: number;
}

// 分页结果接口
export interface PageResult<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  totalPages: number;
}

// 咨询问题查询结果接口
export interface InquiryQueryResult {
  total: number;
  totalPage: number;
  pageSize: number;
  currentPage: number;
  list: InquiryDetail[];
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
  success: boolean;
}

// 问题类型接口
export interface QuestionType {
  id?: number;
  questionTypeId: number;
  monitoringGroupId?: number;
  sortOrder?: number; // 问题类型排序顺序
  typeName: string;
  typeDesc: string;
}

// 监控任务接口
export interface MonitorTask {
  monitoringGroupId?: string | number;
  monitoringGroupName: string; // 监控组名称
  monitoringGroupDesc: string; // 监控组描述
  monitoringGroupOwner: string[]; // 监控组负责人
  dxGroupIds: string[]; // 大象群ID
  monitoredOrgIds: string[]; // 监控组织ID
  monitoredMisIds: string[]; // 监控用户MIS
  keywords: string[]; // 关键词
  questionTypes: QuestionType[]; // 问题类型
  monitoringTimeRangeType?: number; // 监控时间范围类型 0:7天, 1:14天, 2:30天
  status?: number; // 任务状态 0-启用，1-停用
  disabled?: boolean; // 前端使用的禁用标志，提交时转换为status
}

// 监控任务创建请求接口
export interface CreateMonitorTaskRequest extends MonitorTask {}

// 监控任务创建响应接口
export interface CreateMonitorTaskResponse {
  monitoringGroupId: number;
  success: boolean;
}

// 监控组列表项接口
export interface MonitoringGroupItem {
  monitoringGroupId: number;
  monitoringGroupName: string;
  monitoringGroupDesc: string;
  monitoringGroupOwner: string;
  dxGroupIds: string;
  monitoredOrgIds: string | null;
  monitoredMisIds: string | null;
  keywords: string;
  monitoringTimeRangeType: number;
  status: number; // 0-正常, 1-禁用
  createTime: string;
  updateTime: string;
  dxGroupIdArray: number[];
  monitoringGroupOwnerArray: string[];
  monitoredOrgIdArray: string[];
  monitoredMisIdArray: string[];
  keywordsArray: string[];
}

// 查询监控组请求参数
export interface QueryMonitoringGroupsParams {
  misId: string;
}

// 统计数据查询参数接口
export interface StatisticsQueryParams {
  startTime: string;
  endTime: string;
  monitorGroupIds: string[];
}

// 统计数据返回结果接口
export interface StatisticsResult {
  unansweredList: any[]; // 未回答的咨询列表
  questionTypeCount: number; // 问题类型数量
  totalInquirers: number; // 总咨询人数
  totalInquiries: number; // 总咨询数量
  unansweredCount: number; // 未回答咨询数量
  questionTypeStatistics?: Array<{
    typeName: string;
    count: number;
    typeId: number;
  }>; // 问题类型统计
  dxGroupStatistics?: Array<{
    groupName: string;
    groupId: number;
    count: number;
  }>; // 群组统计
  inquiryDetails?: InquiryDetail[]; // 咨询详情列表
  inquirerStatistics?: Array<{
    misId: string;
    orgId: string;
    orgName: string | null;
    count: number;
  }>; // 咨询人统计
}

// 组织搜索结果项接口
export interface OrgSearchResultItem {
  tenantId: number;
  source: string;
  orgId: string;
  name: string;
  enName: string;
  categoryId: number;
  categoryName: string;
  level: number | null;
  costCenterId: string;
  headEmpId: string;
  headName: string;
  headJobNumber: string;
  headMis: string;
  hrbpEmpId: string;
  hrbpName: string;
  hrbpJobNumber: string;
  hrbpMis: string;
  parentId: string;
  parentName: string;
  orgPath: string;
  orgNamePath: string;
  bgId: string;
  bgName: string;
  bgSub1Id: string;
  bgSub1Name: string;
  cityId: string | null;
  cityName: string | null;
  mirrorOrgId: string | null;
  mirrorOrgName: string | null;
  type: number;
  sort: number;
  status: number;
  snapshot: any;
  currentLevel: number;
  validDate: string;
}