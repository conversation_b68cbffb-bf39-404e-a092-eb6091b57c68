<template>
  <div class="inquiry-detail-dialog">
    <el-dialog
      v-model="dialogVisible"
      title="咨询详情列表"
      width="90%"
    >
      <el-table
        :data="inquiryList"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        v-loading="loading"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column label="提问者" prop="questionerMisId" width="120" />
        <el-table-column label="组织信息" min-width="180">
          <template #default="{ row }">
            <div v-if="getOrgInfo(row.questionerOrgId)" class="org-info">
              <span class="org-name">{{ getOrgInfo(row.questionerOrgId)?.name }}</span>
              <span class="org-path">{{ getOrgInfo(row.questionerOrgId)?.path }}</span>
            </div>
            <div v-else-if="row.questionerOrgId" class="org-info loading-org">
              <span v-if="orgLoadingMap[row.questionerOrgId]">加载中...</span>
              <span v-else class="org-id">{{ row.questionerOrgId }}</span>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="问题内容" min-width="280">
          <template #default="{ row }">
            <div class="question-content">
              {{ row.summarizedQuestion }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="来源群" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <el-link 
              type="primary" 
              :underline="false"
              @click="openGroupChat(row.dxGroupId)"
            >
              {{ getGroupName(row.dxGroupId, row.dxGroupName) }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="问题类型" width="120">
          <template #default="{ row }">
            <el-tag type="primary">{{ getQuestionTypeName(row.questionType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="回应状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.replyStatus === 0 ? 'danger' : 'success'" size="small">
              {{ scope.row.replyStatus === 0 ? '无回应' : '有回应' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="提问时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.messageCts) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[5, 10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>

      <!-- 问题详情弹窗 -->
      <el-dialog
        v-model="detailDialogVisible"
        title="问题详情"
        width="800px"
        destroy-on-close
      >
        <div v-if="currentQuestion" class="detail-container">
          <div class="detail-item">
            <div class="label">ID:</div>
            <div class="value">{{ currentQuestion.id }}</div>
          </div>
          <div class="detail-item">
            <div class="label">提问者:</div>
            <div class="value">{{ currentQuestion.questionerMisId }}</div>
          </div>
          <div class="detail-item">
            <div class="label">组织信息:</div>
            <div class="value">
              <div v-if="getOrgInfo(currentQuestion.questionerOrgId)" class="org-detail-info">
                <div>{{ getOrgInfo(currentQuestion.questionerOrgId)?.name }}</div>
                <div class="org-path-detail">{{ getOrgInfo(currentQuestion.questionerOrgId)?.path }}</div>
              </div>
              <span v-else>{{ currentQuestion.questionerOrgId || '-' }}</span>
            </div>
          </div>
          <div class="detail-item">
            <div class="label">创建时间:</div>
            <div class="value">{{ formatDateTime(currentQuestion.messageCts) }}</div>
          </div>
          <div class="detail-item">
            <div class="label">问题类型:</div>
            <div class="value">
              <el-tag type="primary">{{ getQuestionTypeName(currentQuestion.questionType) }}</el-tag>
            </div>
          </div>
          <div class="detail-item">
            <div class="label">来源群:</div>
            <div class="value">
              <el-link 
                type="primary" 
                :underline="false"
                @click="openGroupChat(currentQuestion.dxGroupId)"
              >
                {{ getGroupName(currentQuestion.dxGroupId, currentQuestion.dxGroupName) }}
              </el-link>
            </div>
          </div>
          <div class="detail-item">
            <div class="label">回应状态:</div>
            <div class="value">
              <el-tag :type="currentQuestion.replyStatus === 1 ? 'success' : 'danger'">
                {{ currentQuestion.replyStatus === 1 ? '已回复' : '未回复' }}
              </el-tag>
            </div>
          </div>
          <div class="detail-item full-width">
            <div class="label">原始消息:</div>
            <div class="value message-content">
              <pre class="formatted-text">{{ currentQuestion.rawQuestionMsg }}</pre>
            </div>
          </div>
          <div class="detail-item full-width">
            <div class="label">问题摘要:</div>
            <div class="value message-content">
              <pre class="formatted-text">{{ currentQuestion.summarizedQuestion }}</pre>
            </div>
          </div>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { InquiryDetail, QuestionType } from '../types';
import { isDev, isTest } from '@/shared/utils/env-helper';
import { queryOrgById, formatOrgNamePath } from '../request';

// 定义props
const props = defineProps<{
  modelValue: boolean;
  inquiryList: InquiryDetail[];
  questionTypes: QuestionType[];
  loading?: boolean;
  dxGroupStatistics?: Array<{groupId: number, groupName: string, count: number}>;
  total?: number;
  pageSize?: number;
  currentPage?: number;
}>();

// 定义事件
const emit = defineEmits(['update:modelValue', 'page-change']);

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 详情弹窗
const detailDialogVisible = ref(false);
const currentQuestion = ref<InquiryDetail | null>(null);

// 群组名称映射
const groupNameMap = computed(() => {
  const map: Record<number, string> = {};
  if (props.dxGroupStatistics && props.dxGroupStatistics.length > 0) {
    props.dxGroupStatistics.forEach(group => {
      map[group.groupId] = group.groupName;
    });
  }
  return map;
});

// 获取群组名称
const getGroupName = (groupId: number, defaultName?: string | null): string => {
  if (defaultName) return defaultName;
  return groupNameMap.value[groupId] || `未知群组(${groupId})`;
};

// 查看详情
const handleViewDetail = (question: InquiryDetail) => {
  currentQuestion.value = question;
  detailDialogVisible.value = true;
};

// 格式化日期显示
const formatDateTime = (timestamp: number): string => {
  if (!timestamp) return '无时间记录';
  
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

// 获取问题类型名称
const getQuestionTypeName = (typeId: number): string => {
  const questionType = props.questionTypes.find(type => type.questionTypeId === typeId);
  return questionType?.typeName || `未知类型`;
};

// 打开群组聊天
const openGroupChat = (groupId: number) => {
  if (!groupId) {
    ElMessage.warning('无效的群组ID');
    return;
  }
  
  // 根据环境决定使用哪个URL（三元运算符写法）
  const baseUrl = isDev() || isTest() 
    ? 'http://xm-web.it.test.sankuai.com/bridge/chat'
    : 'https://x.sankuai.com/bridge/chat';
  
  // 构建完整URL
  const url = `${baseUrl}?gid=${groupId}`;
  
  // 在新窗口中打开URL
  window.open(url, '_blank');
};

// 组织信息缓存
interface OrgInfo {
  name: string;
  path: string;
}

const orgInfoMap = ref<Record<string, OrgInfo>>({});
const orgLoadingMap = ref<Record<string, boolean>>({});

// 加载组织信息
const loadOrgInfo = async (orgId: string) => {
  if (!orgId || orgInfoMap.value[orgId] || orgLoadingMap.value[orgId]) return;
  
  orgLoadingMap.value[orgId] = true;
  
  try {
    const res = await queryOrgById(orgId);
    if (res && res.code === 0 && res.data) {
      orgInfoMap.value[orgId] = {
        name: res.data.name || '未知组织',
        path: formatOrgNamePath(res.data.orgNamePath || '')
      };
    }
  } catch (error) {
    console.error(`获取组织[${orgId}]信息失败:`, error);
  } finally {
    orgLoadingMap.value[orgId] = false;
  }
};

// 获取组织信息
const getOrgInfo = (orgId: string): OrgInfo | null => {
  if (!orgId) return null;
  
  if (!orgInfoMap.value[orgId] && !orgLoadingMap.value[orgId]) {
    loadOrgInfo(orgId);
  }
  
  return orgInfoMap.value[orgId] || null;
};

// 监听对话框显示状态
watch(() => dialogVisible.value, (newVal) => {
  if (newVal && props.inquiryList.length > 0) {
    loadAllOrgInfo();
  }
});

// 监听列表数据变化
watch(() => props.inquiryList, (newVal) => {
  if (dialogVisible.value && newVal.length > 0) {
    loadAllOrgInfo();
  }
}, { deep: true });

// 加载所有组织信息
const loadAllOrgInfo = () => {
  props.inquiryList.forEach(item => {
    if (item.questionerOrgId) {
      loadOrgInfo(item.questionerOrgId);
    }
  });
};

// 在组件挂载时预加载组织信息
onMounted(() => {
  if (dialogVisible.value && props.inquiryList.length > 0) {
    loadAllOrgInfo();
  }
});

const pageSize = computed(() => props.pageSize || 5);
const currentPage = computed(() => props.currentPage || 1);
const total = computed(() => props.total || 0);

const handlePageChange = (page: number) => {
  emit('page-change', page, pageSize.value);
};
const handleSizeChange = (size: number) => {
  emit('page-change', 1, size); // 切换每页条数时重置到第一页
};
</script>

<style scoped lang="scss">
.inquiry-detail-dialog {
  .question-content {
    max-height: 120px;
    overflow-y: auto;
    line-height: 1.5;
    text-align: left;
  }
  
  .detail-container {
    display: flex;
    flex-wrap: wrap;
    
    .detail-item {
      display: flex;
      margin-bottom: 16px;
      width: 50%;
      
      &.full-width {
        width: 100%;
      }
      
      .label {
        width: 80px;
        font-weight: bold;
        color: #606266;
      }
      
      .value {
        flex: 1;
        
        &.message-content {
          background-color: #f5f7fa;
          padding: 12px;
          border-radius: 4px;
          border: 1px solid #e4e7ed;
          max-height: 300px;
          overflow: auto;
          
          .formatted-text {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-all;
            font-family: inherit;
            font-size: inherit;
            line-height: 1.5;
            width: 100%;
          }
        }
      }
    }
  }
  
  .org-info {
    display: flex;
    flex-direction: column;
    line-height: 1.5;
    
    .org-name {
      font-weight: 500;
      color: #333;
    }
    
    .org-path {
      font-size: 12px;
      color: #909399;
    }
    
    .org-id {
      color: #909399;
      font-size: 12px;
    }
  }
  
  .loading-org {
    color: #909399;
    font-style: italic;
    font-size: 12px;
  }
  
  .org-detail-info {
    line-height: 1.6;
    
    .org-path-detail {
      font-size: 13px;
      color: #909399;
      margin-top: 4px;
    }
  }
}
</style> 