import httpRequest from '../../../../../../utils/httpRequest';
import { InquiryQueryParams, ApiResponse, InquiryQueryResult, CreateMonitorTaskRequest, CreateMonitorTaskResponse, QueryMonitoringGroupsParams, MonitoringGroupItem, StatisticsQueryParams } from '../types';

// 按时间范围查询咨询问题（生成Excel）
export const queryInquiriesByTimeRange = async (params: InquiryQueryParams) => {
  const url = '/inquiry/monitoring/queryByTimeRange';
  return httpRequest.rawRequestPostAsJson(url, params);
};

// 按时间范围查询咨询问题（不生成Excel）
export const queryInquiriesByTimeRangeWithoutExcel = async (params: InquiryQueryParams) => {
  const url = '/inquiry/monitoring/queryByTimeRangeWithoutExcel';
  return httpRequest.rawRequestPostAsJson(url, params);
};

/**
 * 创建监控任务
 * @param params 创建监控任务的请求参数
 * @returns API响应
 */
export const createMonitorTask = async (params: CreateMonitorTaskRequest): Promise<any> => {
  try {
    const url = '/inquiry/monitoring/addMonitoringGroup';
    return await httpRequest.rawRequestPostAsJson(url, params);
  } catch (error) {
    console.error('创建监控任务失败:', error);
    throw error;
  }
};

/**
 * 更新监控任务
 * @param params 更新监控任务的请求参数
 * @returns API响应
 */
export const updateMonitorTask = async (params: any): Promise<any> => {
  try {
    const url = '/inquiry/monitoring/updateMonitoringGroup';
    return await httpRequest.rawRequestPostAsJson(url, params);
  } catch (error) {
    console.error('更新监控任务失败:', error);
    throw error;
  }
};

/**
 * 删除监控任务
 * @param monitoringGroupId 监控任务ID
 * @returns API响应
 */
export const deleteMonitorTask = async (monitoringGroupId: number): Promise<any> => {
  try {
    const url = '/inquiry/monitoring/deleteMonitoringGroup';
    return await httpRequest.rawRequestPostAsForm(url, {
      monitoringGroupId
    });
  } catch (error) {
    console.error('删除监控任务失败:', error);
    throw error;
  }
};

/**
 * 查询用户的监控组列表
 * @param params 查询参数，包含用户misId
 * @returns 监控组列表
 */
export const queryMonitoringGroups = async (params: QueryMonitoringGroupsParams): Promise<any> => {
  try {
    const url = '/inquiry/monitoring/queryMonitoringGroupsByMisId';
    // 根据Postman成功的请求格式，构造form-data格式的数据
    return await httpRequest.rawRequestPostAsForm(url, {
      misId: [params.misId]
    });
  } catch (error) {
    console.error('查询监控组列表失败:', error);
    throw error;
  }
};

/**
 * 查询统计数据
 * @param params 查询参数
 * @returns 统计数据结果
 */
export const queryStatistics = (params: StatisticsQueryParams) => {
  return httpRequest.rawRequestPostAsJson('/inquiry/statistics/queryStatistics', params);
};

/**
 * 根据组织路径查询组织
 * @param orgPath 组织路径，格式如"美团/核心本地商业/业务研发平台"
 * @returns 组织详情信息
 */
export const queryOrgByNamePath = async (orgPath: string): Promise<any> => {
  try {
    // 处理用户输入的组织路径
    // 1. 添加"公司"前缀
    // 2. 将"/"替换为"-"
    let formattedPath = orgPath;
    if (!formattedPath.startsWith('公司')) {
      formattedPath = '公司-' + formattedPath;
    }
    // 替换分隔符
    formattedPath = formattedPath.replace(/\//g, '-');
    
    const url = '/org/queryByNamePath';
    return await httpRequest.rawRequestGet(url, { orgNamePath: formattedPath });
  } catch (error) {
    console.error('通过组织路径查询组织失败:', error);
    throw error;
  }
};

/**
 * 根据组织ID查询组织详情
 * @param id 组织ID
 * @returns 组织详情
 */
export const queryOrgById = async (id: string): Promise<any> => {
  try {
    const url = '/org/queryOrgById';
    const res = await httpRequest.rawRequestGet(url, { id }) as any;
    
    return res;
  } catch (error) {
    console.error('获取组织详情失败:', error);
    throw error;
  }
};

/**
 * 获取灰度测试配置
 * @returns 灰度配置信息
 */
export const getGrayscaleConfig = async () => {
  try {
    const res = await httpRequest.rawRequestGet('/api/test/grayscale-config');
    return res;
  } catch (error) {
    console.error('获取灰度配置失败:', error);
    return {
      code: -1,
      message: '获取灰度配置失败',
      data: {
        config: {
          monitorGroupIds: [],
          misIds: []
        }
      }
    };
  }
};

/**
 * 格式化组织路径显示
 * 1. 移除"公司-"前缀
 * 2. 将"-"替换为"/"
 * @param orgNamePath 组织全路径，格式如"公司-部门-团队"
 * @returns 处理后的组织路径，格式如"部门/团队"
 */
export const formatOrgNamePath = (orgNamePath: string): string => {
  if (!orgNamePath) return '';
  
  // 移除"公司-"前缀
  let formattedPath = orgNamePath;
  if (formattedPath.startsWith('公司-')) {
    formattedPath = formattedPath.substring(3);
  }
  
  // 将"-"替换为"/"
  formattedPath = formattedPath.replace(/-/g, '/');
  
  return formattedPath;
};

/**
 * 分页查询未回答咨询
 * @param params 查询参数，结构与queryInquiriesByTimeRange一致
 * @returns 分页数据 { total, totalPage, pageSize, unansweredCount, currentPage, list }
 */
export const queryUnansweredInquiries = async (params: InquiryQueryParams) => {
  const url = '/inquiry/monitoring/unansweredInquiries';
  return httpRequest.rawRequestPostAsJson(url, params);
};