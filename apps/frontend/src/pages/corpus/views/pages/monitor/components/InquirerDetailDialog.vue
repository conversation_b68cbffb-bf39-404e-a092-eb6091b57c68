<template>
  <div class="inquirer-detail-dialog">
    <el-dialog
      v-model="dialogVisible"
      title="咨询人统计"
      width="65%"
    >
      <div class="inquirer-stats">
        <el-table
          :data="inquirerStatistics"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          v-loading="loading"
        >
          <el-table-column type="index" width="50" label="#" />
          <el-table-column label="咨询人" prop="misId" min-width="120" />
          <el-table-column label="组织信息" prop="orgInfo" min-width="220">
            <template #default="{ row }">
              <div v-if="orgInfoMap[row.orgId]" class="org-info">
                <span class="org-name">{{ orgInfoMap[row.orgId].name }}</span>
                <span class="org-path">{{ orgInfoMap[row.orgId].path }}</span>
              </div>
              <div v-else-if="row.orgId" class="org-info loading-org">
                <span v-if="orgLoadingMap[row.orgId]">加载中...</span>
                <span v-else class="org-id">{{ row.orgId }}</span>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="咨询次数" prop="count" width="100">
            <template #default="{ row }">
              <el-tag type="primary">{{ row.count }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="占比" width="180">
            <template #default="{ row }">
              <el-progress 
                :percentage="calculatePercentage(row.count)" 
                :stroke-width="10"
                :format="format"
                :color="getProgressColor(row.count)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, onMounted } from 'vue';
import { queryOrgById, formatOrgNamePath } from '../request';

// 定义props
const props = defineProps<{
  modelValue: boolean;
  inquirerStatistics: Array<{
    misId: string;
    orgId: string;
    orgName: string | null;
    count: number;
  }>;
  loading?: boolean;
}>();

// 定义事件
const emit = defineEmits(['update:modelValue']);

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 存储组织信息的映射
interface OrgInfo {
  name: string;
  path: string;
}

const orgInfoMap = ref<Record<string, OrgInfo>>({});
const orgLoadingMap = ref<Record<string, boolean>>({});

// 加载组织信息
const loadOrgInfo = async (orgId: string) => {
  if (!orgId || orgInfoMap.value[orgId] || orgLoadingMap.value[orgId]) return;
  
  orgLoadingMap.value[orgId] = true;
  
  try {
    const res = await queryOrgById(orgId);
    if (res && res.code === 0 && res.data) {
      orgInfoMap.value[orgId] = {
        name: res.data.name || '未知组织',
        path: formatOrgNamePath(res.data.orgNamePath || '')
      };
    }
  } catch (error) {
    console.error(`获取组织[${orgId}]信息失败:`, error);
  } finally {
    orgLoadingMap.value[orgId] = false;
  }
};

// 监听对话框显示状态，当显示时加载组织信息
watch(() => dialogVisible.value, (newVal) => {
  if (newVal && props.inquirerStatistics.length > 0) {
    loadAllOrgInfo();
  }
});

// 监听列表数据变化，当数据变化时加载新的组织信息
watch(() => props.inquirerStatistics, (newVal) => {
  if (dialogVisible.value && newVal.length > 0) {
    loadAllOrgInfo();
  }
}, { deep: true });

// 加载所有组织信息
const loadAllOrgInfo = () => {
  props.inquirerStatistics.forEach(item => {
    if (item.orgId) {
      loadOrgInfo(item.orgId);
    }
  });
};

// 在组件挂载时预加载组织信息
onMounted(() => {
  if (dialogVisible.value && props.inquirerStatistics.length > 0) {
    loadAllOrgInfo();
  }
});

// 计算总数量
const totalCount = computed(() => {
  return props.inquirerStatistics.reduce((sum, item) => sum + item.count, 0);
});

// 计算百分比
const calculatePercentage = (count: number): number => {
  if (totalCount.value === 0) return 0;
  return Math.round((count / totalCount.value) * 100);
};

// 格式化百分比显示
const format = (percentage: number) => {
  return `${percentage}%`;
};

// 获取进度条颜色
const getProgressColor = (count: number): string => {
  const percentage = calculatePercentage(count);
  if (percentage > 75) return '#f56c6c';
  if (percentage > 50) return '#e6a23c';
  if (percentage > 25) return '#409eff';
  return '#67c23a';
};
</script>

<style scoped lang="scss">
.inquirer-detail-dialog {
  .inquirer-stats {
    margin-bottom: 20px;
  }
  
  .org-info {
    display: flex;
    flex-direction: column;
    line-height: 1.5;
    
    .org-name {
      font-weight: 500;
      color: #333;
    }
    
    .org-path {
      font-size: 12px;
      color: #909399;
    }
    
    .org-id {
      color: #909399;
      font-size: 12px;
    }
  }
  
  .loading-org {
    color: #909399;
    font-style: italic;
    font-size: 12px;
  }
}
</style> 