<template>
  <div class="edit-monitor-task">
    <el-dialog
      v-model="dialogVisible"
      title="编辑监控任务"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="650px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        label-position="left"
        class="monitor-form"
      >
        <el-form-item label="监控组名称" prop="monitoringGroupName">
          <el-input v-model="formData.monitoringGroupName" placeholder="请输入监控组名称" />
        </el-form-item>
        
        <el-form-item label="监控组描述" prop="monitoringGroupDesc">
          <el-input v-model="formData.monitoringGroupDesc" placeholder="请输入监控组描述" type="textarea" :rows="2" />
        </el-form-item>
        
        <el-form-item label="监控组负责人" prop="monitoringGroupOwner" class="input-group-item">
          <div class="input-with-button">
            <el-input
              v-model="monitoringGroupOwnerInput"
              placeholder="请输入负责人MIS账号"
              clearable
              @keyup.enter="handleAddOwner"
            />
            <el-button type="primary" @click="handleAddOwner">添加</el-button>
          </div>
        </el-form-item>
        
        <!-- 已添加的负责人标签 - 只在有内容时显示 -->
        <el-form-item v-show="formData.monitoringGroupOwner && formData.monitoringGroupOwner.length > 0" label="" class="owner-tags-item">
          <div class="tag-container">
            <el-tag
              v-for="(tag, index) in formData.monitoringGroupOwner"
              :key="index"
              :closable="index !== 0"
              @close="removeOwnerTag(index)"
              class="owner-tag"
              type="primary"
              effect="light"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item prop="dxGroupIds" class="input-group-item">
          <template #label>
            <span class="label-flex">
              大象群ID
              <el-tooltip content="大象群ID可以在大象WEB端获取" placement="top">
                <a
                  href="https://km.sankuai.com/page/982430233"
                  target="_blank"
                  class="question-icon-circle custom-question"
                  tabindex="-1"
                  aria-label="获取大象群ID"
                ></a>
              </el-tooltip>
            </span>
          </template>
          <div class="input-with-button">
            <el-input
              v-model="dxGroupIdInput"
              placeholder="请输入大象群ID"
              clearable
              @keyup.enter="handleAddDxGroupId"
            />
            <el-button type="primary" @click="handleAddDxGroupId">添加</el-button>
          </div>
        </el-form-item>
        
        <!-- 已添加的大象群ID标签 - 只在有内容时显示 -->
        <el-form-item v-show="formData.dxGroupIds && formData.dxGroupIds.length > 0" label="" class="owner-tags-item">
          <div class="tag-container">
            <el-tag
              v-for="(id, index) in formData.dxGroupIds"
              :key="index"
              closable
              @close="removeDxGroupId(index)"
              class="owner-tag"
              type="primary"
              effect="light"
            >
              {{ id }}
            </el-tag>
          </div>
        </el-form-item>
        
        <!-- 重点关注组织的输入区域 -->
        <el-form-item label="重点关注组织" prop="monitoredOrgIds" class="org-item">
          <div class="input-with-button">
            <el-select
              v-model="orgIdInput"
              filterable
              remote
              reserve-keyword
              placeholder="仅支持输入完整部门链，例如：美团/核心本地商业"
              :remote-method="searchOrg"
              :loading="orgSearchLoading"
              :clearable="!!orgIdInput"
              style="width: 100%"
              class="org-select"
              :popper-class="'org-select-dropdown'"
              value-key="orgId"
            >
              <template #display-value="data">
                {{ data?.name || data }}
              </template>
              <el-option
                v-if="selectedOrg"
                :key="selectedOrg.orgId"
                :label="selectedOrg.name"
                :value="selectedOrg"
              >
                <div class="org-option">
                  <div class="org-name">{{ selectedOrg.name }} ({{ selectedOrg.orgId }})</div>
                  <div class="org-path">{{ selectedOrg.orgNamePath }}</div>
                </div>
              </el-option>
            </el-select>
            <el-button type="primary" @click="handleAddOrgId">添加</el-button>
          </div>
          
          <div class="tag-wrapper">
            <el-tag
              v-for="(orgId, index) in formData.monitoredOrgIds"
              :key="orgId"
              class="tag-item"
              closable
              @close="removeOrgId(index)"
            >
              {{ getOrgDisplayName(orgId) }}
            </el-tag>
          </div>
        </el-form-item>
        
        <!-- 重点关注用户的输入区域 -->
        <el-form-item label="重点关注用户" prop="monitoredMisIds" class="input-group-item">
          <div class="input-with-button">
            <el-input
              v-model="misInput"
              placeholder="请输入用户MIS账号"
              clearable
              @keyup.enter="handleAddMis"
            />
            <el-button type="primary" @click="handleAddMis">添加</el-button>
          </div>
        </el-form-item>
        
        <!-- 已添加的用户MIS标签 - 只在有内容时显示且数组不为空 -->
        <el-form-item v-show="formData.monitoredMisIds && formData.monitoredMisIds.length > 0" label="" class="owner-tags-item">
          <div class="tag-container">
            <el-tag
              v-for="(mis, index) in formData.monitoredMisIds"
              :key="index"
              closable
              @close="removeMis(index)"
              class="owner-tag"
              type="primary"
              effect="light"
            >
              {{ mis }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="关键词" prop="keywords" class="input-group-item">
          <div class="input-with-button">
            <el-input
              v-model="keywordInput"
              placeholder="请输入关键词"
              clearable
              @keyup.enter="handleAddKeyword"
            />
            <el-button type="primary" @click="handleAddKeyword">添加</el-button>
          </div>
        </el-form-item>
        
        <!-- 已添加的关键词标签 - 只在有内容时显示 -->
        <el-form-item v-show="formData.keywords && formData.keywords.length > 0" label="" class="owner-tags-item">
          <div class="tag-container">
            <el-tag
              v-for="(keyword, index) in formData.keywords"
              :key="index"
              closable
              @close="removeKeyword(index)"
              class="owner-tag"
              type="primary"
              effect="light"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="问题类型" prop="questionTypes">
          <el-table
            :data="formData.questionTypes"
            border
            style="width: 100%"
            max-height="200"
            row-key="questionTypeId"
            empty-text="点击下方按钮添加问题类型"
          >
            <el-table-column label="序号" width="60" align="center">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="typeName" label="类型名称" min-width="120">
              <template #default="scope">
                <el-input v-model="scope.row.typeName" placeholder="请输入类型名称" />
              </template>
            </el-table-column>
            <el-table-column prop="typeDesc" label="类型描述" min-width="150">
              <template #default="scope">
                <el-input v-model="scope.row.typeDesc" placeholder="请输入类型描述" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <div class="operation-btns">
                  <el-button v-if="scope.$index !== 0" type="primary" text @click="moveQuestionTypeUp(scope.$index)">
                    <el-icon><ArrowUp /></el-icon>
                  </el-button>
                  <el-button v-if="scope.$index !== formData.questionTypes.length - 1" type="primary" text @click="moveQuestionTypeDown(scope.$index)">
                    <el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <el-button type="danger" text @click="removeQuestionType(scope.$index)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="form-actions">
            <el-button type="primary" link @click="addQuestionType">
              <el-icon><Plus /></el-icon>添加问题类型
            </el-button>
          </div>
        </el-form-item>
        
        <!-- 监控时间段 -->
        <el-form-item label="监控时间段" prop="monitoringTimeRangeType" class="mr30">
          <el-radio-group v-model="formData.monitoringTimeRangeType">
            <el-radio :label="0">7天</el-radio>
            <el-radio :label="1">14天</el-radio>
            <el-radio v-if="show30DaysOption" :label="2">30天</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="监控状态">
          <div class="status-switch-container">
            <span class="status-label normal-label">正常</span>
            <el-switch
              v-model="formData.disabled"
              class="status-switch"
              :active-value="true"
              :inactive-value="false"
              inactive-color="#13ce66"
              active-color="#ff4949"
            />
            <span class="status-label disabled-label">禁用</span>
            <el-tag v-if="formData.disabled" class="warning-tag" type="danger" size="small">
              <el-icon><WarningFilled /></el-icon>
              禁用后，该监控任务将不再生效，不会采集新的数据
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="danger" @click="handleDelete">删除监控任务</el-button>
          <div class="right-buttons">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitLoading">
              保存
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteConfirmVisible"
      title="删除确认"
      width="400px"
      append-to-body
    >
      <div class="delete-confirm-content">
        <el-icon class="warning-icon"><WarningFilled /></el-icon>
        <p>确定要删除监控任务 "{{ formData.monitoringGroupName }}" 吗？</p>
        <p class="warning-text">此操作不可恢复，删除后相关数据将无法找回。</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteConfirmVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete" :loading="deleteLoading">确定删除</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose, nextTick, onMounted, watch } from 'vue';
import { ElMessage, FormInstance, ElMessageBox } from 'element-plus';
import { Plus, ArrowUp, ArrowDown, WarningFilled, InfoFilled, QuestionFilled } from '@element-plus/icons-vue';
import { QuestionType, MonitorTask, OrgSearchResultItem } from '../types';
import { updateMonitorTask, deleteMonitorTask, queryOrgByNamePath, queryOrgById, getGrayscaleConfig } from '../request';
import { getCurrentUser } from '@/shared/services/userService';
import httpRequest from '@/utils/httpRequest';

// 定义组件事件
const emit = defineEmits(['success', 'cancel', 'delete']);

// 表单引用
const formRef = ref<FormInstance>();

// 对话框控制
const dialogVisible = ref(false);
const submitLoading = ref(false);

// 当前编辑的监控任务ID
const currentMonitorTaskId = ref<number>();

// 负责人输入框
const monitoringGroupOwnerInput = ref('');
const dxGroupIdInput = ref('');
const orgIdInput = ref('');
const misInput = ref('');
const keywordInput = ref('');

// 组织ID输入
const orgSearchLoading = ref(false);
const selectedOrg = ref<any>(null);
const orgCache = ref<Record<string, OrgSearchResultItem>>({});
const orgNameCache = ref<Record<string, string>>({});

// 灰度测试相关
const currentUserMis = ref(''); // 当前用户MIS
const show30DaysOption = ref(false); // 是否显示30天选项

// 表单数据
const formData = reactive<MonitorTask & { disabled: boolean }>({
  monitoringGroupName: '',
  monitoringGroupDesc: '',
  monitoringGroupOwner: [],
  dxGroupIds: [],
  monitoredOrgIds: [],
  monitoredMisIds: [],
  keywords: [],
  questionTypes: [],
  monitoringTimeRangeType: 0, // 默认选择7天(值为0)
  disabled: false // 默认启用状态
});

// 表单数据与后端映射关系说明
// monitoringTimeRangeType: 0/1/2 - 对应后端枚举值，映射关系：7天->0, 14天->1, 30天->2

// 表单校验规则
const rules = {
  monitoringGroupName: [
    { required: true, message: '请输入监控组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  monitoringGroupDesc: [
    { required: true, message: '请输入监控组描述', trigger: 'blur' },
    { max: 200, message: '长度不超过 200 个字符', trigger: 'blur' }
  ],
  monitoringGroupOwner: [
    { required: true, message: '请添加至少一个负责人', trigger: 'change' },
    { type: 'array', min: 1, message: '请添加至少一个负责人', trigger: 'change' }
  ],
  dxGroupIds: [
    { required: true, message: '请添加至少一个大象群ID', trigger: 'change' },
    { type: 'array', min: 1, message: '请添加至少一个大象群ID', trigger: 'change' }
  ],
  keywords: [
    { required: true, message: '请添加至少一个关键词', trigger: 'change' },
    { type: 'array', min: 1, message: '请添加至少一个关键词', trigger: 'change' }
  ],
  questionTypes: [
    { required: true, type: 'array', min: 1, message: '请添加至少一个问题类型', trigger: 'change' }
  ],
  monitoringTimeRangeType: [
    { required: true, message: '请选择监控时间范围', trigger: 'change' }
  ],
  monitoredOrgIds: [
    { type: 'array', message: '组织ID必须是数组', trigger: 'change' }
  ]
};

// 获取组织显示名称
const getOrgDisplayName = (orgId: string): string => {
  // 如果名称缓存中已有，直接返回
  if (orgNameCache.value[orgId]) {
    return orgNameCache.value[orgId];
  }
  
  // 如果对象缓存中有完整信息
  if (orgCache.value[orgId]) {
    const org = orgCache.value[orgId];
    // 更新名称缓存
    orgNameCache.value[orgId] = org.name || org.orgName || `${orgId}`;
    return orgNameCache.value[orgId];
  }
  
  // 如果都没有，查询API获取组织信息
  loadOrgInfo(orgId);
  
  // 在获取到之前，显示ID
  return `${orgId}`;
};

// 加载组织信息
const loadOrgInfo = async (orgId: string) => {
  try {
    // 调用接口获取组织详情
    const res = await queryOrgById(orgId);
    if (res && res.code === 0 && res.data) {
      // 保存到缓存中
      orgNameCache.value[orgId] = res.data.name || res.data.orgName || `${orgId}`;
      
      // 如果不在对象缓存中，也添加进去
      if (!orgCache.value[orgId]) {
        orgCache.value[orgId] = res.data;
      }
    }
  } catch (error) {
    console.error(`获取组织[${orgId}]信息失败:`, error);
  }
};

// 批量加载所有组织信息
const loadAllOrgInfo = () => {
  // 加载所有已选组织的信息
  if (formData.monitoredOrgIds && formData.monitoredOrgIds.length > 0) {
    formData.monitoredOrgIds.forEach(orgId => {
      if (!orgNameCache.value[orgId] && !orgCache.value[orgId]) {
        loadOrgInfo(orgId);
      }
    });
  }
};

// 初始化当前用户信息
const initCurrentUser = async () => {
  try {
    // 使用userService中的getCurrentUser方法获取用户信息
    const userInfo = await getCurrentUser();
    if (userInfo && userInfo.login) {
      currentUserMis.value = userInfo.login;
    } else {
      console.error('获取用户信息失败：用户信息不完整');
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
  }
};

// 检查灰度配置
const checkGrayscaleConfig = async () => {
  try {
    // 默认不显示30天选项
    show30DaysOption.value = false;
    
    // 获取灰度配置
    const res = await getGrayscaleConfig();
    
    if (res && res.code === 0 && res.data && res.data.config) {
      const { monitorGroupIds, misIds } = res.data.config;
      
      // 如果都为空数组，表示全量开放
      if ((!monitorGroupIds || monitorGroupIds.length === 0) && 
          (!misIds || misIds.length === 0)) {
        show30DaysOption.value = true;
        return;
      }
      
      // 检查用户MIS是否在白名单中
      const misInWhitelist = misIds && misIds.includes(currentUserMis.value);
      
      // 检查当前正在编辑的监控组ID是否在白名单中
      const groupInWhitelist = monitorGroupIds && currentMonitorTaskId.value && 
                              monitorGroupIds.includes(currentMonitorTaskId.value.toString());
      
      // 只有当用户MIS和监控组ID都在白名单中才显示30天选项
      show30DaysOption.value = misInWhitelist && groupInWhitelist;
    }
  } catch (error) {
    console.error('检查灰度配置失败:', error);
    // 发生错误时，不显示30天选项
    show30DaysOption.value = false;
  }
};

// 打开对话框
const open = async (task?: MonitorTask) => {
  // 重置表单数据
  resetForm();
  
  // 如果有传入的任务数据，填充表单
  if (task) {
    // 保存当前编辑的监控任务ID
    currentMonitorTaskId.value = task.monitoringGroupId;
    
    formData.monitoringGroupId = task.monitoringGroupId;
    formData.monitoringGroupName = task.monitoringGroupName;
    formData.monitoringGroupDesc = task.monitoringGroupDesc;
    formData.monitoringGroupOwner = Array.isArray(task.monitoringGroupOwner) ? [...task.monitoringGroupOwner] : [];
    formData.dxGroupIds = Array.isArray(task.dxGroupIds) ? [...task.dxGroupIds] : [];
    
    // 确保组织ID和用户MIS是正确的数组，过滤掉空值
    const orgIds = Array.isArray(task.monitoredOrgIds) ? [...task.monitoredOrgIds] : [];
    formData.monitoredOrgIds = orgIds.filter(id => id && id.trim() !== '');
    
    const misIds = Array.isArray(task.monitoredMisIds) ? [...task.monitoredMisIds] : [];
    formData.monitoredMisIds = misIds.filter(id => id && id.trim() !== '');
    
    formData.keywords = Array.isArray(task.keywords) ? [...task.keywords] : [];
    
    // 设置监控时间范围
    formData.monitoringTimeRangeType = task.monitoringTimeRangeType !== undefined ? task.monitoringTimeRangeType : 0;
    
    formData.disabled = task.disabled || task.status === 1;
    
    // 处理问题类型数据
    if (Array.isArray(task.questionTypes) && task.questionTypes.length > 0) {
      // 确保questionTypes是数组且保留问题类型ID
      formData.questionTypes = task.questionTypes.map((item, index) => ({
        questionTypeId: item.questionTypeId || item.id || index + 1,
        monitoringGroupId: task.monitoringGroupId,
        typeName: item.typeName || item.questionTypeName || '',  // 支持两种字段名
        typeDesc: item.typeDesc || item.questionTypeDesc || '',  // 支持两种字段名
        sortOrder: item.sortOrder || index + 1
      }));
    } else {
      // 如果没有问题类型数据，则初始化为空数组
      formData.questionTypes = [];
    }
    
    // 检查灰度配置，确定是否显示30天选项
    await checkGrayscaleConfig();
  }
  // 最后才显示对话框
  dialogVisible.value = true;
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  emit('cancel');
};

// 处理负责人输入变化
const handleAddOwner = () => {
  if (!monitoringGroupOwnerInput.value) {
    return;
  }
  
  // 处理输入的MIS账号
  const owner = monitoringGroupOwnerInput.value.trim();
  
  // 检查是否为空
  if (!owner) {
    return;
  }
  
  // 检查是否已存在
  if (!formData.monitoringGroupOwner.includes(owner)) {
    formData.monitoringGroupOwner.push(owner);
  } else {
    ElMessage.warning(`负责人 ${owner} 已存在`);
  }
  
  // 清空输入框
  monitoringGroupOwnerInput.value = '';
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoringGroupOwner');
};

// 移除负责人标签
const removeOwnerTag = (index: number) => {
  formData.monitoringGroupOwner.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoringGroupOwner');
};

// 重置表单
const resetForm = () => {
  
  // 清空所有输入框
  monitoringGroupOwnerInput.value = '';
  dxGroupIdInput.value = '';
  orgIdInput.value = '';
  misInput.value = '';
  keywordInput.value = '';
  
  // 清空表单数据对象的每一个属性
  formData.monitoringGroupName = '';
  formData.monitoringGroupDesc = '';
  formData.monitoringGroupOwner = [];
  formData.dxGroupIds = [];
  formData.monitoredOrgIds = [];
  formData.monitoredMisIds = [];
  formData.keywords = [];
  formData.questionTypes = [];
  formData.monitoringTimeRangeType = 0;
  formData.disabled = false;
  
  // 强制清除所有标签
  clearAllTags();
  
  // 重置表单验证状态
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
};

// 清除所有标签的方法
const clearAllTags = () => {
  // 清空所有标签数组
  formData.monitoringGroupOwner = [];
  formData.dxGroupIds = [];
  formData.monitoredOrgIds = [];
  formData.monitoredMisIds = [];
  formData.keywords = [];
  
  // 移除可能存在的清除按钮
  const clearButtons = document.querySelectorAll('.el-input__clear');
  clearButtons.forEach(button => {
    (button as HTMLElement).style.display = 'none';
  });
};

// 添加问题类型按钮点击
const addQuestionType = () => {
  // 生成新的问题类型ID (自增)
  const newId = formData.questionTypes.length > 0 
    ? Math.max(...formData.questionTypes.map(item => item.questionTypeId)) + 1 
    : 1;
  
  // 直接添加一个新的问题类型到表格
  formData.questionTypes.push({
    questionTypeId: newId,
    monitoringGroupId: currentMonitorTaskId.value, // 添加监控组ID
    typeName: '',
    typeDesc: '',
    sortOrder: formData.questionTypes.length + 1 // 设置排序序号为当前长度+1
  });
};

// 移除问题类型
const removeQuestionType = (index: number) => {
  formData.questionTypes.splice(index, 1);
  
  // 重新计算排序序号
  formData.questionTypes.forEach((item, idx) => {
    item.sortOrder = idx + 1;
  });
  
  // 触发表单验证更新
  formRef.value?.validateField('questionTypes');
};

// 上移问题类型
const moveQuestionTypeUp = (index: number) => {
  if (index > 0) {
    const temp = formData.questionTypes[index];
    formData.questionTypes[index] = formData.questionTypes[index - 1];
    formData.questionTypes[index - 1] = temp;
    
    // 调整排序序号
    formData.questionTypes[index].sortOrder = index + 1;
    formData.questionTypes[index - 1].sortOrder = index;
  }
};

// 下移问题类型
const moveQuestionTypeDown = (index: number) => {
  if (index < formData.questionTypes.length - 1) {
    const temp = formData.questionTypes[index];
    formData.questionTypes[index] = formData.questionTypes[index + 1];
    formData.questionTypes[index + 1] = temp;
    
    // 调整排序序号
    formData.questionTypes[index].sortOrder = index + 1;
    formData.questionTypes[index + 1].sortOrder = index + 2;
  }
};

// 处理大象群ID输入
const handleAddDxGroupId = () => {
  if (!dxGroupIdInput.value) {
    return;
  }
  
  // 处理输入的群ID
  const idStr = dxGroupIdInput.value.trim();
  
  // 检查是否为空
  if (!idStr) {
    return;
  }
  
  // 检查是否为数字
  const id = Number(idStr);
  if (isNaN(id)) {
    ElMessage.warning('请输入有效的数字ID');
    return;
  }
  
  // 检查是否已存在
  if (!formData.dxGroupIds.includes(id)) {
    formData.dxGroupIds.push(id);
  } else {
    ElMessage.warning(`大象群ID ${id} 已存在`);
  }
  
  // 清空输入框
  dxGroupIdInput.value = '';
  
  // 触发表单验证更新
  formRef.value?.validateField('dxGroupIds');
};

// 移除大象群ID标签
const removeDxGroupId = (index: number) => {
  formData.dxGroupIds.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('dxGroupIds');
};

// 通过组织路径搜索组织
const searchOrg = async (query: string) => {
  if (!query || query.trim() === '') {
    selectedOrg.value = null;
    return;
  }
  
  orgSearchLoading.value = true;
  try {
    const res = await queryOrgByNamePath(query);
    if (res?.code === 0 && res.data) {
      // 如果查询成功并返回了组织数据
      selectedOrg.value = res.data;
      
      // 更新缓存
      if (selectedOrg.value.orgId) {
        orgCache.value[selectedOrg.value.orgId] = selectedOrg.value;
        orgNameCache.value[selectedOrg.value.orgId] = selectedOrg.value.name;
      }
    } else {
      // 如果查询失败或没有返回组织数据
      selectedOrg.value = null;
    }
  } catch (error) {
    console.error('查询组织失败:', error);
    selectedOrg.value = null;
  } finally {
    orgSearchLoading.value = false;
  }
};

// 处理组织ID添加
const handleAddOrgId = () => {
  if (!orgIdInput.value || !selectedOrg.value) {
    ElMessage.warning('请先搜索并选择一个有效的组织');
    return;
  }
  
  const orgId = selectedOrg.value.orgId;
  
  // 确保有组织ID
  if (!orgId) {
    ElMessage.warning('所选组织无效');
    return;
  }
  
  // 检查是否已存在
  if (!formData.monitoredOrgIds.includes(orgId)) {
    // 将组织ID添加到表单数据中
    formData.monitoredOrgIds.push(orgId);
    
    // 缓存完整的组织对象
    if (!orgCache.value[orgId]) {
      orgCache.value[orgId] = selectedOrg.value;
    }
    
    // 缓存组织名称
    if (!orgNameCache.value[orgId]) {
      orgNameCache.value[orgId] = selectedOrg.value.name;
    }
  } else {
    ElMessage.warning(`组织 ${selectedOrg.value.name} 已存在`);
  }
  
  // 清空输入框和选中的组织
  orgIdInput.value = '';
  selectedOrg.value = null;
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoredOrgIds');
};

// 移除组织ID标签
const removeOrgId = (index: number) => {
  formData.monitoredOrgIds.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoredOrgIds');
};

// 处理用户MIS输入
const handleAddMis = () => {
  if (!misInput.value) {
    return;
  }
  
  // 处理输入的用户MIS，确保是字符串
  const mis = String(misInput.value).trim();
  
  // 检查是否为空
  if (!mis) {
    return;
  }
  
  // 检查是否已存在
  if (!formData.monitoredMisIds.includes(mis)) {
    formData.monitoredMisIds.push(mis);
  } else {
    ElMessage.warning(`用户MIS ${mis} 已存在`);
  }
  
  // 清空输入框
  misInput.value = '';
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoredMisIds');
};

// 移除用户MIS标签
const removeMis = (index: number) => {
  formData.monitoredMisIds.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoredMisIds');
};

// 处理关键词输入
const handleAddKeyword = () => {
  if (!keywordInput.value) {
    return;
  }
  
  // 处理输入的关键词
  const keyword = keywordInput.value.trim();
  
  // 检查是否为空
  if (!keyword) {
    return;
  }
  
  // 检查是否已存在
  if (!formData.keywords.includes(keyword)) {
    formData.keywords.push(keyword);
  } else {
    ElMessage.warning(`关键词 ${keyword} 已存在`);
  }
  
  // 清空输入框
  keywordInput.value = '';
  
  // 触发表单验证更新
  formRef.value?.validateField('keywords');
};

// 移除关键词标签
const removeKeyword = (index: number) => {
  formData.keywords.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('keywords');
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (!valid) {
      console.error('表单验证失败');
      ElMessage.error('请完善表单信息');
      return;
    }

    // 检查监控组负责人是否为空
    if (!formData.monitoringGroupOwner || formData.monitoringGroupOwner.length === 0) {
      ElMessage.warning('请至少添加一名监控组负责人');
      return;
    }

    // 检查大象群ID是否为空
    if (!formData.dxGroupIds || formData.dxGroupIds.length === 0) {
      ElMessage.warning('请至少添加一个大象群ID');
      return;
    }

    // 检查问题类型是否至少有一个且没有空值
    if (!formData.questionTypes || formData.questionTypes.length === 0) {
      ElMessage.warning('请至少添加一个问题类型');
      return;
    }

    // 检查问题类型是否有空值
    const hasEmptyQuestionType = formData.questionTypes.some(
      item => !item.typeName || !item.typeDesc
    );
    
    if (hasEmptyQuestionType) {
      ElMessage.warning('问题类型名称和描述不能为空');
      return;
    }
    
    submitLoading.value = true;
    
    try {
      // 构造请求数据，确保问题类型有排序序号和monitoringGroupId
      const questionTypes = formData.questionTypes.map((type, index) => ({
        questionTypeId: type.questionTypeId,
        monitoringGroupId: currentMonitorTaskId.value,
        questionTypeName: type.typeName,
        questionTypeDesc: type.typeDesc,
        sortOrder: type.sortOrder || (index + 1)
      }));
      
      // 构造请求数据，将disabled转换为status
      const requestData = {
        ...formData,
        questionTypes,
        status: formData.disabled ? 1 : 0, // 将disabled转换为status
        // 确保monitoredMisIds是非空字符串数组
        monitoredMisIds: formData.monitoredMisIds,
        // 确保monitoredOrgIds是非空字符串数组
        monitoredOrgIds: formData.monitoredOrgIds
      };
      
      // 确保dxGroupIds中的值是数字
      requestData.dxGroupIds = requestData.dxGroupIds.map(id => typeof id === 'string' ? Number(id) : id);
      
      // 删除不需要的属性
      delete requestData.disabled;
      
      // 确保监控组ID正确设置
      if (currentMonitorTaskId.value) {
        requestData.monitoringGroupId = currentMonitorTaskId.value;
      }
      
      // 调用API更新监控任务
      const res = await updateMonitorTask({
        ...requestData,
        operatorMisId: currentUserMis.value
      });
      
      // 根据实际接口返回处理成功/失败逻辑
      if (res?.code === 0 && res.success) {
        ElMessage.success('更新监控任务成功');
        dialogVisible.value = false;
        emit('success', {
          ...requestData,
          disabled: requestData.status === 1, // 转换回前端使用的disabled属性
          needRefresh: true // 添加需要刷新的标志
        });
      } else {
        ElMessage.error(res?.msg || '更新监控任务失败');
      }
    } catch (error) {
      console.error('更新监控任务失败:', error);
      ElMessage.error('更新监控任务失败，请稍后重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 删除确认对话框
const deleteConfirmVisible = ref(false);
const deleteLoading = ref(false);

// 处理删除按钮点击
const handleDelete = () => {
  deleteConfirmVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  deleteLoading.value = true;
  
  try {
    // 调用API删除监控任务
    const res = await deleteMonitorTask(currentMonitorTaskId.value);
    
    // 根据实际接口返回处理成功/失败逻辑
    if (res?.code === 0 && res.success) {
      ElMessage.success('删除监控任务成功');
      deleteConfirmVisible.value = false;
      dialogVisible.value = false;
      emit('delete', currentMonitorTaskId.value, true); // 添加第二个参数表示需要刷新页面
    } else {
      ElMessage.error(res?.msg || '删除监控任务失败');
    }
  } catch (error) {
    console.error('删除监控任务失败:', error);
    ElMessage.error('删除监控任务失败，请稍后重试');
  } finally {
    deleteLoading.value = false;
  }
};

// 在组件加载后，预先加载样式和初始化
onMounted(async () => {
  // 添加全局样式，确保下拉菜单中的文本能够完整显示
  const style = document.createElement('style');
  style.textContent = `
    .el-select-dropdown__item-text {
      white-space: normal !important;
      word-break: break-all !important;
      overflow: visible !important;
      width: 100% !important;
      display: inline-block !important;
    }
    
    .el-select-dropdown .el-select-dropdown__item {
      height: auto !important;
      line-height: 1.5 !important;
      padding: 10px !important;
    }
    
    /* 确保select输入框内的文本也能够显示完整 */
    .el-select__tags-text,
    .el-select .el-input__inner {
      white-space: normal !important;
      word-break: break-all !important;
    }
    
    /* 自定义清除按钮样式，只在有内容时显示 */
    .el-input__clear {
      display: none !important;
    }
    
    .el-input.is-focus .el-input__clear,
    .el-input:hover .el-input__clear {
      display: none !important;
    }
  `;
  document.head.appendChild(style);
  
  // 获取当前用户信息
  await initCurrentUser();
  
  // 初始化清除按钮处理
  setTimeout(initClearButtonHandlers, 100);
});

// 初始化清除按钮处理函数
const initClearButtonHandlers = () => {
  // 移除所有清除按钮
  const clearButtons = document.querySelectorAll('.el-input__clear');
  clearButtons.forEach(button => {
    (button as HTMLElement).style.display = 'none';
  });
  
  // 监听输入框的focus事件，确保聚焦时也不显示清除按钮
  const inputWrappers = document.querySelectorAll('.el-input__wrapper');
  inputWrappers.forEach(wrapper => {
    wrapper.addEventListener('mouseenter', () => {
      const clearBtn = wrapper.querySelector('.el-input__clear') as HTMLElement;
      if (clearBtn) clearBtn.style.display = 'none';
    });
  });
};

// 监听formData.monitoredOrgIds变化，加载新添加的组织信息
watch(() => formData.monitoredOrgIds, (newIds, oldIds) => {
  if (newIds && newIds.length > 0) {
    // 找出新增的ID
    const newAddedIds = newIds.filter(id => !oldIds || !oldIds.includes(id));
    newAddedIds.forEach(orgId => {
      if (!orgNameCache.value[orgId] && !orgCache.value[orgId]) {
        loadOrgInfo(orgId);
      }
    });
  }
}, { deep: true });

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped lang="scss">
.edit-monitor-task {
  .monitor-form {
    :deep(.el-form-item__label) {
      font-weight: 500;
    }
    
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  .form-actions {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    gap: 10px;
  }
  
  .input-with-button {
    display: flex;
    gap: 10px;
    width: 100%;
    
    .el-input {
      flex: 1;
    }
    
    .el-button {
      width: 80px;
      flex-shrink: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  
  .tag-wrapper {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
  }
  
  .org-item {
    margin-bottom: 18px;
  }
  
  .tag-item {
    margin-right: 8px;
    margin-bottom: 8px;
  }
  
  .input-group-item {
    margin-bottom: 15px;
  }
  
  .owner-tags-item {
    margin-top: -8px;
    margin-bottom: 18px;
    
    :deep(.el-form-item__content) {
      margin-left: 120px !important;
    }
  }
  
  .tag-container {
    margin-top: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 5px 0;
    
    .owner-tag {
      margin-right: 0;
    }
  }
  
  :deep(.el-form-item__content) {
    width: calc(100% - 120px);
  }
  
  :deep(.el-input) {
    width: 100%;
  }
  
  .operation-btns {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 5px;
  }
  
  .status-switch-container {
    display: flex;
    align-items: center;
    
    .status-label {
      font-size: 14px;
    }
    
    .normal-label {
      color: #606266;
      margin-right: 10px;
    }
    
    .disabled-label {
      color: #ff4949;
      margin: 0 10px;
    }
    
    .warning-tag {
      margin-left: 5px;
      
      .el-icon {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }
  
  .status-tip {
    display: none; // 隐藏原来的提示
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  width: 100%;
  
  .right-buttons {
    display: flex;
    gap: 10px;
  }
  
  .el-button + .el-button {
    margin-left: 0;
  }
}

.delete-confirm-content {
  text-align: center;
  padding: 20px 0;
  
  .warning-icon {
    font-size: 48px;
    color: #F56C6C;
    margin-bottom: 15px;
  }
  
  p {
    margin: 8px 0;
    font-size: 16px;
  }
  
  .warning-text {
    color: #F56C6C;
    font-size: 14px;
  }
}

.form-actions {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.org-option {
  display: flex;
  flex-direction: column;
  padding: 0 5px;
  
  .org-name {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .org-path {
    font-size: 12px;
    color: #909399;
  }
}

.input-with-button {
  display: flex;
  align-items: center;
  gap: 10px;
  
  .el-select {
    flex: 1;
  }
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.tag-list .el-tag {
  margin-right: 0;
  display: flex;
  align-items: center;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}

.form-footer {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.subordinate-tip {
  margin-top: 10px;
  text-align: center;
}

.org-option {
  display: flex;
  flex-direction: column;
}

.org-name {
  font-weight: 500;
}

.org-path {
  font-size: 12px;
  color: #909399;
}

:deep(.org-select-dropdown) {
  max-height: 400px !important;
  min-width: 500px !important;
  
  .el-select-dropdown__wrap {
    max-height: 380px !important;
  }
  
  .el-select-dropdown__item {
    height: auto;
    padding: 8px 10px;
    line-height: 1.5;
    white-space: normal;
    word-break: break-all;
    
    &.hover, &:hover {
      .org-path {
        display: block !important;
      }
    }
  }
}

.label-flex {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  
  .question-icon-circle.custom-question {
    margin-left: 8px;
  }
}

.question-icon-circle.custom-question {
  margin-left: 8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #909399;
  color: #fff;
  font-size: 10px;
  cursor: pointer;
  text-decoration: none;
  position: relative;
  transition: background 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background: #606266;
  }
  &::before {
    content: '?';
    display: block;
    font-size: 11px;
    font-weight: bold;
    color: #fff;
    line-height: 16px;
    text-align: center;
    width: 100%;
    height: 100%;
  }
}

.label-link {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
  font-weight: 500;
  &:hover {
    color: #409eff;
  }
}
</style> 