<template>
  <div class="question-type-chart">
    <div class="chart-header">
      <h3 class="chart-title">问题类型分布</h3>
      <el-tooltip content="展示不同问题类型的数量分布" placement="top">
        <el-icon><QuestionFilled /></el-icon>
      </el-tooltip>
    </div>
    <div ref="chartRef" class="chart-container" v-loading="loading"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { queryStatistics } from '../request';

// 注册 ECharts 组件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent,
  CanvasRenderer
]);

const props = defineProps<{
  monitorGroupId: number | null;
  startTime: number;
  endTime: number;
}>();

const chartRef = ref<HTMLElement | null>(null);
const loading = ref(false);
let chart: echarts.ECharts | null = null;

// 用于存储问题类型数据
const chartData = ref({
  types: ['未解决问题', '已解决问题'] as string[],
  counts: [0, 0] as number[],
  colors: ['#5B8FF9', '#8FD460'] as string[] // 简化为两种状态的颜色
});

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', handleResize);
});

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  
  // 设置基本配置
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#eee',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      },
      padding: [10, 12],
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowBlur: 10
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemWidth: 16,
      itemHeight: 16,
      itemGap: 16,
      textStyle: {
        fontSize: 14
      },
      formatter: function(name) {
        // 找到对应的数据项
        const dataItem = option.series[0].data.find(item => item.name === name);
        if (dataItem) {
          return `${name}: ${dataItem.value}`;
        }
        return name;
      }
    },
    series: [
      {
        name: '问题类型',
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2,
          borderRadius: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.2)'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 0, name: '暂无数据' }
        ],
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
    ]
  };
  
  chart.setOption(option);
};

// 更新图表
const updateChart = () => {
  if (chart) {
    const seriesData = chartData.value.types.map((type, index) => ({
      value: chartData.value.counts[index],
      name: type,
      itemStyle: { 
        color: chartData.value.colors[index] || getDefaultColor(index)
      }
    }));
    
    chart.setOption({
      series: [
        {
          data: seriesData
        }
      ]
    });
  }
};

// 获取默认颜色
const getDefaultColor = (index: number) => {
  const colors = [
    '#5B8FF9', '#8FD460', '#F6BD16', '#5D7092', '#E8684A', 
    '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3'
  ];
  return colors[index % colors.length];
};

// 处理窗口大小变化
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

// 获取问题类型数据
const fetchTypeData = async () => {
  if (!props.monitorGroupId || !props.startTime || !props.endTime) {
    console.warn('饼图数据获取条件不满足', props);
    return;
  }
  
  loading.value = true;
  
  try {
    const response = await queryStatistics({
      startTime: String(props.startTime),
      endTime: String(props.endTime),
      monitorGroupIds: [String(props.monitorGroupId)]
    });
    
    if (response && response.code === 0 && response.success && response.data) {
      // 从API响应中提取问题类型数据
      if (response.data.questionTypeStatistics && response.data.questionTypeStatistics.length > 0) {
        const types: string[] = [];
        const counts: number[] = [];
        const colors: string[] = [];
        
        // 按数量排序
        const sortedStats = [...response.data.questionTypeStatistics].sort((a, b) => b.count - a.count);
        
        sortedStats.forEach((item, index) => {
          types.push(item.typeName);
          counts.push(item.count);
          colors.push(getDefaultColor(index));
        });
        
        // 更新图表数据
        chartData.value = {
          types,
          counts,
          colors
        };
      } else if (!response.data.questionTypeStatistics || response.data.questionTypeStatistics.length === 0) {
        // 如果没有数据，显示空状态
        chartData.value = {
          types: ['暂无数据'],
          counts: [100],
          colors: ['#EBEEF5']
        };
      }
      
      // 更新图表
      updateChart();
    } else {
      console.error('获取问题类型数据失败', response);
      ElMessage.error(response?.msg || '获取问题类型数据失败');
    }
  } catch (error) {
    console.error('获取问题类型数据异常:', error);
    ElMessage.error('获取问题类型数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 监听属性变化，重新获取数据
watch(
  () => [props.monitorGroupId, props.startTime, props.endTime],
  () => {
    // 不再主动请求数据，由父组件通过refreshWithData方法提供数据
    // fetchTypeData(); // 注释掉自动触发的请求
  },
  { deep: true }
);

// 使用预先获取的数据刷新图表
const refreshWithData = async (data: any) => {
  if (!data) {
    console.warn('问题类型图表没有收到有效数据');
    return;
  }
  
  loading.value = true;
  
  try {
    // 从传入的数据中提取问题类型数据
    if (data.questionTypeStatistics && data.questionTypeStatistics.length > 0) {
      const types: string[] = [];
      const counts: number[] = [];
      const colors: string[] = [];
      
      // 按数量排序
      const sortedStats = [...data.questionTypeStatistics].sort((a, b) => b.count - a.count);
      
      sortedStats.forEach((item, index) => {
        types.push(item.typeName);
        counts.push(item.count);
        colors.push(getDefaultColor(index));
      });
      
      // 更新图表数据
      chartData.value = {
        types,
        counts,
        colors
      };
    } else if (!data.questionTypeStatistics || data.questionTypeStatistics.length === 0) {
      // 如果没有数据，显示空状态
      chartData.value = {
        types: ['暂无数据'],
        counts: [100],
        colors: ['#EBEEF5']
      };
    }
    
    // 更新图表
    updateChart();
  } catch (error) {
    console.error('问题类型图表使用共享数据更新失败:', error);
  } finally {
    loading.value = false;
  }
};

// 暴露方法
defineExpose({
  refresh: fetchTypeData,
  refreshWithData
});
</script>

<style scoped lang="scss">
.question-type-chart {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-top: 20px;
  width: calc(50% - 12px);
  
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .chart-title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
      margin: 0;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: linear-gradient(to bottom, #5B8FF9, #85a5ff);
        border-radius: 2px;
      }
    }
    
    .el-icon {
      color: #909399;
      cursor: pointer;
      font-size: 18px;
      
      &:hover {
        color: #5B8FF9;
      }
    }
  }
  
  .chart-container {
    width: 100%;
    height: 320px;
    border-radius: 4px;
    overflow: hidden;
  }
}
</style> 