<template>
  <div class="question-detail-dialog">
    <el-dialog
      v-model="dialogVisible"
      title="问题详情"
      width="80%"
      destroy-on-close
    >
      <div class="question-detail" v-if="questionData">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID:">
            {{ questionData.id }}
          </el-descriptions-item>
          <el-descriptions-item label="提问者:">
            {{ questionData.questionerMisId }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间:">
            {{ formatDateTime(questionData.createTime || questionData.ctime) }}
          </el-descriptions-item>
          <el-descriptions-item label="问题类型:">
            {{ questionData.questionType ? getQuestionTypeName(questionData.questionType) : '' }}
          </el-descriptions-item>
          <el-descriptions-item label="来源群:">
            <el-link 
              type="primary" 
              :underline="false"
              @click="openGroupChat(questionData.dxGroupId, questionData.dxGroupName)"
            >
              {{ questionData.dxGroupName || `未知群组(${questionData.dxGroupId})` }}
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="回应状态:">
            <!-- 根据接口新定义：replyStatus: 0-无回应，1-有回应 -->
            <el-tag :type="questionData.replyStatus === 0 ? 'danger' : 'success'" size="small">
              {{ questionData.replyStatus === 0 ? '无回应' : '有回应' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="content-section">
          <div class="content-item">
            <div class="content-label">原始消息:</div>
            <div class="content-value">
              <pre class="formatted-text">{{ questionData.rawQuestionMsg }}</pre>
            </div>
          </div>
          
          <div class="content-item">
            <div class="content-label">问题摘要:</div>
            <div class="content-value">
              <pre class="formatted-text">{{ questionData.summarizedQuestion }}</pre>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { isDev, isTest } from '@/shared/utils/env-helper';

// 定义props
const props = defineProps<{
  modelValue: boolean;
  questionData: {
    id: number;
    questionerMisId: string;
    questionType?: number;
    replyStatus: number;
    dxGroupId: number;
    dxGroupName?: string;
    rawQuestionMsg: string;
    summarizedQuestion: string;
    createTime?: number;
    ctime?: number;
  } | null;
  questionTypes?: Array<{
    questionTypeId: number;
    typeName: string;
  }>;
}>();

// 定义事件
const emit = defineEmits(['update:modelValue']);

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 获取问题类型名称
const getQuestionTypeName = (typeId: number): string => {
  if (!props.questionTypes || props.questionTypes.length === 0) {
    return `类型(${typeId})`;
  }
  
  const questionType = props.questionTypes.find(type => type.questionTypeId === typeId);
  return questionType?.typeName || `类型(${typeId})`;
};

// 格式化日期显示
const formatDateTime = (timestamp?: number): string => {
  if (!timestamp) return '无时间记录';
  
  try {
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return String(timestamp);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  } catch (e) {
    return String(timestamp);
  }
};

// 打开群组聊天
const openGroupChat = (groupId?: number, groupName?: string) => {
  if (!groupId) {
    ElMessage.warning('无效的群组ID');
    return;
  }
  
  // 根据环境决定使用哪个URL
  const baseUrl = isDev() || isTest() 
    ? 'http://xm-web.it.test.sankuai.com/bridge/chat'
    : 'https://x.sankuai.com/bridge/chat';
  
  // 构建完整URL
  const url = `${baseUrl}?gid=${groupId}`;
  
  // 在新窗口中打开URL
  window.open(url, '_blank');
};
</script>

<style scoped lang="scss">
.question-detail-dialog {
  .question-detail {
    padding: 10px;
  }
  
  .content-section {
    margin-top: 20px;
    
    .content-item {
      margin-bottom: 20px;
      
      .content-label {
        font-weight: bold;
        margin-bottom: 8px;
        color: #606266;
      }
      
      .content-value {
        padding: 12px;
        background-color: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        max-height: 300px;
        overflow: auto;
        line-height: 1.5;
        
        .formatted-text {
          margin: 0;
          white-space: pre-wrap;
          word-break: break-all;
          font-family: inherit;
          font-size: inherit;
          line-height: inherit;
          width: 100%;
          overflow-x: auto;
        }
      }
    }
  }
  
  .message-content {
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 300px;
    overflow-y: auto;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
    line-height: 1.5;
    
    .scrollable-text {
      width: 100%;
    }
  }
}
</style> 