<template>
  <div class="monitor-stats-dashboard">
    <!-- 关键统计卡片 -->
    <el-row :gutter="20" class="key-metrics-row">
      <el-col :span="6" v-for="(item, index) in keyMetricCards" :key="item.key">
        <div 
          class="stat-card key-metric-card"
          :class="`card-${index + 1}`"
          @click="handleCardClick(item.key)"
        >
          <div class="card-header">
            <span class="title">{{ item.title }}</span>
            <el-tooltip v-if="item.tooltip" :content="item.tooltip" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="card-content">
            <div class="value key-value">{{ item.value }}</div>
            <div class="description">{{ item.description }}</div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { InquiryDetail, StatisticsResult, QuestionType } from '../types';
import { queryStatistics } from '../request';

const props = defineProps<{
  monitorGroupId: number | null;
  timeRange: [number, number];
  loading: boolean;
  questionTypes?: QuestionType[];
  unansweredCount?: number;
}>();

const emit = defineEmits(['view-details']);

// 统计数据
const statsData = ref<StatisticsResult>({
  unansweredList: [],
  questionTypeCount: 0,
  totalInquirers: 0,
  totalInquiries: 0,
  unansweredCount: 0
});

// 添加一个刷新计数器，用于手动触发统计数据刷新
const refreshCount = ref(0);

// 组件内部loading状态
const localLoading = ref(false);

// 关键指标卡片数据
const keyMetricCards = computed(() => [
  {
    key: 'totalInquiries',
    title: '总咨询数量',
    value: statsData.value.totalInquiries || 0,
    description: '所选时间范围内的咨询总数',
    tooltip: '所选监控组在指定时间范围内的咨询总数'
  },
  {
    key: 'questionTypeCount',
    title: '问题类型数量',
    value: statsData.value.questionTypeCount || 0,
    description: '问题类型的数量',
    tooltip: '监控组中配置的问题类型数量'
  },
  {
    key: 'totalInquirers',
    title: '总咨询人数',
    value: statsData.value.totalInquirers || 0,
    description: '不同咨询人数量',
    tooltip: '不同咨询者的数量'
  },
  {
    key: 'unansweredCount',
    title: '未回答咨询数量',
    value: props.unansweredCount !== undefined ? props.unansweredCount : (statsData.value.unansweredCount || 0),
    description: '未被回答的咨询数量',
    tooltip: '未被回答或处理的咨询数量'
  }
]);

// 监听时间范围和监控组ID变化，重新获取统计数据
watch(
  [() => props.timeRange, () => props.monitorGroupId],
  async ([newTimeRange, newMonitorGroupId], [oldTimeRange, oldMonitorGroupId]) => {
    // 不再主动触发请求，只记录变化
    // 父组件会通过getQueryStatisticsData方法主动获取数据
    
    // 注意：不再在这里调用fetchStatisticsData()，由父组件显式调用刷新方法
  },
  { deep: true }
);

// 提供给父组件的刷新方法
const refresh = async (params?: { startTime: string, endTime: string, monitorGroupIds: number[] }) => {
  
  // 参数校验，确保参数有效
  if (params && (!params.monitorGroupIds || params.monitorGroupIds.length === 0)) {
    console.warn('刷新统计数据参数无效:', params);
    return false;
  }
  
  refreshCount.value++;
  
  // 确保有数据可显示，避免在请求过程中显示空数据
  if (!statsData.value || !statsData.value.totalInquiries) {
    statsData.value = {
      unansweredList: [],
      questionTypeCount: props.questionTypes?.length || 0,
      totalInquirers: 0,
      totalInquiries: 0,
      unansweredCount: 0
    };
  }
  
  try {
    if (params) {
      // 如果提供了参数，则使用参数直接获取统计数据，不依赖props
      await fetchStatisticsDataWithParams(params);
    } else {
      // 否则使用props中的参数获取
      await fetchStatisticsData();
    }
    return true;
  } catch (error) {
    console.error('统计数据刷新失败:', error);
    return false;
  }
};

// 使用指定参数获取统计数据
const fetchStatisticsDataWithParams = async (params: { startTime: string, endTime: string, monitorGroupIds: number[] }) => {
  if (!params.monitorGroupIds || params.monitorGroupIds.length === 0) {
    console.warn('统计数据获取条件不满足', params);
    return;
  }
  
  localLoading.value = true;
  console.log('使用参数获取统计数据:', {
    monitorGroupIds: params.monitorGroupIds,
    startTime: new Date(Number(params.startTime)).toLocaleString(),
    endTime: new Date(Number(params.endTime)).toLocaleString(),
  });
  
  try {
    const response = await queryStatistics({
      startTime: params.startTime,
      endTime: params.endTime,
      monitorGroupIds: params.monitorGroupIds.map(id => String(id))
    });
    
    if (response && response.code === 0 && response.success) {
      // 确保响应数据包含所有需要的字段，并设置默认值为0
      statsData.value = {
        unansweredList: response.data?.unansweredList || [],
        questionTypeCount: response.data?.questionTypeCount || 0,
        totalInquirers: response.data?.totalInquirers || 0,
        totalInquiries: response.data?.totalInquiries || 0,
        unansweredCount: response.data?.unansweredCount || 0,
        questionTypeStatistics: response.data?.questionTypeStatistics || [],
        dxGroupStatistics: response.data?.dxGroupStatistics || [],
        inquiryDetails: response.data?.inquiryDetails || [],
        inquirerStatistics: response.data?.inquirerStatistics || []
      };
    } else {
      console.error('获取统计数据响应错误', response);
      ElMessage.error(response?.msg || '获取统计数据失败');
    }
  } catch (error) {
    console.error('获取统计数据请求异常:', error);
    ElMessage.error('获取统计数据失败，请稍后重试');
  } finally {
    localLoading.value = false;
  }
};

// 更新统计数据显示
const updateStatsDisplay = () => {
  if (!statsData.value) {
    statsData.value = {
      unansweredList: [],
      questionTypeCount: props.questionTypes?.length || 0,
      totalInquirers: 0,
      totalInquiries: 0,
      unansweredCount: 0,
      questionTypeStatistics: [],
      dxGroupStatistics: [],
      inquiryDetails: [],
      inquirerStatistics: []
    };
  } else {
    // 确保所有数值属性都有默认值0，避免显示空白
    statsData.value.questionTypeCount = statsData.value.questionTypeCount || 0;
    statsData.value.totalInquirers = statsData.value.totalInquirers || 0;
    statsData.value.totalInquiries = statsData.value.totalInquiries || 0;
    statsData.value.unansweredCount = statsData.value.unansweredCount || 0;
    
    // 确保各类统计数组都有默认空数组
    statsData.value.questionTypeStatistics = statsData.value.questionTypeStatistics || [];
    statsData.value.dxGroupStatistics = statsData.value.dxGroupStatistics || [];
    statsData.value.inquiryDetails = statsData.value.inquiryDetails || [];
    statsData.value.inquirerStatistics = statsData.value.inquirerStatistics || [];
  }
  
};

/**
 * 获取查询统计数据
 * @param monitorGroupId 监控组ID
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns API响应结果
 */
const getQueryStatisticsData = async (
  monitorGroupId: string, 
  startTime: string, 
  endTime: string
) => {
  const monitorGroupIds = [monitorGroupId];
  
  if (!monitorGroupIds.length) {
    console.error('未提供有效的监控组ID');
    return null;
  }
  
  localLoading.value = true;
  
  try {
    const response = await queryStatistics({
      startTime,
      endTime,
      monitorGroupIds
    });
    
    if (response && response.code === 0) {
      // 确保响应数据包含所有需要的字段，并设置默认值为0
      statsData.value = {
        unansweredList: response.data?.unansweredList || [],
        questionTypeCount: response.data?.questionTypeCount || 0,
        totalInquirers: response.data?.totalInquirers || 0,
        totalInquiries: response.data?.totalInquiries || 0,
        unansweredCount: response.data?.unansweredCount || 0,
        questionTypeStatistics: response.data?.questionTypeStatistics || [],
        dxGroupStatistics: response.data?.dxGroupStatistics || [],
        inquiryDetails: response.data?.inquiryDetails || [],
        inquirerStatistics: response.data?.inquirerStatistics || []
      };
      updateStatsDisplay();
      return response;
    } else {
      console.error('获取统计数据失败', response);
      return null;
    }
  } catch (error) {
    console.error('获取统计数据异常:', error);
    return null;
  } finally {
    localLoading.value = false;
  }
};

// 获取统计数据
const fetchStatisticsData = async () => {
  if (!props.monitorGroupId || !props.timeRange || props.timeRange.length !== 2) {
    console.warn('统计数据获取条件不满足', {
      monitorGroupId: props.monitorGroupId,
      timeRange: props.timeRange
    });
    return;
  }
  
  // 如果父组件正在loading中，则不重复请求
  if (props.loading) {
    return;
  }
  
  localLoading.value = true;
  console.log('获取统计数据参数:', {
    monitorGroupId: props.monitorGroupId,
    startTime: new Date(props.timeRange[0]).toLocaleString(),
    endTime: new Date(props.timeRange[1]).toLocaleString(),
  });
  
  try {
    const response = await queryStatistics({
      startTime: String(props.timeRange[0]),
      endTime: String(props.timeRange[1]),
      monitorGroupIds: [String(props.monitorGroupId)]
    });
    
    if (response && response.code === 0 && response.success) {
      // 确保响应数据包含所有需要的字段，并设置默认值为0
      statsData.value = {
        unansweredList: response.data?.unansweredList || [],
        questionTypeCount: response.data?.questionTypeCount || 0,
        totalInquirers: response.data?.totalInquirers || 0,
        totalInquiries: response.data?.totalInquiries || 0,
        unansweredCount: response.data?.unansweredCount || 0,
        questionTypeStatistics: response.data?.questionTypeStatistics || [],
        dxGroupStatistics: response.data?.dxGroupStatistics || [],
        inquiryDetails: response.data?.inquiryDetails || [],
        inquirerStatistics: response.data?.inquirerStatistics || []
      };
    } else {
      console.error('获取统计数据响应错误', response);
      ElMessage.error(response?.msg || '获取统计数据失败');
    }
  } catch (error) {
    console.error('获取统计数据请求异常:', error);
    ElMessage.error('获取统计数据失败，请稍后重试');
  } finally {
    localLoading.value = false;
  }
};

// 处理卡片点击事件
const handleCardClick = (key: string) => {
  // 根据不同的卡片类型，触发不同的详情查看事件
  emit('view-details', {
    type: key,
    data: statsData.value
  });
};

// 暴露方法
defineExpose({
  refresh,
  getQueryStatisticsData
});
</script>

<style scoped lang="scss">
.monitor-stats-dashboard {
  .key-metrics-row {
    margin-bottom: 16px;
  }
  
  .stat-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 20px;
    height: 160px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #ebeef5;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, #409EFF, #53a8ff);
    }
    
    &.card-1, &.card-2, &.card-3, &.card-4 {
      &::before {
        background: linear-gradient(90deg, #409EFF, #53a8ff);
      }
      .key-value {
        background: linear-gradient(135deg, #409EFF 10%, #53a8ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      border-color: #dcdfe6;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .title {
        font-size: 16px;
        color: #303133;
        font-weight: 500;
      }
      
      .el-icon {
        color: #909399;
        font-size: 16px;
      }
    }
    
    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      .value {
        font-size: 28px;
        color: #303133;
        font-weight: 600;
        margin-bottom: 12px;
      }
      
      .key-value {
        font-size: 42px;
        color: #409EFF;
        font-weight: 700;
      }
      
      .description {
        font-size: 14px;
        color: #606266;
        text-align: center;
      }
    }
  }
}
</style> 