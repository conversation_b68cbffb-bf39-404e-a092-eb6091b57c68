<template>
  <div class="monitor-container">
    <div class="page-header">
      <h2>
        大象群聊咨询问题监控收集
        <el-tooltip content="监控任务执行频率为一天一次" placement="right">
          <el-tag size="small" type="info" style="margin-left: 8px; vertical-align: middle;">每日更新</el-tag>
        </el-tooltip>
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleEditTask" v-if="currentMonitorTask">
          <el-icon><Edit /></el-icon>编辑监控组
        </el-button>
        <el-button type="primary" @click="showAddTaskDialog">
          <el-icon><Plus /></el-icon>新增监控任务
        </el-button>
      </div>
    </div>
    
    <!-- 选择监控组下拉框 -->
    <div class="monitor-selector">
      <el-select
        v-model="selectedMonitoringGroupId"
        placeholder="请选择监控组"
        style="width: 300px"
        @change="handleMonitoringGroupChange"
        :loading="monitoringGroupsLoading"
      >
        <el-option
          v-for="item in monitoringGroups"
          :key="item.monitoringGroupId"
          :label="item.monitoringGroupName"
          :value="item.monitoringGroupId"
        >
          <div class="monitor-option">
            <span style="margin-right: 20px;">{{ item.monitoringGroupName }}</span>
            <el-tag size="small" :type="item.status === 1 ? 'danger' : 'success'" effect="dark">
              {{ item.status === 1 ? '已禁用' : '正常' }}
            </el-tag>
          </div>
        </el-option>
      </el-select>
    </div>
    
    <div class="content-area" v-if="currentMonitorTask">
      <!-- 监控组状态提示 -->
      <div class="monitor-status">
        <el-alert
          v-if="currentMonitorTask.disabled"
          title="当前监控组已禁用，不会收集新的问题数据"
          type="warning"
          :closable="false"
          show-icon
        />
        <div class="monitor-info">
          <div class="monitor-info-left">
            <span class="label">当前监控组：</span>
            <span class="value">{{ currentMonitorTask.monitoringGroupName }}</span>
            <el-tag v-if="currentMonitorTask.disabled" type="danger" effect="dark" size="small">已禁用</el-tag>
            <el-tag v-else type="success" effect="dark" size="small">正常</el-tag>
          </div>
          <el-button 
            class="stats-button" 
            type="primary" 
            @click="goToStatistics" 
            :icon="DataAnalysis"
          >
            统计分析
          </el-button>
        </div>
      </div>
      
      <!-- 咨询问题查询组件 -->
      <InquirySearchCard 
        ref="inquirySearchCardRef"
        :key="inquiryCardKey"
        @update:inquiry-list="handleInquiryListUpdate" 
        :currentMonitorGroupId="currentMonitorTask?.monitoringGroupId"
        :questionTypes="currentMonitorTask?.questionTypes"
        @time-range-change="handleTimeRangeChange"
        @update-statistics="handleUpdateStatistics"
        @hide-statistics="handleHideStatistics"
      />
    </div>
    
    <!-- 未选择监控组时的提示 -->
    <div class="empty-state" v-else>
      <el-empty description="请选择监控组查看统计数据" />
    </div>
    
    <!-- 新增监控任务组件 -->
    <AddMonitorTask ref="addMonitorTaskRef" @success="handleAddTaskSuccess" @cancel="handleAddTaskCancel" />
    
    <!-- 编辑监控任务组件 -->
    <EditMonitorTask 
      ref="editMonitorTaskRef" 
      @success="handleEditTaskSuccess" 
      @cancel="handleEditTaskCancel"
      @delete="handleDeleteTaskSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Plus, Edit, DataAnalysis } from '@element-plus/icons-vue';
import InquirySearchCard from './components/InquirySearchCard.vue';
import AddMonitorTask from './components/AddMonitorTask.vue';
import EditMonitorTask from './components/EditMonitorTask.vue';
import { InquiryDetail, MonitorTask, MonitoringGroupItem } from './types';
import { queryMonitoringGroups } from './request';
import { getCurrentUser } from '@/shared/services/userService';
import MonitorStats from './components/MonitorStats.vue';
import InquiryTrendChart from './components/InquiryTrendChart.vue';

// 获取路由参数
const route = useRoute();
const router = useRouter();
const rgId = ref<number>(Number(route.query.rgId) || 0);

// 当前用户MIS ID
const currentMisId = ref<string>('');

// 监控组列表
const monitoringGroups = ref<MonitoringGroupItem[]>([]);
const monitoringGroupsLoading = ref<boolean>(false);
const selectedMonitoringGroupId = ref<number | null>(null);

// 添加咨询问题列表数据
const inquiryList = ref<InquiryDetail[]>([]);

// 当前选中的监控任务
const currentMonitorTask = ref<MonitorTask | null>(null);

// 添加监控任务组件引用
const addMonitorTaskRef = ref();
const editMonitorTaskRef = ref();
const monitorStatsRef = ref();
const trendChartRef = ref();

// 统计分析相关
const inquiryTimeRange = ref<[number, number]>([
  new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 7 * 24 * 60 * 60 * 1000, // 7天前的0点
  new Date(new Date().setHours(23, 59, 59, 999)).getTime() // 今天的23:59:59
]);
const statisticsLoading = ref(false);

// 新增变量和函数
const showStatistics = ref(false);

// 新增变量：用于强制重建InquirySearchCard组件
const inquiryCardKey = ref(0);

// 添加InquirySearchCard组件的ref
const inquirySearchCardRef = ref();

// 初始化函数
const init = async () => {
  try {
    // 获取当前用户信息
    const userInfo = await getCurrentUser();
    currentMisId.value = userInfo.login;
    
    // 获取监控组列表
    await fetchMonitoringGroups();
    return true; // 返回成功状态
  } catch (error) {
    console.error('初始化失败:', error);
    ElMessage.error('初始化失败，请刷新页面重试');
    return false; // 返回失败状态
  }
};

// 获取监控组列表
const fetchMonitoringGroups = async () => {
  if (!currentMisId.value) {
    ElMessage.warning('未获取到用户ID');
    return;
  }
  
  monitoringGroupsLoading.value = true;
  
  try {
    const response = await queryMonitoringGroups({ misId: currentMisId.value });
    
    if (response?.code === 0 && response.success) {
      // 根据API返回的结构获取监控组列表
      monitoringGroups.value = response.data?.monitoringGroups || [];
      
      // 不自动选择第一个监控组，保持selectedMonitoringGroupId为null，等待用户手动选择
      // 检查URL中是否有指定的监控组ID
      const urlGroupId = Number(route.query.groupId);
      if (urlGroupId && monitoringGroups.value.some(group => group.monitoringGroupId === urlGroupId)) {
        // 如果URL中指定了有效的监控组ID，则选择它
        selectedMonitoringGroupId.value = urlGroupId;
        await fetchMonitorTaskDetail(urlGroupId);
      } else {
        // 不自动选择，让用户手动选择
        selectedMonitoringGroupId.value = null;
        currentMonitorTask.value = null;
      }
    } else {
      ElMessage.error(response?.msg || '获取监控组列表失败');
    }
  } catch (error) {
    console.error('获取监控组列表失败:', error);
    ElMessage.error('获取监控组列表失败');
  } finally {
    monitoringGroupsLoading.value = false;
  }
};

// 处理监控组选择变化
const handleMonitoringGroupChange = async (groupId: number) => {
  if (!groupId) {
    router.replace({ path: '/corpus/monitor' });
    return;
  }
  // 只更新路由，不再手动触发查询
  // 路由变化后，watch监听器会自动处理查询逻辑
  await router.replace({ path: '/corpus/monitor', query: { groupId } });
  
  // 移除以下重复的自动查询逻辑，因为watch监听器会处理
  /* setTimeout(() => {
    if (currentMonitorTask.value && currentMonitorTask.value.monitoringGroupId) {
      if (inquirySearchCardRef.value) {
        inquirySearchCardRef.value.executeQuery();
      } else {
        console.warn('未找到InquirySearchCard组件引用');
      }
    }
  }, 300); */
};

// 获取监控任务详情
const fetchMonitorTaskDetail = async (groupId: number): Promise<void> => {
  try {
    // 从监控组列表中找到选中的监控组
    const selectedGroup = monitoringGroups.value.find(item => item.monitoringGroupId === groupId);
    
    if (selectedGroup) {
      // 使用API返回的问题类型数组
      const questionTypes = selectedGroup.questionTypes || [];
      
      // 根据API返回的完整数据构造监控任务对象
      currentMonitorTask.value = {
        monitoringGroupId: selectedGroup.monitoringGroupId,
        monitoringGroupName: selectedGroup.monitoringGroupName,
        monitoringGroupDesc: selectedGroup.monitoringGroupDesc,
        // 使用API返回的解析好的数组
        monitoringGroupOwner: selectedGroup.monitoringGroupOwnerArray || [],
        dxGroupIds: selectedGroup.dxGroupIdArray || [],
        monitoredOrgIds: selectedGroup.monitoredOrgIdArray || [],
        monitoredMisIds: selectedGroup.monitoredMisIdArray || [],
        keywords: selectedGroup.keywordsArray || [],
        questionTypes: questionTypes, // 使用API返回的问题类型，不再使用默认值
        monitoringTimeRangeType: selectedGroup.monitoringTimeRangeType,
        disabled: selectedGroup.status === 1 // 1表示禁用
      };
      
      // 将完整的监控组信息保存到本地变量
      localStorage.setItem('currentMonitorGroup', JSON.stringify({
        ...selectedGroup,
        // 添加问题类型信息到本地存储
        questionTypes: questionTypes
      }));
      
    } else {
      currentMonitorTask.value = null;
      ElMessage.warning('未找到选中的监控组信息');
    }
  } catch (error) {
    console.error('获取监控任务详情失败:', error);
    ElMessage.error('获取监控任务详情失败');
    throw error; // 向上抛出错误，使调用处的Promise.then能够感知到错误
  }
};

// 处理咨询问题列表更新
const handleInquiryListUpdate = (list: InquiryDetail[]) => {
  inquiryList.value = list;
};

// 显示添加监控任务对话框
const showAddTaskDialog = () => {
  addMonitorTaskRef.value?.open();
};

// 处理添加监控任务成功
const handleAddTaskSuccess = async (task: MonitorTask) => {
  // 新建后跳转到新建监控组
  await fetchMonitoringGroups();
  if (task.monitoringGroupId) {
    router.replace({ path: '/corpus/monitor', query: { groupId: task.monitoringGroupId } });
  } else {
    router.replace({ path: '/corpus/monitor' });
  }
};

// 处理添加监控任务取消
const handleAddTaskCancel = () => {
};

// 处理编辑监控任务
const handleEditTask = () => {
  if (!currentMonitorTask.value || !selectedMonitoringGroupId.value) {
    ElMessage.warning('请先选择监控组');
    return;
  }

  
  // 获取当前选中的监控组信息
  const selectedGroup = monitoringGroups.value.find(
    item => item.monitoringGroupId === selectedMonitoringGroupId.value
  );
  
  if (!selectedGroup) {
    ElMessage.warning('未找到当前监控组信息，请刷新页面重试');
    return;
  }
  
  
  // 克隆当前监控任务信息，避免引用问题
  const taskToEdit = JSON.parse(JSON.stringify(currentMonitorTask.value));
  
  // 确保所有数组字段都是有效的数组
  taskToEdit.monitoringGroupOwner = Array.isArray(taskToEdit.monitoringGroupOwner) 
    ? taskToEdit.monitoringGroupOwner 
    : (selectedGroup.monitoringGroupOwnerArray || []);
    
  taskToEdit.dxGroupIds = Array.isArray(taskToEdit.dxGroupIds) 
    ? taskToEdit.dxGroupIds 
    : (selectedGroup.dxGroupIdArray || []);
    
  taskToEdit.monitoredOrgIds = Array.isArray(taskToEdit.monitoredOrgIds) 
    ? taskToEdit.monitoredOrgIds 
    : (selectedGroup.monitoredOrgIdArray || []);
    
  taskToEdit.monitoredMisIds = Array.isArray(taskToEdit.monitoredMisIds) 
    ? taskToEdit.monitoredMisIds 
    : (selectedGroup.monitoredMisIdArray || []);
    
  taskToEdit.keywords = Array.isArray(taskToEdit.keywords) 
    ? taskToEdit.keywords 
    : (selectedGroup.keywordsArray || []);
  
  // 确保至少有一个问题类型
  if (!Array.isArray(taskToEdit.questionTypes)) {
    taskToEdit.questionTypes = [];
  }
  
  // 确保表单数据包含最新监控组状态
  taskToEdit.monitoringGroupId = selectedMonitoringGroupId.value;
  taskToEdit.status = selectedGroup.status;
  taskToEdit.disabled = selectedGroup.status === 1;
  
  
  // 打开编辑对话框
  editMonitorTaskRef.value?.open(taskToEdit);
};

// 处理编辑监控任务成功
const handleEditTaskSuccess = async (task: MonitorTask) => {
  // 如果需要刷新页面
  if (task.needRefresh) {
    // 更新当前任务
    currentMonitorTask.value = task;
    
    // 刷新监控组列表
    await fetchMonitoringGroups();
  } else {
    // 更新当前任务
    currentMonitorTask.value = task;
  }
  
  // 清空查询结果
  inquiryList.value = [];
  
  // 隐藏统计数据卡片，等待用户点击查询
  showStatistics.value = false;
};

// 处理编辑监控任务取消
const handleEditTaskCancel = () => {
};

// 处理监控任务删除成功
const handleDeleteTaskSuccess = async (taskId: number, needRefresh?: boolean) => {
  
  // 如果当前任务被删除，清空当前任务
  if (currentMonitorTask.value?.monitoringGroupId === taskId) {
    currentMonitorTask.value = null;
    selectedMonitoringGroupId.value = null;
    
    // 清空查询结果
    inquiryList.value = [];
  }
  
  // 刷新监控组列表
  await fetchMonitoringGroups();
  
  // 如果删除后没有选中的监控组，但有其他监控组，则自动选择第一个
  if (!selectedMonitoringGroupId.value && monitoringGroups.value.length > 0) {
    const firstGroupId = monitoringGroups.value[0].monitoringGroupId;
    selectedMonitoringGroupId.value = firstGroupId;
    
    // 获取新选中的监控组详情
    await fetchMonitorTaskDetail(firstGroupId);
    
    ElMessage.success(`已自动切换到监控组: ${currentMonitorTask.value?.monitoringGroupName}`);
  }
};

// 添加刷新列表的函数
const handleRefreshList = async () => {
  // 刷新监控组列表
  await fetchMonitoringGroups();
};

// 监听路由变化，处理参数
watch(
  () => route.query,
  (query) => {
    const urlGroupId = Number(query.groupId);
    const keepState = query.keepState === 'true';
    
    
    if (!urlGroupId) {
      selectedMonitoringGroupId.value = null;
      currentMonitorTask.value = null;
      inquiryList.value = [];
      showStatistics.value = false;
      inquiryTimeRange.value = [
        new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 7 * 24 * 60 * 60 * 1000,
        new Date(new Date().setHours(23, 59, 59, 999)).getTime()
      ];
      inquiryCardKey.value++;
      return;
    }
    
    selectedMonitoringGroupId.value = urlGroupId;
    if (monitoringGroups.value.length > 0) {
      fetchMonitorTaskDetail(urlGroupId).then(() => {
        // 获取到监控任务详情后，延迟一小段时间确保组件已更新，然后自动执行查询
        setTimeout(() => {
          // 如果当前有监控任务信息且查询组件已挂载，调用查询方法
          if (currentMonitorTask.value && inquirySearchCardRef.value) {
            inquirySearchCardRef.value.executeQuery();
            // 记录是从哪种情况触发的查询
            if (keepState) {
            } else {
            }
          } else {
            console.warn('查询组件未就绪，无法执行查询', { 
              currentMonitorTask: !!currentMonitorTask.value, 
              inquirySearchCardRef: !!inquirySearchCardRef.value 
            });
          }
        }, 500); // 增加延迟时间确保组件已挂载
      }).catch(err => {
        console.error('获取监控任务详情失败:', err);
        ElMessage.error('获取监控任务详情失败，请刷新页面重试');
      });
    } else {
      console.warn('监控组列表为空，无法获取监控任务详情');
    }
    
    // 切换监控组时，重置表格、统计、时间范围
    inquiryList.value = [];
    showStatistics.value = false;
    inquiryTimeRange.value = [
      new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 7 * 24 * 60 * 60 * 1000,
      new Date(new Date().setHours(23, 59, 59, 999)).getTime()
    ];
    inquiryCardKey.value++;
  },
  { immediate: true, deep: true }
);

// 在组件挂载后执行初始化
onMounted(() => {
  init().then(() => {
    // 初始化完成后，检查是否需要自动执行查询
    const shouldAutoQuery = localStorage.getItem('monitor_auto_query') === 'true';
    if (shouldAutoQuery) {
      // 清除标记，避免下次进入时重复执行
      localStorage.removeItem('monitor_auto_query');
      
      // 确保组件已经完全挂载和初始化
      setTimeout(() => {
        if (inquirySearchCardRef.value) {
          inquirySearchCardRef.value.executeQuery();
        } else {
          console.warn('无法执行自动查询，查询组件未就绪');
        }
      }, 800); // 使用更长的延迟确保组件完全挂载
    }
  });
});

// 处理统计分析视图详情
const handleStatsViewDetails = (eventData: { type: string, data: any }) => {
  // 根据不同的卡片类型展示不同的详情
  switch (eventData.type) {
    case 'totalInquiries':
      // 显示全部咨询列表 - 当前表格已显示，可以添加过滤或高亮
      ElMessage.info(`共有 ${eventData.data.totalInquiries} 条咨询数据`);
      break;
    case 'questionTypeCount':
      // 显示问题类型详情
      ElMessage.info(`监控组配置了 ${eventData.data.questionTypeCount} 种问题类型`);
      break;
    case 'totalInquirers':
      // 显示咨询人列表
      ElMessage.info(`共有 ${eventData.data.totalInquirers} 个不同的咨询者`);
      break;
    case 'unansweredCount':
      // 显示未回答咨询列表
      if (eventData.data.unansweredCount > 0) {
        ElMessage.info(`共有 ${eventData.data.unansweredCount} 条未回答的咨询`);
      } else {
        ElMessage.info('没有未回答的咨询');
      }
      break;
    default:
      break;
  }
};

// 处理时间范围变化
const handleTimeRangeChange = (newRange: [number, number]) => {
  inquiryTimeRange.value = newRange;
  // 当时间范围变化时，可以在这里处理其他逻辑
  
  // 不再在时间范围变化时显示统计数据，而是仅在查询按钮点击时显示
  // showStatistics.value = true;
};

// 处理统计数据更新
const handleUpdateStatistics = async (params: { startTime: string, endTime: string, monitorGroupIds: number[] }) => {
  
  // 先更新数据
  inquiryTimeRange.value = [Number(params.startTime), Number(params.endTime)];
  statisticsLoading.value = true;
  
  // 立即显示统计卡片，确保UI响应
  showStatistics.value = true;
  
  try {
    
    // 先调用MonitorStats组件的刷新方法获取最新统计数据，直接传入查询参数
    if (monitorStatsRef.value) {
      // 注意：这里await可能导致问题，如果refresh方法内部出错
      monitorStatsRef.value.refresh(params).catch(err => {
        console.error('统计数据刷新出错:', err);
      });
    } else {
      console.warn('未找到统计组件引用，尝试延迟刷新');
      // 如果引用不存在，可能是因为组件还未挂载，尝试延迟获取
      setTimeout(() => {
        if (monitorStatsRef.value) {
          monitorStatsRef.value.refresh(params);
        } else {
          console.error('延迟后仍未找到统计组件引用');
        }
      }, 100);
    }
    
    // 刷新趋势图
    if (trendChartRef.value) {
      trendChartRef.value.refresh().catch(err => {
        console.error('趋势图数据刷新出错:', err);
      });
    }
  } catch (error) {
    console.error('刷新统计数据失败:', error);
    // 即使出错也不隐藏统计卡片，避免UI不一致
  } finally {
    statisticsLoading.value = false;
  }
};

// 处理隐藏统计数据
const handleHideStatistics = () => {
  showStatistics.value = false;
};

// 跳转到统计详情页面
const goToStatistics = () => {
  if (!currentMonitorTask.value?.monitoringGroupId) {
    ElMessage.warning('请先选择监控组');
    return;
  }
  
  // 获取当前的查询时间范围
  let startTime = inquiryTimeRange.value[0];
  let endTime = inquiryTimeRange.value[1];
  
  // 如果没有进行过查询，使用默认的时间范围（最近30天）
  if (!startTime || !endTime) {
    const now = new Date();
    endTime = now.setHours(23, 59, 59, 999);
    startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).setHours(0, 0, 0, 0);
  }
  
  // 确保时间范围不超过90天
  const maxTimeRange = 90 * 24 * 60 * 60 * 1000; // 90天的毫秒数
  if (endTime - startTime > maxTimeRange) {
    startTime = new Date(endTime - maxTimeRange).setHours(0, 0, 0, 0);
    ElMessage.warning('时间范围已自动调整为最近90天');
  }
  
  router.push({
    path: '/corpus/monitor/statistics',
    query: { 
      id: currentMonitorTask.value.monitoringGroupId,
      startTime: String(startTime),
      endTime: String(endTime)
    }
  });
};
</script>

<style scoped lang="scss">
.monitor-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 64px);
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    
    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #303133;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .monitor-selector {
    margin-bottom: 24px;
    
    :deep(.el-select) {
      width: 100%;
    }
  }
  
  .monitor-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    span {
      margin-right: 15px;
    }
  }
  
  .content-area {
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .monitor-status {
    margin-bottom: 20px;
    
    .monitor-info {
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background-color: #f5f7fa;
      border-radius: 4px;
      
      .monitor-info-left {
        display: flex;
        align-items: center;
      }
      
      .label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
      }
      
      .value {
        color: #303133;
        font-weight: 500;
        margin-right: 8px;
      }
      
      .el-tag {
        margin-left: 8px;
      }
      
      .stats-button {
        background: linear-gradient(90deg, #409EFF, #1677FF);
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
        }
      }
    }
  }
  
  .empty-state {
    height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
    
    :deep(.el-empty__description) {
      font-size: 16px;
      color: #909399;
      margin-top: 16px;
    }
    
    :deep(.el-empty__image) {
      width: 180px;
      height: 180px;
    }
  }
}
</style> 