<template>
  <div class="add-monitor-task">
    <el-dialog
      v-model="dialogVisible"
      title="添加监控任务"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="650px"
      :before-close="handleClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        label-position="left"
        class="monitor-form"
      >
        <el-form-item label="监控组名称" prop="monitoringGroupName">
          <el-input v-model="formData.monitoringGroupName" placeholder="请输入监控组名称" />
        </el-form-item>
        
        <el-form-item label="监控组描述" prop="monitoringGroupDesc">
          <el-input v-model="formData.monitoringGroupDesc" placeholder="请输入监控组描述" type="textarea" :rows="2" />
        </el-form-item>
        
        <el-form-item label="监控组负责人" prop="monitoringGroupOwner" class="input-group-item">
          <div class="input-with-button">
            <el-input
              v-model="monitoringGroupOwnerInput"
              placeholder="请输入负责人MIS账号"
              :clearable="!!monitoringGroupOwnerInput"
              @keyup.enter="handleAddOwner"
            />
            <el-button type="primary" @click="handleAddOwner">添加</el-button>
          </div>
        </el-form-item>
        
        <!-- 已添加的负责人标签 -->
        <el-form-item v-show="formData.monitoringGroupOwner.length > 0" label="" class="owner-tags-item">
          <div class="tag-container">
            <el-tag
              v-for="(tag, index) in formData.monitoringGroupOwner"
              :key="index"
              :closable="index !== 0"
              @close="removeOwnerTag(index)"
              class="owner-tag"
              type="primary"
              effect="light"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item prop="dxGroupIds" class="input-group-item">
          <template #label>
            <span class="label-flex">
              <span>大象群ID</span>
              <el-tooltip content="大象群ID可以在大象WEB端获取" placement="top">
                <a
                  href="https://km.sankuai.com/page/982430233"
                  target="_blank"
                  class="question-icon-circle custom-question"
                  tabindex="-1"
                  aria-label="获取大象群ID"
                ></a>
              </el-tooltip>
            </span>
          </template>
          <div class="input-with-button">
            <el-input
              v-model="dxGroupIdInput"
              placeholder="请输入大象群ID"
              :clearable="!!dxGroupIdInput"
              @keyup.enter="handleAddDxGroupId"
            />
            <el-button type="primary" @click="handleAddDxGroupId">添加</el-button>
          </div>
        </el-form-item>
        
        <!-- 已添加的大象群ID标签 -->
        <el-form-item v-show="formData.dxGroupIds.length > 0" label="" class="owner-tags-item">
          <div class="tag-container">
            <el-tag
              v-for="(id, index) in formData.dxGroupIds"
              :key="index"
              closable
              @close="removeDxGroupId(index)"
              class="owner-tag"
              type="primary"
              effect="light"
            >
              {{ id }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="重点关注组织" prop="monitoredOrgIds" class="org-item">
          <div class="input-with-button">
            <el-select
              v-model="orgIdInput"
              filterable
              remote
              reserve-keyword
              placeholder="仅支持输入完整部门链，例如：美团/核心本地商业"
              :remote-method="searchOrg"
              :loading="orgSearchLoading"
              :clearable="!!orgIdInput"
              style="width: 100%"
              class="org-select"
              :popper-class="'org-select-dropdown'"
              value-key="orgId"
            >
              <template #display-value="data">
                {{ data?.name || data }}
              </template>
              <el-option
                v-if="selectedOrg"
                :key="selectedOrg.orgId"
                :label="selectedOrg.name"
                :value="selectedOrg"
              >
                <div class="org-option">
                  <div class="org-name">{{ selectedOrg.name }} ({{ selectedOrg.orgId }})</div>
                  <div class="org-path">{{ selectedOrg.orgNamePath }}</div>
                </div>
              </el-option>
            </el-select>
            <el-button type="primary" @click="handleAddOrgId">添加</el-button>
          </div>
          
          <div class="tag-wrapper">
            <el-tag
              v-for="(orgId, index) in formData.monitoredOrgIds"
              :key="orgId"
              class="tag-item"
              closable
              @close="removeOrgId(index)"
            >
              {{ getOrgDisplayName(orgId) }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="重点关注用户" prop="monitoredMisIds" class="input-group-item">
          <div class="input-with-button">
            <el-input
              v-model="misInput"
              placeholder="请输入用户MIS账号"
              :clearable="!!misInput"
              @keyup.enter="handleAddMis"
            />
            <el-button type="primary" @click="handleAddMis">添加</el-button>
          </div>
        </el-form-item>
        
        <!-- 已添加的用户MIS标签 -->
        <el-form-item v-show="formData.monitoredMisIds.length > 0" label="" class="owner-tags-item">
          <div class="tag-container">
            <el-tag
              v-for="(mis, index) in formData.monitoredMisIds"
              :key="index"
              closable
              @close="removeMis(index)"
              class="owner-tag"
              type="primary"
              effect="light"
            >
              {{ mis }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="关键词" prop="keywords" class="input-group-item">
          <div class="input-with-button">
            <el-input
              v-model="keywordInput"
              placeholder="请输入关键词"
              :clearable="!!keywordInput"
              @keyup.enter="handleAddKeyword"
            />
            <el-button type="primary" @click="handleAddKeyword">添加</el-button>
          </div>
        </el-form-item>
        
        <!-- 已添加的关键词标签 -->
        <el-form-item v-show="formData.keywords.length > 0" label="" class="owner-tags-item">
          <div class="tag-container">
            <el-tag
              v-for="(keyword, index) in formData.keywords"
              :key="index"
              closable
              @close="removeKeyword(index)"
              class="owner-tag"
              type="primary"
              effect="light"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="问题类型" prop="questionTypes">
          <el-table
            :data="formData.questionTypes"
            border
            style="width: 100%"
            max-height="200"
            row-key="questionTypeId"
            empty-text="点击下方按钮添加问题类型"
          >
            <el-table-column label="序号" width="60" align="center">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="typeName" label="类型名称" min-width="120">
              <template #default="scope">
                <el-input v-model="scope.row.typeName" placeholder="请输入类型名称" />
              </template>
            </el-table-column>
            <el-table-column prop="typeDesc" label="类型描述" min-width="150">
              <template #default="scope">
                <el-input v-model="scope.row.typeDesc" placeholder="请输入类型描述" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <div class="operation-btns">
                  <el-button v-if="scope.$index !== 0" type="primary" text @click="moveQuestionTypeUp(scope.$index)">
                    <el-icon><ArrowUp /></el-icon>
                  </el-button>
                  <el-button v-if="scope.$index !== formData.questionTypes.length - 1" type="primary" text @click="moveQuestionTypeDown(scope.$index)">
                    <el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <el-button type="danger" text @click="removeQuestionType(scope.$index)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="form-actions">
            <el-button type="primary" link @click="addQuestionType">
              <el-icon><Plus /></el-icon>添加问题类型
            </el-button>
          </div>
        </el-form-item>
        
        <!-- 监控时间段 -->
        <el-form-item label="监控时间段" prop="monitoringTimeRangeType" class="mr30">
          <el-radio-group v-model="formData.monitoringTimeRangeType">
            <el-radio :label="0">7天</el-radio>
            <el-radio :label="1">14天</el-radio>
            <el-radio v-if="show30DaysOption" :label="2">30天</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose, nextTick, onMounted, watch } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { Plus, ArrowUp, ArrowDown, InfoFilled, QuestionFilled } from '@element-plus/icons-vue';
import { QuestionType, MonitorTask, CreateMonitorTaskRequest, OrgSearchResultItem } from '../types';
import { createMonitorTask, queryOrgByNamePath, queryOrgById, getGrayscaleConfig } from '../request';
import { getCurrentUser } from '@/shared/services/userService';
import httpRequest from '@/utils/httpRequest';

// 定义组件事件
const emit = defineEmits(['success', 'cancel']);

// 表单引用
const formRef = ref<FormInstance>();

// 对话框控制
const dialogVisible = ref(false);
const submitLoading = ref(false);

// 负责人输入框
const monitoringGroupOwnerInput = ref('');
const dxGroupIdInput = ref('');
const orgIdInput = ref('');
const misInput = ref('');
const keywordInput = ref('');

// 组织相关
const orgSearchLoading = ref(false);
const selectedOrg = ref<any>(null);
const orgCache = ref<Record<string, OrgSearchResultItem>>({});

// 组织名称缓存
const orgNameCache = ref<Record<string, string>>({});

// 灰度测试相关
const currentUserMis = ref(''); // 当前用户MIS
const show30DaysOption = ref(false); // 是否显示30天选项

// 表单数据
const formData = reactive<MonitorTask>({
  monitoringGroupName: '',
  monitoringGroupDesc: '',
  monitoringGroupOwner: [],
  dxGroupIds: [],
  monitoredOrgIds: [],
  monitoredMisIds: [],
  keywords: [],
  questionTypes: [],
  monitoringTimeRangeType: 0 // 默认选择7天(值为0)
});

// 表单数据与后端映射关系说明
// monitoringTimeRangeType: 0/1/2 - 对应后端枚举值，映射关系：7天->0, 14天->1, 30天->2

// 表单校验规则
const rules = {
  monitoringGroupName: [
    { required: true, message: '请输入监控组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  monitoringGroupDesc: [
    { required: true, message: '请输入监控组描述', trigger: 'blur' },
    { max: 200, message: '长度不超过 200 个字符', trigger: 'blur' }
  ],
  monitoringGroupOwner: [
    { required: true, message: '请添加至少一个负责人', trigger: 'change' },
    { type: 'array', min: 1, message: '请添加至少一个负责人', trigger: 'change' }
  ],
  dxGroupIds: [
    { required: true, message: '请添加至少一个大象群ID', trigger: 'change' },
    { type: 'array', min: 1, message: '请添加至少一个大象群ID', trigger: 'change' }
  ],
  keywords: [
    { required: true, message: '请添加至少一个关键词', trigger: 'change' },
    { type: 'array', min: 1, message: '请添加至少一个关键词', trigger: 'change' }
  ],
  questionTypes: [
    { required: true, type: 'array', min: 1, message: '请添加至少一个问题类型', trigger: 'change' }
  ],
  monitoringTimeRangeType: [
    { required: true, message: '请选择监控时间范围', trigger: 'change' }
  ],
  monitoredOrgIds: [
    { type: 'array', message: '组织ID必须是数组', trigger: 'change' }
  ]
};

// 初始化当前用户信息
const initCurrentUser = async () => {
  try {
    // 使用userService中的getCurrentUser方法获取用户信息
    const userInfo = await getCurrentUser();
    if (userInfo && userInfo.login) {
      currentUserMis.value = userInfo.login;
    } else {
      console.error('获取用户信息失败：用户信息不完整');
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
  }
};

// 检查灰度配置
const checkGrayscaleConfig = async () => {
  try {
    // 默认不显示30天选项
    show30DaysOption.value = false;
    
    // 获取灰度配置
    const res = await getGrayscaleConfig();
    
    if (res && res.code === 0 && res.data && res.data.config) {
      const { monitorGroupIds, misIds } = res.data.config;
      
      // 如果都为空数组，表示全量开放
      if ((!monitorGroupIds || monitorGroupIds.length === 0) && 
          (!misIds || misIds.length === 0)) {
        show30DaysOption.value = true;
        return;
      }
      
      // 检查用户MIS是否在白名单中（新增监控任务时，只能检查用户MIS）
      const misInWhitelist = misIds && misIds.includes(currentUserMis.value);
      
      // 新增任务时，只能依据用户MIS判断
      show30DaysOption.value = misInWhitelist;
    }
  } catch (error) {
    console.error('检查灰度配置失败:', error);
    // 发生错误时，不显示30天选项
    show30DaysOption.value = false;
  }
};

// 修改 open 方法，在打开添加对话框时检查灰度配置
const open = async () => {
  dialogVisible.value = true;
  resetForm();
  await checkGrayscaleConfig();
  if (currentUserMis.value && !formData.monitoringGroupOwner.includes(currentUserMis.value)) {
    formData.monitoringGroupOwner.unshift(currentUserMis.value);
  }
  // 问题类型为空时默认加一个"其他"类型
  if (!formData.questionTypes || formData.questionTypes.length === 0) {
    formData.questionTypes = [{ typeName: '其他', typeDesc: '其他问题', sortOrder: 1 }];
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  emit('cancel');
};

// 处理负责人输入变化
const handleAddOwner = () => {
  if (!monitoringGroupOwnerInput.value) {
    return;
  }
  
  // 处理输入的MIS账号
  const owner = monitoringGroupOwnerInput.value.trim();
  
  // 检查是否为空
  if (!owner) {
    return;
  }
  
  // 检查是否已存在
  if (!formData.monitoringGroupOwner.includes(owner)) {
    formData.monitoringGroupOwner.push(owner);
  } else {
    ElMessage.warning(`负责人 ${owner} 已存在`);
  }
  
  // 清空输入框
  monitoringGroupOwnerInput.value = '';
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoringGroupOwner');
};

// 移除负责人标签
const removeOwnerTag = (index: number) => {
  formData.monitoringGroupOwner.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoringGroupOwner');
};

// 重置表单
const resetForm = () => {
  nextTick(() => {
    if (formRef.value) {
      formRef.value.resetFields();
    }
    monitoringGroupOwnerInput.value = '';
    dxGroupIdInput.value = '';
    orgIdInput.value = '';
    misInput.value = '';
    keywordInput.value = '';
    Object.assign(formData, {
      monitoringGroupName: '',
      monitoringGroupDesc: '',
      monitoringGroupOwner: [],
      dxGroupIds: [],
      monitoredOrgIds: [],
      monitoredMisIds: [],
      keywords: [],
      questionTypes: [
        { typeName: '其他', typeDesc: '其他问题', sortOrder: 1 }
      ],
      monitoringTimeRangeType: 0
    });
    clearAllTags();
    if (currentUserMis.value) {
      formData.monitoringGroupOwner.unshift(currentUserMis.value);
    }
  });
};

// 清除所有标签的方法
const clearAllTags = () => {
  // 清空所有标签数组
  formData.monitoringGroupOwner = [];
  formData.dxGroupIds = [];
  formData.monitoredOrgIds = [];
  formData.monitoredMisIds = [];
  formData.keywords = [];
  
  // 移除可能存在的清除按钮
  const clearButtons = document.querySelectorAll('.el-input__clear');
  clearButtons.forEach(button => {
    (button as HTMLElement).style.display = 'none';
  });
};

// 添加问题类型
const addQuestionType = () => {
  // 生成新的问题类型ID (自增)
  const newId = formData.questionTypes.length > 0 
    ? Math.max(...formData.questionTypes.map(item => item.questionTypeId)) + 1 
    : 1;
  
  // 直接添加一个新的问题类型到表格
  formData.questionTypes.push({
    questionTypeId: newId,
    typeName: '',     // 使用typeName以保持前端内部一致性
    typeDesc: '',     // 使用typeDesc以保持前端内部一致性
    sortOrder: formData.questionTypes.length + 1 // 设置排序序号为当前长度+1
  });
};

// 移除问题类型
const removeQuestionType = (index: number) => {
  formData.questionTypes.splice(index, 1);
  
  // 重新计算排序序号
  formData.questionTypes.forEach((item, idx) => {
    item.sortOrder = idx + 1;
  });
  
  // 触发表单验证更新
  formRef.value?.validateField('questionTypes');
};

// 上移问题类型
const moveQuestionTypeUp = (index: number) => {
  if (index > 0) {
    const temp = formData.questionTypes[index];
    formData.questionTypes[index] = formData.questionTypes[index - 1];
    formData.questionTypes[index - 1] = temp;
    
    // 调整排序序号
    formData.questionTypes[index].sortOrder = index + 1;
    formData.questionTypes[index - 1].sortOrder = index;
  }
};

// 下移问题类型
const moveQuestionTypeDown = (index: number) => {
  if (index < formData.questionTypes.length - 1) {
    const temp = formData.questionTypes[index];
    formData.questionTypes[index] = formData.questionTypes[index + 1];
    formData.questionTypes[index + 1] = temp;
    
    // 调整排序序号
    formData.questionTypes[index].sortOrder = index + 1;
    formData.questionTypes[index + 1].sortOrder = index + 2;
  }
};

// 处理大象群ID输入
const handleAddDxGroupId = () => {
  if (!dxGroupIdInput.value) {
    return;
  }
  
  // 处理输入的群ID
  const idStr = dxGroupIdInput.value.trim();
  
  // 检查是否为空
  if (!idStr) {
    return;
  }
  
  // 检查是否为数字
  const id = Number(idStr);
  if (isNaN(id)) {
    ElMessage.warning('请输入有效的数字ID');
    return;
  }
  
  // 检查是否已存在
  if (!formData.dxGroupIds.includes(id)) {
    formData.dxGroupIds.push(id);
  } else {
    ElMessage.warning(`大象群ID ${id} 已存在`);
  }
  
  // 清空输入框
  dxGroupIdInput.value = '';
  
  // 触发表单验证更新
  formRef.value?.validateField('dxGroupIds');
};

// 移除大象群ID标签
const removeDxGroupId = (index: number) => {
  formData.dxGroupIds.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('dxGroupIds');
};

// 在组件加载后，预先加载样式和初始化
onMounted(async () => {
  // 添加全局样式，确保下拉菜单中的文本能够完整显示
  const style = document.createElement('style');
  style.textContent = `
    .el-select-dropdown__item-text {
      white-space: normal !important;
      word-break: break-all !important;
      overflow: visible !important;
      width: 100% !important;
      display: inline-block !important;
    }
    
    .el-select-dropdown .el-select-dropdown__item {
      height: auto !important;
      line-height: 1.5 !important;
      padding: 10px !important;
    }
    
    /* 确保select输入框内的文本也能够显示完整 */
    .el-select__tags-text,
    .el-select .el-input__inner {
      white-space: normal !important;
      word-break: break-all !important;
    }
    
    /* 自定义清除按钮样式，只在有内容时显示 */
    .el-input__clear {
      display: none !important;
    }
    
    .el-input.is-focus .el-input__clear,
    .el-input:hover .el-input__clear {
      display: none !important;
    }
  `;
  document.head.appendChild(style);
  
  // 获取当前用户信息
  await initCurrentUser();
  
  // 加载所有组织信息
  nextTick(() => {
    loadAllOrgInfo();
  });
  
  // 初始化清除按钮处理
  setTimeout(initClearButtonHandlers, 100);
});

// 初始化清除按钮处理函数
const initClearButtonHandlers = () => {
  // 移除所有清除按钮
  const clearButtons = document.querySelectorAll('.el-input__clear');
  clearButtons.forEach(button => {
    (button as HTMLElement).style.display = 'none';
  });
  
  // 监听输入框的focus事件，确保聚焦时也不显示清除按钮
  const inputWrappers = document.querySelectorAll('.el-input__wrapper');
  inputWrappers.forEach(wrapper => {
    wrapper.addEventListener('mouseenter', () => {
      const clearBtn = wrapper.querySelector('.el-input__clear') as HTMLElement;
      if (clearBtn) clearBtn.style.display = 'none';
    });
  });
};

// 监听formData.monitoredOrgIds变化，加载新添加的组织信息
watch(() => formData.monitoredOrgIds, (newIds, oldIds) => {
  if (newIds && newIds.length > 0) {
    // 找出新增的ID
    const newAddedIds = newIds.filter(id => !oldIds || !oldIds.includes(id));
    newAddedIds.forEach(orgId => {
      if (!orgNameCache.value[orgId] && !orgCache.value[orgId]) {
        loadOrgInfo(orgId);
      }
    });
  }
}, { deep: true });

// 通过组织路径搜索组织
const searchOrg = async (query: string) => {
  if (!query || query.trim() === '') {
    selectedOrg.value = null;
    return;
  }
  
  orgSearchLoading.value = true;
  try {
    const res = await queryOrgByNamePath(query);
    if (res?.code === 0 && res.data) {
      // 如果查询成功并返回了组织数据
      selectedOrg.value = res.data;
      
      // 更新缓存
      if (selectedOrg.value.orgId) {
        orgCache.value[selectedOrg.value.orgId] = selectedOrg.value;
        orgNameCache.value[selectedOrg.value.orgId] = selectedOrg.value.name;
      }
    } else {
      // 如果查询失败或没有返回组织数据
      selectedOrg.value = null;
    }
  } catch (error) {
    console.error('查询组织失败:', error);
    selectedOrg.value = null;
  } finally {
    orgSearchLoading.value = false;
  }
};

// 获取组织显示名称
const getOrgDisplayName = (orgId: string): string => {
  // 如果名称缓存中已有，直接返回
  if (orgNameCache.value[orgId]) {
    return orgNameCache.value[orgId];
  }
  
  // 如果对象缓存中有完整信息
  if (orgCache.value[orgId]) {
    const org = orgCache.value[orgId];
    // 更新名称缓存
    orgNameCache.value[orgId] = org.name || org.orgName || `${orgId}`;
    return orgNameCache.value[orgId];
  }
  
  // 如果都没有，查询API获取组织信息
  loadOrgInfo(orgId);
  
  // 在获取到之前，显示ID
  return `${orgId}`;
};

// 加载组织信息
const loadOrgInfo = async (orgId: string) => {
  try {
    // 调用接口获取组织详情
    const res = await queryOrgById(orgId);
    if (res && res.code === 0 && res.data) {
      // 保存到缓存中
      orgNameCache.value[orgId] = res.data.name || res.data.orgName || `${orgId}`;
      
      // 如果不在对象缓存中，也添加进去
      if (!orgCache.value[orgId]) {
        orgCache.value[orgId] = res.data;
      }
    }
  } catch (error) {
    console.error(`获取组织[${orgId}]信息失败:`, error);
  }
};

// 批量加载所有组织信息
const loadAllOrgInfo = () => {
  // 加载所有已选组织的信息
  if (formData.monitoredOrgIds && formData.monitoredOrgIds.length > 0) {
    formData.monitoredOrgIds.forEach(orgId => {
      if (!orgNameCache.value[orgId] && !orgCache.value[orgId]) {
        loadOrgInfo(orgId);
      }
    });
  }
};

// 处理组织ID添加
const handleAddOrgId = () => {
  if (!orgIdInput.value || !selectedOrg.value) {
    ElMessage.warning('请先搜索并选择一个有效的组织');
    return;
  }
  
  const orgId = selectedOrg.value.orgId;
  
  // 确保有组织ID
  if (!orgId) {
    ElMessage.warning('所选组织无效');
    return;
  }
  
  // 检查是否已存在
  if (!formData.monitoredOrgIds.includes(orgId)) {
    // 将组织ID添加到表单数据中
    formData.monitoredOrgIds.push(orgId);
    
    // 缓存完整的组织对象
    if (!orgCache.value[orgId]) {
      orgCache.value[orgId] = selectedOrg.value;
    }
    
    // 缓存组织名称
    if (!orgNameCache.value[orgId]) {
      orgNameCache.value[orgId] = selectedOrg.value.name;
    }
  } else {
    ElMessage.warning(`组织 ${selectedOrg.value.name} 已存在`);
  }
  
  // 清空输入框和选中的组织
  orgIdInput.value = '';
  selectedOrg.value = null;
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoredOrgIds');
};

// 移除组织ID标签
const removeOrgId = (index: number) => {
  formData.monitoredOrgIds.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoredOrgIds');
};

// 处理用户MIS输入
const handleAddMis = () => {
  if (!misInput.value) {
    return;
  }
  
  // 处理输入的用户MIS，确保是字符串
  const mis = String(misInput.value).trim();
  
  // 检查是否为空
  if (!mis) {
    return;
  }
  
  // 检查是否已存在
  if (!formData.monitoredMisIds.includes(mis)) {
    formData.monitoredMisIds.push(mis);
  } else {
    ElMessage.warning(`用户MIS ${mis} 已存在`);
  }
  
  // 清空输入框
  misInput.value = '';
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoredMisIds');
};

// 移除用户MIS标签
const removeMis = (index: number) => {
  formData.monitoredMisIds.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('monitoredMisIds');
};

// 处理关键词输入
const handleAddKeyword = () => {
  if (!keywordInput.value) {
    return;
  }
  
  // 处理输入的关键词
  const keyword = keywordInput.value.trim();
  
  // 检查是否为空
  if (!keyword) {
    return;
  }
  
  // 检查是否已存在
  if (!formData.keywords.includes(keyword)) {
    formData.keywords.push(keyword);
  } else {
    ElMessage.warning(`关键词 ${keyword} 已存在`);
  }
  
  // 清空输入框
  keywordInput.value = '';
  
  // 触发表单验证更新
  formRef.value?.validateField('keywords');
};

// 移除关键词标签
const removeKeyword = (index: number) => {
  formData.keywords.splice(index, 1);
  
  // 触发表单验证更新
  formRef.value?.validateField('keywords');
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return;
    
    // 检查问题类型
    const hasEmptyQuestionType = formData.questionTypes.some(
      (item) => !item.typeName || !item.typeDesc
    );
    
    if (hasEmptyQuestionType) {
      ElMessage.warning('问题类型名称和描述不能为空');
      return;
    }
    
    submitLoading.value = true;
    
    try {
      // 构造问题类型数据，确保排序序号正确
      const questionTypes = formData.questionTypes.map((type, index) => ({
        questionTypeId: type.questionTypeId,
        questionTypeName: type.typeName, // 将typeName映射为questionTypeName
        questionTypeDesc: type.typeDesc, // 将typeDesc映射为questionTypeDesc
        sortOrder: type.sortOrder || (index + 1)
      }));
      
      // 构造请求数据
      const requestData = {
        ...formData,
        questionTypes,
        status: 0, // 新建任务默认状态为正常(0)
        // 确保monitoredOrgIds是非空字符串数组
        monitoredOrgIds: formData.monitoredOrgIds,
        // 确保monitoredMisIds是非空字符串数组
        monitoredMisIds: formData.monitoredMisIds
      };
      
      // 确保dxGroupIds中的值是数字
      requestData.dxGroupIds = requestData.dxGroupIds.map(id => typeof id === 'string' ? Number(id) : id);
      
      // 调用API创建监控任务
      const res = await createMonitorTask(requestData);
      
      if (res?.code === 0 && res.success) {
        // 获取创建成功返回的任务ID
        const monitoringGroupId = res.data?.monitoringGroupId || 0;
        
        ElMessage.success('创建监控任务成功');
        dialogVisible.value = false;
        // 返回创建的任务数据，包括ID
        emit('success', { 
          ...requestData, 
          monitoringGroupId,
          needRefresh: true // 添加需要刷新的标志
        });
      } else {
        ElMessage.error(res?.msg || '创建监控任务失败');
      }
    } catch (error) {
      console.error('创建监控任务失败:', error);
      ElMessage.error('创建监控任务失败，请稍后重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped lang="scss">
.add-monitor-task {
  .monitor-form {
    :deep(.el-form-item__label) {
      font-weight: 500;
    }
    
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  .form-actions {
    margin-top: 10px;
    display: flex;
    justify-content: center;
  }
  
  .input-with-button {
    display: flex;
    gap: 10px;
    width: 100%;
    
    .el-input {
      flex: 1;
    }
    
    .el-button {
      width: 80px;
      flex-shrink: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  
  .input-group-item {
    margin-bottom: 15px;
  }
  
  .owner-tags-item {
    margin-top: -8px;
    margin-bottom: 18px;
    
    :deep(.el-form-item__content) {
      margin-left: 120px !important;
    }
  }
  
  .tag-container {
    margin-top: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 5px 0;
    
    .owner-tag {
      margin-right: 0;
    }
  }
  
  :deep(.el-form-item__content) {
    width: calc(100% - 120px);
  }
  
  :deep(.el-input) {
    width: 100%;
  }
  
  .operation-btns {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 5px;
  }
  
  .tag-item {
    margin-right: 8px;
    margin-bottom: 8px;
  }
  
  .tag-wrapper {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
  }
  
  .org-item {
    margin-bottom: 18px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  
  .el-button + .el-button {
    margin-left: 10px;
  }
}

.org-option {
  display: flex;
  flex-direction: column;
  padding: 0 5px;
  
  .org-name {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .org-path {
    font-size: 12px;
    color: #909399;
  }
}

.input-with-button {
  display: flex;
  align-items: center;
  gap: 10px;
  
  .el-select {
    flex: 1;
  }
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.tag-list .el-tag {
  margin-right: 0;
  display: flex;
  align-items: center;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.subordinate-tip {
  margin-top: 10px;
  text-align: center;
}

:deep(.org-select-dropdown) {
  max-height: 400px !important;
  min-width: 500px !important;
  
  .el-select-dropdown__wrap {
    max-height: 380px !important;
  }
  
  .el-select-dropdown__item {
    height: auto;
    padding: 8px 10px;
    line-height: 1.5;
    white-space: normal;
    word-break: break-all;
    
    &.hover, &:hover {
      .org-path {
        display: block !important;
      }
    }
  }
}

.grayscale-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  
  .el-icon {
    margin-right: 4px;
    color: #e6a23c;
  }
}

.icon-link {
  display: flex;
  align-items: center;
  margin-left: 8px;
  text-decoration: none;
}

.label-flex {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  
  .question-icon-circle.custom-question {
    margin-left: 8px;
  }
}

.question-icon-circle.custom-question {
  margin-left: 8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #909399;
  color: #fff;
  font-size: 10px;
  cursor: pointer;
  text-decoration: none;
  position: relative;
  transition: background 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background: #606266;
  }
  &::before {
    content: '?';
    display: block;
    font-size: 11px;
    font-weight: bold;
    color: #fff;
    line-height: 16px;
    text-align: center;
    width: 100%;
    height: 100%;
  }
}

.label-link {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
  font-weight: 500;
  &:hover {
    color: #409eff;
  }
}
</style> 