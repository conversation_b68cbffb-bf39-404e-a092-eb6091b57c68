<template>
  <div class="operation-status-dashboard">
    <!-- 关键统计卡片 -->
    <el-row :gutter="16" class="key-metrics-row">
      <el-col :span="6" v-for="item in keyMetricCards" :key="item.key">
        <div
          class="stat-card key-metric-card"
          @click="handleCardClick(item.key)"
        >
          <div class="card-header">
            <span class="title">{{ item.title }}</span>
            <el-tooltip v-if="item.tooltip" :content="item.tooltip" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="card-content">
            <div class="value key-value">{{ item.value }}</div>
            <div class="description">{{ item.description }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 次要统计卡片 -->
    <el-row :gutter="16" class="secondary-metrics-row">
      <el-col :span="8" v-for="item in secondaryMetricCards" :key="item.key">
        <div
          class="stat-card secondary-card"
          @click="handleCardClick(item.key)"
        >
          <div class="card-header">
            <span class="title">{{ item.title }}</span>
            <el-tooltip v-if="item.tooltip" :content="item.tooltip" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="card-content">
            <div class="value">{{ item.value }}</div>
            <div class="description">{{ item.description }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <!-- 语料生成趋势图 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card trend-chart-card">
          <template #header>
            <div class="card-header">
              <span>语料生成趋势（本周期）</span>
            </div>
          </template>
          <div class="chart-container trend-chart-container" ref="taskTrendChartRef"></div>
        </el-card>
      </el-col>

      <!-- 语料生成触发来源 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card pie-chart-card">
          <template #header>
            <div class="card-header">
              <span>语料生成触发来源（本周期）</span>
            </div>
          </template>
          <div class="chart-container pie-chart-container" ref="platformSourceChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 部门排名图表（占满整行） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card rank-chart-card">
          <template #header>
            <div class="chart-header">
              <div class="title-container">
                <div class="title-left">
                  <div class="chart-title">语料生成使用频率部门排名</div>
                  <el-tooltip
                    content="展示部门按照语料生成使用次数从高到低排序，点击可查看部门详情。"
                    placement="top"
                    effect="light"
                  >
                    <el-icon class="help-icon"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
                <div class="chart-actions">
                  <el-button type="text" @click="showAllOrgRankDialog = true">查看全部</el-button>
                </div>
              </div>
              <div class="chart-subtitle">按照每个部门的使用次数从高到低排序（前10）</div>
            </div>
          </template>
          <div class="chart-container rank-chart-container" ref="orgRankChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 值班组语料数量排名图表（占满整行） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card rank-chart-card">
          <template #header>
            <div class="chart-header">
              <div class="title-container">
                <div class="title-left">
                  <div class="chart-title">值班组语料数量排名</div>
                  <el-tooltip
                    content="展示值班组按照本周期语料数量从高到低排序，点击可查看值班组详情。"
                    placement="top"
                    effect="light"
                  >
                    <el-icon class="help-icon"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
                <div class="chart-actions">
                  <el-button type="text" @click="showAllRgRankDialog = true">查看所有</el-button>
                </div>
              </div>
              <div class="chart-subtitle">按照每个值班组的本周期语料数量从高到低排序（前10）</div>
            </div>
          </template>
          <div class="chart-container rank-chart-container" ref="rgCorpusRankChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 值班组历史语料总数排名图表（占满整行） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card rank-chart-card">
          <template #header>
            <div class="chart-header">
              <div class="title-container">
                <div class="title-left">
                  <div class="chart-title">值班组历史语料总数排名</div>
                  <el-tooltip
                    content="展示值班组按照历史语料总数从高到低排序，点击可查看值班组详情。"
                    placement="top"
                    effect="light"
                  >
                    <el-icon class="help-icon"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
                <div class="chart-actions">
                  <el-button type="text" @click="showAllTotalCorpusRankDialog = true">查看所有</el-button>
                </div>
              </div>
              <div class="chart-subtitle">按照每个值班组的历史语料总数从高到低排序（前10）</div>
            </div>
          </template>
          <div class="chart-container rank-chart-container" ref="totalCorpusRankChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 接入的值班组列表弹窗 -->
    <el-dialog
      v-model="showActiveRgDialog"
      title="接入的值班组列表（全部数据）"
      width="80%"
      :before-close="() => showActiveRgDialog = false"
    >
      <el-table :data="activeRgList" style="width: 100%" max-height="500">
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="rgName" label="值班组名称" min-width="150" />
        <el-table-column prop="rgId" label="值班组ID" width="100" />
        <el-table-column prop="rgOwner" label="负责人" width="100" />
        <el-table-column prop="rgOrgName" label="所属组织" width="150" show-overflow-tooltip />
        <el-table-column prop="totalCorpusCount" label="历史语料总数" width="120" />
        <el-table-column prop="timeRangeCorpusCount" label="本周期语料数" width="140" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.timeRangeCorpusCount > 0 ? 'success' : 'info'">
              {{ scope.row.timeRangeCorpusCount > 0 ? '活跃' : '非活跃' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="text" @click="viewRgDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 小助手被@次数详情弹窗 -->
    <el-dialog
      v-model="showBotMessagesDialog"
      title="小助手被@次数详情"
      width="80%"
      :before-close="() => showBotMessagesDialog = false"
    >
      <el-table :data="botMessagesList" style="width: 100%" max-height="400">
        <el-table-column prop="msgId" label="消息ID" width="120" />
        <el-table-column prop="fromName" label="发送者" width="100" />
        <el-table-column prop="fromMis" label="MIS ID" width="120" />
        <el-table-column prop="message" label="消息内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="userOrgName" label="组织" width="150" show-overflow-tooltip />
        <el-table-column prop="cts" label="发送时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.cts) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 值班组信息详情弹窗 -->
    <el-dialog
      v-model="showRgInfoDialog"
      title="接入的值班组详情"
      width="80%"
      :before-close="() => showRgInfoDialog = false"
    >
      <el-table :data="rgInfoList" style="width: 100%" max-height="400">
        <el-table-column prop="rgName" label="值班组名称" min-width="150" />
        <el-table-column prop="rgId" label="值班组ID" width="100" />
        <el-table-column prop="rgOwner" label="负责人" width="100" />
        <el-table-column prop="rgOrgName" label="所属组织" width="150" show-overflow-tooltip />
        <el-table-column prop="timeRangeCorpusCount" label="时间范围内语料数" width="140" />
        <el-table-column prop="totalCorpusCount" label="历史语料总数" width="120" />
      </el-table>
    </el-dialog>

    <!-- Friday空间详情弹窗 -->
    <el-dialog
      v-model="showFridaySpaceDialog"
      title="接入的Friday空间详情"
      width="80%"
      :before-close="() => showFridaySpaceDialog = false"
    >
      <el-table :data="fridaySpaceList" style="width: 100%" max-height="400">
        <el-table-column prop="spaceName" label="空间名称" min-width="150" />
        <el-table-column prop="spaceId" label="空间ID" width="100" />
        <el-table-column prop="spaceDesc" label="空间描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ctime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.ctime) }}
          </template>
        </el-table-column>
        <el-table-column prop="spaceRgIds" label="绑定值班组" width="150">
          <template #default="scope">
            {{ scope.row.spaceRgIds.join(', ') }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 未保存的语料任务详情弹窗 -->
    <el-dialog
      v-model="showUnsavedTasksDialog"
      title="未保存的语料任务详情"
      width="80%"
      :before-close="() => showUnsavedTasksDialog = false"
    >
      <el-table :data="unsavedTaskList" style="width: 100%" max-height="400">
        <el-table-column prop="taskId" label="任务ID" width="120" show-overflow-tooltip />
        <el-table-column prop="ticketId" label="工单ID" width="100" />
        <el-table-column prop="creatorUserName" label="创建人" width="100" />
        <el-table-column prop="creatorOrgName" label="创建人组织" width="150" show-overflow-tooltip />
        <el-table-column prop="taskMessage" label="任务消息" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(new Date(scope.row.createTime).getTime()) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 所有部门排名弹窗 -->
    <el-dialog
      v-model="showAllOrgRankDialog"
      title="语料生成使用频率部门排名（全部）"
      width="90%"
      :before-close="() => showAllOrgRankDialog = false"
    >
      <el-table :data="allOrgRankListWithDetails" style="width: 100%" max-height="500">
        <el-table-column type="index" label="排名" width="80" />
        <el-table-column prop="orgName" label="部门名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="orgPath" label="部门路径" min-width="250" show-overflow-tooltip />
        <el-table-column prop="count" label="使用次数" width="120" />
        <el-table-column prop="rgCount" label="值班组数量" width="120" />
        <el-table-column prop="activeRgCount" label="活跃值班组" width="120">
          <template #default="scope">
            <span>{{ scope.row.activeRgCount }}/{{ scope.row.rgCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalCorpusCount" label="部门语料总数" width="140" />
        <el-table-column prop="timeRangeCorpusCount" label="本周期语料数量" width="140" />
        <el-table-column prop="fridaySpaceCount" label="Friday空间数" width="120" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="text" @click="viewOrgDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 所有值班组排名弹窗（本周期） -->
    <el-dialog
      v-model="showAllRgRankDialog"
      title="值班组语料数量排名（本周期全部）"
      width="80%"
      :before-close="() => showAllRgRankDialog = false"
    >
      <el-table :data="allRgRankList" style="width: 100%" max-height="500">
        <el-table-column type="index" label="排名" width="80" />
        <el-table-column prop="rgName" label="值班组名称" min-width="150" />
        <el-table-column prop="rgId" label="值班组ID" width="100" />
        <el-table-column prop="rgOwner" label="负责人" width="100" />
        <el-table-column prop="rgOrgName" label="所属组织" width="150" show-overflow-tooltip />
        <el-table-column prop="timeRangeCorpusCount" label="时间范围内语料数" width="140" />
        <el-table-column prop="totalCorpusCount" label="历史语料总数" width="120" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="text" @click="viewRgDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 所有值班组历史语料总数排名弹窗 -->
    <el-dialog
      v-model="showAllTotalCorpusRankDialog"
      title="值班组历史语料总数排名（全部数据）"
      width="80%"
      :before-close="() => showAllTotalCorpusRankDialog = false"
    >
      <el-table :data="allTotalCorpusRankList" style="width: 100%" max-height="500">
        <el-table-column type="index" label="排名" width="80" />
        <el-table-column prop="rgName" label="值班组名称" min-width="150" />
        <el-table-column prop="rgId" label="值班组ID" width="100" />
        <el-table-column prop="rgOwner" label="负责人" width="100" />
        <el-table-column prop="rgOrgName" label="所属组织" width="150" show-overflow-tooltip />
        <el-table-column prop="totalCorpusCount" label="历史语料总数" width="120" />
        <el-table-column prop="timeRangeCorpusCount" label="时间范围内语料数" width="140" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="text" @click="viewRgDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 已接入部门列表弹窗 -->
    <el-dialog
      v-model="showOrgListDialog"
      title="已接入的部门列表"
      width="90%"
      :before-close="() => showOrgListDialog = false"
    >
      <el-table :data="orgStatsList" style="width: 100%" max-height="500">
        <el-table-column type="index" label="排名" width="80" />
        <el-table-column prop="orgName" label="部门名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="orgPath" label="部门路径" min-width="250" show-overflow-tooltip />
        <el-table-column prop="rgCount" label="值班组数量" width="120" />
        <el-table-column prop="activeRgCount" label="活跃值班组" width="120">
          <template #default="scope">
            <span>{{ scope.row.activeRgCount }}/{{ scope.row.rgCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalCorpusCount" label="部门语料总数" width="140" />
        <el-table-column prop="timeRangeCorpusCount" label="本周期语料数量" width="140" />
        <el-table-column prop="fridaySpaceCount" label="Friday空间数" width="120" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="text" @click="viewOrgDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 部门详情弹窗 -->
    <el-dialog
      v-model="showOrgDetailDialog"
      :title="`${selectedOrgInfo?.orgName} - 部门详情`"
      width="90%"
      :before-close="() => showOrgDetailDialog = false"
    >
      <div v-if="selectedOrgInfo" class="org-detail-content">
        <!-- 部门基础信息 -->
        <el-card class="org-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>部门基础信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="部门名称">{{ selectedOrgInfo.orgName }}</el-descriptions-item>
            <el-descriptions-item label="部门ID">{{ selectedOrgInfo.orgId }}</el-descriptions-item>
            <el-descriptions-item label="部门路径" :span="2">{{ selectedOrgInfo.orgPath }}</el-descriptions-item>
            <el-descriptions-item label="值班组总数">{{ selectedOrgInfo.rgCount }}</el-descriptions-item>
            <el-descriptions-item label="活跃值班组数">{{ selectedOrgInfo.activeRgCount }}</el-descriptions-item>
            <el-descriptions-item label="部门语料总数">{{ selectedOrgInfo.totalCorpusCount }}</el-descriptions-item>
            <el-descriptions-item label="本周期新增语料">{{ selectedOrgInfo.timeRangeCorpusCount }}</el-descriptions-item>
            <el-descriptions-item label="Friday空间数量">{{ selectedOrgInfo.fridaySpaceCount }}</el-descriptions-item>
            <el-descriptions-item label="语料增长率">
              <span v-if="selectedOrgInfo.totalCorpusCount > 0">
                {{ ((selectedOrgInfo.timeRangeCorpusCount / selectedOrgInfo.totalCorpusCount) * 100).toFixed(2) }}%
              </span>
              <span v-else>-</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 部门值班组详情 -->
        <el-card class="org-rg-card" shadow="never" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>部门值班组详情</span>
            </div>
          </template>
          <el-table :data="selectedOrgInfo.rgList" style="width: 100%" max-height="400">
            <el-table-column prop="rgName" label="值班组名称" min-width="150" />
            <el-table-column prop="rgId" label="值班组ID" width="100" />
            <el-table-column prop="rgOwner" label="负责人" width="100" />
            <el-table-column prop="totalCorpusCount" label="历史语料总数" width="120" />
            <el-table-column prop="timeRangeCorpusCount" label="本周期语料数" width="120" />
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.timeRangeCorpusCount > 0 ? 'success' : 'info'">
                  {{ scope.row.timeRangeCorpusCount > 0 ? '活跃' : '非活跃' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 部门Friday空间详情 -->
        <el-card class="org-friday-card" shadow="never" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>部门Friday空间详情</span>
            </div>
          </template>
          <el-table :data="selectedOrgFridaySpaces" style="width: 100%" max-height="400">
            <el-table-column prop="spaceName" label="空间名称" min-width="200" show-overflow-tooltip />
            <el-table-column prop="spaceId" label="空间ID" width="120" />
            <el-table-column prop="spaceDesc" label="空间描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="ctime" label="创建时间" width="180">
              <template #default="scope">
                {{ scope.row.ctime ? formatDateTime(scope.row.ctime) : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="spaceRgIds" label="绑定值班组" width="150">
              <template #default="scope">
                {{ scope.row.spaceRgIds ? scope.row.spaceRgIds.join(', ') : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag type="success">
                  活跃
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-dialog>

    <!-- 语料生成任务列表弹窗 -->
    <el-dialog
      v-model="showTaskListDialog"
      title="语料生成任务列表（本周期）"
      width="90%"
      :before-close="() => showTaskListDialog = false"
    >
      <div class="task-list-content">
        <!-- 平台统计概览 -->
        <el-row :gutter="16" class="platform-overview">
          <el-col :span="6" v-for="platform in tasksByPlatform" :key="platform.platformId">
            <el-card shadow="hover" class="platform-card" :style="{ borderColor: getPlatformColor(platform.platformId) }">
              <div class="platform-info">
                <div class="platform-header">
                  <span class="platform-icon">{{ getPlatformIcon(platform.platformId) }}</span>
                  <div class="platform-name" :style="{ color: getPlatformColor(platform.platformId) }">
                    {{ platform.platformName }}
                  </div>
                </div>
                <div class="platform-stats">
                  <div class="stat-item">
                    <span class="label">总任务数：</span>
                    <span class="value total">{{ platform.totalCount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="label">成功：</span>
                    <span class="value success">{{ platform.successCount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="label">失败：</span>
                    <span class="value failed">{{ platform.failedCount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="label">进行中：</span>
                    <span class="value processing">{{ platform.processingCount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="label">未保存：</span>
                    <span class="value unsaved">{{ platform.unsavedCount }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 任务详细列表 -->
        <el-card shadow="never" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>任务详细列表</span>
            </div>
          </template>

          <!-- 按平台分组的折叠面板 -->
          <el-collapse>
            <el-collapse-item
              v-for="platform in tasksByPlatform"
              :key="platform.platformId"
              :name="platform.platformId.toString()"
            >
              <template #title>
                <div class="collapse-title">
                  <span class="platform-icon">{{ getPlatformIcon(platform.platformId) }}</span>
                  <span class="platform-name" :style="{ color: getPlatformColor(platform.platformId) }">
                    {{ platform.platformName }}
                  </span>
                  <el-tag type="info" size="small">{{ platform.totalCount }}个任务</el-tag>
                  <el-tag type="success" size="small" style="margin-left: 8px;">成功{{ platform.successCount }}</el-tag>
                  <el-tag type="danger" size="small" style="margin-left: 8px;">失败{{ platform.failedCount }}</el-tag>
                  <el-tag type="primary" size="small" style="margin-left: 8px;">进行中{{ platform.processingCount }}</el-tag>
                  <el-tag type="warning" size="small" style="margin-left: 8px;">未保存{{ platform.unsavedCount }}</el-tag>
                </div>
              </template>

              <el-table :data="platform.tasks" style="width: 100%" max-height="400">
                <el-table-column prop="taskId" label="任务ID" width="120" show-overflow-tooltip />
                <el-table-column prop="ticketId" label="工单ID" width="100" />
                <el-table-column prop="creatorUserName" label="创建人" width="100" />
                <el-table-column prop="creatorOrgName" label="创建人部门" width="150" show-overflow-tooltip />
                <el-table-column prop="taskMessage" label="任务消息" min-width="200" show-overflow-tooltip />
                <el-table-column label="状态" width="100">
                  <template #default="scope">
                    <!-- 优先显示任务状态，如果是成功状态则显示保存状态 -->
                    <el-tag v-if="scope.row.taskStatus === 2" type="danger">
                      失败
                    </el-tag>
                    <el-tag v-else-if="scope.row.taskStatus === 0" type="primary">
                      进行中
                    </el-tag>
                    <el-tag v-else :type="scope.row.taskSaved ? 'success' : 'warning'">
                      {{ scope.row.taskSaved ? '已保存' : '未保存' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="180">
                  <template #default="scope">
                    {{ formatDateTime(new Date(scope.row.createTime).getTime()) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button type="text" @click="viewTaskDetail(scope.row)">查看详情</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </div>
    </el-dialog>

    <!-- 任务详情弹窗 -->
    <el-dialog
      v-model="showTaskDetailDialog"
      :title="`任务详情 - ${selectedTaskInfo?.taskId}`"
      width="80%"
      :before-close="() => showTaskDetailDialog = false"
    >
      <div v-if="selectedTaskInfo" class="task-detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTaskInfo.taskId }}</el-descriptions-item>
          <el-descriptions-item label="工单ID">{{ selectedTaskInfo.ticketId }}</el-descriptions-item>
          <el-descriptions-item label="平台来源">{{ getPlatformName(selectedTaskInfo.platformId) }}</el-descriptions-item>
          <el-descriptions-item label="大象群ID">{{ selectedTaskInfo.dxGroupId }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getTaskStatusType(selectedTaskInfo.taskStatus)">
              {{ getTaskStatusText(selectedTaskInfo.taskStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="保存状态">
            <el-tag :type="selectedTaskInfo.taskSaved ? 'success' : 'warning'">
              {{ selectedTaskInfo.taskSaved ? '已保存' : '未保存' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ selectedTaskInfo.creatorUserName }}</el-descriptions-item>
          <el-descriptions-item label="创建人MIS">{{ selectedTaskInfo.creatorMisId }}</el-descriptions-item>
          <el-descriptions-item label="创建人部门">{{ selectedTaskInfo.creatorOrgName }}</el-descriptions-item>
          <el-descriptions-item label="创建人部门ID">{{ selectedTaskInfo.creatorOrgId }}</el-descriptions-item>
          <el-descriptions-item label="部门路径" :span="2">{{ selectedTaskInfo.creatorOrgPath }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(new Date(selectedTaskInfo.createTime).getTime()) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(new Date(selectedTaskInfo.updateTime).getTime()) }}</el-descriptions-item>
          <el-descriptions-item label="任务消息" :span="2">
            <div class="task-message">{{ selectedTaskInfo.taskMessage }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 失败任务详情弹窗 -->
    <el-dialog
      v-model="showFailedTasksDialog"
      title="失败任务详情（本周期）"
      width="80%"
      :before-close="() => showFailedTasksDialog = false"
    >
      <!-- 按平台分组的折叠面板 -->
      <el-collapse>
        <el-collapse-item
          v-for="platform in failedTasksByPlatform"
          :key="platform.platformId"
          :name="platform.platformId.toString()"
        >
          <template #title>
            <div class="collapse-title">
              <span class="platform-icon">{{ getPlatformIcon(platform.platformId) }}</span>
              <span class="platform-name" :style="{ color: getPlatformColor(platform.platformId) }">
                {{ platform.platformName }}
              </span>
              <el-tag type="danger" size="small">{{ platform.totalCount }}个失败任务</el-tag>
            </div>
          </template>

          <el-table :data="platform.tasks" style="width: 100%" max-height="400">
            <el-table-column prop="taskId" label="任务ID" width="120" show-overflow-tooltip />
            <el-table-column prop="ticketId" label="工单ID" width="100" />
            <el-table-column prop="creatorUserName" label="创建人" width="100" />
            <el-table-column prop="creatorOrgName" label="创建人部门" width="150" show-overflow-tooltip />
            <el-table-column prop="taskMessage" label="任务消息" min-width="200" show-overflow-tooltip />
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getTaskStatusType(scope.row.taskStatus)">
                  {{ getTaskStatusText(scope.row.taskStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(new Date(scope.row.createTime).getTime()) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="text" @click="viewTaskDetail(scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </el-dialog>

    <!-- 进行中任务详情弹窗 -->
    <el-dialog
      v-model="showProcessingTasksDialog"
      title="进行中任务详情（本周期）"
      width="80%"
      :before-close="() => showProcessingTasksDialog = false"
    >
      <div class="processing-tasks-header">
        <el-alert
          title="异常提醒"
          type="warning"
          description="以下任务状态为进行中，正常情况下任务应该很快完成。长时间处于进行中状态可能表示系统异常，请及时处理。"
          show-icon
          :closable="false"
          style="margin-bottom: 16px;"
        />
      </div>

      <!-- 按平台分组的折叠面板 -->
      <el-collapse>
        <el-collapse-item
          v-for="platform in processingTasksByPlatform"
          :key="platform.platformId"
          :name="platform.platformId.toString()"
        >
          <template #title>
            <div class="collapse-title">
              <span class="platform-icon">{{ getPlatformIcon(platform.platformId) }}</span>
              <span class="platform-name" :style="{ color: getPlatformColor(platform.platformId) }">
                {{ platform.platformName }}
              </span>
              <el-tag type="primary" size="small">{{ platform.totalCount }}个进行中任务</el-tag>
            </div>
          </template>

          <el-table :data="platform.tasks" style="width: 100%" max-height="400">
            <el-table-column prop="taskId" label="任务ID" width="120" show-overflow-tooltip />
            <el-table-column prop="ticketId" label="工单ID" width="100" />
            <el-table-column prop="creatorUserName" label="创建人" width="100" />
            <el-table-column prop="creatorOrgName" label="创建人部门" width="150" show-overflow-tooltip />
            <el-table-column prop="taskMessage" label="任务消息" min-width="200" show-overflow-tooltip />
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getTaskStatusType(scope.row.taskStatus)">
                  {{ getTaskStatusText(scope.row.taskStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(new Date(scope.row.createTime).getTime()) }}
              </template>
            </el-table-column>
            <el-table-column label="持续时间" width="120">
              <template #default="scope">
                <span class="duration-text">
                  {{ calculateDuration(scope.row.createTime) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="text" @click="viewTaskDetail(scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </el-dialog>

    <!-- 当前总运行任务详情弹窗 -->
    <el-dialog
      v-model="showAllRunningTasksDialog"
      title="当前总运行任务详情（全部数据）"
      width="80%"
      :before-close="() => showAllRunningTasksDialog = false"
    >
      <div class="all-running-tasks-header">
        <el-alert
          title="系统运行状态"
          type="info"
          description="以下是系统中所有值班组当前正在运行的任务，包括所有时间段的运行任务。长时间运行的任务可能需要关注。"
          show-icon
          :closable="false"
          style="margin-bottom: 16px;"
        />
      </div>

      <!-- 按平台分组的折叠面板 -->
      <el-collapse>
        <el-collapse-item
          v-for="platform in allRunningTasksByPlatform"
          :key="platform.platformId"
          :name="platform.platformId.toString()"
        >
          <template #title>
            <div class="collapse-title">
              <span class="platform-icon">{{ getPlatformIcon(platform.platformId) }}</span>
              <span class="platform-name" :style="{ color: getPlatformColor(platform.platformId) }">
                {{ platform.platformName }}
              </span>
              <el-tag type="primary" size="small">{{ platform.totalCount }}个运行任务</el-tag>
            </div>
          </template>

          <el-table :data="platform.tasks" style="width: 100%" max-height="400">
            <el-table-column prop="taskId" label="任务ID" width="120" show-overflow-tooltip />
            <el-table-column prop="ticketId" label="工单ID" width="100" />
            <el-table-column prop="creatorUserName" label="创建人" width="100" />
            <el-table-column prop="creatorOrgName" label="创建人部门" width="150" show-overflow-tooltip />
            <el-table-column prop="taskMessage" label="任务消息" min-width="200" show-overflow-tooltip />
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getTaskStatusType(scope.row.taskStatus)">
                  {{ getTaskStatusText(scope.row.taskStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(new Date(scope.row.createTime).getTime()) }}
              </template>
            </el-table-column>
            <el-table-column label="持续时间" width="120">
              <template #default="scope">
                <span class="duration-text">
                  {{ calculateDuration(scope.row.createTime) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="text" @click="viewTaskDetail(scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </el-dialog>

    <!-- 值班组详情弹窗 -->
    <el-dialog
      v-model="showRgDetailDialog"
      :title="`${selectedRgInfo?.rgName} - 值班组详情`"
      width="80%"
      :before-close="() => showRgDetailDialog = false"
    >
      <div v-if="selectedRgInfo" class="rg-detail-content">
        <!-- 值班组基础信息 -->
        <el-card class="rg-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>值班组基础信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="值班组名称">{{ selectedRgInfo.rgName }}</el-descriptions-item>
            <el-descriptions-item label="值班组ID">{{ selectedRgInfo.rgId }}</el-descriptions-item>
            <el-descriptions-item label="负责人">{{ selectedRgInfo.rgOwner }}</el-descriptions-item>
            <el-descriptions-item label="所属组织">{{ selectedRgInfo.rgOrgName }}</el-descriptions-item>
            <el-descriptions-item label="组织路径" :span="2">{{ selectedRgInfo.rgOrgPath }}</el-descriptions-item>
            <el-descriptions-item label="历史语料总数">{{ selectedRgInfo.totalCorpusCount }}</el-descriptions-item>
            <el-descriptions-item label="本周期语料数">{{ selectedRgInfo.timeRangeCorpusCount }}</el-descriptions-item>
            <el-descriptions-item label="语料增长率">
              <span v-if="selectedRgInfo.totalCorpusCount > 0">
                {{ ((selectedRgInfo.timeRangeCorpusCount / selectedRgInfo.totalCorpusCount) * 100).toFixed(2) }}%
              </span>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="selectedRgInfo.timeRangeCorpusCount > 0 ? 'success' : 'info'">
                {{ selectedRgInfo.timeRangeCorpusCount > 0 ? '活跃' : '非活跃' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 值班组绑定的Friday空间 -->
        <el-card class="rg-friday-card" shadow="never" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>绑定的Friday空间</span>
            </div>
          </template>
          <div v-if="selectedRgFridaySpaces.length > 0">
            <el-table :data="selectedRgFridaySpaces" style="width: 100%" max-height="400">
              <el-table-column prop="spaceName" label="空间名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="spaceId" label="空间ID" width="120" />
              <el-table-column prop="spaceDesc" label="空间描述" min-width="200" show-overflow-tooltip />
              <el-table-column prop="ctime" label="创建时间" width="180">
                <template #default="scope">
                  {{ scope.row.ctime ? formatDateTime(scope.row.ctime) : '-' }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无绑定的Friday空间" />
          </div>
        </el-card>

        <!-- 值班组任务列表 -->
        <el-card class="rg-tasks-card" shadow="never" style="margin-top: 20px;" v-if="selectedRgTasks.length > 0">
          <template #header>
            <div class="card-header">
              <span>最近任务记录</span>
            </div>
          </template>
          <el-table :data="selectedRgTasks" style="width: 100%" max-height="400">
            <el-table-column prop="taskId" label="任务ID" width="120" show-overflow-tooltip />
            <el-table-column prop="ticketId" label="工单ID" width="100" />
            <el-table-column prop="creatorUserName" label="创建人" width="100" />
            <el-table-column prop="taskMessage" label="任务消息" min-width="200" show-overflow-tooltip />
            <el-table-column label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getTaskStatusType(scope.row.taskStatus)">
                  {{ getTaskStatusText(scope.row.taskStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(new Date(scope.row.createTime).getTime()) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="text" @click="viewTaskDetail(scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, onBeforeUnmount } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import type {
  CorpusOperationStatusDTO,
  CorpusStatsRgInfoItem,
  CorpusGenerateTaskItemForStats
} from '../../../types';

// 定义props
const props = defineProps<{
  statusData: CorpusOperationStatusDTO | null;
  loading: boolean;
  dateRange: [number, number] | null;
}>();

// 图表引用
const taskTrendChartRef = ref<HTMLElement | null>(null);
const platformSourceChartRef = ref<HTMLElement | null>(null);
const orgRankChartRef = ref<HTMLElement | null>(null);
const rgCorpusRankChartRef = ref<HTMLElement | null>(null);
const totalCorpusRankChartRef = ref<HTMLElement | null>(null);

// 对话框控制
const showBotMessagesDialog = ref(false);
const showRgInfoDialog = ref(false);
const showActiveRgDialog = ref(false); // 新增：接入的值班组数量弹窗
const showFridaySpaceDialog = ref(false);
const showUnsavedTasksDialog = ref(false);
const showAllOrgRankDialog = ref(false);
const showAllRgRankDialog = ref(false);
const showAllTotalCorpusRankDialog = ref(false);
const showOrgListDialog = ref(false);
const showOrgDetailDialog = ref(false);
const showTaskListDialog = ref(false);
const showTaskDetailDialog = ref(false);
const showFailedTasksDialog = ref(false);
const showProcessingTasksDialog = ref(false);
const showAllRunningTasksDialog = ref(false);
const showRgDetailDialog = ref(false);

// 选中的部门信息
const selectedOrgInfo = ref<any>(null);
// 选中的任务信息
const selectedTaskInfo = ref<CorpusGenerateTaskItemForStats | null>(null);
// 选中的值班组信息
const selectedRgInfo = ref<CorpusStatsRgInfoItem | null>(null);

// 选中部门的Friday空间列表
const selectedOrgFridaySpaces = computed(() => {
  if (!selectedOrgInfo.value || !props.statusData?.corpusStatsFridaySpaceList || !props.statusData?.corpusStatsRgInfoList) return [];

  // 获取该部门下的所有值班组ID
  const orgRgIds = selectedOrgInfo.value.rgList?.map((rg: any) => rg.rgId) || [];

  // 筛选包含该部门值班组的Friday空间
  return props.statusData.corpusStatsFridaySpaceList.filter(space => {
    // Friday空间通过spaceRgIds字段绑定值班组
    return space.spaceRgIds && space.spaceRgIds.some((rgId: string) => orgRgIds.includes(rgId));
  });
});

// 选中值班组的Friday空间列表
const selectedRgFridaySpaces = computed(() => {
  if (!selectedRgInfo.value || !props.statusData?.corpusStatsFridaySpaceList) return [];

  return props.statusData.corpusStatsFridaySpaceList.filter(space => {
    return space.spaceRgIds && space.spaceRgIds.includes(selectedRgInfo.value!.rgId);
  });
});

// 选中值班组的任务列表
const selectedRgTasks = computed(() => {
  if (!selectedRgInfo.value || !props.statusData?.dailyCorpusGenerateTaskCountList) return [];

  // 过滤出与该值班组相关的任务
  return props.statusData.dailyCorpusGenerateTaskCountList.filter(
    (task: CorpusGenerateTaskItemForStats) => task.rgId === selectedRgInfo.value!.rgId
  ).slice(0, 10); // 只显示最近10条任务
});

// 图表实例
let taskTrendChart: echarts.ECharts | null = null;
let platformSourceChart: echarts.ECharts | null = null;
let orgRankChart: echarts.ECharts | null = null;
let rgCorpusRankChart: echarts.ECharts | null = null;
let totalCorpusRankChart: echarts.ECharts | null = null;

// 计算属性
const botMessagesCount = computed(() => props.statusData?.corpusBotMessages.length || 0);
const botMessagesList = computed(() => props.statusData?.corpusBotMessages || []);

const taskGenerateCount = computed(() => props.statusData?.dailyCorpusGenerateTaskCountList.length || 0);

const rgInfoList = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return [];
  return [...props.statusData.corpusStatsRgInfoList]
    .sort((a, b) => b.timeRangeCorpusCount - a.timeRangeCorpusCount);
});

// 接入的值班组列表（按历史语料总数排序）
const activeRgList = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return [];
  return [...props.statusData.corpusStatsRgInfoList]
    .sort((a, b) => b.totalCorpusCount - a.totalCorpusCount);
});

// 接入的值班组总数（全部数据）
const activeRgCount = computed(() => activeRgList.value.length);

const totalCorpusCount = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return 0;
  return props.statusData.corpusStatsRgInfoList.reduce((sum: number, rg: CorpusStatsRgInfoItem) => sum + rg.totalCorpusCount, 0);
});

// 区间内入库的语料总数
const timeRangeCorpusCount = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return 0;
  return props.statusData.corpusStatsRgInfoList.reduce((sum: number, rg: CorpusStatsRgInfoItem) => sum + rg.timeRangeCorpusCount, 0);
});

const fridaySpaceList = computed(() => props.statusData?.corpusStatsFridaySpaceList || []);
const fridaySpaceCount = computed(() => fridaySpaceList.value.length);

// 失败任务列表和数量
const failedTaskList = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];
  return props.statusData.dailyCorpusGenerateTaskCountList.filter((task: CorpusGenerateTaskItemForStats) => task.taskStatus === 2);
});
const failedTaskCount = computed(() => failedTaskList.value.length);

// 进行中任务列表和数量（异常任务）
const processingTaskList = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];
  return props.statusData.dailyCorpusGenerateTaskCountList.filter((task: CorpusGenerateTaskItemForStats) => task.taskStatus === 0);
});
const processingTaskCount = computed(() => processingTaskList.value.length);

// 当前总的正在进行中任务数量（来自allRunningStatusTasks）
const allRunningTasksList = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return [];
  const allTasks: CorpusGenerateTaskItemForStats[] = [];
  props.statusData.corpusStatsRgInfoList.forEach((rg: CorpusStatsRgInfoItem) => {
    if (rg.allRunningStatusTasks) {
      allTasks.push(...rg.allRunningStatusTasks);
    }
  });
  return allTasks;
});
const allRunningTasksCount = computed(() => allRunningTasksList.value.length);

// 未保存任务列表和数量（排除失败任务和进行中任务）
const unsavedTaskList = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];
  return props.statusData.dailyCorpusGenerateTaskCountList.filter((task: CorpusGenerateTaskItemForStats) =>
    !task.taskSaved && task.taskStatus === 1  // 未保存且是成功状态
  );
});
const unsavedTaskCount = computed(() => unsavedTaskList.value.length);

// 已接入的部门数量（通过rgOrgId去重）
const connectedOrgCount = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return 0;
  const orgIds = new Set(props.statusData.corpusStatsRgInfoList.map((rg: CorpusStatsRgInfoItem) => rg.rgOrgId));
  return orgIds.size;
});

// 部门统计信息
const orgStatsMap = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return new Map();

  const orgMap = new Map<string, {
    orgId: string;
    orgName: string;
    orgPath: string;
    rgCount: number;
    totalCorpusCount: number;
    timeRangeCorpusCount: number;
    rgList: CorpusStatsRgInfoItem[];
    fridaySpaceCount: number;
    activeRgCount: number; // 本周期有语料的值班组数量
  }>();

  // 统计每个部门的信息
  props.statusData.corpusStatsRgInfoList.forEach((rg: CorpusStatsRgInfoItem) => {
    const orgId = rg.rgOrgId;
    if (!orgMap.has(orgId)) {
      orgMap.set(orgId, {
        orgId,
        orgName: rg.rgOrgName,
        orgPath: rg.rgOrgPath,
        rgCount: 0,
        totalCorpusCount: 0,
        timeRangeCorpusCount: 0,
        rgList: [],
        fridaySpaceCount: 0,
        activeRgCount: 0
      });
    }

    const orgInfo = orgMap.get(orgId)!;
    orgInfo.rgCount++;
    orgInfo.totalCorpusCount += rg.totalCorpusCount;
    orgInfo.timeRangeCorpusCount += rg.timeRangeCorpusCount;
    orgInfo.rgList.push(rg);

    // 统计活跃值班组（本周期有语料的）
    if (rg.timeRangeCorpusCount > 0) {
      orgInfo.activeRgCount++;
    }
  });

  // 统计Friday空间数量
  if (props.statusData?.corpusStatsFridaySpaceList) {
    props.statusData.corpusStatsFridaySpaceList.forEach((space: any) => {
      space.spaceRgIds.forEach((rgId: string) => {
        // 找到对应的值班组所属部门
        const rg = props.statusData?.corpusStatsRgInfoList.find((r: CorpusStatsRgInfoItem) => r.rgId === rgId);
        if (rg) {
          const orgInfo = orgMap.get(rg.rgOrgId);
          if (orgInfo) {
            orgInfo.fridaySpaceCount++;
          }
        }
      });
    });
  }

  return orgMap;
});

// 部门列表（按语料总数排序）
const orgStatsList = computed(() => {
  return Array.from(orgStatsMap.value.values())
    .sort((a, b) => b.totalCorpusCount - a.totalCorpusCount);
});

// 按平台分类的任务列表
const tasksByPlatform = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];

  const platformMap = new Map<number, {
    platformId: number;
    platformName: string;
    tasks: CorpusGenerateTaskItemForStats[];
    totalCount: number;
    savedCount: number;
    unsavedCount: number;
    failedCount: number;
    successCount: number;
    processingCount: number;
  }>();

  props.statusData.dailyCorpusGenerateTaskCountList.forEach((task: CorpusGenerateTaskItemForStats) => {
    const platformId = task.platformId;
    if (!platformMap.has(platformId)) {
      platformMap.set(platformId, {
        platformId,
        platformName: getPlatformName(platformId),
        tasks: [],
        totalCount: 0,
        savedCount: 0,
        unsavedCount: 0,
        failedCount: 0,
        successCount: 0,
        processingCount: 0
      });
    }

    const platformInfo = platformMap.get(platformId)!;
    platformInfo.tasks.push(task);
    platformInfo.totalCount++;

    // 按任务状态统计
    if (task.taskStatus === 2) {
      // 失败任务
      platformInfo.failedCount++;
    } else if (task.taskStatus === 1) {
      // 成功任务
      platformInfo.successCount++;
    } else if (task.taskStatus === 0) {
      // 进行中任务
      platformInfo.processingCount++;
    }

    // 保存状态统计（失败任务不计入未保存）
    if (task.taskSaved) {
      platformInfo.savedCount++;
    } else if (task.taskStatus !== 2) {
      // 未保存且不是失败状态
      platformInfo.unsavedCount++;
    }
  });

  return Array.from(platformMap.values())
    .sort((a, b) => b.totalCount - a.totalCount);
});

// 按平台分类的失败任务列表
const failedTasksByPlatform = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];

  const platformMap = new Map<number, {
    platformId: number;
    platformName: string;
    tasks: CorpusGenerateTaskItemForStats[];
    totalCount: number;
  }>();

  // 只处理失败任务
  const failedTasks = props.statusData.dailyCorpusGenerateTaskCountList.filter(
    (task: CorpusGenerateTaskItemForStats) => task.taskStatus === 2
  );

  failedTasks.forEach((task: CorpusGenerateTaskItemForStats) => {
    const platformId = task.platformId;
    if (!platformMap.has(platformId)) {
      platformMap.set(platformId, {
        platformId,
        platformName: getPlatformName(platformId),
        tasks: [],
        totalCount: 0
      });
    }

    const platformInfo = platformMap.get(platformId)!;
    platformInfo.tasks.push(task);
    platformInfo.totalCount++;
  });

  return Array.from(platformMap.values())
    .sort((a, b) => b.totalCount - a.totalCount);
});

// 按平台分类的进行中任务列表
const processingTasksByPlatform = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];

  const platformMap = new Map<number, {
    platformId: number;
    platformName: string;
    tasks: CorpusGenerateTaskItemForStats[];
    totalCount: number;
  }>();

  // 只处理进行中任务
  const processingTasks = props.statusData.dailyCorpusGenerateTaskCountList.filter(
    (task: CorpusGenerateTaskItemForStats) => task.taskStatus === 0
  );

  processingTasks.forEach((task: CorpusGenerateTaskItemForStats) => {
    const platformId = task.platformId;
    if (!platformMap.has(platformId)) {
      platformMap.set(platformId, {
        platformId,
        platformName: getPlatformName(platformId),
        tasks: [],
        totalCount: 0
      });
    }

    const platformInfo = platformMap.get(platformId)!;
    platformInfo.tasks.push(task);
    platformInfo.totalCount++;
  });

  return Array.from(platformMap.values())
    .sort((a, b) => b.totalCount - a.totalCount);
});

// 按平台分组的所有运行任务
const allRunningTasksByPlatform = computed(() => {
  if (!allRunningTasksList.value.length) return [];

  const platformMap = new Map<number, {
    platformId: number;
    platformName: string;
    tasks: CorpusGenerateTaskItemForStats[];
    totalCount: number;
  }>();

  allRunningTasksList.value.forEach((task: CorpusGenerateTaskItemForStats) => {
    const platformId = task.platformId;
    if (!platformMap.has(platformId)) {
      platformMap.set(platformId, {
        platformId,
        platformName: getPlatformName(platformId),
        tasks: [],
        totalCount: 0
      });
    }

    const platformInfo = platformMap.get(platformId)!;
    platformInfo.tasks.push(task);
    platformInfo.totalCount++;
  });

  return Array.from(platformMap.values())
    .sort((a, b) => b.totalCount - a.totalCount);
});

// 关键指标卡片
const keyMetricCards = computed(() => [
  {
    key: 'connectedOrgs',
    title: '已接入的部门数量（全部数据）',
    value: connectedOrgCount.value,
    description: '已接入语料服务的部门总数',
    tooltip: '系统中所有接入语料服务的部门数量'
  },
  {
    key: 'activeRg',
    title: '接入的值班组数量（全部数据）',
    value: activeRgCount.value,
    description: '系统中接入的值班组总数',
    tooltip: '系统中所有接入且有语料数据的值班组数量'
  },
  {
    key: 'timeRangeCorpus',
    title: '入库的语料总数（本周期）',
    value: timeRangeCorpusCount.value,
    description: '本周期新增入库的语料数量',
    tooltip: '统计时间范围内所有值班组新增入库的语料总数'
  },
  {
    key: 'failedTasks',
    title: '任务失败数量（本周期）',
    value: failedTaskCount.value,
    description: '本周期生成失败的任务数量',
    tooltip: '统计时间范围内状态为失败的语料生成任务数量'
  }
]);

// 次要指标卡片
const secondaryMetricCards = computed(() => [
  {
    key: 'allCorpus',
    title: '数据库中语料总数（全部数据）',
    value: totalCorpusCount.value,
    description: '系统数据库中所有语料的总数量',
    tooltip: '所有值班组历史语料总数的总和'
  },
  {
    key: 'taskGenerate',
    title: '语料生成次数（本周期）',
    value: taskGenerateCount.value,
    description: '本周期语料生成任务的总数',
    tooltip: '统计时间范围内创建的语料生成任务总数'
  },
  // {
  //   key: 'processingTasks',
  //   title: '进行中任务数量（本周期）',
  //   value: processingTaskCount.value,
  //   description: '本周期处于进行中状态的任务数量',
  //   tooltip: '统计时间范围内状态为进行中的语料生成任务数量'
  // },
  {
    key: 'unsavedTasks',
    title: '未保存的语料任务（本周期）',
    value: unsavedTaskCount.value,
    description: '本周期生成但未保存的任务数量',
    tooltip: '统计时间范围内已生成但尚未保存的语料任务数量'
  },
  {
    key: 'botMessages',
    title: '小助手被@次数（本周期）',
    value: botMessagesCount.value,
    description: '本周期语料助手机器人被@的次数',
    tooltip: '统计时间范围内语料助手机器人被@的总次数'
  },
  {
    key: 'fridaySpaces',
    title: 'Friday空间总数（全部数据）',
    value: fridaySpaceCount.value,
    description: '系统中Friday空间的总数量',
    tooltip: '系统中已接入的Friday空间总数量'
  },
  {
    key: 'allRunningTasks',
    title: '当前处于运行状态的任务数量（全部数据）',
    value: allRunningTasksCount.value,
    description: '当前系统中所有正在运行的任务总数',
    tooltip: '所有值班组当前正在进行中的任务总数量，包括所有时间段的运行任务'
  }
]);

// 所有部门排名列表
const allOrgRankList = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];

  const orgCounts = new Map<string, number>();
  props.statusData.dailyCorpusGenerateTaskCountList.forEach((task: CorpusGenerateTaskItemForStats) => {
    if (task.creatorOrgName) {
      orgCounts.set(task.creatorOrgName, (orgCounts.get(task.creatorOrgName) || 0) + 1);
    }
  });

  return Array.from(orgCounts.entries())
    .map(([orgName, count]) => ({ orgName, count }))
    .sort((a, b) => b.count - a.count);
});

// 带详细信息的部门排名列表
const allOrgRankListWithDetails = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];

  // 先获取使用频率统计
  const orgCounts = new Map<string, number>();
  props.statusData.dailyCorpusGenerateTaskCountList.forEach((task: CorpusGenerateTaskItemForStats) => {
    if (task.creatorOrgName) {
      orgCounts.set(task.creatorOrgName, (orgCounts.get(task.creatorOrgName) || 0) + 1);
    }
  });

  // 结合部门详细信息
  const orgRankWithDetails = Array.from(orgCounts.entries()).map(([orgName, count]) => {
    // 从orgStatsMap中查找对应的部门详细信息
    const orgDetail = Array.from(orgStatsMap.value.values()).find(org => org.orgName === orgName);

    return {
      orgName,
      count,
      orgPath: orgDetail?.orgPath || '-',
      rgCount: orgDetail?.rgCount || 0,
      activeRgCount: orgDetail?.activeRgCount || 0,
      totalCorpusCount: orgDetail?.totalCorpusCount || 0,
      timeRangeCorpusCount: orgDetail?.timeRangeCorpusCount || 0,
      fridaySpaceCount: orgDetail?.fridaySpaceCount || 0,
      orgId: orgDetail?.orgId || '',
      rgList: orgDetail?.rgList || []
    };
  });

  return orgRankWithDetails.sort((a, b) => b.count - a.count);
});

// 所有值班组排名列表（按本周期语料数量）
const allRgRankList = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return [];

  return [...props.statusData.corpusStatsRgInfoList]
    .sort((a, b) => b.timeRangeCorpusCount - a.timeRangeCorpusCount);
});

// 所有值班组历史语料总数排名列表
const allTotalCorpusRankList = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return [];

  return [...props.statusData.corpusStatsRgInfoList]
    .sort((a, b) => b.totalCorpusCount - a.totalCorpusCount);
});

// 格式化日期时间
const formatDateTime = (timestamp: number) => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 获取平台名称
const getPlatformName = (platformId: number) => {
  const platformMap: Record<number, string> = {
    1: '知识库语料助手机器人',
    2: '语料处理平台管理端',
    3: '语料合并',
    4: '大象群监控',
    5: '学城问答上传',
    6: '大象单聊合并转发'
  };
  return platformMap[platformId] || `未知平台(${platformId})`;
};

// 获取平台颜色
const getPlatformColor = (platformId: number) => {
  const colorMap: Record<number, string> = {
    1: '#409EFF', // 蓝色 - 大象群机器人
    2: '#67C23A', // 绿色 - Web端
    3: '#E6A23C', // 橙色 - Web端合并
    4: '#F56C6C', // 红色 - 大象监控
    5: '#909399', // 灰色 - 学城问答上传
    6: '#9C27B0'  // 紫色 - 大象单聊合并转发
  };
  return colorMap[platformId] || '#606266';
};

// 获取平台图标
const getPlatformIcon = (platformId: number) => {
  const iconMap: Record<number, string> = {
    1: '🤖', // 大象群机器人
    2: '🌐', // Web端
    3: '🔗', // Web端合并
    4: '📊', // 大象监控
    5: '📚', // 学城问答上传
    6: '💬'  // 大象单聊合并转发
  };
  return iconMap[platformId] || '❓';
};

// 获取任务状态文本
const getTaskStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '进行中',
    1: '成功',
    2: '失败'
  };
  return statusMap[status] || `未知状态(${status})`;
};

// 获取任务状态类型（用于标签颜色）
const getTaskStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'primary',  // 进行中 - 蓝色
    1: 'success',  // 成功 - 绿色
    2: 'danger'    // 失败 - 红色
  };
  return typeMap[status] || 'info';
};

// 处理卡片点击事件
const handleCardClick = (key: string) => {
  console.log('Card clicked:', key); // 添加日志，跟踪点击事件
  
  switch (key) {
    case 'connectedOrgs':
    case 'allCorpus':
      showOrgListDialog.value = true;
      break;
    case 'botMessages':
      showBotMessagesDialog.value = true;
      break;
    case 'taskGenerate':
      showTaskListDialog.value = true;
      break;
    case 'failedTasks':
      showFailedTasksDialog.value = true;
      break;
    case 'processingTasks':
      showProcessingTasksDialog.value = true;
      break;
    case 'activeRg':
      console.log('Opening activeRg dialog'); // 添加日志，确认进入了这个分支
      showActiveRgDialog.value = true;
      console.log('showActiveRgDialog set to:', showActiveRgDialog.value); // 添加日志，确认变量被正确设置
      break;
    case 'unsavedTasks':
      showUnsavedTasksDialog.value = true;
      break;
    case 'fridaySpaces':
      showFridaySpaceDialog.value = true;
      break;
    case 'timeRangeCorpus':
      showRgInfoDialog.value = true; // 显示值班组详情，因为区间语料数来自值班组
      break;
    case 'allRunningTasks':
      showAllRunningTasksDialog.value = true;
      break;
    default:
      // 其他卡片暂不处理
      break;
  }
};

// 查看部门详情
const viewOrgDetail = (orgInfo: any) => {
  selectedOrgInfo.value = orgInfo;
  showOrgDetailDialog.value = true;
};

// 查看任务详情
const viewTaskDetail = (task: CorpusGenerateTaskItemForStats) => {
  selectedTaskInfo.value = task;
  showTaskDetailDialog.value = true;
};

// 计算任务持续时间
const calculateDuration = (createTime: string) => {
  const now = new Date();
  const taskTime = new Date(createTime);
  const diffMs = now.getTime() - taskTime.getTime();

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    return `${diffDays}天${diffHours % 24}小时`;
  } else if (diffHours > 0) {
    return `${diffHours}小时${diffMinutes % 60}分钟`;
  } else {
    return `${diffMinutes}分钟`;
  }
};

// 初始化图表
const initCharts = () => {
  if (taskTrendChartRef.value) {
    taskTrendChart = echarts.init(taskTrendChartRef.value);
  }
  if (platformSourceChartRef.value) {
    platformSourceChart = echarts.init(platformSourceChartRef.value);
  }
  if (orgRankChartRef.value) {
    orgRankChart = echarts.init(orgRankChartRef.value);
  }
  if (rgCorpusRankChartRef.value) {
    rgCorpusRankChart = echarts.init(rgCorpusRankChartRef.value);
  }
  if (totalCorpusRankChartRef.value) {
    totalCorpusRankChart = echarts.init(totalCorpusRankChartRef.value);
  }

  // 初始化后立即更新图表
  updateCharts();
};

// 更新图表数据
const updateCharts = () => {
  if (!props.statusData) return;

  // 更新语料生成趋势图
  updateTaskTrendChart();

  // 更新语料生成触发来源图
  updatePlatformSourceChart();

  // 更新部门排名图
  updateOrgRankChart();

  // 更新值班组语料数量排名图
  updateRgCorpusRankChart();

  // 更新值班组历史语料总数排名图
  updateTotalCorpusRankChart();
};

// 更新语料生成趋势图
const updateTaskTrendChart = () => {
  if (!taskTrendChart || !props.statusData) return;

  // 按日期分组统计不同状态的任务数量
  const successByDate = new Map<string, number>();
  const failedByDate = new Map<string, number>();
  const processingByDate = new Map<string, number>();
  const unsavedByDate = new Map<string, number>();

  props.statusData.dailyCorpusGenerateTaskCountList.forEach((task: CorpusGenerateTaskItemForStats) => {
    // 使用本地时间格式化日期，避免时区问题
    const taskDate = new Date(task.createTime);
    const year = taskDate.getFullYear();
    const month = String(taskDate.getMonth() + 1).padStart(2, '0');
    const day = String(taskDate.getDate()).padStart(2, '0');
    const date = `${year}-${month}-${day}`;

    // 按任务状态分类统计
    if (task.taskStatus === 1) {
      // 成功任务
      successByDate.set(date, (successByDate.get(date) || 0) + 1);
    } else if (task.taskStatus === 2) {
      // 失败任务
      failedByDate.set(date, (failedByDate.get(date) || 0) + 1);
    } else if (task.taskStatus === 0) {
      // 进行中任务
      processingByDate.set(date, (processingByDate.get(date) || 0) + 1);
    }

    // 未保存任务统计（失败任务不计入未保存）
    if (!task.taskSaved && task.taskStatus !== 2) {
      // 未保存且不是失败状态
      unsavedByDate.set(date, (unsavedByDate.get(date) || 0) + 1);
    }
  });

  // 生成完整的日期范围（使用传入的日期范围）
  let completeDateRange: string[] = [];

  if (props.dateRange && props.dateRange.length === 2) {
    // 使用传入的日期范围
    const [beginTimestamp, endTimestamp] = props.dateRange;

    // 创建日期对象，避免时区问题
    const startDate = new Date(beginTimestamp);
    const endDate = new Date(endTimestamp);

    // 使用本地时间，避免UTC转换问题
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth();
    const startDay = startDate.getDate();

    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth();
    const endDay = endDate.getDate();

    // 重新创建日期对象，确保使用本地时间
    const localStartDate = new Date(startYear, startMonth, startDay);
    const localEndDate = new Date(endYear, endMonth, endDay);

    // 调试信息
    console.log('Date range processing:', {
      originalRange: props.dateRange,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      localStartDate: localStartDate.toISOString(),
      localEndDate: localEndDate.toISOString()
    });

    const currentDate = new Date(localStartDate);
    while (currentDate <= localEndDate) {
      // 使用本地时间格式化日期
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      completeDateRange.push(`${year}-${month}-${day}`);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // 调试信息
  } else {
    // 如果没有传入日期范围，从数据中推断或使用默认范围
    const allDates = Array.from(new Set([
      ...Array.from(successByDate.keys()),
      ...Array.from(failedByDate.keys()),
      ...Array.from(processingByDate.keys()),
      ...Array.from(unsavedByDate.keys())
    ]));

    if (allDates.length === 0) {
      // 如果没有数据，使用默认的7天范围
      const today = new Date();
      const endDate = new Date(today);
      endDate.setDate(today.getDate() - 1); // 昨天
      const startDate = new Date(endDate);
      startDate.setDate(endDate.getDate() - 6); // 7天前

      const currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        completeDateRange.push(currentDate.toISOString().split('T')[0]);
        currentDate.setDate(currentDate.getDate() + 1);
      }
    } else {
      // 从数据中推断日期范围
      const dateNumbers = allDates.map(d => new Date(d).getTime());
      const minDate = new Date(Math.min(...dateNumbers));
      const maxDate = new Date(Math.max(...dateNumbers));

      const currentDate = new Date(minDate);
      while (currentDate <= maxDate) {
        completeDateRange.push(currentDate.toISOString().split('T')[0]);
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }
  }

  // 为完整的日期范围生成数据，没有数据的日期显示为0
  const successCounts = completeDateRange.map(date => successByDate.get(date) || 0);
  const failedCounts = completeDateRange.map(date => failedByDate.get(date) || 0);
  const processingCounts = completeDateRange.map(date => processingByDate.get(date) || 0);
  const unsavedCounts = completeDateRange.map(date => unsavedByDate.get(date) || 0);
  const sortedDates = completeDateRange;

  taskTrendChart.setOption({
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = params[0].name + '<br/>';
        params.forEach((param: any) => {
          result += param.marker + param.seriesName + ': ' + param.value + '次<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['成功任务', '失败任务', '进行中任务', '未保存任务'],
      top: 10
    },
    grid: {
      left: '8%',   // 减少左边距
      right: '3%',  // 减少右边距
      top: '12%',   // 减少顶部空间，因为没有标题了
      bottom: '15%' // 为X轴标签留出更多空间
    },
    xAxis: {
      type: 'category',
      data: sortedDates,
      axisLabel: {
        rotate: 45,     // 旋转标签
        fontSize: 11,   // 减小字体
        margin: 10,     // 增加标签与轴的距离
        interval: 0     // 显示所有标签
      }
    },
    yAxis: {
      type: 'value',
      name: '任务数量'
    },
    series: [
      {
        name: '成功任务',
        type: 'line',
        data: successCounts,
        smooth: true,
        itemStyle: {
          color: '#67C23A'  // 绿色 - 与成功卡片颜色一致
        },
        areaStyle: {
          opacity: 0.3
        }
      },
      {
        name: '失败任务',
        type: 'line',
        data: failedCounts,
        smooth: true,
        itemStyle: {
          color: '#F56C6C'  // 红色 - 与失败卡片颜色一致
        },
        areaStyle: {
          opacity: 0.3
        }
      },
      {
        name: '进行中任务',
        type: 'line',
        data: processingCounts,
        smooth: true,
        itemStyle: {
          color: '#409EFF'  // 蓝色 - 与进行中卡片颜色一致
        },
        areaStyle: {
          opacity: 0.3
        }
      },
      {
        name: '未保存任务',
        type: 'line',
        data: unsavedCounts,
        smooth: true,
        itemStyle: {
          color: '#E6A23C'  // 橙色 - 与未保存卡片颜色一致
        },
        areaStyle: {
          opacity: 0.3
        }
      }
    ]
  });
};

// 更新语料生成触发来源图
const updatePlatformSourceChart = () => {
  if (!platformSourceChart || !props.statusData) return;

  // 按平台ID分组统计
  const platformCounts = new Map<number, number>();

  props.statusData.dailyCorpusGenerateTaskCountList.forEach((task: CorpusGenerateTaskItemForStats) => {
    platformCounts.set(task.platformId, (platformCounts.get(task.platformId) || 0) + 1);
  });

  const pieData = Array.from(platformCounts.entries()).map(([platformId, count]) => ({
    name: getPlatformName(platformId),
    value: count
  }));

  platformSourceChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: pieData.map(item => item.name)
    },
    series: [
      {
        name: '触发来源',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['45%', '50%'],  // 稍微向右移动，因为没有标题了
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: pieData
      }
    ]
  });
};

// 更新部门排名图
const updateOrgRankChart = () => {
  if (!orgRankChart || !props.statusData) return;

  const orgRankData = allOrgRankList.value.slice(0, 10); // 只显示前10名

  // 如果没有数据，显示无数据状态
  if (orgRankData.length === 0) {
    orgRankChart.setOption({
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无部门数据',
          fontSize: 14,
          fill: '#909399'
        }
      }
    });
    return;
  }

  // 格式化部门名称，截断过长的文本
  const formatOrgName = (name: string): string => {
    const maxLength = 15;
    if (name.length <= maxLength) {
      return name;
    }
    return name.substring(0, maxLength) + '...';
  };

  // 反转数组以确保最大值在顶部
  const orgNames = [...orgRankData.map(item => formatOrgName(item.orgName))].reverse();
  const orgCounts = [...orgRankData.map(item => item.count)].reverse();

  orgRankChart.setOption({
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        return `<div style="font-weight: bold">${params.name}</div>使用次数: ${params.value}<div style="margin-top: 5px; font-size: 12px; color: #909399;">点击图表可查看部门详情</div>`;
      }
    },
    grid: {
      left: '3%',
      right: '15%',  // 增加右边距为x轴标签留出空间
      bottom: '15%',  // 增加底部空间
      top: '5%',      // 增加顶部空间
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '使用次数',
      nameLocation: 'end'
    },
    yAxis: {
      type: 'category',
      data: orgNames,
      axisLabel: {
        interval: 0,
        formatter: (value: string) => value,
        margin: 8
      }
    },
    series: [
      {
        name: '使用次数',
        type: 'bar',
        data: orgCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#E6A23C' },
            { offset: 0.5, color: '#F56C6C' },
            { offset: 1, color: '#F56C6C' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#F56C6C' },
              { offset: 0.7, color: '#E6A23C' },
              { offset: 1, color: '#FAECD8' }
            ])
          }
        }
      }
    ]
  });

  // 注册点击事件
  orgRankChart.off('click'); // 先移除之前的事件监听
  orgRankChart.on('click', (params) => {
    const dataIndex = params.dataIndex;
    // 反转索引，与显示顺序保持一致
    const adjustedIndex = orgRankData.length - 1 - dataIndex;

    if (adjustedIndex >= 0 && adjustedIndex < orgRankData.length) {
      const selectedOrg = orgRankData[adjustedIndex];
      // 查找对应的部门详细信息
      const orgDetail = Array.from(orgStatsMap.value.values()).find(org => org.orgName === selectedOrg.orgName);
      if (orgDetail) {
        viewOrgDetail(orgDetail);
      }
    }
  });
};

// 更新值班组语料数量排名图
const updateRgCorpusRankChart = () => {
  if (!rgCorpusRankChart || !props.statusData) return;

  const rgRankData = allRgRankList.value.slice(0, 10); // 只显示前10名

  // 如果没有数据，显示无数据状态
  if (rgRankData.length === 0) {
    rgCorpusRankChart.setOption({
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无值班组数据',
          fontSize: 14,
          fill: '#909399'
        }
      }
    });
    return;
  }

  // 格式化值班组名称，截断过长的文本
  const formatRgName = (name: string): string => {
    const maxLength = 20;
    if (name.length <= maxLength) {
      return name;
    }
    return name.substring(0, maxLength) + '...';
  };

  // 反转数组以确保最大值在顶部
  const rgNames = [...rgRankData.map(item => formatRgName(item.rgName))].reverse();
  const rgCounts = [...rgRankData.map(item => item.timeRangeCorpusCount)].reverse();

  rgCorpusRankChart.setOption({
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        return `<div style="font-weight: bold">${params.name}</div>语料数量: ${params.value}个<div style="margin-top: 5px; font-size: 12px; color: #909399;">点击图表可查看值班组详情</div>`;
      }
    },
    grid: {
      left: '3%',
      right: '15%',  // 增加右边距为x轴标签留出空间
      bottom: '15%',  // 增加底部空间
      top: '5%',      // 增加顶部空间
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '语料数量',
      nameLocation: 'end'
    },
    yAxis: {
      type: 'category',
      data: rgNames,
      axisLabel: {
        interval: 0,
        formatter: (value: string) => value,
        margin: 8
      }
    },
    series: [
      {
        name: '语料数量',
        type: 'bar',
        data: rgCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      }
    ]
  });

  // 注册点击事件
  rgCorpusRankChart.off('click'); // 先移除之前的事件监听
  rgCorpusRankChart.on('click', (params) => {
    const dataIndex = params.dataIndex;
    // 反转索引，与显示顺序保持一致
    const adjustedIndex = rgRankData.length - 1 - dataIndex;

    if (adjustedIndex >= 0 && adjustedIndex < rgRankData.length) {
      const selectedRg = rgRankData[adjustedIndex];
      // 查看值班组详情
      viewRgDetail(selectedRg);
    }
  });
};

// 更新值班组历史语料总数排名图
const updateTotalCorpusRankChart = () => {
  if (!totalCorpusRankChart || !props.statusData) return;

  const totalCorpusRankData = allTotalCorpusRankList.value.slice(0, 10); // 只显示前10名

  // 如果没有数据，显示无数据状态
  if (totalCorpusRankData.length === 0) {
    totalCorpusRankChart.setOption({
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无值班组数据',
          fontSize: 14,
          fill: '#909399'
        }
      }
    });
    return;
  }

  // 格式化值班组名称，截断过长的文本
  const formatRgName = (name: string): string => {
    const maxLength = 20;
    if (name.length <= maxLength) {
      return name;
    }
    return name.substring(0, maxLength) + '...';
  };

  // 反转数组以确保最大值在顶部
  const rgNames = [...totalCorpusRankData.map(item => formatRgName(item.rgName))].reverse();
  const rgCounts = [...totalCorpusRankData.map(item => item.totalCorpusCount)].reverse();

  totalCorpusRankChart.setOption({
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        return `<div style="font-weight: bold">${params.name}</div>历史语料总数: ${params.value}个<div style="margin-top: 5px; font-size: 12px; color: #909399;">点击图表可查看值班组详情</div>`;
      }
    },
    grid: {
      left: '3%',
      right: '15%',  // 增加右边距为x轴标签留出空间
      bottom: '15%',  // 增加底部空间
      top: '5%',      // 增加顶部空间
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '历史语料总数',
      nameLocation: 'end'
    },
    yAxis: {
      type: 'category',
      data: rgNames,
      axisLabel: {
        interval: 0,
        formatter: (value: string) => value,
        margin: 8
      }
    },
    series: [
      {
        name: '历史语料总数',
        type: 'bar',
        data: rgCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#909399' },
            { offset: 0.5, color: '#606266' },
            { offset: 1, color: '#606266' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#606266' },
              { offset: 0.7, color: '#909399' },
              { offset: 1, color: '#C0C4CC' }
            ])
          }
        }
      }
    ]
  });

  // 注册点击事件
  totalCorpusRankChart.off('click'); // 先移除之前的事件监听
  totalCorpusRankChart.on('click', (params) => {
    const dataIndex = params.dataIndex;
    // 反转索引，与显示顺序保持一致
    const adjustedIndex = totalCorpusRankData.length - 1 - dataIndex;

    if (adjustedIndex >= 0 && adjustedIndex < totalCorpusRankData.length) {
      const selectedRg = totalCorpusRankData[adjustedIndex];
      // 查看值班组详情
      viewRgDetail(selectedRg);
    }
  });
};

// 监听数据变化，更新图表
watch(() => props.statusData, () => {
  nextTick(() => {
    updateCharts();
  });
}, { deep: true });

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  taskTrendChart?.resize();
  platformSourceChart?.resize();
  orgRankChart?.resize();
  rgCorpusRankChart?.resize();
  totalCorpusRankChart?.resize();
};

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initCharts();
    window.addEventListener('resize', handleResize);
  });
});

// 组件卸载前清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  taskTrendChart?.dispose();
  platformSourceChart?.dispose();
  orgRankChart?.dispose();
  rgCorpusRankChart?.dispose();
  totalCorpusRankChart?.dispose();
});

// 添加查看值班组详情的方法
const viewRgDetail = (rgInfo: CorpusStatsRgInfoItem) => {
  selectedRgInfo.value = rgInfo;
  showRgDetailDialog.value = true;
};
</script>

<style scoped lang="scss">
.operation-status-dashboard {
  .key-metrics-row {
    margin-bottom: 30px;
  }

  .secondary-metrics-row {
    margin-bottom: 20px;
  }

  .stat-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #ebeef5;

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
      transform: translateY(-3px);
    }

    &.key-metric-card {
      height: 180px;
      box-shadow: 0 4px 15px 0 rgba(64, 158, 255, 0.25);
      border: 2px solid #409EFF;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 30px 30px 0;
        border-color: transparent #409EFF transparent transparent;
      }

      &:hover {
        box-shadow: 0 6px 25px 0 rgba(64, 158, 255, 0.35);
      }

      .card-header .title {
        font-weight: 600;
        color: #202945;
        font-size: 16px;
      }

      .card-content {
        .value.key-value {
          font-size: 36px;
          margin-top: 10px;
        }

        .description {
          font-size: 14px;
        }
      }
    }

    &.secondary-card {
      height: 130px;

      .card-header .title {
        font-size: 15px;
      }

      .card-content {
        .value {
          font-size: 28px;
          color: #67c23a;
        }

        .description {
          font-size: 13px;
        }
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .value {
        font-size: 32px;
        font-weight: bold;
        color: #67c23a;
        margin-bottom: 8px;

        &.key-value {
          color: #409EFF;
          font-size: 38px;
          font-weight: 700;
          text-shadow: 0 1px 2px rgba(64, 158, 255, 0.2);
        }
      }

      .description {
        font-size: 14px;
        color: #909399;
        text-align: center;
      }
    }
  }

  .chart-row {
    margin-top: 20px;
  }

  .chart-card {
    height: 450px;  // 默认图表高度

    &.trend-chart-card {
      height: 530px;  // 趋势图增加高度确保内容完整显示
    }

    &.pie-chart-card {
      height: 530px;  // 饼图增加高度确保内容完整显示
    }

    &.rank-chart-card {
      height: 500px;  // 参考statsDashboard的排名图高度

      // 去掉排名卡片的header分界线
      :deep(.el-card__header) {
        border-bottom: none;
        padding-bottom: 0;
      }
    }
  }

  .chart-container {
    height: 400px;  // 默认图表容器高度

    &.trend-chart-container {
      height: 450px;  // 趋势图容器增加高度
    }

    &.pie-chart-container {
      height: 450px;  // 饼图容器增加高度
    }

    &.rank-chart-container {
      height: 400px;  // 参考statsDashboard的排名图容器高度
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-weight: 500;
    }
  }

  // 部门详情样式
  .org-detail-content {
    .org-info-card,
    .org-rg-card,
    .org-friday-card {
      .card-header {
        font-weight: 600;
        color: #303133;
      }
    }

    .org-info-card {
      margin-bottom: 20px;
    }

    .el-descriptions {
      margin-top: 10px;
    }

    .el-tag {
      font-size: 12px;
    }
  }

  // 任务列表样式
  .task-list-content {
    .platform-overview {
      margin-bottom: 20px;
    }

    .platform-card {
      height: 180px;  // 增加高度以容纳5个统计项
      border-width: 2px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .platform-info {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .platform-header {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          .platform-icon {
            font-size: 20px;
            margin-right: 8px;
          }

          .platform-name {
            font-size: 16px;
            font-weight: 600;
            flex: 1;
          }
        }

        .platform-stats {
          .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;  // 减少间距以适应更多项目

            .label {
              font-size: 11px;  // 稍微减小字体
              color: #909399;
            }

            .value {
              font-size: 13px;  // 稍微减小字体
              font-weight: 500;

              &.total {
                color: #409EFF;
              }

              &.success {
                color: #67C23A;
              }

              &.failed {
                color: #F56C6C;
              }

              &.processing {
                color: #409EFF;  // 使用蓝色区分进行中状态
              }

              &.unsaved {
                color: #E6A23C;
              }
            }
          }
        }
      }
    }

    .collapse-title {
      display: flex;
      align-items: center;
      width: 100%;

      .platform-icon {
        font-size: 16px;
        margin-right: 8px;
      }

      .platform-name {
        font-weight: 600;
        margin-right: 12px;
      }
    }
  }

  // 任务详情样式
  .task-detail-content {
    .el-descriptions {
      margin-top: 10px;
    }

    .task-message {
      max-height: 100px;
      overflow-y: auto;
      padding: 8px;
      background-color: #f5f7fa;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1.5;
    }

    .el-tag {
      font-size: 12px;
    }
  }

  // 进行中任务样式
  .processing-tasks-header {
    .el-alert {
      border-radius: 6px;
    }
  }

  .duration-text {
    font-weight: 500;
    color: #E6A23C;
  }

  // 任务对话框样式
  .task-dialog-content {
    .platform-overview {
      margin-bottom: 20px;
    }

    .processing-tasks-header {
      margin-bottom: 16px;
    }
  }

  // 图表头部样式（参考statDashboard）
  .chart-header {
    margin-bottom: 16px;

    .title-container {
      display: flex;
      align-items: center;
      justify-content: space-between;  // 让标题和按钮分布在两端
      margin-bottom: 8px;

      .title-left {
        display: flex;
        align-items: center;

        .chart-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-right: 8px;
        }

        .help-icon {
          color: #909399;
          cursor: pointer;
          font-size: 14px;

          &:hover {
            color: #409EFF;
          }
        }
      }

      .chart-actions {
        display: flex;
        align-items: center;
      }
    }

    .chart-subtitle {
      font-size: 12px;
      color: #909399;
      margin-bottom: 8px;
    }
  }

  // 值班组详情样式
  .rg-detail-content {
    .rg-info-card,
    .rg-friday-card,
    .rg-tasks-card {
      .card-header {
        font-weight: 600;
        color: #303133;
      }
    }

    .rg-info-card {
      margin-bottom: 20px;
    }

    .el-descriptions {
      margin-top: 10px;
    }

    .el-tag {
      font-size: 12px;
    }

    .no-data {
      padding: 20px 0;
    }
  }
}
</style>