<template>
  <div class="corpus-operation-status-container">
    <div class="header">
      <div class="header-left">
        <h2>语料服务运营报表</h2>
        <div class="data-limit-hint">
          
        </div>
      </div>
      <div class="header-right">
        <!-- 强制重新生成按钮，仅在有数据且非加载状态时显示 -->
        <el-button
          v-if="hasData && !loading"
          type="primary"
          size="default"
          plain
          @click="handleForceRegenerateClick"
          class="force-regenerate-btn"
        >
          <el-icon class="el-icon--left"><RefreshRight /></el-icon>
          强制刷新数据
        </el-button>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="x"
          :shortcuts="dateShortcuts"
          :disabled-date="disableFutureDates"
          @change="handleDateChange"
        />
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else class="content">
      <OperationStatusDashboard
        :status-data="operationStatusData"
        :loading="loading"
        :date-range="dateRange"
      />
    </div>

    <!-- 添加加载进度面板 -->
    <LoadingProgressPanel
      v-model:visible="showLoadingPanel"
      title="数据加载中"
      message="正在处理统计数据..."
      :tips="[
        '大模型正在处理您的请求，请稍候',
        '数据处理时间取决于查询时间范围的长度',
        '处理完成后将自动显示统计结果'
      ]"
      :duration="30000"
      ref="loadingPanelRef"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { RefreshRight } from '@element-plus/icons-vue';
import httpRequest from '../../../../../utils/httpRequest';
import { API_PATHS } from '../../../request/api';
import type { CorpusOperationStatusDTO } from '../../../types';
import LoadingProgressPanel from '../../components/LoadingProgressPanel.vue';
import OperationStatusDashboard from './components/OperationStatusDashboard.vue';

// 日期选择快捷方式
const dateShortcuts = [
  {
    text: '上一自然周',
    value: () => {
      // 获取当前日期，设置到昨天23:59:59.999
      const now = new Date();
      const end = new Date(now);
      end.setDate(end.getDate() - 1); // 设置为昨天
      end.setHours(23, 59, 59, 999);

      const currentDay = end.getDay() || 7; // 将周日的0转换为7

      // 上周日23:59:59.999
      const lastSunday = new Date(end);
      lastSunday.setDate(end.getDate() - currentDay);
      lastSunday.setHours(23, 59, 59, 999);

      // 上周一00:00:00.000
      const lastMonday = new Date(lastSunday);
      lastMonday.setDate(lastSunday.getDate() - 6);
      lastMonday.setHours(0, 0, 0, 0);

      return [lastMonday, lastSunday];
    },
  },
  {
    text: '最近7天',
    value: () => {
      // 昨天23:59:59.999结束
      const end = new Date();
      end.setDate(end.getDate() - 1); // 设置为昨天
      end.setHours(23, 59, 59, 999);

      // 7天前00:00:00.000开始
      const start = new Date(end);
      start.setDate(end.getDate() - 6); // 从昨天往前推6天，总共7天
      start.setHours(0, 0, 0, 0);

      return [start, end];
    },
  },
  {
    text: '最近14天',
    value: () => {
      // 昨天23:59:59.999结束
      const end = new Date();
      end.setDate(end.getDate() - 1); // 设置为昨天
      end.setHours(23, 59, 59, 999);

      // 14天前00:00:00.000开始
      const start = new Date(end);
      start.setDate(end.getDate() - 13); // 从昨天往前推13天，总共14天
      start.setHours(0, 0, 0, 0);

      return [start, end];
    },
  }
];

// 状态变量
const operationStatusData = ref<CorpusOperationStatusDTO | null>(null);
const loading = ref(false);

// 计算是否有数据可显示
const hasData = computed(() => {
  return operationStatusData.value !== null;
});

// 加载面板相关
const showLoadingPanel = ref(false);
const loadingPanelRef = ref();

// 设置默认时间范围为最近7天（不含今天）
const getDefaultDateRange = (): [number, number] => {
  // 结束日期设置为昨天的23:59:59.999
  const end = new Date();
  end.setDate(end.getDate() - 1); // 设置为昨天
  end.setHours(23, 59, 59, 999);

  // 开始日期设置为7天前的00:00:00.000
  const start = new Date(end);
  start.setDate(end.getDate() - 6); // 从昨天往前推6天，总共7天
  start.setHours(0, 0, 0, 0);

  return [start.getTime(), end.getTime()];
};

const dateRange = ref<[number, number]>(getDefaultDateRange());

// 禁用今天及未来的日期
const disableFutureDates = (time: Date) => {
  // 获取当前日期并设置到昨天结束
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() >= today.getTime();
};

// 获取语料服务运营状态数据
const fetchOperationStatus = async (forceGenerate = false) => {
  if (!dateRange.value) return;

  // 取得选中的起始日期和结束日期时间戳(ms)
  let [beginDate, endDate] = dateRange.value;

  // 调整时间边界
  // 将开始日期设置为当天的00:00:00
  const beginDateTime = new Date(beginDate);
  beginDateTime.setHours(0, 0, 0, 0);
  beginDate = beginDateTime.getTime();

  // 将结束日期设置为当天的23:59:59.999
  const endDateTime = new Date(endDate);
  endDateTime.setHours(23, 59, 59, 999);
  endDate = endDateTime.getTime();

  loading.value = true;
  showLoadingPanel.value = true; // 显示加载进度面板

  try {
    const res = await httpRequest.rawRequestGet(API_PATHS.GET_CORPUS_OPERATION_STATUS, {
      beginDate,
      endDate,
      forceGenerate // 添加forceGenerate参数
    });

    if (res && typeof res === 'object' && 'code' in res && 'data' in res) {
      if (res.code === 0) {
        operationStatusData.value = res.data;
        // 完成加载
        loadingPanelRef.value?.complete();
        // 如果是强制重新生成，显示提示
        if (forceGenerate) {
          ElMessage.success('数据已强制重新生成');
        }
      } else {
        // 显示接口返回的错误信息
        ElMessage.error((res as any).msg || '获取语料服务运营状态数据失败');
        console.error('获取语料服务运营状态数据失败:', res);
        operationStatusData.value = null;
        showLoadingPanel.value = false; // 出错时直接关闭加载面板
      }
    } else {
      ElMessage.error('获取语料服务运营状态数据失败：接口返回数据格式错误');
      console.error('获取语料服务运营状态数据失败:', res);
      operationStatusData.value = null;
      showLoadingPanel.value = false; // 出错时直接关闭加载面板
    }
  } catch (error) {
    ElMessage.error('获取语料服务运营状态数据失败：请求异常');
    console.error('获取语料服务运营状态数据错误:', error);
    operationStatusData.value = null;
    showLoadingPanel.value = false; // 出错时直接关闭加载面板
  } finally {
    loading.value = false;
  }
};

// 处理强制重新生成按钮点击
const handleForceRegenerateClick = () => {
  ElMessageBox.confirm(
    '强制刷新将重新计算统计数据，可能需要较长时间。确定要继续吗？',
    '强制刷新数据',
    {
      confirmButtonText: '确认刷新',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      fetchOperationStatus(true); // 传入true表示强制重新生成
    })
    .catch(() => {
      // 用户取消，不做任何操作
    });
};

// 处理日期变化
const handleDateChange = () => {
  fetchOperationStatus(); // 默认使用缓存
};

// 页面加载时自动获取数据
fetchOperationStatus();
</script>

<style scoped lang="scss">
.corpus-operation-status-container {
  padding: 20px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      display: flex;
      align-items: center;

      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }

      .data-limit-hint {
        margin-left: 8px;
        font-size: 14px;
        color: #909399;
      }
    }

    .header-right {
      display: flex;
      gap: 16px;
      align-items: center;

      .force-regenerate-btn {
        white-space: nowrap;
      }
    }
  }

  .loading-container {
    margin: 24px 0;
  }

  .content {
    margin-top: 24px;
  }
}
</style>