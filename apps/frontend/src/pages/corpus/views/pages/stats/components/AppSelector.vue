<template>
  <div class="app-selector">
    <el-select
      v-model="selectedAppId"
      placeholder="请选择应用"
      clearable
      filterable
      @change="handleAppChange"
    >
      <el-option
        v-for="app in appList"
        :key="app.appId"
        :label="app.appName"
        :value="app.appId"
      >
        <div class="app-option">
          <span>{{ app.appName }}</span>
          <small class="app-owners">
            所有者: {{ app.owner ? app.owner.join(', ') : '无' }}
          </small>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineEmits } from 'vue';
import httpRequest from '../../../../../../utils/httpRequest';
import { API_PATHS } from '../../../../request/api';
import type { FridayAppInfo } from '../../../../types';

const emit = defineEmits(['change']);
const appList = ref<FridayAppInfo[]>([]);
const selectedAppId = ref('');

const fetchAppList = async () => {
  try {
    const res = await httpRequest.rawRequestGet(API_PATHS.QUERY_FRIDAY_APP_INFO_LIST);
    if (res && typeof res === 'object' && 'code' in res && 'data' in res && res.code === 0) {
      appList.value = res.data;
    } else {
      console.error('获取应用列表失败:', res);
    }
  } catch (error) {
    console.error('获取应用列表错误:', error);
  }
};

const handleAppChange = (value: string) => {
  emit('change', value);
};

onMounted(() => {
  fetchAppList();
});
</script>

<style scoped lang="scss">
.app-selector {
  margin-bottom: 20px;
  
  .app-option {
    display: flex;
    flex-direction: column;
    
    .app-owners {
      color: #909399;
      font-size: 12px;
    }
  }
}
</style> 