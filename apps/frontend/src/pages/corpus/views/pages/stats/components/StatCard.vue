<template>
  <el-card class="stat-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span>{{ title }}</span>
        <el-tooltip v-if="tooltip" :content="tooltip" placement="top">
          <el-icon><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
    </template>
    <div class="card-content">
      <div class="value">{{ value }}</div>
      <div class="description">{{ description }}</div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  tooltip: {
    type: String,
    default: ''
  }
});
</script>

<style scoped lang="scss">
.stat-card {
  margin-bottom: 16px;
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 500;
  }
  
  .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .value {
      font-size: 28px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 8px;
    }
    
    .description {
      font-size: 14px;
      color: #909399;
    }
  }
}
</style> 