<template>
  <div class="corpus-rank-chart">
    <div class="chart-header">
      <div class="chart-title">语料召回排名 TOP 10</div>
      <div class="chart-subtitle">按语料被召回的次数排序</div>
    </div>
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps } from 'vue';
import * as echarts from 'echarts';
import type { RecalledCorpusChunkInfo } from '../../../../../types';

const props = defineProps<{
  recallInfoByQuestion: Record<string, RecalledCorpusChunkInfo[]>;
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 处理数据，计算每个语料被召回的次数并排序
const processData = () => {
  const corpusRecallCounts: Record<string, {
    corpusId: string,
    title: string,
    content: string,
    count: number
  }> = {};
  
  // 遍历所有问题的召回语料
  Object.values(props.recallInfoByQuestion || {}).forEach(recalls => {
    recalls.forEach(corpus => {
      if (!corpusRecallCounts[corpus.corpusId]) {
        corpusRecallCounts[corpus.corpusId] = {
          corpusId: corpus.corpusId,
          title: corpus.title,
          content: corpus.content,
          count: 0
        };
      }
      corpusRecallCounts[corpus.corpusId].count++;
    });
  });
  
  // 转换为数组并排序
  const corpusRanks = Object.values(corpusRecallCounts)
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
  
  return corpusRanks;
};

// 格式化语料标题，截断过长的标题
const formatTitle = (title: string): string => {
  const maxLength = 20;
  if (title.length <= maxLength) {
    return title;
  }
  return title.substring(0, maxLength) + '...';
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  updateChart();
  
  window.addEventListener('resize', () => {
    chart?.resize();
  });
};

// 更新图表数据
const updateChart = () => {
  if (!chart) return;
  
  const corpusRanks = processData();
  
  // 获取语料标题和对应的召回次数数组
  const titles = corpusRanks.map(item => formatTitle(item.title));
  const counts = corpusRanks.map(item => item.count);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const dataIndex = params[0].dataIndex;
        const corpus = corpusRanks[dataIndex];
        return `<div style="max-width: 300px; word-break: break-all;">
                  <div><strong>语料ID:</strong> ${corpus.corpusId}</div>
                  <div><strong>标题:</strong> ${corpus.title}</div>
                  <div><strong>内容:</strong> ${corpus.content.substring(0, 100)}${corpus.content.length > 100 ? '...' : ''}</div>
                  <div><strong>召回次数:</strong> ${corpus.count}</div>
                </div>`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '召回次数',
      nameLocation: 'end'
    },
    yAxis: {
      type: 'category',
      data: titles,
      axisLabel: {
        interval: 0,
        formatter: (value: string) => value,
        margin: 8
      }
    },
    series: [
      {
        name: '召回次数',
        type: 'bar',
        data: counts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      }
    ]
  };
  
  chart.setOption(option);
};

// 监听数据变化
watch(() => props.recallInfoByQuestion, updateChart, { deep: true });

onMounted(() => {
  initChart();
});
</script>

<style scoped lang="scss">
.corpus-rank-chart {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 20px;
  
  .chart-header {
    margin-bottom: 16px;
    
    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
    
    .chart-subtitle {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
  
  .chart-container {
    height: 300px;
  }
}
</style> 