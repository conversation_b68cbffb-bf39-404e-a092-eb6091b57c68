<template>
  <div class="question-cluster-list">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else-if="clusters.length === 0" class="empty-state">
      <el-empty description="暂无类型数据" />
    </div>

    <template v-else>
      <!-- 总览信息 -->
      <div class="overview-section">
        <div class="overview-stats">
          <div class="stat-item">
            <span class="stat-label">问题聚类数量：</span>
            <span class="stat-value">{{ clusters.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">问题总数：</span>
            <span class="stat-value">{{ totalQuestionCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">已解决问题：</span>
            <span class="stat-value solved">{{ solvedQuestionCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">未解决问题：</span>
            <span class="stat-value unsolved">{{ unsolvedQuestionCount }}</span>
          </div>
        </div>
      </div>

      <el-divider />

      <!-- 聚类列表 -->
      <div class="cluster-list">
        <div
          v-for="(cluster, index) in clusters"
          :key="index"
          class="cluster-item"
        >
          <!-- 聚类头部 -->
          <div class="cluster-header" @click="toggleCluster(index)">
            <div class="cluster-info">
              <div class="cluster-title">
                <el-icon class="expand-icon" :class="{ 'expanded': expandedClusters.has(index) }">
                  <ArrowRight />
                </el-icon>
                <span class="cluster-pattern">{{ cluster.questionPattern }}</span>
              </div>
              <div class="cluster-meta">
                <el-tag type="primary" size="small">
                  {{ cluster.questionList.length }} 个问题
                </el-tag>
                <el-tag
                  v-if="getClusterSolvedCount(cluster) > 0"
                  type="success"
                  size="small"
                >
                  {{ getClusterSolvedCount(cluster) }} 个已解决
                </el-tag>
                <el-tag
                  v-if="getClusterUnsolvedCount(cluster) > 0"
                  type="danger"
                  size="small"
                >
                  {{ getClusterUnsolvedCount(cluster) }} 个未解决
                </el-tag>
              </div>
            </div>
            <div class="cluster-actions">
              <el-button
                type="text"
                size="small"
                @click.stop="viewAllQuestions(cluster)"
              >
                查看全部
              </el-button>
            </div>
          </div>

          <!-- 展开的问题列表 -->
          <div v-if="expandedClusters.has(index)" class="cluster-content">
            <div class="questions-list">
              <div
                v-for="(question, qIndex) in cluster.questionList"
                :key="qIndex"
                class="question-item"
                :class="question.solved ? 'solved' : 'unsolved'"
                @click="openQuestionDetail(question)"
              >
                <div class="question-index">{{ qIndex + 1 }}.</div>
                <div class="question-content">
                  <div class="question-text">{{ question.question }}</div>
                  <div class="question-meta">
                    <span class="question-time">{{ formatDateTime(question.questionTime) }}</span>
                    <span class="question-id">ID: {{ question.questionMessageId }}</span>
                  </div>
                </div>
                <div class="question-actions">
                  <el-tag
                    :type="question.solved ? 'success' : 'danger'"
                    size="small"
                    class="status-tag"
                  >
                    {{ question.solved ? '已解决' : '未解决' }}
                  </el-tag>
                  <el-button
                    type="text"
                    size="small"
                    :class="question.solved ? 'view-btn-solved' : 'view-btn-unsolved'"
                    @click.stop="openQuestionDetail(question)"
                  >
                    查看详情
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 问题详情弹窗 -->
      <el-dialog
        v-model="questionDialogVisible"
        :title="'问题详情'"
        width="60%"
        :before-close="handleQuestionDialogClose"
      >
        <template v-if="selectedQuestion">
          <div class="question-detail">
            <div class="question-detail-header">
              <div class="question-title">问题详情</div>
              <el-button
                type="primary"
                size="small"
                @click="openConversationDetail"
                v-if="selectedQuestion.questionConversationId"
              >
                查看会话详情
              </el-button>
            </div>
            <div class="question-detail-item">
              <div class="detail-label">问题内容</div>
              <div class="detail-value">{{ selectedQuestion.question }}</div>
            </div>
            <div class="question-detail-item">
              <div class="detail-label">提问时间</div>
              <div class="detail-value">{{ formatDateTime(selectedQuestion.questionTime) }}</div>
            </div>
            <div class="question-detail-item">
              <div class="detail-label">问题ID</div>
              <div class="detail-value">{{ selectedQuestion.questionMessageId }}</div>
            </div>
            <div class="question-detail-item">
              <div class="detail-label">会话ID</div>
              <div class="detail-value">{{ selectedQuestion.questionConversationId }}</div>
            </div>
            <div class="question-detail-item">
              <div class="detail-label">解决状态</div>
              <div class="detail-value">
                <el-tag
                  :type="selectedQuestion.solved ? 'success' : 'danger'"
                  size="small"
                >
                  {{ selectedQuestion.solved ? '已解决' : '未解决' }}
                </el-tag>
              </div>
            </div>

            <!-- 召回语料信息 -->
            <template v-if="displayRecallInfo">
              <div class="question-detail-item">
                <div class="detail-label">召回语料信息</div>
                <div class="detail-value" v-if="displayRecallInfo.length > 0">
                  <div v-for="(chunk, index) in displayRecallInfo" :key="chunk.corpusId" class="recall-chunk">
                    <div class="chunk-header">
                      <div class="chunk-title">{{ index + 1 }}. {{ chunk.title || '无标题' }}</div>
                      <div class="chunk-source" :class="getSourceTypeClass(chunk.source)">
                        {{ getSourceTypeText(chunk.source) }}
                      </div>
                    </div>
                    <div class="chunk-content">{{ chunk.content }}</div>
                  </div>
                </div>
                <div class="detail-value no-data" v-else>
                  未找到召回语料
                </div>
              </div>
            </template>
          </div>
        </template>
      </el-dialog>

      <!-- 聚类全部问题弹窗 -->
      <el-dialog
        v-model="clusterDialogVisible"
        :title="selectedCluster?.questionPattern || '聚类问题列表'"
        width="70%"
        :before-close="handleClusterDialogClose"
      >
        <template v-if="selectedCluster">
          <div class="cluster-detail-header">
            <div class="cluster-questions-count">
              <span>共 {{ selectedCluster.questionList.length }} 个问题</span>
              <span class="solved-count">
                （{{ getClusterSolvedCount(selectedCluster) }} 个已解决，{{ getClusterUnsolvedCount(selectedCluster) }} 个未解决）
              </span>
            </div>
          </div>
          <el-divider />
          <div class="cluster-questions-list">
            <div
              v-for="(questionItem, index) in selectedCluster.questionList"
              :key="index"
              class="question-item"
              :class="questionItem.solved ? 'solved' : 'unsolved'"
              @click="openQuestionDetail(questionItem)"
            >
              <div class="question-index">{{ index + 1 }}.</div>
              <div class="question-content">
                <div class="question-text">{{ questionItem.question }}</div>
                <div class="question-meta">
                  <span class="question-time">{{ formatDateTime(questionItem.questionTime) }}</span>
                  <span class="question-id">ID: {{ questionItem.questionMessageId }}</span>
                </div>
              </div>
              <div class="question-actions">
                <el-tag
                  :type="questionItem.solved ? 'success' : 'danger'"
                  size="small"
                  class="status-tag"
                >
                  {{ questionItem.solved ? '已解决' : '未解决' }}
                </el-tag>
                <el-button
                  type="text"
                  class="view-btn"
                  :class="questionItem.solved ? 'view-btn-solved' : 'view-btn-unsolved'"
                  @click.stop="openQuestionDetail(questionItem)"
                >查看</el-button>
              </div>
            </div>
          </div>
        </template>
      </el-dialog>

      <!-- 会话详情弹窗 -->
      <el-dialog
        v-model="conversationDialogVisible"
        :title="`会话详情: ${selectedQuestion?.questionConversationId || ''}`"
        width="70%"
        @opened="handleConversationDialogOpened"
      >
        <ConversationDetailPanel
          :messages="conversationMessages"
          :loading="conversationLoading"
          :error="conversationLoadError"
          :current-message-id="selectedQuestion?.questionMessageId"
          @view-recall-info="viewMessageRecallInfo"
        />

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="conversationDialogVisible = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import { ArrowRight } from '@element-plus/icons-vue';
import ConversationDetailPanel from './ConversationDetailPanel.vue';
import httpRequest from '../../../../../../../utils/httpRequest';
import { API_PATHS } from '../../../../../request/api';

// 更新问题聚类接口定义
interface QuestionItem {
  question: string;
  questionMessageId: string;
  questionConversationId: string;
  questionTime: number;
  solved: boolean;
}

interface QuestionCluster {
  questionPattern: string;
  questionList: QuestionItem[];
}

// Friday会话消息DTO接口
interface FridayConversationMessageDTO {
  messageId: string;
  conversationId: string;
  userId: string;
  userType: string;
  role: string;
  generateType: string;
  message: string;
  status: string;
  addTime: string | number;
  parentMessageId: string;
  appId: string;
  conversationName: string;
}

// 召回语料信息接口
interface RecalledCorpusChunkInfo {
  corpusId: string;
  parentId: string;
  source: number;
  title: string;
  content: string;
}

const props = defineProps<{
  data: {
    clusters: QuestionCluster[];
    loading: boolean;
    recallInfoByQuestion?: Record<string, RecalledCorpusChunkInfo[]>;
  }
}>();

// 展开状态管理
const expandedClusters = ref(new Set<number>());

// 弹窗状态
const questionDialogVisible = ref(false);
const clusterDialogVisible = ref(false);
const conversationDialogVisible = ref(false);
const selectedQuestion = ref<QuestionItem | null>(null);
const selectedCluster = ref<QuestionCluster | null>(null);

// 会话相关状态
const conversationLoading = ref(false);
const conversationLoadError = ref('');
const conversationMessages = ref<FridayConversationMessageDTO[]>([]);

// 计算当前选中问题的召回信息
const displayRecallInfo = computed(() => {
  if (!selectedQuestion.value) return null;
  return props.data.recallInfoByQuestion?.[selectedQuestion.value.question] || [];
});

// 格式化日期时间
const formatDateTime = (timestamp: number): string => {
  if (!timestamp) return '无时间信息';

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 加载状态
const loading = computed(() => props.data.loading || false);

// 聚类数据
const clusters = computed(() => {
  if (!props.data.clusters) return [];

  // 复制一份数据进行排序，避免修改原数据
  return [...props.data.clusters].sort((a, b) => {
    // 按问题数量从高到低排序
    return b.questionList.length - a.questionList.length;
  });
});

// 计算总问题数量
const totalQuestionCount = computed(() => {
  return clusters.value.reduce((total, cluster) => total + cluster.questionList.length, 0);
});

// 计算已解决问题数量
const solvedQuestionCount = computed(() => {
  return clusters.value.reduce((total, cluster) => {
    return total + cluster.questionList.filter(q => q.solved).length;
  }, 0);
});

// 计算未解决问题数量
const unsolvedQuestionCount = computed(() => {
  return totalQuestionCount.value - solvedQuestionCount.value;
});

// 获取聚类中已解决问题数量
const getClusterSolvedCount = (cluster: QuestionCluster) => {
  return cluster.questionList.filter(q => q.solved).length;
};

// 获取聚类中未解决问题数量
const getClusterUnsolvedCount = (cluster: QuestionCluster) => {
  return cluster.questionList.filter(q => !q.solved).length;
};

// 切换聚类展开状态
const toggleCluster = (index: number) => {
  if (expandedClusters.value.has(index)) {
    expandedClusters.value.delete(index);
  } else {
    expandedClusters.value.add(index);
  }
};

// 查看聚类全部问题
const viewAllQuestions = (cluster: QuestionCluster) => {
  selectedCluster.value = cluster;
  clusterDialogVisible.value = true;
};

// 打开问题详情
const openQuestionDetail = (question: QuestionItem) => {
  selectedQuestion.value = question;
  questionDialogVisible.value = true;
};

// 关闭弹窗
const handleQuestionDialogClose = () => {
  questionDialogVisible.value = false;
  selectedQuestion.value = null;
};

const handleClusterDialogClose = () => {
  clusterDialogVisible.value = false;
  selectedCluster.value = null;
};

// 打开会话详情弹窗
const openConversationDetail = () => {
  if (!selectedQuestion.value?.questionConversationId) {
    console.error('无法打开会话详情：缺少会话ID');
    return;
  }
  conversationDialogVisible.value = true;
};

// 处理会话详情弹窗打开
const handleConversationDialogOpened = async () => {
  if (!selectedQuestion.value?.questionConversationId) {
    conversationLoadError.value = '无效的会话ID';
    return;
  }

  const conversationId = selectedQuestion.value.questionConversationId;
  conversationLoading.value = true;
  conversationLoadError.value = '';
  conversationMessages.value = [];

  try {
    // 使用API_PATHS常量获取会话消息
    const response = await httpRequest.rawRequestGet(
      API_PATHS.QUERY_CONVERSATION_MESSAGES,
      { conversationId }
    ) as unknown as { code: number; message: string; data: FridayConversationMessageDTO[] };

    if (response.code === 0 && Array.isArray(response.data)) {
      conversationMessages.value = response.data.sort((a: FridayConversationMessageDTO, b: FridayConversationMessageDTO) => {
        // 按时间排序
        return new Date(a.addTime).getTime() - new Date(b.addTime).getTime();
      });
    } else {
      conversationLoadError.value = response.message || '获取会话消息失败';
    }
  } catch (error) {
    console.error('加载会话消息出错:', error);
    conversationLoadError.value = '获取会话消息时发生错误，请稍后重试';
  } finally {
    conversationLoading.value = false;
  }
};

// 查看消息召回信息
const viewMessageRecallInfo = (message: FridayConversationMessageDTO) => {
  if (message.role === 'user') {
    // 设置问题数据
    const questionTime = message.addTime ? new Date(message.addTime).getTime() : Date.now();
    selectedQuestion.value = {
      question: message.message,
      questionMessageId: message.messageId,
      questionConversationId: message.conversationId,
      questionTime,
      solved: false
    };

    // 打开问题详情弹窗
    conversationDialogVisible.value = false;
    questionDialogVisible.value = true;
  }
};

// 获取语料来源类型文本
const getSourceTypeText = (source: number) => {
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

// 获取语料来源类型样式类
const getSourceTypeClass = (source: number) => {
  switch (source) {
    case 0:
      return 'source-other';
    case 1:
      return 'source-service';
    case 2:
      return 'source-friday';
    default:
      return 'source-unknown';
  }
};
</script>

<style scoped lang="scss">
.question-cluster-list {
  .loading-container {
    padding: 20px 0;
  }

  .empty-state {
    padding: 40px 0;
  }

  .overview-section {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;

    .overview-stats {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;

      .stat-item {
        display: flex;
        align-items: center;

        .stat-label {
          color: #606266;
          font-size: 14px;
        }

        .stat-value {
          color: #E6A23C;
          font-weight: bold;
          font-size: 16px;
          margin-left: 4px;

          &.solved {
            color: #67C23A;
          }

          &.unsolved {
            color: #F56C6C;
          }
        }
      }
    }
  }

  .cluster-list {
    .cluster-item {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      margin-bottom: 12px;
      overflow: hidden;

      .cluster-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background: #fafafa;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: #f0f0f0;
        }

        .cluster-info {
          flex: 1;

          .cluster-title {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .expand-icon {
              margin-right: 8px;
              transition: transform 0.3s;
              color: #909399;

              &.expanded {
                transform: rotate(90deg);
              }
            }

            .cluster-pattern {
              font-weight: 500;
              color: #303133;
              font-size: 15px;
            }
          }

          .cluster-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
          }
        }

        .cluster-actions {
          .el-button {
            color: #409EFF;
          }
        }
      }

      .cluster-content {
        border-top: 1px solid #ebeef5;

        .questions-list {
          .question-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #f5f7fa;
            cursor: pointer;
            transition: background-color 0.3s;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background: #f8f9fa;
            }

            &.unsolved {
              .question-text {
                color: #F56C6C;
              }
            }

            &.solved {
              .question-text {
                color: #67C23A;
              }
            }

            .question-index {
              color: #909399;
              font-size: 14px;
              margin-right: 12px;
              min-width: 24px;
            }

            .question-content {
              flex: 1;

              .question-text {
                font-size: 14px;
                line-height: 1.5;
                margin-bottom: 4px;
              }

              .question-meta {
                display: flex;
                gap: 16px;

                .question-time,
                .question-id {
                  color: #909399;
                  font-size: 12px;
                }
              }
            }

            .question-actions {
              display: flex;
              align-items: center;
              gap: 8px;

              .view-btn-unsolved {
                color: #F56C6C;
              }

              .view-btn-solved {
                color: #67C23A;
              }
            }
          }
        }
      }
    }
  }
}

// 问题详情弹窗样式
.question-detail {
  .question-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .question-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
  }

  .question-detail-item {
    margin-bottom: 16px;

    .detail-label {
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .detail-value {
      color: #606266;
      font-size: 14px;
      line-height: 1.6;

      &.no-data {
        color: #909399;
        font-style: italic;
      }
    }

    .recall-chunk {
      border: 1px solid #ebeef5;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 8px;

      .chunk-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .chunk-title {
          font-weight: 500;
          color: #303133;
          font-size: 14px;
        }

        .chunk-source {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;

          &.source-other {
            background: #f0f0f0;
            color: #909399;
          }

          &.source-service {
            background: #e1f3d8;
            color: #67c23a;
          }

          &.source-friday {
            background: #ecf5ff;
            color: #409eff;
          }

          &.source-unknown {
            background: #f4f4f5;
            color: #909399;
          }
        }
      }

      .chunk-content {
        color: #606266;
        font-size: 13px;
        line-height: 1.5;
        background: #fafafa;
        padding: 8px;
        border-radius: 4px;
      }
    }
  }
}

// 聚类详情弹窗样式
.cluster-detail-header {
  .cluster-questions-count {
    font-size: 16px;
    color: #303133;

    .solved-count {
      color: #67C23A;
      font-weight: 500;
    }
  }
}

.cluster-questions-list {
  .question-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f5f7fa;
    cursor: pointer;
    transition: background-color 0.3s;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f8f9fa;
    }

    &.unsolved {
      .question-text {
        color: #F56C6C;
      }
    }

    &.solved {
      .question-text {
        color: #67C23A;
      }
    }

    .question-index {
      color: #909399;
      font-size: 14px;
      margin-right: 12px;
      min-width: 24px;
    }

    .question-content {
      flex: 1;

      .question-text {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 4px;
      }

      .question-meta {
        display: flex;
        gap: 16px;

        .question-time,
        .question-id {
          color: #909399;
          font-size: 12px;
        }
      }
    }

    .question-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .view-btn-unsolved {
        color: #F56C6C;
      }

      .view-btn-solved {
        color: #67C23A;
      }
    }
  }
}
</style>