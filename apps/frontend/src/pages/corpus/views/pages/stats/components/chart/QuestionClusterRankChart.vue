<template>
  <div class="question-cluster-rank-chart">
    <div class="chart-header">
      <div class="title-container">
        <div class="chart-title">周期内用户提问聚类排名</div>
        <el-tooltip
          content="展示问题聚类按照包含问题数量从高到低排序，点击可查看聚类详情。"
          placement="top"
          effect="light"
        >
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="chart-subtitle">按照每种类型的问题数量从高到低排序</div>
    </div>
    <div ref="chartRef" class="chart-container"></div>
    
    <!-- 聚类详情弹窗 -->
    <el-dialog
      v-model="clusterDialogVisible"
      :title="selectedCluster?.questionPattern || '问题聚类详情'"
      width="60%"
      :before-close="handleClusterDialogClose"
    >
      <template v-if="selectedCluster">
        <div class="cluster-detail-header">
          <div class="cluster-questions-count">
            <span>共 {{ selectedCluster.questionList.length }} 个问题</span>
          </div>
        </div>
        <el-divider />
        <div class="cluster-questions-list">
          <div 
            v-for="(questionItem, index) in selectedCluster.questionList" 
            :key="index"
            class="question-item"
            :class="questionItem.solved ? 'solved' : 'unsolved'"
            @click="openQuestionDetail(questionItem)"
          >
            <div class="question-index">{{ index + 1 }}.</div>
            <div class="question-content">
              <div class="question-text">{{ questionItem.question }}</div>
              <div class="question-meta">
                <span class="question-time">{{ formatDateTime(questionItem.questionTime) }}</span>
                <span class="question-id">ID: {{ questionItem.questionMessageId }}</span>
              </div>
            </div>
            <div class="question-actions">
              <el-tag
                :type="questionItem.solved ? 'success' : 'danger'"
                size="small"
                class="status-tag"
              >
                {{ questionItem.solved ? '已解决' : '未解决' }}
              </el-tag>
              <el-button 
                type="text" 
                class="view-btn" 
                :class="questionItem.solved ? 'view-btn-solved' : 'view-btn-unsolved'"
                @click.stop="openQuestionDetail(questionItem)"
              >查看</el-button>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
    
    <!-- 问题详情弹窗 -->
    <el-dialog
      v-model="questionDialogVisible"
      title="问题详情"
      width="50%"
      :before-close="handleQuestionDialogClose"
    >
      <div v-if="selectedQuestionData" class="question-detail-container">
        <div class="question-detail-header">
          <div class="question-title">问题详情</div>
          <el-button 
            type="primary" 
            size="small" 
            @click="openConversationDetail" 
            v-if="selectedQuestionData.questionConversationId"
          >
            查看会话详情
          </el-button>
        </div>
        <div class="question-detail-item">
          <div class="detail-label">问题内容</div>
          <div class="detail-value question-content" v-html="renderedQuestionContent"></div>
        </div>
        <div class="question-detail-item">
          <div class="detail-label">
            消息ID
            <el-button 
              type="primary" 
              size="small" 
              link 
              @click="manuallySearchRecallInfo"
              style="margin-left: 10px;"
            >
              手动搜索召回信息
            </el-button>
          </div>
          <div class="detail-value detail-id">{{ selectedQuestionData.questionMessageId }}</div>
        </div>
        <div class="question-detail-item">
          <div class="detail-label">所属会话ID</div>
          <div class="detail-value detail-id">{{ selectedQuestionData.questionConversationId }}</div>
        </div>
        
        <!-- 召回语料信息 -->
        <template v-if="displayRecallInfo">
          <div class="question-detail-item">
            <div class="detail-label">召回语料信息</div>
            <div class="detail-value" v-if="displayRecallInfo.recalledCorpusChunkInfoList.length > 0">
              <div v-for="(chunk, index) in displayRecallInfo.recalledCorpusChunkInfoList" :key="chunk.corpusId" class="recall-chunk">
                <div class="chunk-header">
                  <div class="chunk-title">{{ index + 1 }}. {{ chunk.title || '无标题' }}</div>
                  <div class="chunk-source" :class="getSourceTypeClass(chunk.source)">
                    {{ getSourceTypeText(chunk.source) }}
                  </div>
                </div>
                <div class="chunk-content">{{ chunk.content }}</div>
              </div>
            </div>
            <div class="detail-value no-data" v-else>
              未找到召回语料
            </div>
          </div>
        </template>
        <div class="question-detail-item" v-else>
          <div class="detail-label">召回语料信息</div>
          <div class="detail-value no-data">
            无可用召回信息
          </div>
        </div>
        
        <!-- 调试信息区域 -->
        <div v-if="debugInfo" class="question-detail-item debug-info">
          <div class="detail-label">调试信息</div>
          <div class="detail-value">
            <pre>{{ debugInfo }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 修改会话详情弹窗部分 -->
    <el-dialog
      v-model="conversationDialogVisible"
      :title="`会话详情: ${selectedQuestionData?.questionConversationId || ''}`"
      width="70%"
      @opened="handleConversationDialogOpened"
    >
      <ConversationDetailPanel
        :messages="conversationMessages"
        :loading="conversationLoading"
        :error="conversationLoadError"
        :current-message-id="selectedQuestionData?.questionMessageId"
        @view-recall-info="viewMessageRecallInfo"
      />
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="conversationDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps, computed } from 'vue';
import * as echarts from 'echarts';
import { QuestionFilled } from '@element-plus/icons-vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import httpRequest from '../../../../../../../utils/httpRequest';
import { API_PATHS } from '../../../../../request/api';
import ConversationDetailPanel from '../detail/ConversationDetailPanel.vue';

// 配置marked选项，确保图片渲染正常
marked.setOptions({
  breaks: true,        // 将回车转换为<br>
  gfm: true            // 使用GitHub风格Markdown
});

// 更新问题聚类接口定义
interface QuestionItem {
  question: string; 
  questionMessageId: string; 
  questionConversationId: string;
  questionTime: number;
  solved: boolean;
}

interface QuestionCluster {
  questionPattern: string;
  questionList: QuestionItem[];
}

interface RecalledCorpusChunkInfo {
  corpusId: string;
  parentId: string;
  source: number;
  title: string;
  content: string;
}

interface QuestionCorpusRecallDetailItem {
  questionContent: string;
  questionMessageId: string;
  questionConversationId: string;
  recalledCorpusChunkInfoList: RecalledCorpusChunkInfo[];
}

// 添加Friday会话消息DTO接口
interface FridayConversationMessageDTO {
  messageId: string;
  conversationId: string;
  userId: string;
  userType: string;
  role: string;
  generateType: string;
  message: string;
  status: string;
  addTime: string | number;
  parentMessageId: string;
  appId: string;
  conversationName: string;
}

const props = defineProps<{
  questionClusters: QuestionCluster[];
  questionAndRecallInfos?: QuestionCorpusRecallDetailItem[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 聚类详情弹窗相关
const clusterDialogVisible = ref(false);
const selectedCluster = ref<QuestionCluster | null>(null);

// 问题详情弹窗相关
const questionDialogVisible = ref(false);
const selectedQuestionData = ref<QuestionItem | null>(null);

// 计算属性：将问题内容转换为HTML（支持markdown和图片）
const renderedQuestionContent = computed(() => {
  if (!selectedQuestionData.value?.question) return '';
  
  try {
    // 使用marked解析markdown为HTML
    const html = marked.parse(selectedQuestionData.value.question) as string;
    
    // 使用DOMPurify清理HTML，防止XSS攻击，但允许图片标签
    return DOMPurify.sanitize(html, {
      ADD_TAGS: ['img'],
      ADD_ATTR: ['src', 'alt', 'title']
    });
  } catch (error) {
    console.error('Error rendering markdown:', error);
    return selectedQuestionData.value.question;
  }
});

// 格式化日期时间
const formatDateTime = (timestamp: number): string => {
  if (!timestamp) return '无时间信息';
  
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 处理聚类弹窗关闭
const handleClusterDialogClose = () => {
  clusterDialogVisible.value = false;
};

// 处理问题弹窗关闭
const handleQuestionDialogClose = () => {
  questionDialogVisible.value = false;
};

// 打开聚类详情弹窗
const openClusterDetail = (cluster: QuestionCluster) => {
  selectedCluster.value = cluster;
  clusterDialogVisible.value = true;
};

// 打开问题详情弹窗
const openQuestionDetail = (questionItem: QuestionItem) => {
  selectedQuestionData.value = questionItem;
  questionDialogVisible.value = true;
};

// 处理数据，按问题数量从高到低排序
const processData = () => {
  if (!props.questionClusters || props.questionClusters.length === 0) {
    return [];
  }
  
  // 复制一份数据进行排序，避免修改原数据
  return [...props.questionClusters]
    .sort((a, b) => b.questionList.length - a.questionList.length);
};

// 格式化聚类模式名称，截断过长的文本
const formatPattern = (pattern: string): string => {
  const maxLength = 25;
  if (pattern.length <= maxLength) {
    return pattern;
  }
  return pattern.substring(0, maxLength) + '...';
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  updateChart();
  
  // 注册点击事件
  chart.on('click', (params) => {
    const dataIndex = params.dataIndex;
    const sortedClusters = processData();
    // 反转后的数组，与显示顺序一致
    const reversedClusters = [...sortedClusters].reverse();
    
    if (dataIndex !== undefined && reversedClusters[dataIndex]) {
      openClusterDetail(reversedClusters[dataIndex]);
    }
  });
  
  window.addEventListener('resize', () => {
    chart?.resize();
  });
};

// 更新图表数据
const updateChart = () => {
  if (!chart) return;
  
  const sortedClusters = processData();
  
  // 如果没有数据，显示无数据状态
  if (sortedClusters.length === 0) {
    chart.setOption({
      title: {
        text: '暂无聚类数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#909399',
          fontSize: 14
        }
      }
    });
    return;
  }
  
  // 获取聚类模式名称和对应的问题数量，反转数组以确保最大值在顶部
  const patterns = [...sortedClusters.map(item => formatPattern(item.questionPattern))].reverse();
  const counts = [...sortedClusters.map(item => item.questionList.length)].reverse();
  
  const option = {
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        return `<div style="font-weight: bold">${params.name}</div>问题数量: ${params.value}<div style="margin-top: 5px; font-size: 12px; color: #909399;">点击图表可查看聚类下问题详情</div>`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '问题数量',
      nameLocation: 'end'
    },
    yAxis: {
      type: 'category',
      data: patterns,
      axisLabel: {
        interval: 0,
        formatter: (value: string) => value,
        margin: 8
      }
    },
    series: [
      {
        name: '问题数量',
        type: 'bar',
        data: counts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#E6A23C' },
            { offset: 0.5, color: '#F56C6C' },
            { offset: 1, color: '#F56C6C' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#F56C6C' },
              { offset: 0.7, color: '#E6A23C' },
              { offset: 1, color: '#FAECD8' }
            ])
          }
        }
      }
    ]
  };
  
  chart.setOption(option);
};

// 监听数据变化
watch(() => props.questionClusters, updateChart, { deep: true });

// 修改计算属性，改进匹配逻辑
const relatedRecallInfo = computed(() => {
  if (!selectedQuestionData.value?.questionMessageId || !props.questionAndRecallInfos) {
    return null;
  }
  
  const questionId = selectedQuestionData.value.questionMessageId;
  // 尝试多种匹配方式
  const matchingInfo = props.questionAndRecallInfos.find(info => {
    // 方式1: 直接严格匹配
    if (info.questionMessageId === questionId) return true;
    
    // 方式2: 转换为字符串后比较
    const infoIdStr = String(info.questionMessageId).trim();
    const questionIdStr = String(questionId).trim();
    if (infoIdStr === questionIdStr) return true;
    
    // 方式3: 如果都是数字字符，尝试转换为数字比较
    if (/^\d+$/.test(infoIdStr) && /^\d+$/.test(questionIdStr)) {
      if (Number(infoIdStr) === Number(questionIdStr)) return true;
    }
    
    // 方式4: 检查一个是否是另一个的子字符串
    if (infoIdStr.includes(questionIdStr) || questionIdStr.includes(infoIdStr)) {
      return true;
    }
    
    return false;
  });
  
  return matchingInfo;
});

// 添加调试信息和手动搜索方法
const debugInfo = ref('');

// 添加一个新的计算属性，用于在手动搜索时找到的匹配结果
const manuallyFoundRecallInfo = ref<QuestionCorpusRecallDetailItem | null>(null);

// 手动搜索召回信息
const manuallySearchRecallInfo = () => {
  debugInfo.value = '';
  manuallyFoundRecallInfo.value = null;
  
  if (!selectedQuestionData.value?.questionMessageId || !props.questionAndRecallInfos) {
    debugInfo.value = `无法搜索: questionMessageId=${selectedQuestionData.value?.questionMessageId}, 
    questionAndRecallInfos=${!!props.questionAndRecallInfos} (${props.questionAndRecallInfos?.length || 0}项)`;
    return;
  }
  
  const questionId = selectedQuestionData.value.questionMessageId;
  debugInfo.value = `搜索ID: ${questionId}\n`;
  debugInfo.value += `questionAndRecallInfos包含${props.questionAndRecallInfos.length}项\n\n`;
  
  // 记录所有ID供比较
  const allIds = props.questionAndRecallInfos.map(info => info.questionMessageId);
  debugInfo.value += `所有ID列表: ${JSON.stringify(allIds, null, 2)}\n\n`;
  
  // 尝试找到匹配项
  let found = false;
  props.questionAndRecallInfos.forEach((info, index) => {
    // 检查各种匹配可能性
    const exactMatch = info.questionMessageId === questionId;
    const stringMatch = String(info.questionMessageId).trim() === String(questionId).trim();
    const numberMatch = !isNaN(Number(info.questionMessageId)) && !isNaN(Number(questionId)) && 
                        Number(info.questionMessageId) === Number(questionId);
    const includesMatch = String(info.questionMessageId).includes(String(questionId)) || 
                          String(questionId).includes(String(info.questionMessageId));
    
    if (exactMatch || stringMatch || numberMatch || includesMatch) {
      found = true;
      debugInfo.value += `找到匹配项 #${index}:\n`;
      debugInfo.value += `匹配类型: ${exactMatch ? '精确匹配' : ''}${stringMatch ? '字符串匹配' : ''}${numberMatch ? '数字匹配' : ''}${includesMatch ? '包含匹配' : ''}\n`;
      debugInfo.value += `问题内容: ${info.questionContent}\n`;
      debugInfo.value += `问题ID: ${info.questionMessageId}\n`;
      debugInfo.value += `召回语料数量: ${info.recalledCorpusChunkInfoList?.length || 0}\n`;
      
      // 存储找到的结果
      manuallyFoundRecallInfo.value = info;
    }
  });
  
  if (!found) {
    debugInfo.value += `未找到匹配项! 请检查ID格式或数据源`;
  }
};

// 召回语料信息显示逻辑
const displayRecallInfo = computed(() => {
  // 优先使用手动查找的结果，其次使用自动匹配的结果
  return manuallyFoundRecallInfo.value || relatedRecallInfo.value;
});

// 添加语料来源类型的映射函数
const getSourceTypeText = (source: number): string => {
  // 根据系统中的枚举定义显示对应文本
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

// 获取语料来源对应的标签样式
const getSourceTypeClass = (source: number): string => {
  switch (source) {
    case 0:
      return 'source-type-0'; // 灰色
    case 1:
      return 'source-type-1'; // 绿色
    case 2:
      return 'source-type-2'; // 橙色
    default:
      return 'source-type-0';
  }
};

// 添加会话详情弹窗相关
const conversationDialogVisible = ref(false);
const conversationLoading = ref(false);
const conversationLoadError = ref('');
const conversationMessages = ref<FridayConversationMessageDTO[]>([]);

// 打开会话详情弹窗
const openConversationDetail = () => {
  if (!selectedQuestionData.value?.questionConversationId) {
    console.error('无法打开会话详情：缺少会话ID');
    return;
  }
  conversationDialogVisible.value = true;
};

// 处理会话详情弹窗打开
const handleConversationDialogOpened = async () => {
  if (!selectedQuestionData.value?.questionConversationId) {
    conversationLoadError.value = '无效的会话ID';
    return;
  }
  
  const conversationId = selectedQuestionData.value.questionConversationId;
  conversationLoading.value = true;
  conversationLoadError.value = '';
  conversationMessages.value = [];
  
  try {
    // 使用API_PATHS常量获取会话消息
    const response = await httpRequest.rawRequestGet(
      API_PATHS.QUERY_CONVERSATION_MESSAGES, 
      { conversationId }
    ) as unknown as { code: number; message: string; data: FridayConversationMessageDTO[] };
    
    if (response.code === 0 && Array.isArray(response.data)) {
      conversationMessages.value = response.data.sort((a: FridayConversationMessageDTO, b: FridayConversationMessageDTO) => {
        // 按时间排序
        return new Date(a.addTime).getTime() - new Date(b.addTime).getTime();
      });
    } else {
      conversationLoadError.value = response.message || '获取会话消息失败';
    }
  } catch (error) {
    console.error('加载会话消息出错:', error);
    conversationLoadError.value = '获取会话消息时发生错误，请稍后重试';
  } finally {
    conversationLoading.value = false;
  }
};

// 查看消息召回信息
const viewMessageRecallInfo = (message: FridayConversationMessageDTO) => {
  if (message.role === 'user') {
    // 更新调试信息
    debugInfo.value = '';
    manuallyFoundRecallInfo.value = null;
    
    // 设置问题数据
    const questionTime = message.addTime ? new Date(message.addTime).getTime() : Date.now();
    selectedQuestionData.value = {
      question: message.message,
      questionMessageId: message.messageId,
      questionConversationId: message.conversationId,
      questionTime,
      solved: false
    };
    
    // 打开问题详情弹窗
    conversationDialogVisible.value = false;
    questionDialogVisible.value = true;
  }
};

onMounted(() => {
  initChart();
});
</script>

<style scoped lang="scss">
.question-cluster-rank-chart {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 20px;
  
  .chart-header {
    margin-bottom: 16px;
    
    .title-container {
      display: flex;
      align-items: center;
      
      .chart-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
      
      .help-icon {
        margin-left: 4px;
        color: #909399;
        font-size: 14px;
        cursor: help;
      }
    }
    
    .chart-subtitle {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
  
  .chart-container {
    height: 400px;
  }
  
  .cluster-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .cluster-questions-count {
      font-size: 14px;
      color: #606266;
    }
  }
  
  .cluster-questions-list {
    max-height: 400px;
    overflow-y: auto;
    
    .question-item {
      display: flex;
      align-items: center;
      padding: 12px;
      margin-bottom: 8px;
      background-color: #f9f9f9;
      border-left: 3px solid #E6A23C;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
      
      &.solved {
        border-left: 3px solid #67C23A;
      }
      
      &.unsolved {
        border-left: 3px solid #F56C6C;
      }
      
      &:hover {
        background-color: #f1f1f1;
      }
      
      .question-index {
        min-width: 24px;
        color: #909399;
        font-size: 14px;
        margin-right: 8px;
      }
      
      .question-content {
        flex: 1;
        line-height: 1.5;
        word-break: break-word;
        color: #303133;
        
        .question-text {
          padding-right: 8px;
          margin-bottom: 6px;
        }
        
        .question-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
          
          .question-id {
            font-family: monospace;
          }
          
          .question-time {
            font-style: italic;
          }
        }
      }
      
      .question-actions {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;
        
        .status-tag {
          white-space: nowrap;
        }
        
        .view-btn {
          font-size: 12px;
          padding: 0;
          margin: 0;
          
          &.view-btn-solved {
            color: #67C23A;
          }
          
          &.view-btn-unsolved {
            color: #F56C6C;
          }
        }
      }
    }
  }
  
  .question-detail-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    
    .question-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .question-title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }
    }
    
    .question-detail-item {
      .detail-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .detail-value {
        padding: 12px;
        background-color: #f9f9f9;
        border-radius: 4px;
        word-break: break-word;
        line-height: 1.6;
        
        &.detail-id {
          font-family: monospace;
          color: #606266;
        }
        
        &.no-data {
          color: #909399;
          font-style: italic;
        }
        
        &.question-content {
          :deep(img) {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 10px 0;
          }
          
          :deep(p) {
            margin: 0 0 16px 0;
          }
          
          :deep(a) {
            color: #409EFF;
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
            }
          }
          
          :deep(code) {
            font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(27, 31, 35, 0.05);
            border-radius: 3px;
          }
          
          :deep(pre) {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f6f8fa;
            border-radius: 6px;
            margin-top: 0;
            margin-bottom: 16px;
          }
        }
        
        .recall-chunk {
          padding: 10px;
          margin-bottom: 10px;
          background-color: #fff;
          border: 1px solid #EBEEF5;
          border-radius: 4px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .chunk-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            .chunk-title {
              font-weight: 500;
              color: #303133;
            }
            
            .chunk-source {
              font-size: 12px;
              padding: 2px 6px;
              border-radius: 10px;
              background-color: #F2F6FC;
              
              &.source-type-0 {
                color: #67C23A;
                background-color: #F0F9EB;
              }
              
              &.source-type-1 {
                color: #409EFF;
                background-color: #F2F6FC;
              }
              
              &.source-type-2 {
                color: #E6A23C;
                background-color: #FDF6EC;
              }
              
              &.source-type-3 {
                color: #F56C6C;
                background-color: #FEF0F0;
              }
              
              &.source-type-4 {
                color: #909399;
                background-color: #F4F4F5;
              }
            }
          }
          
          .chunk-content {
            color: #606266;
            font-size: 13px;
            line-height: 1.5;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
          }
        }
      }
    }
  }
  
  .loading-container {
    padding: 20px;
  }
}
</style>