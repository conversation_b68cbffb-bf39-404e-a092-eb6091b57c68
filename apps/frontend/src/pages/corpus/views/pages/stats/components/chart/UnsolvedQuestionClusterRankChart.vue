<template>
  <div class="unsolved-question-cluster-rank-chart">
    <div class="chart-header">
      <div class="title-container">
        <div class="chart-title">未解决问题聚类排名</div>
        <el-tooltip
          content="展示未解决问题聚类按照包含未解决问题数量从高到低排序，点击可查看聚类详情。"
          placement="top"
          effect="light"
        >
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="chart-subtitle">按照每种类型的未解决问题数量从高到低排序</div>
    </div>
    <div ref="chartRef" class="chart-container"></div>
    
    <!-- 聚类详情弹窗 -->
    <el-dialog
      v-model="clusterDialogVisible"
      :title="selectedCluster?.questionPattern || '未解决问题聚类详情'"
      width="60%"
      :before-close="handleClusterDialogClose"
    >
      <template v-if="selectedCluster">
        <div class="cluster-detail-header">
          <div class="cluster-questions-count">
            <span>共 {{ selectedCluster.unsolvedQuestions.length }} 个未解决问题</span>
            <span class="total-count">(总问题数: {{ selectedCluster.questionList.length }})</span>
          </div>
        </div>
        <el-divider />
        <div class="cluster-questions-list">
          <div 
            v-for="(questionItem, index) in selectedCluster.unsolvedQuestions" 
            :key="index"
            class="question-item unsolved"
            @click="openQuestionDetail(questionItem)"
          >
            <div class="question-index">{{ index + 1 }}.</div>
            <div class="question-content">
              <div class="question-text">{{ questionItem.question }}</div>
              <div class="question-meta">
                <span class="question-time">{{ formatDateTime(questionItem.questionTime) }}</span>
                <span class="question-id">ID: {{ questionItem.questionMessageId }}</span>
              </div>
            </div>
            <div class="question-actions">
              <el-tag
                type="danger"
                size="small"
                class="status-tag"
              >
                未解决
              </el-tag>
              <el-button 
                type="text" 
                class="view-btn view-btn-unsolved"
                @click.stop="openQuestionDetail(questionItem)"
              >查看</el-button>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
    
    <!-- 问题详情弹窗 -->
    <el-dialog
      v-model="questionDialogVisible"
      title="未解决问题详情"
      width="50%"
      :before-close="handleQuestionDialogClose"
    >
      <div v-if="selectedQuestionData" class="question-detail-container">
        <div class="question-detail-header">
          <div class="question-title">问题详情</div>
          <el-button 
            type="primary" 
            size="small" 
            @click="openConversationDetail" 
            v-if="selectedQuestionData.questionConversationId"
          >
            查看会话详情
          </el-button>
        </div>
        <div class="question-detail-item">
          <div class="detail-label">问题内容</div>
          <div class="detail-value question-content" v-html="renderedQuestionContent"></div>
        </div>
        <div class="question-detail-item">
          <div class="detail-label">
            消息ID
            <el-button 
              type="primary" 
              size="small" 
              link 
              @click="manuallySearchRecallInfo"
              style="margin-left: 10px;"
            >
              手动搜索召回信息
            </el-button>
          </div>
          <div class="detail-value id">{{ selectedQuestionData.questionMessageId }}</div>
        </div>
        <div class="question-detail-item">
          <div class="detail-label">提问时间</div>
          <div class="detail-value">{{ formatDateTime(selectedQuestionData.questionTime) }}</div>
        </div>
        <div class="question-detail-item">
          <div class="detail-label">所属会话ID</div>
          <div class="detail-value id">{{ selectedQuestionData.questionConversationId }}</div>
        </div>
        
        <!-- 召回语料信息 -->
        <template v-if="displayRecallInfo">
          <div class="question-detail-item">
            <div class="detail-label">召回语料信息</div>
            <div class="detail-value" v-if="displayRecallInfo.recalledCorpusChunkInfoList.length > 0">
              <div v-for="(chunk, index) in displayRecallInfo.recalledCorpusChunkInfoList" :key="chunk.corpusId" class="recall-chunk">
                <div class="chunk-header">
                  <div class="chunk-title">{{ index + 1 }}. {{ chunk.title || '无标题' }}</div>
                  <div class="chunk-source" :class="getSourceTypeClass(chunk.source)">
                    {{ getSourceTypeText(chunk.source) }}
                  </div>
                </div>
                <div class="chunk-content">{{ chunk.content }}</div>
              </div>
            </div>
            <div class="detail-value no-data" v-else>
              未找到召回语料
            </div>
          </div>
        </template>
        <div class="question-detail-item" v-else>
          <div class="detail-label">召回语料信息</div>
          <div class="detail-value no-data">
            无可用召回信息
          </div>
        </div>
        
        <!-- 调试信息区域 -->
        <div v-if="debugInfo" class="question-detail-item debug-info">
          <div class="detail-label">调试信息</div>
          <div class="detail-value">
            <pre>{{ debugInfo }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 会话详情弹窗 -->
    <el-dialog
      v-model="conversationDialogVisible"
      :title="`会话详情: ${selectedQuestionData?.questionConversationId || ''}`"
      width="70%"
      @opened="handleConversationDialogOpened"
    >
      <ConversationDetailPanel
        :messages="conversationMessages"
        :loading="conversationLoading"
        :error="conversationLoadError"
        :current-message-id="selectedQuestionData?.questionMessageId"
        @view-recall-info="viewMessageRecallInfo"
      />
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="conversationDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps, computed } from 'vue';
import * as echarts from 'echarts';
import { QuestionFilled } from '@element-plus/icons-vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import httpRequest from '../../../../../../../utils/httpRequest';
import { API_PATHS } from '../../../../../request/api';
import ConversationDetailPanel from '../detail/ConversationDetailPanel.vue';

// 配置marked选项，确保图片渲染正常
marked.setOptions({
  breaks: true,        // 将回车转换为<br>
  gfm: true            // 使用GitHub风格Markdown
});

// 问题项接口定义
interface QuestionItem {
  question: string; 
  questionMessageId: string; 
  questionConversationId: string;
  questionTime: number;
  solved: boolean;
}

// 问题聚类接口定义
interface QuestionCluster {
  questionPattern: string;
  questionList: QuestionItem[];
}

// 未解决问题聚类接口
interface UnsolvedQuestionCluster extends QuestionCluster {
  unsolvedQuestions: QuestionItem[];
}

interface RecalledCorpusChunkInfo {
  corpusId: string;
  parentId: string;
  source: number;
  title: string;
  content: string;
}

interface QuestionCorpusRecallDetailItem {
  questionContent: string;
  questionMessageId: string;
  questionConversationId: string;
  recalledCorpusChunkInfoList: RecalledCorpusChunkInfo[];
}

// 添加Friday会话消息DTO接口
interface FridayConversationMessageDTO {
  messageId: string;
  conversationId: string;
  userId: string;
  userType: string;
  role: string;
  generateType: string;
  message: string;
  status: string;
  addTime: string | number;
  parentMessageId: string;
  appId: string;
  conversationName: string;
}

const props = defineProps<{
  questionClusters: QuestionCluster[];
  questionAndRecallInfos?: QuestionCorpusRecallDetailItem[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 计算包含未解决问题的聚类
const unsolvedClusters = computed<UnsolvedQuestionCluster[]>(() => {
  return props.questionClusters
    .map(cluster => {
      const unsolvedQuestions = cluster.questionList.filter(q => !q.solved);
      return {
        ...cluster,
        unsolvedQuestions
      };
    })
    .filter(cluster => cluster.unsolvedQuestions.length > 0)
    .sort((a, b) => b.unsolvedQuestions.length - a.unsolvedQuestions.length); // 按未解决问题数量降序排列
});

// 聚类详情弹窗相关
const clusterDialogVisible = ref(false);
const selectedCluster = ref<UnsolvedQuestionCluster | null>(null);

// 问题详情弹窗相关
const questionDialogVisible = ref(false);
const selectedQuestionData = ref<QuestionItem | null>(null);
const displayRecallInfo = ref<QuestionCorpusRecallDetailItem | null>(null);
const debugInfo = ref<string | null>(null);

// 会话详情弹窗相关
const conversationDialogVisible = ref(false);
const conversationMessages = ref<FridayConversationMessageDTO[]>([]);
const conversationLoading = ref(false);
const conversationLoadError = ref<string | null>(null);

// 渲染问题内容（支持Markdown）
const renderedQuestionContent = computed(() => {
  if (!selectedQuestionData.value) return '';
  const markdown = selectedQuestionData.value.question;
  const html = DOMPurify.sanitize(marked.parse(markdown));
  return html;
});

// 格式化日期时间
const formatDateTime = (timestamp: number) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  // 初始化ECharts实例
  chart = echarts.init(chartRef.value);
  
  // 更新图表数据
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!chart) return;
  
  // 使用未解决问题聚类数据
  const chartData = unsolvedClusters.value.slice(0, 10); // 取前10个聚类
  
  // 如果没有数据，显示空状态
  if (chartData.length === 0) {
    chart.setOption({
      title: {
        text: '暂无未解决问题聚类数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 16,
          fontWeight: 'normal'
        }
      }
    });
    return;
  }
  
  // 准备图表数据
  const categories = chartData.map(item => item.questionPattern);
  const data = chartData.map(item => item.unsolvedQuestions.length);
  
  // 计算最大值，用于设置Y轴范围
  const maxValue = Math.max(...data);
  const yAxisMax = Math.ceil(maxValue * 1.2); // 最大值的1.2倍，确保有足够空间
  
  // 设置图表选项
  chart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const data = params[0];
        return `
          <div style="margin: 0px 0 0; line-height: 1;">
            <div style="font-weight: bold; margin-bottom: 5px;">${data.name}</div>
            <div>未解决问题数: <span style="color:#f56c6c;font-weight:bold">${data.value}</span></div>
          </div>
        `;
      }
    },
    grid: {
      left: '8%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        interval: 0,
        rotate: 45,
        formatter: function(value: string) {
          // 如果文本太长，截断并加上省略号
          if (value.length > 12) {
            return value.substring(0, 12) + '...';
          }
          return value;
        },
        textStyle: {
          fontSize: 12
        },
        margin: 14
      }
    },
    yAxis: {
      type: 'value',
      name: '未解决问题数量',
      nameTextStyle: {
        padding: [0, 0, 0, 40]
      },
      max: yAxisMax
    },
    series: [
      {
        name: '未解决问题数量',
        type: 'bar',
        barWidth: '60%',
        data: data,
        itemStyle: {
          color: '#f56c6c' // 使用红色表示未解决问题
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }
    ]
  });
};

// 处理聚类点击事件
const handleClusterClick = (params: any) => {
  const index = params.dataIndex;
  if (index >= 0 && index < unsolvedClusters.value.length) {
    selectedCluster.value = unsolvedClusters.value[index];
    clusterDialogVisible.value = true;
  }
};

// 关闭聚类详情弹窗
const handleClusterDialogClose = () => {
  clusterDialogVisible.value = false;
  selectedCluster.value = null;
};

// 打开问题详情
const openQuestionDetail = (question: QuestionItem) => {
  selectedQuestionData.value = question;
  
  // 查找问题的召回信息
  if (props.questionAndRecallInfos) {
    const recallInfo = props.questionAndRecallInfos.find(
      item => item.questionMessageId === question.questionMessageId
    );
    displayRecallInfo.value = recallInfo || null;
  } else {
    displayRecallInfo.value = null;
  }
  
  questionDialogVisible.value = true;
  
  // 如果聚类详情弹窗已打开，则关闭它
  clusterDialogVisible.value = false;
};

// 关闭问题详情弹窗
const handleQuestionDialogClose = () => {
  questionDialogVisible.value = false;
  selectedQuestionData.value = null;
  displayRecallInfo.value = null;
  debugInfo.value = null;
};

// 查看消息的召回信息
const viewMessageRecallInfo = async (message: FridayConversationMessageDTO) => {
  debugInfo.value = '正在搜索消息召回信息...';
  
  try {
    const response = await httpRequest.rawRequestPostAsJson(
      API_PATHS.QUERY_SIMILAR_CONTENT_WITH_SCORE,
      { messageId: message.messageId }
    );
    
    if (response && response.code === 0 && response.data) {
      displayRecallInfo.value = response.data;
      debugInfo.value = '搜索完成';
      
      // 关闭会话详情弹窗，打开问题详情弹窗
      conversationDialogVisible.value = false;
      questionDialogVisible.value = true;
    } else {
      debugInfo.value = `搜索失败: ${response?.message || '未知错误'}`;
    }
  } catch (error: any) {
    debugInfo.value = `搜索出错: ${error.message || '未知错误'}`;
  }
};

// 手动搜索召回信息
const manuallySearchRecallInfo = async () => {
  if (!selectedQuestionData.value) return;
  
  debugInfo.value = '正在搜索召回信息...';
  
  try {
    const response = await httpRequest.rawRequestPostAsJson(
      API_PATHS.QUERY_SIMILAR_CONTENT_WITH_SCORE,
      {
        messageId: selectedQuestionData.value.questionMessageId
      }
    );
    
    if (response && response.code === 0 && response.data) {
      displayRecallInfo.value = response.data;
      debugInfo.value = '搜索完成';
    } else {
      debugInfo.value = `搜索失败: ${response?.message || '未知错误'}`;
    }
  } catch (error: any) {
    debugInfo.value = `搜索出错: ${error.message || '未知错误'}`;
  }
};

// 打开会话详情
const openConversationDetail = async () => {
  if (!selectedQuestionData.value?.questionConversationId) return;
  
  conversationDialogVisible.value = true;
  conversationLoading.value = true;
  conversationMessages.value = [];
  conversationLoadError.value = null;
};

// 会话详情弹窗打开后加载会话消息
const handleConversationDialogOpened = async () => {
  if (!selectedQuestionData.value?.questionConversationId) {
    conversationLoadError.value = '无效的会话ID';
    return;
  }
  
  const conversationId = selectedQuestionData.value.questionConversationId;
  conversationLoading.value = true;
  conversationLoadError.value = '';
  conversationMessages.value = [];
  
  try {
    // 使用API_PATHS常量获取会话消息，改为GET请求
    const response = await httpRequest.rawRequestGet(
      API_PATHS.QUERY_CONVERSATION_MESSAGES, 
      { conversationId }
    ) as unknown as { code: number; message: string; data: FridayConversationMessageDTO[] };
    
    if (response.code === 0 && Array.isArray(response.data)) {
      conversationMessages.value = response.data.sort((a: FridayConversationMessageDTO, b: FridayConversationMessageDTO) => {
        // 按时间排序
        return new Date(a.addTime).getTime() - new Date(b.addTime).getTime();
      });
    } else {
      conversationLoadError.value = response.message || '获取会话消息失败';
    }
  } catch (error: any) {
    console.error('加载会话消息出错:', error);
    conversationLoadError.value = '获取会话消息时发生错误，请稍后重试';
  } finally {
    conversationLoading.value = false;
  }
};

// 获取语料来源类型的文本
const getSourceTypeText = (source: number) => {
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

// 获取语料来源类型的样式类
const getSourceTypeClass = (source: number) => {
  switch (source) {
    case 0:
      return 'source-other';
    case 1:
      return 'source-service';
    case 2:
      return 'source-friday';
    default:
      return 'source-unknown';
  }
};

// 监听窗口大小变化，调整图表大小
window.addEventListener('resize', () => {
  chart?.resize();
});

// 监听聚类数据变化，更新图表
watch(() => props.questionClusters, updateChart, { deep: true });

// 在组件挂载后初始化图表
onMounted(() => {
  initChart();
  
  // 添加图表点击事件
  if (chart) {
    chart.on('click', 'series', handleClusterClick);
  }
});
</script>

<style scoped lang="scss">
.unsolved-question-cluster-rank-chart {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
  
  .chart-header {
    margin-bottom: 16px;
    
    .title-container {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      
      .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .help-icon {
        margin-left: 8px;
        color: #909399;
        cursor: pointer;
      }
    }
    
    .chart-subtitle {
      font-size: 13px;
      color: #909399;
    }
  }
  
  .chart-container {
    height: 420px;
    width: 100%;
  }
}

.cluster-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .cluster-questions-count {
    font-size: 14px;
    color: #606266;
    
    .total-count {
      margin-left: 10px;
      color: #909399;
    }
  }
}

.cluster-questions-list {
  .question-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 8px;
    border-bottom: 1px solid #ebeef5;
    transition: background-color 0.3s;
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &.unsolved {
      border-left: 3px solid #f56c6c;
    }
    
    .question-index {
      width: 30px;
      color: #909399;
      font-weight: 500;
    }
    
    .question-content {
      flex: 1;
      
      .question-text {
        font-size: 14px;
        color: #303133;
        margin-bottom: 6px;
      }
      
      .question-meta {
        font-size: 12px;
        color: #909399;
        
        .question-time {
          margin-right: 12px;
        }
      }
    }
    
    .question-actions {
      display: flex;
      align-items: center;
      
      .status-tag {
        margin-right: 8px;
      }
      
      .view-btn {
        font-size: 13px;
        
        &.view-btn-unsolved {
          color: #f56c6c;
        }
      }
    }
  }
}

.question-detail-container {
  .question-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .question-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .question-detail-item {
    margin-bottom: 16px;
    
    .detail-label {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .detail-value {
      font-size: 14px;
      color: #303133;
      background-color: #f5f7fa;
      padding: 10px;
      border-radius: 4px;
      
      &.id {
        font-family: monospace;
      }
      
      &.question-content {
        white-space: pre-wrap;
      }
      
      &.no-data {
        color: #909399;
        font-style: italic;
      }
    }
    
    &.debug-info {
      margin-top: 24px;
      
      .detail-value {
        font-family: monospace;
        font-size: 12px;
        white-space: pre-wrap;
      }
    }
  }
  
  .recall-chunk {
    margin-bottom: 12px;
    padding: 12px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    
    .chunk-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .chunk-title {
        font-weight: 500;
        color: #303133;
      }
      
      .chunk-source {
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 2px;
        
        &.source-other {
          background-color: #f0f2f5;
          color: #909399;
        }
        
        &.source-service {
          background-color: #e1f3d8;
          color: #67c23a;
        }
        
        &.source-friday {
          background-color: #fdf6ec;
          color: #e6a23c;
        }
        
        &.source-unknown {
          background-color: #f0f2f5;
          color: #909399;
        }
      }
    }
    
    .chunk-content {
      font-size: 13px;
      color: #606266;
      white-space: pre-wrap;
    }
  }
}
</style> 