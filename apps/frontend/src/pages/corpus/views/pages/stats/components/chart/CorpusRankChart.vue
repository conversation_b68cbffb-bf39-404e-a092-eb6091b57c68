<template>
  <div class="corpus-rank-chart">
    <div class="chart-header">
      <div class="title-container">
        <div class="chart-title">语料召回排名 TOP 10</div>
        <el-tooltip
          content="展示被召回次数最多的前10个语料，按召回次数降序排列。点击柱状图可查看语料详情。"
          placement="top"
          effect="light"
        >
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="chart-subtitle">按语料被召回的次数排序</div>
    </div>
    <div ref="chartRef" class="chart-container"></div>
    
    <!-- 语料详情弹窗 -->
    <el-dialog
      v-model="corpusDialogVisible"
      title="语料详情"
      width="60%"
      :before-close="handleDialogClose"
    >
      <template v-if="selectedCorpus">
        <div class="corpus-info">
          <div class="info-item">
            <div class="info-label">语料ID</div>
            <div class="info-value corpus-id">{{ selectedCorpus.corpusId }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">标题</div>
            <div class="info-value">{{ selectedCorpus.title || '无标题' }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">召回次数</div>
            <div class="info-value count">{{ selectedCorpus.count }}</div>
          </div>
          <div class="info-item full-width">
            <div class="info-label">内容</div>
            <div class="info-value content">{{ selectedCorpus.content || '无内容' }}</div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps } from 'vue';
import * as echarts from 'echarts';
import { QuestionFilled } from '@element-plus/icons-vue';
import type { RecalledCorpusChunkInfo } from '../../../../../types';

const props = defineProps<{
  recallInfoByQuestion: Record<string, RecalledCorpusChunkInfo[]>;
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 弹窗相关
const corpusDialogVisible = ref(false);
const selectedCorpus = ref<{
  corpusId: string;
  title: string;
  content: string;
  count: number;
} | null>(null);

// 处理弹窗关闭
const handleDialogClose = () => {
  corpusDialogVisible.value = false;
};

// 打开语料详情弹窗
const openCorpusDetail = (corpus: {
  corpusId: string;
  title: string;
  content: string;
  count: number;
}) => {
  selectedCorpus.value = corpus;
  corpusDialogVisible.value = true;
};

// 处理数据，计算每个语料被召回的次数并排序
const processData = () => {
  const corpusRecallCounts: Record<string, {
    corpusId: string,
    title: string,
    content: string,
    count: number
  }> = {};
  
  // 遍历所有问题的召回语料
  Object.values(props.recallInfoByQuestion || {}).forEach(recalls => {
    recalls.forEach(corpus => {
      if (!corpusRecallCounts[corpus.corpusId]) {
        corpusRecallCounts[corpus.corpusId] = {
          corpusId: corpus.corpusId,
          title: corpus.title,
          content: corpus.content,
          count: 0
        };
      }
      corpusRecallCounts[corpus.corpusId].count++;
    });
  });
  
  // 转换为数组并按照降序排列
  const corpusRanks = Object.values(corpusRecallCounts)
    .sort((a, b) => b.count - a.count) // 确保按照召回次数降序排列
    .slice(0, 10);
  
  return corpusRanks;
};

// 格式化语料标题，截断过长的标题
const formatTitle = (title: string): string => {
  const maxLength = 20;
  if (title.length <= maxLength) {
    return title;
  }
  return title.substring(0, maxLength) + '...';
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  updateChart();
  
  // 注册图表点击事件
  chart.on('click', (params) => {
    const dataIndex = params.dataIndex;
    const corpusRanks = processData();
    // 反转索引，与显示顺序保持一致
    const adjustedIndex = corpusRanks.length - 1 - dataIndex;
    
    if (adjustedIndex >= 0 && adjustedIndex < corpusRanks.length) {
      openCorpusDetail(corpusRanks[adjustedIndex]);
    }
  });
  
  window.addEventListener('resize', () => {
    chart?.resize();
  });
};

// 更新图表数据
const updateChart = () => {
  if (!chart) return;
  
  const corpusRanks = processData();
  
  // 如果没有数据，显示无数据状态
  if (corpusRanks.length === 0) {
    chart.setOption({
      title: {
        text: '暂无语料数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#909399',
          fontSize: 14
        }
      }
    });
    return;
  }
  
  // 获取语料标题和对应的召回次数数组，确保按照降序排列
  const titles = corpusRanks.map(item => formatTitle(item.title));
  const counts = corpusRanks.map(item => item.count);
  
  const option = {
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        return `<div style="font-weight: bold">${params.name}</div>召回次数: ${params.value}<div style="margin-top: 5px; font-size: 12px; color: #909399;">点击图表可查看语料详情</div>`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '召回次数',
      nameLocation: 'end',
      axisPointer: {
        show: false
      }
    },
    yAxis: {
      type: 'category',
      data: titles.slice().reverse(), // 反转数组，确保按召回次数从大到小排列显示
      axisLabel: {
        interval: 0,
        formatter: (value: string) => value,
        margin: 8
      },
      axisPointer: {
        show: false
      }
    },
    series: [
      {
        name: '召回次数',
        type: 'bar',
        data: counts.slice().reverse(), // 与Y轴数据保持一致，同样反转
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      }
    ]
  };
  
  chart.setOption(option);
};

// 监听数据变化
watch(() => props.recallInfoByQuestion, updateChart, { deep: true });

onMounted(() => {
  initChart();
});
</script>

<style scoped lang="scss">
.corpus-rank-chart {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 20px;
  
  .chart-header {
    margin-bottom: 16px;
    
    .title-container {
      display: flex;
      align-items: center;
      
      .chart-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
      
      .help-icon {
        margin-left: 4px;
        color: #909399;
        font-size: 14px;
        cursor: help;
      }
    }
    
    .chart-subtitle {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
  
  .chart-container {
    height: 300px;
  }
  
  .corpus-info {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    
    .info-item {
      min-width: calc(50% - 8px);
      
      &.full-width {
        width: 100%;
      }
      
      .info-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .info-value {
        padding: 12px;
        background-color: #f9f9f9;
        border-radius: 4px;
        word-break: break-word;
        
        &.corpus-id {
          font-family: monospace;
          color: #606266;
        }
        
        &.count {
          color: #188df0;
          font-weight: bold;
        }
        
        &.content {
          max-height: 300px;
          overflow-y: auto;
          white-space: pre-wrap;
          line-height: 1.6;
        }
      }
    }
  }
}
</style> 