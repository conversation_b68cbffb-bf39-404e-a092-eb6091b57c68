<template>
  <div class="recall-details">
    <template v-if="showByQuestion">
      <!-- 按问题分组显示 -->
      <el-collapse accordion>
        <el-collapse-item 
          v-for="(recalls, question) in data.recallInfoByQuestion" 
          :key="question"
          :title="formatQuestionTitle(question, recalls.length)"
        >
          <template #title>
            <div class="question-title">
              <el-tooltip
                effect="dark"
                :content="question"
                placement="top"
                :hide-after="0"
              >
                <div class="question-text">{{ formatQuestionText(question) }}</div>
              </el-tooltip>
              <el-tag size="small" class="recall-count" type="success">{{ recalls.length }}条召回</el-tag>
            </div>
          </template>
          
          <el-table
            :data="recalls"
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column label="语料ID" prop="corpusId" width="180" />
            <el-table-column label="标题" prop="title" />
            <el-table-column label="来源" prop="source" width="120">
              <template #default="{ row }">
                <el-tag :type="getSourceTagType(row.source)">
                  {{ getSourceText(row.source) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="内容">
              <template #default="{ row }">
                <el-tooltip
                  effect="dark"
                  :content="row.content"
                  placement="top"
                  :hide-after="0"
                >
                  <div class="content-preview">{{ row.content }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </template>
    
    <template v-else>
      <!-- 列表显示 -->
      <el-table
        :data="pagedChunks"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column label="语料ID" prop="corpusId" width="180" />
        <el-table-column label="标题" prop="title" />
        <el-table-column label="来源" prop="source" width="120">
          <template #default="{ row }">
            <el-tag :type="getSourceTagType(row.source)">
              {{ getSourceText(row.source) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="内容">
          <template #default="{ row }">
            <el-tooltip
              effect="dark"
              :content="row.content"
              placement="top"
              :hide-after="0"
            >
              <div class="content-preview">{{ row.content }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container" v-if="totalPages > 1">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import type { RecalledCorpusChunkInfo } from '../../../../../types';

const props = defineProps<{
  data: {
    recallInfoByQuestion?: Record<string, RecalledCorpusChunkInfo[]>;
    chunks?: RecalledCorpusChunkInfo[];
  }
}>();

// 判断显示方式
const showByQuestion = computed(() => {
  return !!props.data.recallInfoByQuestion;
});

// 展平的所有语料块数据
const allChunks = computed<RecalledCorpusChunkInfo[]>(() => {
  if (props.data.chunks) {
    return props.data.chunks;
  } else if (props.data.recallInfoByQuestion) {
    const result: RecalledCorpusChunkInfo[] = [];
    Object.values(props.data.recallInfoByQuestion).forEach(chunks => {
      result.push(...chunks);
    });
    return result;
  }
  return [];
});

// 分页设置
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = computed(() => allChunks.value.length);
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

// 分页后的语料块
const pagedChunks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return allChunks.value.slice(start, end);
});

// 分页变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

// 获取语料来源文本描述
const getSourceText = (source: number): string => {
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

// 获取语料来源对应的标签类型
const getSourceTagType = (source: number): string => {
  switch (source) {
    case 0:
      return 'info';
    case 1:
      return 'success';
    case 2:
      return 'warning';
    default:
      return 'info';
  }
};

// 格式化问题文本，截断过长的问题
const formatQuestionText = (question: string): string => {
  // 截取问题的前60个字符，超出部分用...代替
  const maxLength = 60;
  if (question.length <= maxLength) {
    return question;
  }
  return question.substring(0, maxLength) + '...';
};

// 格式化问题标题（不再使用，使用模板代替）
const formatQuestionTitle = (question: string, recallCount: number): string => {
  return `问题: ${formatQuestionText(question)} (${recallCount}条召回)`;
};
</script>

<style scoped lang="scss">
.recall-details {
  .question-title {
    display: flex;
    align-items: center;
    
    .question-text {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 10px;
    }
    
    .recall-count {
      flex-shrink: 0;
      margin-left: 8px;
    }
  }
  
  .content-preview {
    max-height: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 