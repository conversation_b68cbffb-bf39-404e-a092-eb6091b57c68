<template>
  <div class="unsolved-question-cluster-list">
    <!-- 总览信息 -->
    <div class="overview-section">
      <div class="overview-stats">
        <div class="stat-item">
          <span class="stat-label">未解决问题聚类数量：</span>
          <span class="stat-value">{{ unsolvedClusters.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">未解决问题总数：</span>
          <span class="stat-value">{{ totalUnsolvedCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">聚类中未解决问题：</span>
          <span class="stat-value clustered">{{ clusteredUnsolvedCount }}</span>
        </div>
        <div class="stat-item" v-if="unclusteredUnsolvedCount > 0">
          <span class="stat-label">未分类未解决问题：</span>
          <span class="stat-value unclustered">{{ unclusteredUnsolvedCount }}</span>
        </div>
      </div>
    </div>

    <el-divider />

    <!-- 聚类列表 -->
    <div class="cluster-list">
      <div
        v-for="(cluster, index) in unsolvedClusters"
        :key="index"
        class="cluster-item"
      >
        <!-- 聚类头部 -->
        <div class="cluster-header" @click="toggleCluster(index)">
          <div class="cluster-info">
            <div class="cluster-title">
              <el-icon class="expand-icon" :class="{ 'expanded': expandedClusters.has(index) }">
                <ArrowRight />
              </el-icon>
              <span class="cluster-pattern">{{ cluster.questionPattern }}</span>
            </div>
            <div class="cluster-meta">
              <el-tag type="danger" size="small">
                {{ cluster.unsolvedQuestions.length }} 个未解决问题
              </el-tag>
              <span class="total-questions">
                （共 {{ cluster.questionList.length }} 个问题）
              </span>
            </div>
          </div>
        </div>

        <!-- 展开的问题列表 -->
        <div v-if="expandedClusters.has(index)" class="cluster-content">
          <div class="questions-list">
            <div
              v-for="(question, qIndex) in cluster.unsolvedQuestions"
              :key="qIndex"
              class="question-item"
              @click="openQuestionDetail(question)"
            >
              <div class="question-index">{{ qIndex + 1 }}.</div>
              <div class="question-content">
                <div class="question-text">{{ question.question }}</div>
                <div class="question-meta">
                  <span class="question-time">{{ formatDateTime(question.questionTime) }}</span>
                  <span class="question-id">ID: {{ question.questionMessageId }}</span>
                </div>
              </div>
              <div class="question-actions">
                <el-tag type="danger" size="small">未解决</el-tag>
                <el-button
                  type="text"
                  size="small"
                  @click.stop="openQuestionDetail(question)"
                >
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-if="unsolvedClusters.length === 0" class="no-data">
      <el-empty description="暂无未解决问题聚类数据" />
    </div>

    <!-- 问题详情弹窗 -->
    <el-dialog
      v-model="questionDialogVisible"
      :title="'问题详情'"
      width="60%"
      :before-close="handleQuestionDialogClose"
    >
      <template v-if="selectedQuestion">
        <div class="question-detail">
          <div class="question-detail-header">
            <div class="question-title">问题详情</div>
            <el-button
              type="primary"
              size="small"
              @click="openConversationDetail"
              v-if="selectedQuestion.questionConversationId"
            >
              查看会话详情
            </el-button>
          </div>
          <div class="question-detail-item">
            <div class="detail-label">问题内容</div>
            <div class="detail-value">{{ selectedQuestion.question }}</div>
          </div>
          <div class="question-detail-item">
            <div class="detail-label">提问时间</div>
            <div class="detail-value">{{ formatDateTime(selectedQuestion.questionTime) }}</div>
          </div>
          <div class="question-detail-item">
            <div class="detail-label">问题ID</div>
            <div class="detail-value">{{ selectedQuestion.questionMessageId }}</div>
          </div>
          <div class="question-detail-item">
            <div class="detail-label">会话ID</div>
            <div class="detail-value">{{ selectedQuestion.questionConversationId }}</div>
          </div>
          <div class="question-detail-item">
            <div class="detail-label">解决状态</div>
            <div class="detail-value">
              <el-tag type="danger" size="small">未解决</el-tag>
            </div>
          </div>

          <!-- 召回语料信息 -->
          <template v-if="displayRecallInfo">
            <div class="question-detail-item">
              <div class="detail-label">召回语料信息</div>
              <div class="detail-value" v-if="displayRecallInfo.length > 0">
                <div v-for="(chunk, index) in displayRecallInfo" :key="chunk.corpusId" class="recall-chunk">
                  <div class="chunk-header">
                    <div class="chunk-title">{{ index + 1 }}. {{ chunk.title || '无标题' }}</div>
                    <div class="chunk-source" :class="getSourceTypeClass(chunk.source)">
                      {{ getSourceTypeText(chunk.source) }}
                    </div>
                  </div>
                  <div class="chunk-content">{{ chunk.content }}</div>
                </div>
              </div>
              <div class="detail-value no-data" v-else>
                未找到召回语料
              </div>
            </div>
          </template>
        </div>
      </template>
    </el-dialog>

    <!-- 会话详情弹窗 -->
    <el-dialog
      v-model="conversationDialogVisible"
      :title="`会话详情: ${selectedQuestion?.questionConversationId || ''}`"
      width="70%"
      @opened="handleConversationDialogOpened"
    >
      <ConversationDetailPanel
        :messages="conversationMessages"
        :loading="conversationLoading"
        :error="conversationLoadError"
        :current-message-id="selectedQuestion?.questionMessageId"
        @view-recall-info="viewMessageRecallInfo"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="conversationDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import { ArrowRight } from '@element-plus/icons-vue';
import type { RecalledCorpusChunkInfo } from '../../../../../types';
import ConversationDetailPanel from './ConversationDetailPanel.vue';
import httpRequest from '../../../../../../../utils/httpRequest';
import { API_PATHS } from '../../../../../request/api';

// 定义问题项类型
interface QuestionItem {
  question: string;
  questionTime: number;
  questionMessageId: string;
  questionConversationId: string;
  solved: boolean;
}

// 定义聚类类型
interface QuestionCluster {
  questionPattern: string;
  questionList: QuestionItem[];
}

// 扩展聚类类型，包含未解决问题
interface UnsolvedQuestionCluster extends QuestionCluster {
  unsolvedQuestions: QuestionItem[];
}

// Friday会话消息DTO接口
interface FridayConversationMessageDTO {
  messageId: string;
  conversationId: string;
  userId: string;
  userType: string;
  role: string;
  generateType: string;
  message: string;
  status: string;
  addTime: string | number;
  parentMessageId: string;
  appId: string;
  conversationName: string;
}

const props = defineProps<{
  data: {
    questionClusters: QuestionCluster[];
    questionList: QuestionItem[];
    recallInfoByQuestion: Record<string, RecalledCorpusChunkInfo[]>;
  }
}>();

// 计算包含未解决问题的聚类
const unsolvedClusters = computed<UnsolvedQuestionCluster[]>(() => {
  return props.data.questionClusters
    .map(cluster => {
      const unsolvedQuestions = cluster.questionList.filter(q => !q.solved);
      return {
        ...cluster,
        unsolvedQuestions
      };
    })
    .filter(cluster => cluster.unsolvedQuestions.length > 0)
    .sort((a, b) => b.unsolvedQuestions.length - a.unsolvedQuestions.length); // 按未解决问题数量降序排列
});

// 计算未解决问题总数（使用完整的问题列表）
const totalUnsolvedCount = computed(() => {
  return props.data.questionList.filter(q => !q.solved).length;
});

// 计算聚类中的未解决问题数量
const clusteredUnsolvedCount = computed(() => {
  return unsolvedClusters.value.reduce((total, cluster) => total + cluster.unsolvedQuestions.length, 0);
});

// 计算未分类的未解决问题数量
const unclusteredUnsolvedCount = computed(() => {
  return totalUnsolvedCount.value - clusteredUnsolvedCount.value;
});

// 展开状态管理
const expandedClusters = ref(new Set<number>());

// 弹窗状态
const questionDialogVisible = ref(false);
const conversationDialogVisible = ref(false);
const selectedQuestion = ref<QuestionItem | null>(null);

// 会话相关状态
const conversationLoading = ref(false);
const conversationLoadError = ref('');
const conversationMessages = ref<FridayConversationMessageDTO[]>([]);

// 计算当前选中问题的召回信息
const displayRecallInfo = computed(() => {
  if (!selectedQuestion.value) return null;
  return props.data.recallInfoByQuestion?.[selectedQuestion.value.question] || [];
});

// 切换聚类展开状态
const toggleCluster = (index: number) => {
  if (expandedClusters.value.has(index)) {
    expandedClusters.value.delete(index);
  } else {
    expandedClusters.value.add(index);
  }
};

// 打开问题详情
const openQuestionDetail = (question: QuestionItem) => {
  selectedQuestion.value = question;
  questionDialogVisible.value = true;
};

// 关闭弹窗
const handleQuestionDialogClose = () => {
  questionDialogVisible.value = false;
  selectedQuestion.value = null;
};

// 格式化时间
const formatDateTime = (timestamp: number) => {
  if (!timestamp) return '未知时间';
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 获取语料来源类型文本
const getSourceTypeText = (source: number) => {
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

// 获取语料来源类型样式类
const getSourceTypeClass = (source: number) => {
  switch (source) {
    case 0:
      return 'source-other';
    case 1:
      return 'source-service';
    case 2:
      return 'source-friday';
    default:
      return 'source-unknown';
  }
};

// 打开会话详情弹窗
const openConversationDetail = () => {
  if (!selectedQuestion.value?.questionConversationId) {
    console.error('无法打开会话详情：缺少会话ID');
    return;
  }
  conversationDialogVisible.value = true;
};

// 处理会话详情弹窗打开
const handleConversationDialogOpened = async () => {
  if (!selectedQuestion.value?.questionConversationId) {
    conversationLoadError.value = '无效的会话ID';
    return;
  }

  const conversationId = selectedQuestion.value.questionConversationId;
  conversationLoading.value = true;
  conversationLoadError.value = '';
  conversationMessages.value = [];

  try {
    // 使用API_PATHS常量获取会话消息
    const response = await httpRequest.rawRequestGet(
      API_PATHS.QUERY_CONVERSATION_MESSAGES,
      { conversationId }
    ) as unknown as { code: number; message: string; data: FridayConversationMessageDTO[] };

    if (response.code === 0 && Array.isArray(response.data)) {
      conversationMessages.value = response.data.sort((a: FridayConversationMessageDTO, b: FridayConversationMessageDTO) => {
        // 按时间排序
        return new Date(a.addTime).getTime() - new Date(b.addTime).getTime();
      });
    } else {
      conversationLoadError.value = response.message || '获取会话消息失败';
    }
  } catch (error) {
    console.error('加载会话消息出错:', error);
    conversationLoadError.value = '获取会话消息时发生错误，请稍后重试';
  } finally {
    conversationLoading.value = false;
  }
};

// 查看消息召回信息
const viewMessageRecallInfo = (message: FridayConversationMessageDTO) => {
  if (message.role === 'user') {
    // 设置问题数据
    const questionTime = message.addTime ? new Date(message.addTime).getTime() : Date.now();
    selectedQuestion.value = {
      question: message.message,
      questionMessageId: message.messageId,
      questionConversationId: message.conversationId,
      questionTime,
      solved: false
    };

    // 打开问题详情弹窗
    conversationDialogVisible.value = false;
    questionDialogVisible.value = true;
  }
};
</script>

<style scoped lang="scss">
.unsolved-question-cluster-list {
  .overview-section {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;

    .overview-stats {
      display: flex;
      gap: 24px;

      .stat-item {
        display: flex;
        align-items: center;

        .stat-label {
          color: #606266;
          font-size: 14px;
        }

        .stat-value {
          color: #E6A23C;
          font-weight: bold;
          font-size: 16px;
          margin-left: 4px;

          &.clustered {
            color: #409EFF;
          }

          &.unclustered {
            color: #F56C6C;
          }
        }
      }
    }
  }

  .cluster-list {
    .cluster-item {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      margin-bottom: 12px;
      overflow: hidden;

      .cluster-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background: #fafafa;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: #f0f0f0;
        }

        .cluster-info {
          flex: 1;

          .cluster-title {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .expand-icon {
              margin-right: 8px;
              transition: transform 0.3s;
              color: #909399;

              &.expanded {
                transform: rotate(90deg);
              }
            }

            .cluster-pattern {
              font-weight: 500;
              color: #303133;
              font-size: 15px;
            }
          }

          .cluster-meta {
            display: flex;
            align-items: center;
            gap: 8px;

            .total-questions {
              color: #909399;
              font-size: 13px;
            }
          }
        }


      }

      .cluster-content {
        border-top: 1px solid #ebeef5;

        .questions-list {
          .question-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #f5f7fa;
            cursor: pointer;
            transition: background-color 0.3s;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background: #f8f9fa;
            }

            .question-index {
              color: #909399;
              font-size: 14px;
              margin-right: 12px;
              min-width: 24px;
            }

            .question-content {
              flex: 1;

              .question-text {
                color: #303133;
                font-size: 14px;
                line-height: 1.5;
                margin-bottom: 4px;
              }

              .question-meta {
                display: flex;
                gap: 16px;

                .question-time,
                .question-id {
                  color: #909399;
                  font-size: 12px;
                }
              }
            }

            .question-actions {
              display: flex;
              align-items: center;
              gap: 8px;

              .el-button {
                color: #F56C6C;
              }
            }
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 40px 0;
  }
}

// 问题详情弹窗样式
.question-detail {
  .question-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .question-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
  }

  .question-detail-item {
    margin-bottom: 16px;

    .detail-label {
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .detail-value {
      color: #606266;
      font-size: 14px;
      line-height: 1.6;

      &.no-data {
        color: #909399;
        font-style: italic;
      }
    }

    .recall-chunk {
      border: 1px solid #ebeef5;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 8px;

      .chunk-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .chunk-title {
          font-weight: 500;
          color: #303133;
          font-size: 14px;
        }

        .chunk-source {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;

          &.source-other {
            background: #e1f3d8;
            color: #67c23a;
          }

          &.source-service {
            background: #ecf5ff;
            color: #409eff;
          }

          &.source-friday {
            background: #fdf6ec;
            color: #e6a23c;
          }

          &.source-unknown {
            background: #f4f4f5;
            color: #909399;
          }
        }
      }

      .chunk-content {
        color: #606266;
        font-size: 13px;
        line-height: 1.5;
        background: #fafafa;
        padding: 8px;
        border-radius: 4px;
      }
    }
  }
}
</style>
