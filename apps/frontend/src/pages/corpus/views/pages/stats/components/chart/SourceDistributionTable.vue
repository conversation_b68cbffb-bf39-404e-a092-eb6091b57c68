<template>
  <div class="source-distribution-table">
    <div class="chart-header">
      <div class="chart-title">语料来源分布表</div>
      <div class="chart-subtitle">各类来源的语料数量和百分比</div>
    </div>
    
    <el-table
      :data="distributionData"
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column 
        label="来源" 
        prop="sourceName" 
        width="200"
      >
        <template #default="{ row }">
          <el-tag :type="getSourceTagType(row.sourceType)">
            {{ row.sourceName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column 
        label="语料数量" 
        prop="count" 
        width="120"
      />
      <el-table-column 
        label="占比" 
        prop="percentage" 
        width="120"
      />
      <el-table-column label="比例">
        <template #default="{ row }">
          <el-progress 
            :percentage="row.percentageValue" 
            :color="getSourceColor(row.sourceType)"
            :show-text="false"
            :stroke-width="18"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import type { RecalledCorpusChunkInfo } from '../../../../../types';

const props = defineProps<{
  chunks: RecalledCorpusChunkInfo[];
}>();

// 计算各来源的分布数据，基于独立分片数量
const distributionData = computed(() => {
  // 使用Set来存储每个来源的独立分片ID，确保不重复计数
  const sourceUniqueChunks: Record<number, Set<string>> = {};
  
  props.chunks.forEach(chunk => {
    if (!sourceUniqueChunks[chunk.source]) {
      sourceUniqueChunks[chunk.source] = new Set();
    }
    sourceUniqueChunks[chunk.source].add(chunk.corpusId);
  });
  
  // 统计每个来源的独立分片数量
  const sourceGroups: Record<number, number> = {};
  Object.entries(sourceUniqueChunks).forEach(([source, uniqueIds]) => {
    sourceGroups[parseInt(source)] = uniqueIds.size;
  });
  
  // 计算总独立分片数量
  const total = Object.values(sourceGroups).reduce((sum, count) => sum + count, 0);
  
  // 转换为表格数据
  const result = Object.entries(sourceGroups).map(([sourceType, count]) => {
    const sourceTypeNumber = parseInt(sourceType);
    const percentage = total ? ((count / total) * 100).toFixed(2) + '%' : '0%';
    const percentageValue = total ? Math.round((count / total) * 100) : 0;
    
    return {
      sourceType: sourceTypeNumber,
      sourceName: getSourceText(sourceTypeNumber),
      count,
      percentage,
      percentageValue
    };
  });
  
  // 按数量降序排序
  return result.sort((a, b) => b.count - a.count);
});

// 获取语料来源文本描述
const getSourceText = (source: number): string => {
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

// 获取语料来源对应的标签类型
const getSourceTagType = (source: number): string => {
  switch (source) {
    case 0:
      return 'info';
    case 1:
      return 'success';
    case 2:
      return 'warning';
    default:
      return 'info';
  }
};

// 获取语料来源对应的颜色
const getSourceColor = (source: number): string => {
  switch (source) {
    case 0:
      return '#909399'; // 灰色
    case 1:
      return '#67C23A'; // 绿色
    case 2:
      return '#E6A23C'; // 橙色
    default:
      return '#909399';
  }
};
</script>

<style scoped lang="scss">
.source-distribution-table {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 20px;
  
  .chart-header {
    margin-bottom: 16px;
    
    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
    
    .chart-subtitle {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
}
</style> 