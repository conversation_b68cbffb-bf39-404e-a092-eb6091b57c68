# 创建新的会话详情面板组件
<template>
  <div class="conversation-list">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <el-alert
      v-if="error"
      type="error"
      :title="error"
      :closable="false"
      show-icon
    />
    
    <div v-else-if="messages.length > 0" class="conversation-messages">
      <div class="messages-header">
        <div class="messages-info">
          <p>会话名称: {{ messages[0]?.conversationName || '未命名会话' }}</p>
          <p>消息数量: {{ messages.length }}</p>
        </div>
      </div>
      
      <div class="message-list">
        <div 
          v-for="(message) in sortedMessages" 
          :key="message.messageId"
          class="message-item"
          :class="{ 
            'user-message': message.role === 'user', 
            'ai-message': message.role === 'assistan',
            'current-message': message.messageId === currentMessageId
          }"
        >
          <div class="message-header">
            <span class="message-role">{{ getRoleName(message.role, message.userId) }}</span>
            <span class="message-time">{{ formatTime(message.addTime) }}</span>
          </div>
          <div class="message-content">{{ message.message }}</div>
          <div class="message-footer">
            <span class="message-id">ID: {{ message.messageId }}</span>
            <template v-if="message.role === 'user'">
              <el-button 
                type="primary" 
                link 
                size="small"
                @click="handleViewRecallInfo(message)"
              >
                查看召回信息
              </el-button>
            </template>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="conversation-detail">
      <el-alert
        type="warning"
        title="无法获取完整会话记录"
        :closable="false"
        show-icon
        style="margin-bottom: 16px;"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FridayConversationMessageDTO } from '../../../../../types/conversation';
import { computed } from 'vue';

const props = defineProps<{
  messages: FridayConversationMessageDTO[];
  loading: boolean;
  error: string;
  currentMessageId?: string;
}>();

const emit = defineEmits<{
  (e: 'viewRecallInfo', message: FridayConversationMessageDTO): void;
}>();

// 对消息进行排序
const sortedMessages = computed(() => {
  const messageMap = new Map<string, FridayConversationMessageDTO>();
  const rootMessages: FridayConversationMessageDTO[] = [];
  
  // 首先构建消息映射
  props.messages.forEach(msg => {
    messageMap.set(msg.messageId, msg);
  });
  
  // 找出所有根消息（没有父消息的消息）
  props.messages.forEach(msg => {
    if (!msg.parentMessageId || !messageMap.has(msg.parentMessageId)) {
      rootMessages.push(msg);
    }
  });
  
  // 按时间排序根消息
  rootMessages.sort((a, b) => {
    const timeA = new Date(a.addTime || 0).getTime();
    const timeB = new Date(b.addTime || 0).getTime();
    return timeA - timeB;
  });
  
  // 递归函数：获取消息及其所有子消息
  const getMessageWithChildren = (message: FridayConversationMessageDTO): FridayConversationMessageDTO[] => {
    const result = [message];
    
    // 找出所有直接子消息
    const children = props.messages.filter(msg => msg.parentMessageId === message.messageId);
    
    // 按时间排序子消息
    children.sort((a, b) => {
      const timeA = new Date(a.addTime || 0).getTime();
      const timeB = new Date(b.addTime || 0).getTime();
      return timeA - timeB;
    });
    
    // 递归处理每个子消息
    children.forEach(child => {
      result.push(...getMessageWithChildren(child));
    });
    
    return result;
  };
  
  // 构建最终的排序消息列表
  const sortedList: FridayConversationMessageDTO[] = [];
  rootMessages.forEach(rootMessage => {
    sortedList.push(...getMessageWithChildren(rootMessage));
  });
  
  return sortedList;
});

// 获取角色名称
const getRoleName = (role: string, userId?: string): string => {
  switch (role) {
    case 'user':
      return `用户${userId ? ` ${userId}` : ''}`;
    case 'assistan':
      return 'AI助手';
    default:
      return '未知角色';
  }
};

// 格式化时间
const formatTime = (timeStr: string | number | undefined): string => {
  if (!timeStr) return '无时间信息';
  
  try {
    const date = new Date(timeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('时间格式化错误:', error);
    return String(timeStr);
  }
};

// 处理查看召回信息
const handleViewRecallInfo = (message: FridayConversationMessageDTO) => {
  emit('viewRecallInfo', message);
};
</script>

<style scoped lang="scss">
.loading-container {
  padding: 20px;
}

.conversation-list {
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .loading-container {
    padding: 20px 0;
  }
  
  .conversation-messages {
    .messages-header {
      margin-bottom: 16px;
      
      .messages-info {
        text-align: right;
        color: #909399;
        font-size: 12px;
        
        p {
          margin: 0 0 4px 0;
        }
      }
    }
    
    .message-list {
      .message-item {
        padding: 12px;
        margin-bottom: 16px;
        border-radius: 8px;
        
        &.user-message {
          background-color: #f2f6fc;
          border-left: 4px solid #409EFF;
        }
        
        &.ai-message {
          background-color: #f5f7fa;
          border-left: 4px solid #67C23A;
        }
        
        .message-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          .message-role {
            font-weight: 500;
            color: #303133;
          }
          
          .message-time {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .message-content {
          font-size: 14px;
          color: #606266;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-word;
          margin-bottom: 8px;
        }
        
        .message-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #909399;
          
          .message-id {
            font-family: monospace;
          }
        }
      }
    }
  }
  
  .conversation-detail {
    h3 {
      font-size: 16px;
      margin-bottom: 16px;
      color: #303133;
    }
  }
  
  .question-detail {
    .question-content {
      margin-bottom: 20px;
      
      h3 {
        font-size: 16px;
        margin-bottom: 10px;
        color: #303133;
      }
      
      p {
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
        word-break: break-all;
        background-color: #f9f9f9;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 16px;
      }
      
      .question-info {
        color: #909399;
        font-size: 13px;
        
        p {
          background-color: transparent;
          padding: 0;
          margin-bottom: 8px;
        }
      }
    }
    
    .recall-info {
      h3 {
        font-size: 16px;
        margin-bottom: 16px;
        color: #303133;
      }
      
      .content-preview {
        max-height: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }
  }
}

</style> 