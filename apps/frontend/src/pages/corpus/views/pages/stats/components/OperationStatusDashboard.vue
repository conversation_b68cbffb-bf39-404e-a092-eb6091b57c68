<template>
  <div class="operation-status-dashboard">
    <el-row :gutter="20">
      <!-- 顶部卡片统计 -->
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>小助手被@次数</span>
              <el-button type="text" @click="showBotMessagesDialog = true">查看详情</el-button>
            </div>
          </template>
          <div class="card-value">{{ botMessagesCount }}</div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>语料生成次数</span>
            </div>
          </template>
          <div class="card-value">{{ taskGenerateCount }}</div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>接入的值班组数量</span>
              <el-button type="text" @click="showRgInfoDialog = true">查看详情</el-button>
            </div>
          </template>
          <div class="card-value">{{ activeRgCount }}</div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>系统管理的语料总数</span>
            </div>
          </template>
          <div class="card-value">{{ totalCorpusCount }}</div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="chart-row">
      <!-- 语料生成趋势图 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>语料生成趋势</span>
            </div>
          </template>
          <div class="chart-container" ref="taskTrendChartRef"></div>
        </el-card>
      </el-col>
      
      <!-- 语料生成触发来源 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>语料生成触发来源</span>
            </div>
          </template>
          <div class="chart-container" ref="platformSourceChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="chart-row">
      <!-- 语料生成使用频率部门排名 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>语料生成使用频率部门排名</span>
            </div>
          </template>
          <div class="chart-container" ref="orgRankChartRef"></div>
        </el-card>
      </el-col>
      
      <!-- 值班组语料数量排名 -->
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>值班组语料数量排名</span>
            </div>
          </template>
          <div class="chart-container" ref="rgCorpusRankChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="chart-row">
      <!-- 接入的Friday空间 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>接入的Friday空间 ({{ fridaySpaceCount }})</span>
              <el-button type="text" @click="showFridaySpaceDialog = true">查看详情</el-button>
            </div>
          </template>
          <el-table :data="fridaySpaceList" style="width: 100%" max-height="300">
            <el-table-column prop="spaceName" label="空间名称" min-width="120" />
            <el-table-column prop="spaceId" label="空间ID" width="100" />
            <el-table-column prop="ctime" label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.ctime) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <!-- 未保存的语料任务 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>未保存的语料任务 ({{ unsavedTaskCount }})</span>
              <el-button type="text" @click="showUnsavedTasksDialog = true">查看详情</el-button>
            </div>
          </template>
          <el-table :data="unsavedTaskList.slice(0, 5)" style="width: 100%" max-height="300">
            <el-table-column prop="taskId" label="任务ID" width="100" />
            <el-table-column prop="ticketId" label="工单ID" width="100" />
            <el-table-column prop="creatorUserName" label="创建人" width="100" />
            <el-table-column prop="createTime" label="创建时间" width="180">
              <template #default="scope">
                {{ formatDateTime(new Date(scope.row.createTime).getTime()) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 小助手消息详情对话框 -->
    <el-dialog
      v-model="showBotMessagesDialog"
      title="小助手被@消息详情"
      width="80%"
    >
      <el-table :data="botMessagesList" style="width: 100%" max-height="500">
        <el-table-column prop="fromName" label="发送人" width="120" />
        <el-table-column prop="userOrgName" label="部门" width="150" />
        <el-table-column prop="message" label="消息内容" min-width="300" show-overflow-tooltip />
        <el-table-column prop="cts" label="发送时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.cts) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    
    <!-- 值班组详情对话框 -->
    <el-dialog
      v-model="showRgInfoDialog"
      title="值班组详情"
      width="80%"
    >
      <el-table :data="rgInfoList" style="width: 100%" max-height="500">
        <el-table-column prop="rgName" label="值班组名称" min-width="150" />
        <el-table-column prop="rgId" label="值班组ID" width="100" />
        <el-table-column prop="rgOwner" label="负责人" width="100" />
        <el-table-column prop="rgOrgName" label="所属部门" width="150" />
        <el-table-column prop="timeRangeCorpusCount" label="时间范围内语料数" width="150" />
        <el-table-column prop="totalCorpusCount" label="历史语料总数" width="120" />
      </el-table>
    </el-dialog>
    
    <!-- Friday空间详情对话框 -->
    <el-dialog
      v-model="showFridaySpaceDialog"
      title="Friday空间详情"
      width="80%"
    >
      <el-table :data="fridaySpaceList" style="width: 100%" max-height="500">
        <el-table-column prop="spaceName" label="空间名称" min-width="150" />
        <el-table-column prop="spaceId" label="空间ID" width="100" />
        <el-table-column prop="spaceDesc" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ctime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.ctime) }}
          </template>
        </el-table-column>
        <el-table-column prop="spaceRgIds" label="关联值班组" width="150">
          <template #default="scope">
            {{ scope.row.spaceRgIds.join(', ') }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    
    <!-- 未保存任务详情对话框 -->
    <el-dialog
      v-model="showUnsavedTasksDialog"
      title="未保存的语料任务详情"
      width="80%"
    >
      <el-table :data="unsavedTaskList" style="width: 100%" max-height="500">
        <el-table-column prop="taskId" label="任务ID" width="100" />
        <el-table-column prop="ticketId" label="工单ID" width="100" />
        <el-table-column prop="creatorUserName" label="创建人" width="100" />
        <el-table-column prop="creatorOrgName" label="部门" width="150" />
        <el-table-column prop="platformId" label="来源" width="100">
          <template #default="scope">
            {{ getPlatformName(scope.row.platformId) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(new Date(scope.row.createTime).getTime()) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart, LineChart, PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import type { CorpusOperationStatusDTO } from '../../../../types';
import { PLATFORM_MAP } from '../../../../constants';

// 注册必要的ECharts组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  BarChart,
  LineChart,
  PieChart,
  CanvasRenderer
]);

// 定义props
const props = defineProps<{
  statusData: CorpusOperationStatusDTO | null;
  loading: boolean;
}>();

// 图表引用
const taskTrendChartRef = ref<HTMLElement | null>(null);
const platformSourceChartRef = ref<HTMLElement | null>(null);
const orgRankChartRef = ref<HTMLElement | null>(null);
const rgCorpusRankChartRef = ref<HTMLElement | null>(null);

// 对话框控制
const showBotMessagesDialog = ref(false);
const showRgInfoDialog = ref(false);
const showFridaySpaceDialog = ref(false);
const showUnsavedTasksDialog = ref(false);

// 图表实例
let taskTrendChart: echarts.ECharts | null = null;
let platformSourceChart: echarts.ECharts | null = null;
let orgRankChart: echarts.ECharts | null = null;
let rgCorpusRankChart: echarts.ECharts | null = null;

// 计算属性
const botMessagesCount = computed(() => props.statusData?.corpusBotMessages.length || 0);
const botMessagesList = computed(() => props.statusData?.corpusBotMessages || []);

const taskGenerateCount = computed(() => props.statusData?.dailyCorpusGenerateTaskCountList.length || 0);

const rgInfoList = computed(() => props.statusData?.corpusStatsRgInfoList || []);
const activeRgCount = computed(() => rgInfoList.value.filter(rg => rg.timeRangeCorpusCount > 0).length);

const totalCorpusCount = computed(() => {
  if (!props.statusData?.corpusStatsRgInfoList) return 0;
  return props.statusData.corpusStatsRgInfoList.reduce((sum, rg) => sum + rg.timeRangeCorpusCount, 0);
});

const fridaySpaceList = computed(() => props.statusData?.corpusStatsFridaySpaceList || []);
const fridaySpaceCount = computed(() => fridaySpaceList.value.length);

const unsavedTaskList = computed(() => {
  if (!props.statusData?.dailyCorpusGenerateTaskCountList) return [];
  return props.statusData.dailyCorpusGenerateTaskCountList.filter(task => !task.taskSaved);
});
const unsavedTaskCount = computed(() => unsavedTaskList.value.length);

// 格式化日期时间
const formatDateTime = (timestamp: number) => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 获取平台名称
const getPlatformName = (platformId: number) => {
  return PLATFORM_MAP[platformId] || `未知(${platformId})`;
};

// 初始化图表
const initCharts = () => {
  if (taskTrendChartRef.value) {
    taskTrendChart = echarts.init(taskTrendChartRef.value);
  }
  
  if (platformSourceChartRef.value) {
    platformSourceChart = echarts.init(platformSourceChartRef.value);
  }
  
  if (orgRankChartRef.value) {
    orgRankChart = echarts.init(orgRankChartRef.value);
  }
  
  if (rgCorpusRankChartRef.value) {
    rgCorpusRankChart = echarts.init(rgCorpusRankChartRef.value);
  }
  
  updateCharts();
};

// 更新图表数据
const updateCharts = () => {
  if (!props.statusData) return;
  
  // 更新语料生成趋势图
  updateTaskTrendChart();
  
  // 更新语料生成触发来源图
  updatePlatformSourceChart();
  
  // 更新部门排名图
  updateOrgRankChart();
  
  // 更新值班组语料数量排名图
  updateRgCorpusRankChart();
};

// 更新语料生成趋势图
const updateTaskTrendChart = () => {
  if (!taskTrendChart || !props.statusData) return;
  
  // 按日期分组统计任务数量
  const tasksByDate = new Map<string, number>();
  
  props.statusData.dailyCorpusGenerateTaskCountList.forEach(task => {
    const date = new Date(task.createTime).toISOString().split('T')[0];
    tasksByDate.set(date, (tasksByDate.get(date) || 0) + 1);
  });
  
  // 排序日期
  const sortedDates = Array.from(tasksByDate.keys()).sort();
  const counts = sortedDates.map(date => tasksByDate.get(date) || 0);
  
  taskTrendChart.setOption({
    title: {
      text: '语料生成趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: sortedDates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '生成次数',
        type: 'line',
        data: counts,
        smooth: true
      }
    ]
  });
};

// 更新语料生成触发来源图
const updatePlatformSourceChart = () => {
  if (!platformSourceChart || !props.statusData) return;
  
  // 按平台ID分组统计
  const platformCounts = new Map<number, number>();
  
  props.statusData.dailyCorpusGenerateTaskCountList.forEach(task => {
    platformCounts.set(task.platformId, (platformCounts.get(task.platformId) || 0) + 1);
  });
  
  const pieData = Array.from(platformCounts.entries()).map(([platformId, count]) => ({
    name: getPlatformName(platformId),
    value: count
  }));
  
  platformSourceChart.setOption({
    title: {
      text: '语料生成触发来源'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: pieData.map(item => item.name)
    },
    series: [
      {
        name: '触发来源',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: pieData
      }
    ]
  });
};

// 更新部门排名图
const updateOrgRankChart = () => {
  if (!orgRankChart || !props.statusData) return;
  
  // 按部门分组统计
  const orgCounts = new Map<string, number>();
  
  props.statusData.dailyCorpusGenerateTaskCountList.forEach(task => {
    if (task.creatorOrgName) {
      orgCounts.set(task.creatorOrgName, (orgCounts.get(task.creatorOrgName) || 0) + 1);
    }
  });
  
  // 排序并取前10
  const sortedOrgs = Array.from(orgCounts.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);
  
  const orgNames = sortedOrgs.map(([name]) => name);
  const orgValues = sortedOrgs.map(([, count]) => count);
  
  orgRankChart.setOption({
    title: {
      text: '语料生成使用频率部门排名'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: orgNames,
      axisLabel: {
        width: 100,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '使用次数',
        type: 'bar',
        data: orgValues
      }
    ]
  });
};

// 更新值班组语料数量排名图
const updateRgCorpusRankChart = () => {
  if (!rgCorpusRankChart || !props.statusData) return;
  
  // 排序并取前10
  const sortedRgs = [...props.statusData.corpusStatsRgInfoList]
    .sort((a, b) => b.timeRangeCorpusCount - a.timeRangeCorpusCount)
    .slice(0, 10);
  
  const rgNames = sortedRgs.map(rg => rg.rgName);
  const rgValues = sortedRgs.map(rg => rg.timeRangeCorpusCount);
  
  rgCorpusRankChart.setOption({
    title: {
      text: '值班组语料数量排名'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: rgNames,
      axisLabel: {
        width: 100,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '语料数量',
        type: 'bar',
        data: rgValues
      }
    ]
  });
};

// 监听数据变化，更新图表
watch(() => props.statusData, () => {
  nextTick(() => {
    updateCharts();
  });
}, { deep: true });

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  taskTrendChart?.resize();
  platformSourceChart?.resize();
  orgRankChart?.resize();
  rgCorpusRankChart?.resize();
};

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initCharts();
    window.addEventListener('resize', handleResize);
  });
});

// 组件卸载前清理
const onBeforeUnmount = () => {
  window.removeEventListener('resize', handleResize);
  taskTrendChart?.dispose();
  platformSourceChart?.dispose();
  orgRankChart?.dispose();
  rgCorpusRankChart?.dispose();
};
</script>

<style scoped lang="scss">
.operation-status-dashboard {
  .stat-card {
    height: 120px;
    
    .card-value {
      font-size: 28px;
      font-weight: 500;
      text-align: center;
      margin-top: 10px;
    }
  }
  
  .chart-row {
    margin-top: 20px;
  }
  
  .chart-card {
    height: 350px;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    span {
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>