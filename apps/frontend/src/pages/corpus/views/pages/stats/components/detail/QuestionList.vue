<template>
  <div class="question-list">
    <el-table
      :data="questions"
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column type="index" width="50" label="#" />
      <el-table-column prop="question" label="问题" min-width="300">
        <template #default="{ row }">
          <div class="question-cell">
            {{ getQuestionText(row) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="时间" width="180">
        <template #default="{ row }">
          <span>{{ formatDate(getQuestionTime(row)) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="召回数量" width="100">
        <template #default="{ row }">
          <span>{{ (props.data.recallInfoByQuestion[getQuestionText(row)] || []).length }}</span>
        </template>
      </el-table-column>
      <el-table-column label="解决状态" width="100">
        <template #default="{ row }">
          <el-tag :type="typeof row === 'string' ? 'info' : (row.solved ? 'success' : 'danger')">
            {{ typeof row === 'string' ? '未知' : (row.solved ? '已解决' : '未解决') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button size="small" @click="handleViewDetail(row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container" v-if="totalPages > 1">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="问题详情"
      width="60%"
    >
      <div class="question-detail">
        <div class="question-content">
          <div class="question-detail-header">
            <h3>问题</h3>
            <div class="action-buttons">
              <el-button 
                v-if="getConversationId(currentQuestion)" 
                type="primary" 
                size="small" 
                @click="handleViewConversation"
              >
                查看会话详情
              </el-button>
            </div>
          </div>
          <p>{{ currentQuestionText }}</p>
          <div class="question-info" v-if="getQuestionId(currentQuestion) || getConversationId(currentQuestion)">
            <p v-if="getQuestionId(currentQuestion)"><strong>问题ID:</strong> {{ getQuestionId(currentQuestion) }}</p>
            <p v-if="getConversationId(currentQuestion)"><strong>会话ID:</strong> {{ getConversationId(currentQuestion) }}</p>
          </div>
          <div class="question-time" v-if="currentQuestionTime">
            提问时间: {{ formatDate(currentQuestionTime) }}
          </div>
        </div>
        
        <div class="related-info" v-if="relatedRecalls.length > 0">
          <h3>相关召回语料 ({{ relatedRecalls.length }})</h3>
          <el-table
            :data="relatedRecalls"
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column label="语料ID" prop="corpusId" width="180" />
            <el-table-column label="标题" prop="title" />
            <el-table-column label="来源" prop="source" width="120">
              <template #default="{ row }">
                <el-tag :type="getSourceTagType(row.source)">
                  {{ getSourceText(row.source) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="内容">
              <template #default="{ row }">
                <el-tooltip
                  effect="dark"
                  :content="row.content"
                  placement="top"
                  :hide-after="0"
                >
                  <div class="content-preview">{{ row.content }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-empty v-else description="无相关召回语料" />
      </div>
    </el-dialog>
    
    <!-- 会话详情弹窗 -->
    <el-dialog
      v-model="conversationDialogVisible"
      :title="`会话详情: ${getConversationId(currentQuestion)}`"
      width="70%"
      @opened="handleConversationDialogOpened"
    >
      <ConversationDetailPanel
        :messages="conversationMessages"
        :loading="conversationLoading"
        :error="conversationLoadError"
        :current-message-id="getQuestionId(currentQuestion)"
        @view-recall-info="viewMessageRecallInfo"
      />
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="conversationDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import httpRequest from '../../../../../../../utils/httpRequest';
import { API_PATHS } from '../../../../../request/api';
import type { RecalledCorpusChunkInfo } from '../../../../../types';
import type { FridayConversationMessageDTO } from '../../../../../types/conversation';
import ConversationDetailPanel from './ConversationDetailPanel.vue';

// 定义问题项类型
interface QuestionItem {
  question: string;
  questionTime?: number;
  questionMessageId?: string;
  questionConversationId?: string;
  solved?: boolean;
}

const props = defineProps<{
  data: {
    questions: Array<string | QuestionItem>;
    recallInfoByQuestion: Record<string, RecalledCorpusChunkInfo[]>;
  }
}>();

// 分页设置
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = computed(() => props.data.questions.length);
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

// 分页处理后的问题列表
const questions = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return props.data.questions.slice(start, end);
});

// 详情弹窗
const dialogVisible = ref(false);
const currentQuestionText = ref('');
const currentQuestionTime = ref(0);
const currentQuestion = ref<string | QuestionItem>('');
const relatedRecalls = ref<RecalledCorpusChunkInfo[]>([]);

// 会话详情弹窗
const conversationDialogVisible = ref(false);
const conversationMessages = ref<FridayConversationMessageDTO[]>([]);
const conversationLoading = ref(false);
const conversationLoadError = ref('');

// 获取问题文本，兼容字符串和对象两种类型
const getQuestionText = (question: string | QuestionItem): string => {
  if (typeof question === 'string') {
    return question;
  }
  return question.question;
};

// 获取问题时间戳，兼容字符串和对象两种类型
const getQuestionTime = (question: string | QuestionItem): number => {
  if (typeof question === 'string') {
    return 0; // 字符串类型无时间戳
  }
  return question.questionTime || 0;
};

// 获取问题ID，兼容字符串和对象两种类型
const getQuestionId = (question: string | QuestionItem): string => {
  if (typeof question === 'string') {
    return ''; // 字符串类型无ID
  }
  return question.questionMessageId || '';
};

// 获取会话ID，兼容字符串和对象两种类型
const getConversationId = (question: string | QuestionItem): string => {
  if (typeof question === 'string') {
    return ''; // 字符串类型无会话ID
  }
  return question.questionConversationId || '';
};

// 格式化日期显示
const formatDate = (timestamp: number): string => {
  if (!timestamp) return '无时间记录';
  
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

// 查看详情
const handleViewDetail = (question: string | QuestionItem) => {
  const questionText = getQuestionText(question);
  currentQuestionText.value = questionText;
  currentQuestionTime.value = getQuestionTime(question);
  currentQuestion.value = question; // 保存原始问题对象或字符串
  
  // 获取问题相关的召回信息
  relatedRecalls.value = props.data.recallInfoByQuestion?.[questionText] || [];
  
  dialogVisible.value = true;
};

// 查看会话详情
const handleViewConversation = () => {
  const conversationId = getConversationId(currentQuestion.value);
  if (!conversationId) {
    // 如果没有会话ID，提示用户
    return;
  }
  
  // 关闭当前弹窗，打开会话详情弹窗
  dialogVisible.value = false;
  conversationDialogVisible.value = true;
};

// 弹窗打开后加载会话消息
const handleConversationDialogOpened = async () => {
  const conversationId = getConversationId(currentQuestion.value);
  if (!conversationId) {
    conversationLoadError.value = '无效的会话ID';
    return;
  }
  
  conversationLoading.value = true;
  conversationLoadError.value = '';
  conversationMessages.value = [];
  
  try {
    // 使用API获取会话消息
    const response = await httpRequest.rawRequestGet(
      API_PATHS.QUERY_CONVERSATION_MESSAGES, 
      { conversationId }
    ) as unknown as { code: number; message: string; data: FridayConversationMessageDTO[] };
    
    if (response.code === 0 && Array.isArray(response.data)) {
      conversationMessages.value = response.data.sort((a: FridayConversationMessageDTO, b: FridayConversationMessageDTO) => {
        // 按时间排序
        return new Date(a.addTime).getTime() - new Date(b.addTime).getTime();
      });
    } else {
      conversationLoadError.value = response.message || '获取会话消息失败';
    }
  } catch (error) {
    console.error('加载会话消息出错:', error);
    conversationLoadError.value = '获取会话消息时发生错误，请稍后重试';
  } finally {
    conversationLoading.value = false;
  }
};

// 查看消息召回信息
const viewMessageRecallInfo = (message: FridayConversationMessageDTO) => {
  if (message.role === 'user') {
    // 更新当前问题为消息中的问题
    const questionText = message.message;
    currentQuestionText.value = questionText;
    currentQuestionTime.value = new Date(message.addTime).getTime();
    currentQuestion.value = {
      question: questionText,
      questionTime: new Date(message.addTime).getTime(),
      questionMessageId: message.messageId.toString(),
      questionConversationId: message.conversationId,
      solved: false
    };
    
    // 获取问题相关的召回信息
    relatedRecalls.value = props.data.recallInfoByQuestion?.[questionText] || [];
    
    // 关闭会话弹窗，打开问题详情弹窗
    conversationDialogVisible.value = false;
    dialogVisible.value = true;
  }
};

// 分页变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

// 获取语料来源文本描述
const getSourceText = (source: number): string => {
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

// 获取语料来源对应的标签类型
const getSourceTagType = (source: number): string => {
  switch (source) {
    case 0:
      return 'info';
    case 1:
      return 'success';
    case 2:
      return 'warning';
    default:
      return 'info';
  }
};
</script>

<style scoped lang="scss">
.question-list {
  .question-cell {
    word-break: break-all;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .question-detail {
    .question-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      h3 {
        margin: 0;
      }
    }
    
    .question-content {
      margin-bottom: 20px;
      
      p {
        font-size: 14px;
        line-height: 1.6;
        word-break: break-all;
        white-space: pre-wrap;
      }
      
      .question-info {
        margin-top: 15px;
        font-size: 13px;
        color: #606266;
        
        p {
          margin: 5px 0;
        }
      }
      
      .question-time {
        margin-top: 10px;
        font-size: 13px;
        color: #909399;
      }
    }
    
    .related-info {
      h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 16px;
      }
      
      .content-preview {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style> 