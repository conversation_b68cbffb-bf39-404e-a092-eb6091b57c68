<template>
  <div class="trend-line-chart">
    <div class="chart-header">
      <div class="chart-title">用户提问与TT数量趋势</div>
      <div class="chart-subtitle">问题数量与TT数量随时间变化趋势</div>
    </div>
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps } from 'vue';
import * as echarts from 'echarts';
import type { AppStatusDTO } from '../../../../../types';

// 定义问题类型接口
interface QuestionItem {
  question: string;
  questionTime: number;
}

// 扩展AppStatusDTO接口，添加questionList字段
interface EnhancedAppStatusDTO extends AppStatusDTO {
  questionList?: Array<QuestionItem>;
}

const props = defineProps<{
  statsData: EnhancedAppStatusDTO;
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 处理数据，生成时间趋势数据
const processData = () => {
  // 从开始时间到结束时间，按天生成日期
  const startDate = new Date(props.statsData.beginTime);
  const endDate = new Date(props.statsData.endTime);
  
  const dateRange: string[] = [];
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    dateRange.push(formatDate(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // 初始化每天的问题和工单数量为0
  const dailyQuestionCount: Record<string, number> = {};
  const dailyTicketCount: Record<string, number> = {};
  
  dateRange.forEach(date => {
    dailyQuestionCount[date] = 0;
    dailyTicketCount[date] = 0;
  });
  
  // 特殊处理：创建一个"无时间戳"分类，用于显示没有时间戳的问题
  const noTimestampKey = "无时间戳";
  dailyQuestionCount[noTimestampKey] = 0;
  
  // 处理问题列表
  const questionList = props.statsData.questionList || [];
  
  // 统计每个问题，并根据其时间戳分配到对应日期
  questionList.forEach(question => {
    if (question.questionTime) {
      const questionDate = formatDate(new Date(question.questionTime));
      
      // 如果日期在查询范围内则计入相应日期，否则计入无时间戳分类
      if (dailyQuestionCount[questionDate] !== undefined) {
        dailyQuestionCount[questionDate]++;
      } else {
        dailyQuestionCount[noTimestampKey]++;
      }
    } else {
      // 没有时间戳的问题
      dailyQuestionCount[noTimestampKey]++;
    }
  });
  
  // 计算每天的工单数量
  const tickets = props.statsData.timeRangeTicketIdList || [];
  tickets.forEach(ticket => {
    if (ticket.createdAt) {
      const ticketDate = formatDate(new Date(ticket.createdAt));
      if (dailyTicketCount[ticketDate] !== undefined) {
        dailyTicketCount[ticketDate]++;
      }
    }
  });
  
  // 将每日统计数据添加到结果数组
  const questionData: number[] = [];
  const ticketData: number[] = [];
  let displayDateRange = [...dateRange];
  
  // 只有当有无时间戳问题时，才添加到X轴
  if (dailyQuestionCount[noTimestampKey] > 0) {
    displayDateRange.push(noTimestampKey);
  }
  
  // 根据是否需要显示无时间戳分类，决定X轴数据
  displayDateRange.forEach(date => {
    if (date === noTimestampKey) {
      questionData.push(dailyQuestionCount[noTimestampKey]);
      ticketData.push(0); // 无时间戳分类没有工单数据
    } else {
      questionData.push(dailyQuestionCount[date]);
      ticketData.push(dailyTicketCount[date]);
    }
  });
  
  return {
    dateRange: displayDateRange,
    questionData,
    ticketData
  };
};

// 格式化日期为 yyyy-MM-dd 格式
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  updateChart();
  
  window.addEventListener('resize', () => {
    chart?.resize();
  });
};

// 更新图表数据
const updateChart = () => {
  if (!chart) return;
  
  const { dateRange, questionData, ticketData } = processData();
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['问题数量', 'TT数量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dateRange,
      axisLabel: {
        interval: 0,
        rotate: dateRange.length > 10 ? 45 : 0,
        formatter: (value: string) => {
          // 对无时间戳标签特殊处理
          if (value === '无时间戳') {
            return value;
          }
          // 如果日期过多，只显示月和日
          if (dateRange.length > 15) {
            const date = new Date(value);
            return `${date.getMonth() + 1}/${date.getDate()}`;
          }
          return value;
        }
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '问题数量',
        type: 'line',
        areaStyle: {
          opacity: 0.3
        },
        emphasis: {
          focus: 'series'
        },
        data: questionData,
        itemStyle: {
          color: '#409EFF'
        },
        lineStyle: {
          width: 2
        },
        smooth: true
      },
      {
        name: 'TT数量',
        type: 'line',
        areaStyle: {
          opacity: 0.3
        },
        emphasis: {
          focus: 'series'
        },
        data: ticketData,
        itemStyle: {
          color: '#E6A23C'
        },
        lineStyle: {
          width: 2
        },
        smooth: true
      }
    ]
  };
  
  chart.setOption(option);
};

// 监听数据变化
watch(() => props.statsData, updateChart, { deep: true });

onMounted(() => {
  initChart();
});
</script>

<style scoped lang="scss">
.trend-line-chart {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 20px;
  
  .chart-header {
    margin-bottom: 16px;
    
    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
    
    .chart-subtitle {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
  
  .chart-container {
    height: 300px;
  }
}
</style> 