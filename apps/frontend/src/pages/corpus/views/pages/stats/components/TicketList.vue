<template>
  <div class="ticket-list">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>相关TT</span>
          <div class="header-right">
            <span class="total-count">共 {{ tickets.length }} 条</span>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="tickets"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column label="TT ID" prop="id" width="180" />
        <el-table-column label="名称" prop="name" />
        <el-table-column label="类型" prop="typeName" width="120" />
        <el-table-column label="状态" prop="state" width="100">
          <template #default="{ row }">
            <el-tag :type="getStateTagType(row.state)">{{ row.state }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="项目" prop="itemName" />
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.updatedAt) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import type { TicketDetailDTO } from '../../../../types';

defineProps({
  tickets: {
    type: Array as () => TicketDetailDTO[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const formatTime = (timestamp: number): string => {
  if (!timestamp) return '--';
  const date = new Date(timestamp);
  return date.toLocaleString();
};

const getStateTagType = (state: string): string => {
  const stateMap: Record<string, string> = {
    '已完成': 'success',
    '处理中': 'warning',
    '待处理': 'info',
    '已关闭': 'danger'
  };
  
  return stateMap[state] || 'info';
};
</script>

<style scoped lang="scss">
.ticket-list {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-right {
      display: flex;
      align-items: center;
      
      .total-count {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}
</style> 