<template>
  <div class="stats-dashboard">
    <!-- 关键统计卡片 -->
    <el-row :gutter="16" class="key-metrics-row">
      <el-col :span="6" v-for="item in keyMetricCards" :key="item.key">
        <div
          class="stat-card key-metric-card"
          @click="handleCardClick(item.key)"
        >
          <div class="card-header">
            <span class="title">{{ item.title }}</span>
            <el-tooltip v-if="item.tooltip" :content="item.tooltip" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="card-content">
            <div class="value key-value">{{ item.value }}</div>
            <div class="description">{{ item.description }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 次要统计卡片容器 -->
    <div class="secondary-metric-container">
      <!-- 展开/收起按钮容器 -->
      <div class="expand-button-container" :class="{ 'expanded': isExpanded }">
        <el-button
          type="primary"
          plain
          size="small"
          @click="isExpanded = !isExpanded"
          class="expand-button"
        >
          {{ isExpanded ? '收起 ↑' : '展开更多指标 ↓' }}
        </el-button>
      </div>

      <!-- 次要统计卡片，只在展开时显示 -->
      <div v-if="isExpanded">
        <!-- 第一排3个卡片 -->
        <el-row :gutter="16" class="secondary-metrics-row">
          <el-col :span="8" v-for="item in secondaryMetricCards.slice(0, 3)" :key="item.key">
            <div
              class="stat-card secondary-card"
              @click="handleCardClick(item.key)"
            >
              <div class="card-header">
                <span class="title">{{ item.title }}</span>
                <el-tooltip v-if="item.tooltip" :content="item.tooltip" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
              <div class="card-content">
                <div class="value">{{ item.value }}</div>
                <div class="description">{{ item.description }}</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 第二排2个卡片，靠左排布 -->
        <el-row :gutter="16" class="expanded-metrics-row">
          <el-col :span="8" v-for="item in secondaryMetricCards.slice(3)" :key="item.key">
            <div
              class="stat-card secondary-card"
              @click="handleCardClick(item.key)"
            >
              <div class="card-header">
                <span class="title">{{ item.title }}</span>
                <el-tooltip v-if="item.tooltip" :content="item.tooltip" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
              <div class="card-content">
                <div class="value">{{ item.value }}</div>
                <div class="description">{{ item.description }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 趋势图表 -->
    <div class="trend-charts">
      <TrendLineChart :stats-data="adaptedStatsData" />
    </div>

    <!-- 问题聚类排行图表 -->
    <el-row :gutter="16" v-if="adaptedQuestionClusters && adaptedQuestionClusters.length > 0">
      <el-col :span="24">
        <QuestionClusterRankChart
          :question-clusters="adaptedQuestionClusters"
          :question-and-recall-infos="statsData.questionAndRecallInfos"
        />
      </el-col>
    </el-row>

    <!-- 未解决问题聚类排行图表 -->
    <el-row :gutter="16" v-if="adaptedQuestionClusters && adaptedQuestionClusters.length > 0">
      <el-col :span="24">
        <UnsolvedQuestionClusterRankChart
          :question-clusters="adaptedQuestionClusters"
          :question-and-recall-infos="statsData.questionAndRecallInfos"
        />
      </el-col>
    </el-row>

    <!-- 语料排名图表 -->
    <el-row :gutter="16">
      <el-col :span="12">
        <CorpusRankChart :recall-info-by-question="statsData.recallInfoByQuestion" />
      </el-col>
      <el-col :span="12">
        <AiGeneratedCorpusRankChart :recall-info-by-question="statsData.recallInfoByQuestion" />
      </el-col>
    </el-row>

    <!-- 饼图区域 -->
    <div class="charts-container">
      <!-- 分布表格 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <SourceDistributionTable :chunks="allRecallChunks" />
        </el-col>
      </el-row>

      <!-- 饼图 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <div class="chart-wrapper">
            <div class="chart-title">问题解决状态分析</div>
            <div ref="recallChartRef" class="chart-container"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="70%"
      :before-close="handleDialogClose"
    >
      <component
        :is="currentDetailComponent"
        :data="dialogData"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, defineProps, shallowRef } from 'vue';
import * as echarts from 'echarts';
import { QuestionFilled } from '@element-plus/icons-vue';
import type { StatsAnalysisResult } from '../../../../utils/statsDataProcessor';
import type { RecalledCorpusChunkInfo } from '../../../../types';

// 导入详情组件
import QuestionList from './detail/QuestionList.vue';
import RecallDetails from './detail/RecallDetails.vue';
import TicketList from './detail/TicketList.vue';
import QuestionClusterList from './detail/QuestionClusterList.vue';
import ConversationList from './detail/ConversationList.vue';
import UnsolvedQuestionClusterList from './detail/UnsolvedQuestionClusterList.vue';

// 导入图表组件
import CorpusRankChart from './chart/CorpusRankChart.vue';
import AiGeneratedCorpusRankChart from './chart/AiGeneratedCorpusRankChart.vue';
import SourceDistributionTable from './chart/SourceDistributionTable.vue';
import TrendLineChart from './chart/TrendLineChart.vue';
import QuestionClusterRankChart from './chart/QuestionClusterRankChart.vue';
import UnsolvedQuestionClusterRankChart from './chart/UnsolvedQuestionClusterRankChart.vue';

const props = defineProps<{
  statsData: StatsAnalysisResult;
  loading: boolean;
}>();

// 计算所有召回语料块
const allRecallChunks = computed<RecalledCorpusChunkInfo[]>(() => {
  const result: RecalledCorpusChunkInfo[] = [];
  Object.values(props.statsData.recallInfoByQuestion || {}).forEach(chunks => {
    result.push(...chunks);
  });
  return result;
});

// 计算非AI生成的独立语料分片数量
const nonAiGeneratedUniqueChunksCount = computed(() => {
  // 获取所有非AI生成的语料块(source !== 1)
  const nonAiChunks = allRecallChunks.value.filter(chunk => chunk.source !== 1);

  // 使用Set来存储唯一的语料ID
  const uniqueIds = new Set<string>();
  nonAiChunks.forEach(chunk => uniqueIds.add(chunk.corpusId));

  return uniqueIds.size;
});

// 计算未提问问题数量
const unsolvedQuestionsCount = computed(() => {
  return props.statsData.questionList.filter(q => !q.solved).length;
});



// 关键指标卡片和次要指标卡片
const keyMetricCards = computed(() => [
  {
    key: 'userMessageCount',
    title: '总提问数量',
    value: props.statsData.questionList.length,
    description: '用户的问题总数',
    tooltip: '统计时间范围内收集到的用户问题总数'
  },
  {
    key: 'questionClusters',
    title: '问题类型数量',
    value: props.statsData.questionClusters?.length || 0,
    description: '问题类型的数量，点击可查看详情',
    tooltip: '系统自动对问题进行类型分组的总数量'
  },
  {
    key: 'unsolvedQuestionsCount',
    title: '未解决问题数量',
    value: unsolvedQuestionsCount.value,
    description: '值班助手回答不了的问题数量',
    tooltip: '时间区间内，所有值班助手回答不了的问题数量总和'
  },
  {
    key: 'ttCount',
    title: 'TT数量',
    value: props.statsData.ttCount,
    description: '机器人对应值班组周期内TT数量',
    tooltip: '时间区间内，所有TT数量总和 (排除值班组成员提的TT)'
  }
]);

const secondaryMetricCards = computed(() => [
  {
    key: 'conversationCount',
    title: '总会话数量',
    value: props.statsData.conversationCount,
    description: '所选时间范围内的会话总数',
    tooltip: '所选时间范围内的会话总数'
  },
  {
    key: 'totalRecallChunksCount',
    title: '总召回分片次数',
    value: props.statsData.totalRecallChunksCount,
    description: '针对所有问题的召回语料分片次数',
    tooltip: '时间区间内，所有问题召回的语料分片次数总和'
  },
  {
    key: 'aiGeneratedChunksCount',
    title: '语料助手生成分片被召回次数',
    value: props.statsData.aiGeneratedChunksCount,
    description: '语料助手生成的语料分片被召回的次数',
    tooltip: '时间区间内，所有语料助手生成的语料分片被召回的次数总和'
  },
  {
    key: 'uniqueAiGeneratedChunksCount',
    title: '独立语料助手生成分片数量',
    value: props.statsData.uniqueAiGeneratedChunksCount,
    description: '召回的去重后的语料助手生成语料分片数量',
    tooltip: '时间区间内，被召回的所有语料助手生成的语料分片去重后的数量总和'
  },
  {
    key: 'nonAiGeneratedUniqueChunksCount',
    title: '非语料助手生成独立分片数量',
    value: nonAiGeneratedUniqueChunksCount.value,
    description: '召回的去重后的非语料助手生成的语料分片数量',
    tooltip: '时间区间内，被召回的所有非语料助手生成的语料分片去重后的数量总和'
  }
]);

// 图表相关
const sourceChartRef = ref<HTMLElement | null>(null);
const recallChartRef = ref<HTMLElement | null>(null);
let sourceChart: echarts.ECharts | null = null;
let recallChart: echarts.ECharts | null = null;

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = ref('');
const dialogData = ref<any>(null);
const currentDetailComponent = shallowRef<any>(null);

// 是否展开更多指标卡片
const isExpanded = ref(false);

// 为 QuestionClusterRankChart 组件适配数据
const adaptedQuestionClusters = computed(() => {
  return props.statsData.questionClusters.map(cluster => ({
    questionPattern: cluster.questionPattern,
    questionList: cluster.questionList.map(q => ({
      question: q.question,
      questionMessageId: q.questionMessageId,
      questionConversationId: q.questionConversationId,
      questionTime: q.questionTime,
      solved: (q as any).solved || false
    }))
  }));
});

// 为 TrendLineChart 组件适配数据
const adaptedStatsData = computed(() => {
  return {
    appId: props.statsData.appId || '',
    appName: props.statsData.appName || '',
    beginTime: props.statsData.beginTime || 0,
    endTime: props.statsData.endTime || 0,
    conversationCount: props.statsData.conversationCount || 0,
    userMessageCount: props.statsData.userMessageCount || 0,
    questionList: props.statsData.questionList.map(q => ({
      question: q.question,
      questionTime: q.questionTime
    })),
    questionAndRecallInfos: props.statsData.questionAndRecallInfos || [],
    questionClusters: [],
    timeRangeTicketIdList: props.statsData.tickets || [],
    relatedRgList: props.statsData.relatedRgList || []
  };
});

// 处理卡片点击事件
const handleCardClick = (key: string) => {
  // 根据不同的卡片类型，设置不同的对话框内容
  switch (key) {
    case 'conversationCount':
      dialogTitle.value = '会话列表';
      dialogData.value = {
        questions: props.statsData.questionList,
        questionAndRecallInfos: props.statsData.questionAndRecallInfos
      };
      currentDetailComponent.value = ConversationList;
      break;
    case 'questionClusters':
      dialogTitle.value = '问题类型列表';
      dialogData.value = {
        clusters: props.statsData.questionClusters,
        loading: props.loading,
        recallInfoByQuestion: props.statsData.recallInfoByQuestion
      };
      currentDetailComponent.value = QuestionClusterList;
      break;
    case 'userMessageCount':
      dialogTitle.value = '问题列表';
      dialogData.value = {
        questions: props.statsData.questionList,
        recallInfoByQuestion: props.statsData.recallInfoByQuestion
      };
      currentDetailComponent.value = QuestionList;
      break;
    case 'totalRecallChunksCount':
      dialogTitle.value = '召回语料详情';
      dialogData.value = {
        recallInfoByQuestion: props.statsData.recallInfoByQuestion
      };
      currentDetailComponent.value = RecallDetails;
      break;
    case 'aiGeneratedChunksCount':
      dialogTitle.value = '语料助手生成语料详情';
      dialogData.value = {
        chunks: allRecallChunks.value.filter(chunk => chunk.source === 1)
      };
      currentDetailComponent.value = RecallDetails;
      break;
    case 'uniqueAiGeneratedChunksCount':
      dialogTitle.value = '独立语料助手生成分片详情';
      // 获取所有语料助手生成的语料块(source === 1)并去重
      const aiChunks = allRecallChunks.value.filter(chunk => chunk.source === 1);
      // 使用Map去重，保留corpusId唯一的对象
      const uniqueAiChunksMap = new Map();
      aiChunks.forEach(chunk => {
        if (!uniqueAiChunksMap.has(chunk.corpusId)) {
          uniqueAiChunksMap.set(chunk.corpusId, chunk);
        }
      });
      const uniqueAiChunks = Array.from(uniqueAiChunksMap.values());

      dialogData.value = {
        chunks: uniqueAiChunks
      };
      currentDetailComponent.value = RecallDetails;
      break;
    case 'nonAiGeneratedUniqueChunksCount':
      dialogTitle.value = '非语料助手生成独立分片详情';
      // 获取所有非语料助手生成的语料块(source !== 1)并去重
      const nonAiChunks = allRecallChunks.value.filter(chunk => chunk.source !== 1);
      // 使用Map去重，保留corpusId唯一的对象
      const uniqueNonAiChunksMap = new Map();
      nonAiChunks.forEach(chunk => {
        if (!uniqueNonAiChunksMap.has(chunk.corpusId)) {
          uniqueNonAiChunksMap.set(chunk.corpusId, chunk);
        }
      });
      const uniqueNonAiChunks = Array.from(uniqueNonAiChunksMap.values());

      dialogData.value = {
        chunks: uniqueNonAiChunks
      };
      currentDetailComponent.value = RecallDetails;
      break;
    case 'unsolvedQuestionsCount':
      dialogTitle.value = '未解决问题聚类列表';
      dialogData.value = {
        questionClusters: props.statsData.questionClusters,
        questionList: props.statsData.questionList,
        recallInfoByQuestion: props.statsData.recallInfoByQuestion
      };
      currentDetailComponent.value = UnsolvedQuestionClusterList;
      break;
    case 'ttCount':
      dialogTitle.value = 'TT列表';
      dialogData.value = {
        tickets: props.statsData.tickets
      };
      currentDetailComponent.value = TicketList;
      break;
    default:
      return;
  }

  dialogVisible.value = true;
};

const handleDialogClose = () => {
  dialogVisible.value = false;
};

// 初始化图表
const initCharts = () => {
  if (sourceChartRef.value) {
    sourceChart = echarts.init(sourceChartRef.value);
  }

  if (recallChartRef.value) {
    recallChart = echarts.init(recallChartRef.value);
  }

  updateCharts();
};

// 更新图表数据
const updateCharts = () => {
  if (!props.statsData) return;

  // 更新语料来源分布图表
  if (sourceChart) {
    const sourceData = [
      { value: props.statsData.aiGeneratedChunksCount, name: 'AI生成' },
      {
        value: props.statsData.totalRecallChunksCount - props.statsData.aiGeneratedChunksCount,
        name: '其他来源'
      }
    ];

    sourceChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['AI生成', '其他来源']
      },
      series: [
        {
          name: '语料来源',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: sourceData
        }
      ]
    });
  }

  // 更新问题解决状态分析图表
  if (recallChart) {
    const solvedQuestionsCount = props.statsData.questionList.filter(q => q.solved).length;
    const unsolvedQuestionsCount = props.statsData.questionList.length - solvedQuestionsCount;

    const recallData = [
      { value: unsolvedQuestionsCount, name: '未解决问题' },
      { value: solvedQuestionsCount, name: '已解决问题' }
    ];

    recallChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['未解决问题', '已解决问题']
      },
      series: [
        {
          name: '问题状态',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: recallData
        }
      ]
    });
  }
};

// 监听数据变化，更新图表
watch(() => props.statsData, updateCharts, { deep: true });

// 监听窗口大小变化，调整图表大小
window.addEventListener('resize', () => {
  sourceChart?.resize();
  recallChart?.resize();
});

onMounted(() => {
  initCharts();
});
</script>

<style scoped lang="scss">
.stats-dashboard {
  .key-metrics-row {
    margin-bottom: 30px;
  }

  .secondary-metrics-row {
    margin-bottom: 5px;
  }

  .expanded-metrics-row {
    margin-top: 5px;
  }

  .stat-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #ebeef5;

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
      transform: translateY(-3px);
    }

    &.key-metric-card {
      height: 180px;
      box-shadow: 0 4px 15px 0 rgba(64, 158, 255, 0.25);
      border: 2px solid #409EFF;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 30px 30px 0;
        border-color: transparent #409EFF transparent transparent;
      }

      &:hover {
        box-shadow: 0 6px 25px 0 rgba(64, 158, 255, 0.35);
      }

      .card-header .title {
        font-weight: 600;
        color: #202945;
        font-size: 16px;
      }

      .card-content {
        .value.key-value {
          font-size: 36px;
          margin-top: 10px;
        }

        .description {
          font-size: 14px;
        }
      }
    }

    &.secondary-card {
      height: 130px;

      .card-header .title {
        font-size: 15px;
      }

      .card-content {
        .value {
          font-size: 28px;
          color: #67c23a;
        }

        .description {
          font-size: 13px;
        }
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .value {
        font-size: 32px;
        font-weight: bold;
        color: #67c23a;
        margin-bottom: 8px;

        &.key-value {
          color: #409EFF;
          font-size: 38px;
          font-weight: 700;
          text-shadow: 0 1px 2px rgba(64, 158, 255, 0.2);
        }
      }

      .description {
        font-size: 14px;
        color: #909399;
        text-align: center;
      }
    }
  }

  .trend-charts {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .charts-container {
    margin-top: 20px;

    .chart-wrapper {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 16px;
      margin-bottom: 16px;

      .chart-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 16px;
      }

      .chart-container {
        height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.secondary-metric-container {
  position: relative;
  margin-top: 5px;
  margin-bottom: 20px;

  .expand-button-container {
    text-align: center;
    margin: -10px 0 20px 0;
    padding: 0;
    position: relative;
    z-index: 10;

    &.expanded {
      margin-bottom: 25px;
    }

    .expand-button {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      border-radius: 20px;
      padding: 8px 20px;
      font-weight: 500;
      font-size: 14px;
      border: 1px solid #409EFF;
      background-color: #ecf5ff;
      color: #409EFF;

      &:hover {
        opacity: 0.9;
        background-color: #d9ecff;
      }
    }
  }

  .expanded-metrics-row {
    margin-top: 5px;
  }
}
</style>