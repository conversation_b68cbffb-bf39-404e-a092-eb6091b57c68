<template>
  <div class="conversation-list">
    <el-table
      :data="pagedConversations"
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column type="index" width="50" label="#" />
      <el-table-column label="会话ID" prop="conversationId" width="300" />
      <el-table-column label="问题数量" width="120">
        <template #default="{ row }">
          <el-tag type="primary">{{ row.questions.length }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="首次提问时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.firstQuestionTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button size="small" @click="handleViewDetail(row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container" v-if="totalPages > 1">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 会话详情弹窗 -->
    <el-dialog
      v-model="conversationDialogVisible"
      :title="`会话详情: ${selectedConversation?.conversationId || ''}`"
      width="70%"
      @opened="handleConversationDialogOpened"
    >
      <ConversationDetailPanel
        :messages="conversationMessages"
        :loading="conversationLoading"
        :error="conversationLoadError"
        :current-message-id="currentQuestionMessageId"
        @view-recall-info="viewMessageRecallInfo"
      />
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="conversationDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 问题详情弹窗 -->
    <el-dialog
      v-model="questionDialogVisible"
      title="问题详情与召回信息"
      width="70%"
    >
      <div v-if="currentQuestionItem" class="question-detail">
        <div class="question-content">
          <h3>问题内容</h3>
          <p>{{ isQuestionItem(currentQuestionItem) ? currentQuestionItem.question : 
             isFridayMessage(currentQuestionItem) ? currentQuestionItem.message : '' }}</p>
          <div class="question-info">
            <p><strong>问题ID:</strong> {{ isQuestionItem(currentQuestionItem) ? currentQuestionItem.questionMessageId : 
               isFridayMessage(currentQuestionItem) ? currentQuestionItem.messageId : '' }}</p>
            <p><strong>会话ID:</strong> {{ isQuestionItem(currentQuestionItem) ? currentQuestionItem.questionConversationId : 
               isFridayMessage(currentQuestionItem) ? currentQuestionItem.conversationId : '' }}</p>
            <p v-if="isQuestionItem(currentQuestionItem) && currentQuestionItem.questionTime">
              <strong>提问时间:</strong> {{ formatDateTime(currentQuestionItem.questionTime) }}
            </p>
            <p v-else-if="isFridayMessage(currentQuestionItem) && currentQuestionItem.addTime">
              <strong>提问时间:</strong> {{ formatTime(currentQuestionItem.addTime) }}
            </p>
          </div>
        </div>
        
        <div v-if="questionRecallInfo" class="recall-info">
          <el-divider />
          <h3>召回语料信息 ({{ questionRecallInfo.recalledCorpusChunkInfoList.length }}条)</h3>
          
          <div v-if="questionRecallInfo.recalledCorpusChunkInfoList.length > 0">
            <el-table
              :data="questionRecallInfo.recalledCorpusChunkInfoList"
              style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            >
              <el-table-column label="语料ID" prop="corpusId" width="180" />
              <el-table-column label="标题" prop="title" />
              <el-table-column label="来源" prop="source" width="120">
                <template #default="{ row }">
                  <el-tag :type="getSourceTagType(row.source)">
                    {{ getSourceText(row.source) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="内容">
                <template #default="{ row }">
                  <el-tooltip
                    effect="dark"
                    :content="row.content"
                    placement="top"
                    :hide-after="0"
                  >
                    <div class="content-preview">{{ row.content }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-empty v-else description="未找到召回信息" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import httpRequest from '../../../../../../../utils/httpRequest';
import { API_PATHS } from '../../../../../request/api';
import ConversationDetailPanel from './ConversationDetailPanel.vue';
import type { FridayConversationMessageDTO, QuestionItem } from '../../../../../types/conversation';

// 会话信息接口
interface ConversationInfo {
  conversationId: string;
  questions: QuestionItem[];
  firstQuestionTime: number;
}

// 语料块接口
interface RecalledCorpusChunkInfo {
  corpusId: string;
  parentId: string;
  source: number;
  title: string;
  content: string;
}

// 问题召回信息接口
interface QuestionCorpusRecallDetailItem {
  questionContent: string;
  questionMessageId: string;
  questionConversationId: string;
  recalledCorpusChunkInfoList: RecalledCorpusChunkInfo[];
}

const props = defineProps<{
  data: {
    questions: QuestionItem[];
    questionAndRecallInfos: QuestionCorpusRecallDetailItem[];
  }
}>();

// 按会话ID分组的问题列表
const conversationMap = computed(() => {
  const map = new Map<string, ConversationInfo>();
  
  props.data.questions.forEach(question => {
    const conversationId = question.questionConversationId;
    
    if (!map.has(conversationId)) {
      map.set(conversationId, {
        conversationId,
        questions: [],
        firstQuestionTime: question.questionTime
      });
    }
    
    const conversation = map.get(conversationId)!;
    conversation.questions.push(question);
    
    // 更新第一次提问时间（找最早的问题时间）
    if (question.questionTime < conversation.firstQuestionTime) {
      conversation.firstQuestionTime = question.questionTime;
    }
  });
  
  // 为每个会话排序问题，按时间升序
  map.forEach(conversation => {
    conversation.questions.sort((a, b) => a.questionTime - b.questionTime);
  });
  
  return map;
});

// 转换为数组形式的会话列表
const conversations = computed<ConversationInfo[]>(() => {
  return Array.from(conversationMap.value.values())
    .sort((a, b) => b.firstQuestionTime - a.firstQuestionTime); // 按首次提问时间降序排序
});

// 分页设置
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = computed(() => conversations.value.length);
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

// 分页后的会话列表
const pagedConversations = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return conversations.value.slice(start, end);
});

// 会话详情弹窗
const conversationDialogVisible = ref(false);
const selectedConversation = ref<ConversationInfo | null>(null);
const conversationMessages = ref<FridayConversationMessageDTO[]>([]);
const conversationLoading = ref(false);
const conversationLoadError = ref('');

// 问题详情弹窗
const questionDialogVisible = ref(false);

// 定义一个类型联合体来解决类型错误
type QuestionItemType = QuestionItem | FridayConversationMessageDTO;
const currentQuestionItem = ref<QuestionItemType | null>(null);

// 判断是否是QuestionItem类型
const isQuestionItem = (item: QuestionItemType): item is QuestionItem => {
  return 'question' in item && 'questionMessageId' in item;
};

// 判断是否是FridayConversationMessageDTO类型
const isFridayMessage = (item: QuestionItemType): item is FridayConversationMessageDTO => {
  return 'message' in item && 'messageId' in item;
};

const questionRecallInfo = computed(() => {
  if (!currentQuestionItem.value) return null;
  
  // 处理新旧两种数据格式
  let messageId = '';
  
  if (isQuestionItem(currentQuestionItem.value)) {
    messageId = currentQuestionItem.value.questionMessageId;
  } else if (isFridayMessage(currentQuestionItem.value)) {
    messageId = currentQuestionItem.value.messageId;
  }
  
  return props.data.questionAndRecallInfos.find(
    info => info.questionMessageId === messageId
  ) || null;
});

// 查看会话详情
const handleViewDetail = (conversation: ConversationInfo) => {
  selectedConversation.value = conversation;
  conversationMessages.value = [];
  conversationLoadError.value = '';
  conversationDialogVisible.value = true;
};

// 加载会话消息
const loadConversationMessages = async (conversationId: string) => {
  if (!conversationId) return;
  
  conversationLoading.value = true;
  conversationLoadError.value = '';
  
  try {
    const response = await httpRequest.rawRequestGet(
      API_PATHS.QUERY_CONVERSATION_MESSAGES, 
      { conversationId }
    ) as unknown as { code: number; message: string; data: FridayConversationMessageDTO[] };
    
    if (response.code === 0 && Array.isArray(response.data)) {
      conversationMessages.value = response.data.sort((a: FridayConversationMessageDTO, b: FridayConversationMessageDTO) => {
        // 按时间排序
        return new Date(a.addTime).getTime() - new Date(b.addTime).getTime();
      });
    } else {
      conversationLoadError.value = response.message || '获取会话消息失败';
    }
  } catch (error) {
    console.error('加载会话消息出错:', error);
    conversationLoadError.value = '获取会话消息时发生错误，请稍后重试';
  } finally {
    conversationLoading.value = false;
  }
};

// 弹窗打开后加载会话消息
const handleConversationDialogOpened = () => {
  if (selectedConversation.value) {
    loadConversationMessages(selectedConversation.value.conversationId);
  }
};

// 查看问题召回详情 (旧格式数据)
const handleViewQuestionDetail = (question: QuestionItem) => {
  currentQuestionItem.value = question;
  questionDialogVisible.value = true;
};

// 分页变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

// 格式化时间字符串
const formatTime = (timeStr: string): string => {
  if (!timeStr) return '无时间信息';
  
  try {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (error) {
    return timeStr;
  }
};

// 格式化日期时间
const formatDateTime = (timestamp: number): string => {
  if (!timestamp) return '无时间信息';
  
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 获取语料来源文本描述
const getSourceText = (source: number): string => {
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

// 获取语料来源对应的标签类型
const getSourceTagType = (source: number): string => {
  switch (source) {
    case 0:
      return 'info';
    case 1:
      return 'success';
    case 2:
      return 'warning';
    default:
      return 'info';
  }
};

// 问题详情数据
const questionDetailData = ref<QuestionItem | null>(null);

// 当前选中的问题
const currentQuestionMessageId = computed(() => {
  return questionDetailData.value?.questionMessageId;
});

// 查看消息详情中的召回信息
const viewMessageRecallInfo = (message: FridayConversationMessageDTO) => {
  const questionItem: QuestionItem = {
    question: message.message,
    questionMessageId: message.messageId.toString(), // 确保为字符串类型
    questionTime: new Date(message.addTime).getTime(),
    questionConversationId: message.conversationId,
    solved: false
  };
  handleViewQuestionDetail(questionItem);
};
</script>

<style scoped lang="scss">
.conversation-list {
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .loading-container {
    padding: 20px 0;
  }
  
  .conversation-messages {
    .messages-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      
      h3 {
        font-size: 16px;
        margin: 0;
        color: #303133;
      }
      
      .messages-info {
        font-size: 13px;
        color: #909399;
        text-align: right;
        
        p {
          margin: 0 0 4px 0;
        }
      }
    }
    
    .message-list {
      .message-item {
        padding: 12px;
        margin-bottom: 16px;
        border-radius: 8px;
        
        &.user-message {
          background-color: #f2f6fc;
          border-left: 4px solid #409EFF;
        }
        
        &.ai-message {
          background-color: #f5f7fa;
          border-left: 4px solid #67C23A;
        }
        
        .message-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          .message-role {
            font-weight: 500;
            color: #303133;
          }
          
          .message-time {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .message-content {
          font-size: 14px;
          color: #606266;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-word;
          margin-bottom: 8px;
        }
        
        .message-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #909399;
          
          .message-id {
            font-family: monospace;
          }
        }
      }
    }
  }
  
  .conversation-detail {
    h3 {
      font-size: 16px;
      margin-bottom: 16px;
      color: #303133;
    }
  }
  
  .question-detail {
    .question-content {
      margin-bottom: 20px;
      
      h3 {
        font-size: 16px;
        margin-bottom: 10px;
        color: #303133;
      }
      
      p {
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
        word-break: break-all;
        background-color: #f9f9f9;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 16px;
      }
      
      .question-info {
        color: #909399;
        font-size: 13px;
        
        p {
          background-color: transparent;
          padding: 0;
          margin-bottom: 8px;
        }
      }
    }
    
    .recall-info {
      h3 {
        font-size: 16px;
        margin-bottom: 16px;
        color: #303133;
      }
      
      .content-preview {
        max-height: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }
  }
}
</style> 