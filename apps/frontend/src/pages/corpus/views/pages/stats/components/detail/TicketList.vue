<template>
  <div class="ticket-list">
    <el-table
      :data="pagedTickets"
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column label="TT ID" prop="id" width="180" />
      <el-table-column label="名称" prop="name" />
      <el-table-column label="类型" prop="typeName" width="120" />
      <el-table-column label="状态" prop="state" width="100">
        <template #default="{ row }">
          <el-tag :type="getStateTagType(row.state)">{{ row.state }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目" prop="itemName" />
      <el-table-column label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatTime(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="更新时间" width="180">
        <template #default="{ row }">
          {{ formatTime(row.updatedAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button size="small" @click="handleViewDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container" v-if="totalPages > 1">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="TT详情"
      width="60%"
    >
      <div class="ticket-detail">
        <div class="ticket-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="TT ID">{{ currentTicket.id }}</el-descriptions-item>
            <el-descriptions-item label="名称">{{ currentTicket.name }}</el-descriptions-item>
            <el-descriptions-item label="类型">{{ currentTicket.typeName }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStateTagType(currentTicket.state)">{{ currentTicket.state }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="项目">{{ currentTicket.itemName }}</el-descriptions-item>
            <el-descriptions-item label="类别">{{ currentTicket.categoryName }}</el-descriptions-item>
            <el-descriptions-item label="分配人">{{ currentTicket.assigned }}</el-descriptions-item>
            <el-descriptions-item label="报告人">{{ currentTicket.reporter }}</el-descriptions-item>
            <el-descriptions-item label="SLA">{{ currentTicket.sla }}</el-descriptions-item>
            <el-descriptions-item label="RG ID">{{ currentTicket.rgId }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatTime(currentTicket.createdAt) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ formatTime(currentTicket.updatedAt) }}</el-descriptions-item>
            <el-descriptions-item label="更新人">{{ currentTicket.updatedBy }}</el-descriptions-item>
            <el-descriptions-item label="TT类型">{{ currentTicket.ticketType }}</el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              <div class="ticket-desc">{{ currentTicket.desc || '无描述' }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import type { TicketDetailDTO } from '../../../../../types';

const props = defineProps<{
  data: {
    tickets: TicketDetailDTO[];
  }
}>();

// 分页设置
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = computed(() => props.data.tickets.length);
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

// 分页后的工单列表
const pagedTickets = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return props.data.tickets.slice(start, end);
});

// 工单详情弹窗
const dialogVisible = ref(false);
const currentTicket = ref<TicketDetailDTO>({} as TicketDetailDTO);

// 查看工单详情
const handleViewDetail = (ticket: TicketDetailDTO) => {
  currentTicket.value = ticket;
  dialogVisible.value = true;
};

// 分页变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

// 格式化时间
const formatTime = (timestamp: number): string => {
  if (!timestamp) return '--';
  const date = new Date(timestamp);
  return date.toLocaleString();
};

// 获取状态对应的标签类型
const getStateTagType = (state: string): string => {
  const stateMap: Record<string, string> = {
    '已完成': 'success',
    '处理中': 'warning',
    '待处理': 'info',
    '已关闭': 'danger'
  };
  
  return stateMap[state] || 'info';
};
</script>

<style scoped lang="scss">
.ticket-list {
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .ticket-detail {
    .ticket-desc {
      white-space: pre-wrap;
      line-height: 1.6;
      max-height: 200px;
      overflow-y: auto;
      padding: 8px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}
</style> 