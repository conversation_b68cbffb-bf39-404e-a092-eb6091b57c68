<template>
  <div class="corpus-list">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>召回语料</span>
          <div class="header-right">
            <span class="total-count">共 {{ totalCount }} 条</span>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="corpusList"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column label="问题" prop="question" />
        <el-table-column label="语料ID" prop="corpusId" width="180" />
        <el-table-column label="标题" prop="title" />
        <el-table-column label="来源" prop="source" width="120">
          <template #default="{ row }">
            <el-tag :type="getSourceTagType(row.source)">
              {{ getSourceText(row.source) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="内容" prop="content">
          <template #default="{ row }">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="row.content"
              placement="top-start"
              :hide-after="0"
            >
              <div class="content-ellipsis">{{ row.content }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import type { RecalledCorpusChunkInfo } from '../../../../types';

const props = defineProps({
  questionAndRecallInfos: {
    type: Object as () => Record<string, RecalledCorpusChunkInfo[]>,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 将嵌套对象转换为扁平数组
const corpusList = computed(() => {
  const result: Array<RecalledCorpusChunkInfo & { question: string }> = [];
  
  Object.entries(props.questionAndRecallInfos || {}).forEach(([question, infos]) => {
    infos.forEach(info => {
      result.push({
        ...info,
        question
      });
    });
  });
  
  return result;
});

const totalCount = computed(() => corpusList.value.length);

const getSourceText = (source: number): string => {
  switch (source) {
    case 0:
      return '其他';
    case 1:
      return '语料处理服务';
    case 2:
      return 'KM导入Friday';
    default:
      return '未知';
  }
};

const getSourceTagType = (source: number): string => {
  switch (source) {
    case 0:
      return 'info';
    case 1:
      return 'success';
    case 2:
      return 'warning';
    default:
      return 'info';
  }
};
</script>

<style scoped lang="scss">
.corpus-list {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-right {
      display: flex;
      align-items: center;
      
      .total-count {
        font-size: 14px;
        color: #909399;
      }
    }
  }
  
  .content-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
  }
}
</style> 