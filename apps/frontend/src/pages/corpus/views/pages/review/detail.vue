<script setup lang="ts">
import { ref, onMounted, computed, onBeforeUnmount, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import httpRequest from '@/utils/httpRequest'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { Connection, Delete, DocumentCopy, Refresh, Collection } from '@element-plus/icons-vue'
import MonacoEditor from '../../components/MonacoEditor.vue'
import LoadingProgressPanel from '../../components/LoadingProgressPanel.vue'
import ViewCorpusDialog from '../../components/ViewCorpusDialog.vue'
import { initMonacoEditor } from '../../../utils/monaco-loader'
import { formatDateTime, formatTitle } from '../../../utils/format'
import { API_PATHS } from '@/pages/corpus/request/api'
import ContentQualityScore from '../../components/ContentQualityScore.vue'
import { debounce } from '../../../utils/debounce'

// 初始化Monaco编辑器配置
initMonacoEditor()

const route = useRoute()
const taskId = ref(route.query.taskId as string)
const mode = ref(route.query.mode as string)
const misId = ref(route.query.misId as string || '')

// 预览状态
const isPreview = ref(false)

// 添加保存按钮状态变量
const isSaving = ref(false)

// 定义表单数据类型接口
interface FormData {
  title: string;
  content: string;
  originalContent: string;
  taskId: string;
  ticketId: string;
  rgId: number;
  misId: string;
  platformId: number;
  tagsIds: string; // 新增：标签ID字符串，逗号分隔
}

// 定义标签选项接口
interface TagOption {
  id: string
  name: string
}

// 表单数据
const formData = ref<FormData>({
  title: '',
  content: '',
  originalContent: '',
  taskId: '',
  ticketId: '',
  rgId: 0,
  misId: misId.value,
  platformId: 0,
  tagsIds: ''
})

// 用于强制重新渲染编辑器组件的key
const editorKey = ref(0)

// 编辑器内容，无论预览状态如何都可以访问
const editorContent = computed({
  get: () => formData.value.content,
  set: (val) => {
    formData.value.content = val
  }
})

// 编辑器实例引用
const editorRef = ref(null)

// 编辑器重置事件处理
const handleEditorReset = () => {
  // 什么都不做，让MonacoEditor组件自己处理重置
  // 必要时可以添加一些辅助操作
  console.log('编辑器重置被触发')
}

// 缺失信息
const taskMissingInfo = ref<string[]>([])

// 检索对比相关
const compareDialogVisible = ref(false)
const compareResults = ref<any[]>([])
const selectedCompareRows = ref<any[]>([])
const compareLoading = ref(false)
const compareLoadingText = ref('正在检索相似语料...')

// 相似语料查询缓存
const similarCorpusCache = ref<{
  taskId: string;
  query: string;
  results: any[];
}>(null)

// 查看详情相关
const viewDialogVisible = ref(false)
const currentViewItem = ref<any>({
  title: '',
  content: '',
  source: '',
  ticketId: '',
  misId: '',
  createTime: '',
  updateTime: ''
})

// 处理单个项目选择
const handleItemSelect = (item: any, selected: boolean) => {
  if (selected) {
    selectedCompareRows.value.push(item)
  } else {
    selectedCompareRows.value = selectedCompareRows.value.filter(row => row.ticketId !== item.ticketId)
  }
}

// 合并对话框可见性
const mergeDialogVisible = ref(false)
// 合并表单数据
const mergeFormData = ref({
  title: '',
  content: ''
})
// 合并表单规则
const mergeRules = {
  title: [{ required: true, message: '请输入合并后标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入合并后内容', trigger: 'blur' }]
}
// 合并表单引用
const mergeFormRef = ref(null)

// 加载面板相关变量
const loadingPanel = ref(false)
const loadingPanelInstance = ref<any>(null)

// 合并预览对话框可见性
const mergePreviewDialogVisible = ref(false)
// 合并预览表单数据
const mergePreviewForm = ref({
  title: '',
  content: '',
  taskId: '',
  ticketId: '',
  corpusIdList: [] as string[],
  tagsIds: '' // 新增：标签ID字符串，逗号分隔
})

// 合并预览对话框的标签管理
const mergePreviewSelectedTagIds = ref<string[]>([])

// 处理合并预览标签选择变化
const handleMergePreviewTagSelectionChange = (tagIds: string[]) => {
  // 如果正在初始化，不执行更新逻辑
  if (isMergeInitializing.value) return
  
  // 限制最多选择3个标签
  if (tagIds.length > 3) {
    ElMessage.warning('最多只能选择3个标签')
    tagIds = tagIds.slice(0, 3)
  }
  
  // 过滤掉无效的标签ID（找不到名称的标签）
  const validTagIds = tagIds.filter(tagId => {
    const tagName = getMergeTagNameById(tagId)
    return tagName !== null && tagName !== '加载中...'
  })
  
  // 如果过滤后的标签数量与原来不同，提示用户
  if (validTagIds.length !== tagIds.length) {
    ElMessage.warning(`已过滤掉 ${tagIds.length - validTagIds.length} 个无效标签`)
  }
  
  mergePreviewSelectedTagIds.value = validTagIds
  
  // 更新mergePreviewForm中的tagsIds
  const selectedTags = mergeTagOptions.value.filter(tag => validTagIds.includes(tag.id))
  
  let submitTagsIds: string = ''
  
  if (selectedTags.length === 0 && mergeDefaultTag.value) {
    // 没有选择任何标签时，提交空字符串，后端会处理为默认标签
    submitTagsIds = ''
  } else {
    // 有选择标签时，提交选择的标签ID
    submitTagsIds = validTagIds.join(',')
  }
  
  mergePreviewForm.value.tagsIds = submitTagsIds
  console.log('合并预览标签选择变化:', { validTagIds, submitTagsIds })
}

// 处理合并选中项
const handleMergeSelected = async () => {
  if (selectedCompareRows.value.length < 1) {
    ElMessage.warning('请至少选择一条语料进行合并')
    return
  }

  // 显示加载进度面板，确保compareLoading为false，以显示合并语料相关的提示
  compareLoading.value = false
  loadingPanel.value = true

  try {
    // 构造请求体数据
    const requestData = {
      triggerSource: 2, // 审核页面
      corpusTextList: [formData.value.content], // 添加当前编辑器中的内容
      corpusIdList: selectedCompareRows.value.map(row => row.ticketId), // 选中的ticketId列表
      misId: formData.value.misId, // 用户ID
      rgId: formData.value.rgId, // 值班组ID
      ticketId: formData.value.ticketId || '' // 当前任务的ticketId
    }

    console.log('发送的合并请求数据:', requestData)

    // 第一阶段进度 - 准备合并
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(15)
    }, 1000)

    // 发送合并请求
    const response = await httpRequest.rawRequestPostAsJson(
      API_PATHS.CREATE_MERGE_CORPUS_TASK,
      requestData
    )

    if (response?.code === 0) {
      const taskId = response.data
      console.log('获取到的taskId:', taskId)
      
      // 第二阶段进度 - 任务已创建
      setTimeout(() => {
        loadingPanelInstance.value?.setProgress(30)
      }, 200)
      
      // 轮询任务状态
      let retryCount = 0
      const maxRetries = 100 // 最多轮询100次
      const pollInterval = 1000 // 每秒轮询一次
      
      // 第三阶段进度 - 开始模型合并
      setTimeout(() => {
        loadingPanelInstance.value?.setProgress(45)
      }, 1000)

      // 轮询任务
      const pollTask = async () => {
        if (retryCount >= maxRetries) {
          loadingPanel.value = false
          compareLoading.value = false
          ElMessage.error('任务处理超时，请稍后重试')
          return
        }
        
        try {
          const pollResponse = await httpRequest.rawRequestGet(API_PATHS.QUERY_MODEL_OUTPUT_BY_TASK_ID, {
            taskId
          })
          console.log(`第 ${retryCount + 1} 次轮询结果:`, pollResponse)
          
          // 根据轮询次数增加进度，确保进度值始终增加
          const currentProgress = 45 + Math.min((retryCount / maxRetries) * 45, 45)
          loadingPanelInstance.value?.setProgress(currentProgress)
          
          if (pollResponse?.code === 0) {
            if (pollResponse.data.taskStatus === 1) {
              // 任务成功，显示预览对话框
              loadingPanelInstance.value?.complete()
              setTimeout(async () => {
                loadingPanel.value = false
                compareLoading.value = false
                mergePreviewForm.value = {
                  title: formatTitle(pollResponse.data.title),
                  content: pollResponse.data.content,
                  taskId: pollResponse.data.taskId,
                  ticketId: pollResponse.data.ticketId,
                  corpusIdList: [...selectedCompareRows.value.map(row => row.ticketId)], // 使用选中的ticketId列表
                  tagsIds: pollResponse.data.tagsIds || ''
                }
                
                // 确保标签列表已加载，如果没有标签数据则先获取
                if (mergeTagOptions.value.length === 0 && !isMergeLoadingTags.value) {
                  console.log('合并预览：标签列表为空，正在获取标签列表...', {
                    mergeTagOptionsLength: mergeTagOptions.value.length,
                    isMergeLoadingTags: isMergeLoadingTags.value,
                    formDataRgId: formData.value.rgId,
                    formDataComplete: formData.value
                  })
                  await fetchMergeTagList()
                } else {
                  console.log('合并预览：跳过获取标签列表', {
                    mergeTagOptionsLength: mergeTagOptions.value.length,
                    isMergeLoadingTags: isMergeLoadingTags.value,
                    reason: mergeTagOptions.value.length > 0 ? '标签列表已存在' : '正在加载中'
                  })
                }
                
                // 初始化合并预览的标签选择状态
                if (pollResponse.data.tagsIds) {
                  const tagIds = pollResponse.data.tagsIds.split(',').filter(id => id.trim())
                  // 过滤掉无效的标签ID（找不到名称的标签）
                  const validTagIds = tagIds.filter(tagId => {
                    const tagName = getMergeTagNameById(tagId)
                    return tagName !== null
                  })
                  mergePreviewSelectedTagIds.value = validTagIds
                  console.log('合并预览：初始化标签选择状态', { originalTagIds: tagIds, validTagIds })
                } else {
                  mergePreviewSelectedTagIds.value = []
                  console.log('合并预览：无初始标签，使用空数组')
                }
                
                mergePreviewDialogVisible.value = true
                compareDialogVisible.value = false // 关闭检索对比对话框
              }, 500)
              return
            } else if (pollResponse.data.taskStatus === 2) {
              // 任务失败
              loadingPanel.value = false
              compareLoading.value = false
              ElMessage.error(`合并失败: ${pollResponse.data.taskMessage || '未知错误'}`)
              return
            }
          }
          
          // 继续轮询
          retryCount++
          setTimeout(pollTask, pollInterval)
        } catch (error) {
          loadingPanel.value = false
          compareLoading.value = false
          console.error('轮询出错:', error)
          ElMessage.error(`轮询失败: ${error.message || '未知错误'}`)
        }
      }
      
      // 开始轮询
      setTimeout(pollTask, pollInterval)
    } else {
      loadingPanel.value = false
      compareLoading.value = false
      ElMessage.error(`创建合并任务失败: ${response?.msg || '未知错误'}`)
    }
  } catch (error) {
    loadingPanel.value = false
    compareLoading.value = false
    console.error('合并语料出错:', error)
    ElMessage.error(`合并语料失败: ${error.message || '未知错误'}`)
  }
}

// 处理合并预览取消
const handleMergePreviewCancel = () => {
  mergePreviewDialogVisible.value = false
  
  // 清空预览表单
  mergePreviewForm.value = {
    title: '',
    content: '',
    taskId: '',
    ticketId: '',
    corpusIdList: [],
    tagsIds: ''
  }
  
  // 清空标签选择状态
  mergePreviewSelectedTagIds.value = []
  
  // 清空选中项
  selectedCompareRows.value = []
  
  // 显示提示信息
  ElMessage({
    type: 'info',
    message: '已取消合并操作',
    duration: 3000
  })
}

// 处理合并预览保存
const handleMergePreviewSave = async () => {
  // 显示加载提示
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在保存合并结果...',
    background: 'rgba(255, 255, 255, 0.9)'
  })
  
  try {
    // 构造请求体
    const requestBody = {
      ticketId: mergePreviewForm.value.ticketId,
      title: mergePreviewForm.value.title,
      content: mergePreviewForm.value.content,
      misId: formData.value.misId,
      rgId: formData.value.rgId,
      source: 4, // 4表示合并来源
      type: -1,
      corpusIdList: mergePreviewForm.value.corpusIdList,
      tagsIds: mergePreviewForm.value.tagsIds
    }

    console.log('合并预览保存请求参数:', requestBody)
    const response = await httpRequest.rawRequestPostAsJson(API_PATHS.SAVE_MERGE_CORPUS, requestBody)
    console.log('合并预览保存响应:', response)

    if (response?.code === 0) {
      // 将合并结果应用到当前编辑器
      formData.value.title = mergePreviewForm.value.title
      formData.value.content = mergePreviewForm.value.content
      
      // 同步标签信息到主编辑器
      if (mergePreviewForm.value.tagsIds) {
        formData.value.tagsIds = mergePreviewForm.value.tagsIds
        // 更新主编辑器的标签选择状态
        const tagIds = mergePreviewForm.value.tagsIds.split(',').filter(id => id.trim())
        // 过滤掉无效的标签ID（找不到名称的标签）
        const validTagIds = tagIds.filter(tagId => {
          const tagName = getTagNameById(tagId)
          return tagName !== null
        })
        selectedTagIds.value = validTagIds
      } else {
        formData.value.tagsIds = ''
        selectedTagIds.value = []
      }
      
      // 强制重新渲染编辑器组件
      editorKey.value += 1
      
      // 关闭合并预览对话框
      mergePreviewDialogVisible.value = false
      
      // 清空预览表单
      mergePreviewForm.value = {
        title: '',
        content: '',
        taskId: '',
        ticketId: '',
        corpusIdList: [],
        tagsIds: ''
      }
      
      // 清空标签选择状态
      mergePreviewSelectedTagIds.value = []
      
      // 清空选中项
      selectedCompareRows.value = []
      
      // 关闭加载提示
      loadingInstance.close()
      
      // 显示成功提示
      ElMessage({
        type: 'success',
        message: '合并成功！结果已应用到编辑器，您可以继续编辑或保存修改',
        duration: 5000,
        showClose: true
      })
    } else {
      // 关闭加载提示
      loadingInstance.close()
      ElMessage.error(response?.msg || '保存失败')
    }
  } catch (error) {
    // 关闭加载提示
    loadingInstance.close()
    console.error('合并语料出错:', error)
    ElMessage.error(`合并语料失败: ${error.message || '未知错误'}`)
  }
}

// 处理批量删除选中项
const handleBatchDeleteSelected = () => {
  if (selectedCompareRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的语料')
    return
  }

  ElMessageBox.confirm(
    `是否确认删除选中的 ${selectedCompareRows.value.length} 条语料？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      // 显示加载指示器
      const loadingInstance = ElLoading.service({
        lock: true,
        text: '正在删除...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      // 获取选中行的 ticketId 列表
      const ticketIds = selectedCompareRows.value.map(row => row.ticketId).join(',')
      const deletedTicketIds = selectedCompareRows.value.map(row => row.ticketId)
      
      // 发送删除请求
      const response = await httpRequest.rawRequestPostAsJson(
        `${API_PATHS.DELETE_CORPUS_BY_TICKET_IDS}?misId=${formData.value.misId}&rgId=${formData.value.rgId}&ticketIds=${ticketIds}`,
        null
      )
      
      // 关闭加载指示器
      loadingInstance.close()
      
      if (response?.code === 0) {
        // 从当前结果列表中过滤掉已删除的项
        compareResults.value = compareResults.value.filter(
          item => !deletedTicketIds.includes(item.ticketId)
        )
        
        // 如果缓存存在，也需要更新缓存
        if (similarCorpusCache.value) {
          similarCorpusCache.value.results = similarCorpusCache.value.results.filter(
            item => !deletedTicketIds.includes(item.ticketId)
          )
        }
        
        // 清空选中项
        selectedCompareRows.value = []
        
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
      } else {
        ElMessage.error(`删除失败: ${response?.msg || '未知错误'}`)
      }
    } catch (error) {
      console.error('删除出错:', error)
      ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
    }
  }).catch(() => {
    // 取消删除
  })
}

// 获取任务详情
const fetchTaskDetail = async () => {
  try {
    // 打印一下 taskId，方便调试
    console.log('当前请求的taskId:', taskId.value)
    
    // 清空缓存，因为任务变了，缓存应该失效
    similarCorpusCache.value = null
    
    // 修改请求路径，确保没有重复的部分
    const response = await httpRequest.rawRequestGet(API_PATHS.QUERY_MODEL_OUTPUT_BY_TASK_ID, {
      taskId: taskId.value
    })

    // 打印返回结果，方便调试
    console.log('请求返回结果:', response)

    if (response?.code === 0 && response?.data) {
      // 先准备数据，确保originalContent始终有值
      const originalContent = response.data.originalContent || response.data.content || ''
      const content = response.data.content || originalContent // 如果content为空，使用originalContent
      
      // 更新表单数据 - 将creatorMisId映射到misId
      formData.value = {
        title: response.data.title || '',
        content: content,
        originalContent: originalContent, // 保存原始内容
        taskId: response.data.taskId || '',
        ticketId: response.data.ticketId || '',
        rgId: response.data.rgId || 0,
        misId: response.data.creatorMisId || response.data.misId || route.query.misId || '', // 优先使用creatorMisId
        platformId: response.data.platformId || 0,
        tagsIds: response.data.tagsIds || ''
      }
      
      // 打印更新后的表单数据，确认taskId和misId
      console.log('更新后的表单数据:', {
        taskId: formData.value.taskId,
        misId: formData.value.misId,
        content: formData.value.content.substring(0, 50) + '...'
      })
      
      // 更新key，强制重新渲染编辑器组件
      editorKey.value += 1
      
      taskMissingInfo.value = response.data.taskMissingInfo || []
      
      // 获取标签列表并初始化标签选择状态
      console.log('fetchTaskDetail: 准备初始化标签数据', {
        rgId: formData.value.rgId,
        tagsIds: formData.value.tagsIds
      })
      await initializeTagData()
      console.log('fetchTaskDetail: 标签数据初始化完成', {
        tagOptionsLength: tagOptions.value.length,
        selectedTagIdsLength: selectedTagIds.value.length
      })
    } else {
      ElMessage.error(response?.msg || '获取任务详情失败')
    }
  } catch (error) {
    // 打印具体错误信息，方便调试
    console.error('获取任务详情失败，具体错误:', error)
    ElMessage.error(`获取任务详情失败: ${error.message || '未知错误'}`)
  }
}

// 切换预览状态
const togglePreview = () => {
  isPreview.value = !isPreview.value
}

// 处理检索对比
const handleCompare = async () => {
  if (!formData.value.content.trim()) {
    ElMessage.warning('请先输入内容')
    return
  }

  // 重置选择状态
  selectedCompareRows.value = []
  
  // 输出当前缓存和查询条件，便于调试
  console.log('当前缓存:', similarCorpusCache.value);
  console.log('当前查询条件:', {
    taskId: formData.value.taskId,
    query: formData.value.content
  });
  
  // 检查缓存是否有效
  if (similarCorpusCache.value && 
      similarCorpusCache.value.taskId === formData.value.taskId && 
      similarCorpusCache.value.query === formData.value.content) {
    
    console.log('使用缓存数据');
    
    // 使用缓存数据
    compareResults.value = [...similarCorpusCache.value.results]
    compareDialogVisible.value = true
    
    // 显示提示
    ElMessage({
      type: 'success',
      message: '使用缓存数据，无需重新查询',
      duration: 2000
    })
    
    return
  }
  
  // 显示加载进度面板，同时设置compareLoading为true来控制显示的提示信息
  compareLoading.value = true
  loadingPanel.value = true
  
  // 第一阶段进度 - 准备检索
  setTimeout(() => {
    loadingPanelInstance.value?.setProgress(15)
  }, 500)

  try {
    const requestData = {
      rgId: formData.value.rgId,
      query: formData.value.content
    }

    // 第二阶段进度 - 开始检索处理
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(45)
    }, 1000)

    const response = await httpRequest.rawRequestPostAsJson(API_PATHS.QUERY_SIMILAR_CONTENT_WITH_SCORE, requestData)

    if (response?.code === 0) {
      // 设置进度为75%
      loadingPanelInstance.value?.setProgress(75)
      
      compareResults.value = (response.data?.data || []).map(item => ({
        ...item,
        selected: false, // 添加选中状态属性
        score: (item.score * 100).toFixed(0) + '%',
        type: item.type === null ? '系统故障' : item.type,
        source: sourceMap[item.source] || '其他',
        title: formatTitle(item.title),
        createTime: formatDateTime(item.createTime),
        updateTime: formatDateTime(item.updateTime),
        tagsname: item.tagsname || [] // 新增：确保标签信息能正确传递
      }))
      
      // 保存查询结果到缓存
      similarCorpusCache.value = {
        taskId: formData.value.taskId,
        query: formData.value.content,
        results: [...compareResults.value]
      }
      
      console.log('已将查询结果保存到缓存:', {
        taskId: similarCorpusCache.value.taskId,
        query: similarCorpusCache.value.query.substring(0, 30) + '...',
        resultsCount: similarCorpusCache.value.results.length
      })
      
      // 完成进度
      loadingPanelInstance.value?.complete()
      
      // 延迟关闭加载面板，以便用户看到完成状态
      setTimeout(() => {
        loadingPanel.value = false
        compareLoading.value = false
        
        if (compareResults.value.length === 0) {
          ElMessage.warning('未找到相似语料')
        } else {
          compareDialogVisible.value = true
        }
      }, 500)
    } else {
      loadingPanel.value = false
      compareLoading.value = false
      ElMessage.error(`检索对比失败: ${response?.msg || '未知错误'}`)
    }
  } catch (error) {
    console.error('检索对比出错:', error)
    loadingPanel.value = false
    compareLoading.value = false
    ElMessage.error(`检索对比失败: ${error.message || '未知错误'}`)
  }
}

// 处理检索对比抽屉关闭
const handleCompareClose = () => {
  compareDialogVisible.value = false
  // 清空选中项
  selectedCompareRows.value = []
}

// 获取相似度样式类
const getSimilarityClass = (score: string) => {
  const value = parseInt(score)
  if (value >= 80) return 'high'
  if (value >= 60) return 'medium'
  return 'low'
}

// 保存修改的原始函数
const saveCorpus = async () => {
  try {
    // 表单验证
    if (!formData.value.title.trim()) {
      ElMessage.warning('请输入标题')
      return
    }
    if (!formData.value.content.trim()) {
      ElMessage.warning('请输入内容')
      return
    }
    
    // 验证必要字段
    if (!formData.value.ticketId) {
      ElMessage.warning('缺少ticketId参数，无法保存')
      return
    }
    
    if (!formData.value.misId) {
      ElMessage.warning('缺少misId参数，无法保存')
      return
    }
    
    if (formData.value.rgId === null || formData.value.rgId === undefined) {
      ElMessage.warning('缺少rgId参数，无法保存')
      return
    }

    // 设置保存状态
    isSaving.value = true

    // 显示加载动画
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在保存...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const requestData = {
      misId: formData.value.misId,
      modifiedContent: formData.value.content,
      rgId: formData.value.rgId,
      title: formData.value.title,
      ticketId: formData.value.ticketId,
      platformId: formData.value.platformId,
      taskId: formData.value.taskId,
      tagsIds: formData.value.tagsIds
    }

    console.log('发送保存请求:', requestData)

    // 使用Form Data格式发送请求而不是JSON格式
    const response = await httpRequest.rawRequestPostAsForm(
      API_PATHS.SAVE_MODIFIED_OUTPUT,
      requestData
    )

    // 关闭加载动画
    loadingInstance.close()
    
    // 无论成功与否，都重置保存状态
    isSaving.value = false

    if (response?.code === 0) {
      // 更新当前保存的内容为最新的原始内容，避免下次重新获取覆盖用户修改
      formData.value.originalContent = formData.value.content
      
      ElMessage({
        type: 'success',
        message: '保存成功',
        duration: 3000
      })
      
      // 不再重新获取任务详情，因为会覆盖用户编辑的内容
      // await fetchTaskDetail()
    } else {
      ElMessage({
        type: 'error',
        message: response?.msg || '保存失败',
        duration: 3000
      })
    }
  } catch (error) {
    console.error('保存失败:', error)
    
    // 关闭加载动画并重置保存状态
    isSaving.value = false
    
    // 显示更详细的错误信息
    let errorMessage = '保存失败';
    if (error.response) {
      // 服务器返回了错误状态码
      errorMessage = `保存失败 (${error.response.status}): ${error.response.data?.msg || '未知错误'}`;
      console.error('错误详情:', error.response);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      errorMessage = '服务器无响应，请检查网络连接';
    } else {
      // 请求发送前出错
      errorMessage = `请求错误: ${error.message}`;
    }
    
    ElMessage({
      type: 'error',
      message: errorMessage,
      duration: 5000
    })
  }
}

// 使用防抖包装原始保存函数
const handleSave = debounce(saveCorpus, 300)

// source映射
const sourceMap = {
  0: 'TT/大象群',
  1: '其他',
  2: '手动新增',
  3: '学城',
  4: '合并'
}

// 处理将选中内容应用到编辑器
const handleApplyToEditor = () => {
  if (selectedCompareRows.value.length === 0) {
    ElMessage.warning('请先选择要应用的语料')
    return
  }

  // 以第一条选中的语料作为应用内容
  if (selectedCompareRows.value.length === 1) {
    // 直接应用内容
    formData.value.content = selectedCompareRows.value[0].content
    // 如果标题为空，也应用标题
    if (!formData.value.title.trim()) {
      formData.value.title = selectedCompareRows.value[0].title
    }
    
    ElMessage.success('已应用所选语料内容到编辑器')
  } else {
    // 合并多条语料的内容
    const mergedContent = selectedCompareRows.value.map(item => item.content).join('\n\n---\n\n')
    formData.value.content = mergedContent
    
    // 如果标题为空，应用第一条的标题
    if (!formData.value.title.trim()) {
      formData.value.title = selectedCompareRows.value[0].title
    }
    
    ElMessage.success(`已将${selectedCompareRows.value.length}条语料内容合并应用到编辑器`)
  }
  
  // 更新编辑器
  editorKey.value += 1
  
 // 关闭抽屉
  compareDialogVisible.value = false
}

// 处理查看详情
const handleViewDetail = (item: any) => {
  // 设置当前查看的语料项，确保标签数据格式正确
  currentViewItem.value = { 
    ...item,
    misId: item.misId,
    // 确保 tagsname 是数组格式
    tagsname: Array.isArray(item.tagsname) 
      ? item.tagsname 
      : (typeof item.tagsname === 'string' && item.tagsname 
          ? item.tagsname.split(',').map(tag => tag.trim()).filter(tag => tag) 
          : [])
  }
  
 // 显示对话框
  viewDialogVisible.value = true

  // 在下一个事件循环中等待对话框打开后设置编辑器状态
  setTimeout(() => {
    // 获取编辑器实例
    const editor = document.querySelector('.view-corpus-editor')
    if (editor && editor.__vue__) {
      const editorComponent = editor.__vue__
      // 设置为预览模式
      if (typeof editorComponent.togglePreview === 'function' && !editorComponent.isPreviewMode) {
        editorComponent.togglePreview()
      }
    }
  }, 300)
}

// 标签相关状态
const tagOptions = ref<TagOption[]>([])
const selectedTagIds = ref<string[]>([])
const isLoadingTags = ref(false)
// 默认标签数据
const defaultTag = ref<TagOption | null>(null)
// 标记是否正在初始化，避免初始化时意外触发更新
const isInitializing = ref(false)

// 合并预览专用的标签相关状态
const mergeTagOptions = ref<TagOption[]>([])
const isMergeLoadingTags = ref(false)
const mergeDefaultTag = ref<TagOption | null>(null)
const isMergeInitializing = ref(false)

// 获取标签列表
const fetchTagList = async () => {
  console.log('fetchTagList 被调用，检查 rgId 值:', {
    rgId: formData.value.rgId,
    rgIdType: typeof formData.value.rgId,
    formData: formData.value,
    routeQuery: route.query
  })
  
  // 修改校验逻辑：优先从多个来源获取rgId
  let rgId = formData.value.rgId
  
  // 如果formData中的rgId无效，尝试从URL参数获取
  if (!rgId || rgId === 0) {
    const urlRgId = route.query.rgId as string
    if (urlRgId && urlRgId !== '0') {
      rgId = parseInt(urlRgId)
      console.log('从URL参数中获取到rgId:', rgId)
    }
  }
  
  // 如果还是没有有效的rgId，再尝试从路由参数获取
  if (!rgId || rgId === 0) {
    const routeRgId = route.params.rgId as string
    if (routeRgId && routeRgId !== '0') {
      rgId = parseInt(routeRgId)
      console.log('从路由参数中获取到rgId:', rgId)
    }
  }
  
  // 最后检查，如果仍然没有有效的rgId，则退出
  if (!rgId || rgId === 0) {
    console.warn('fetchTagList 退出：无法获取有效的rgId', {
      formDataRgId: formData.value.rgId,
      urlRgId: route.query.rgId,
      routeRgId: route.params.rgId,
      routeQuery: route.query,
      routeParams: route.params
    })
    ElMessage.warning('缺少值班组信息，无法加载标签列表')
    return
  }
  
  try {
    isLoadingTags.value = true
    console.log('发送标签列表请求:', { rgId })
    
    const response = await httpRequest.rawRequestGet('/rgTags/listTags', {
      rgId: rgId
    })
    
    console.log('标签列表请求响应:', response)
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      // 根据实际API返回格式调整映射
      const formattedTags = response.data.map((tag: any) => {
        const id = tag.id === null ? null : String(tag.id || tag.tagId || tag.value || '')
        const name = tag.name || tag.tagName || tag.label || `标签${id || 'default'}`
        
        return {
          id,
          name
        }
      }).filter(tag => tag.name) // 只过滤掉无名称的数据
      
      console.log('格式化后的标签数据:', formattedTags)
      
      // 设置默认标签（第一个标签，id为null）
      if (formattedTags.length > 0 && formattedTags[0].id === null) {
        defaultTag.value = formattedTags[0]
        // 从可选标签中移除默认标签，因为它会自动显示
        tagOptions.value = formattedTags.slice(1)
        console.log('设置默认标签:', defaultTag.value, '可选标签数量:', tagOptions.value.length)
      } else {
        defaultTag.value = null
        tagOptions.value = formattedTags
        console.log('无默认标签，所有标签数量:', tagOptions.value.length)
      }
      
    } else {
      console.warn('获取标签列表失败:', response?.msg || '数据格式错误', response)
      tagOptions.value = []
      defaultTag.value = null
      ElMessage.warning(`获取标签列表失败: ${response?.msg || '数据格式错误'}`)
    }
  } catch (error) {
    console.error('获取标签列表出错:', error)
    tagOptions.value = []
    defaultTag.value = null
    ElMessage.error(`获取标签列表失败: ${error.message || '网络错误'}`)
  } finally {
    isLoadingTags.value = false
  }
}

// 处理标签选择变化
const handleTagSelectionChange = (tagIds: string[]) => {
  // 如果正在初始化，不执行更新逻辑
  if (isInitializing.value) return
  
  // 限制最多选择3个标签
  if (tagIds.length > 3) {
    ElMessage.warning('最多只能选择3个标签')
    tagIds = tagIds.slice(0, 3)
  }
  
  // 过滤掉无效的标签ID（找不到名称的标签）
  const validTagIds = tagIds.filter(tagId => {
    const tagName = getTagNameById(tagId)
    return tagName !== null && tagName !== '加载中...'
  })
  
  // 如果过滤后的标签数量与原来不同，提示用户
  if (validTagIds.length !== tagIds.length) {
    ElMessage.warning(`已过滤掉 ${tagIds.length - validTagIds.length} 个无效标签`)
  }
  
  // 更新选中的标签ID
  selectedTagIds.value = validTagIds
  
  // 更新formData中的tagsIds
  const selectedTags = tagOptions.value.filter(tag => validTagIds.includes(tag.id))
  
  let submitTagsIds: string = ''
  
  if (selectedTags.length === 0 && defaultTag.value) {
    // 没有选择任何标签时，提交空字符串，后端会处理为默认标签
    submitTagsIds = ''
  } else {
    // 有选择标签时，提交选择的标签ID
    submitTagsIds = validTagIds.join(',')
  }
  
  formData.value.tagsIds = submitTagsIds
}

// 初始化标签数据
const initializeTagData = async () => {
  // 设置初始化标志
  isInitializing.value = true
  
  try {
    // 首先获取标签列表
    await fetchTagList()
    
    // 获取标签列表后，初始化选中状态
    if (formData.value.tagsIds) {
      const tagIds = formData.value.tagsIds.split(',').filter(id => id.trim())
      
      // 过滤掉无效的标签ID（找不到名称的标签）
      const validTagIds = tagIds.filter(tagId => {
        const tagName = getTagNameById(tagId)
        return tagName !== null
      })
      
      // 如果有无效标签被过滤掉，更新 formData
      if (validTagIds.length !== tagIds.length) {
        formData.value.tagsIds = validTagIds.join(',')
        console.warn(`初始化时过滤掉了 ${tagIds.length - validTagIds.length} 个无效标签`)
      }
      
      selectedTagIds.value = validTagIds
    } else {
      selectedTagIds.value = []
    }
  } catch (error) {
    console.error('初始化标签数据失败:', error)
  } finally {
    // 初始化完成，允许正常的标签选择更新
    isInitializing.value = false
  }
}

// 监听合并预览对话框的可见性变化
watch(mergePreviewDialogVisible, async (newVisible) => {
  if (newVisible) {
    console.log('合并预览对话框打开，检查合并预览标签数据状态')
    // 设置初始化标志
    isMergeInitializing.value = true
    
    try {
      // 总是重新获取标签列表，确保数据是最新的
      console.log('合并预览对话框：开始获取标签列表...', {
        currentMergeTagOptionsLength: mergeTagOptions.value.length,
        isMergeLoadingTags: isMergeLoadingTags.value,
        formDataRgId: formData.value.rgId,
        routeQueryRgId: route.query.rgId
      })
      
      await fetchMergeTagList()
      
      console.log('合并预览对话框：标签列表获取完成', {
        mergeTagOptionsLength: mergeTagOptions.value.length,
        mergeDefaultTag: mergeDefaultTag.value
      })
      
      // 如果标签列表为空，给出提示
      if (mergeTagOptions.value.length === 0) {
        console.warn('合并预览对话框：标签列表为空，可能是获取失败或该值班组没有配置标签')
      }
      
      // 强制确保加载状态被重置
      setTimeout(() => {
        if (isMergeLoadingTags.value) {
          console.warn('强制重置isMergeLoadingTags状态')
          isMergeLoadingTags.value = false
        }
      }, 100)
      
    } catch (error) {
      console.error('合并预览对话框：标签列表获取异常', error)
      ElMessage.error('获取标签列表失败，请重试')
    } finally {
      // 完成初始化
      isMergeInitializing.value = false
      
      // 再次确保加载状态被重置
      nextTick(() => {
        if (isMergeLoadingTags.value) {
          console.warn('在finally块中强制重置isMergeLoadingTags状态')
          isMergeLoadingTags.value = false
        }
      })
    }
  } else {
    // 对话框关闭时重置标签状态
    mergePreviewSelectedTagIds.value = []
    isMergeInitializing.value = false
    isMergeLoadingTags.value = false // 确保关闭时重置加载状态
    console.log('合并预览对话框关闭，重置标签状态')
  }
})

// 根据标签ID获取标签名称的辅助函数
const getTagNameById = (tagId: string) => {
  // 如果标签ID为空，返回null
  if (!tagId) {
    return null
  }
  
  // 如果正在加载标签，返回加载提示
  if (isLoadingTags.value) {
    return '加载中...'
  }
  
  // 在所有标签选项中查找
  const foundTag = tagOptions.value.find(tag => tag.id === tagId)
  if (foundTag) {
    return foundTag.name
  }
  
  // 如果找不到标签，返回null（不显示该标签）
  console.warn(`未找到ID为 ${tagId} 的标签，该标签将被忽略`)
  return null
}

// 计算属性：获取有效的标签（过滤掉找不到名称的标签）
const validSelectedTags = computed(() => {
  if (isLoadingTags.value) {
    return []
  }
  
  return selectedTagIds.value
    .map(tagId => ({
      id: tagId,
      name: getTagNameById(tagId)
    }))
    .filter(tag => tag.name !== null) // 过滤掉找不到名称的标签
})

// 在组件挂载时获取任务详情
onMounted(() => {
  if (taskId.value) {
    fetchTaskDetail()
  }
  
  // 添加全局样式以确保确认对话框在编辑器全屏模式上方显示
  const style = document.createElement('style')
  style.textContent = `
    .editor-reset-confirm-dialog {
      z-index: 10001 !important;
    }
    .el-message-box {
      z-index: 9999 !important;
    }
    .el-overlay {
      z-index: 9998 !important;
    }
  `
  document.head.appendChild(style)
  
  // 组件卸载时移除样式
  onBeforeUnmount(() => {
    if (style.parentNode) {
      document.head.removeChild(style)
    }
  })
})

// 获取合并预览的标签列表
const fetchMergeTagList = async () => {
  console.log('fetchMergeTagList 被调用，检查 rgId 值:', {
    rgId: formData.value.rgId,
    rgIdType: typeof formData.value.rgId,
    formData: formData.value,
    routeQuery: route.query
  })
  
  // 修改校验逻辑：优先从多个来源获取rgId
  let rgId = formData.value.rgId
  
  // 如果formData中的rgId无效，尝试从URL参数获取
  if (!rgId || rgId === 0) {
    const urlRgId = route.query.rgId as string
    if (urlRgId && urlRgId !== '0') {
      rgId = parseInt(urlRgId)
      console.log('从URL参数中获取到rgId:', rgId)
    }
  }
  
  // 如果还是没有有效的rgId，再尝试从路由参数获取
  if (!rgId || rgId === 0) {
    const routeRgId = route.params.rgId as string
    if (routeRgId && routeRgId !== '0') {
      rgId = parseInt(routeRgId)
      console.log('从路由参数中获取到rgId:', rgId)
    }
  }
  
  // 最后检查，如果仍然没有有效的rgId，则退出
  if (!rgId || rgId === 0) {
    console.warn('fetchMergeTagList 退出：无法获取有效的rgId', {
      formDataRgId: formData.value.rgId,
      urlRgId: route.query.rgId,
      routeRgId: route.params.rgId,
      routeQuery: route.query,
      routeParams: route.params
    })
    ElMessage.warning('缺少值班组信息，无法加载标签列表')
    return
  }
  
  try {
    isMergeLoadingTags.value = true
    console.log('发送合并预览标签列表请求:', { rgId })
    
    const response = await httpRequest.rawRequestGet('/rgTags/listTags', {
      rgId: rgId
    })
    
    console.log('合并预览标签列表请求响应:', response)
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      console.log('合并预览标签API原始数据:', response.data)
      
      // 根据实际API返回格式调整映射
      const formattedTags = response.data.map((tag: any) => {
        // 更严格的ID处理：如果ID为null，转换为字符串'null'，以便el-select正确处理
        const id = tag.id === null ? 'null' : String(tag.id || tag.tagId || tag.value || '')
        const name = tag.name || tag.tagName || tag.label || `标签${id || 'default'}`
        
        console.log('处理标签项:', { originalTag: tag, processedId: id, processedName: name })
        
        return {
          id,
          name
        }
      }).filter(tag => tag.name) // 只过滤掉无名称的数据
      
      console.log('格式化后的合并预览标签数据:', formattedTags)
      
      // 设置默认标签（第一个标签，id为'null'字符串）
      if (formattedTags.length > 0 && formattedTags[0].id === 'null') {
        mergeDefaultTag.value = formattedTags[0]
        // 合并预览对话框应该显示所有标签，包括默认标签，让用户可以选择
        mergeTagOptions.value = formattedTags
        console.log('设置合并预览默认标签:', mergeDefaultTag.value, '可选标签数量:', mergeTagOptions.value.length)
      } else {
        mergeDefaultTag.value = null
        mergeTagOptions.value = formattedTags
        console.log('无合并预览默认标签，所有标签数量:', mergeTagOptions.value.length)
      }
      
      // 验证最终设置的数据
      console.log('最终合并预览标签设置结果:', {
        mergeTagOptionsLength: mergeTagOptions.value.length,
        mergeTagOptions: mergeTagOptions.value,
        mergeDefaultTag: mergeDefaultTag.value,
        isMergeLoadingTags: isMergeLoadingTags.value
      })
      
    } else {
      console.warn('获取合并预览标签列表失败:', response?.msg || '数据格式错误', response)
      mergeTagOptions.value = []
      mergeDefaultTag.value = null
      ElMessage.warning(`获取标签列表失败: ${response?.msg || '数据格式错误'}`)
    }
  } catch (error) {
    console.error('获取合并预览标签列表出错:', error)
    mergeTagOptions.value = []
    mergeDefaultTag.value = null
    ElMessage.error(`获取标签列表失败: ${error.message || '网络错误'}`)
  } finally {
    isMergeLoadingTags.value = false
    console.log('fetchMergeTagList 执行完成，重置加载状态:', {
      isMergeLoadingTags: isMergeLoadingTags.value,
      mergeTagOptionsLength: mergeTagOptions.value.length,
      mergeDefaultTag: mergeDefaultTag.value
    })
    
    // 强制触发Vue响应式更新
    nextTick(() => {
      console.log('nextTick后的状态验证:', {
        isMergeLoadingTags: isMergeLoadingTags.value,
        mergeTagOptionsLength: mergeTagOptions.value.length,
        shouldShowSelect: !isMergeLoadingTags.value
      })
    })
  }
}

// 根据标签ID获取合并预览标签名称的辅助函数
const getMergeTagNameById = (tagId: string) => {
  // 如果正在加载标签，返回加载提示
  if (isMergeLoadingTags.value) {
    return '加载中...'
  }
  
  // 在合并预览标签选项中查找
  const foundTag = mergeTagOptions.value.find(tag => tag.id === tagId)
  if (foundTag) {
    return foundTag.name
  }
  
  // 特殊处理：如果tagId为空字符串或'null'字符串，且存在默认标签，返回默认标签名称
  if ((!tagId || tagId === 'null') && mergeDefaultTag.value) {
    return mergeDefaultTag.value.name
  }
  
  // 如果找不到标签，返回null（不显示该标签）
  console.warn(`未找到ID为 ${tagId} 的合并预览标签，该标签将被忽略`)
  return null
}

// 处理打开标签管理对话框
const handleOpenTagManagement = () => {
  // 在这里添加打开标签管理对话框的逻辑
  console.log('打开标签管理对话框')
}
</script>

<template>
  <div class="review-detail">
    <div class="page-header">
      <h2>语料审核详情</h2>
      <div class="actions">
        <el-button @click="handleCompare" :disabled="isSaving">检索对比</el-button>
        <el-button type="primary" @click="handleSave" :loading="isSaving" :disabled="isSaving">保存语料</el-button>
      </div>
    </div>

    <div class="page-content">
      <div class="main-content">
        <div class="section">
          <div class="section-header-row">
            <div class="section-title">内容标题</div>
            <ContentQualityScore :content="formData.content" :visible="true" />
          </div>
          <el-input
            v-model="formData.title"
            placeholder="请输入标题"
          />
        </div>

        <div class="section">
          <div class="content-section">
            <div class="edit-area">
              <div class="edit-title-container">
                <div class="edit-title">编辑内容</div>
              </div>
              <!-- Monaco编辑器 -->
              <MonacoEditor
                ref="editorRef"
                v-model="editorContent"
                :key="editorKey"
                language="markdown"
                height="320px"
                class="convert-corpus-editor"
                :title="formData.title"
                :options="{
                  wordWrap: 'on',
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  lineNumbers: 'on',
                  lineDecorationsWidth: 0,
                  folding: true,
                  renderLineHighlight: 'all',
                  automaticLayout: true,
                  readOnly: isPreview
                }"
                @reset="handleEditorReset"
              />
              <!-- 预览模式下显示的内容 -->
              <div v-if="isPreview" class="preview-content">
                <div class="preview-title">预览内容</div>
                <div class="preview-text">{{ formData.content }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="section">
          <div class="section-header-row">
            <div class="section-title">
              <el-icon class="header-icon"><Collection /></el-icon>
              标签管理
            </div>
            <div class="tag-limit-tip">最多选择3个标签</div>
          </div>
          
          <!-- 标签选择器 -->
          <div class="tag-selection-area">
            <!-- 标签数据加载中的状态 -->
            <div v-if="isLoadingTags" class="tag-loading-state">
              <el-skeleton :rows="2" animated>
                <template #default>
                  <div style="margin-bottom: 12px;">
                    <el-skeleton-item variant="text" style="width: 50%" />
                  </div>
                  <div>
                    <el-skeleton-item variant="button" style="width: 100%; height: 32px;" />
                  </div>
                </template>
              </el-skeleton>
            </div>
            
            <!-- 标签数据加载完成后的正常显示 -->
            <template v-else>
              <!-- 显示默认标签提示 -->
              <div v-if="defaultTag && selectedTagIds.length === 0" class="default-tag-display">
                <el-tag type="info" size="default" class="default-tag">
                  {{ defaultTag.name }}（默认标签）
                </el-tag>
                <span class="default-tag-tip">未选择其他标签时将使用默认标签</span>
              </div>
              
              <!-- 标签选择器 -->
              <el-select
                :model-value="selectedTagIds"
                @update:model-value="handleTagSelectionChange"
                multiple
                placeholder="请选择标签，支持多选"
                style="width: 100%"
                filterable
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="2"
              >
                <el-option
                  v-for="tag in tagOptions"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.id"
                />
              </el-select>
              
              <!-- 已选择标签预览 -->
              <div v-if="validSelectedTags.length > 0" class="selected-tags-preview">
                <div class="preview-title">已选择的标签：</div>
                <div class="preview-tags">
                  <el-tag
                    v-for="tag in validSelectedTags"
                    :key="tag.id"
                    type="primary"
                    size="small"
                    class="preview-tag"
                  >
                    {{ tag.name }}
                  </el-tag>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="side-content">
        <div class="missing-info">
          <div class="side-title">
            模型认为缺失的信息 ({{ taskMissingInfo.length }})
          </div>
          <div class="missing-list" v-if="taskMissingInfo.length">
            <div
              v-for="(item, index) in taskMissingInfo"
              :key="index"
              class="missing-item"
            >
              {{ item }}
            </div>
          </div>
          <div v-else class="no-missing">暂无缺失信息</div>
        </div>
      </div>
    </div>

    <!-- 检索对比抽屉 -->
    <el-drawer
      v-model="compareDialogVisible"
      title="相似语料检索结果"
      size="40%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="compare-dialog"
      direction="rtl"
      :modal="false"
      :append-to-body="true"
      :destroy-on-close="false"
      @closed="handleCompareClose"
    >
      <div class="compare-header">
        <div class="compare-tip">
          共找到 {{ compareResults.length }} 条相似内容
          <div class="sub-tip">系统根据语义相似度对语料进行了排序，您可以选择一条或多条语料进行合并或删除操作。</div>
        </div>
      </div>

      <div class="compare-list">
        <div v-for="(item, index) in compareResults" :key="index" class="compare-item">
          <div class="compare-item-header">
            <div class="left-section">
              <el-checkbox 
                v-model="item.selected" 
                @change="(val) => handleItemSelect(item, val)"
              />
              <div class="task-id">{{ item.ticketId }}</div>
              <div class="similarity-score" :class="getSimilarityClass(item.score)">
                相似度 {{ item.score }}
              </div>
            </div>
            <div class="actions">
              <el-button link type="primary" @click="handleViewDetail(item)">
                查看详情
              </el-button>
            </div>
          </div>
          <div class="compare-item-content">
            <div class="content-text">{{ item.content }}</div>
          </div>
          <div class="compare-item-footer">
            <div class="update-time">更新时间：{{ item.updateTime }}</div>
          </div>
        </div>
        <div v-if="compareResults.length === 0" class="no-data">
          暂无相似语料
        </div>
      </div>
      
      <template #footer>
        <div class="drawer-footer">
          <div class="selected-count" v-if="selectedCompareRows.length > 0">
            已选择 {{ selectedCompareRows.length }} 项
          </div>
          <div class="footer-buttons">
            <el-button 
              @click="handleMergeSelected" 
              class="merge-btn"
              :disabled="selectedCompareRows.length < 1"
            >
              <el-icon><Connection /></el-icon>合并语料
            </el-button>
            <el-button 
              @click="handleBatchDeleteSelected" 
              type="danger"
              class="delete-btn"
              :disabled="selectedCompareRows.length === 0"
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
            <el-button @click="compareDialogVisible = false">关闭</el-button>
          </div>
        </div>
      </template>
    </el-drawer>
    
    <!-- 合并语料预览对话框 -->
    <el-dialog
      v-model="mergePreviewDialogVisible"
      title="合并语料预览"
      width="70%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="merge-preview-dialog"
      :modal-append-to-body="true"
      :append-to-body="true"
    >
      <el-form
        ref="mergeFormRef"
        :model="mergePreviewForm"
        :rules="mergeRules"
        label-width="100px"
      >
        <el-form-item label="合并后标题" prop="title">
          <el-input
            v-model="mergePreviewForm.title"
            placeholder="请输入合并后标题"
          />
        </el-form-item>
        
        <!-- 标签管理 -->
        <el-form-item label="标签" class="tags-item">
          <!-- 标签加载中状态 -->
          <div v-if="isMergeLoadingTags" class="tag-loading-state">
            <el-skeleton :rows="1" animated>
              <template #default>
                <el-skeleton-item variant="button" style="width: 100%; height: 32px;" />
              </template>
            </el-skeleton>
            <div style="margin-top: 8px; font-size: 12px; color: #909399;">正在加载标签列表...</div>
          </div>
          
          <!-- 标签加载完成后显示 -->
          <template v-else>
            <!-- 默认标签显示（当没有选择其他标签时） -->
            <div v-if="mergePreviewSelectedTagIds.length === 0 && mergeDefaultTag" class="default-tag-container">
              <el-tag
                :closable="false"
                type="info"
                class="default-tag"
              >
                {{ mergeDefaultTag.name }}
              </el-tag>
              <span style="margin-left: 8px; font-size: 12px; color: #909399;">（默认标签，可在下拉框中选择其他标签）</span>
            </div>
            
            <!-- 标签选择下拉框 -->
            <el-select
              v-model="mergePreviewSelectedTagIds"
              multiple
              :key="`merge-select-${mergeTagOptions.length}-${isMergeLoadingTags}`"
              placeholder="请选择标签（最多3个）"
              style="width: 100%"
              clearable
              :multiple-limit="3"
              @change="handleMergePreviewTagSelectionChange"
              :popper-append-to-body="false"
              :teleported="false"
              popper-class="merge-tag-select-dropdown"
            >
              <!-- 只显示有效ID的标签，排除默认标签 -->
              <el-option
                v-for="tag in mergeTagOptions.filter(t => t.id !== 'null')"
                :key="tag.id"
                :label="tag.name"
                :value="tag.id"
              >
                {{ tag.name }}
              </el-option>
            </el-select>
            
            <!-- 选择状态提示 -->
            <div style="margin-top: 8px; font-size: 12px; color: #909399;">
              <div v-if="mergeTagOptions.filter(t => t.id !== 'null').length > 0">
                共{{ mergeTagOptions.filter(t => t.id !== 'null').length }}个标签可选，已选择{{ mergePreviewSelectedTagIds.length }}/3个
                <span v-if="mergeDefaultTag && mergePreviewSelectedTagIds.length === 0">，当前显示默认标签</span>
              </div>
              <div v-else style="color: #f56c6c;">
                暂无可选标签，请检查值班组是否配置了标签
              </div>
            </div>
          </template>
        </el-form-item>
        
        <el-form-item label="合并后内容" prop="content">
          <MonacoEditor
            v-model="mergePreviewForm.content"
            language="markdown"
            height="400px"
            class="merge-preview-editor"
            :options="{
              wordWrap: 'on',
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              lineNumbers: 'on',
              lineDecorationsWidth: 0,
              folding: true,
              renderLineHighlight: 'all',
              automaticLayout: true
            }"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleMergePreviewCancel">取消</el-button>
          <el-button type="primary" @click="handleMergePreviewSave">确认合并</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 语料处理加载进度面板 -->
    <LoadingProgressPanel
      v-model:visible="loadingPanel"
      ref="loadingPanelInstance"
      :title="compareLoading ? '相似语料检索中' : '请求处理中'"
      message="系统正在处理您的请求..."
      :tips="compareLoading ? [
        '正在检索相似的语料',
        '请耐心等待，处理完成后会自动显示结果'
      ] : [
        '大模型正在分析内容，预计需要30-60秒',
        '根据内容复杂度和系统负载，等待时间可能会有所不同',
        '请耐心等待，处理完成后会自动显示结果'
      ]"
      :duration="30000"
    />

    <!-- 查看详情对话框 -->
    <ViewCorpusDialog
      v-model="viewDialogVisible"
      :corpus-data="currentViewItem"
    />
  </div>
</template>

<style lang="scss" scoped>
.review-detail {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #303133;
    }

    .actions {
      display: flex;
      gap: 12px;
    }
  }

  .page-content {
    flex: 1;
    display: flex;
    gap: 24px;
    min-height: 0;

    .main-content {
      flex: 1;
      min-width: 0;

      .section {
        margin-bottom: 24px;

        .section-header-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
        
        .section-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
            display: flex;
            align-items: center;
            gap: 8px;
            
            .header-icon {
              color: #409EFF;
              font-size: 18px;
            }
          }
          
          .tag-limit-tip {
            background: #ecf5ff;
            color: #409EFF;
            border: 1px solid #b3d8ff;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
          }
        }

        .content-section {
          margin-top: 20px;

          .edit-area {
            background: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 16px;

            .edit-title-container {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
            }

            .edit-title {
              font-size: 14px;
              font-weight: 500;
              color: #409EFF;
            }

            .edit-controls {
              display: flex;
              align-items: center;

              .ml-2 {
                margin-left: 8px;
              }
            }

            .convert-corpus-editor {
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              display: block;
            }

            .preview-content {
              margin-top: 16px;
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              padding: 16px;
              background-color: #fafafa;

              .preview-title {
                font-size: 14px;
                font-weight: 500;
                color: #67c23a;
                margin-bottom: 12px;
              }

              .preview-text {
                font-size: 14px;
                line-height: 1.6;
                color: #606266;
                white-space: pre-wrap;
                word-break: break-all;
              }
            }
          }
        }
      }
    }

    .side-content {
      width: 280px;
      flex-shrink: 0;

      .missing-info {
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 16px;

        .side-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 16px;
        }

        .missing-list {
          .missing-item {
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #fdf6ec;
            border-radius: 4px;
            color: #e6a23c;
            font-size: 14px;
            line-height: 1.4;
            border-left: 3px solid #e6a23c;
          }
        }

        .no-missing {
          color: #e6a23c;
          font-size: 14px;
          text-align: center;
          padding: 20px 0;
          background: #fdf6ec;
          border-radius: 4px;
        }
      }
    }
  }
}

/* 检索对比抽屉样式 */
.compare-dialog {
  :deep(.el-drawer__header) {
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  :deep(.el-drawer__body) {
    padding: 20px;
    overflow-y: auto;
  }

  :deep(.el-drawer__footer) {
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .drawer-footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .selected-count {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }

    .footer-buttons {
      display: flex;
      gap: 12px;
      
      .merge-btn {
        &.is-disabled {
          color: #c0c4cc;
          background-color: #fff;
          border-color: #e4e7ed;
        }
      }
      
      .delete-btn {
        &.is-disabled {
          color: #c0c4cc;
          background-color: #fff;
          border-color: #e4e7ed;
        }
      }
    }
  }

  .compare-header {
    margin-bottom: 20px;

    .compare-tip {
      font-size: 16px;
      color: #303133;
      line-height: 1.6;

      .sub-tip {
        margin-top: 8px;
        font-size: 14px;
        color: #909399;
        line-height: 1.5;
      }
    }
  }

  .compare-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding-bottom: 60px;

    .compare-item {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      border: 1px solid #e4e7ed;

      .compare-item-header {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;

        .left-section {
          display: flex;
          align-items: center;
          gap: 12px;

          :deep(.el-checkbox) {
            margin-right: 0;
          }

          .task-id {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
          }
        }

        .similarity-score {
          display: inline-block;
          padding: 2px 12px;
          border-radius: 12px;
          font-weight: 500;
          font-size: 13px;
          
          &.high {
            background-color: #e8f5e9;
            color: #4caf50;
          }
          
          &.medium {
            background-color: #fff3e0;
            color: #ff9800;
          }
          
          &.low {
            background-color: #ffebee;
            color: #f44336;
          }
        }

        .actions {
          .el-button {
            font-size: 13px;
            color: #409eff;
          }
        }
      }

      .compare-item-content {
        padding: 16px;
        
        .content-text {
          font-size: 14px;
          line-height: 1.6;
          color: #303133;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }
      }

      .compare-item-footer {
        padding: 12px 16px;
        border-top: 1px solid #f0f0f0;
        background: #fafafa;

        .update-time {
          font-size: 13px;
          color: #909399;
        }
      }
    }

    .no-data {
      text-align: center;
      padding: 40px 0;
      color: #909399;
      font-size: 14px;
    }
  }
}

/* 合并预览对话框样式 */
.merge-preview-dialog {
  z-index: 2000;
  
  :deep(.el-dialog) {
    z-index: 2000;
  }
  
  :deep(.el-dialog__wrapper) {
    z-index: 2000;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #303133;
  }

  .edit-area {
    width: 100%;
    margin-bottom: 16px;
  }
  
  /* 标签管理样式 */
  .tags-item {
    margin-bottom: 16px;
    
    .tag-loading-state {
      padding: 8px 0;
      
      .el-skeleton {
        margin-bottom: 8px;
      }
    }
    
    .default-tag-container {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      
      .default-tag {
        background-color: #f4f4f5;
        border-color: #d3d4d6;
        color: #606266;
        font-size: 12px;
        
        &:hover {
          background-color: #f4f4f5;
          border-color: #d3d4d6;
        }
      }
    }
    
    :deep(.el-form-item__label) {
      color: #606266;
      font-weight: 600;
      font-size: 14px;
      min-width: 80px;
      white-space: nowrap;
      margin-right: 8px;
    }
    
    :deep(.el-form-item__content) {
      flex: 1;
      margin-left: 0 !important;
    }
    
    :deep(.el-select) {
      width: 100%;
      
      .el-select__wrapper {
        width: 100%;
      }
      
      .el-select__tags {
        flex-wrap: wrap;
        max-width: 100%;
      }
      
      .el-tag {
        margin: 2px 4px 2px 0;
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
        font-size: 12px;
        
        &:hover {
          background-color: #bae7ff;
          border-color: #69c0ff;
        }
        
        .el-tag__close {
          background-color: rgba(24, 144, 255, 0.1);
          color: #1890ff;
          border-radius: 50%;
          
          &:hover {
            background-color: #1890ff;
            color: #fff;
          }
        }
      }
    }
  }
}

/* 确保下拉框选项有足够高的z-index */
:deep(.el-select-dropdown) {
  z-index: 3000 !important;
}

:deep(.merge-tag-select-dropdown) {
  z-index: 3000 !important;
}

/* 确保所有popper类型的下拉框都有足够高的z-index */
:deep(.el-popper) {
  z-index: 3000 !important;
}

/* 合并预览对话框编辑器样式 */
.merge-preview-editor {
  width: 100%;
  min-height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  
  :deep(.monaco-editor-wrapper) {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    height: 100%;
  }
  
  :deep(.monaco-editor) {
    width: 100% !important;
  }
  
  :deep(.overflow-guard) {
    width: 100% !important;
  }
}

/* 确保确认对话框显示在最上层 */
:deep(.editor-reset-confirm-dialog) {
  z-index: 10001 !important;
}

/* 查看详情对话框样式 */
.view-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 30px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #303133;
    font-size: 14px;
  }

  :deep(.el-input__wrapper) {
    background-color: #f5f7fa;
    box-shadow: none !important;
    cursor: default;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }

  :deep(.el-input__inner) {
    color: #606266;
    font-size: 13px;
    cursor: default;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* 查看语料对话框中Monaco编辑器的样式 */
.view-corpus-editor {
  width: 100%;
  min-height: 320px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  
  :deep(.editor-toolbar) {
    .reset-btn {
      display: none !important; // 强制隐藏重置按钮
    }
  }
  
  :deep(.monaco-editor-wrapper) {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
}

/* 标签管理区域样式 */
.tag-selection-area {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .tag-loading-state {
    padding: 16px;
    background: #fafafa;
    border: 1px solid #ebeef5;
    border-radius: 8px;
  }

  .default-tag-display {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e1ecff 100%);
    border-radius: 8px;
    border: 1px solid #b3d8ff;

    .default-tag {
      flex-shrink: 0;
      background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
      border-color: #409EFF;
      color: #fff;
      font-weight: 500;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
    }

    .default-tag-tip {
      font-size: 13px;
      color: #606266;
      font-weight: 500;
    }
  }

  .selected-tags-preview {
    padding: 12px 16px;
    background: #f0f4ff;
    border: 1px solid #d9ecff;
    border-radius: 8px;

    .preview-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
    }

    .preview-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .preview-tag {
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
        font-size: 12px;
      }
    }
  }

  :deep(.el-select) {
    .el-select__wrapper {
      border-radius: 8px;
      border: 2px solid #e4e7ed;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #c0c4cc;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      &.is-focused {
        border-color: #409EFF;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }

    .el-tag {
      margin: 2px 4px 2px 0;
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
      font-size: 12px;

      &:hover {
        background-color: #bae7ff;
        border-color: #69c0ff;
      }

      .el-tag__close {
        background-color: rgba(24, 144, 255, 0.1);
        color: #1890ff;
        border-radius: 50%;

        &:hover {
          background-color: #1890ff;
          color: #fff;
        }
      }
    }
  }
}
</style> 