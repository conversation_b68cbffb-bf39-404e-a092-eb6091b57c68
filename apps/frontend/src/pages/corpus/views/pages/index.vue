<script setup lang="ts">
import { ref, onMounted, computed, watch, onUnmounted, nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import httpRequest from '@/utils/httpRequest'
import { API_PATHS } from '../../request/api'
import { getCurrentUser } from '@/shared/services/userService'
import { formatDateTime, formatTitle } from '../../utils/format'
import { useRouter } from 'vue-router'
import { useRoute } from 'vue-router'
import { debounce } from '../../utils/debounce'
import {
  Search,
  Delete,
  Edit,
  View,
  Plus,
  Refresh,
  Search as SearchIcon,
  Document,
  Warning,
  WarningFilled,
  CircleCheck,
  CircleClose,
  CircleCheckFilled,
  CircleCloseFilled,
  Loading,
  MoreFilled,
  FullScreen,
  Minus,
  Close,
  Connection,
  QuestionFilled,
  List,
  Setting,
  Download,
  Reading
} from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import MonacoEditor from '../components/MonacoEditor.vue'
import LoadingProgressPanel from '../components/LoadingProgressPanel.vue'
import ViewCorpusDialog from '../components/ViewCorpusDialog.vue'
import EditCorpusDialog from '../components/EditCorpusDialog.vue'
import ConvertCorpusDialog from '../components/ConvertCorpusDialog.vue'
import TaskListDialog from '../components/TaskListDialog.vue'
import MessageNotification from '../components/MessageNotification.vue'
import ContentQualityScore from '../components/ContentQualityScore.vue'
import { initMonacoEditor } from '../../utils/monaco-loader'
import { 
  CorpusItem, 
  CorpusResponse, 
  RGListItem, 
  TTItem, 
  CORPUS_TABLE_COLUMNS, 
  SOURCE_MAP,
  TT_TABLE_COLUMNS,
  COMPARE_TABLE_COLUMNS,
  KB_STATUS_MAP,
  FORM_RULES,
  MERGE_FORM_RULES,
  EDIT_FORM_RULES,
  PAGE_SIZES
} from '../../types'
import { handleConvertToCorpus as convertCorpus } from '../../utils/corpusConverter'
import { batchDeleteCorpus, deleteCorpus, deleteCompareCorpus, searchSimilarCorpus, mergeSelectedCorpus } from '../../utils/corpusManager'
import TaskBadge from '../components/TaskBadge.vue'
import { isDev, isTest } from '@/shared/utils/env-helper'
import TTFilterPanel from '../components/TTFilterPanel.vue'
import CustomWorkSpace from '../components/CustomWorkSpace.vue'
import AdvancedSearch from '../components/AdvancedSearch.vue'
import AddCorpusDialog from '../components/AddCorpusDialog.vue'

// 初始化Monaco Editor配置
initMonacoEditor()

// 格式化内容显示，将Markdown格式转换为易读的纯文本
const formatContentForDisplay = (content: string): string => {
  if (!content) return ''
  
  return content
    // 移除Markdown标题标记
    .replace(/^#{1,6}\s+/gm, '')
    // 移除粗体标记
    .replace(/\*\*(.*?)\*\*/g, '$1')
    // 移除斜体标记
    .replace(/\*(.*?)\*/g, '$1')
    // 移除代码块标记
    .replace(/```[\s\S]*?```/g, '[代码块]')
    // 移除行内代码标记
    .replace(/`([^`]+)`/g, '$1')
    // 移除链接标记，保留链接文本
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // 移除图片标记
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '[图片: $1]')
    // 将多个连续的换行符替换为单个空格
    .replace(/\n+/g, ' ')
    // 移除多余的空格
    .replace(/\s+/g, ' ')
    // 去除首尾空格
    .trim()
}

// 任务轮询计时器
const taskPollingTimer = ref<number | null>(null)

// 设置组件大小
const large = 'default'

// 本地存储的键名
const STORAGE_KEY = 'corpus_current_team'

// 保存值班组选择到本地存储
const saveTeamToLocalStorage = (teamId: number) => {
  localStorage.setItem(STORAGE_KEY, String(teamId))
}

// 从本地存储获取值班组选择
const getTeamFromLocalStorage = (): number | null => {
  const savedTeam = localStorage.getItem(STORAGE_KEY)
  return savedTeam ? Number(savedTeam) : null
}

// 值班组选项
const shiftTeams = ref<{ label: string; value: number }[]>([])

// 当前选中的值班组
const currentTeam = ref<number>()

// 在 setup 中添加 currentMisId 的响应式引用
const currentMisId = ref<string>('')
// 添加 currentEmpId 的响应式引用
const currentEmpId = ref<string>('')

// 初始化Vue Router
const router = useRouter()
const route = useRoute()

// 初始化时获取用户信息
const initUserInfo = async () => {
  try {
    console.log('开始获取用户信息...')
    const userInfo = await getCurrentUser()
    console.log('获取到的用户信息:', userInfo)
    
    if (!userInfo) {
      console.error('userInfo 为空')
      ElMessage.error('获取用户信息失败：用户信息为空')
      return
    }
    
    if (!userInfo.login) {
      console.error('userInfo.login 为空:', userInfo)
      ElMessage.error('获取用户信息失败：用户登录信息缺失')
      return
    }

    currentMisId.value = userInfo.login
    currentEmpId.value = userInfo.id
    console.log('设置 currentMisId:', currentMisId.value)
    console.log('设置 currentEmpId:', currentEmpId.value)
    
    // 确保在设置完 misId 后再调用 fetchRGList
    if (currentMisId.value) {
      await fetchRGList()
      
      // 获取URL中的rgId参数
      const urlParams = new URLSearchParams(window.location.search)
      const rgIdFromUrl = urlParams.get('rgId')
      
      if (rgIdFromUrl) {
        const rgIdNum = Number(rgIdFromUrl)
        // 验证rgId是否有效且存在于值班组列表中
        if (!isNaN(rgIdNum) && rgIdNum > 0 && shiftTeams.value.some(team => team.value === rgIdNum)) {
          // 保存到本地存储
          saveTeamToLocalStorage(rgIdNum)
          // 设置为当前值班组
          currentTeam.value = rgIdNum
          console.log('从URL设置值班组:', rgIdNum)
        } else {
          console.warn('URL中的rgId无效或不在值班组列表中:', rgIdFromUrl)
        }
      } else {
        // URL中没有rgId参数，尝试从本地存储获取
        const savedTeam = getTeamFromLocalStorage()
        // 确认该值班组仍然存在于当前值班组列表中
        if (savedTeam && shiftTeams.value.some(team => team.value === savedTeam)) {
          // 设置当前值班组
          currentTeam.value = savedTeam
          console.log('从本地存储设置值班组:', savedTeam)
        }
      }
    } else {
      console.error('初始化失败：无法获取 misId')
      ElMessage.error('初始化失败：无法获取用户标识')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error(`获取用户信息失败: ${error.message || '网络错误'}`)
    
    // 可以在这里添加重试逻辑
    setTimeout(() => {
      console.log('5秒后重试获取用户信息...')
      initUserInfo()
    }, 5000)
  }
}

// 立即执行初始化
initUserInfo()

// 移除 onMounted 中的用户信息获取逻辑
onMounted(async () => {
  // 检查是否需要自动打开标签管理
  if (route.query.openTagManagement === 'true') {
    // 等待组件完全加载后再打开标签管理
    nextTick(() => {
      customWorkSpaceDefaultTab.value = 'tagManagement'
      customWorkSpaceVisible.value = true
      
      // 清除查询参数，避免刷新页面时重复打开
      router.replace({
        path: route.path,
        query: {
          ...route.query,
          openTagManagement: undefined
        }
      })
    })
  }
})

// 获取值班组列表
const fetchRGList = async () => {
  if (!currentMisId.value) {
    console.warn('misId 未获取到，暂不发送请求。当前 currentMisId:', currentMisId.value)
    return
  }

  try {
    console.log('开始获取值班组列表，使用 misId:', currentMisId.value)
    const res = await httpRequest.rawRequestGet(API_PATHS.QUERY_RG_LIST, {
      misId: currentMisId.value,
      pageNum: 1,
      pageSize: 1000
    })
    
    console.log('值班组列表接口响应:', res)
    
    if (res.code === 0) {
      shiftTeams.value = res.data.map((item: RGListItem) => ({
        label: item.name,
        value: item.id
      }))
      console.log('获取到值班组列表:', shiftTeams.value)
    } else {
      console.warn('获取值班组列表失败:', res.message || res.msg)
      ElMessage.warning(`获取值班组列表失败: ${res.message || res.msg || '未知错误'}`)
    }
  } catch (error) {
    console.error('获取值班组列表出错:', error)
    ElMessage.error(`获取值班组列表失败: ${error.message || '网络错误'}`)
  }
}

// 修改组件挂载时的逻辑，移除重复的从本地存储获取值班组的代码
onMounted(() => {
  // 其他初始化代码...
})

// 搜索关键词
const searchKeyword = ref('')

// 原始表格数据备份
const originalTableData = ref<CorpusItem[]>([])

// 表格数据
const tableData = ref<CorpusItem[]>([])

// 选中的行
const selectedRows = ref<CorpusItem[]>([])

// 表格列配置
const columns = CORPUS_TABLE_COLUMNS

// 新增语料对话框可见性
const dialogVisible = ref(false)

// 合并语料对话框可见性
const mergeDialogVisible = ref(false)

// 合并语料表单数据
const mergeFormData = ref({
  title: '',
  content: ''
})

// 合并表单规则
const mergeRules = MERGE_FORM_RULES

// 合并表单ref
const mergeFormRef = ref<FormInstance>()

// 检索对比对话框可见性
const compareDialogVisible = ref(false)

// 自定义工作空间对话框可见性
const customWorkSpaceVisible = ref(false)

// 检索结果数据
const compareResults = ref<CorpusItem[]>([])

// 检索结果选中的行
const selectedCompareRows = ref<CorpusItem[]>([])

// 检索结果列配置
const compareColumns = COMPARE_TABLE_COLUMNS

// 查看语料对话框可见性
const viewDialogVisible = ref(false)

// 当前查看的语料数据
const currentViewItem = ref<CorpusItem>({
  ticketId: '',
  title: '',
  content: '',
  type: 0,
  source: 0,
  misId: '',
  updateTime: '',
  createTime: '',
  backgroundKnowledge: '',
  sop: '',
  rule: ''
})

// 编辑语料对话框可见性
const editDialogVisible = ref(false)

// 编辑语料表单数据
const editFormData = ref({
  title: '',
  content: '',
  tagsname: [],
  tagsIds: ''
})

// 编辑表单ref
const editFormRef = ref<FormInstance>()

// 当前编辑的语料ID
const currentEditId = ref<number>(0)

// TT群转语料对话框可见性
const ttDialogVisible = ref(false)

// TT对话框全屏状态
const ttDialogFullscreen = ref(false)

// 转换语料对话框可见性
const convertDialogVisible = ref(false)

// 转换语料表单数据
const convertFormData = ref({
  title: '',
  content: '',
  originalContent: '', // 添加原始内容字段
  taskId: '',
  ticketId: '',
  rgId: 0,
  tagsIds: '', // 新增：标签ID字符串，逗号分隔
  taskMissingInfo: [] as string[] // 新增：模型认为缺失的信息
})

// TT列表数据
const ttTableData = ref<TTItem[]>([])

// TT列表分页
const ttPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// TT列表过滤条件
const ttFilterData = ref({
  createdAtStart: null,
  createdAtEnd: null,
  isFiltered: true  // 默认为筛选后
})

// 在组件挂载时设置默认过滤条件
onMounted(() => {
  // 设置默认时间：开始时间为7天前，结束时间为当前时间
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)
  
  // 设置开始时间为当天的 00:00:00.000
  startDate.setHours(0, 0, 0, 0)
  
  // 设置结束时间为当天的 23:59:59.999
  endDate.setHours(23, 59, 59, 999)
  
  ttFilterData.value = {
    createdAtStart: startDate.getTime(),  // 毫秒时间戳
    createdAtEnd: endDate.getTime(),      // 毫秒时间戳
    isFiltered: true  // 默认为筛选后
  }
  
  console.log('初始化时间范围:', {
    startDate: new Date(ttFilterData.value.createdAtStart).toLocaleString(),
    endDate: new Date(ttFilterData.value.createdAtEnd).toLocaleString(),
    startTimestamp: ttFilterData.value.createdAtStart,
    endTimestamp: ttFilterData.value.createdAtEnd
  })
})

// 使用计算属性创建一个键，用于重新渲染过滤器面板
const ttFilterKey = computed(() => ttDialogVisible.value ? 'visible' : 'hidden')

// TT列表加载状态
const ttLoading = ref(false)

// 状态映射
const kbStatusMap = KB_STATUS_MAP

// 计算选中的语料数量

// 计算当前页显示的数据
const currentPageTTData = computed(() => {
  const start = (ttPagination.value.currentPage - 1) * ttPagination.value.pageSize
  const end = start + ttPagination.value.pageSize
  return ttTableData.value.slice(start, end)
})

// TT列表列配置
const ttColumns = TT_TABLE_COLUMNS

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10) // 每页显示条数
const pageSizes = PAGE_SIZES
const total = ref(0)

// 高级搜索参数
const advancedSearchParams = ref({
  ticketId: '',
  title: '',
  content: '',
  source: null,
  creator: '',
  startTime: '',
  endTime: '',
  tagsIds: '' // 新增：标签ID字符串，逗号分隔
})

// 获取语料列表
const fetchCorpusList = async () => {
  if (!currentTeam.value) return
  if (!currentMisId.value) {
    ElMessage.error('未获取到用户信息，请刷新页面重试')
    return
  }

  // 设置加载状态
  tableLoading.value = true

  try {
    // 构建请求参数
    const params: Record<string, any> = {
      rgId: currentTeam.value,
      misId: currentMisId.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      strMatch: searchKeyword.value || ''
    }
    
    // 添加高级搜索参数
    if (advancedSearchParams.value.ticketId) {
      params.ticketId = advancedSearchParams.value.ticketId
    }
    if (advancedSearchParams.value.title) {
      params.title = advancedSearchParams.value.title
    }
    if (advancedSearchParams.value.content) {
      params.content = advancedSearchParams.value.content
    }
    if (advancedSearchParams.value.source !== null) {
      params.source = advancedSearchParams.value.source
    }
    if (advancedSearchParams.value.creator) {
      params.creator = advancedSearchParams.value.creator
    }
    if (advancedSearchParams.value.startTime) {
      params.startTime = advancedSearchParams.value.startTime
    }
    if (advancedSearchParams.value.endTime) {
      params.endTime = advancedSearchParams.value.endTime
    }
    // 新增：添加标签筛选参数
    if (advancedSearchParams.value.tagsIds) {
      params.tagsIds = advancedSearchParams.value.tagsIds
    }
    
    console.log('发送分页请求，参数：', params)

    const response = await httpRequest.rawRequestGet(API_PATHS.QUERY_CORPUS_LIST, params)
    
    console.log('获取语料列表原始响应：', response)
    
    if (response?.code === 0 && response?.data) {
      tableData.value = response.data.list.map(item => ({
        ...item,
        type: item.type === -1 ? '系统故障' : item.type,
        source: SOURCE_MAP[item.source] || '其他',
        title: formatTitle(item.title),
        updateTime: formatDateTime(item.updateTime),
        createTime: formatDateTime(item.createTime)
      }))

      total.value = response.data.total || 0
      
      // 如果搜索无结果，显示提示
      if ((searchKeyword.value || Object.values(advancedSearchParams.value).some(v => v !== '' && v !== null)) && tableData.value.length === 0) {
        ElMessage.warning('未找到匹配的语料数据，请尝试其他关键词')
      }
    } else {
      ElMessage.error(`获取语料列表失败: ${response?.msg || '未知错误'}`)
    }
  } catch (error) {
    console.error('获取语料列表出错:', error)
    ElMessage.error('获取语料列表失败')
  } finally {
    // 无论成功失败，都需要关闭加载状态
    tableLoading.value = false
  }
}

// 处理页码改变
const handleCurrentChange = (val: number) => {
  // 计算最大页数
  const maxPage = Math.ceil(total.value / pageSize.value)
  // 如果输入的页码超过最大页数，则使用最大页数
  currentPage.value = val > maxPage ? maxPage : val
  fetchCorpusList()
}

// 创建防抖版本的页码改变函数
const debouncedHandleCurrentChange = debounce(handleCurrentChange, 300)

// 处理每页条数改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  // 重置为第一页
  currentPage.value = 1
  fetchCorpusList()
}

// 创建防抖版本的每页条数改变函数
const debouncedHandleSizeChange = debounce(handleSizeChange, 300)

// 监听值班组选择变化
watch(currentTeam, async (newValue) => {
  if (newValue) {
    // 保存选择到本地存储
    saveTeamToLocalStorage(newValue)
    
    // 检查工作空间权限
    await checkWorkspacePermission(newValue)
    
    currentPage.value = 1 // 重置到第一页
    fetchCorpusList()
  } else {
    // 当未选择值班组时，清空表格数据
    tableData.value = []
    originalTableData.value = []
    selectedRows.value = []
    total.value = 0
    
    // 清除本地存储
    localStorage.removeItem(STORAGE_KEY)
  }
})

// 检查工作空间权限
const hasWorkspacePermission = ref(true)
const workspacePermissionMessage = ref('')

const checkWorkspacePermission = async (rgId: number) => {
  try {
    if (!currentMisId.value) {
      console.error('无法获取用户misId，跳过权限检查')
      return
    }

    // 调用权限检查API
    const response = await httpRequest.rawRequestGet(API_PATHS.WORKSPACE_CHECK_PERMISSION, {
      modifier: currentMisId.value,
      rgId: rgId
    })
    
    console.log('工作空间权限检查结果:', response)
    
    // 检查响应数据
    if (response && response.code === 0) {
      hasWorkspacePermission.value = response.data.hasPermission
      
      // 如果没有权限，显示警告提示
      if (!hasWorkspacePermission.value) {
        // 显示警告提示
        ElMessage.warning({
          message: '当前值班组未绑定自定义工作空间，可能影响部分功能使用',
          duration: 5000
        })
      }
    } else {
      console.error('工作空间权限检查API返回错误:', response)
    }
  } catch (error) {
    console.error('检查工作空间权限失败:', error)
  }
}

// 处理搜索
const handleSearch = () => {
  if (!currentTeam.value) {
    ElMessage.warning('请先选择值班组')
    return
  }
  
  // 清空高级搜索参数
  advancedSearchParams.value = {
    ticketId: '',
    title: '',
    content: '',
    source: null,
    creator: '',
    startTime: '',
    endTime: '',
    tagsIds: '' // 新增：重置标签搜索参数
  }
  
  currentPage.value = 1 // 重置到第一页
  fetchCorpusList() // 重新获取数据
}

// 创建防抖版本的处理搜索函数
const debouncedHandleSearch = debounce(handleSearch, 300)

// 监听输入框回车事件
const handleKeywordKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    debouncedHandleSearch()
  }
}

// 处理手动新增
const handleManualAdd = () => {
  dialogVisible.value = true
  // 在下一个tick中调用子组件的初始化方法
  nextTick(() => {
    addCorpusDialogRef.value?.initForm()
  })
  ;(document.activeElement as HTMLElement)?.blur()
}

// 创建防抖版本的手动新增函数
const debouncedHandleManualAdd = debounce(handleManualAdd, 300)

// 获取TT列表
const fetchTTList = async (autoNextPage = true) => {
  if (!currentTeam.value) {
    ElMessage.error('请先选择值班组')
    return
  }

  ttLoading.value = true

  try {
    // 确保每次请求都有默认的时间参数
    if (!ttFilterData.value.createdAtStart || !ttFilterData.value.createdAtEnd) {
      // 设置默认的时间范围
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - 7)
      
      // 设置开始时间为当天的 00:00:00.000
      startDate.setHours(0, 0, 0, 0)
      
      // 设置结束时间为当天的 23:59:59.999
      endDate.setHours(23, 59, 59, 999)
      
      ttFilterData.value.createdAtStart = startDate.getTime()
      ttFilterData.value.createdAtEnd = endDate.getTime()
    }

    const requestData = {
      misId: [currentMisId.value],
      rgId: [String(currentTeam.value)],
      pageNum: ttPagination.value.currentPage,
      pageSize: ttPagination.value.pageSize,
      // 始终添加时间筛选参数
      createdAtStart: ttFilterData.value.createdAtStart,
      createdAtEnd: ttFilterData.value.createdAtEnd
    }

    console.log('发送的请求数据:', requestData)
    console.log('时间范围:', {
      startDate: new Date(requestData.createdAtStart).toLocaleString(),
      endDate: new Date(requestData.createdAtEnd).toLocaleString(),
      isFiltered: ttFilterData.value.isFiltered
    })

    // 根据筛选方式选择不同的API接口
    const apiPath = ttFilterData.value.isFiltered 
      ? API_PATHS.QUERY_TT_LIST      // 筛选后
      : API_PATHS.QUERY_TT_LIST_ALL  // 筛选前

    // 使用 rawRequestPostAsForm 方法发送请求
    const response = await httpRequest.rawRequestPostAsForm(apiPath, requestData)

    console.log('TT列表接口返回数据:', response)

    if (response?.code === 0 && response?.data) {
      // 更新分页信息
      ttPagination.value.total = response.data.total || 0
      ttPagination.value.currentPage = response.data.currentPage || 1

      // 计算总页数
      const totalPages = Math.ceil(ttPagination.value.total / ttPagination.value.pageSize)

      // 转换数据格式
      const formattedData = response.data.data.map(item => ({
        ...item,
        createdAt: formatDateTime(new Date(item.createdAt).toISOString()),
        updatedAt: formatDateTime(new Date(item.updatedAt).toISOString()),
        kbTimestamp: item.kbTimestamp ? formatDateTime(new Date(item.kbTimestamp).toISOString()) : '-',
        kbUpdateUser: item.kbUpdateUser || '-',
        kbStatus: item.kbStatus
      }))

      // 如果当前页数据为空且不是最后一页，且autoNextPage为true，则自动请求下一页
      if (formattedData.length === 0 && ttPagination.value.currentPage < totalPages && autoNextPage) {
        console.log('当前页数据为空，自动请求下一页')
        ttPagination.value.currentPage++
        ttLoading.value = false
        return fetchTTList(true)
      }

      ttTableData.value = formattedData
      console.log('转换后的TT列表数据:', ttTableData.value)
      
      // 更新表头复选框状态
      updateHeaderCheckboxState()
      
    } else {
      console.error('接口返回数据格式不正确:', response)
      ElMessage.error(`获取TT列表失败: ${response?.msg || '未知错误'}`)
    }
  } catch (error) {
    console.error('获取TT列表出错:', error)
    ElMessage.error(`获取TT列表失败: ${error.message || '未知错误'}`)
  } finally {
    ttLoading.value = false
  }
}

// 处理过滤条件变化
const handleTTFilterChange = (filterData) => {
  console.log('过滤条件变化:', filterData)
  ttFilterData.value = filterData
  ttPagination.value.currentPage = 1 // 重置到第一页
  fetchTTList() // 重新获取数据
}

// 创建防抖版本的TT过滤条件变化函数
const debouncedHandleTTFilterChange = debounce(handleTTFilterChange, 300)

// 处理TT群转语料
const handleTTImport = () => {
  // 确保ttSelectedMap已初始化
  if (!ttSelectedMap.value) {
    ttSelectedMap.value = new Map<string, TTItem>()
  }
  
  // 使用 openTTDialog 方法打开对话框
  openTTDialog()
  ;(document.activeElement as HTMLElement)?.blur()
}

// 创建防抖版本的TT群转语料函数
const debouncedHandleTTImport = debounce(handleTTImport, 300)

// 批量删除语料
const handleBatchDelete = async () => {
  if (isBatchDeleting.value) return
  
  try {
    isBatchDeleting.value = true
    
    const result = await batchDeleteCorpus({
      selectedRows: selectedRows.value,
      currentMisId: currentMisId.value,
      currentTeam: currentTeam.value,
      refreshCallback: async () => {
        // 清空选中项
        selectedRows.value = []
        // 刷新列表
        await fetchCorpusList()
      }
    })
    
    return result
  } finally {
    isBatchDeleting.value = false
  }
}

// 创建防抖版本的批量删除函数
const debouncedHandleBatchDelete = debounce(handleBatchDelete, 300)

// 合并预览对话框可见性
const mergePreviewDialogVisible = ref(false)

// 合并预览表单数据
const mergePreviewForm = ref({
  title: '',
  content: '',
  taskId: '', // 添加 taskId
  ticketId: '', // 添加 ticketId
  corpusIdList: [] as string[], // 添加 corpusIdList
  tagsIds: '' // 添加 tagsIds
})

// 添加标签管理相关的响应式数据
// 标签相关数据
const mergeTagOptions = ref<{ id: string; name: string }[]>([])
const mergeSelectedTagIds = ref<string[]>([])
const isMergeLoadingTags = ref(false)
const mergeDefaultTag = ref<{ id: string; name: string } | null>(null)
const isMergeInitializing = ref(false)

// 获取合并预览的标签列表
const fetchMergeTagList = async () => {
  if (!currentTeam.value) return
  
  try {
    isMergeLoadingTags.value = true
    const response = await httpRequest.rawRequestGet('/rgTags/listTags', {
      rgId: currentTeam.value
    })
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      // 根据实际API返回格式调整映射
      const formattedTags = response.data.map((tag: any) => {
        const id = tag.id === null ? null : String(tag.id || tag.tagId || tag.value || '')
        const name = tag.name || tag.tagName || tag.label || `标签${id || 'default'}`
        
        return {
          id,
          name
        }
      }).filter(tag => tag.name) // 只过滤掉无名称的数据
      
      // 设置默认标签（第一个标签，id为null）
      if (formattedTags.length > 0 && formattedTags[0].id === null) {
        mergeDefaultTag.value = formattedTags[0]
        // 从可选标签中移除默认标签，因为它会自动显示
        mergeTagOptions.value = formattedTags.slice(1)
      } else {
        mergeDefaultTag.value = null
        mergeTagOptions.value = formattedTags
      }
      
    } else {
      console.warn('获取合并预览标签列表失败:', response?.msg || '数据格式错误')
      mergeTagOptions.value = []
      mergeDefaultTag.value = null
    }
  } catch (error) {
    console.error('获取合并预览标签列表出错:', error)
    mergeTagOptions.value = []
    mergeDefaultTag.value = null
  } finally {
    isMergeLoadingTags.value = false
  }
}

// 处理合并预览标签选择变化
const handleMergeTagSelectionChange = (tagIds: string[]) => {
  // 如果正在初始化，不执行更新逻辑
  if (isMergeInitializing.value) return
  
  // 过滤掉空字符串，防止"添加标签"选项被误选
  const filteredTagIds = tagIds.filter(tag => tag !== '')
  
  // 限制最多选择3个标签
  if (filteredTagIds.length > 3) {
    ElMessage.warning('最多只能选择3个标签')
    const limitedTagIds = filteredTagIds.slice(0, 3)
    // 更新选中的标签ID
    mergeSelectedTagIds.value = limitedTagIds
  } else {
    mergeSelectedTagIds.value = filteredTagIds
  }
  
  // 更新mergePreviewForm中的tagsIds
  const selectedTags = mergeTagOptions.value.filter(tag => mergeSelectedTagIds.value.includes(tag.id))
  
  let submitTagsIds: string = ''
  
  if (selectedTags.length === 0 && mergeDefaultTag.value) {
    // 没有选择任何标签时，提交空字符串，后端会处理为默认标签
    submitTagsIds = ''
  } else {
    // 有选择标签时，提交选择的标签ID
    submitTagsIds = mergeSelectedTagIds.value.join(',')
  }
  
  mergePreviewForm.value = {
    ...mergePreviewForm.value,
    tagsIds: submitTagsIds
  }
}

// 处理合并语料
const handleMergeCorpus = async () => {
  if (selectedRows.value.length < 2) return
  if (isMerging.value) return
  
  // 检查是否选择了值班组
  if (!currentTeam.value) {
    ElMessage.error('请先选择值班组')
    return
  }

  try {
    // 设置合并状态为true
    isMerging.value = true
    
    // 设置加载面板标题
    loadingPanelTitle.value = '请求处理中'
    // 恢复默认提示信息
    loadingPanelTips.value = [
      '大模型正在分析内容，预计需要30-60秒',
      '根据内容复杂度和系统负载，等待时间可能会有所不同',
      '请耐心等待，处理完成后会自动显示结果'
    ]
    // 显示加载进度面板
    loadingPanel.value = true

    // 构造请求体数据
    const requestData = {
      triggerSource: 1, // 首页语料列表
      corpusTextList: [], // 首页为空数组
      corpusIdList: selectedRows.value.map(row => row.ticketId), // 选中的ticketId列表
      misId: currentMisId.value,
      rgId: currentTeam.value,
      ticketId: '' // 首页为空字符串
    }

    // 第一阶段进度 - 准备合并
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(15)
    }, 1000)

    console.log('发送的请求数据:', requestData)

    // 发送合并请求
    const response = await httpRequest.rawRequestPostAsJson(
      '/corpus/createMergeCorpusTask',
      requestData
    )

    if (response?.code === 0 && response?.data) {
      const taskId = response.data
      console.log('获取到的taskId:', taskId)
      mergePreviewForm.value.taskId = taskId // 保存 taskId
      
      // 第二阶段进度 - 任务已创建
      setTimeout(() => {
        loadingPanelInstance.value?.setProgress(30)
      }, 200)
      
      // 开始轮询任务状态
      let retryCount = 0
      const maxRetries = 100 // 最多轮询100次
      const pollInterval = 1000 // 每秒轮询一次
      
      // 第三阶段进度 - 开始模型合并
      setTimeout(() => {
        loadingPanelInstance.value?.setProgress(45)
      }, 1000)
      
      const pollTask = async () => {
        if (retryCount >= maxRetries) {
          loadingPanel.value = false
          isMerging.value = false
          ElMessage.error('任务处理超时，请稍后重试')
          return
        }
        
        try {
          const pollResponse = await httpRequest.rawRequestGet('/review/queryModelOutputByTaskId', {
            taskId
          })
          console.log(`第 ${retryCount + 1} 次轮询结果:`, pollResponse)
          
          // 根据轮询次数增加进度，确保进度值始终增加
          const currentProgress = 45 + Math.min((retryCount / maxRetries) * 45, 45)
          loadingPanelInstance.value?.setProgress(currentProgress)
          
          if (pollResponse?.code === 0) {
            if (pollResponse.data.taskStatus === 1) {
              // 任务成功，显示预览对话框
              loadingPanelInstance.value?.complete()
              setTimeout(() => {
                loadingPanel.value = false
                isMerging.value = false
                mergePreviewForm.value = {
                  title: formatTitle(pollResponse.data.title),
                  content: pollResponse.data.content,
                  taskId: pollResponse.data.taskId,
                  ticketId: pollResponse.data.ticketId, // 保存 ticketId
                  corpusIdList: selectedRows.value.map(row => row.ticketId), // 保存 corpusIdList
                  tagsIds: pollResponse.data.tagsIds || ''  // 修改：从queryModelOutputByTaskId返回的数据中获取tagsIds字段
                }
                mergePreviewDialogVisible.value = true
              }, 500)
              return
            } else if (pollResponse.data.taskStatus === 2) {
              // 任务失败
              loadingPanel.value = false
              isMerging.value = false
              ElMessage.error(`合并失败: ${pollResponse.data.taskMessage || '未知错误'}`)
              return
            }
          }
          
          // 继续轮询
          retryCount++
          setTimeout(pollTask, pollInterval)
        } catch (error: any) {
          loadingPanel.value = false
          isMerging.value = false
          console.error('轮询出错:', error)
          ElMessage.error(`轮询失败: ${error.message || '未知错误'}`)
        }
      }
      
      // 开始轮询
      setTimeout(pollTask, pollInterval)
    } else {
      loadingPanel.value = false
      isMerging.value = false
      ElMessage.error(`创建合并任务失败: ${response?.msg || '未知错误'}`)
    }
  } catch (error: any) {
    loadingPanel.value = false
    isMerging.value = false
    console.error('合并语料出错:', error)
    ElMessage.error(`合并语料失败: ${error.message || '未知错误'}`)
  }
}

// 创建防抖版本的合并语料函数
const debouncedHandleMergeCorpus = debounce(handleMergeCorpus, 300)

// 处理合并预览取消
const handleMergePreviewCancel = () => {
  mergePreviewDialogVisible.value = false
  mergePreviewForm.value = {
    title: '',
    content: '',
    taskId: '',
    ticketId: '',
    corpusIdList: [],
    tagsIds: ''
  }
  // 重置标签相关状态
  mergeSelectedTagIds.value = []
  mergeTagOptions.value = []
  mergeDefaultTag.value = null
  isMergeInitializing.value = false
}

// 创建防抖版本的合并预览取消函数
const debouncedHandleMergePreviewCancel = debounce(handleMergePreviewCancel, 300)

// 处理合并预览保存
const handleMergePreviewSave = async () => {
  if (isMergePreviewSaving.value) return
  
  try {
    // 设置合并预览保存状态为true
    isMergePreviewSaving.value = true
    
    // 构造请求体
    const requestBody = {
      ticketId: mergePreviewForm.value.ticketId,
      title: mergePreviewForm.value.title,
      content: mergePreviewForm.value.content,
      misId: currentMisId.value,
      rgId: currentTeam.value,
      source: 4, // 4表示合并来源
      type: -1,
      corpusIdList: mergePreviewForm.value.corpusIdList,
      tagsIds: mergePreviewForm.value.tagsIds || ''  // 新增：添加tagsIds字段
    }

    console.log('合并预览保存请求参数:', requestBody)
    const response = await httpRequest.rawRequestPostAsJson('/corpus/saveMergeCorpus', requestBody)
    console.log('合并预览保存响应:', response)

    if (response?.code === 0) {
      ElMessage.success('保存成功')
      // 关闭合并预览对话框
      mergePreviewDialogVisible.value = false
      // 关闭转换语料审核表单
      convertDialogVisible.value = false
      // 清空预览表单
      mergePreviewForm.value = {
        title: '',
        content: '',
        taskId: '',
        ticketId: '',
        corpusIdList: []
      }
      // 刷新首页语料列表
      fetchCorpusList()
    } else {
      ElMessage.error(response?.msg || '保存失败')
    }
  } catch (error) {
    console.error('合并语料出错:', error)
    ElMessage.error(`合并语料失败: ${error.message || '未知错误'}`)
  } finally {
    // 最后重置合并预览保存状态
    isMergePreviewSaving.value = false
  }
}

// 创建防抖版本的合并预览保存函数
const debouncedHandleMergePreviewSave = debounce(handleMergePreviewSave, 300)

// 添加一个变量来标识检索对比的来源
const compareSource = ref<'main' | 'convert'>('main')

// 检索相似内容
const handleCompare = async () => {
  if (selectedRows.value.length !== 1) {
    ElMessage.warning('请选择一条语料进行检索')
    return
  }
  
  if (isComparing.value) return

  // 检查是否选择了值班组
  if (!currentTeam.value) {
    ElMessage.error('请先选择值班组')
    return
  }

  try {
    // 设置检索状态为true
    isComparing.value = true
    
    // 设置来源为首页
    compareSource.value = 'main'
    
    // 重置选择状态
    selectedCompareRows.value = []
    // 重置检索结果
    compareResults.value = []

    // 设置加载面板标题
    loadingPanelTitle.value = '相似语料检索中'
    // 设置检索相似语料特有的提示信息
    loadingPanelTips.value = [
      '正在检索相似的语料',
      '请耐心等待，处理完成后会自动显示结果'
    ]
    // 显示加载进度面板
    loadingPanel.value = true

    // 调用工具函数执行检索
    const result = await searchSimilarCorpus({
      content: selectedRows.value[0].content,
      currentTeam: currentTeam.value,
      ticketId: selectedRows.value[0].ticketId,
      loadingPanelInstance: loadingPanelInstance.value,
      SOURCE_MAP,
      formatTitle,
      formatDateTime
    })
    
    // 延迟关闭加载面板，以便用户看到完成状态
    setTimeout(() => {
      loadingPanel.value = false
      isComparing.value = false
      
      if (result.success) {
        // 设置检索结果
        compareResults.value = result.data
        
        if (compareResults.value.length === 0) {
          ElMessage.warning('未找到相似语料')
        } else {
          compareDialogVisible.value = true
        }
      } else {
        // 显示错误信息
        ElMessage.error(result.error || '检索失败')
      }
    }, 500)
  } catch (error) {
    loadingPanel.value = false
    isComparing.value = false
    console.error('检索失败:', error)
    ElMessage.error(`检索失败: ${error.message || '未知错误'}`)
  }
}

// 创建防抖版本的检索相似内容函数
const debouncedHandleCompare = debounce(handleCompare, 300)

// 转换语料检索相似内容
const handleConvertCompare = async () => {
  // 检查是否选择了值班组
  if (!currentTeam.value) {
    ElMessage.error('请先选择值班组')
    return
  }

  // 设置来源为转换语料审核
  compareSource.value = 'convert'
  
  // 重置选择状态
  selectedCompareRows.value = []
  // 重置检索结果
  compareResults.value = []

  // 设置加载面板标题
  loadingPanelTitle.value = '相似语料检索中'
  // 设置检索相似语料特有的提示信息
  loadingPanelTips.value = [
    '正在检索相似的语料',
    '请耐心等待，处理完成后会自动显示结果'
  ]
  // 显示加载进度面板
  loadingPanel.value = true

  try {
    // 调用工具函数执行检索
    const result = await searchSimilarCorpus({
      content: convertFormData.value.content,
      currentTeam: currentTeam.value,
      ticketId: convertFormData.value.ticketId,
      loadingPanelInstance: loadingPanelInstance.value,
      SOURCE_MAP,
      formatTitle,
      formatDateTime
    })
    
    // 延迟关闭加载面板，以便用户看到完成状态
    setTimeout(() => {
      loadingPanel.value = false
      
      if (result.success) {
        // 设置检索结果
        compareResults.value = result.data
        
        if (compareResults.value.length === 0) {
          ElMessage.warning('未找到相似语料')
        } else {
          compareDialogVisible.value = true
        }
      } else {
        // 显示错误信息
        ElMessage.error(result.error || '检索失败')
      }
    }, 500)
  } catch (error) {
    loadingPanel.value = false
    console.error('检索失败:', error)
    ElMessage.error(`检索失败: ${error.message || '未知错误'}`)
  }
}

// 处理检索对比抽屉关闭
const handleCompareClose = () => {
  compareDialogVisible.value = false
  // 重置选择状态
  selectedCompareRows.value = []
  // 重置检索结果中的选中状态
  compareResults.value = compareResults.value.map(item => ({
    ...item,
    selected: false
  }))
}

// 表格多选变化
const handleSelectionChange = (val: CorpusItem[]) => {
  selectedRows.value = val
}

// 添加编辑表单提交状态
const isEditSubmitting = ref(false)
// 添加合并预览保存状态
const isMergePreviewSaving = ref(false)
// 添加批量删除状态
const isBatchDeleting = ref(false)
// 添加合并语料状态
const isMerging = ref(false)
// 添加检索相似状态
const isComparing = ref(false)
// 添加转换语料保存状态
const isConvertSaving = ref(false)
// 添加刷新状态
const isRefreshing = ref(false)
// 单条删除状态字典
const isDeletingItem = ref<Record<string, boolean>>({})
// TT导入状态
const isTTImporting = ref(false)

// 处理合并表单提交
const handleMergeSubmit = async () => {
  if (!currentMisId.value) {
    ElMessage.error('未获取到用户信息，请刷新页面重试')
    return
  }
  try {
    const res = await httpRequest.rawRequestPostAsJson(API_PATHS.MERGE_SUBMIT, {
      misId: currentMisId.value,
      ids: selectedRows.value.map(item => item.id),
      data: mergePreviewForm.value
    })
    if (res.code === 0) {
      ElMessage.success('提交成功')
      mergePreviewDialogVisible.value = false
      fetchCorpusList()
    } else {
      ElMessage.error(res.message || '提交失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

// 处理合并取消
const handleMergeCancel = () => {
  mergeDialogVisible.value = false
  mergeFormData.value = {
    title: '',
    content: ''
  }
  ;(document.activeElement as HTMLElement)?.blur()
}

// 处理查看语料
const handleView = (row: CorpusItem) => {
  currentViewItem.value = { ...row }
  viewDialogVisible.value = true
  ;(document.activeElement as HTMLElement)?.blur()
}

// 处理编辑语料
const handleEdit = (row: CorpusItem) => {
  currentEditId.value = row.ticketId // 修改为使用 ticketId
  editFormData.value = {
    title: row.title,
    content: row.content,
    tagsname: row.tagsname || [],
    tagsIds: row.tagsIds || ''
  }
  editDialogVisible.value = true

  // 在对话框显示后，延迟一段时间初始化monaco编辑器布局
  setTimeout(() => {
    const editorWrappers = document.querySelectorAll('.monaco-editor-wrapper')
    if (editorWrappers.length > 0) {
      const editorInstance = editorWrappers[0].__vue__?.getEditor?.()
      if (editorInstance && typeof editorInstance.layout === 'function') {
        editorInstance.layout()
      }
    }
  }, 300)
  
  ;(document.activeElement as HTMLElement)?.blur()
}

// 处理编辑取消
const handleEditCancel = () => {
  editFormData.value = {
    title: '',
    content: '',
    tagsname: [],
    tagsIds: ''
  }
  ;(document.activeElement as HTMLElement)?.blur()
}

// 处理编辑表单提交
const handleEditSubmit = async () => {
  if (!editFormRef.value || isEditSubmitting.value) return
  
  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 设置编辑提交状态为true
        isEditSubmitting.value = true
        
        // 确保rgId是数字类型
        const rgId = parseInt(currentTeam.value)
        if (!rgId || rgId <= 0) {
          ElMessage.error('无效的值班组ID')
          return
        }

        // 构造URL参数
        const queryParams = `misId=${currentMisId.value}&rgId=${rgId}&ticketId=${currentEditId.value}`

        // 构造请求体
        const requestBody = {
          title: editFormData.value.title,
          type: -1,
          content: editFormData.value.content
        }

        console.log('编辑语料URL参数:', queryParams)
        console.log('编辑语料请求体:', requestBody)
        
        const response = await httpRequest.rawRequestPostAsJson(
          `/corpus/modifyCorpusByTicketId?${queryParams}`,
          requestBody
        )
        console.log('编辑语料响应:', response)

        if (response?.code === 0) {
          ElMessage.success('保存成功')
          editDialogVisible.value = false
          // 刷新列表
          await fetchCorpusList()
        } else {
          ElMessage.error(`保存失败: ${response?.msg || '未知错误'}`)
        }
      } catch (error) {
        console.error('保存语料出错:', error)
        ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
      } finally {
        // 最后重置编辑提交状态
        isEditSubmitting.value = false
      }
    } else {
      // 表单验证失败
      isEditSubmitting.value = false
    }
  })
}

// 创建防抖版本的编辑表单提交函数
const debouncedHandleEditSubmit = debounce(handleEditSubmit, 300)

// 处理查看TT
const handleViewTT = (row: TTItem) => {
  // 构造完整的 URL
  const ttDetailUrl = `${TT_DETAIL_BASE_URL}?id=${row.ticketId}`
  
  // 在新窗口中打开 URL
  window.open(ttDetailUrl, '_blank')
  
  // 移除焦点
  ;(document.activeElement as HTMLElement)?.blur()
}

// 处理转换语料
const handleConvertToCorpus = async (row: TTItem) => {
  // 不处理待解决或未建群状态的数据
  if (row.kbStatus === 4 || row.kbStatus === 5) {
    const statusText = row.kbStatus === 4 ? '待解决' : '未建群';
    ElMessage.warning(`${statusText}状态的TT不可转换`);
    return false;
  }
  
  // 不处理三年以上的数据
  if (isCreatedTimeExceedThreeYears(row.createdAt)) {
    ElMessage.warning('只支持三年内的TT数据');
    return false;
  }
  
  return convertCorpus({
    row,
    currentMisId: currentMisId.value,
    currentEmpId: currentEmpId.value,
    currentTeam: currentTeam.value,
    loadingPanel,
    loadingPanelTitle,
    loadingPanelInstance,
    taskMissingInfo,
    convertDialogVisible,
    convertFormData,
    onNotify: (message) => {
      messageNotificationRef.value?.addMessage(message);
    }
  })
}

// 组件卸载时的处理
onUnmounted(() => {
  if (taskPollingTimer.value) {
    clearInterval(taskPollingTimer.value)
    taskPollingTimer.value = null
  }
})

// 处理分页变化
const handleTTPageChange = (page: number) => {
  // 设置页码
  ttPagination.value.currentPage = page
  // 获取新页面的数据并恢复选中状态
  fetchTTList().then(() => {
    // 强制更新复选框状态
    nextTick(() => {
      updateHeaderCheckboxState()
    })
  })
  ;(document.activeElement as HTMLElement)?.blur()
}

// 创建防抖版本的TT分页变化函数
const debouncedHandleTTPageChange = debounce(handleTTPageChange, 300)

// 处理每页条数变化
const handleTTSizeChange = (size: number) => {
  // 设置页面大小
  ttPagination.value.pageSize = size
  ttPagination.value.currentPage = 1 // 重置到第一页
  // 获取新数据并恢复选中状态
  fetchTTList().then(() => {
    // 强制更新复选框状态
    nextTick(() => {
      updateHeaderCheckboxState()
    })
  })
  ;(document.activeElement as HTMLElement)?.blur()
}

// 创建防抖版本的TT每页条数变化函数
const debouncedHandleTTSizeChange = debounce(handleTTSizeChange, 300)

// 处理TT对话框全屏切换
const handleTTFullscreen = () => {
  ttDialogFullscreen.value = !ttDialogFullscreen.value
  // 强制隐藏原生关闭按钮
  if (ttDialogFullscreen.value) {
    setTimeout(() => {
      const closeBtn = document.querySelector('.tt-dialog .el-dialog__close')
      if (closeBtn) {
        ;(closeBtn as HTMLElement).style.display = 'none'
      }
    }, 0)
  }
  ;(document.activeElement as HTMLElement)?.blur()
}

// 处理删除
const handleDelete = async (row: CorpusItem) => {
  if (isDeletingItem.value[row.ticketId]) return;
  
  try {
    // 设置删除状态为true
    isDeletingItem.value = {
      ...isDeletingItem.value,
      [row.ticketId]: true
    };
    
    return await deleteCorpus({
      ticketId: row.ticketId,
      currentMisId: currentMisId.value,
      currentTeam: currentTeam.value,
      refreshCallback: async () => {
        await fetchCorpusList()
      }
    });
  } finally {
    // 重置删除状态
    setTimeout(() => {
      isDeletingItem.value = {
        ...isDeletingItem.value,
        [row.ticketId]: false
      };
    }, 500); // 延迟重置，确保UI状态可见
  }
}

// 创建防抖版本的删除函数
const debouncedHandleDelete = debounce(handleDelete, 300)

// 处理转换语料保存
const handleConvertSave = async () => {
  if (isConvertSaving.value) return

  try {
    // 设置保存状态为true
    isConvertSaving.value = true
    
    // 构造请求参数
    const requestData = {
      misId: [currentMisId.value],
      optimus_risk_level: ['71'],
      modifiedContent: [convertFormData.value.content], 
      rgId: [String(convertFormData.value.rgId)], 
      optimus_platform: ['1'],
      _token: ['eJyV0t1LwzAQAPD/Jb5mbT4uX4MhFVEmDU7X8YesjXOMteNtYog/u9eIi17EoSW/rjc5a5pv8hxWpExZ8wwRslHOJIx4RnLNKGka3HFANfGSQVKWErWQ8xyxaXUhpLV8fmSjBfccE0tsGWMzDCw4E4wypnFUG lllQAXjFriknktesO4zyv9m228s3OZ11ou6z1zfbd19l6v8vxPrzVvlmHvG6q8HmWv4Tj/sq3XS40c/y88 12Wk10WDEHwY84F3IEJoiRr2w1Cg4ceClX1jp8q/83JDjsrozDarAUFOAup3J/SbFBHCWT5CAYpFEiyfXSfFCs4EkKxZJsL4M9wCUJlE3CPDBJmAfxe2ob81QS7gepr415qYc1g2wvhxNA6uGwQqYeDieVsUf8ZUCapBjTSTYJj2wbjwyf/vTo6NNDcYd5IAU1gtOLeVnex4BUhionftexquurb/GPxPW23jSocPNZzjfTorjeFLPHyYR8/wD5Jqls'],
      optimus_code: ['10'],
      optimus_partner: ['52'],
      optimus_origin_url: ['http://dos.banma.test.sankuai.com/compliance/index#/feroFast/26091?taskId=6eb094ea-1123-47e2-ad8d-e9494a33b889'],
      title: [convertFormData.value.title], 
      ticketId: [convertFormData.value.ticketId],
      tagsIds: [convertFormData.value.tagsIds || ''], // 新增：标签ID参数
      ...(convertFormData.value.taskId ? { taskId: [convertFormData.value.taskId] } : {})  // 新增：如果有 taskId，则包含在请求数据中
    }
    
    console.log('发送的请求数据:', requestData)

    // 发送请求
    const response = await httpRequest.rawRequestPostAsForm(
      '/review/saveModifiedOutput',
      requestData
    )
    
    console.log('saveModifiedOutput接口返回数据:', response)

    if (response?.code === 0) {
      ElMessage({
        type: 'success',
        message: '保存成功'
      })
      convertDialogVisible.value = false
      // 重新获取TT列表数据
      fetchTTList()
    } else {
      ElMessage.error(`保存失败: ${response?.msg || '未知错误'}`)
    }
  } catch (error) {
    console.error('保存语料出错:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  } finally {
    // 重置保存状态
    isConvertSaving.value = false
  }
}

// 创建防抖版本的转换语料保存函数
const debouncedHandleConvertSave = debounce(handleConvertSave, 300)

// 获取相似度样式类
const getSimilarityClass = (score: string) => {
  const value = parseInt(score)
  if (value >= 80) return 'high'
  if (value >= 60) return 'medium'
  return 'low'
}

// 处理合并选中项
const handleMergeSelected = async () => {
  // 检查是否有选中的语料（包括初始检索语料）
  if (compareSource.value === 'main' && selectedCompareRows.value.length === 0) {
    ElMessage.warning('请至少选择一条相似语料进行合并')
    return
  }

  // 检查是否选择了值班组
  if (!currentTeam.value) {
    ElMessage.error('请先选择值班组')
    return
  }

  // 设置加载面板标题
  loadingPanelTitle.value = '请求处理中'
  // 恢复默认提示信息
  loadingPanelTips.value = [
    '大模型正在分析内容，预计需要30-60秒',
    '根据内容复杂度和系统负载，等待时间可能会有所不同',
    '请耐心等待，处理完成后会自动显示结果'
  ]
  // 显示加载进度面板
  loadingPanel.value = true

  try {
    // 调用合并语料工具函数
    const result = await mergeSelectedCorpus({
      compareSource: compareSource.value,
      selectedRows: compareSource.value === 'main' ? selectedRows.value : undefined,
      selectedCompareRows: selectedCompareRows.value,
      currentMisId: currentMisId.value,
      currentTeam: currentTeam.value,
      convertFormData: compareSource.value === 'convert' ? convertFormData.value : undefined,
      loadingPanelInstance: loadingPanelInstance.value,
      formatTitle
    })

    if (result.success && result.data) {
      // 处理成功结果
      setTimeout(() => {
        loadingPanel.value = false
        mergePreviewForm.value = {
          title: result.data.title,
          content: result.data.content,
          taskId: result.data.taskId,
          ticketId: result.data.ticketId,
          corpusIdList: result.data.corpusIdList,
          tagsIds: result.data.tagsIds || ''  // 新增：从mergeSelectedCorpus返回的数据中获取tagsIds字段
        }
        mergePreviewDialogVisible.value = true
        compareDialogVisible.value = false // 关闭检索对比对话框
      }, 500)
    } else {
      loadingPanel.value = false
      ElMessage.error(result.error || '合并失败，请重试')
    }
  } catch (error) {
    loadingPanel.value = false
    console.error('合并语料出错:', error)
    ElMessage.error(`合并语料失败: ${error.message || '未知错误'}`)
  }
}

// 处理检索结果表格多选变化
const handleCompareSelectionChange = (val: CorpusItem[]) => {
  selectedCompareRows.value = val
}

// 处理批量删除选中项
const handleBatchDeleteSelected = async () => {
  await handleBatchDeleteCompare()
}

// 在 script setup 中添加 taskMissingInfo 数据
const taskMissingInfo = ref<string[]>([])

// 处理查看详情
const handleViewDetail = (row: CorpusItem) => {
  currentViewItem.value = { ...row }
  viewDialogVisible.value = true

  // 在下一个事件循环中等待对话框打开后设置编辑器状态
  setTimeout(() => {
    // 获取编辑器实例
    const editor = document.querySelector('.view-corpus-editor')
    if (editor && editor.__vue__) {
      const editorComponent = editor.__vue__
      // 设置为预览模式
      if (typeof editorComponent.togglePreview === 'function' && !editorComponent.isPreviewMode) {
        editorComponent.togglePreview()
      }
    }
  }, 300)

  ;(document.activeElement as HTMLElement)?.blur()
}

// 处理单个项目选择
const handleItemSelect = (item: CorpusItem, selected: boolean) => {
  if (selected) {
    selectedCompareRows.value.push(item)
  } else {
    selectedCompareRows.value = selectedCompareRows.value.filter(row => row.ticketId !== item.ticketId)
  }
}

// 监听编辑对话框可见性变化
watch(() => editDialogVisible.value, (newVisible) => {
  if (newVisible) {
    // 当对话框显示时，延迟一段时间再重新触发Monaco编辑器布局
    setTimeout(() => {
      const editorWrappers = document.querySelectorAll('.monaco-editor-wrapper')
      if (editorWrappers.length > 0) {
        try {
          // 尝试通过Vue组件实例获取编辑器实例
          const editorWrapper = editorWrappers[0]
          const vueInstance = editorWrapper.__vue__
          if (vueInstance && typeof vueInstance.getEditor === 'function') {
            const editor = vueInstance.getEditor()
            if (editor && typeof editor.layout === 'function') {
              editor.layout()
            }
          }
        } catch (e) {
          console.error('初始化编辑器失败:', e)
        }
      }
    }, 300)
  }
})

// 处理编辑对话框打开
const handleEditDialogOpened = () => {
  setTimeout(() => {
    // 在对话框完全打开后，初始化编辑器布局
    const editorWrappers = document.querySelectorAll('.edit-corpus-editor')
    if (editorWrappers.length > 0) {
      const editorWrapper = editorWrappers[0] as any
      // 获取编辑器实例并调用layout方法
      if (editorWrapper.__vue__ && typeof editorWrapper.__vue__.getEditor === 'function') {
        const editor = editorWrapper.__vue__.getEditor()
        if (editor && typeof editor.layout === 'function') {
          editor.layout()
        }
      }
      
      // 备用方法：直接通过DOM查找monaco-editor元素并触发布局更新
      const monacoEditors = document.querySelectorAll('.edit-dialog .monaco-editor')
      if (monacoEditors.length > 0) {
        // 强制调整宽度样式
        monacoEditors.forEach((editor: any) => {
          editor.style.width = '100%'
          // 查找overflow-guard元素并调整宽度
          const overflowGuards = editor.querySelectorAll('.overflow-guard')
          if (overflowGuards.length > 0) {
            overflowGuards.forEach((guard: any) => {
              guard.style.width = '100%'
            })
          }
        })
      }
    }
  }, 300) // 延迟300ms确保DOM已完全渲染
}

// 添加对editDialogVisible的监听，在对话框打开时更新编辑器布局
watch(() => editDialogVisible.value, (newVal) => {
  if (newVal) {
    // 对话框显示时，延迟更新编辑器布局
    setTimeout(() => {
      handleEditDialogOpened()
    }, 300)
  }
})

// 添加对话框可见性监听
watch(() => dialogVisible.value, (newVal) => {
  if (newVal) {
    // 对话框打开后，设置一个延时，确保DOM已经渲染
    setTimeout(() => {
      handleDialogOpened();
    }, 300);
  }
});

// 处理对话框打开后初始化编辑器布局
const handleDialogOpened = () => {
  // 尝试获取编辑器容器
  const editorWrappers = document.querySelectorAll('.add-corpus-editor .monaco-editor-wrapper');
  if (editorWrappers.length > 0) {
    try {
      // 尝试通过Vue组件实例获取编辑器实例
      const editorWrapper = editorWrappers[0] as any;
      if (editorWrapper.__vue__ && typeof editorWrapper.__vue__.getEditor === 'function') {
        const editor = editorWrapper.__vue__.getEditor();
        if (editor && typeof editor.layout === 'function') {
          editor.layout();
        }
      }
    } catch (e) {
      console.warn('通过Vue实例调整编辑器布局失败', e);
    }

    // 备用方法：直接调整DOM元素
    try {
      // 直接查找monaco-editor元素并强制设置宽度
      const editors = document.querySelectorAll('.add-corpus-editor .monaco-editor');
      editors.forEach((editor: any) => {
        editor.style.width = '100%';
        const overflowGuards = editor.querySelectorAll('.overflow-guard');
        overflowGuards.forEach((guard: any) => {
          guard.style.width = '100%';
        });
      });
    } catch (e) {
      console.warn('通过DOM操作调整编辑器布局失败', e);
    }
  }
};

// 添加处理无需入库的方法
const handleMarkAsIgnore = async (row: any) => {
  try {
    // 打印行数据，用于调试
    console.log('完整的row对象:', row)
    
    // URL参数
    const params = {
      misId: currentMisId.value,
      rgId: currentTeam.value,
      ticketId: row.ticketId
    }
    
    // 使用 row.name 作为标题
    const requestBody = {
      title: row.name || ''
    }
    console.log('标记为忽略的参数：', { params, requestBody })

    // 构造查询字符串
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&')

    const response = await httpRequest.rawRequestPostAsJson(
      `/corpus/saveIgnoreCorpus?${queryString}`,
      requestBody
    )

    if (response.code === 0) {
      ElMessage.success({
        message: '标记为忽略成功',
        duration: 2000,
        showClose: true,
        customClass: 'custom-message',
        appendTo: document.body
      })
      // 刷新TT列表
      await fetchTTList()
    } else {
      // 添加失败响应提示
      ElMessage.error({
        message: `标记为忽略失败: ${response.msg || '未知错误'}`,
        duration: 3000,
        showClose: true
      })
      
      // 添加系统通知
      messageNotificationRef.value?.addMessage({
        title: '标记为忽略失败',
        content: `TT ID: ${row.ticketId} - ${row.name || '未命名'} 标记失败: ${response.msg || '未知错误'}`,
        type: 'error'
      })
    }
  } catch (error) {
    console.error('标记为忽略失败:', error)
    // 添加异常错误提示
    ElMessage.error({
      message: `标记为忽略失败: ${error.message || '未知错误'}`,
      duration: 3000,
      showClose: true
    })
    
    // 添加系统通知
    messageNotificationRef.value?.addMessage({
      title: '标记为忽略失败',
      content: `TT ID: ${row.ticketId} - ${row.name || '未命名'} 处理异常: ${error.message || '未知错误'}`,
      type: 'error'
    })
  }
}

// 添加查看TT语料详情的方法
const handleViewTTCorpus = async (row) => {
  try {
    // 构造请求参数，如果是已合并状态，使用 kbMergedToId 作为 ticketId
    const params = {
      rgId: currentTeam.value,
      ticketId: row.kbStatus === 3 ? row.kbMergedToId : row.ticketId,
      misId: currentMisId.value
    }
    
    const response = await httpRequest.rawRequestGet('/corpus/queryCorpusAllByTicketIdRgId', params)
    
    if (response?.code === 0 && response?.data) {
      currentViewItem.value = {
        ...response.data,
        title: formatTitle(response.data.title),
        source: SOURCE_MAP[response.data.source] || '其他',
        createTime: formatDateTime(response.data.createTime),
        updateTime: formatDateTime(response.data.updateTime),
        isMerged: row.kbStatus === 3 // 添加标记，用于区分是否为合并后的记录
      }
      viewDialogVisible.value = true
  } else {
      ElMessage.error(`获取语料详情失败: ${response?.msg || '未知错误'}`)
  }
  } catch (error) {
    console.error('获取语料详情出错:', error)
    ElMessage.error(`获取语料详情失败: ${error.message || '未知错误'}`)
}
}

// 转换进度显示控制
const loadingPanel = ref(false)
const loadingPanelInstance = ref<InstanceType<typeof LoadingProgressPanel> | null>(null)
// 加载面板标题
const loadingPanelTitle = ref('请求处理中')
// 加载面板提示信息
const loadingPanelTips = ref([
  '大模型正在分析内容，预计需要30-60秒',
  '根据内容复杂度和系统负载，等待时间可能会有所不同',
  '请耐心等待，处理完成后会自动显示结果'
])

// 添加 TT 详情页面的基础 URL 常量
const TT_DETAIL_BASE_URL = (() => {
  if (isDev() || isTest()) {
    return 'https://tt.cloud.test.sankuai.com/ticket/detail'
  }
  return 'https://tt.sankuai.com/ticket/detail'
})()

// 分页组件
const paginationContainer = ref<HTMLDivElement | null>(null)

// 任务列表对话框可见性
const taskListDialogVisible = ref(false)

// 处理查看任务列表
const handleViewTaskList = () => {
  if (!currentTeam.value) {
    ElMessage.warning('请先选择值班组')
    return
  }
  taskListDialogVisible.value = true
  ;(document.activeElement as HTMLElement)?.blur()
}

// 处理批量删除比较结果
const handleBatchDeleteCompare = async () => {
  const result = await deleteCompareCorpus({
    selectedRows: selectedCompareRows.value,
    currentMisId: currentMisId.value,
    currentTeam: currentTeam.value,
    compareResults: compareResults,
    clearSelection: () => {
      // 清空选中项
      selectedCompareRows.value = []
    },
    refreshMainList: async () => {
      await fetchCorpusList()
    }
  })
  return result
}

// 处理KM Wiki导入
const handleKmWikiImport = () => {
  if (!currentTeam.value) {
    ElMessage.warning('请先选择值班组')
    return
  }
  
  // 使用路由导航到kmwiki页面并传递rgId和misId参数
  router.push({
    path: '/corpus/kmwiki',
    query: {
      rgId: currentTeam.value,
      misId: currentMisId.value
    }
  })
  
  // 移除焦点
  ;(document.activeElement as HTMLElement)?.blur()
}

// 创建防抖版本的KM Wiki导入函数
const debouncedHandleKmWikiImport = debounce(handleKmWikiImport, 300)

// TT列表选中项管理 - 使用ID作为键，TTItem作为值
const ttSelectedMap = ref(new Map<string, TTItem>())

// 计算选中的语料数量
const selectedTTCount = computed(() => ttSelectedMap.value.size)

// 返回给定行是否被选中 - 用于自定义选择列样式和状态
const isRowSelected = (row: TTItem) => {
  return ttSelectedMap.value.has(row.ticketId)
}

// 检查TT创建时间是否超过三年
const isCreatedTimeExceedThreeYears = (createdTimeStr: string) => {
  if (!createdTimeStr) return false;
  
  try {
    // 解析创建时间字符串为Date对象
    const createdTime = new Date(createdTimeStr);
    
    // 获取当前时间
    const now = new Date();
    
    // 计算三年前的时间
    const threeYearsAgo = new Date();
    threeYearsAgo.setFullYear(now.getFullYear() - 3);
    
    // 如果创建时间早于三年前，则返回true
    return createdTime < threeYearsAgo;
  } catch (error) {
    console.error('日期解析错误:', error);
    return false;
  }
}

// 检查行是否可选（综合多种条件）
const isRowSelectable = (row: TTItem) => {
  // 待解决状态和未建群状态不可选
  if (row.kbStatus === 4 || row.kbStatus === 5) return false;
  
  // 检查创建时间是否超过三年
  if (isCreatedTimeExceedThreeYears(row.createdAt)) return false;
  
  return true;
}

// 获取行不可选原因
const getRowDisabledReason = (row: TTItem) => {
  if (row.kbStatus === 4) return '待解决状态的TT不可转换';
  
  if (row.kbStatus === 5) return '未建群状态的TT不可转换';
  
  if (isCreatedTimeExceedThreeYears(row.createdAt)) return '只支持三年内的TT数据';
  
  return '';
}

// 处理行选择切换
const handleTTRowSelectionChange = (row: TTItem) => {
  // 如果行不可选，直接返回
  if (!isRowSelectable(row)) return;
  
  if (ttSelectedMap.value.has(row.ticketId)) {
    ttSelectedMap.value.delete(row.ticketId)
  } else {
    ttSelectedMap.value.set(row.ticketId, row)
  }
  console.log(`当前共选中${ttSelectedMap.value.size}条语料`)
  
  // 更新表头复选框状态
  updateHeaderCheckboxState()
}

// 处理全选/取消全选
const handleSelectAll = () => {
  // 获取所有可选行
  const selectableRows = ttTableData.value.filter(isRowSelectable);
  
  // 检查当前可选行是否已全选
  const isAllSelected = selectableRows.length > 0 && 
    selectableRows.every(row => ttSelectedMap?.value?.has(row.ticketId));
  
  // 根据当前全选状态进行切换
  if (isAllSelected) {
    // 当前已全选，取消全选
    ttTableData.value.forEach(row => {
      ttSelectedMap.value.delete(row.ticketId);
    });
  } else {
    // 当前未全选，进行全选（仅选择可选行）
    selectableRows.forEach(row => {
      ttSelectedMap.value.set(row.ticketId, row);
    });
  }
  
  console.log(`全选操作后，当前共选中${ttSelectedMap.value.size}条语料`);
  
  // 更新表头复选框状态
  updateHeaderCheckboxState();
}

// 清空TT选择
const clearTTSelection = () => {
  ttSelectedMap.value.clear();
  console.log('已清空所有选择');
  
  // 清空选择后立即更新表头复选框状态
  updateHeaderCheckboxState();
}

// 处理批量转换语料
const handleBatchConvert = async () => {
  if (ttSelectedMap.value.size === 0) {
    ElMessage.warning('请至少选择一条语料');
    return;
  }
  
  // 获取所有选中项的ID和项目
  const selectedItems = Array.from(ttSelectedMap.value.values());
  const selectedIds = selectedItems.map(item => item.ticketId);
  console.log('批量转换语料，选中ID:', selectedIds);
  
  try {
    // 显示加载中
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在批量转换语料...',
      background: 'rgba(255, 255, 255, 0.7)',
    });
    
    // 构造请求参数
    const params = {
      empId: currentEmpId.value,
      misId: currentMisId.value,
      ticketIds: selectedIds.join(',')
    };
    
    // 发起请求前添加通知
    messageNotificationRef.value?.addMessage({
      title: '批量转换任务开始',
      content: `开始处理${selectedIds.length}条语料的批量转换请求`,
      type: 'info'
    });
    
    // 发起请求
    const response = await httpRequest.rawRequestGet(
      `${API_PATHS.BATCH_ADD_TT_CONTENT}?empId=${params.empId}&misId=${params.misId}&ticketIds=${params.ticketIds}`
    );
    
    loadingInstance.close();
    
    if (response?.code === 0) {
      // 从响应中获取成功转换的数量信息
      const successCount = response.data?.success || 0;
      const totalCount = response.data?.total || selectedIds.length;
      const failedCount = response.data?.failed || 0;
      const failedTicketIds = response.data?.failedTicketIds || [];
      const failureReasons = response.data?.failureReasons || {};
      
      // 使用成功数量替代总数量
      ElMessage.success(`成功提交${successCount}/${totalCount}条语料转换任务`);
      
      // 添加成功通知
      messageNotificationRef.value?.addMessage({
        title: '批量转换任务已提交',
        content: `成功提交${successCount}/${totalCount}条语料转换任务，请耐心等待处理完成`,
        type: 'success'
      });
      
      // 如果有失败的任务，添加失败通知
      if (failedCount > 0) {
        // 构建失败原因详情，格式化为"ID: 原因"的形式
        const failureDetails = failedTicketIds.map(id => 
          `${id}: ${failureReasons[id] || '未知原因'}`
        ).join('<br>');
        
        messageNotificationRef.value?.addMessage({
          title: '部分转换任务失败',
          content: `有${failedCount}条语料转换失败：<br>${failureDetails}`,
          type: 'warning',
          dangerouslyUseHTMLString: true
        });
      }
      
      // 为每个成功的TT添加单独的通知
      // 过滤掉失败的TT
      const successItems = selectedItems.filter(item => !failedTicketIds.includes(item.ticketId));
      successItems.forEach((item, index) => {
        setTimeout(() => {
          messageNotificationRef.value?.addMessage({
            title: `TT${index + 1}/${successItems.length}已加入处理队列`,
            content: `TT ID: ${item.ticketId} - ${item.name || '未命名'}`,
            type: 'info'
          });
        }, 200 * (index + 1)); // 错开通知显示时间
      });
      
      // 成功后清空选择
      clearTTSelection();
      // 可选：刷新TT列表
      await fetchTTList();
    } else {
      ElMessage.error(`批量转换失败: ${response?.msg || '未知错误'}`);
      
      // 添加错误消息通知
      messageNotificationRef.value?.addMessage({
        title: '批量转换失败',
        content: response?.msg || '未知错误',
        type: 'error'
      });
    }
  } catch (error) {
    console.error('批量转换语料出错:', error);
    ElMessage.error(`批量转换失败: ${error.message || '未知错误'}`);
    
    // 添加错误消息通知
    messageNotificationRef.value?.addMessage({
      title: '批量转换失败',
      content: error.message || '未知错误',
      type: 'error'
    });
  }
}

// TT表格实例

// 表头全选复选框ref
const headerCheckboxRef = ref<HTMLInputElement | null>(null)

// 设置表头checkbox的indeterminate状态
const updateHeaderCheckboxState = () => {
  if (!headerCheckboxRef.value) return
  
  // 过滤出所有可选行
  const selectableRows = ttTableData.value.filter(isRowSelectable);
  const totalCount = selectableRows.length;
  const selectedCount = selectableRows.filter(row => ttSelectedMap.value.has(row.ticketId)).length;
  
  // 设置复选框的选中状态
  headerCheckboxRef.value.checked = selectedCount > 0 && selectedCount === totalCount && totalCount > 0;
  
  // 设置半选状态
  headerCheckboxRef.value.indeterminate = selectedCount > 0 && selectedCount < totalCount;
}

// 在表格数据加载后更新表头复选框状态
watch(ttTableData, () => {
  updateHeaderCheckboxState()
}, { immediate: true })

// 添加表格行样式类的方法
const getRowClass = (row: TTItem) => {
  return isRowSelected(row) ? 'selected-row' : ''
}

// 消息通知引用
const messageNotificationRef = ref<InstanceType<typeof MessageNotification> | null>(null);

// 定义表格加载状态
const tableLoading = ref(false);

// 刷新列表
const handleRefreshList = async () => {
  // 验证是否选择了值班组
  if (!currentTeam.value) {
    ElMessage.warning('请先选择值班组');
    return;
  }
  
  // 显示加载中状态
  tableLoading.value = true;
  
  try {
    // 执行数据刷新
    await fetchCorpusList();
    
    // 显示成功提示
    ElMessage.success({
      message: '刷新成功',
      duration: 1500
    });
  } catch (error) {
    console.error('刷新列表失败:', error);
    ElMessage.error('刷新失败，请重试');
  } finally {
    // 无论成功失败，都需要关闭加载状态
    tableLoading.value = false;
  }
}

// 创建防抖版本的刷新列表函数
const debouncedHandleRefreshList = debounce(handleRefreshList, 300)

// 打开TT对话框
const openTTDialog = () => {
  ttDialogVisible.value = true
  
  // 在下一个tick获取数据，避免组件刚初始化就发请求
  nextTick(() => {
    fetchTTList()
  })
}

// 添加对CustomWorkSpace组件的引用
const customWorkSpaceRef = ref()

// 处理自定义SOP
const handleCustomSOP = () => {
  // 先设置对话框可见
  customWorkSpaceVisible.value = true;
  
  // 确保CustomWorkSpace组件已经渲染完成后再调用fetchData方法
  nextTick(() => {
    if (customWorkSpaceRef.value) {
      console.log('调用CustomWorkSpace的fetchData方法');
      customWorkSpaceRef.value.fetchData();
    }
  });
}

// 处理高级搜索
const handleAdvancedSearch = (searchParams: Record<string, any>) => {
  if (!currentTeam.value) {
    ElMessage.warning('请先选择值班组')
    return
  }
  
  // 更新高级搜索参数
  advancedSearchParams.value = {
    ticketId: searchParams.ticketId || '',
    title: searchParams.title || '',
    content: searchParams.content || '',
    source: searchParams.source || null,
    creator: searchParams.creator || '',
    startTime: searchParams.startTime || '',
    endTime: searchParams.endTime || '',
    tagsIds: searchParams.tagsIds || '' // 新增：处理标签搜索参数
  }
  
  // 清空搜索框关键词
  searchKeyword.value = ''
  
  currentPage.value = 1 // 重置到第一页
  fetchCorpusList() // 重新获取数据
}

// 添加导出Excel状态
const isExporting = ref(false)

// 导出Excel功能
const exportExcel = async () => {
  // 检查是否选择了值班组
  if (!currentTeam.value) {
    ElMessage.error('请先选择值班组')
    return
  }

  try {
    isExporting.value = true
    
    // 构建导出参数，与搜索参数一致
    const exportParams: Record<string, any> = {
      rgId: currentTeam.value,
      misId: currentMisId.value,
      strMatch: searchKeyword.value || ''
    }
    
    // 添加高级搜索参数
    if (advancedSearchParams.value.ticketId) {
      exportParams.ticketId = advancedSearchParams.value.ticketId
    }
    if (advancedSearchParams.value.title) {
      exportParams.title = advancedSearchParams.value.title
    }
    if (advancedSearchParams.value.content) {
      exportParams.content = advancedSearchParams.value.content
    }
    if (advancedSearchParams.value.source !== null) {
      exportParams.source = advancedSearchParams.value.source
    }
    if (advancedSearchParams.value.creator) {
      exportParams.creator = advancedSearchParams.value.creator
    }
    if (advancedSearchParams.value.startTime) {
      exportParams.startTime = advancedSearchParams.value.startTime
    }
    if (advancedSearchParams.value.endTime) {
      exportParams.endTime = advancedSearchParams.value.endTime
    }
    // 新增：添加标签筛选参数
    if (advancedSearchParams.value.tagsIds) {
      exportParams.tagsIds = advancedSearchParams.value.tagsIds
    }
    
    console.log('导出Excel参数:', exportParams)
    
    // 调用导出接口
    const res = await httpRequest.rawRequestGet(API_PATHS.EXPORT_ALL_CORPUS_TO_EXCEL, exportParams)
    
    if (res.code === 0 && res.data?.excelDownloadLink) {
      // 创建下载链接并触发下载
      const link = document.createElement('a')
      link.href = res.data.excelDownloadLink
      link.target = '_blank'
      link.download = '语料导出.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      ElMessage.success('导出成功')
    } else {
      ElMessage.error(res.msg || '导出失败')
    }
  } catch (error) {
    console.error('导出Excel失败:', error)
    ElMessage.error('导出Excel失败')
      } finally {
      isExporting.value = false
    }
  }

// 处理转换语料对话框保存  
const handleConvertDialogSave = async () => {
  try {
    await handleConvertSave()
  } catch (error) {
    console.error('TT群转语料：handleConvertDialogSave 调用 handleConvertSave 失败:', error)
  }
}

// 处理转换语料对话框检索对比
const handleConvertDialogCompare = () => {
  handleConvertCompare()
}

// 处理转换语料对话框关闭
const handleConvertDialogClose = () => {
  convertDialogVisible.value = false
}

// 监听合并预览对话框可见性
watch(() => mergePreviewDialogVisible.value, async (newVisible) => {
  if (newVisible) {
    // 设置初始化标志
    isMergeInitializing.value = true
    
    // 对话框打开时获取标签列表
    await fetchMergeTagList()
    
    // 等待标签列表加载完成后再初始化选中状态
    await nextTick()
    
    // 初始化选中的标签状态
    const originalTagsIds = mergePreviewForm.value.tagsIds
    if (originalTagsIds) {
      const tagIds = originalTagsIds.split(',').filter(id => id.trim())
      mergeSelectedTagIds.value = tagIds
    } else {
      mergeSelectedTagIds.value = []
    }
    
    // 完成初始化
    isMergeInitializing.value = false
  } else {
    // 对话框关闭时重置标签状态
    mergeSelectedTagIds.value = []
    isMergeInitializing.value = false
  }
})

// 监听mergeSelectedTagIds变化，过滤掉空值
watch(mergeSelectedTagIds, (newTagIds) => {
  // 过滤掉空字符串，防止"添加标签"选项被误选
  const filteredTagIds = newTagIds.filter(tag => tag !== '')
  if (filteredTagIds.length !== newTagIds.length && !isMergeInitializing.value) {
    mergeSelectedTagIds.value = filteredTagIds
  }
}, { deep: true })

// 添加AddCorpusDialog组件的引用
const addCorpusDialogRef = ref<InstanceType<typeof AddCorpusDialog> | null>(null)

// 用于控制CustomWorkSpace的默认标签
const customWorkSpaceDefaultTab = ref('sop')

// 处理打开标签管理面板的方法
const handleOpenTagManagement = (e?: Event) => {
  // 阻止默认行为和事件冒泡
  if (e) {
    e.preventDefault()
    e.stopPropagation()
  }
  
  // 确保"添加标签"的空值不会被添加到选中的标签中
  // 移除可能被意外添加的空字符串值
  mergeSelectedTagIds.value = mergeSelectedTagIds.value.filter(tag => tag !== '')
  
  // 强制关闭下拉框但保留当前对话框状态
  nextTick(() => {
    const selectComponents = document.querySelectorAll('.el-select')
    selectComponents.forEach((select: any) => {
      if (select.__vue__ && select.__vue__.blur) {
        select.__vue__.blur()
      }
    })
  })
  
  // 设置默认标签为标签管理
  customWorkSpaceDefaultTab.value = 'tagManagement'
  
  // 直接打开CustomWorkSpace，保留当前对话框状态
  customWorkSpaceVisible.value = true
}

// 处理标签列表头问号图标点击事件
const handleOpenTagManagementTooltip = (e?: Event) => {
  // 调用已有的标签管理打开方法
  handleOpenTagManagement(e)
}

// 处理标签更新事件
const handleTagUpdated = () => {
  // 刷新编辑语料对话框的标签列表
  if (editCorpusDialogRef.value) {
    editCorpusDialogRef.value.refreshTagList()
  }
  
  // 刷新新增语料对话框的标签列表
  if (addCorpusDialogRef.value) {
    addCorpusDialogRef.value.refreshTagList()
  }
  
  // 刷新合并预览对话框的标签列表
  if (mergePreviewDialogVisible.value) {
    fetchMergeTagList()
  }
  
  console.log('标签已更新，已刷新相关对话框的标签列表')
}

// 添加EditCorpusDialog组件的引用
const editCorpusDialogRef = ref<InstanceType<typeof EditCorpusDialog> | null>(null)
</script>

<template>
  <div class="corpus-management">
    <el-container style="height: 100%;">
      <el-main :style="!currentTeam ? 'padding: 0; background-color: #fff;' : 'padding: 0; overflow: hidden;'">
        <!-- 未选择值班组时的欢迎页面 -->
        <div v-if="!currentTeam" class="welcome-container">
          <h1 class="welcome-title">欢迎使用大模型语料处理服务</h1>
          <div class="team-selection-container">
            <span class="selection-label">请选择值班组：</span>
            <el-select
              v-model="currentTeam"
              placeholder="请选择值班组"
              class="team-select"
              :size="large"
            >
              <el-option
                v-for="item in shiftTeams"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="footer-text">© 2025 大模型语料处理服务</div>
        </div>

        <!-- 选择值班组后显示的内容 -->
        <template v-else>
          <!-- 页面标题 -->
          <div class="page-title">
            <div class="title-content">
              <h2>大模型语料处理服务</h2>
              <el-tooltip
                placement="top"
                effect="light"
              >
                <template #content>
                  <span>
                    <a href="https://km.sankuai.com/collabpage/2708110417" target="_blank" class="help-link">使用指南</a>
                  </span>
                </template>
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="title-actions">
              <el-button
                type="info"
                class="custom-sop-button"
                @click="handleCustomSOP"
                :size="large"
                title="设置"
              >
                <el-icon><Setting /></el-icon>设置
              </el-button>
              <el-button
                class="refresh-btn"
                :class="{ 'is-loading': tableLoading }"
                @click="debouncedHandleRefreshList"
                title="刷新语料列表"
                :disabled="tableLoading || isRefreshing"
                :loading="isRefreshing"
              >
                <el-icon v-if="!tableLoading && !isRefreshing"><Refresh /></el-icon>
                <el-icon v-else class="is-loading"><Loading /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 顶部操作区 -->
          <div class="operation-area">
            <!-- 值班组选择 -->
            <div class="shift-team-select">
              <el-select
                v-model="currentTeam"
                placeholder="请选择值班组"
                class="team-select"
                :size="large"
              >
                <el-option
                  v-for="item in shiftTeams"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              
            </div>

            <!-- 搜索和功能按钮区 -->
            <div class="search-and-functions">
              <div class="search-area">
                <div class="search-box">
                  <el-input
                    v-model="searchKeyword"
                    placeholder="可搜索ID、标题、内容"
                    class="search-input"
                    :size="large"
                    @keydown="handleKeywordKeydown"
                  >
                    <template #prefix>
                      <el-icon :size="20"><Search /></el-icon>
                    </template>
                  </el-input>
                  <el-button type="primary" @click="debouncedHandleSearch" :size="large" :loading="tableLoading" :disabled="tableLoading">
                    <el-icon :size="20"><Search /></el-icon>搜索
                  </el-button>
                  
                  <!-- 高级检索组件 -->
                  <AdvancedSearch @search="handleAdvancedSearch" @open-tag-management="handleOpenTagManagement" :rgId="currentTeam" :misId="currentMisId" />
                </div>
              </div>
              
              <div class="function-buttons">
                <el-button type="default" @click="debouncedHandleManualAdd" :size="large">
                  <el-icon :size="20"><Plus /></el-icon>手动新增
                </el-button>
                <el-button type="success" @click="debouncedHandleTTImport" :size="large">
                  <el-icon :size="20"><Tickets /></el-icon>TT群转语料
                </el-button>
                <el-button type="warning" @click="debouncedHandleKmWikiImport" :size="large">
                  <el-icon :size="20"><Document /></el-icon>学城文档添加
                </el-button>
                <div class="action-group">
                  <TaskBadge :rg-id="currentTeam" :mis-id="currentMisId">
                    <el-button @click="handleViewTaskList">
                      <el-icon><List /></el-icon>
                      任务列表
                    </el-button>
                  </TaskBadge>
                </div>
              </div>
            </div>
            
            <!-- 工作空间权限提示 -->
            <el-alert
              v-if="!hasWorkspacePermission && currentTeam"
              type="warning"
              class="workspace-permission-alert"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="permission-message">
                  当前值班组未绑定自定义工作空间，可能影响部分功能使用，请先绑定工作空间，<a href="https://km.sankuai.com/collabpage/2708110417#b-7db99856020d4bdf90bd97be217abc08" target="_blank" class="reference-link">使用手册</a>
                </div>
              </template>
            </el-alert>

            <!-- 批量操作按钮 -->
            <div class="batch-operations">
              <el-button 
                type="danger" 
                @click="debouncedHandleBatchDelete" 
                :size="large"
                :disabled="selectedRows.length === 0 || isBatchDeleting"
                :loading="isBatchDeleting"
                :class="{ 'delete-button': true, 'delete-button-disabled': selectedRows.length === 0 }"
              >
                <el-icon :size="20"><Delete /></el-icon>批量删除
              </el-button>
              <el-button 
                @click="debouncedHandleMergeCorpus" 
                :size="large"
                :disabled="selectedRows.length < 2 || isMerging"
                :loading="isMerging"
                :class="{ 'merge-button': true, 'merge-button-disabled': selectedRows.length < 2 }"
              >
                <el-icon :size="20"><Connection /></el-icon>合并语料
              </el-button>
              <el-button 
                @click="debouncedHandleCompare" 
                :size="large"
                :disabled="selectedRows.length !== 1 || isComparing"
                :loading="isComparing"
                :class="{ 'compare-button': true, 'compare-button-disabled': selectedRows.length !== 1 }"
              >
                <el-icon :size="20"><SearchIcon /></el-icon>检索相似
              </el-button>
              <el-button 
                type="primary" 
                plain
                class="export-excel-btn"
                @click="exportExcel" 
                :loading="isExporting" 
                :size="large"
              >
                <el-icon :size="20"><Download /></el-icon>导出Excel
              </el-button>
            </div>
          </div>

          <!-- 表格区域 -->
          <div class="table-area">
            <el-table
              :data="tableData"
              style="width: 100%"
              :size="large"
              :max-height="'100%'"
              border
              @selection-change="handleSelectionChange"
              v-loading="tableLoading"
              element-loading-text="正在加载语料列表..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
            >
              <el-table-column type="selection" width="55" fixed="left" />
              <!-- 动态列 -->
              <el-table-column
                v-for="col in columns"
                :key="col.prop"
                :prop="col.prop"
                :label="col.label"
                :width="col.width"
                :min-width="col.minWidth"
                :fixed="col.fixed"
                :show-overflow-tooltip="col.showOverflowTooltip"
              >
                <!-- 为标签列添加自定义表头模板 -->
                <template v-if="col.prop === 'tagsname'" #header>
                  <span style="display: flex; align-items: center; gap: 6px;">
                    标签
                    <el-tooltip
                      placement="top"
                      effect="dark"
                    >
                      <template #content>
                        您可以在设置面板的"分类标签管理"部分进行标签管理
                      </template>
                      <el-icon 
                        style="cursor: pointer; color: #909399; font-size: 14px;"
                        @click="handleOpenTagManagementTooltip"
                      >
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </span>
                </template>
                
                <!-- 为内容列添加自定义模板 -->
                <template v-if="col.prop === 'content'" #default="scope">
                  <el-tooltip
                    effect="dark"
                    :content="formatContentForDisplay(scope.row.content)"
                    placement="top"
                    :hide-after="0"
                  >
                    <div class="content-display">{{ formatContentForDisplay(scope.row.content) }}</div>
                  </el-tooltip>
                </template>
                
                <!-- 为标签列添加自定义模板 -->
                <template v-if="col.prop === 'tagsname'" #default="scope">
                  <div class="tags-display">
                    <template v-if="scope.row.tagsname && scope.row.tagsname.length > 0">
                      <el-tag
                        v-for="(tag, index) in scope.row.tagsname.slice(0, 2)"
                        :key="index"
                        size="small"
                        class="tag-item"
                        type="primary"
                      >
                        {{ tag }}
                      </el-tag>
                      <el-tooltip
                        v-if="scope.row.tagsname.length > 2"
                        :content="scope.row.tagsname.slice(2).join(', ')"
                        placement="top"
                      >
                        <el-tag size="small" class="tag-item" type="info">
                          +{{ scope.row.tagsname.length - 2 }}
                        </el-tag>
                      </el-tooltip>
                    </template>
                    <span v-else class="no-tags">-</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="150">
                <template #default="scope">
                  <el-button link type="primary" :size="large" @click="handleView(scope.row)">
                    <el-icon :size="16"><View /></el-icon>
                  </el-button>
                  <el-button link type="primary" :size="large" @click="handleEdit(scope.row)">
                    <el-icon :size="16"><Edit /></el-icon>
                  </el-button>
                  <el-button link type="danger" :size="large" @click="debouncedHandleDelete(scope.row)" :loading="isDeletingItem[scope.row.ticketId]" :disabled="isDeletingItem[scope.row.ticketId]">
                    <el-icon :size="16"><Delete /></el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页组件 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="pageSizes"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @current-change="debouncedHandleCurrentChange"
                @size-change="debouncedHandleSizeChange"
                :pager-count="7"
                :disabled="!total"
              />
            </div>
          </div>
        </template>
      </el-main>
    </el-container>

    <!-- 新增语料对话框组件 -->
    <AddCorpusDialog
      ref="addCorpusDialogRef"
      v-model="dialogVisible"
      :current-mis-id="currentMisId"
      :current-team="currentTeam"
      :size="large"
      @refresh="fetchCorpusList"
      @open-tag-management="handleOpenTagManagement"
    />

    <!-- 合并语料对话框 -->
    <el-dialog
      v-model="mergeDialogVisible"
      title="合并语料"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="mergeFormRef"
        :model="mergeFormData"
        :rules="mergeRules"
        label-width="100px"
      >
        <el-form-item label="合并后标题" prop="title">
          <el-input
            v-model="mergeFormData.title"
            placeholder="请输入合并后标题"
            :size="large"
          />
        </el-form-item>
        <el-form-item label="合并后内容" prop="content">
          <el-input
            v-model="mergeFormData.content"
            type="textarea"
            :rows="16"
            placeholder="请输入合并后内容"
            :size="large"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleMergeCancel" :size="large">取消</el-button>
          <el-button type="primary" @click="handleMergeSubmit" :size="large">确认合并</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 检索对比对话框 -->
    <el-drawer
      v-model="compareDialogVisible"
      title="相似语料检索结果"
      size="40%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="compare-dialog"
      direction="rtl"
      :modal="false"
      :append-to-body="true"
      :destroy-on-close="false"
      @closed="handleCompareClose"
    >
      <div class="compare-header">
        <div class="compare-tip">
          共找到 {{ compareResults.length }} 条相似内容
          <div class="sub-tip">系统根据语义相似度对语料进行了排序，您可以选择一条或多条语料进行合并或删除操作。</div>
        </div>
      </div>

      <div class="compare-list">
        <div v-for="(item, index) in compareResults" :key="index" class="compare-item">
          <div class="compare-item-header">
            <div class="left-section">
              <el-checkbox 
                v-model="item.selected" 
                @change="(val) => handleItemSelect(item, val)"
              />
              <div class="task-id">{{ item.ticketId }}</div>
              <div class="similarity-score" :class="getSimilarityClass(item.score)">
                相似度 {{ item.score }}
              </div>
            </div>
            <div class="actions">
              <el-button link type="primary" @click="handleViewDetail(item)">
                查看详情
              </el-button>
            </div>
          </div>
          <div class="compare-item-content">
            <div class="content-text">{{ formatContentForDisplay(item.content) }}</div>
          </div>
          <div class="compare-item-footer">
            <div class="update-time">更新时间：{{ item.updateTime }}</div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="drawer-footer">
          <div class="selected-count" v-if="selectedCompareRows.length > 0">
            已选择 {{ selectedCompareRows.length }} 项
          </div>
          <div class="footer-buttons">
            <el-button 
              @click="handleMergeSelected" 
              :size="large" 
              class="merge-btn"
              :disabled="compareSource.value === 'main' ? selectedCompareRows.length === 0 : selectedCompareRows.length === 0"
            >
              <el-icon><Connection /></el-icon>合并语料
            </el-button>
            <el-button 
              @click="handleBatchDeleteSelected" 
              :size="large" 
              class="delete-btn"
              :disabled="selectedCompareRows.length === 0"
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
            <el-button @click="compareDialogVisible = false" :size="large">关闭</el-button>
          </div>
        </div>
      </template>
    </el-drawer>

    <!-- 查看语料对话框 -->
    <ViewCorpusDialog
      v-model="viewDialogVisible"
      :corpus-data="currentViewItem"
    />

    <!-- 编辑语料对话框 -->
    <EditCorpusDialog
      ref="editCorpusDialogRef"
      v-model="editDialogVisible"
      :form-data="editFormData"
      :current-edit-id="currentEditId"
      :current-mis-id="currentMisId"
      :current-team="currentTeam"
      :is-submitting="isEditSubmitting"
      :size="large"
      @update:form-data="editFormData = $event"
      @refresh="fetchCorpusList"
      @cancel="handleEditCancel"
      @open-tag-management="handleOpenTagManagement"
    />

    <!-- TT群转语料对话框 -->
    <el-dialog
      v-model="ttDialogVisible"
      title="TT群转语料"
      width="90%"
      :fullscreen="ttDialogFullscreen"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      class="tt-dialog"
      top="4vh"
    >
      <template #header="{ close }">
        <div class="tt-dialog-header">
          <span>TT群转语料</span>
          <div class="tt-dialog-buttons">
            <el-button
              link
              @click="fetchTTList"
              class="refresh-button"
              title="刷新列表"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
            <el-button
              link
              @click="handleTTFullscreen"
              class="fullscreen-button"
              :title="ttDialogFullscreen ? '退出全屏' : '全屏'"
            >
              <el-icon v-if="ttDialogFullscreen"><Minus /></el-icon>
              <el-icon v-else><FullScreen /></el-icon>
            </el-button>
            <el-button
              link
              @click="close"
              class="close-button"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
      </template>

      <div class="tt-table-container">
        <!-- 添加过滤面板 -->
        <TTFilterPanel 
          v-model="ttFilterData" 
          @filter-change="debouncedHandleTTFilterChange"
          :key="ttFilterKey"
        />
        
        <!-- 添加选中提示信息 -->
        <div v-if="selectedTTCount > 0" class="tt-selected-info">
          <div class="selected-info">
            已选中 <span class="selected-count">{{ selectedTTCount }}</span> 条语料
          </div>
          <div class="action-buttons">
            <el-button type="primary" size="default" @click="handleBatchConvert">批量转换</el-button>
            <el-button @click="clearTTSelection">清空选择</el-button>
          </div>
        </div>
        <el-table
          :data="ttTableData"
          style="width: 100%; height: 100%; table-layout: fixed;"
          :size="large"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          v-loading="ttLoading"
          element-loading-text="正在加载TT列表..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0.9)"
          row-key="ticketId"
          :row-class-name="getRowClass"
        >
          <!-- 自定义选择列 -->
          <el-table-column
            width="55"
            align="center"
          >
            <template #header>
              <input 
                type="checkbox" 
                ref="headerCheckboxRef"
                :checked="ttTableData.length > 0 && ttTableData.every(row => ttSelectedMap?.value?.has(row.ticketId))"
                @click="handleSelectAll()"
                style="cursor: pointer;"
              />
            </template>
            <template #default="{ row }">
              <el-tooltip
                v-if="!isRowSelectable(row)"
                :content="getRowDisabledReason(row)"
                placement="top"
                effect="dark"
              >
                <input 
                  type="checkbox" 
                  :checked="isRowSelected(row)"
                  @change="handleTTRowSelectionChange(row)"
                  :key="row.ticketId"
                  :disabled="!isRowSelectable(row)"
                  :style="!isRowSelectable(row) ? 'cursor: not-allowed; opacity: 0.5;' : 'cursor: pointer;'"
                />
              </el-tooltip>
              <input 
                v-else
                type="checkbox" 
                :checked="isRowSelected(row)"
                @change="handleTTRowSelectionChange(row)"
                :key="row.ticketId"
                style="cursor: pointer;"
              />
            </template>
          </el-table-column>
          <el-table-column
            v-for="col in ttColumns"
            :key="col.prop"
            :prop="col.prop"
            :label="col.label"
            :width="col.width"
            :min-width="col.minWidth"
            :show-overflow-tooltip="col.showOverflowTooltip"
          >
            <template #default="scope" v-if="col.prop === 'kbStatus'">
                <el-tag :type="kbStatusMap[scope.row.kbStatus]?.type || 'info'">
                  {{ kbStatusMap[scope.row.kbStatus]?.label || '未知状态' }}
              </el-tag>
            </template>
              <template #default="scope" v-else-if="col.prop === 'kbUpdateUser'">
                {{ scope.row.kbUpdateUser || '-' }}
              </template>
              <template #default="scope" v-else-if="col.prop === 'kbTimestamp'">
                {{ scope.row.kbTimestamp || '-' }}
              </template>
          </el-table-column>
            <el-table-column label="操作" fixed="right" width="80">
            <template #default="scope">
                <el-dropdown trigger="click">
                  <el-button class="more-action-btn" :size="large" text>
                    <el-icon :size="20"><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleViewTT(scope.row)">
                        <el-icon><View /></el-icon>查看TT
                      </el-dropdown-item>
                      <el-dropdown-item 
                        v-if="scope.row.kbStatus !== 4 && scope.row.kbStatus !== 5" 
                        @click="handleConvertToCorpus(scope.row)"
                        :disabled="isCreatedTimeExceedThreeYears(scope.row.createdAt)"
                        :class="{'disabled-dropdown-item': isCreatedTimeExceedThreeYears(scope.row.createdAt)}"
                      >
                        <el-tooltip 
                          v-if="isCreatedTimeExceedThreeYears(scope.row.createdAt)" 
                          content="只支持三年内的TT数据" 
                          placement="left" 
                          effect="dark"
                        >
                          <span>
                            <el-icon><Refresh /></el-icon>转换语料
                          </span>
                        </el-tooltip>
                        <span v-else>
                          <el-icon><Refresh /></el-icon>转换语料
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.kbStatus === 0" @click="handleMarkAsIgnore(scope.row)">
                        <el-icon><Close /></el-icon>无需入库
                      </el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.kbStatus === 1 || scope.row.kbStatus === 3" @click="handleViewTTCorpus(scope.row)">
                        <el-icon><View /></el-icon>查看语料
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <div class="tt-pagination">
          <el-pagination
            v-model:current-page="ttPagination.currentPage"
            :page-size="ttPagination.pageSize"
            :page-sizes="[5, 10, 20, 50]"
            :total="ttPagination.total"
            :pager-count="7"
            @current-change="debouncedHandleTTPageChange"
            @size-change="debouncedHandleTTSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
          />
          </div>
      </div>
    </el-dialog>

    <!-- 转换语料对话框 -->
    <ConvertCorpusDialog
      v-model="convertDialogVisible"
      :form-data="convertFormData"
      :current-mis-id="currentMisId"
      :current-emp-id="currentEmpId"
      :current-team="currentTeam"
      :task-missing-info="taskMissingInfo"
      :is-loading="isConvertSaving"
      :size="large"
      @update:form-data="convertFormData = $event"
      @save="handleConvertDialogSave"
      @compare="handleConvertDialogCompare"
      @close="handleConvertDialogClose"
    />

    <!-- 合并预览对话框 -->
    <el-dialog
      v-model="mergePreviewDialogVisible"
      title="合并语料预览"
      width="70%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="merge-preview-dialog"
    >
      <el-form label-width="100px">
        <el-form-item label="标题">
          <el-input
            v-model="mergePreviewForm.title"
            :size="large"
          />
        </el-form-item>
        
        <!-- 添加标签选择 -->
        <el-form-item label="标签" class="tags-item">
          <!-- 默认标签显示（当没有选择其他标签时） -->
          <div v-if="mergeSelectedTagIds.length === 0 && mergeDefaultTag" class="default-tag-container">
            <el-tag
              :closable="false"
              type="info"
              class="default-tag"
            >
              {{ mergeDefaultTag.name }}
            </el-tag>
          </div>
          
          <el-select
            v-model="mergeSelectedTagIds"
            multiple
            :placeholder="`请选择标签（最多3个，已选${mergeSelectedTagIds.length}个）`"
            :size="large"
            :loading="isMergeLoadingTags"
            @change="handleMergeTagSelectionChange"
            style="width: 100%"
            clearable
            :multiple-limit="3"
          >
            <el-option
              v-for="tag in mergeTagOptions"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            >
              {{ tag.name }}
            </el-option>
            <!-- 添加"添加标签"选项 -->
            <el-option
              key="add-tag-merge"
              label="📝 添加标签"
              value=""
              @click.stop="handleOpenTagManagement"
              style="border-top: 1px solid #e4e7ed;"
              :disabled="true"
            >
              <div style="display: flex; align-items: center; color: #409EFF; font-weight: 500; cursor: pointer;" @click.stop="handleOpenTagManagement">
                <el-icon style="margin-right: 8px; font-size: 14px;"><Plus /></el-icon>
                <span>添加标签</span>
              </div>
            </el-option>
          </el-select>
          <!-- 选择状态提示 -->
          <div v-if="mergeTagOptions.length > 0" style="margin-top: 8px; font-size: 12px; color: #909399;">
            共{{ mergeTagOptions.length }}个标签可选，已选择{{ mergeSelectedTagIds.length }}/3个
            <span v-if="mergeDefaultTag && mergeSelectedTagIds.length === 0">，当前显示默认标签</span>
          </div>
        </el-form-item>
        
        <el-form-item label="内容">
          <div class="edit-area">
            <MonacoEditor
              v-model="mergePreviewForm.content"
              language="markdown"
              height="400px"
              :options="{
                wordWrap: 'on',
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                lineNumbers: 'on',
                lineDecorationsWidth: 0,
                folding: true,
                renderLineHighlight: 'all',
                automaticLayout: true
              }"
              class="merge-preview-editor"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="debouncedHandleMergePreviewCancel" :size="large" :disabled="isMergePreviewSaving">取消</el-button>
          <el-button type="primary" @click="debouncedHandleMergePreviewSave" :size="large" :loading="isMergePreviewSaving" :disabled="isMergePreviewSaving">确认保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 语料转换加载进度面板 -->
    <LoadingProgressPanel
      v-model:visible="loadingPanel"
      ref="loadingPanelInstance"
      :title="loadingPanelTitle"
      message="系统正在处理您的请求..."
      :tips="loadingPanelTips"
      :duration="300000"
    />

    <!-- 添加任务列表对话框组件 -->
    <TaskListDialog
      v-model="taskListDialogVisible"
      :rgId="currentTeam"
    />
    
    <!-- 消息通知组件 -->
    <MessageNotification ref="messageNotificationRef" />
    
    <!-- 只保留内容区悬浮卡片，无el-dialog弹窗 -->
    <CustomWorkSpace
      v-if="customWorkSpaceVisible"
      ref="customWorkSpaceRef"
      :rg-id="currentTeam"
      :mis-id="currentMisId"
      :default-tab="customWorkSpaceDefaultTab"
      :visible="customWorkSpaceVisible"
      @close="customWorkSpaceVisible = false"
      @tag-updated="handleTagUpdated"
    />
  </div>
</template>

<style lang="scss" scoped>
.corpus-management {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .export-excel-btn {
    background-color: #f0f9ff;
    border-color: #409EFF;
    transition: all 0.3s;
    
    &:hover {
      background-color: #ecf5ff;
      color: #409EFF;
      border-color: #a0cfff;
    }
    
    .el-icon {
      margin-right: 4px;
    }
  }
  
  :deep(.el-container) {
    height: 100%;
    width: 100%;
    min-height: 0;
    overflow: hidden;
  }

  :deep(.el-main) {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0;
  }

  .page-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;
    
    .title-content {
      display: flex;
      align-items: center;
      gap: 12px;
      
      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      
      .help-icon {
        font-size: 16px;
        color: #909399;
        cursor: pointer;
        margin-left: 8px;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
    
    .title-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .custom-sop-button {
        background-color: #ebeef5;
        color: #4c4e52;
        border-radius: 4px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        border: 1px solid #dcdfe6;
        font-weight: normal;
        font-size: 14px;
        transition: all 0.25s ease;
        
        .el-icon {
          margin-right: 6px;
          font-size: 16px;
          color: #5a5e66;
          transition: color 0.25s ease;
        }
        
        &:hover {
          background-color: #e4e7ed;
          color: #303133;
          border-color: #c0c4cc;
          
          .el-icon {
            color: #303133;
          }
        }
        
        &:active {
          background-color: #dcdfe6;
          color: #303133;
          border-color: #c0c4cc;
        }
      }
      
      .refresh-btn {
        background-color: #ebeef5;
        color: #4c4e52;
        width: 32px;
        height: 32px;
        min-width: 32px;
        padding: 0;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #dcdfe6;
        transition: all 0.25s ease;
        
        .el-icon {
          font-size: 16px;
          color: #5a5e66;
          transition: all 0.25s ease;
        }
        
        &:hover {
          background-color: #e4e7ed;
          color: #303133;
          border-color: #c0c4cc;
          
          .el-icon {
            color: #303133;
          }
        }
        
        &:active {
          background-color: #dcdfe6;
          color: #303133;
          border-color: #c0c4cc;
        }
        
        &.is-loading {
          pointer-events: none;
          
          .is-loading {
            animation: rotating 1s linear infinite;
          }
        }
      }
      
      @keyframes rotating {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    }
  }

  :deep(.help-link) {
    color: #409EFF;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }

  .operation-area {
    flex-shrink: 0;
    padding: 20px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;

    .shift-team-select {
      flex: 0 0 auto;
      margin: 0;
      
      .team-select {
        width: 200px;
        :deep(.el-input__wrapper) {
          font-size: 14px;
        }
      }
    }

    .search-and-functions {
      flex: 1;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 10px;
      margin: 0;

      .search-box {
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
        min-width: 300px;

        .search-input {
          width: 300px;

          :deep(.el-input__wrapper) {
            font-size: 14px;
          }

          :deep(.el-input__prefix) {
            color: #909399;
          }
        }
      }

      .function-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;

        .el-button {
          font-size: 14px;
          padding: 10px 16px;
          white-space: nowrap;
          
          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }

    .batch-operations {
      flex-basis: 100%;
      display: flex;
      gap: 10px;
      margin: 0;

      .el-button {
        font-size: 14px;
        padding: 10px 16px;
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }

  .table-area {
    flex: 1;
    padding: 20px;
    background: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;

    :deep(.el-table) {
      height: 100%;
      
      .el-table__inner-wrapper {
        height: 100%;
      }

      .el-table__body-wrapper {
        overflow-y: auto;
      }

      // 设置表格横向滚动
      .el-table__body {
        min-width: 100%;
      }

      // 确保操作列固定在右侧
      .el-table__fixed-right {
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        box-shadow: -2px 0 6px rgba(0, 0, 0, 0.1);
      }

      .el-button {
        font-size: 14px;
        margin: 0 2px;
        padding: 6px;

        .el-icon {
          font-size: 14px;
        }
      }

      .cell {
        line-height: 1.5;
      }

      // 确保TT标题列优先展示
      :deep(.el-table__body td.el-table__cell[data-key="name"],
      .el-table__header th.el-table__cell[data-key="name"]) {
        min-width: 300px !important;
        width: auto !important;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}

.merge-button {
  &.merge-button-disabled {
    opacity: 0.8;
    cursor: not-allowed;
  }
}

.delete-button {
  &.delete-button-disabled {
    opacity: 0.8;
    cursor: not-allowed;
  }
}

.compare-button {
  &.compare-button-disabled {
    opacity: 0.8;
    cursor: not-allowed;
  }
}

.compare-dialog {
  :deep(.el-drawer__header) {
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  :deep(.el-drawer__body) {
    padding: 20px;
    overflow-y: auto;
  }

  .compare-header {
    margin-bottom: 20px;

    .compare-tip {
      font-size: 16px;
      color: #303133;
      line-height: 1.6;

      .sub-tip {
        margin-top: 8px;
        font-size: 14px;
        color: #909399;
        line-height: 1.5;
      }
    }
  }

  .compare-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding-bottom: 60px;

    .compare-item {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      border: 1px solid #e4e7ed;

      .compare-item-header {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;

        .left-section {
          display: flex;
          align-items: center;
          gap: 12px;

          :deep(.el-checkbox) {
            margin-right: 0;
          }

          .task-id {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
          }
        }

        .similarity-score {
          display: inline-block;
          padding: 2px 12px;
          border-radius: 12px;
          font-weight: 500;
          font-size: 13px;
          
          &.high {
            background-color: #e8f5e9;
            color: #4caf50;
          }
          
          &.medium {
            background-color: #fff3e0;
            color: #ff9800;
          }
          
          &.low {
            background-color: #ffebee;
            color: #f44336;
          }
        }

        .actions {
          .el-button {
            font-size: 13px;
            color: #409eff;
          }
        }
      }

      .compare-item-content {
        padding: 16px;
        
        .content-text {
          font-size: 14px;
          line-height: 1.6;
          color: #303133;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }
      }

      .compare-item-footer {
        padding: 12px 16px;
        border-top: 1px solid #f0f0f0;
        background: #fafafa;

        .update-time {
          font-size: 13px;
          color: #909399;
        }
      }
    }
  }

  .drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 20px;
    background: #fff;
    border-top: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .selected-count {
      font-size: 14px;
      color: #606266;
    }

    .footer-buttons {
      display: flex;
      gap: 10px;
    }
    
    .el-button {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .el-icon {
        font-size: 16px;
      }

      &[disabled] {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}



.edit-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  .edit-area {
    width: 100%;
    margin-bottom: 16px;
    
    :deep(.monaco-editor-container) {
      min-height: 320px;
    }
    
    .edit-corpus-editor {
      width: 100%;
      display: block;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
    }
    
    :deep(.monaco-editor-wrapper) {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      overflow: hidden;
    }
    
    :deep(.monaco-editor) {
      width: 100% !important;
    }
    
    :deep(.overflow-guard) {
      width: 100% !important;
    }
  }
}

.tt-dialog {
  height: 88vh;
  display: flex;
  flex-direction: column;
  
  :deep(.el-dialog__header) {
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }
  
  :deep(.el-dialog__body) {
    flex: 1;
    overflow: hidden; // 修改为hidden，避免出现双滚动条
    padding: 20px;
    display: flex;
    flex-direction: column;
  }
  
  .tt-table-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    min-height: 0; // 修复flex布局中的overflow问题
    
    .el-table {
      flex: 1;
      height: 0; // 配合flex:1使表格扩展占满空间
    }
    
    :deep(.el-table__inner-wrapper) {
      height: 100%;
    }
    
    :deep(.el-table__body-wrapper) {
      height: calc(100% - 48px) !important; // 减去表头高度
      overflow-y: auto;
    }
    
    // 确保TT标题列优先展示
    :deep(.cell) {
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    
    :deep(.el-table__row .el-table__cell:nth-child(2)) {
      min-width: 300px !important;
      width: auto !important;
    }
  }
  
  :deep(.el-dialog__headerbtn) {
    display: none !important;
  }

  .tt-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .tt-dialog-buttons {
      display: flex;
      gap: 8px;
      align-items: center;

      .el-button {
        padding: 4px;
        height: 32px;
        width: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .el-icon {
          font-size: 20px;
        }

        &:hover {
          background-color: #f5f7fa;
          border-radius: 4px;
        }

        &.fullscreen-button {
          color: #409EFF;
        }

        &.refresh-button {
          color: #409EFF;
        }

        &.close-button {
          color: #909399;
        }
      }
    }
  }

  :deep(.el-table) {
    .el-table__row {
      height: 60px;
    }

    .el-table__cell {
      padding: 12px 0;
    }

    .el-button {
      .el-icon {
        margin-right: 4px;
      }
    }
    
    // 三点按钮样式
    .more-action-btn {
      padding: 8px;
      background-color: transparent;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
      }
      
      .el-icon {
        margin-right: 0;
        color: #606266;
      }
    }
  }

  .tt-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    flex-shrink: 0; // 防止分页器被压缩

    :deep(.el-pagination) {
      .el-pager {
        li {
          min-width: 32px;
          height: 32px;
          line-height: 32px;
          font-weight: normal;
          margin: 0 4px;
          
          &.is-active {
            font-weight: bold;
          }
        }
      }
    }
  }
}

:deep(.convert-confirm-dialog) {
  .el-message-box__header {
    padding-top: 20px;
  }
  
  .el-message-box__title {
    font-size: 18px;
    font-weight: 600;
  }
  
  .el-message-box__content {
    padding: 30px 20px;
    font-size: 16px;
  }
  
  .el-message-box__btns {
    padding: 10px 20px 20px;
    
    .el-button {
      font-size: 14px;
      padding: 8px 20px;
      
      &--primary {
        background-color: #409EFF;
        border-color: #409EFF;
        
        &:hover {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
}

// 添加禁用按钮样式
.disabled-button {
  opacity: 0.5;
  cursor: not-allowed !important;
  
  &:hover {
    background-color: transparent !important;
  }
  
  .el-icon {
    opacity: 0.5;
  }
}

.convert-dialog {
  :deep(.el-dialog) {
    z-index: 2400;
  }
}

.compare-dialog {
  z-index: 2401;
}

.no-team-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background-color: #fff;
  border-radius: 8px;
  margin: 20px;

  :deep(.el-empty) {
    padding: 40px;

    .el-empty__description {
      margin-top: 20px;
      
      p {
        color: #909399;
        font-size: 16px;
        line-height: 1.6;
      }
    }
  }
}

.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 50px); /* 调整高度，减去额外的空间避免滚动条 */
  background-color: #fff;
  width: 100%;
  position: relative;

  .welcome-title {
    font-size: 32px;
    font-weight: 400;
    color: #202124;
    // margin: 80px 0 50px;
    text-align: center;
    letter-spacing: -0.5px;
    line-height: 1.3;
    max-width: 700px;
  }

  .team-selection-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    width: 100%;
    max-width: 500px;
    position: relative;
    margin: 0 auto;

    .selection-label {
      font-size: 18px;
      font-weight: 400;
      color: #5f6368;
      white-space: nowrap;
    }

    .team-select {
      width: 320px;
      :deep(.el-input__wrapper) {
        font-size: 16px;
        border-radius: 24px;
        height: 48px;
        padding: 0 16px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);
        border: 1px solid transparent;
        
        &:hover, &.is-focus {
          box-shadow: 0 2px 10px rgba(32, 33, 36, 0.35);
        }
      }

      :deep(.el-input__suffix) {
        color: #5f6368;
      }

      :deep(.el-select-dropdown__item) {
        font-size: 14px;
        padding: 10px 20px;
        
        &.selected {
          font-weight: normal;
          color: #202124;
          background-color: #f1f3f4;
        }
        
        &:hover {
          background-color: #f1f3f4;
        }
      }
    }
  }

  .footer-text {
    position: absolute;
    bottom: 20px;
    color: #5f6368;
    font-size: 14px;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;

  :deep(.el-pagination) {
    .el-pager {
      li {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        font-weight: normal;
        margin: 0 4px;
        
        &.is-active {
          font-weight: bold;
        }
      }
    }
  }
}

// Monaco Editor样式调整
:deep(.monaco-editor-wrapper) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  
  .editor-toolbar {
    background-color: #f7f9fc;
  }
}

.edit-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  :deep(.el-form-item.editor-form-item) {
    margin-bottom: 0;
    
    .el-form-item__content {
      height: 360px;
      min-height: 320px;
      overflow: visible;
    }
    
    .monaco-editor-wrapper {
      height: 100%;
      min-height: 320px;
      
      .monaco-editor-container,
      :deep(.monaco-editor) {
        height: calc(100% - 40px) !important;
      }
    }
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
  }
}

// 确保对话框中的编辑器内容区域足够高
.el-dialog {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  margin: 0 auto !important;
  
  :deep(.el-dialog__body) {
    flex: 1;
    overflow: auto;
  }
}

// 新增语料对话框中Monaco编辑器的样式
.add-corpus-editor {
  width: 100%;
  min-height: 320px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* 处理Monaco编辑器在对话框中的显示 */
:deep(.monaco-editor-wrapper) {
  margin: 0;
  
  &.fullscreen {
    z-index: 3001;
  }
}

/* 确保编辑器宽度正确 */
:deep(.monaco-editor) {
  width: 100% !important;
  
  .overflow-guard {
    width: 100% !important;
  }
}

/* 查看语料对话框中Monaco编辑器的样式 */
.view-corpus-editor {
  width: 100%;
  min-height: 320px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  
  :deep(.editor-toolbar) {
    .reset-btn {
      display: none !important; // 强制隐藏重置按钮
    }
  }
  
  :deep(.monaco-editor-wrapper) {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
}

/* 转换语料对话框编辑器样式 */
.convert-corpus-editor {
  .monaco-editor-wrapper {
    width: 100%;
    min-height: 320px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;

    .monaco-editor-container {
      min-height: 320px;
    }
  }
}

/* 查看语料对话框编辑器样式 */
.view-corpus-editor {
  width: 100%;
  
  .monaco-editor-wrapper {
    width: 100%;
    min-height: 320px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;

    .monaco-editor-container {
      min-height: 320px;
    }
    
    .reset-btn {
      display: none !important;
    }
  }
}

/* 合并预览对话框编辑器样式 */
.merge-preview-editor {
  width: 100%;
  min-height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  
  :deep(.monaco-editor-wrapper) {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    height: 100%;
  }
  
  :deep(.monaco-editor) {
    width: 100% !important;
  }
  
  :deep(.overflow-guard) {
    width: 100% !important;
  }
}

/* 合并预览对话框样式 */
.merge-preview-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 30px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #303133;
  }

  .edit-area {
    width: 100%;
    margin-bottom: 16px;
  }
  
  /* 标签管理样式 */
  .tags-item {
    margin-bottom: 16px;
    
    .default-tag-container {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      
      .default-tag {
        background-color: #f4f4f5;
        border-color: #d3d4d6;
        color: #606266;
        font-size: 12px;
        
        &:hover {
          background-color: #f4f4f5;
          border-color: #d3d4d6;
        }
      }
    }
    
    :deep(.el-form-item__label) {
      color: #606266;
      font-weight: 600;
      font-size: 14px;
      min-width: 80px;
      white-space: nowrap;
      margin-right: 8px;
    }
    
    :deep(.el-form-item__content) {
      flex: 1;
      margin-left: 0 !important;
    }
    
    :deep(.el-select) {
      width: 100%;
      
      .el-select__wrapper {
        width: 100%;
      }
      
      .el-select__tags {
        flex-wrap: wrap;
        max-width: 100%;
      }
      
      .el-tag {
        margin: 2px 4px 2px 0;
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
        font-size: 12px;
        
        &:hover {
          background-color: #bae7ff;
          border-color: #69c0ff;
        }
        
        .el-tag__close {
          background-color: rgba(24, 144, 255, 0.1);
          color: #1890ff;
          border-radius: 50%;
          
          &:hover {
            background-color: #1890ff;
            color: #fff;
          }
        }
      }
    }
  }
}

.tt-table-container {
  height: calc(100vh - 200px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  /* 添加选中信息样式 */
  .tt-selected-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 0;
    margin-bottom: 10px;
  }

  .selected-info {
    font-size: 15px;
    color: #606266;
    
    .selected-count {
      color: #409EFF;
      font-weight: 500;
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    margin-left: auto;  /* 将按钮推到右边 */
    
    .el-button {
      font-size: 14px;
      padding: 9px 16px;
      height: 36px;
      
      &.el-button--primary {
        font-weight: 500;
      }
    }
  }

  .el-table {
    flex: 1;
    overflow: auto;
    
    /* 添加选中行样式 */
    .selected-row {
      background-color: #f0f9ff;
      
      td {
        background-color: #f0f9ff !important;
      }
      
      &:hover td {
        background-color: #e6f3ff !important;
      }
    }
  }
  
  .tt-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }
}

/* 调整对话框内容区域的内边距 */
:deep(.el-dialog) {
  .el-dialog__body {
    padding-top: 12px;  /* 只调整上方内边距 */
  }
}

.action-group {
  display: inline-flex;
  align-items: center;
  margin-left: 12px;
}

/* 添加样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  .header-title {
    font-weight: bold;
    font-size: 15px;
    color: #303133;
  }
}

// 添加禁用下拉菜单项样式
:deep(.disabled-dropdown-item) {
  cursor: not-allowed !important;
  opacity: 0.5;
  color: #a8a8a8 !important;
  
  .el-icon {
    color: #a8a8a8 !important;
  }
}

.custom-workspace-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
    min-height: 600px;
  }
  
  :deep(.el-dialog__header) {
    padding: 16px 20px;
    margin-right: 0;
    border-bottom: 1px solid #ebeef5;
  }
}

// 添加自定义标签样式
:deep(.el-tag.el-tag--no-group) {
  --el-tag-bg-color: #e8d4fd;
  --el-tag-border-color: #d4b4ff;
  --el-tag-text-color: #7b1fa2;
  --el-tag-hover-color: #6a1b9a;
}

.search-and-functions {
      flex: 1;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 10px;
      margin: 0;

      .search-box {
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
        min-width: 300px;

        .search-input {
          width: 300px;

          :deep(.el-input__wrapper) {
            font-size: 14px;
          }

          :deep(.el-input__prefix) {
            color: #909399;
          }
        }
      }

      .search-area {
        display: flex;
        flex-direction: column;
        position: relative;
        min-width: 300px;

        .search-box {
          display: flex;
          align-items: center;
          gap: 10px;
          flex-wrap: nowrap;
          position: relative;

          .search-input {
            width: 300px;

            :deep(.el-input__wrapper) {
              font-size: 14px;
            }

            :deep(.el-input__prefix) {
              color: #909399;
            }
          }
        }
      }
}

/* 工作空间权限提示样式 */
.workspace-permission-alert {
  margin: 10px 0;
  width: 100%;
  border-radius: 4px;
  
  :deep(.el-alert__content) {
    width: 100%;
  }
  
  :deep(.el-alert__icon) {
    font-size: 16px;
    margin-right: 10px;
  }
  
  .permission-message {
    font-size: 14px;
    width: 100%;
    line-height: 1.5;
    
    .reference-link {
      color: #1890ff;
      text-decoration: underline;
      
      &:hover {
        color: #40a9ff;
      }
    }
  }
}

/* 内容显示样式 */
.content-display {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  color: #606266;
  font-size: 14px;
  
  /* 确保内容在表格单元格中正确显示 */
  word-break: break-word;
  word-wrap: break-word;
}

/* 表格内容列的tooltip样式优化 */
:deep(.el-table .el-table__cell) {
  .content-display {
    cursor: pointer;
    
    &:hover {
      color: #409EFF;
    }
  }
}

/* 标签显示样式 */
.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  
  .tag-item {
    margin: 0;
    font-size: 12px;
    border-radius: 4px;
    
    &:not(:last-child) {
      margin-right: 4px;
    }
  }
  
  .no-tags {
    color: #c0c4cc;
    font-size: 14px;
  }
}

/* 标签列表样式优化 */
:deep(.el-table .el-table__cell) {
  .tags-display {
    min-height: 24px;
    padding: 2px 0;
  }
}

/* 内容显示样式 */
.content-text {
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 14px;
  color: #303133;
}

/* 全局z-index层级控制 - 确保标签管理弹窗显示在最上层 */
:deep(.custom-workspace) {
  z-index: 2500 !important;
}

/* 确保标签管理相关的所有弹窗都有足够高的z-index */
:deep(.tag-dialog) {
  z-index: 3500 !important;
}

:deep(.tag-add-dialog) {
  z-index: 3500 !important;
}

:deep(.tag-edit-dialog) {
  z-index: 3510 !important;
}

/* 确保Element Plus的消息确认框有更高的z-index */
:deep(.el-message-box) {
  z-index: 9999 !important;
}

/* 确保所有标签相关的下拉框显示在分类标签管理面板下方 */
:deep(.el-select-dropdown) {
  z-index: 2300 !important;
}

/* 全局控制Element Plus下拉框的z-index */
:deep(.el-popper.is-pure) {
  z-index: 2300 !important;
}

:deep(.el-popper[data-popper-placement]) {
  z-index: 2300 !important;
}

/* 全局控制Element Plus对话框的z-index */
:deep(.el-overlay) {
  &[z-index] {
    z-index: 3000 !important;
  }
}

/* 确保标签删除确认对话框在标签管理面板上方 */
:deep(.tag-delete-confirm) {
  z-index: 3600 !important;
}

/* 确保append-to-body的对话框有正确的z-index */
:global(.tag-dialog) {
  z-index: 3500 !important;
}

:global(.tag-add-dialog) {
  z-index: 3500 !important;
}

:global(.tag-edit-dialog) {
  z-index: 3510 !important;
}

/* 标签列表头问号图标样式 */
:deep(.el-table-column .cell) {
  span {
    display: flex;
    align-items: center;
    gap: 6px;
    
    .el-icon {
      transition: color 0.2s ease;
      
      &:hover {
        color: #409EFF !important;
        transform: scale(1.1);
      }
    }
  }
}

/* 标签列表头的tooltip样式优化 */
:deep(.el-tooltip__popper.is-dark) {
  max-width: 300px;
  
  .el-tooltip__content {
    word-wrap: break-word;
    line-height: 1.4;
  }
}
</style>

<style lang="scss">
/* 全局样式确保所有下拉框在分类标签管理面板下方 */
.el-select-dropdown {
  z-index: 2300 !important;
}

.el-popper {
  z-index: 2300 !important;
}

.el-popper.is-pure {
  z-index: 2300 !important;
}

.el-popper[data-popper-placement] {
  z-index: 2300 !important;
}

/* 例外：标签操作下拉框需要显示在管理面板之上 */
.tag-operation-dropdown {
  z-index: 2700 !important;
}

.tag-operation-dropdown .el-dropdown-menu {
  z-index: 2700 !important;
}

.tag-operation-dropdown.el-popper {
  z-index: 2700 !important;
}

/* 确保自定义工作空间显示在下拉框上方 */
.custom-workspace {
  z-index: 2500 !important;
}

/* 确保消息确认框在最上层 */
.el-message-box {
  z-index: 9999 !important;
}

.el-message-box__wrapper {
  z-index: 9999 !important;
}

/* 特别针对标签删除确认框 */
.tag-delete-confirm-box {
  z-index: 10000 !important;
}

/* 修复标签删除确认框的遮罩层显示 */
body > .el-overlay {
  z-index: 9998 !important;
}
</style> 