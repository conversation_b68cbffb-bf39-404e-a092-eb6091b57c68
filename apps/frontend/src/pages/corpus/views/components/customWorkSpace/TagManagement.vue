<template>
  <div class="tag-management-config">
    <div class="settings-section">
      <div class="tag-list">
        <!-- 标签管理头部 -->
        <div class="tag-header">
          <div class="header-title">
            <span>标签列表</span>
          </div>
          <div class="header-actions">
            <el-button type="primary" @click="showAddDialog" class="add-button">
              <el-icon><Plus /></el-icon>
              新增标签
            </el-button>
          </div>
        </div>
        
        <el-skeleton :rows="3" animated v-if="loading" />
        <template v-else>
          <div v-if="tagList.length === 0" class="empty-list">
            <el-empty description="暂无标签，请添加" />
          </div>
          
          <el-table v-else :data="tagList" style="width: 100%" stripe :row-class-name="tableRowClassName">
            <el-table-column prop="tagName" label="标签名" min-width="150" />
            <el-table-column prop="tagDesc" label="描述" min-width="300" show-overflow-tooltip />
            <el-table-column prop="ctime" label="创建时间" min-width="180">
              <template #default="{ row }">
                {{ formatDate(row.ctime) }}
              </template>
            </el-table-column>
            <el-table-column prop="misId" label="操作人" min-width="150" />
            <el-table-column label="操作" width="80" align="center" fixed="right">
              <template #default="{ row, $index }">
                <!-- 如果是第一个标签（默认标签），不显示操作按钮 -->
                <template v-if="$index !== 0">
                  <el-dropdown trigger="click" popper-class="tag-operation-dropdown">
                    <el-button class="more-btn" text>
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleEdit(row)" :disabled="actionLoading">
                          <el-icon color="#409eff"><Edit /></el-icon>
                          <span class="dropdown-label">编辑</span>
                        </el-dropdown-item>
                        <el-dropdown-item @click="handleDelete(row)" :disabled="actionLoading">
                          <el-icon color="#f56c6c"><Delete /></el-icon>
                          <span class="dropdown-label">删除</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
    </div>

    <!-- 新增标签对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增标签"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="标签名" prop="tagName">
          <el-input v-model="formData.tagName" placeholder="请输入标签名" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="标签描述" prop="tagDesc">
          <el-input 
            v-model="formData.tagDesc" 
            type="textarea" 
            :rows="3"
            placeholder="请详细描述标签用途，AI将根据标签名称和描述内容自动分类语料" 
            maxlength="200" 
            show-word-limit 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false" :disabled="submitLoading">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑标签对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑标签"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editFormData"
        :rules="editRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="标签名">
          <el-input v-model="editFormData.tagName" disabled />
        </el-form-item>
        <el-form-item label="标签描述" prop="tagDesc">
          <el-input 
            v-model="editFormData.tagDesc" 
            type="textarea" 
            :rows="3"
            placeholder="请详细描述标签用途，AI将根据标签名称和描述内容自动分类语料" 
            maxlength="200" 
            show-word-limit 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false" :disabled="submitLoading">取消</el-button>
          <el-button type="primary" @click="submitEditForm" :loading="submitLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Plus, Delete, Edit, MoreFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import httpRequest from '@/utils/httpRequest';
import { API_PATHS } from '../../../request/api';

// 定义props来接收父组件传递的rgId和misId
const props = defineProps({
  rgId: {
    type: Number,
    required: true
  },
  misId: {
    type: String,
    required: true
  }
});

// 定义标签类型接口
interface Tag {
  id: number;
  tagName: string;
  tagDesc: string;
  rgId: number;
  misId: string;
  ctime: string;
  utime: string;
}

// 标签列表
const tagList = ref<Tag[]>([]);
const loading = ref(false);
const actionLoading = ref(false);

// 新增标签相关
const addDialogVisible = ref(false);
const submitLoading = ref(false);
const formRef = ref();
const formData = ref({
  tagName: '',
  tagDesc: '',
});

// 表单校验规则
const rules = {
  tagName: [
    { required: true, message: '请输入标签名', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在1到50个字符之间', trigger: 'blur' }
  ],
  tagDesc: [
    { required: true, message: '请输入标签描述', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在1到200个字符之间', trigger: 'blur' }
  ]
};

// 编辑标签相关
const editDialogVisible = ref(false);
const editFormRef = ref();
const editFormData = ref({
  id: 0,
  tagName: '',
  tagDesc: '',
});

// 编辑表单校验规则
const editRules = {
  tagDesc: [
    { required: true, message: '请输入标签描述', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在1到200个字符之间', trigger: 'blur' }
  ]
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (e) {
    return dateString;
  }
};

// 获取标签列表
const fetchTagList = async () => {
  loading.value = true;
  try {
    const res = await httpRequest.rawRequestGet(`${API_PATHS.RG_TAGS_LIST}?rgId=${props.rgId}`);
    
    if (res.code === 0) {
      tagList.value = res.data || [];
    } else {
      ElMessage.error(res.message || res.msg || '获取标签列表失败');
      tagList.value = [];
    }
  } catch (error) {
    console.error('获取标签列表失败:', error);
    ElMessage.error('获取标签列表失败，请稍后重试');
    tagList.value = [];
  } finally {
    loading.value = false;
  }
};

// 显示新增对话框
const showAddDialog = () => {
  formData.value = {
    tagName: '',
    tagDesc: '',
  };
  addDialogVisible.value = true;
};

// 提交新增表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    submitLoading.value = true;
    try {
      const params = {
        rgId: props.rgId,
        tagName: formData.value.tagName,
        tagDesc: formData.value.tagDesc,
        misId: props.misId
      };
      
      const res = await httpRequest.rawRequestPostAsJson(API_PATHS.RG_TAGS_ADD, params);
      
      if (res.code === 0) {
        ElMessage.success('新增标签成功');
        addDialogVisible.value = false;
        fetchTagList(); // 重新获取列表
      } else {
        ElMessage.error(res.message || res.msg || '新增标签失败');
      }
    } catch (error) {
      console.error('新增标签失败:', error);
      ElMessage.error('新增标签失败，请稍后重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 显示编辑对话框
const handleEdit = (row: Tag) => {
  editFormData.value = {
    id: row.id,
    tagName: row.tagName,
    tagDesc: row.tagDesc,
  };
  editDialogVisible.value = true;
};

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return;
  
  await editFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    submitLoading.value = true;
    try {
      const params = {
        id: editFormData.value.id,
        tagDesc: editFormData.value.tagDesc,
        misId: props.misId
      };
      
      const res = await httpRequest.rawRequestPostAsForm(API_PATHS.RG_TAGS_UPDATE, params);
      
      if (res.code === 0) {
        ElMessage.success('修改标签成功');
        editDialogVisible.value = false;
        fetchTagList(); // 重新获取列表
      } else {
        ElMessage.error(res.message || res.msg || '修改标签失败');
      }
    } catch (error) {
      console.error('修改标签失败:', error);
      ElMessage.error('修改标签失败，请稍后重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 删除标签
const handleDelete = (row: Tag) => {
  ElMessageBox.confirm(
    `确定要删除标签 "${row.tagName}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'tag-delete-confirm',
      appendToBody: true
    }
  )
    .then(async () => {
      actionLoading.value = true;
      try {
        const params = {
          id: row.id
        };
        
        const res = await httpRequest.rawRequestPostAsForm(API_PATHS.RG_TAGS_DELETE, params);
        
        if (res.code === 0) {
          ElMessage.success('删除标签成功');
          fetchTagList(); // 重新获取列表
        } else {
          ElMessage.error(res.message || res.msg || '删除标签失败');
        }
      } catch (error) {
        console.error('删除标签失败:', error);
        ElMessage.error('删除标签失败，请稍后重试');
      } finally {
        actionLoading.value = false;
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTagList();
});

// 暴露方法供父组件调用
defineExpose({
  fetchTagList
});

// 添加行样式类名函数
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  return rowIndex === 0 ? 'default-row' : '';
};
</script>

<style scoped lang="scss">
.tag-management-config {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  min-width: 0;
  min-height: 0;
  padding: 0;
  
  .settings-section {
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    
    .tag-list {
      background: #f9fafb;
      border-radius: 8px;
      padding: 24px 16px;
      
      .tag-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .header-title {
          span {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }
        
        .header-actions {
          .add-button {
            min-width: auto;
            width: auto;
            height: 36px;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            
            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
      
      .el-table {
        background: #fff;
        border-radius: 8px;
        font-size: 15px;
        
        :deep(.el-table__header) {
          font-weight: 600;
        }
        
        :deep(.el-table-column--default) {
          .cell {
            padding: 8px;
          }
        }
      }
      
      .empty-list {
        padding: 40px 0;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 24px;
  
  .el-button {
    min-width: 100px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
  }
  
  .el-button--primary {
    background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
    border: none;
    color: #fff;
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  
  :deep(.el-icon) {
    font-size: 20px;
    color: #606266;
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  
  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }
  
  .dropdown-label {
    font-size: 14px;
  }
  
  &.is-disabled {
    cursor: not-allowed;
    
    .el-icon, .dropdown-label {
      opacity: 0.5;
    }
  }
}

// 优化滚动条
.tag-management-config ::-webkit-scrollbar {
  width: 8px;
  background: #f1f1f1;
}

.tag-management-config ::-webkit-scrollbar-thumb {
  background: #d3dce6;
  border-radius: 4px;
}

// 确保图标正常显示
:deep(.el-icon) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  line-height: 1;
}

// 确保操作按钮正常显示
:deep(.el-button--text) {
  padding: 8px;
}

.tag-operation-dropdown {
  // Add any necessary styles for the dropdown
}
</style>

<style lang="scss">
/* 专门针对标签操作下拉框的样式 */
.tag-operation-dropdown {
  z-index: 2700 !important;
}

/* 标签删除确认对话框样式 */
.tag-delete-confirm {
  z-index: 3600 !important;
}
</style> 