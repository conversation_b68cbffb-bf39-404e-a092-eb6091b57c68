<script setup lang="ts">
// 在顶部添加全局变量声明
declare global {
  interface Window {
    __sopContent?: string;
  }
}

import { ref, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
// 导入MonacoEditor组件
import MonacoEditor from '../MonacoEditor.vue'
// 导入HTTP请求工具和API路径
import httpRequest from '../../../../../utils/httpRequest'
import { API_PATHS } from '../../../request/api'
// 导入图标
import { Check, Close } from '@element-plus/icons-vue'

// 定义emit
const emit = defineEmits(['close'])

// 定义props接收父组件传递的属性
const props = defineProps({
  rgId: {
    type: Number,
    required: true
  },
  misId: {
    type: String,
    required: true
  }
})

// 编辑器加载状态
const loading = ref(true)
// 编辑器内容 - 初始设为空字符串而非"加载中..."
const sopContent = ref('')
// 用于保存初始加载的SOP内容，用于重置功能
const initialSopContent = ref('')
// 编辑器引用
const editorRef = ref()
// 编辑器对话框可见性（始终为true，因为我们始终显示编辑器）
const editorVisible = ref(true)
// 提交状态
const submitting = ref(false)

// 定义响应类型接口
interface ApiResponse {
  code: number;
  message: string;
  data: any;
}

// 获取最新的SOP数据
async function fetchLatestSop() {
  loading.value = true
  sopContent.value = '加载中...' // 仅在加载时显示这个临时信息
  
  try {
    // 输出请求参数
    console.log('fetchLatestSop 请求参数:', {
      rgId: props.rgId,
      misId: props.misId
    });
    
    // 调用API获取最新SOP
    const response = await httpRequest.rawRequestGet(`${API_PATHS.QUERY_LATEST_SOP_BY_RG_ID}`, {
      rgId: props.rgId,
      misId: props.misId
    })
    
    // 将响应数据转换为接口类型
    const apiResponse = response as unknown as ApiResponse;
    
    // 输出响应结果
    console.log('fetchLatestSop 响应结果:', {
      code: apiResponse.code,
      message: apiResponse.message,
      dataType: typeof apiResponse.data,
      dataLength: apiResponse.data ? apiResponse.data.length : 0
    });
    
    if (apiResponse && apiResponse.code === 0) {
      const data = apiResponse.data;
      
      // 简化逻辑：优先直接使用字符串
      if (typeof data === 'string') {
        // 数据是字符串，直接使用
        sopContent.value = data;
        initialSopContent.value = data;
      } else if (data) {
        // 数据是对象或其他类型，尝试提取或转换为字符串
        if (typeof data === 'object' && (data.docs || data.question)) {
          // 包含常见字段的对象
          sopContent.value = String(data.docs || data.question);
          initialSopContent.value = sopContent.value;
        } else {
          // 其他情况，尝试转为字符串
          try {
            sopContent.value = JSON.stringify(data, null, 2);
            initialSopContent.value = sopContent.value;
          } catch (e) {
            sopContent.value = '无法处理的SOP数据格式';
            initialSopContent.value = sopContent.value;
          }
        }
      } else {
        // 数据为空
        sopContent.value = '';
        initialSopContent.value = '';
      }
    } else {
      // API请求失败处理
      ElMessage.error(apiResponse?.message || '获取SOP失败');
      sopContent.value = '暂无SOP数据';
      initialSopContent.value = '暂无SOP数据';
    }
  } catch (error) {
    const content = '网络请求失败，请稍后重试';
    sopContent.value = content;
    initialSopContent.value = content; // 保存初始内容用于重置
    ElMessage.error('获取最新SOP失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理编辑器对话框打开
// 这个函数模拟KnowledgeProcess中的handleEditDialogOpened函数
const handleEditorInitialized = () => {
  setTimeout(() => {
    // 初始化编辑器布局
    const sopEditor = document.querySelector('.sop-editor-container');
    if (sopEditor && (sopEditor as any).__vue__) {
      const editorComponent = (sopEditor as any).__vue__;
      const editor = editorComponent.getEditor?.();
      if (editor && typeof editor.layout === 'function') {
        editor.layout();
      }
    }
    
    // 备用方法：直接通过DOM查找monaco-editor元素并触发布局更新
    const monacoEditors = document.querySelectorAll('.monaco-editor');
    if (monacoEditors.length > 0) {
      // 强制调整宽度样式
      monacoEditors.forEach((editor: any) => {
        editor.style.width = '100%';
        // 查找overflow-guard元素并调整宽度
        const overflowGuards = editor.querySelectorAll('.overflow-guard');
        if (overflowGuards.length > 0) {
          overflowGuards.forEach((guard: any) => {
            guard.style.width = '100%';
          });
        }
      });
    }
  }, 300); // 延迟300ms确保DOM已完全渲染
}

// 手动触发编辑器预览/编辑模式切换
function toggleEditorPreview() {
  if (!editorRef.value || !editorRef.value.togglePreview) return;
  
  // 记录当前模式
  const wasInPreviewMode = editorRef.value.isPreviewMode;
  
  // 执行切换
  editorRef.value.togglePreview();
  
  // 如果从预览模式切换到编辑模式，需要重新调整布局
  if (wasInPreviewMode) {
    handleEditorInitialized();
  }
}

// 获取编辑器当前是否处于预览模式
function isInPreviewMode() {
  return editorRef.value?.isPreviewMode || false;
}

// 组件挂载时获取SOP数据
onMounted(async () => {
  // 输出rgId和misId，检查是否正确传入
  console.log('onMounted 组件参数:', {
    rgId: props.rgId,
    misId: props.misId,
    rgIdType: typeof props.rgId,
    misIdType: typeof props.misId
  });
  
  // 加载数据
  await fetchLatestSop();
  
  // 在下一个DOM更新周期后初始化编辑器
  nextTick(() => {
    handleEditorInitialized();
  });
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleEditorInitialized);
  
  // 组件卸载时移除监听
  return () => {
    window.removeEventListener('resize', handleEditorInitialized);
  };
});

// 向父组件暴露方法
defineExpose({
  toggleEditorPreview,
  isInPreviewMode,
  fetchLatestSop
});

// 处理更新SOP内容
const handleUpdate = async () => {
  if (submitting.value) return;
  
  // 检查内容是否为空
  if (!sopContent.value || sopContent.value.trim() === '') {
    ElMessage.warning('SOP内容不能为空');
    return;
  }
  
  try {
    submitting.value = true;
    
    // 构造请求体
    const requestBody = {
      sop: sopContent.value
    };
    
    // 输出更新请求参数
    console.log('handleUpdate 更新请求参数:', {
      rgId: props.rgId,
      misId: props.misId,
      requestBodyLength: requestBody.sop.length,
      requestBodyPreview: requestBody.sop.substring(0, 100) + '...'
    });
    
    // 将rgId和misId直接拼接到URL后面
    const url = `${API_PATHS.UPDATE_SOP}?rgId=${props.rgId}&misId=${props.misId}`;
    
    // 发送更新请求
    const response = await httpRequest.rawRequestPostAsJson(url, requestBody);
    
    // 将响应数据转换为接口类型
    const apiResponse = response as unknown as ApiResponse;
    
    if (apiResponse && apiResponse.code === 0) {
      ElMessage.success('SOP更新成功');
      // 更新初始内容，以便重置功能使用最新内容
      initialSopContent.value = sopContent.value;
      
      // 关闭组件
      emit('close');
    } else {
      ElMessage.error(apiResponse?.message || '更新SOP失败');
    }
  } catch (error) {
    console.error('更新SOP出错:', error);
    ElMessage.error('网络异常，请稍后重试');
  } finally {
    submitting.value = false;
  }
};

// 处理关闭窗口
const handleCancel = () => {
  // 触发关闭事件
  emit('close')
}
</script>

<template>
  <div class="sop-config">
    <div class="editor-container" v-loading="loading">
      
      <div class="editor-content">
        <MonacoEditor
          ref="editorRef"
          v-model="sopContent"
          language="markdown"
          height="400px"
          class="sop-editor-container"
          :options="{
            wordWrap: 'on',
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            lineNumbers: 'on',
            lineDecorationsWidth: 0,
            folding: true,
            renderLineHighlight: 'all',
            automaticLayout: true
          }"
          :show-reset-button="true"
          title="SOP模板内容"
          :initial-content="initialSopContent"
        />
        
        <!-- 按钮区域 -->
        <div class="editor-actions">
          <el-button 
            type="primary" 
            class="update-btn" 
            @click="handleUpdate"
            :loading="submitting"
            :disabled="submitting"
          >
            {{ submitting ? '更新中...' : '更新' }}
          </el-button>
          <el-button class="cancel-btn" @click="handleCancel" :disabled="submitting">
            取消
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.sop-config {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  padding: 0;
  box-sizing: border-box;
  
  .editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    position: relative;
    
    // 添加优雅的边框效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 12px;
      padding: 1px;
      background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(242, 242, 242, 0.8));
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      pointer-events: none;
    }
    
    .editor-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: transparent;
      position: relative;
      width: 100% !important;
      min-width: 0 !important;
      max-width: 100% !important;
      padding: 0 !important;
      overflow: hidden;
      
      /* 按钮区域样式 */
      .editor-actions {
        padding: 16px 24px;
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        border-top: 1px solid rgba(0, 0, 0, 0.06);
        background-color: transparent !important;
        margin-top: 10px;
        border-bottom-left-radius: 12px;
        border-bottom-right-radius: 12px;
        position: relative;
        z-index: 2;
        margin: 0 16px 8px 16px;
        
        // 去除任何可能的背景色
        &::before,
        &::after {
          display: none;
        }
        
        .el-button {
          min-width: 100px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s;
          
          &.update-btn {
            background-color: #1890ff;
            border-color: #1890ff;
            
            &:hover {
              background-color: #40a9ff;
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
              transform: translateY(-1px);
            }
          }
          
          &.cancel-btn {
            &:hover {
              background-color: #f7f7f7;
              border-color: #d9d9d9;
              transform: translateY(-1px);
            }
          }
          
          .el-icon {
            margin-right: 6px;
          }
        }
      }
    }
  }
}

/* 编辑器样式 */
.sop-editor-container {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  flex: 1;
  border: none;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  background-color: white;
  box-sizing: border-box;
  margin: 24px 16px 0 16px;
  
  :deep(.monaco-editor-wrapper) {
    border: none;
    height: 100% !important;
    min-height: 300px;
  }
  
  :deep(.monaco-editor) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    height: 100% !important;
    min-height: 300px;
    display: block;
    background-color: white;
    box-sizing: border-box;
  }
  
  :deep(.monaco-editor .overflow-guard) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    height: 100% !important;
    min-height: 300px;
    box-sizing: border-box;
  }
  
  // 确保编辑器下方没有白色区域
  :deep(.monaco-editor-background),
  :deep(.monaco-editor .margin),
  :deep(.monaco-workbench) {
    background-color: white !important;
  }
}

// 覆盖Element Plus可能的默认样式
:deep(.el-dialog__body),
:deep(.el-card__body) {
  background-color: transparent !important;
}
</style> 