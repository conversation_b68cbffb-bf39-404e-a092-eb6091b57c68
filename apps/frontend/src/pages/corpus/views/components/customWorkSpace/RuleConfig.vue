<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import MonacoEditor from '../MonacoEditor.vue'
import httpRequest from '../../../../../utils/httpRequest'

const props = defineProps({
  rgId: {
    type: Number,
    required: true
  },
  misId: {
    type: String,
    required: true
  }
})

const loading = ref(true)
const ruleContent = ref('')
const editorRef = ref()
const submitting = ref(false)
const emit = defineEmits(['close'])
const originalContent = ref('')

interface ApiResponse {
  code: number;
  msg: string;
  data: any;
  success: boolean;
}

async function fetchLatestRule() {
  loading.value = true
  ruleContent.value = '加载中...'
  try {
    const url = `/corpus/queryLatestRuleByRgId?rgId=${encodeURIComponent(props.rgId)}&misId=${encodeURIComponent(props.misId)}`
    const response = await httpRequest.rawRequestGet(url)
    const apiResponse = response as unknown as ApiResponse
    if (apiResponse && apiResponse.code === 0) {
      ruleContent.value = apiResponse.data || ''
      originalContent.value = apiResponse.data || ''
    } else {
      ElMessage.error(apiResponse?.msg || '获取自定义规则失败')
      ruleContent.value = '暂无自定义规则数据'
      originalContent.value = ''
    }
  } catch (error) {
    ruleContent.value = '网络请求失败，请稍后重试'
    originalContent.value = ''
    ElMessage.error('获取自定义规则失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 更新自定义规则
const handleUpdate = async () => {
  if (submitting.value) return
  try {
    submitting.value = true
    const response = await httpRequest.rawRequestPostAsJson('/corpus/updateRule', {
      rgId: props.rgId,
      misId: props.misId,
      rule: ruleContent.value
    })
    const apiResponse = response as unknown as ApiResponse
    if (apiResponse && apiResponse.code === 0) {
      ElMessage.success('自定义规则更新成功')
    } else {
      ElMessage.error(apiResponse?.msg || '更新自定义规则失败')
    }
  } catch (error) {
    ElMessage.error('网络异常，请稍后重试')
  } finally {
    submitting.value = false
  }
}

const handleReset = () => {
  ruleContent.value = originalContent.value
}

const handleCancel = () => {
  emit('close')
}

onMounted(async () => {
  await fetchLatestRule()
  nextTick(() => {
    const editor = document.querySelector('.rule-editor-container')
    if (editor) {
      editor.dispatchEvent(new Event('resize'))
    }
  })
})

defineExpose({
  fetchLatestRule
})
</script>

<template>
  <div class="background-knowledge-config">
    <div class="editor-container" v-loading="loading">
      <div class="editor-content">
        <MonacoEditor
          ref="editorRef"
          v-model="ruleContent"
          language="markdown"
          height="400px"
          class="background-knowledge-editor-container"
          :options="{
            wordWrap: 'on',
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            lineNumbers: 'on',
            lineDecorationsWidth: 0,
            folding: true,
            renderLineHighlight: 'all',
            automaticLayout: true
          }"
          title="自定义规则"
          :initialContent="originalContent"
        />
        <div class="editor-actions">
          <el-button 
            type="primary" 
            class="update-btn" 
            @click="handleUpdate"
            :loading="submitting"
            :disabled="submitting"
          >
            {{ submitting ? '更新中...' : '更新' }}
          </el-button>
          <el-button class="cancel-btn" @click="handleCancel" :disabled="submitting">
            取消
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.background-knowledge-config {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  padding: 0;
  box-sizing: border-box;
  .editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    position: relative;
    .editor-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: transparent;
      position: relative;
      width: 100% !important;
      min-width: 0 !important;
      max-width: 100% !important;
      padding: 0 !important;
      overflow: hidden;
    }
  }
}
.background-knowledge-editor-container {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  flex: 1;
  border: none;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  background-color: white;
  box-sizing: border-box;
  margin: 24px 16px 0 16px;
  :deep(.monaco-editor-wrapper) {
    border: none;
    height: 100% !important;
    min-height: 300px;
  }
  :deep(.monaco-editor) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    height: 100% !important;
    min-height: 300px;
    display: block;
    background-color: white;
    box-sizing: border-box;
  }
  :deep(.monaco-editor .overflow-guard) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    height: 100% !important;
    min-height: 300px;
    box-sizing: border-box;
  }
  :deep(.monaco-editor-background),
  :deep(.monaco-editor .margin),
  :deep(.monaco-workbench) {
    background-color: white !important;
  }
}
.editor-actions {
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  background-color: transparent !important;
  margin-top: 10px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  position: relative;
  z-index: 2;
  margin: 0 16px 8px 16px;
  .el-button {
    min-width: 100px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;
    &.update-btn {
      background-color: #1890ff;
      border-color: #1890ff;
      &:hover {
        background-color: #40a9ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
        transform: translateY(-1px);
      }
    }
    &.reset-btn {
      &:hover {
        background-color: #f7f7f7;
        border-color: #d9d9d9;
        transform: translateY(-1px);
      }
    }
    &.cancel-btn {
      &:hover {
        background-color: #f7f7f7;
        border-color: #d9d9d9;
        transform: translateY(-1px);
      }
    }
    .el-icon {
      margin-right: 6px;
    }
  }
}
</style> 