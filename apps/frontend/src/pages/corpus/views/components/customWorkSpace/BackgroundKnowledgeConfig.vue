<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import MonacoEditor from '../MonacoEditor.vue'
import httpRequest from '../../../../../utils/httpRequest'
import { API_PATHS } from '../../../request/api'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  rgId: {
    type: Number,
    required: true
  },
  misId: {
    type: String,
    required: true
  }
})

const loading = ref(true)
const knowledgeContent = ref('')
const editorRef = ref()
const submitting = ref(false)
const emit = defineEmits(['close'])
const originalContent = ref('')

interface ApiResponse {
  code: number;
  msg: string;
  data: any;
  success: boolean;
}

async function fetchLatestBackgroundKnowledge() {
  loading.value = true
  knowledgeContent.value = '加载中...'
  try {
    const url = `${API_PATHS.QUERY_LATEST_BACKGROUND_KNOWLEDGE_BY_RG_ID}?rgId=${encodeURIComponent(props.rgId)}&misId=${encodeURIComponent(props.misId)}`
    const response = await httpRequest.rawRequestGet(url)
    const apiResponse = response as unknown as ApiResponse
    if (apiResponse && apiResponse.code === 0) {
      knowledgeContent.value = apiResponse.data || ''
      originalContent.value = apiResponse.data || ''
    } else {
      ElMessage.error(apiResponse?.msg || '获取自定义背景知识失败')
      knowledgeContent.value = '暂无自定义背景知识数据'
      originalContent.value = ''
    }
  } catch (error) {
    knowledgeContent.value = '网络请求失败，请稍后重试'
    originalContent.value = ''
    ElMessage.error('获取自定义背景知识失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 更新自定义背景知识
const handleUpdate = async () => {
  if (submitting.value) return
  // 允许内容为空，不做校验
  try {
    submitting.value = true
    const response = await httpRequest.rawRequestPostAsJson(API_PATHS.UPDATE_BACKGROUND_KNOWLEDGE, {
      rgId: props.rgId,
      misId: props.misId,
      knowledgeContent: knowledgeContent.value
    })
    const apiResponse = response as unknown as ApiResponse
    if (apiResponse && apiResponse.code === 0) {
      ElMessage.success('自定义背景知识更新成功')
    } else {
      ElMessage.error(apiResponse?.msg || '更新自定义背景知识失败')
    }
  } catch (error) {
    ElMessage.error('网络异常，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('close')
}

const handleReset = () => {
  knowledgeContent.value = originalContent.value
}

onMounted(async () => {
  await fetchLatestBackgroundKnowledge()
  nextTick(() => {
    // 触发编辑器布局刷新
    const editor = document.querySelector('.background-knowledge-editor-container')
    if (editor) {
      editor.dispatchEvent(new Event('resize'))
    }
  })
})

defineExpose({
  fetchLatestBackgroundKnowledge
})
</script>

<template>
  <div class="background-knowledge-config">
    <div class="editor-container" v-loading="loading">
      <div class="editor-content">
        <MonacoEditor
          ref="editorRef"
          v-model="knowledgeContent"
          language="markdown"
          height="400px"
          class="background-knowledge-editor-container"
          :options="{
            wordWrap: 'on',
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            lineNumbers: 'on',
            lineDecorationsWidth: 0,
            folding: true,
            renderLineHighlight: 'all',
            automaticLayout: true
          }"
          title="自定义背景知识"
          :initialContent="originalContent"
        />
        <!-- 按钮区域 -->
        <div class="editor-actions">
          <el-button 
            type="primary" 
            class="update-btn" 
            @click="handleUpdate"
            :loading="submitting"
            :disabled="submitting"
          >
            {{ submitting ? '更新中...' : '更新' }}
          </el-button>
          <el-button class="cancel-btn" @click="handleCancel" :disabled="submitting">
            取消
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.background-knowledge-config {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  padding: 0;
  box-sizing: border-box;
  .editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    position: relative;
    .editor-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: transparent;
      position: relative;
      width: 100% !important;
      min-width: 0 !important;
      max-width: 100% !important;
      padding: 0 !important;
      overflow: hidden;
    }
  }
}
.background-knowledge-editor-container {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  flex: 1;
  border: none;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  background-color: white;
  box-sizing: border-box;
  margin: 24px 16px 0 16px;
  :deep(.monaco-editor-wrapper) {
    border: none;
    height: 100% !important;
    min-height: 300px;
  }
  :deep(.monaco-editor) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    height: 100% !important;
    min-height: 300px;
    display: block;
    background-color: white;
    box-sizing: border-box;
  }
  :deep(.monaco-editor .overflow-guard) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    height: 100% !important;
    min-height: 300px;
    box-sizing: border-box;
  }
  :deep(.monaco-editor-background),
  :deep(.monaco-editor .margin),
  :deep(.monaco-workbench) {
    background-color: white !important;
  }
}
.editor-actions {
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  background-color: transparent !important;
  margin-top: 10px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  position: relative;
  z-index: 2;
  margin: 0 16px 8px 16px;
  .el-button {
    min-width: 100px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;
    &.update-btn {
      background-color: #1890ff;
      border-color: #1890ff;
      &:hover {
        background-color: #40a9ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
        transform: translateY(-1px);
      }
    }
    &.cancel-btn {
      &:hover {
        background-color: #f7f7f7;
        border-color: #d9d9d9;
        transform: translateY(-1px);
      }
    }
    .el-icon {
      margin-right: 6px;
    }
  }
}
</style> 