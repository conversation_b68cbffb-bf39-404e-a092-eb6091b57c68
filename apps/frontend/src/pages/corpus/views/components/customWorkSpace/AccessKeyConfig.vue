<template>
  <div class="access-key-config">
    <div class="settings-section">
      <div class="access-key-list">
        <!-- 新增按钮区域，始终显示 -->
        <div class="access-key-header">
          <div class="header-title">
            <span>AccessKey列表</span>
          </div>
          <div class="header-actions">
            <el-button type="primary" @click="showAddDialog" class="add-button">
              <el-icon><Plus /></el-icon>
              新增AccessKey
            </el-button>
          </div>
        </div>
        
        <el-skeleton :rows="3" animated v-if="loading" />
        <template v-else>
          <div v-if="accessKeys.length === 0" class="empty-list">
            <el-empty description="暂无AccessKey，请添加" />
          </div>
          
          <el-table v-else :data="accessKeys" border stripe style="width: 100%">
            <el-table-column label="AccessKey" prop="ak" min-width="380">
              <template #default="scope">
                <span class="access-key-display">
                  <span class="visible-part">{{ getVisiblePart(scope.row.ak) }}</span><span class="hidden-part">{{ getHiddenPart(scope.row.ak) }}</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column label="名称" prop="akName" min-width="150" />
            <el-table-column label="使用次数" prop="count" width="120" align="center">
              <template #default="scope">
                {{ scope.row.count || 0 }}
              </template>
            </el-table-column>
            <el-table-column label="创建时间" min-width="180">
              <template #default="scope">
                <template v-if="!scope.row.defaultKey">
                  {{ formatDate(scope.row.ctime) }}
                </template>
                <span v-else class="default-key-hint">--</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center" fixed="right">
              <template #default="scope">
                <el-dropdown trigger="click">
                  <el-button class="more-btn" text>
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleCopyLink(scope.row)" :disabled="actionLoading">
                        <el-icon color="#67c23a"><CopyDocument /></el-icon>
                        <span class="dropdown-label">复制API链接</span>
                      </el-dropdown-item>
                      <template v-if="!scope.row.defaultKey">
                        <el-dropdown-item @click="handleEdit(scope.row)" :disabled="actionLoading">
                          <el-icon color="#409eff"><Edit /></el-icon>
                          <span class="dropdown-label">编辑</span>
                        </el-dropdown-item>
                        <el-dropdown-item @click="handleDelete(scope.row)" :disabled="actionLoading">
                          <el-icon color="#f56c6c"><Delete /></el-icon>
                          <span class="dropdown-label">删除</span>
                        </el-dropdown-item>
                      </template>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
    </div>

    <!-- 新增AccessKey对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增AccessKey"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="名称" prop="akName">
          <el-input v-model="formData.akName" placeholder="请输入AccessKey名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false" :disabled="submitLoading">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改AccessKey名称对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="修改AccessKey名称"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editFormData"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="名称" prop="akName">
          <el-input v-model="editFormData.akName" placeholder="请输入AccessKey名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false" :disabled="submitLoading">取消</el-button>
          <el-button type="primary" @click="submitEditForm" :loading="submitLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Plus, Delete, Edit, MoreFilled, CopyDocument, QuestionFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import httpRequest from '@/utils/httpRequest';
import { API_PATHS } from '../../../request/api';
import { isDev, isTest } from '@/shared/utils/env-helper';

// 定义props来接收父组件传递的rgId和misId
const props = defineProps({
  rgId: {
    type: Number,
    required: true
  },
  misId: {
    type: String,
    required: true
  }
});

// 定义AccessKey类型接口
interface AccessKey {
  id: number;
  ak: string;
  akName: string;
  misId: string;
  rgIds: string;
  defaultKey: boolean;
  count: number;
  ctime: string;
  utime: string;
}

// AccessKey列表
const accessKeys = ref<AccessKey[]>([]);
const loading = ref(false);
const actionLoading = ref(false);

// 新增AccessKey相关
const addDialogVisible = ref(false);
const submitLoading = ref(false);
const formRef = ref();
const formData = ref({
  akName: '',
});

// 表单校验规则
const rules = {
  akName: [
    { required: true, message: '请输入AccessKey名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在1到50个字符之间', trigger: 'blur' }
  ]
};

// 修改AccessKey名称相关
const editDialogVisible = ref(false);
const editFormRef = ref();
const editFormData = ref({
  akName: '',
  ak: '',
  misId: '',
});

// 获取可见部分
const getVisiblePart = (ak: string) => {
  if (!ak) return '';
  const visibleLength = 8;
  return ak.substring(0, Math.min(visibleLength, ak.length));
};

// 获取隐藏部分
const getHiddenPart = (ak: string) => {
  if (!ak) return '';
  const visibleLength = 8;
  if (ak.length <= visibleLength) {
    return '';
  }
  return '********************';
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (e) {
    return dateString;
  }
};

// 获取AccessKey列表
const fetchAccessKeys = async () => {
  loading.value = true;
  try {
    const res = await httpRequest.rawRequestGet(`${API_PATHS.ACCESS_KEY_LIST}?rgId=${props.rgId}&misId=${props.misId}`);
    if (res.code === 0 && res.data) {
      accessKeys.value = res.data;
    } else {
      ElMessage.error(res.msg || '获取AccessKey列表失败');
      accessKeys.value = [];
    }
  } catch (error) {
    console.error('获取AccessKey列表失败:', error);
    ElMessage.error('获取AccessKey列表失败，请稍后重试');
    accessKeys.value = [];
  } finally {
    loading.value = false;
  }
};

// 显示新增对话框
const showAddDialog = () => {
  formData.value = {
    akName: '',
  };
  addDialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    submitLoading.value = true;
    try {
      // 使用正确的接口和参数
      const params = {
        misId: props.misId,
        akName: formData.value.akName,
        rgIds: props.rgId.toString()
      };
      
      // 使用 API_PATHS 中定义的常量和 rawRequestPostAsForm 方法
      const res = await httpRequest.rawRequestPostAsForm(API_PATHS.ACCESS_KEY_GENERATE, params);
      
      if (res.code === 0) {
        ElMessage.success('新增AccessKey成功');
        addDialogVisible.value = false;
        fetchAccessKeys(); // 重新获取列表
      } else {
        ElMessage.error(res.msg || '新增AccessKey失败');
      }
    } catch (error) {
      console.error('新增AccessKey失败:', error);
      ElMessage.error('新增AccessKey失败，请稍后重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 删除AccessKey
const handleDelete = (row: AccessKey) => {
  if (row.defaultKey) {
    ElMessage.warning('默认AccessKey不可删除');
    return;
  }
  
  ElMessageBox.confirm(
    `确定要删除AccessKey "${row.akName || row.ak}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      actionLoading.value = true;
      try {
        // 使用 API_PATHS 中定义的常量和新的请求参数格式
        const params = {
          ak: row.ak,
          misId: props.misId
        };
        
        const res = await httpRequest.rawRequestPostAsForm(API_PATHS.ACCESS_KEY_DELETE, params);
        
        if (res.code === 0 && res.data && res.data.success) {
          ElMessage.success(res.data.message || '删除AccessKey成功');
          fetchAccessKeys(); // 重新获取列表
        } else {
          ElMessage.error(res.data?.message || res.msg || '删除AccessKey失败');
        }
      } catch (error) {
        console.error('删除AccessKey失败:', error);
        ElMessage.error('删除AccessKey失败，请稍后重试');
      } finally {
        actionLoading.value = false;
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 显示编辑对话框
const handleEdit = (row: AccessKey) => {
  editFormData.value = {
    akName: row.akName,
    ak: row.ak,
    misId: props.misId,
  };
  editDialogVisible.value = true;
};

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return;
  
  await editFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    submitLoading.value = true;
    try {
      // 使用 API_PATHS 中定义的常量和 rawRequestPostAsForm 方法
      const params = {
        ak: editFormData.value.ak,
        misId: editFormData.value.misId,
        newAkName: editFormData.value.akName
      };
      
      const res = await httpRequest.rawRequestPostAsForm(API_PATHS.ACCESS_KEY_UPDATE_NAME, params);
      
      if (res.code === 0 && res.data && res.data.success) {
        ElMessage.success(res.data.message || '修改AccessKey名称成功');
        editDialogVisible.value = false;
        fetchAccessKeys(); // 重新获取列表
      } else {
        ElMessage.error(res.data?.message || res.msg || '修改AccessKey名称失败');
      }
    } catch (error) {
      console.error('修改AccessKey名称失败:', error);
      ElMessage.error('修改AccessKey名称失败，请稍后重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 复制API链接
const handleCopyLink = async (row: AccessKey) => {
  try {
    // 根据环境判断使用不同的API前缀
    const baseUrl = isDev() || isTest() 
      ? 'https://deliveryopenapi.test.meituan.com' 
      : 'https://deliveryopenapi.meituan.com';
    
    const apiUrl = `${baseUrl}/llm/corpus/queryLatestContentByRgId?rgId=${props.rgId}&ak=${row.ak}`;
    
    // 使用现代浏览器的剪贴板API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(apiUrl);
      ElMessage.success('API链接已复制到剪贴板');
    } else {
      // 兼容旧浏览器的复制方法
      const textArea = document.createElement('textarea');
      textArea.value = apiUrl;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      try {
        document.execCommand('copy');
        ElMessage.success('API链接已复制到剪贴板');
      } catch (err) {
        console.error('复制失败:', err);
        ElMessage.error('复制失败，请手动复制链接');
        // 可以考虑显示链接让用户手动复制
      } finally {
        document.body.removeChild(textArea);
      }
    }
  } catch (error) {
    console.error('复制链接失败:', error);
    ElMessage.error('复制失败，请稍后重试');
  }
};

// 初始化
onMounted(() => {
  fetchAccessKeys();
});

// 暴露方法给父组件
defineExpose({
  fetchAccessKeys
});
</script>

<style scoped lang="scss">
.access-key-config {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  min-width: 0;
  min-height: 0;
  padding: 0;
  
  .settings-section {
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    
    .access-key-list {
      background: #f9fafb;
      border-radius: 8px;
      padding: 24px 16px;
      
      .access-key-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .header-title {
          span {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }
        
        .header-actions {
          .add-button {
            min-width: auto;
            width: auto;
            height: 36px;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            
            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
      
      .el-table {
        background: #fff;
        border-radius: 8px;
        font-size: 15px;
        
        :deep(.el-table__header) {
          font-weight: 600;
        }
        
        :deep(.el-table-column--default) {
          .cell {
            padding: 8px;
          }
        }
      }
      
      .empty-list {
        padding: 40px 0;
      }
    }
  }
}

.header-center {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  
  :deep(.el-icon) {
    font-size: 20px;
    color: #606266;
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  
  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }
  
  .dropdown-label {
    font-size: 14px;
  }
  
  &.is-disabled {
    cursor: not-allowed;
    
    .el-icon, .dropdown-label {
      opacity: 0.5;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 24px;
  
  .el-button {
    min-width: 100px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
  }
  
  .el-button--primary {
    background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
    border: none;
    color: #fff;
  }
}

// 优化滚动条
.access-key-config ::-webkit-scrollbar {
  width: 8px;
  background: #f1f1f1;
}

.access-key-config ::-webkit-scrollbar-thumb {
  background: #d3dce6;
  border-radius: 4px;
}

// 确保图标正常显示
:deep(.el-icon) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  line-height: 1;
}

// 确保操作按钮正常显示
:deep(.el-button--text) {
  padding: 8px;
}

.default-key-hint {
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

.access-key-display {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: #606266;
  
  .visible-part {
    letter-spacing: normal;
  }
  
  .hidden-part {
    letter-spacing: 1px;
  }
}
</style> 