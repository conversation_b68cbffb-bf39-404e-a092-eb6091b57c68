<template>
  <div class="workspace-settings">
    <div class="settings-section">
      <div class="workspace-list">
        <!-- 新增按钮区域，始终显示 -->
        <div class="workspace-header">
          <div class="header-title">
            <span>工作空间列表</span>
          </div>
          <div class="header-actions">
            <el-button type="primary" @click="showAddDialog" class="add-button">
              <el-icon><Plus /></el-icon>
              新增工作空间
            </el-button>
          </div>
        </div>
        
        <el-skeleton :rows="3" animated v-if="loading" />
        <template v-else>
          <div v-if="workspaces.length === 0" class="empty-list">
            <el-empty description="暂无工作空间，请点击「新增工作空间」按钮添加" />
          </div>
          
          <el-table v-else :data="workspaces" border stripe style="width: 100%">
            <el-table-column label="工作空间ID" prop="spaceId" min-width="180" />
            <el-table-column label="工作空间名称" prop="spaceName" min-width="200" />
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-tooltip
                  :content="isWorkspaceLocked(scope.row.spaceId) ? '系统保留的工作空间，不可删除' : '删除工作空间'"
                  placement="top"
                >
                  <span class="delete-button-wrapper">
                    <template v-if="isWorkspaceLocked(scope.row.spaceId)">
                      <el-button 
                        link 
                        type="info" 
                        class="locked-button"
                        disabled
                      >
                        <el-icon><Lock /></el-icon>
                      </el-button>
                    </template>
                    <template v-else>
                      <el-button 
                        link 
                        type="danger" 
                        @click="handleDelete(scope.row)"
                        :disabled="deleteLoading"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </template>
                  </span>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
    </div>

    <!-- 新增工作空间对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增工作空间"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        label-position="left"
        :hide-required-asterisk="true"
      >
        <el-form-item label="工作空间ID" prop="spaceId">
          <template #label>
            <div class="form-label">
              <el-tooltip content="在Friday平台获取，例如：https://friday.sankuai.com/app/knowledge/detail/411370013433857/6180132/fileList 其中6180132为工作空间ID" placement="top">
                <el-icon class="help-icon" @click="handleHelpClick('spaceId')"><QuestionFilled /></el-icon>
              </el-tooltip>
              <span>工作空间ID</span>
            </div>
          </template>
          <el-input v-model="formData.spaceId" placeholder="请输入工作空间ID" />
        </el-form-item>
        
        <el-form-item label="工作空间名称" prop="spaceName">
          <template #label>
            <div class="form-label">
              <el-tooltip content="自定义工作空间名称" placement="top">
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
              <span>工作空间名称</span>
            </div>
          </template>
          <el-input v-model="formData.spaceName" placeholder="请输入工作空间名称" />
        </el-form-item>
        
        <el-form-item label="Access Key" prop="accessKey">
          <template #label>
            <div class="form-label">
              <el-tooltip content="在租户列表中查看" placement="top">
                <el-icon class="help-icon" @click="handleHelpClick('accessKey')"><QuestionFilled /></el-icon>
              </el-tooltip>
              <span>AppKey</span>
            </div>
          </template>
          <el-input v-model="formData.accessKey" placeholder="请输入AppKey" />
        </el-form-item>
        
        <el-form-item label="App Secret" prop="appSecret">
          <template #label>
            <div class="form-label">
              <el-tooltip content="在租户列表中查看" placement="top">
                <el-icon class="help-icon" @click="handleHelpClick('appSecret')"><QuestionFilled /></el-icon>
              </el-tooltip>
              <span>SecretKey</span>
            </div>
          </template>
          <el-input v-model="formData.appSecret" placeholder="请输入SecretKey" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false" :disabled="submitLoading">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Plus, Delete, Lock, QuestionFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import httpRequest from '@/shared/utils/httpRequest';
import { API_PATHS } from '../../../../request/api';
import { isDev, isTest } from '@/shared/utils/env-helper';

const props = defineProps({
  rgId: {
    type: Number,
    required: true
  },
  misId: {
    type: String,
    required: true
  }
});

// 工作空间列表
const workspaces = ref<{ spaceId: string; spaceName: string }[]>([]);
const loading = ref(false);
const deleteLoading = ref(false);

// 新增工作空间相关
const addDialogVisible = ref(false);
const submitLoading = ref(false);
const formRef = ref();
const formData = ref({
  spaceId: '',
  spaceName: '',
  accessKey: '',
  appSecret: ''
});

// 表单校验规则
const rules = {
  spaceId: [
    { required: true, message: '请输入工作空间ID', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在1到50个字符之间', trigger: 'blur' }
  ],
  spaceName: [
    { required: true, message: '请输入工作空间名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在1到50个字符之间', trigger: 'blur' }
  ],
  accessKey: [
    { required: true, message: '请输入Access Key', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在1到100个字符之间', trigger: 'blur' }
  ],
  appSecret: [
    { required: true, message: '请输入App Secret', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在1到100个字符之间', trigger: 'blur' }
  ]
};

// 判断工作空间是否禁止删除
const isWorkspaceLocked = (spaceId: string): boolean => {
  // 测试或开发环境下，禁止删除ID为6180112的工作空间
  if ((isDev() || isTest()) && spaceId === '6180112') {
    return true;
  }
  // 其他环境下，禁止删除ID为1304772的工作空间
  if (!(isDev() || isTest()) && spaceId === '1304772') {
    return true;
  }
  return false;
};

// 获取工作空间列表
const fetchWorkspaces = async () => {
  loading.value = true;
  try {
    const res = await httpRequest.rawRequestGet(`/api/llm/corpus/api/workspace/config?rgId=${props.rgId}`);
    if (res.code === 0 && res.data) {
      workspaces.value = res.data;
    } else {
      // 如果code为10039（未找到对应的工作空间配置），不显示错误提示
      if (res.code !== 10039) {
        const errorMsg = res.msg || '获取工作空间列表失败';
        ElMessage.error(errorMsg);
      }
      // 无论如何，将工作空间列表设置为空数组
      workspaces.value = [];
    }
  } catch (error) {
    console.error('获取工作空间列表失败:', error);
    ElMessage.error('获取工作空间列表失败，请稍后重试');
    workspaces.value = [];
  } finally {
    loading.value = false;
  }
};

// 显示新增对话框
const showAddDialog = () => {
  formData.value = {
    spaceId: '',
    spaceName: '',
    accessKey: '',
    appSecret: ''
  };
  addDialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    submitLoading.value = true;
    try {
      const params = {
        rgId: props.rgId,
        spaceId: formData.value.spaceId,
        spaceName: formData.value.spaceName,
        accessKey: formData.value.accessKey,
        appSecret: formData.value.appSecret
      };
      
      const res = await httpRequest.rawRequestPostAsJson('/api/llm/corpus/api/workspace/test/validate', params);
      
      // 检查data.valid字段，无论code是否为0
      if (res.code === 0 && res.data && res.data.valid === true) {
        ElMessage.success('工作空间添加成功');
        addDialogVisible.value = false;
        fetchWorkspaces(); // 重新获取列表
      } else {
        // 优先使用data.message作为错误信息
        const errorMsg = (res.data && res.data.message) || res.msg || '工作空间配置验证失败，请检查配置信息';
        // 检查是否包含a标签，若有则用ElMessageBox.alert渲染HTML
        if (/<a\s+href=['"][^'"]+['"]/.test(errorMsg)) {
          ElMessageBox.alert(errorMsg, '验证失败', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            type: 'warning',
          });
        } else {
          ElMessage.error(errorMsg);
        }
      }
    } catch (error) {
      console.error('工作空间添加失败:', error);
      ElMessage.error('工作空间添加失败，请稍后重试');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 删除工作空间
const handleDelete = (row: { spaceId: string; spaceName?: string }) => {
  // 再次检查是否是受保护的工作空间
  if (isWorkspaceLocked(row.spaceId)) {
    ElMessage.warning('此工作空间不可删除');
    return;
  }
  
  // 获取展示名称，如果有spaceName则显示"spaceName(spaceId)"，否则只显示spaceId
  const displayName = row.spaceName ? `"${row.spaceName}"(${row.spaceId})` : row.spaceId;
  
  ElMessageBox.confirm(
    `确定要删除工作空间 ${displayName} 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      deleteLoading.value = true;
      try {
        // 使用正确的删除接口路径，通过URL形式传递参数
        const res = await httpRequest.rawRequestPostAsQuery(`/api/llm/corpus/api/workspace/delete?rgId=${props.rgId}&spaceId=${row.spaceId}&misId=${props.misId}`, {});
        
        if (res.code === 0 && (!res.data || res.data.success !== false)) {
          ElMessage.success('删除成功');
          fetchWorkspaces(); // 重新获取列表
        } else {
          // 如果code为10039（未找到对应的工作空间配置），不显示错误提示
          if (res.code !== 10039) {
            // 优先使用data.message作为错误信息
            const errorMsg = (res.data && res.data.message) || res.msg || '删除失败';
            ElMessage.error(errorMsg);
          }
        }
      } catch (error) {
        console.error('删除工作空间失败:', error);
        ElMessage.error('删除失败，请稍后重试');
      } finally {
        deleteLoading.value = false;
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 处理问号图标点击事件，根据不同字段提供不同帮助页面
const handleHelpClick = (fieldType: string = 'default') => {
  let url = '';
  
  // 根据字段类型和环境确定跳转URL
  switch (fieldType) {
    case 'spaceId':
      url = isDev() || isTest() 
        ? 'https://friday.ai.test.sankuai.com/app/appList/appListV2List'
        : 'https://aigc.sankuai.com/app/knowledge/list';
      break;
    case 'accessKey':
    case 'appSecret':
      url = isDev() || isTest() 
        ? 'https://aispeech.ai.test.sankuai.com/console/requirement/list'
        : 'https://speech.sankuai.com/console/requirement/list';
      break;
    default:
      return; // 其他字段不跳转
  }
  
  // 在新窗口打开链接
  window.open(url, '_blank');
};

// 初始化
onMounted(() => {
  fetchWorkspaces();
});

// 暴露方法给父组件
defineExpose({
  fetchWorkspaces
});
</script>

<style scoped lang="scss">
.workspace-settings {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  min-width: 0;
  min-height: 0;
  padding: 0;
  .settings-section {
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    .workspace-list {
      background: #f9fafb;
      border-radius: 8px;
      padding: 24px 16px;
      
      .workspace-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .header-title {
          span {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }
        
        .header-actions {
          .add-button {
            min-width: auto;
            width: auto;
            height: 36px;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            
            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
      
      .el-table {
        background: #fff;
        border-radius: 8px;
        font-size: 15px;
      }
      .empty-list {
        padding: 40px 0;
      }
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 24px;
  .el-button {
    min-width: 120px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
  }
  .el-button--primary {
    background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
    border: none;
    color: #fff;
  }
}
// 优化滚动条
.workspace-settings ::-webkit-scrollbar {
  width: 8px;
  background: #f1f1f1;
}
.workspace-settings ::-webkit-scrollbar-thumb {
  background: #d3dce6;
  border-radius: 4px;
}

.delete-button-wrapper {
  display: inline-flex;
  align-items: center;
}

.locked-button {
  color: #909399 !important;
  cursor: not-allowed !important;
  
  &:hover {
    background-color: transparent !important;
  }
}

/* 覆盖element-plus的默认样式 */
:deep(.el-button.is-disabled.locked-button) {
  opacity: 0.7;
}

// 替换之前的label-with-icon样式
.form-label {
  display: flex;
  align-items: center;
  
  span {
    margin-left: 4px;
  }
  
  .help-icon {
    color: #909399;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s;
    
    &:hover {
      color: #409EFF;
    }
  }
}

// 确保表单标签宽度适合内容
:deep(.el-form-item__label) {
  line-height: 22px;
}

// 设置对话框内容区域的宽度适合表单
:deep(.el-dialog__body) {
  padding: 20px 30px;
}

// 移除原来的内联帮助样式
.inline-help-form-item,
.label-with-icon {
  display: none;
}
</style> 