<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, nextTick, onMounted } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElLoading } from 'element-plus'
import MonacoEditor from './MonacoEditor.vue'
import { EDIT_FORM_RULES } from '../../types'
import httpRequest from '@/utils/httpRequest'
import { debounce } from '../../utils/debounce'
import { formatTitle } from '../../utils/format'
import { Plus } from '@element-plus/icons-vue'

// 定义标签选项接口
interface TagOption {
  id: string
  name: string
}

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({
      title: '',
      content: '',
      tagsname: [],
      tagsIds: ''
    })
  },
  currentEditId: {
    type: [String, Number],
    default: ''
  },
  currentMisId: {
    type: String,
    default: ''
  },
  currentTeam: {
    type: [String, Number],
    default: ''
  },
  isSubmitting: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'large'
  },
  showAddTagOption: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'update:formData', data: any): void
  (e: 'cancel'): void
  (e: 'refresh'): void
  (e: 'open-tag-management'): void  // 新增：打开标签管理的事件
}>()

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  }
})

// 使用计算属性处理表单数据
const editFormData = computed({
  get: () => props.formData,
  set: (value) => {
    emit('update:formData', value)
  }
})

// 表单ref
const editFormRef = ref<FormInstance>()

// 编辑表单规则
const editRules = EDIT_FORM_RULES

// 是否提交中的状态
const isEditSubmitting = ref(false)

// 标签相关数据
const tagOptions = ref<TagOption[]>([])
const selectedTagIds = ref<string[]>([])
const isLoadingTags = ref(false)
// 默认标签数据
const defaultTag = ref<TagOption | null>(null)
// 标记是否正在初始化，避免初始化时意外触发更新
const isInitializing = ref(false)

// 获取标签列表
const fetchTagList = async () => {
  if (!props.currentTeam) return
  
  try {
    isLoadingTags.value = true
    const response = await httpRequest.rawRequestGet('/rgTags/listTags', {
      rgId: props.currentTeam
    })
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      // 根据实际API返回格式调整映射
      const formattedTags = response.data.map((tag: any) => {
        const id = tag.id === null ? null : String(tag.id || tag.tagId || tag.value || '')
        const name = tag.name || tag.tagName || tag.label || `标签${id || 'default'}`
        
        return {
          id,
          name
        }
      }).filter(tag => tag.name) // 只过滤掉无名称的数据
      
      // 设置默认标签（第一个标签，id为null）
      if (formattedTags.length > 0 && formattedTags[0].id === null) {
        defaultTag.value = formattedTags[0]
        // 从可选标签中移除默认标签，因为它会自动显示
        tagOptions.value = formattedTags.slice(1)
      } else {
        defaultTag.value = null
        tagOptions.value = formattedTags
      }
      
    } else {
      console.warn('获取标签列表失败:', response?.msg || '数据格式错误')
      tagOptions.value = []
      defaultTag.value = null
    }
  } catch (error) {
    console.error('获取标签列表出错:', error)
    tagOptions.value = []
    defaultTag.value = null
  } finally {
    isLoadingTags.value = false
  }
}

// 处理标签选择变化
const handleTagSelectionChange = (tagIds: string[]) => {
  // 如果正在初始化，不执行更新逻辑
  if (isInitializing.value) return
  
  // 过滤掉空字符串，防止"添加标签"选项被误选
  const filteredTagIds = tagIds.filter(tag => tag !== '')
  
  // 限制最多选择3个标签
  if (filteredTagIds.length > 3) {
    ElMessage.warning('最多只能选择3个标签')
    const limitedTagIds = filteredTagIds.slice(0, 3)
    // 更新选中的标签ID
    selectedTagIds.value = limitedTagIds
  } else {
    selectedTagIds.value = filteredTagIds
  }
  
  // 更新editFormData中的tagsIds
  const selectedTags = tagOptions.value.filter(tag => selectedTagIds.value.includes(tag.id))
  
  let submitTagsIds: string = ''
  
  if (selectedTags.length === 0 && defaultTag.value) {
    // 没有选择任何标签时，提交空字符串，后端会处理为默认标签
    submitTagsIds = ''
  } else {
    // 有选择标签时，提交选择的标签ID
    submitTagsIds = selectedTagIds.value.join(',')
  }
  
  editFormData.value = {
    ...editFormData.value,
    tagsIds: submitTagsIds
  }
}

// 监听对话框显示状态
watch(() => props.modelValue, (newVisible) => {
  if (newVisible) {
    // 设置初始化标志
    isInitializing.value = true
    
    // 对话框打开时获取标签列表
    fetchTagList().then(() => {
      // 等待标签列表加载完成后再初始化选中状态
      nextTick(() => {
        // 保存原始的tagsIds，确保不会丢失
        const originalTagsIds = editFormData.value.tagsIds
        
        if (originalTagsIds) {
          const tagIds = originalTagsIds.split(',').filter(id => id.trim())
          selectedTagIds.value = tagIds
          
          // 重要：确保tagsIds保持原始值，不要被重置
          editFormData.value = {
            ...editFormData.value,
            tagsIds: originalTagsIds
          }
        } else if (editFormData.value.tagsname && Array.isArray(editFormData.value.tagsname)) {
          // 如果没有tagsIds但有tagsname，尝试通过名称匹配ID
          const matchedIds = editFormData.value.tagsname
            .map(name => tagOptions.value.find(tag => tag.name === name)?.id)
            .filter(id => id) as string[]
          selectedTagIds.value = matchedIds
          
          // 设置对应的tagsIds
          editFormData.value = {
            ...editFormData.value,
            tagsIds: matchedIds.join(',')
          }
        } else {
          selectedTagIds.value = []
          // 如果没有任何标签，保持tagsIds为空字符串（不是undefined）
          editFormData.value = {
            ...editFormData.value,
            tagsIds: ''
          }
        }
        
        // 初始化完成，允许正常的标签选择更新
        isInitializing.value = false
      })
    })
  } else {
    // 对话框关闭时重置初始化标志
    isInitializing.value = false
  }
})

// 组件挂载时获取标签列表
onMounted(() => {
  if (props.modelValue) {
    fetchTagList()
  }
})

// 监听selectedTagIds变化，过滤掉空值
watch(selectedTagIds, (newTagIds) => {
  // 过滤掉空字符串，防止"添加标签"选项被误选
  const filteredTagIds = newTagIds.filter(tag => tag !== '')
  if (filteredTagIds.length !== newTagIds.length && !isInitializing.value) {
    selectedTagIds.value = filteredTagIds
  }
}, { deep: true })

// 处理编辑对话框打开
const handleEditDialogOpened = () => {
  setTimeout(() => {
    // 在对话框完全打开后，初始化编辑器布局
    const editEditor = document.querySelector('.edit-corpus-editor');
    if (editEditor && (editEditor as any).__vue__) {
      const editorComponent = (editEditor as any).__vue__;
      const editor = editorComponent.getEditor?.();
      if (editor && typeof editor.layout === 'function') {
        editor.layout();
      }
    }
    
    // 备用方法：直接通过DOM查找monaco-editor元素并触发布局更新
    const monacoEditors = document.querySelectorAll('.edit-dialog .monaco-editor');
    if (monacoEditors.length > 0) {
      // 强制调整宽度样式
      monacoEditors.forEach((editor: any) => {
        editor.style.width = '100%';
        // 查找overflow-guard元素并调整宽度
        const overflowGuards = editor.querySelectorAll('.overflow-guard');
        if (overflowGuards.length > 0) {
          overflowGuards.forEach((guard: any) => {
            guard.style.width = '100%';
          });
        }
      });
    }
  }, 300); // 延迟300ms确保DOM已完全渲染
}

// 处理确定按钮点击（立即显示loading，然后防抖处理）
const handleConfirmClick = () => {
  // 如果已经在提交中，直接返回
  if (isEditSubmitting.value) return
  
  // 立即设置loading状态
  isEditSubmitting.value = true
  
  // 使用防抖处理实际的提交逻辑
  debouncedHandleEditSubmit()
}

// 处理编辑表单提交
const handleEditSubmit = async () => {
  if (!editFormRef.value) return
  
  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 确保rgId是数字类型
        const rgId = parseInt(props.currentTeam.toString())
        if (!rgId || rgId <= 0) {
          ElMessage.error('无效的值班组ID')
          return
        }

        // 构造URL参数
        const queryParams = `misId=${props.currentMisId}&rgId=${rgId}&ticketId=${props.currentEditId}`

        // 构造请求体
        const requestBody = {
          title: editFormData.value.title,
          type: -1,
          content: editFormData.value.content,
          tagsIds: editFormData.value.tagsIds || ''
        }
        
        const response = await httpRequest.rawRequestPostAsJson(
          `/corpus/modifyCorpusByTicketId?${queryParams}`,
          requestBody
        )

        if (response?.code === 0) {
          ElMessage.success('保存成功')
          dialogVisible.value = false
          // 触发刷新事件
          emit('refresh')
        } else {
          ElMessage.error(`保存失败: ${response?.msg || '未知错误'}`)
        }
      } catch (error) {
        console.error('保存语料出错:', error)
        ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
      } finally {
        // 最后重置编辑提交状态
        isEditSubmitting.value = false
      }
    } else {
      // 表单验证失败，重置loading状态
      ElMessage.error('请检查表单内容')
      isEditSubmitting.value = false
    }
  })
}

// 创建防抖版本的编辑表单提交函数
const debouncedHandleEditSubmit = debounce(handleEditSubmit, 300)

// 处理编辑取消
const handleEditCancel = () => {
  dialogVisible.value = false
  editFormData.value = {
    title: '',
    content: '',
    tagsname: [],
    tagsIds: ''
  }
  ;(document.activeElement as HTMLElement)?.blur()
  emit('cancel')
}

// 创建防抖版本的编辑取消函数
const debouncedHandleEditCancel = debounce(handleEditCancel, 300)

// 处理点击"添加标签"选项
const handleAddTagClick = (e: Event) => {
  // 强制阻止所有默认行为和事件传播
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  
  // 确保"添加标签"的空值不会被添加到选中的标签中
  selectedTagIds.value = selectedTagIds.value.filter(tag => tag !== '')
  
  // 强制关闭下拉框
  nextTick(() => {
    const selectComponents = document.querySelectorAll('.el-select')
    selectComponents.forEach((select: any) => {
      if (select.__vue__ && select.__vue__.blur) {
        select.__vue__.blur()
      }
    })
  })
  
  // 直接通知父组件打开标签管理，保留当前对话框状态
  emit('open-tag-management')
  
  return false
}

// 暴露刷新标签列表的方法给父组件
defineExpose({
  refreshTagList: fetchTagList
})
</script>

<template>
  <!-- 编辑语料对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="编辑语料"
    width="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="edit-dialog"
    @opened="handleEditDialogOpened"
  >
    <el-form
      ref="editFormRef"
      :model="editFormData"
      :rules="editRules"
      label-width="100px"
    >
      <el-form-item label="问题标题" prop="title">
        <el-input
          v-model="editFormData.title"
          placeholder="请输入问题标题"
          :size="size"
        />
      </el-form-item>
      
      <!-- 添加标签选择 -->
      <el-form-item label="标签" class="tags-item">
        <!-- 默认标签显示（当没有选择其他标签时） -->
        <div v-if="selectedTagIds.length === 0 && defaultTag" class="default-tag-container">
          <el-tag
            :closable="false"
            type="info"
            class="default-tag"
          >
            {{ defaultTag.name }}
          </el-tag>
        </div>
        
        <el-select
          v-model="selectedTagIds"
          multiple
          :placeholder="`请选择标签（最多3个，已选${selectedTagIds.length}个）`"
          :size="size"
          :loading="isLoadingTags"
          @change="handleTagSelectionChange"
          style="width: 100%"
          clearable
          :multiple-limit="3"
        >
          <el-option
            v-for="tag in tagOptions"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
          >
            {{ tag.name }}
          </el-option>
          <!-- 添加"添加标签"选项 - 只有在showAddTagOption为true时才显示 -->
          <el-option
            v-if="showAddTagOption"
            key="add-tag"
            label="📝 添加标签"
            value=""
            style="border-top: 1px solid #e4e7ed;"
            :disabled="true"
            @mousedown.stop.prevent="handleAddTagClick"
            @click.stop.prevent="handleAddTagClick"
          >
            <div 
              style="display: flex; align-items: center; color: #409EFF; font-weight: 500; cursor: pointer; pointer-events: auto;" 
              @mousedown.stop.prevent="handleAddTagClick"
              @click.stop.prevent="handleAddTagClick"
            >
              <el-icon style="margin-right: 8px; font-size: 14px;"><Plus /></el-icon>
              <span>添加标签</span>
            </div>
          </el-option>
        </el-select>
        <!-- 选择状态提示 -->
        <div v-if="tagOptions.length > 0" style="margin-top: 8px; font-size: 12px; color: #909399;">
          共{{ tagOptions.length }}个标签可选，已选择{{ selectedTagIds.length }}/3个
          <span v-if="defaultTag && selectedTagIds.length === 0">，当前显示默认标签</span>
        </div>
      </el-form-item>
      
      <el-form-item label="问题内容" prop="content">
        <div class="edit-area">
          <MonacoEditor
            v-model="editFormData.content"
            language="markdown"
            height="320px"
            :options="{
              wordWrap: 'on',
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              lineNumbers: 'on',
              lineDecorationsWidth: 0,
              folding: true,
              renderLineHighlight: 'all',
              automaticLayout: true
            }"
            class="edit-corpus-editor"
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button 
          @click="debouncedHandleEditCancel" 
          :size="size" 
          :disabled="isEditSubmitting"
        >
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleConfirmClick" 
          :size="size" 
          :loading="isEditSubmitting" 
          :disabled="isEditSubmitting"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.edit-dialog {
  .edit-area {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: none;
  }
  
  .edit-corpus-editor {
    width: 100%;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    overflow: hidden;
    min-height: 320px;
    
    :deep(.monaco-editor-wrapper) {
      display: block !important;
      height: 320px !important;
      width: 100% !important;
      border: none;
      border-radius: 4px;
      
      .monaco-editor-container {
        width: 100% !important;
        height: calc(100% - 40px) !important;
        min-height: 280px !important;
        display: block !important;
        overflow: visible !important;
      }
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  .tags-item {
    margin-bottom: 16px;
    
    .default-tag-container {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      
      .default-tag {
        background-color: #f4f4f5;
        border-color: #d3d4d6;
        color: #606266;
        font-size: 12px;
        
        &:hover {
          background-color: #f4f4f5;
          border-color: #d3d4d6;
        }
      }
    }
    
    :deep(.el-form-item__label) {
      color: #606266;
      font-weight: 600;
      font-size: 14px;
      min-width: 80px;
      white-space: nowrap;
      margin-right: 8px;
    }
    
    :deep(.el-form-item__content) {
      flex: 1;
      margin-left: 0 !important;
    }
    
    :deep(.el-select) {
      width: 100%;
      
      .el-select__wrapper {
        width: 100%;
      }
      
      .el-select__tags {
        flex-wrap: wrap;
        max-width: 100%;
      }
      
      .el-tag {
        margin: 2px 4px 2px 0;
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;
        font-size: 12px;
        
        &:hover {
          background-color: #bae7ff;
          border-color: #69c0ff;
        }
        
        .el-tag__close {
          color: #1890ff;
          
          &:hover {
            background-color: #1890ff;
            color: #fff;
          }
        }
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

/* 编辑语料对话框中Monaco编辑器的样式 */
:deep(.edit-dialog .monaco-editor) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

:deep(.edit-dialog .monaco-editor-wrapper) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* 确保编辑对话框内的标签选择下拉框显示在分类标签管理面板下方 */
:deep(.edit-dialog .el-select-dropdown) {
  z-index: 2300 !important;
}

:deep(.el-select-dropdown) {
  z-index: 2300 !important;
}

:deep(.el-popper) {
  z-index: 2300 !important;
}

/* 确保CustomWorkSpace及其内部的对话框有更高的z-index */
:deep(.custom-workspace) {
  z-index: 2500 !important;
}

/* 确保标签管理相关的所有弹窗都有足够高的z-index */
:deep(.tag-dialog) {
  z-index: 3500 !important;
}

:deep(.tag-add-dialog) {
  z-index: 3500 !important;
}

:deep(.tag-edit-dialog) {
  z-index: 3510 !important;
}
</style> 