<template>
  <div>
    <div v-if="score !== 0" class="quality-score" :class="getScoreColorClass(score)">
      <span class="score-title">内容质量：</span>
      <span class="score-value" @mouseenter="showReasons = true" @mouseleave="showReasons = false">
        {{ getScoreDescription(score) }}
        <el-tooltip v-if="reasons.length > 0" popper-class="reasons-tooltip" effect="light" :visible="showReasons">
          <template #content>
            <div class="reasons-content">
              <div v-for="(reason, index) in reasons" :key="index" class="reason-item">
                {{ reason }}
              </div>
            </div>
          </template>
          <el-icon class="info-icon"><InfoFilled /></el-icon>
        </el-tooltip>
      </span>
      <el-icon v-if="isLoading" class="loading-icon"><Loading /></el-icon>
    </div>
    <div v-else-if="isLoading" class="quality-score">
      <span class="score-title">内容质量：</span>
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>评估中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { InfoFilled, Loading } from '@element-plus/icons-vue';
import httpRequest from '@/utils/httpRequest';

const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
});

// 内容质量评分状态
const score = ref(0);
const reasons = ref<string[]>([]);
const isLoading = ref(false);
const debounceTimer = ref(null);
const showReasons = ref(false);

// 获取评分描述
const getScoreDescription = (scoreValue: number) => {
  if (scoreValue === -1) return '需要修改';
  if (scoreValue >= 90) return '优秀';
  if (scoreValue >= 80) return '良好';
  if (scoreValue >= 70) return '中等';
  if (scoreValue >= 60) return '一般';
  return '需改进';
};

// 获取评分颜色样式
const getScoreColorClass = (scoreValue: number) => {
  if (scoreValue === -1) return 'score-needs-edit';
  if (scoreValue >= 90) return 'score-excellent';
  if (scoreValue >= 80) return 'score-good';
  if (scoreValue >= 60) return 'score-average';
  return 'score-poor';
};

// 获取内容质量评分
const getContentQualityAssessment = async (content: string) => {
  if (!content || isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    const response = await httpRequest.rawRequestPostAsJson(
      '/review/getContentQualityAssessment',
      { query: content }
    );
    
    if (response?.code === 0 && response.data?.result) {
      // 保存评分值，即使是-1也保存
      score.value = response.data.result.score;
      reasons.value = response.data.result.reasons || [];
    } else {
      console.warn('获取内容质量评分失败:', response?.msg);
      score.value = 0;
      reasons.value = [];
    }
  } catch (error) {
    console.error('获取内容质量评分出错:', error);
    score.value = 0;
    reasons.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 当内容变化时获取评分（带防抖）
watch(() => props.content, (newContent) => {
  if (props.visible && newContent) {
    // 清除旧的定时器
    if (debounceTimer.value) {
      clearTimeout(debounceTimer.value);
      debounceTimer.value = null;
    }
    
    // 设置新的定时器
    debounceTimer.value = setTimeout(() => {
      getContentQualityAssessment(newContent);
    }, 1000);
  }
});

// 当对话框可见性变化时处理
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.content) {
    // 延迟500ms，确保内容已经加载完成
    setTimeout(() => {
      getContentQualityAssessment(props.content);
    }, 500);
  } else {
    // 重置评分
    score.value = 0;
    reasons.value = [];
  }
});

// 组件挂载时检查是否需要获取评分
onMounted(() => {
  if (props.visible && props.content) {
    getContentQualityAssessment(props.content);
  }
});
</script>

<style lang="scss" scoped>
.quality-score {
  display: flex;
  align-items: center;
  font-size: 14px;
  
  .score-title {
    margin-right: 4px;
    color: #606266;
  }
  
  .score-value {
    font-weight: bold;
    font-size: 16px;
    margin-right: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  
  .info-icon, .loading-icon {
    margin-left: 4px;
    cursor: pointer;
  }
  
  .loading-icon {
    animation: rotating 2s linear infinite;
  }
  
  &.score-excellent .score-value {
    color: #67c23a;
  }
  
  &.score-good .score-value {
    color: #409EFF;
  }
  
  &.score-average .score-value {
    color: #e6a23c;
  }
  
  &.score-poor .score-value {
    color: #f56c6c;
  }
  
  &.score-needs-edit .score-value {
    color: #F56C6C;
    text-decoration: underline dashed;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 添加全局样式，确保tooltip内容正确显示 */
:global(.reasons-tooltip) {
  max-width: 300px !important;
  
  .el-tooltip__content {
    white-space: normal !important;
  }
}

.reasons-content {
  max-width: 280px;
  text-align: left;
  
  .reason-item {
    padding: 3px 0;
    line-height: 1.4;
    white-space: normal;
    word-break: break-word;
    
    &:not(:last-child) {
      border-bottom: 1px dashed #e0e0e0;
      margin-bottom: 3px;
    }
  }
}
</style>
