<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import httpRequest from '@/utils/httpRequest'
import MonacoEditor from './MonacoEditor.vue'
import LoadingProgressPanel from './LoadingProgressPanel.vue'
import ViewCorpusDialog from './ViewCorpusDialog.vue'
import MergeCorpusDialog from './MergeCorpusDialog.vue'
import ContentQualityScore from './ContentQualityScore.vue'
import { formatDateTime } from '../../utils/format'
import { SOURCE_MAP, POLL_CONFIG } from '../../constants'
import { API_PATHS } from '../../request/api'
import { getCurrentUser } from '@/shared/services/userService'
import { debounce } from '../../../utils/debounce'
import { Plus } from '@element-plus/icons-vue'

// 定义标签选项接口
interface TagOption {
  id: string
  name: string
}

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({
      title: '',
      content: '',
      taskId: '',
      ticketId: '',
      rgId: 0
    })
  },
  missingInfo: {
    type: Array as () => string[],
    default: () => []
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'save', 'open-tag-management'])

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
    
    // 当对话框关闭时，重置焦点状态
    if (!value) {
      setTimeout(() => {
        // 移除父组件中按钮的焦点状态
        document.querySelectorAll('.el-button').forEach(btn => {
          (btn as HTMLElement).blur();
        });
      }, 100);
    }
  }
})

// 本地表单数据
const localForm = ref({
  title: '',
  content: '',
  taskId: '',
  ticketId: '',
  rgId: 0,
  tagsIds: '' // 新增：标签ID字符串，逗号分隔
})

// 检索对比对话框可见性
const compareDialogVisible = ref(false)

// 检索结果数据
const compareResults = ref<any[]>([])

// 检索结果选中的行
const selectedCompareRows = ref<any[]>([])

// 检索结果列配置
const compareColumns = [
  { prop: 'ticketId', label: 'TASK-ID', width: 150 },
  { prop: 'score', label: '相似度', width: 100, sortable: true },
  { prop: 'title', label: '标题', minWidth: 200, showOverflowTooltip: true },
  { prop: 'content', label: '内容', minWidth: 300, showOverflowTooltip: true },
  { prop: 'updateTime', label: '更新时间', width: 160 }
]

// 加载进度面板相关
const loadingPanel = ref(false)
const loadingPanelInstance = ref(null)
const loadingPanelTitle = ref('相似语料检索中')
const loadingPanelTips = ref([
  '正在检索相似的语料',
  '请耐心等待，处理完成后会自动显示结果'
])

// 轮询状态
const pollTimer = ref(null)
const pollCount = ref(0)

// 编辑器实例引用
const editorRef = ref(null)

// 强制组件刷新的key
const editorKey = ref(0)

// 合并语料对话框相关
const mergeCorpusVisible = ref(false)
const selectedCorpusItems = ref([])
const mergePreviewForm = ref({
  title: '',
  content: '',
  taskId: '',
  ticketId: '',
  corpusIdList: [],
  type: '-1'
})

// 查看详情相关
const viewCorpusVisible = ref(false)
const currentViewItem = ref({
  ticketId: '',
  title: '',
  content: '',
  source: '',
  misId: '',
  createTime: '',
  updateTime: '',
  tagsname: ''
})

// 添加保存按钮状态变量
const isSaving = ref(false)

// 添加合并语料状态变量
const isMerging = ref(false)

// 添加批量删除状态变量
const isDeleting = ref(false)

// 添加合并保存状态
const isMergeSaving = ref(false)

// 防抖状态
let saveTimeout: number | null = null

// 标签相关数据
const tagOptions = ref<TagOption[]>([])
const selectedTagIds = ref<string[]>([])
const isLoadingTags = ref(false)
// 默认标签数据
const defaultTag = ref<TagOption | null>(null)
// 标记是否正在初始化，避免初始化时意外触发更新
const isInitializing = ref(false)

// 初始化数据
const initData = () => {
  localForm.value = {
    title: props.formData.title || '',
    content: props.formData.content || '',
    taskId: props.formData.taskId || '',
    ticketId: props.formData.ticketId || '',
    rgId: props.formData.rgId || 0,
    tagsIds: props.formData.tagsIds || ''
  }
}

// 当对话框打开时初始化数据
watch(() => props.modelValue, (newVisible) => {
  if (newVisible) {
    // 首先初始化数据
    initData()
    // 然后在下一个tick强制刷新编辑器
    nextTick(() => {
      editorKey.value += 1
      // 获取标签列表
      fetchTagList()
    })
  }
}, { immediate: true })

// 当表单数据变化时更新
watch(() => props.formData, () => {
  if (props.modelValue) {
    initData()
  }
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  initData()
})

// 获取标签列表
const fetchTagList = async () => {
  if (!localForm.value.rgId) return
  
  try {
    isLoadingTags.value = true
    const response = await httpRequest.rawRequestGet('/rgTags/listTags', {
      rgId: localForm.value.rgId
    })
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      // 根据实际API返回格式调整映射
      const formattedTags = response.data.map((tag: any) => {
        const id = tag.id === null ? null : String(tag.id || tag.tagId || tag.value || '')
        const name = tag.name || tag.tagName || tag.label || `标签${id || 'default'}`
        
        return {
          id,
          name
        }
      }).filter(tag => tag.name) // 只过滤掉无名称的数据
      
      // 设置默认标签（第一个标签，id为null）
      if (formattedTags.length > 0 && formattedTags[0].id === null) {
        defaultTag.value = formattedTags[0]
        // 从可选标签中移除默认标签，因为它会自动显示
        tagOptions.value = formattedTags.slice(1)
      } else {
        defaultTag.value = null
        tagOptions.value = formattedTags
      }
      
      // 初始化选中标签
      initSelectedTags()
      
    } else {
      console.warn('获取标签列表失败:', response?.msg || '数据格式错误')
      tagOptions.value = []
      defaultTag.value = null
    }
  } catch (error) {
    console.error('获取标签列表出错:', error)
    tagOptions.value = []
    defaultTag.value = null
  } finally {
    isLoadingTags.value = false
  }
}

// 初始化选中标签
const initSelectedTags = () => {
  isInitializing.value = true
  
  // 保存原始的tagsIds，确保不会丢失
  const originalTagsIds = localForm.value.tagsIds
  
  if (originalTagsIds) {
    const tagIds = originalTagsIds.split(',').filter(id => id.trim())
    selectedTagIds.value = tagIds
  } else {
    selectedTagIds.value = []
  }
  
  // 初始化完成，允许正常的标签选择更新
  isInitializing.value = false
}

// 处理标签选择变化
const handleTagSelectionChange = (tagIds: string[]) => {
  // 如果正在初始化，不执行更新逻辑
  if (isInitializing.value) return
  
  // 过滤掉空字符串，防止"添加标签"选项被误选
  const filteredTagIds = tagIds.filter(tag => tag !== '')
  
  // 限制最多选择3个标签
  if (filteredTagIds.length > 3) {
    ElMessage.warning('最多只能选择3个标签')
    const limitedTagIds = filteredTagIds.slice(0, 3)
    // 更新选中的标签ID
    selectedTagIds.value = limitedTagIds
  } else {
    selectedTagIds.value = filteredTagIds
  }
  
  // 更新localForm中的tagsIds
  let submitTagsIds: string = ''
  
  if (selectedTagIds.value.length === 0 && defaultTag.value) {
    // 没有选择任何标签时，提交空字符串，后端会处理为默认标签
    submitTagsIds = ''
  } else {
    // 有选择标签时，提交选择的标签ID
    submitTagsIds = selectedTagIds.value.join(',')
  }
  
  localForm.value = {
    ...localForm.value,
    tagsIds: submitTagsIds
  }
}

// 处理点击"添加标签"选项
const handleAddTagClick = (e: Event) => {
  // 强制阻止所有默认行为和事件传播
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  
  // 确保"添加标签"的空值不会被添加到选中的标签中
  selectedTagIds.value = selectedTagIds.value.filter(tag => tag !== '')
  
  // 强制关闭下拉框
  nextTick(() => {
    const selectComponents = document.querySelectorAll('.el-select')
    selectComponents.forEach((select: any) => {
      if (select.__vue__ && select.__vue__.blur) {
        select.__vue__.blur()
      }
    })
  })
  
  // 直接通知父组件打开标签管理，保留当前对话框状态
  emit('open-tag-management')
  
  return false
}

// 获取相似度样式类
const getSimilarityClass = (score: string) => {
  const value = parseInt(score)
  if (value >= 80) return 'high'
  if (value >= 60) return 'medium'
  return 'low'
}

// 处理检索对比
const handleCompare = async () => {
  // 重置选择状态
  selectedCompareRows.value = []
  // 重置检索结果
  compareResults.value = []

  // 检查内容是否为空
  if (!localForm.value.content) {
    ElMessage.warning('请先填写内容再进行检索')
    return
  }

  // 设置进度面板标题和提示
  loadingPanelTitle.value = '相似语料检索中'
  loadingPanelTips.value = [
    '正在检索相似的语料',
    '请耐心等待，处理完成后会自动显示结果'
  ]
  // 显示加载进度面板
  loadingPanel.value = true

  try {
    const content = localForm.value.content
    
    // 第一阶段进度 - 准备检索
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(15)
    }, 500)
    
    // 如果内容超过1000字符，进行分段处理
    const contentLength = content.length
    if (contentLength > 1000) {
      // 第二阶段进度 - 开始分段处理
      setTimeout(() => {
        loadingPanelInstance.value?.setProgress(30)
      }, 500)
      
      // 将内容分成多个段落，每段不超过1000字符
      const segments = []
      let start = 0
      while (start < contentLength) {
        let end = start + 1000
        // 尝试在句号、问号或感叹号处断开
        while (end < contentLength && end < start + 1200 && !'.?!。？！'.includes(content[end - 1])) {
          end++
        }
        if (end >= contentLength || end >= start + 1200) {
          end = start + 1000
        }
        segments.push(content.substring(start, end))
        start = end
      }

      // 分别处理每个段落
      const allResults = []
      for (let i = 0; i < segments.length; i++) {
        // 更新进度条
        const segmentProgress = 30 + Math.min((i / segments.length) * 60, 60)
        loadingPanelInstance.value?.setProgress(segmentProgress)
        
        const requestData = {
          rgId: localForm.value.rgId,
          query: segments[i]
        }

        const response = await httpRequest.rawRequestPostAsJson(
          '/review/querySimilarContentWithScore',
          requestData,
          {
            timeout: 300000 // 每段30秒超时
          }
        )

        if (response?.code === 0 && response.data?.data) {
          allResults.push(...response.data.data)
        } else {
          // 处理错误状态
          const errorMsg = response?.msg || '检索失败，请重试'
          console.error('检索分段失败, 接口返回:', response)
          throw new Error(errorMsg)
        }

        // 等待1秒再处理下一段
        if (i < segments.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      // 合并结果并按相似度排序
      compareResults.value = allResults
        .reduce((unique, item) => {
          const exists = unique.find(u => u.ticketId === item.ticketId)
          if (!exists) {
            unique.push(item)
          } else if (item.score > exists.score) {
            exists.score = item.score
          }
          return unique
        }, [])
        // 过滤掉相同ticketId的项（如果有）
        .filter(item => item.ticketId !== localForm.value.ticketId)
        .sort((a, b) => b.score - a.score)
        .map(item => ({
          ...item,
          score: (item.score * 100).toFixed(0),
          tagsname: item.tagsname || []
        }))
    } else {
      // 内容较短，直接处理
      // 第二阶段进度 - 开始检索处理
      setTimeout(() => {
        loadingPanelInstance.value?.setProgress(45)
      }, 500)
      
      const requestData = {
        rgId: localForm.value.rgId,
        query: content
      }

      const response = await httpRequest.rawRequestPostAsJson(
        '/review/querySimilarContentWithScore',
        requestData
      )

      if (response?.code === 0 && response.data) {
        const resultData = response.data.data || []
        // 过滤掉相同ticketId的项（如果有）
        compareResults.value = resultData
          .filter(item => item.ticketId !== localForm.value.ticketId)
          .sort((a, b) => b.score - a.score)
          .map(item => ({
            ...item,
            score: (item.score * 100).toFixed(0),
            tagsname: item.tagsname || []
          }))
        
        // 第三阶段进度 - 检索完成
        setTimeout(() => {
          loadingPanelInstance.value?.setProgress(90)
        }, 500)
      } else {
        // 处理错误状态
        const errorMsg = response?.msg || '检索失败，请重试'
        console.error('检索失败, 接口返回:', response)
        throw new Error(errorMsg)
      }
    }

    // 完成进度
    loadingPanelInstance.value?.complete()
    
    // 打开检索对话框
    setTimeout(() => {
      loadingPanel.value = false
      // 只有在有结果时才显示成功消息并打开对话框
      if (compareResults.value.length > 0) {
        compareDialogVisible.value = true
        ElMessage.success(`共找到 ${compareResults.value.length} 条相似内容`)
      } else {
        ElMessage.warning('未找到相似内容')
      }
    }, 500)
  } catch (error) {
    console.error('检索失败:', error)
    // 确保在显示错误前关闭加载面板
    loadingPanelInstance.value?.complete()
    setTimeout(() => {
      loadingPanel.value = false
      ElMessage.error(`检索失败: ${error.message || '未知错误'}`)
    }, 500)
  }
}

// 处理合并语料
const handleMergeCorpus = async () => {
  if (selectedCompareRows.value.length < 1) {
    ElMessage.warning('请至少选择一条语料进行合并')
    return
  }
  
  if (isMerging.value) return
  
  // 设置进度面板标题和提示
  loadingPanelTitle.value = '合并语料中'
  loadingPanelTips.value = [
    '系统正在处理合并请求',
    '请耐心等待，处理完成后会自动显示结果'
  ]
  
  try {
    // 设置合并状态为true
    isMerging.value = true
  
    // 显示加载进度面板
    loadingPanel.value = true
    
    // 第一阶段进度 - 准备合并
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(15)
    }, 500)
    
    // 获取当前用户的misId（如果需要的话，也可以从props中获取）
    let misId = ''
    try {
      const userInfo = await getCurrentUser()
      misId = userInfo?.login || ''
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 错误处理，但不中断流程
    }
    
    // 构造请求体数据
    const requestData = {
      triggerSource: 2, // 转换语料审核为2
      corpusTextList: [localForm.value.content], // 转换语料审核时使用编辑内容
      corpusIdList: selectedCompareRows.value.map(row => row.ticketId), // 选中的语料ID列表
      rgId: localForm.value.rgId,
      ticketId: localForm.value.ticketId, // 当前正在审核的ticketId
      misId: misId, // 添加misId参数
      tagsIds: localForm.value.tagsIds // 添加tagsIds参数
    }
    
    
    // 发送合并请求
    const response = await httpRequest.rawRequestPostAsJson(
      API_PATHS.CREATE_MERGE_CORPUS_TASK,
      requestData
    )
    
    // 第二阶段进度 - 合并请求已发送
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(45)
    }, 500)
    
    // 处理响应
    if (response?.code === 0) {
      const taskId = response.data
      
      if (!taskId) {
        throw new Error('任务ID获取失败')
      }
      
      // 保存taskId用于轮询
      mergePreviewForm.value.taskId = taskId
      
      // 开始轮询任务结果
      pollMergeTask(taskId)
    } else {
      // 处理错误状态
      const errorMsg = response?.msg || '合并语料失败，请重试'
      console.error('创建合并任务失败, 接口返回:', response)
      throw new Error(errorMsg)
    }
  } catch (error) {
    console.error('合并语料失败:', error)
    // 确保在显示错误前关闭加载面板
    loadingPanelInstance.value?.complete()
    clearPollTimer()
    
    setTimeout(() => {
      loadingPanel.value = false
      isMerging.value = false
      ElMessage.error(`合并语料失败: ${error.message || '未知错误'}`)
    }, 500)
  }
}

// 轮询合并任务结果
const pollMergeTask = async (taskId) => {
  // 清理之前的轮询
  clearPollTimer()
  
  // 重置轮询计数
  pollCount.value = 0
  
  // 设置轮询提示
  loadingPanelTips.value = [
    '系统正在处理合并请求',
    '请耐心等待，处理完成后会自动显示结果'
  ]
  
  // 启动轮询
  pollTimer.value = setInterval(async () => {
    try {
      pollCount.value++
      
      // 更新进度
      const progress = Math.min(45 + (pollCount.value / POLL_CONFIG.MAX_RETRIES) * 45, 90)
      loadingPanelInstance.value?.setProgress(progress)
      
      // 如果达到最大轮询次数，停止轮询
      if (pollCount.value > POLL_CONFIG.MAX_RETRIES) {
        clearPollTimer()
        isMerging.value = false
        throw new Error('轮询超时，请稍后查看任务列表')
      }
      
      // 查询任务状态
      const response = await httpRequest.rawRequestGet(
        '/review/queryModelOutputByTaskId',
        { taskId }
      )
      
      
      // 任务成功完成
      if (response?.code === 0 && response?.data && response.data.taskStatus === 1) {
        // 停止轮询
        clearPollTimer()
        
        // 完成进度
        loadingPanelInstance.value?.complete()
        
        // 设置合并预览表单数据
        mergePreviewForm.value = {
          title: response.data.title || '',
          content: response.data.content || '',
          taskId: response.data.taskId,
          ticketId: response.data.ticketId,
          corpusIdList: selectedCompareRows.value.map(row => row.ticketId),
          type: '-1',
          tagsIds: response.data.tagsIds || ''
        }
        
        // 关闭进度面板并打开合并预览
        setTimeout(() => {
          loadingPanel.value = false
          isMerging.value = false
          mergeCorpusVisible.value = true
          compareDialogVisible.value = false // 关闭检索对比对话框
          
          // 清空选中状态
          selectedCompareRows.value = []
        }, 500)
      } 
      // 任务失败
      else if (response?.code === 0 && response?.data && response.data.taskStatus === 2) {
        // 停止轮询
        clearPollTimer()
        
        // 处理失败状态
        const errorMsg = response.data.taskMessage || '合并语料失败，请重试'
        throw new Error(errorMsg)
      } 
      // 任务仍在处理中
      else if (response?.code === 0 && response?.data) {
        // 继续轮询
      } 
      // 其他错误情况
      else {
        const errorMsg = response?.msg || '查询任务状态失败'
        console.error('查询任务状态失败:', response)
        throw new Error(errorMsg)
      }
    } catch (error) {
      console.error('轮询出错:', error)
      
      // 停止轮询
      clearPollTimer()
      
      // 完成进度并显示错误
      loadingPanelInstance.value?.complete()
      setTimeout(() => {
        loadingPanel.value = false
        isMerging.value = false
        ElMessage.error(`合并语料失败: ${error.message || '未知错误'}`)
      }, 500)
    }
  }, POLL_CONFIG.INTERVAL)
}

// 处理合并语料保存
const handleMergeCorpusSave = async (formData) => {
  if (isMergeSaving.value) return
  
  try {
    isMergeSaving.value = true
    
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在保存合并语料...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    // 获取当前用户的misId
    let misId = ''
    try {
      const userInfo = await getCurrentUser()
      misId = userInfo?.login || ''
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 错误处理，但不中断流程
    }
    
    // 发送保存请求
    const requestData = {
      title: formData.title,
      content: formData.content,
      ticketId: formData.ticketId,
      corpusIdList: formData.corpusIdList,
      rgId: formData.rgId,
      taskId: formData.taskId,
      misId: misId || '',
      source: 4, // 合并语料固定为4
      type: formData.type || -1,
      tagsIds: formData.tagsIds || ''
    }
    
    // 发送请求
    const response = await httpRequest.rawRequestPostAsForm(
      '/review/saveModifiedOutput',
      requestData
    )
    
    loadingInstance.close()
    
    if (response?.code === 0) {
      ElMessage.success('合并语料保存成功')
      
      // 关闭合并对话框
      mergeCorpusVisible.value = false
      
      // 触发保存事件
      emit('save', formData)
      
      // 关闭整个转换语料审核表单
      dialogVisible.value = false
    } else {
      ElMessage.error(response?.msg || '合并语料保存失败')
    }
  } catch (error) {
    console.error('保存合并语料失败:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  } finally {
    isMergeSaving.value = false
  }
}

// 清理轮询定时器
const clearPollTimer = () => {
  if (pollTimer.value) {
    clearInterval(pollTimer.value)
    pollTimer.value = null
  }
}

// 在组件销毁时清理定时器
onBeforeUnmount(() => {
  clearPollTimer()
})

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedCompareRows.value.length === 0) {
    ElMessage.warning('请至少选择一条语料')
    return
  }
  
  if (isDeleting.value) return
  
  const ticketIds = selectedCompareRows.value.map(item => item.ticketId)
  const count = ticketIds.length
  
  ElMessageBox.confirm(
    `确定要删除选中的${count}条语料吗？此操作不可恢复。`,
    '删除语料',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        isDeleting.value = true
        
        const loadingInstance = ElLoading.service({
          lock: true,
          text: '正在删除...',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        
        // 获取当前用户的misId
        let misId = ''
        try {
          const userInfo = await getCurrentUser()
          misId = userInfo?.login || ''
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 错误处理，但不中断流程
        }
        
        // 构造URL查询字符串
        const queryParams = `misId=${misId}&rgId=${localForm.value.rgId}&ticketIds=${ticketIds.join(',')}`
        
        
        // 使用POST请求，但参数放在URL中，而不是请求体中
        const response = await httpRequest.rawRequestPostAsJson(
          `/corpus/deleteCorpusByTicketIds?${queryParams}`,
          null  // 请求体为空
        )
        
        loadingInstance.close()
        
        if (response?.code === 0) {
          ElMessage.success(`成功删除${count}条语料`)
          // 从结果列表中移除删除的项
          compareResults.value = compareResults.value.filter(
            item => !ticketIds.includes(item.ticketId)
          )
          // 清空选中项
          selectedCompareRows.value = []
          // 重置所有项的选中状态
          compareResults.value.forEach(item => {
            item.selected = false
          })
        } else {
          ElMessage.error(response?.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除语料失败:', error)
        ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
      } finally {
        isDeleting.value = false
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 处理查看详情
const handleViewDetail = (item) => {
  // 设置查看的语料数据
  currentViewItem.value = {
    ticketId: item.ticketId || '',
    title: item.title || '',
    content: item.content || '',
    source: SOURCE_MAP[item.source] || '相似语料',
    misId: item.misId || '',
    createTime: formatDateTime(item.createTime) || '',
    updateTime: formatDateTime(item.updateTime) || '',
    tagsname: Array.isArray(item.tagsname) ? item.tagsname : []  // 确保tagsname是数组类型
  }
  // 打开查看详情对话框
  viewCorpusVisible.value = true
}

// 处理保存 - 简化版本
const handleSave = async () => {
  try {
    await performSave()
  } catch (error) {
    console.error('ReviewDialog - 保存出错:', error)
  }
}

// 实际执行保存的方法
const performSave = async () => {
  if (isSaving.value) {
    return
  }
  
  if (!localForm.value.title.trim()) {
    ElMessage.warning('请输入标题')
    return
  }

  if (!localForm.value.content.trim()) {
    ElMessage.warning('请输入内容')
    return
  }

  try {
    isSaving.value = true
    
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在保存...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 获取当前用户的misId
    let misId = ''
    try {
      const userInfo = await getCurrentUser()
      misId = userInfo?.login || ''
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 错误处理，但不中断流程
    }

    // 构建请求数据
    const requestData = {
      modifiedContent: [localForm.value.content],
      taskId: [localForm.value.taskId],
      optimus_platform: ['1'],
      _token: ['token'],
      optimus_code: ['10'],
      optimus_partner: ['52'],
      optimus_origin_url: [''],
      title: [localForm.value.title],
      ticketId: [localForm.value.ticketId],
      misId: [misId || ''],
      optimus_risk_level: ['71'],
      rgId: [String(localForm.value.rgId)],
      tagsIds: [localForm.value.tagsIds || '']
    }
    
    // 发送请求
    const response = await httpRequest.rawRequestPostAsForm(
      '/review/saveModifiedOutput',
      requestData
    )
    
    loadingInstance.close()

    // 严格判断接口返回状态
    if (response && response.code === 0) {
      ElMessage.success('保存成功')
      // 触发保存事件
      emit('save', localForm.value)
      // 关闭对话框
      dialogVisible.value = false
    } else {
      // 显示具体的错误信息
      const errorMsg = response?.msg || response?.message || '保存失败，请重试'
      console.error('保存失败, 接口返回:', response)
      ElMessage.error(`保存失败: ${errorMsg}`)
    }
  } catch (error) {
    console.error('ReviewDialog - 保存语料出错:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  } finally {
    isSaving.value = false
  }
}

// 处理关闭检索对话框
const handleCompareClose = () => {
  // 清空选中数据
  selectedCompareRows.value = []
}

// 处理单个项的选择
const handleItemSelect = (item, selected) => {
  if (selected) {
    // 将项添加到选中数组
    selectedCompareRows.value.push(item)
  } else {
    // 从选中数组中移除项
    const index = selectedCompareRows.value.findIndex(row => row.ticketId === item.ticketId)
    if (index !== -1) {
      selectedCompareRows.value.splice(index, 1)
    }
  }
}
</script>

<template>
  <!-- 转换语料审核对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="转换语料审核"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="review-dialog"
    destroy-on-close
  >
    <template #header>
      <div class="dialog-header">
        <span class="dialog-title">转换语料审核</span>
        <div class="dialog-actions">
          <el-button @click="handleCompare" :disabled="isSaving">检索对比</el-button>
          <el-button type="primary" @click="handleSave" :loading="isSaving" :disabled="isSaving">确定保存</el-button>
        </div>
      </div>
    </template>

    <div class="review-content">
      <div class="content-main">
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">内容标题</div>
            <ContentQualityScore 
              :content="localForm.content" 
              :visible="dialogVisible" 
            />
          </div>
          <el-input
            v-model="localForm.title"
            placeholder="请输入标题"
          />
        </div>

        <!-- 添加标签选择 -->
        <div class="form-section">
          <div class="section-header">
            <div class="header-title">标签</div>
          </div>
          <div class="tags-item">
            <!-- 默认标签显示（当没有选择其他标签时） -->
            <div v-if="selectedTagIds.length === 0 && defaultTag" class="default-tag-container">
              <el-tag
                :closable="false"
                type="info"
                class="default-tag"
              >
                {{ defaultTag.name }}
              </el-tag>
            </div>
            
            <el-select
              v-model="selectedTagIds"
              multiple
              :placeholder="`请选择标签（最多3个，已选${selectedTagIds.length}个）`"
              :loading="isLoadingTags"
              @change="handleTagSelectionChange"
              style="width: 100%"
              clearable
              :multiple-limit="3"
            >
              <el-option
                v-for="tag in tagOptions"
                :key="tag.id"
                :label="tag.name"
                :value="tag.id"
              >
                {{ tag.name }}
              </el-option>
              <!-- 添加"添加标签"选项 -->
              <el-option
                key="add-tag"
                label="📝 添加标签"
                value=""
                style="border-top: 1px solid #e4e7ed;"
                :disabled="true"
                @mousedown.stop.prevent="handleAddTagClick"
                @click.stop.prevent="handleAddTagClick"
              >
                <div 
                  style="display: flex; align-items: center; color: #409EFF; font-weight: 500; cursor: pointer; pointer-events: auto;" 
                  @mousedown.stop.prevent="handleAddTagClick"
                  @click.stop.prevent="handleAddTagClick"
                >
                  <el-icon style="margin-right: 8px; font-size: 14px;"><Plus /></el-icon>
                  <span>添加标签</span>
                </div>
              </el-option>
            </el-select>
            <!-- 选择状态提示 -->
            <div v-if="tagOptions.length > 0" style="margin-top: 8px; font-size: 12px; color: #909399;">
              共{{ tagOptions.length }}个标签可选，已选择{{ selectedTagIds.length }}/3个
              <span v-if="defaultTag && selectedTagIds.length === 0">，当前显示默认标签</span>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="section-header">编辑内容</div>
          <MonacoEditor
            :key="editorKey"
            ref="editorRef"
            v-model="localForm.content"
            language="markdown"
            height="500px"
            class="content-editor"
            :read-only="false"
            :options="{
              automaticLayout: true,
              wordWrap: 'on',
              lineNumbers: 'on',
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              readOnly: false,
              fontSize: 15,
              lineHeight: 22
            }"
          />
        </div>
      </div>

      <div class="content-side">
        <div class="missing-info">
          <div class="side-title">
            模型认为缺失的信息 ({{ missingInfo.length }})
          </div>
          <div class="missing-list" v-if="missingInfo.length">
            <div
              v-for="(item, index) in missingInfo"
              :key="index"
              class="missing-item"
            >
              {{ item }}
            </div>
          </div>
          <div v-else class="no-missing">暂无缺失信息</div>
        </div>
      </div>
    </div>
  </el-dialog>
  
  <!-- 检索对比抽屉 -->
  <el-drawer
    v-model="compareDialogVisible"
    title="相似语料检索结果"
    size="40%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="compare-dialog"
    direction="rtl"
    :modal="false"
    :append-to-body="true"
    :destroy-on-close="false"
    @closed="handleCompareClose"
  >
    <div class="compare-header">
      <div class="compare-tip">
        共找到 {{ compareResults.length }} 条相似内容
        <div class="sub-tip">系统根据语义相似度对语料进行了排序，您可以选择一条或多条语料进行合并或应用操作。</div>
      </div>
    </div>
    
    <div class="compare-list">
      <div v-for="(item, index) in compareResults" :key="index" class="compare-item">
        <div class="compare-item-header">
          <div class="left-section">
            <el-checkbox 
              v-model="item.selected" 
              @change="(val) => handleItemSelect(item, val)"
            />
            <div class="task-id">{{ item.ticketId }}</div>
            <div class="similarity-score" :class="getSimilarityClass(item.score)">
              相似度 {{ item.score }}%
            </div>
          </div>
          <div class="actions">
            <el-button link type="primary" @click="handleViewDetail(item)">
              查看详情
            </el-button>
          </div>
        </div>
        <div class="compare-item-content">
          <div class="content-text">{{ item.content }}</div>
        </div>
        <div class="compare-item-footer">
          <div class="update-time">更新时间：{{ item.updateTime }}</div>
        </div>
      </div>
      <div v-if="compareResults.length === 0" class="no-data">
        暂无相似语料
      </div>
    </div>
    
    <template #footer>
      <div class="drawer-footer">
        <div class="selected-count" v-if="selectedCompareRows.length > 0">
          已选择 {{ selectedCompareRows.length }} 项
        </div>
        <div class="footer-buttons">
          <el-button
            @click="handleMergeCorpus"
            :disabled="selectedCompareRows.length < 1 || isMerging"
            :loading="isMerging"
          >
            合并语料
          </el-button>
          <el-button
            type="danger"
            @click="handleBatchDelete"
            :disabled="selectedCompareRows.length === 0 || isDeleting"
            :loading="isDeleting"
          >
            批量删除
          </el-button>
          <el-button @click="compareDialogVisible = false" :disabled="isMerging || isDeleting">关闭</el-button>
        </div>
      </div>
    </template>
  </el-drawer>

  <!-- 语料处理加载进度面板 -->
  <LoadingProgressPanel
    v-model:visible="loadingPanel"
    ref="loadingPanelInstance"
    :title="loadingPanelTitle"
    message="系统正在处理您的请求..."
    :tips="loadingPanelTips"
    :duration="30000"
  />
  
  <!-- 查看语料详情对话框 -->
  <ViewCorpusDialog
    v-model="viewCorpusVisible"
    :corpus-data="currentViewItem"
  />

  <!-- 合并语料对话框 -->
  <MergeCorpusDialog
    v-model="mergeCorpusVisible"
    :form-data="mergePreviewForm"
    :rg-id="localForm.rgId"
    @save="handleMergeCorpusSave"
  />
</template>

<style lang="scss" scoped>
.review-dialog {
  :deep(.el-dialog) {
    margin-top: 5vh !important;
    height: 90vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__header) {
    padding: 15px 20px;
    margin-right: 0;
    border-bottom: 1px solid #dcdfe6;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .dialog-title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
    }

    .dialog-actions {
      display: flex;
      gap: 12px;
    }
  }

  .review-content {
    height: 100%;
    display: flex;
    overflow: hidden;

    .content-main {
      flex: 1;
      padding: 5px 20px 15px 20px;
      overflow-y: auto;
      border-right: 1px solid #dcdfe6;

      .form-section {
        margin-bottom: 12px;

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .header-title {
            font-weight: bold;
            font-size: 15px;
            color: #303133;
          }
        }

        .el-input {
          margin-bottom: 12px;
          :deep(.el-input__inner) {
            font-size: 15px;
          }
        }

        .content-editor {
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          min-height: 500px;
          display: block;
          
          :deep(.monaco-editor-wrapper) {
            display: block;
            height: 500px;
            
            .monaco-editor-container {
              width: 100%;
              height: calc(100% - 40px) !important;
              min-height: 450px;
              display: block !important;
              overflow: visible;
            }

            .editor-toolbar {
              display: flex !important;
            }
          }
        }

        /* 标签选择样式 */
        .tags-item {
          margin-bottom: 16px;
          
          .default-tag-container {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            
            .default-tag {
              background-color: #f4f4f5;
              border-color: #d3d4d6;
              color: #606266;
              font-size: 12px;
              
              &:hover {
                background-color: #f4f4f5;
                border-color: #d3d4d6;
              }
            }
          }
        }
      }
    }

    .content-side {
      width: 300px;
      flex-shrink: 0;
      padding: 15px;
      background: #fff;

      .missing-info {
        .side-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 12px;
        }

        .missing-list {
          .missing-item {
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #fdf6ec;
            border-radius: 4px;
            color: #e6a23c;
            font-size: 15px;
            line-height: 1.4;
            border-left: 3px solid #e6a23c;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .no-missing {
          color: #909399;
          font-size: 15px;
          text-align: center;
          padding: 20px 0;
        }
      }
    }
  }
}

.compare-dialog {
  :deep(.el-drawer__header) {
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  :deep(.el-drawer__body) {
    padding: 20px;
    overflow-y: auto;
  }

  :deep(.el-drawer__footer) {
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .drawer-footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .selected-count {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }

    .footer-buttons {
      display: flex;
      gap: 12px;
      
      .el-button {
        margin-left: 0;
      }
    }
  }

  .compare-header {
    margin-bottom: 20px;

    .compare-tip {
      font-size: 16px;
      color: #303133;
      line-height: 1.6;

      .sub-tip {
        margin-top: 8px;
        font-size: 14px;
        color: #909399;
        line-height: 1.5;
      }
    }
  }

  .compare-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding-bottom: 60px;

    .compare-item {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      border: 1px solid #e4e7ed;

      .compare-item-header {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;

        .left-section {
          display: flex;
          align-items: center;
          gap: 12px;

          :deep(.el-checkbox) {
            margin-right: 0;
          }

          .task-id {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
          }
        }

        .similarity-score {
          display: inline-block;
          padding: 2px 12px;
          border-radius: 12px;
          font-weight: 500;
          font-size: 13px;
          
          &.high {
            background-color: #e8f5e9;
            color: #4caf50;
          }
          
          &.medium {
            background-color: #fff3e0;
            color: #ff9800;
          }
          
          &.low {
            background-color: #ffebee;
            color: #f44336;
          }
        }

        .actions {
          .el-button {
            font-size: 13px;
            color: #409eff;
          }
        }
      }

      .compare-item-content {
        padding: 16px;
        
        .content-text {
          font-size: 14px;
          line-height: 1.6;
          color: #303133;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }
      }

      .compare-item-footer {
        padding: 12px 16px;
        border-top: 1px solid #f0f0f0;
        background: #fafafa;

        .update-time {
          font-size: 13px;
          color: #909399;
        }
      }
    }

    .no-data {
      text-align: center;
      padding: 40px 0;
      color: #909399;
      font-size: 14px;
    }
  }
}
</style> 