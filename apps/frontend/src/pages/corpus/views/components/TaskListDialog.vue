<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { 
  Refresh,
  CircleCloseFilled,
  Warning,
  Clock,
  Document,
  User,
  Calendar,
  InfoFilled
} from '@element-plus/icons-vue'
import httpRequest from '@/utils/httpRequest'
import { API_PATHS } from '../../request/api'
import { TaskItem, TaskStatusMap } from '../../types/taskList'
import { formatDateTime } from '../../utils/format'
import { getCurrentUser } from '@/shared/services/userService'
import { PLATFORM_MAP } from '../../constants'
import ConvertCorpusDialog from './ConvertCorpusDialog.vue'
import CustomWorkSpace from './CustomWorkSpace.vue'

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  rgId: {
    type: [Number, String],
    default: ''
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'close'])

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
    // 如果是关闭对话框，则触发close事件
    if (!value) {
      emit('close')
    }
  }
})

// 表格数据
const tableData = ref<TaskItem[]>([])

// 当前选中的行
const currentSelectedRow = ref<TaskItem | null>(null)

// 加载状态
const loading = ref(false)

// 分页相关
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 设置组件大小
const large = 'default'

// 是否只看自己的提交
const onlyShowMine = ref(false)

// 当前用户的 misId
const currentMisId = ref('')

// 转换语料审核对话框
const reviewDialogVisible = ref(false)
const reviewFormData = ref({
  title: '',
  content: '',
  originalContent: '',
  taskId: '',
  ticketId: '',
  rgId: 0,
  tagsIds: '',
  taskMissingInfo: []
})

// 添加 missingInfo 数据
const taskMissingInfo = ref<string[]>([])

// 添加当前用户empId
const currentEmpId = ref('')

// 添加保存状态
const isConvertSaving = ref(false)

// 添加标签管理相关状态
const customWorkSpaceVisible = ref(false)
const customWorkSpaceDefaultTab = ref('tagManagement') // 默认打开分类标签管理标签页

// 添加ConvertCorpusDialog组件的引用
const convertCorpusDialogRef = ref<InstanceType<typeof ConvertCorpusDialog> | null>(null)

// 获取用户信息
const getUserInfo = async () => {
  try {
    const userInfo = await getCurrentUser()
    if (userInfo?.login) {
      currentMisId.value = userInfo.login
      currentEmpId.value = userInfo.id || '' // 添加empId获取
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 在组件挂载时获取用户信息
onMounted(async () => {
  await getUserInfo()
})

// 监听筛选条件变化
watch(onlyShowMine, async (newValue) => {
  if (newValue && !currentMisId.value) {
    // 如果开启"只看我的"但没有 misId，重新获取用户信息
    await getUserInfo()
    if (!currentMisId.value) {
      onlyShowMine.value = false
      return
    }
  }
  pagination.value.currentPage = 1 // 切换筛选条件时重置页码
  fetchTaskList()
})

// 监听rgId变化，当组件可见且rgId有效时自动获取数据
watch([() => props.rgId, () => dialogVisible.value], ([newRgId, newVisible]) => {
  if (newVisible && newRgId) {
    fetchTaskList()
  }
}, { immediate: true })

// 获取任务列表
const fetchTaskList = async () => {
  if (!props.rgId) {
    ElMessage.warning('未选择值班组，无法获取任务列表')
    return
  }

  loading.value = true
  try {
    const params = {
      rgId: props.rgId,
      pageNum: pagination.value.currentPage,
      pageSize: pagination.value.pageSize
    }

    // 根据筛选条件选择不同的接口
    const apiPath = onlyShowMine.value ? API_PATHS.QUERY_PERSONAL_TASK_LIST : API_PATHS.QUERY_TASK_LIST
    
    // 如果是查看个人任务，添加 misId 参数
    if (onlyShowMine.value) {
      params.misId = currentMisId.value
    }

    const response = await httpRequest.rawRequestGet(apiPath, params)

    if (response?.code === 0 && response?.data) {
      // 处理每个任务项，格式化时间
      tableData.value = response.data.list.map(item => {
        return {
          ...item,
          taskId: item.taskId, 
          platformId: item.platformId, 
          platform: getPlatformLabel(item.platformId),
          createTime: formatDateTime(item.createTime),
          updateTime: formatDateTime(item.updateTime)
        }
      })
      pagination.value.total = response.data.total || 0
    } else {
      ElMessage.error(response?.msg || '获取任务列表失败')
    }
  } catch (error) {
    console.error('获取任务列表出错:', error)
    ElMessage.error(`获取任务列表失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

// 刷新列表
const handleRefresh = () => {
  // 只重新发送请求获取数据，不改变任何状态
  fetchTaskList()
}

// 处理页码改变
const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  fetchTaskList()
}

// 处理每页条数改变
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  pagination.value.currentPage = 1
  fetchTaskList()
}

// 获取状态标签类型
const getStatusType = (status: number) => {
  return TaskStatusMap[status]?.type || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: number) => {
  return TaskStatusMap[status]?.label || '未知'
}

// 获取平台来源文本
const getPlatformLabel = (platformId: number | string) => {
  // 如果platformId是null、undefined或空字符串，返回未知来源
  if (platformId === null || platformId === undefined || platformId === '') {
    return '未知来源'
  }
  
  // 确保platformId是数字类型
  const id = typeof platformId === 'string' ? parseInt(platformId, 10) : platformId
  
  // 如果转换后不是有效数字，返回未知来源
  if (isNaN(id)) {
    return '未知来源'
  }
  
  // 返回对应平台名称或默认值
  return PLATFORM_MAP[id] || '未知来源'
}

// 处理查看详情
const handleViewDetail = async (row: TaskItem) => {
  // 设置当前选中行
  currentSelectedRow.value = row;
  
  if (row.taskStatus === 2) { // 失败状态
    ElMessageBox.alert(
      `<div class="task-error-content">
        <div class="error-header">
          <div class="error-title">
            <span class="main-title">任务执行失败</span>
            <span class="sub-title">Task Execution Failed</span>
          </div>
        </div>
        <div class="error-details">
          <div class="error-section">
            <div class="section-header">
              <span>错误信息</span>
            </div>
            <div class="error-message">${row.taskMessage || '未知错误'}</div>
          </div>
          <div class="error-section">
            <div class="section-header">
              <span>任务信息</span>
            </div>
            <div class="info-grid">
              <div class="info-item info-item-full">
                <span class="label">任务ID：</span>
                <span class="value">${row.taskId}</span>
              </div>
              <div class="info-item">
                <span class="label">创建人：</span>
                <span class="value">${row.creatorMisId}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">${row.createTime}</span>
              </div>
              <div class="info-item">
                <span class="label">更新时间：</span>
                <span class="value">${row.updateTime}</span>
              </div>
            </div>
          </div>
        </div>
      </div>`,
      ' ',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '关闭',
        customClass: 'task-error-dialog',
        callback: (action) => {
          // 弹窗关闭后重置当前选中行
          currentSelectedRow.value = null;
          
          // 移除所有按钮的焦点状态
          setTimeout(() => {
            document.querySelectorAll('.el-button').forEach(btn => {
              (btn as HTMLElement).blur();
            });
          }, 1);
        }
      }
    );
  } else if (row.taskStatus === 1) {
    try {
      const response = await httpRequest.rawRequestGet('/review/queryModelOutputByTaskId', {
        taskId: row.taskId
      })

      if (response?.code === 0 && response?.data) {
        // 先准备好表单数据
        const content = response.data.content || row.content || ''
        const title = response.data.title || row.title || ''
        
        reviewFormData.value = {
          title,
          content,
          originalContent: content,
          taskId: row.taskId,
          ticketId: row.ticketId || '',
          rgId: props.rgId,
          tagsIds: response.data.taskTagsIds || '',
          taskMissingInfo: response.data.taskMissingInfo || []
        }
        
        // 设置 missingInfo 数据
        taskMissingInfo.value = response.data.taskMissingInfo || []
        
        // 打开对话框
        reviewDialogVisible.value = true
      } else {
        const errorMsg = response?.msg || '获取任务详情失败'
        console.error('获取任务详情失败:', errorMsg)
        ElMessage.error(errorMsg)
      }
    } catch (error) {
      console.error('获取任务详情失败:', error)
      ElMessage.error('获取任务详情失败: ' + (error.message || '未知错误'))
    }
  } else {
    ElMessage.info('任务正在处理中，暂无详情可查看')
    // 对于处理中的任务，也需要重置选中状态
    currentSelectedRow.value = null;
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  
  // 添加重置按钮状态的逻辑
  setTimeout(() => {
    // 重置所有按钮的焦点状态
    document.querySelectorAll('.el-button').forEach(btn => {
      (btn as HTMLElement).blur();
    });
    // 如果存在选中行的状态变量，重置它
    if (typeof currentSelectedRow !== 'undefined') {
      currentSelectedRow.value = null;
    }
  }, 1);
}

// 处理转换保存
const handleConvertSave = async () => {
  if (isConvertSaving.value) return
  
  try {
    isConvertSaving.value = true
    
    // 构造请求参数
    const requestData = {
      misId: [currentMisId.value],
      optimus_risk_level: ['71'],
      modifiedContent: [reviewFormData.value.content], 
      rgId: [String(reviewFormData.value.rgId)], 
      optimus_platform: ['1'],
      _token: ['eJyV0t1LwzAQAPD/Jb5mbT4uX4MhFVEmDU7X8YesjXOMteNtYog/u9eIi17EoSW/rjc5a5pv8hxWpExZ8wwRslHOJIx4RnLNKGka3HFANfGSQVKWErWQ8xyxaXUhpLV8fmSjBfccE0tsGWMzDCw4E4wypnFUG lllQAXjFriknktesO4zyv9m228s3OZ11ou6z1zfbd19l6v8vxPrzVvlmHvG6q8HmWv4Tj/sq3XS40c/y88 12Wk10WDEHwY84F3IEJoiRr2w1Cg4ceClX1jp8q/83JDjsrozDarAUFOAup3J/SbFBHCWT5CAYpFEiyfXSfFCs4EkKxZJsL4M9wCUJlE3CPDBJmAfxe2ob81QS7gepr415qYc1g2wvhxNA6uGwQqYeDieVsUf8ZUCapBjTSTYJj2wbjwyf/vTo6NNDcYd5IAU1gtOLeVnex4BUhionftexquurb/GPxPW23jSocPNZzjfTorjeFLPHyYR8/wD5Jqls'],
      optimus_code: ['10'],
      optimus_partner: ['52'],
      optimus_origin_url: ['http://dos.banma.test.sankuai.com/compliance/index#/feroFast/26091?taskId=6eb094ea-1123-47e2-ad8d-e9494a33b889'],
      title: [reviewFormData.value.title], 
      ticketId: [reviewFormData.value.ticketId],
      tagsIds: [reviewFormData.value.tagsIds || ''],
      ...(reviewFormData.value.taskId ? { taskId: [reviewFormData.value.taskId] } : {})
    }
    

    // 发送请求
    const response = await httpRequest.rawRequestPostAsForm(
      '/review/saveModifiedOutput',
      requestData
    )
    

    if (response?.code === 0) {
      ElMessage.success('保存成功')
      reviewDialogVisible.value = false
      // 刷新任务列表
      fetchTaskList()
    } else {
      ElMessage.error(`保存失败: ${response?.msg || '未知错误'}`)
    }
  } catch (error) {
    console.error('任务列表-保存语料出错:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  } finally {
    isConvertSaving.value = false
  }
}

// 处理转换比较
const handleConvertCompare = async () => {
  // 检索相似语料的逻辑可以在这里实现
  // 暂时只显示提示
  ElMessage.info('检索对比功能开发中...')
}

// 处理转换关闭
const handleConvertClose = () => {
  reviewDialogVisible.value = false
  
  // 重置选中状态
  currentSelectedRow.value = null
  
  // 移除所有按钮的焦点状态
  setTimeout(() => {
    document.querySelectorAll('.el-button').forEach(btn => {
      (btn as HTMLElement).blur()
    })
  }, 100)
}

// 处理打开标签管理
const handleOpenTagManagement = () => {
  customWorkSpaceVisible.value = true
}

// 处理标签更新
const handleTagUpdated = () => {
  // 当标签更新后，刷新ConvertCorpusDialog中的标签列表
  if (reviewDialogVisible.value && convertCorpusDialogRef.value) {
    // 调用ConvertCorpusDialog的refreshTagList方法刷新标签列表
    convertCorpusDialogRef.value.refreshTagList()
    console.log('已刷新转换语料审核对话框的标签列表')
  }
}

// 处理自定义工作空间关闭
const handleCustomWorkSpaceClose = () => {
  customWorkSpaceVisible.value = false
  
  // 在标签管理面板关闭时刷新转换语料审核对话框的标签列表
  if (reviewDialogVisible.value && convertCorpusDialogRef.value) {
    // 调用刷新方法
    convertCorpusDialogRef.value.refreshTagList()
    console.log('标签管理面板关闭，已刷新转换语料审核对话框的标签列表')
  }
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="task-list-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <div class="header-left">
          <span class="title">提交任务列表</span>
          <span class="subtitle">（仅显示最近一个月的任务）</span>
          <el-switch
            v-model="onlyShowMine"
            class="filter-switch"
            active-text="只看我的"
          />
        </div>
        <div class="dialog-header-actions">
          <button
            class="el-button is-link refresh-button"
            title="刷新列表"
            @click="handleRefresh"
          >
            <i class="el-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                <path fill="currentColor" d="M771.776 794.88A384 384 0 0 1 128 512h64a320 320 0 0 0 555.712 216.448H654.72a32 32 0 1 1 0-64h149.056a32 32 0 0 1 32 32v148.928a32 32 0 1 1-64 0v-50.56zM276.288 295.616h92.992a32 32 0 0 1 0 64H220.16a32 32 0 0 1-32-32V178.56a32 32 0 0 1 64 0v50.56A384 384 0 0 1 896.128 512h-64a320 320 0 0 0-555.776-216.384z"></path>
              </svg>
            </i>
          </button>
          <button
            class="el-button is-link close-button"
            title="关闭"
            @click="handleClose"
          >
            <i class="el-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                <path fill="currentColor" d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"></path>
              </svg>
            </i>
          </button>
        </div>
      </div>
    </template>
    
    <div class="dialog-content">
      <el-table
        :data="tableData"
        style="width: 100%"
        :size="large"
        v-loading="loading"
        border
        height="100%"
      >
        <el-table-column prop="taskId" label="任务ID" min-width="200" show-overflow-tooltip />
        <el-table-column label="TT ID" min-width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.ticketId === 0 || scope.row.ticketId === '0' ? 'null' : scope.row.ticketId }}
          </template>
        </el-table-column>
        <el-table-column label="任务来源" min-width="120" align="center">
          <template #default="scope">
            {{ getPlatformLabel(scope.row.platformId) }}
          </template>
        </el-table-column>
        <el-table-column prop="creatorMisId" label="创建人" min-width="120" />
        <el-table-column prop="createTime" label="创建时间" min-width="160" />
        <el-table-column prop="updateTime" label="更新时间" min-width="160" />
        <el-table-column label="任务状态" min-width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.taskStatus)">
              {{ getStatusLabel(scope.row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120" align="center">
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="handleViewDetail(scope.row)"
              :disabled="scope.row.taskStatus === 0"
              :class="{ 'is-active': currentSelectedRow && currentSelectedRow.taskId === scope.row.taskId }"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </el-dialog>
  
  <!-- 转换语料审核对话框 -->
  <ConvertCorpusDialog
    v-model="reviewDialogVisible"
    :form-data="reviewFormData"
    :current-mis-id="currentMisId"
    :current-emp-id="currentEmpId"
    :current-team="props.rgId"
    :task-missing-info="taskMissingInfo"
    :is-loading="isConvertSaving"
    :size="large"
    @update:form-data="reviewFormData = $event"
    @save="handleConvertSave"
    @compare="handleConvertCompare"
    @close="handleConvertClose"
    @open-tag-management="handleOpenTagManagement"
    ref="convertCorpusDialogRef"
  />
  
  <!-- 添加标签管理组件 -->
  <CustomWorkSpace
    v-if="customWorkSpaceVisible"
    :rg-id="props.rgId"
    :mis-id="currentMisId"
    :default-tab="customWorkSpaceDefaultTab"
    :visible="customWorkSpaceVisible"
    @close="handleCustomWorkSpaceClose"
    @tag-updated="handleTagUpdated"
  />
</template>

<style lang="scss">
/* 全局样式，不使用scoped，确保可以影响弹窗 */
.task-error-dialog {
  .el-message-box {
    padding: 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    max-width: 600px !important;
    width: 90% !important;
    z-index: 9999 !important;
  }

  .el-message-box__header {
    display: none !important;
  }
  
  .el-message-box__content {
    padding: 0 !important;
    margin: 0 !important;
    
    .task-error-content {
      .error-header {
        background: #F56C6C !important;
        padding: 24px !important;
        text-align: center !important;
        
        .error-title {
          display: flex !important;
          flex-direction: column !important;
          gap: 4px !important;
          
          .main-title {
            font-size: 20px !important;
            font-weight: 600 !important;
            color: #fff !important;
          }
          
          .sub-title {
            font-size: 14px !important;
            color: rgba(255, 255, 255, 0.8) !important;
          }
        }
      }
      
      .error-details {
        padding: 24px !important;
        
        .error-section {
          & + .error-section {
            margin-top: 24px !important;
            padding-top: 24px !important;
            border-top: 1px solid #EBEEF5 !important;
          }
          
          .section-header {
            display: flex !important;
            align-items: center !important;
            margin-bottom: 16px !important;
            color: #303133 !important;
            font-weight: 600 !important;
            font-size: 16px !important;
            border-left: 3px solid #F56C6C !important;
            padding-left: 10px !important;
          }
          
          .error-message {
            font-size: 14px !important;
            line-height: 1.6 !important;
            color: #606266 !important;
            background-color: #F5F7FA !important;
            padding: 16px !important;
            border-radius: 8px !important;
            margin-bottom: 16px !important;
            word-break: break-all !important;
            white-space: pre-wrap !important;
            border-left: 4px solid #F56C6C !important;
          }
          
          .info-grid {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)) !important;
            gap: 16px !important;
            
            .info-item {
              display: flex !important;
              align-items: center !important;
              padding: 8px !important;
              background-color: #F5F7FA !important;
              border-radius: 4px !important;
              font-size: 14px !important;
              
              .label {
                color: #909399 !important;
                margin-right: 8px !important;
                font-weight: normal !important;
              }
              
              .value {
                color: #303133 !important;
                font-weight: 500 !important;
                word-break: break-all !important;
              }
            }
          }
        }
      }
    }
  }
  
  .el-message-box__btns {
    padding: 16px 24px !important;
    border-top: 1px solid #EBEEF5 !important;
    text-align: right !important;
    
    .el-button {
      min-width: 100px !important;
      padding: 8px 20px !important;
      font-size: 14px !important;
      border-radius: 4px !important;
      
      &.el-button--primary {
        background-color: #F56C6C !important;
        border-color: #F56C6C !important;
        color: #fff !important;
        
        &:hover {
          background-color: #f78989 !important;
          border-color: #f78989 !important;
        }
        
        &:active {
          background-color: #dd6161 !important;
          border-color: #dd6161 !important;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.task-list-dialog {
  :deep(.el-dialog) {
    margin-top: 5vh !important;
    height: 90vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    padding: 20px;
    overflow: hidden;
  }
  
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .title {
        font-size: 16px;
        font-weight: 500;
      }
      
      .subtitle {
        font-size: 13px;
        color: #909399;
      }

      .filter-switch {
        margin-left: 16px;
      }
    }
    
    .dialog-header-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .refresh-button,
      .close-button {
        padding: 4px;
        height: auto;
        background: none;
        border: none;
        cursor: pointer;
        color: #909399;
        
        &:hover {
          color: #606266;
        }
        
        .el-icon {
          font-size: 20px;
          margin-right: 0;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          
          svg {
            width: 1em;
            height: 1em;
          }
        }
      }
    }
  }

  .dialog-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .el-table {
      flex: 1;
      overflow: hidden;
      
      :deep(.el-table__inner-wrapper) {
        height: 100%;
      }
      
      :deep(.el-table__body-wrapper) {
        overflow-y: auto;
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  :deep(.el-table) {
    .el-table__header {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
        height: 60px !important;
        line-height: 60px !important;
      }
    }
    
    .el-table__body {
      tr.el-table__row {
        height: 60px !important;
        font-size: 14px !important;
        
        > td.el-table__cell {
          height: 60px !important;
          padding: 8px 0 !important;
          font-size: 14px !important;
          
          .cell {
            font-size: 14px !important;
            line-height: 24px !important;
          }
        }
      }
    }
    
    .el-button {
      margin: 0 2px;
      font-size: 14px !important;
      
      .el-icon {
        margin-right: 4px;
      }
      
      // 添加激活状态的样式
      &.is-active {
        font-weight: bold;
        color: var(--el-color-primary);
        text-decoration: underline;
      }
      
      // 取消按钮焦点状态
      &:focus, &:focus-visible {
        outline: none !important;
        box-shadow: none !important;
      }
    }
  }
}

.review-dialog {
  :deep(.el-dialog) {
    margin-top: 5vh !important;
    height: 90vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .review-form {
    .form-section {
      margin-bottom: 20px;

      .section-header {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 12px;
      }

      .missing-info-list {
        background: #fff9f0;
        border-radius: 4px;
        padding: 16px;

        .missing-info-item {
          color: #e6a23c;
          line-height: 1.6;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
    padding-top: 20px;
  }
}

/* 防止按钮保留焦点状态的样式 */
.el-button:focus {
  outline: none !important;
  box-shadow: none !important;
}

.el-button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

// 任务错误对话框样式
:deep(.task-error-dialog) {
  .el-message-box__header {
    padding-bottom: 10px;
  }
  
  .el-message-box__title {
    display: none;
  }
  
  .el-message-box__content {
    padding: 0;
  }
  
  .task-error-content {
    .error-header {
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      
      .error-title {
        display: flex;
        flex-direction: column;
        
        .main-title {
          font-size: 18px;
          font-weight: 600;
          color: #f56c6c;
        }
        
        .sub-title {
          margin-top: 5px;
          font-size: 14px;
          color: #909399;
        }
      }
    }
    
    .error-details {
      padding: 20px;
      
      .error-section {
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .section-header {
          font-size: 15px;
          font-weight: 600;
          margin-bottom: 10px;
          color: #303133;
        }
        
        .error-message {
          padding: 12px;
          background-color: #fef0f0;
          border-radius: 4px;
          color: #f56c6c;
          font-size: 14px;
          line-height: 1.5;
          word-break: break-word;
        }
        
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
          
          .info-item {
            display: flex;
            
            .label {
              color: #606266;
              min-width: 80px;
            }
            
            .value {
              color: #303133;
              word-break: break-all;
            }
            
            &.info-item-full {
              grid-column: span 2;
            }
          }
        }
      }
    }
  }
}
</style> 