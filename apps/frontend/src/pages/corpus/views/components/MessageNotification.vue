<template>
  <div class="task-badge-wrapper">
    <slot></slot>
    <el-badge
      v-if="count > 0"
      :value="count"
      :max="99"
      class="task-badge"
    />
  </div>
  <div class="message-notification-container">
    <!-- 消息面板 -->
    <transition name="slide">
      <div v-show="!isCollapsed" class="message-panel">
        <div class="panel-header">
          <span class="panel-title">系统消息 ({{ messages.length }})</span>
          <div class="header-actions">
            <el-button 
              type="text" 
              class="hide-button"
              @click="toggleCollapse"
            >
              <el-icon><CloseBold /></el-icon>
              隐藏
            </el-button>
          </div>
        </div>
        <div class="message-list" v-if="messages.length > 0">
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="message-item"
            :class="{ unread: !message.read }"
          >
            <el-icon class="message-icon" :class="message.type">
              <component :is="getIconByType(message.type)" />
            </el-icon>
            <div class="message-content">
              <div class="message-title">{{ message.title }}</div>
              <div v-if="message.dangerouslyUseHTMLString" class="message-text" v-html="message.content"></div>
              <div v-else class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.time) }}</div>
            </div>
          </div>
        </div>
        <div v-else class="empty-message">
          暂无消息
        </div>
      </div>
    </transition>
    
    <!-- 铃铛按钮，使用 v-show 而不是 v-if -->
    <transition name="fade">
      <div v-show="isCollapsed" class="toggle-button" @click="toggleCollapse">
        <el-badge :value="unreadCount" :hidden="unreadCount === 0">
          <el-button circle :size="large">
            <el-icon><BellFilled /></el-icon>
          </el-button>
        </el-badge>
      </div>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.message-notification-container {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 9999;
}

.message-panel {
  width: 360px;
  max-height: 480px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  margin-bottom: 16px;
  overflow: hidden;
  position: relative;
  z-index: 9999;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #ebeef5;
    
    .panel-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
    
    .header-actions {
      .hide-button {
        color: #909399;
        font-size: 13px;
        
        &:hover {
          color: #409EFF;
        }
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  .message-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 8px 0;
  }
  
  .message-item {
    display: flex;
    padding: 12px 16px;
    transition: background-color 0.3s;
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &.unread {
      background-color: #f0f9ff;
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
    
    .message-icon {
      flex-shrink: 0;
      font-size: 16px;
      margin-right: 12px;
      margin-top: 2px;
      
      &.success { color: #67c23a; }
      &.warning { color: #e6a23c; }
      &.error { color: #f56c6c; }
      &.info { color: #909399; }
    }
    
    .message-content {
      flex: 1;
      min-width: 0;
      
      .message-title {
        font-size: 14px;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .message-text {
        font-size: 13px;
        color: #606266;
        margin-bottom: 4px;
      }
      
      .message-time {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .empty-message {
    padding: 32px 16px;
    text-align: center;
    color: #909399;
    font-size: 14px;
  }
}

/* 设置过渡效果 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease-out;
}

.slide-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 给按钮添加淡入淡出效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 固定按钮位置 */
.toggle-button {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 9999;
  
  .el-button {
    background-color: #409EFF;
    border-color: #409EFF;
    color: #fff;
    
    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
    
    &:focus {
      background-color: #409EFF;
      border-color: #409EFF;
    }
  }
}

// 徽标样式
.task-badge-wrapper {
  position: relative;
  display: inline-flex;
}

:deep(.task-badge) {
  position: absolute;
  top: -8px;
  right: -8px;
  transform-origin: 100% 0%;
  animation: badge-scale 0.3s ease-in-out;
  
  .el-badge__content {
    background-color: #ff4d4f;
    transition: all 0.3s;
  }
}

@keyframes badge-scale {
  0% {
    transform: scale(0);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import {
  BellFilled,
  CircleCheckFilled,
  WarningFilled,
  InfoFilled,
  CloseBold
} from '@element-plus/icons-vue'
import { fetchPendingTaskCount, REFRESH_INTERVAL } from '../../utils/taskCounter'
import dayjs from 'dayjs'

const props = defineProps<{
  rgId: number;
  misId: string;
}>()

interface Message {
  title: string;
  content: string;
  type: 'success' | 'warning' | 'info' | 'error';
  time: Date;
  read: boolean;
  dangerouslyUseHTMLString?: boolean;
}

const messages = ref<Message[]>([])
const isCollapsed = ref(true)
const count = ref(0)
let timer: NodeJS.Timer | null = null
let isFirstLoad = true

// 添加消息方法
const addMessage = (message: Omit<Message, 'time' | 'read'>) => {
  messages.value.unshift({
    ...message,
    time: new Date(),
    read: false
  })

  // 如果消息超过50条，删除旧消息
  if (messages.value.length > 50) {
    messages.value = messages.value.slice(0, 50)
  }
}

// 获取任务数量
const updateTaskCount = async () => {
  const newCount = await fetchPendingTaskCount({
    rgId: props.rgId,
    misId: props.misId
  })
  
  if (newCount !== -1) {
    count.value = newCount
  }
  
  if (isFirstLoad && newCount > 0) {
    isFirstLoad = false
    setTimeout(updateTaskCount, 1000)
  }
}

// 开始定时刷新
const startPolling = () => {
  updateTaskCount()
  timer = setInterval(updateTaskCount, REFRESH_INTERVAL)
}

// 停止定时刷新
const stopPolling = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 根据消息类型获取图标
const getIconByType = (type: string) => {
  switch (type) {
    case 'success':
      return CircleCheckFilled
    case 'warning':
      return WarningFilled
    case 'error':
      return CloseBold
    case 'info':
    default:
      return InfoFilled
  }
}

// 格式化时间
const formatTime = (time: Date) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 计算未读消息数量
const unreadCount = computed(() => {
  return messages.value.filter(msg => !msg.read).length
})

// 监听属性变化
watch(
  () => [props.rgId, props.misId],
  () => {
    if (props.rgId && props.misId) {
      isFirstLoad = true
      stopPolling()
      startPolling()
    } else {
      stopPolling()
    }
  },
  { immediate: true }
)

onMounted(() => {
  if (props.rgId && props.misId) {
    startPolling()
  }
})

onUnmounted(() => {
  stopPolling()
})

// 暴露方法给父组件
defineExpose({
  addMessage
})
</script> 