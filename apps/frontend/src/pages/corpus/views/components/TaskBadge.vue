<template>
  <div class="task-badge-wrapper">
    <slot></slot>
    <el-badge
      v-if="count > 0"
      :value="count"
      :max="99"
      class="task-badge"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { fetchPendingTaskCount, REFRESH_INTERVAL } from '../../utils/taskCounter'

const props = defineProps<{
  rgId: number;
  misId: string;
}>()

const count = ref(0)
let timer: NodeJS.Timer | null = null
let isFirstLoad = true

// 获取任务数量
const updateTaskCount = async () => {
  const newCount = await fetchPendingTaskCount({
    rgId: props.rgId,
    misId: props.misId
  })
  
  // 只有在不是节流状态(-1)时才更新计数
  if (newCount !== -1) {
    count.value = newCount
  }
  
  // 首次加载后，如果有任务，立即再次检查
  if (isFirstLoad && newCount > 0) {
    isFirstLoad = false
    setTimeout(updateTaskCount, 1000) // 1秒后再次检查
  }
}

// 开始定时刷新
const startPolling = () => {
  updateTaskCount() // 立即执行一次
  timer = setInterval(updateTaskCount, REFRESH_INTERVAL)
}

// 停止定时刷新
const stopPolling = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 监听属性变化
watch(
  () => [props.rgId, props.misId],
  () => {
    if (props.rgId && props.misId) {
      isFirstLoad = true // 重置首次加载标志
      stopPolling()
      startPolling()
    } else {
      stopPolling()
    }
  },
  { immediate: true }
)

onMounted(() => {
  if (props.rgId && props.misId) {
    startPolling()
  }
})

onUnmounted(() => {
  stopPolling()
})
</script>

<style lang="scss" scoped>
.task-badge-wrapper {
  position: relative;
  display: inline-flex;
}

:deep(.task-badge) {
  position: absolute;
  top: -8px;
  right: -8px;
  transform-origin: 100% 0%;
  animation: badge-scale 0.3s ease-in-out;
  
  .el-badge__content {
    background-color: #ff4d4f;
    transition: all 0.3s;
  }
}

@keyframes badge-scale {
  0% {
    transform: scale(0);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style> 