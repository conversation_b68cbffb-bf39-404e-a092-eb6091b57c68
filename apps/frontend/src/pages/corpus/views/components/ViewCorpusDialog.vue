<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, onMounted } from 'vue'
import { Document, Reading, List, InfoFilled, Connection, User, Clock, Refresh, Collection, QuestionFilled } from '@element-plus/icons-vue'
import MonacoEditor from './MonacoEditor.vue'

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  corpusData: {
    type: Object,
    default: () => ({
      ticketId: '',
      title: '',
      content: '',
      type: '',
      source: '',
      misId: '',
      updateTime: '',
      createTime: '',
      backgroundKnowledge: '',
      sop: '',
      rule: '',
      tagsname: []
    })
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue'])

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  }
})

// 编辑器实例引用
const viewEditor = ref(null)

// 强制组件刷新的key
const editorKey = ref(0)

// 折叠面板状态管理
const activeCollapseNames = ref([])
const sopCollapseNames = ref([])
const backgroundCollapseNames = ref([])
const ruleCollapseNames = ref([])

// 监听corpusData变化，更新编辑器
watch([() => props.corpusData, () => props.modelValue], ([newCorpusData, newVisible]) => {
  if (newVisible) {
    // 重置所有折叠面板状态为关闭
    activeCollapseNames.value = []
    sopCollapseNames.value = []
    backgroundCollapseNames.value = []
    ruleCollapseNames.value = []
    // 在下一个tick强制刷新编辑器
    setTimeout(() => {
      editorKey.value += 1
    }, 200)
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  if (props.modelValue && props.corpusData.content) {
    // 强制刷新编辑器
    setTimeout(() => {
      editorKey.value += 1
    }, 200)
  }
})

// 处理关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<template>
  <!-- 查看语料详情对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="查看语料"
    width="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="view-corpus-dialog"
  >
    <div class="corpus-content">
      <el-form label-width="100px" class="corpus-form">
        <div class="metadata-section">
          <div class="metadata-grid">
            <el-form-item label="问题来源" class="metadata-item">
              <el-input v-model="corpusData.source" readonly placeholder="暂无" />
            </el-form-item>
            
            <el-form-item label="创建人" class="metadata-item">
              <el-input v-model="corpusData.misId" readonly placeholder="暂无" />
            </el-form-item>
            
            <el-form-item label="创建时间" class="metadata-item">
              <el-input v-model="corpusData.createTime" readonly placeholder="暂无" />
            </el-form-item>
            
            <el-form-item label="最后更新时间" class="metadata-item">
              <el-input v-model="corpusData.updateTime" readonly placeholder="暂无" />
            </el-form-item>
          </div>
          
          <!-- 标签单独成行 -->
          <div class="tags-row">
            <el-form-item label="标签" class="metadata-item">
              <div class="tags-container">
                <template v-if="corpusData.tagsname && Array.isArray(corpusData.tagsname) && corpusData.tagsname.length > 0">
                  <el-tag
                    v-for="tag in corpusData.tagsname"
                    :key="tag"
                    class="corpus-tag"
                    type="info"
                    size="small"
                  >
                    {{ tag }}
                  </el-tag>
                </template>
                <span v-else class="no-tags">暂无标签</span>
              </div>
            </el-form-item>
          </div>
        </div>
        
        <el-form-item label="问题标题" class="title-item">
          <div class="form-value title-value">{{ corpusData.title || '暂无标题' }}</div>
        </el-form-item>
        
        <el-form-item label="语料内容" class="content-item">
          <div class="editor-wrapper">
            <MonacoEditor
              :key="editorKey"
              ref="viewEditor"
              v-model="corpusData.content"
              language="markdown"
              height="320px"
              :options="{
                wordWrap: 'on',
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                lineNumbers: 'on',
                readOnly: true,
                lineDecorationsWidth: 0,
                folding: true,
                renderLineHighlight: 'all',
                automaticLayout: true,
                value: corpusData.content
              }"
              :showResetButton="false"
              :defaultPreviewMode="true"
              class="view-corpus-editor"
            />
          </div>
        </el-form-item>
        
        <!-- 关联资源折叠面板 -->
        <div class="resources-section">
          <el-collapse v-model="activeCollapseNames" class="main-resource-collapse">
            <el-collapse-item name="resources">
              <template #title>
                <div class="main-collapse-title">
                  <span class="main-resource-name">语料生成参考资源</span>
                  <el-tooltip 
                    placement="top"
                    effect="dark"
                    :show-after="300"
                  >
                    <template #content>
                      <div>显示生成此语料时参考的SOP、背景知识和业务规则等资源内容。</div>
                      <div>您可以在首页的设置面板中对这些资源进行配置和管理。</div>
                      <div style="color: #f56c6c; margin-top: 8px;">注意：仅展示功能上线后生成的语料信息，存量数据暂不支持查看。</div>
                    </template>
                    <el-icon class="help-icon">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="main-collapse-content">
                <!-- SOP -->
                <el-collapse v-model="sopCollapseNames" class="resource-collapse">
                  <el-collapse-item name="sop">
                    <template #title>
                      <div class="collapse-title">
                        <span class="resource-name">运用的SOP</span>
                      </div>
                    </template>
                    <div class="collapse-content">
                      <MonacoEditor
                        :key="`sop-${editorKey}`"
                        :model-value="corpusData.sop || ''"
                        language="markdown"
                        height="300px"
                        :options="{
                          wordWrap: 'on',
                          minimap: { enabled: false },
                          scrollBeyondLastLine: false,
                          lineNumbers: 'on',
                          readOnly: true,
                          lineDecorationsWidth: 0,
                          folding: true,
                          renderLineHighlight: 'all',
                          automaticLayout: true
                        }"
                        :showResetButton="false"
                        class="resource-editor"
                      />
                    </div>
                  </el-collapse-item>
                </el-collapse>
                
                <!-- 背景知识 -->
                <el-collapse v-model="backgroundCollapseNames" class="resource-collapse">
                  <el-collapse-item name="background">
                    <template #title>
                      <div class="collapse-title">
                        <span class="resource-name">运用的背景知识</span>
                      </div>
                    </template>
                    <div class="collapse-content">
                      <MonacoEditor
                        :key="`bg-${editorKey}`"
                        :model-value="corpusData.backgroundKnowledge || ''"
                        language="markdown"
                        height="300px"
                        :options="{
                          wordWrap: 'on',
                          minimap: { enabled: false },
                          scrollBeyondLastLine: false,
                          lineNumbers: 'on',
                          readOnly: true,
                          lineDecorationsWidth: 0,
                          folding: true,
                          renderLineHighlight: 'all',
                          automaticLayout: true
                        }"
                        :showResetButton="false"
                        class="resource-editor"
                      />
                    </div>
                  </el-collapse-item>
                </el-collapse>
                
                <!-- 规则 -->
                <el-collapse v-model="ruleCollapseNames" class="resource-collapse">
                  <el-collapse-item name="rule">
                    <template #title>
                      <div class="collapse-title">
                        <span class="resource-name">运用的规则</span>
                      </div>
                    </template>
                    <div class="collapse-content">
                      <MonacoEditor
                        :key="`rule-${editorKey}`"
                        :model-value="corpusData.rule || ''"
                        language="markdown"
                        height="300px"
                        :options="{
                          wordWrap: 'on',
                          minimap: { enabled: false },
                          scrollBeyondLastLine: false,
                          lineNumbers: 'on',
                          readOnly: true,
                          lineDecorationsWidth: 0,
                          folding: true,
                          renderLineHighlight: 'all',
                          automaticLayout: true
                        }"
                        :showResetButton="false"
                        class="resource-editor"
                      />
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" size="large" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.view-corpus-dialog {
  :deep(.el-dialog) {
    margin-top: 3vh !important;
    min-height: 60vh;
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08);
  }

  :deep(.el-dialog__header) {
    padding: 24px 32px 20px;
    margin-right: 0;
    border-bottom: 1px solid #e4e7ed;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
    
    .el-dialog__title {
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }
    
    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #606266;
        font-size: 18px;
        
        &:hover {
          color: #409eff;
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    flex: 1;
    padding: 24px 20px;
    overflow-y: auto;
    background-color: #fafbfc;
  }

  :deep(.el-dialog__footer) {
    padding: 20px 32px 24px;
    border-top: 1px solid #f0f2f5;
    background-color: #fff;
    border-radius: 0 0 12px 12px;
  }
}

.corpus-content {
  .corpus-form {
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    
    :deep(.el-form-item) {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      
      .el-form-item__label {
        padding-right: 8px !important;
      }
    }
    
    .metadata-section {
      margin-bottom: 24px;
      background-color: #fff;
      border-radius: 12px;
      padding: 20px 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      border: 1px solid #e4e7ed;
      
      .metadata-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        margin-bottom: 16px;
        
        .metadata-item {
          :deep(.el-form-item__label) {
            color: #606266;
            font-weight: 600;
            font-size: 14px;
            min-width: 80px;
            white-space: nowrap;
            margin-right: 8px;
          }
          
          :deep(.el-form-item__content) {
            flex: 1;
            margin-left: 0 !important;
          }
          
          :deep(.el-input) {
            width: 100%;
            
            .el-input__wrapper {
              background-color: #ffffff;
              border: 1px solid #e1e8ed;
              border-radius: 6px;
              box-shadow: none;
              padding: 1px 11px;
              
              &:hover {
                border-color: #c0c4cc;
              }
              
              &.is-focus {
                border-color: #409eff;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
              }
            }
            
            .el-input__inner {
              background-color: transparent;
              border: none;
              color: #606266;
              font-size: 14px;
              font-weight: 500;
              padding: 0;
              
              &:focus {
                border: none;
                box-shadow: none;
              }
            }
          }
        }
      }
      
      .tags-row {
        .metadata-item {
          :deep(.el-form-item__label) {
            color: #606266;
            font-weight: 600;
            font-size: 14px;
            min-width: 80px;
            white-space: nowrap;
            margin-right: 8px;
          }
          
          :deep(.el-form-item__content) {
            flex: 1;
            margin-left: 0 !important;
          }
          
          .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            align-items: flex-start;
            min-height: 24px;
            padding: 4px 12px;
            background-color: #ffffff;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            width: 100%;
            
            .corpus-tag {
              margin: 0;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;
              padding: 3px 6px;
              background-color: #e6f7ff;
              border-color: #91d5ff;
              color: #1890ff;
              height: 22px;
              line-height: 16px;
              display: inline-flex;
              align-items: center;
              
              &:hover {
                background-color: #bae7ff;
                border-color: #69c0ff;
              }
            }
            
            .no-tags {
              color: #909399;
              font-size: 14px;
              font-style: italic;
              line-height: 16px;
            }
          }
        }
      }
    }
    
    .title-item {
      margin-bottom: 16px;
      
      :deep(.el-form-item__label) {
        font-weight: 600;
        color: #2c3e50;
        font-size: 15px;
        width: 100px !important;
        flex-shrink: 0;
        display: flex;
        align-items: flex-start;
        padding-top: 6px;
        line-height: 1.4;
      }
      
      :deep(.el-form-item__content) {
        flex: 1;
        margin-left: 0 !important;
      }
      
      .form-value.title-value {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        padding: 6px 12px;
        background-color: #fafbfc;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        width: 100%;
        word-wrap: break-word;
        white-space: normal;
        line-height: 1.4;
        display: flex;
        align-items: center;
        min-height: 20px;
      }
    }
    
    .content-item {
      margin-bottom: 20px;
      
      :deep(.el-form-item__label) {
        font-weight: 600;
        color: #2c3e50;
        font-size: 15px;
        width: 100px !important;
        flex-shrink: 0;
      }
      
      :deep(.el-form-item__content) {
        flex: 1;
        margin-left: 0 !important;
      }
      
      .editor-wrapper {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: none;
        width: 100%;
        
        .view-corpus-editor {
          border: 1px solid #e1e8ed;
          border-radius: 8px;
          overflow: hidden;
          min-height: 320px;
          width: 100%;
          
          :deep(.monaco-editor-wrapper) {
            display: block !important;
            height: 320px !important;
            width: 100% !important;
            
            .monaco-editor-container {
              width: 100% !important;
              height: calc(100% - 40px) !important;
              min-height: 280px !important;
              display: block !important;
              overflow: visible !important;
            }
          }
        }
      }
    }
    
    .resources-section {
      margin-top: 0;
      padding-top: 0;
      border-top: none;
      
      .main-resource-collapse {
        margin-bottom: 16px;
        border: none;
        
        :deep(.el-collapse) {
          border: none;
        }
        
        :deep(.el-collapse-item__header) {
          background: #f5f7fa;
          border: none;
          border-radius: 12px;
          padding: 18px 24px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          transition: all 0.3s ease;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
          
          &:hover {
            background: #e9ecef;
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          }
          
          .el-collapse-item__arrow {
            color: #606266;
            font-weight: bold;
            font-size: 16px;
          }
        }
        
        :deep(.el-collapse-item__content) {
          padding: 0;
          border: none;
          background-color: transparent;
        }
        
        :deep(.el-collapse-item__wrap) {
          border: none;
          background-color: transparent;
        }
        
        .main-collapse-title {
          display: flex;
          align-items: center;
          color: #303133;
          
          .main-resource-name {
            font-weight: 600;
            flex: 1;
            font-size: 16px;
          }
          
          .help-icon {
            margin-left: 8px;
            font-size: 16px;
            color: #909399;
            cursor: pointer;
            transition: color 0.3s ease;
            
            &:hover {
              color: #409eff;
            }
          }
        }
        
        .main-collapse-content {
          padding: 20px;
          background-color: #fafbfc;
          border: 1px solid #e1e8ed;
          border-top: none;
          border-radius: 0 0 12px 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }
      }
      
      .resource-collapse {
          margin-bottom: 12px;
        
        :deep(.el-collapse-item__header) {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border: 1px solid #dcdfe6;
          border-radius: 10px;
          padding: 16px 20px;
          font-size: 15px;
          font-weight: 600;
          transition: all 0.3s ease;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
          
          &:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          }
          
          .el-collapse-item__arrow {
            color: #606266;
            font-weight: bold;
            font-size: 14px;
          }
        }
        
        :deep(.el-collapse-item__content) {
          padding: 0;
          border: none;
        }
        
        :deep(.el-collapse-item__wrap) {
          border: none;
          background-color: transparent;
        }
        
        .collapse-title {
          display: flex;
          align-items: center;
          color: #303133;
          
          .resource-name {
            font-weight: 600;
            flex: 1;
          }
        }
        
        .collapse-content {
          padding: 20px;
          background-color: #fff;
          border: 1px solid #e1e8ed;
          border-top: none;
          border-radius: 0 0 10px 10px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
          
          .resource-editor {
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
            min-height: 300px;
            
            :deep(.monaco-editor-wrapper) {
              display: block !important;
              height: 300px !important;
              width: 100% !important;
              
              .monaco-editor-container {
                width: 100% !important;
                height: calc(100% - 40px) !important;
                min-height: 260px !important;
                display: block !important;
                overflow: visible !important;
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: center;
  
  .el-button {
    padding: 12px 32px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 8px;
    background-color: #409eff;
    border: 1px solid #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
    
    &:active {
      background-color: #3a8ee6;
      border-color: #3a8ee6;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .view-corpus-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto !important;
    }
    
    :deep(.el-dialog__body) {
      padding: 20px;
    }
  }
  
  .corpus-content .corpus-form {
    padding: 16px;
    
    .metadata-section .metadata-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }
}
</style> 