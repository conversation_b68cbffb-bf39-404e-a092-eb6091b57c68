<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue'

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      createdAtStart: null,
      createdAtEnd: null,
      isFiltered: true  // 默认改为筛选后
    })
  },
  autoInit: {
    type: Boolean,
    default: false // 默认不自动初始化筛选条件
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'filter-change'])

// 本地过滤器数据
const localFilter = ref({
  dateRange: null as [Date, Date] | null,
  isFiltered: true  // 默认改为筛选后
})

// 获取默认的日期范围（7天）
const getDefaultDateRange = () => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)
  return [startDate, endDate]
}

// 初始化组件
onMounted(() => {
  // 设置默认时间：开始时间为7天前，结束时间为当前时间
  const [startDate, endDate] = getDefaultDateRange()
  
  localFilter.value.dateRange = [startDate, endDate]
  localFilter.value.isFiltered = props.modelValue.isFiltered !== undefined ? props.modelValue.isFiltered : true  // 使用props传入的值，默认为true
  
  // 初始化默认值，但不触发事件
  const filterData = {
    createdAtStart: startDate.getTime(),  // 使用毫秒时间戳
    createdAtEnd: endDate.getTime() + 86399999, // 结束时间设为当天的最后一毫秒 (23:59:59.999)
    isFiltered: localFilter.value.isFiltered
  }
  
  // 仅更新父组件的 modelValue，不触发额外的请求
  emit('update:modelValue', filterData)
  
  // 只有在 autoInit 为 true 时才触发筛选变化事件
  if (props.autoInit) {
    emit('filter-change', filterData)
  }
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && (newVal.createdAtStart || newVal.createdAtEnd)) {
    // 如果父组件传递了时间范围，更新本地日期范围
    if (newVal.createdAtStart && newVal.createdAtEnd) {
      localFilter.value.dateRange = [
        new Date(newVal.createdAtStart),
        new Date(newVal.createdAtEnd)
      ]
    }
  }
  
  if (newVal && newVal.isFiltered !== undefined) {
    localFilter.value.isFiltered = newVal.isFiltered
  }
}, { deep: true })

// 处理筛选条件变化
const handleFilterChange = () => {
  // 如果用户清除了日期范围，使用默认值
  let dateRange = localFilter.value.dateRange
  if (!dateRange) {
    dateRange = getDefaultDateRange()
  }
  
  // 获取开始日期和结束日期
  const startDate = new Date(dateRange[0])
  
  // 设置开始时间为当天的 00:00:00.000
  startDate.setHours(0, 0, 0, 0)
  
  // 获取结束日期，并设置为当天的 23:59:59.999
  const endDate = new Date(dateRange[1])
  endDate.setHours(23, 59, 59, 999)
  
  const filterData = {
    createdAtStart: startDate.getTime(),  // 毫秒时间戳
    createdAtEnd: endDate.getTime(),      // 毫秒时间戳
    isFiltered: localFilter.value.isFiltered
  }
  
  console.log('筛选条件变化:', {
    startDate: new Date(filterData.createdAtStart).toLocaleString(),
    endDate: new Date(filterData.createdAtEnd).toLocaleString(),
    startTimestamp: filterData.createdAtStart,
    endTimestamp: filterData.createdAtEnd,
    isFiltered: filterData.isFiltered
  })
  
  // 更新 v-model 值
  emit('update:modelValue', filterData)
  
  // 触发筛选变化事件
  emit('filter-change', filterData)
}

// 时间选择器快捷选项
const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    }
  }
]
</script>

<template>
  <div class="tt-filter-panel">
    <div class="filter-section date-filter-container">
      <div class="control-wrapper">
        <span class="control-label">创建时间</span>
        <el-date-picker
          v-model="localFilter.dateRange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="shortcuts"
          @change="handleFilterChange"
          style="width: 320px"
          class="custom-date-picker"
        />
      </div>
    </div>
    
    <div class="filter-section filter-switch-container">
      <div class="control-wrapper">
        <el-tooltip
          effect="light"
          content="筛选条件：TT状态为已解决/已关闭 or 未创建大象群"
          placement="top"
          :enterable="false"
        >
          <div class="switch-container">
            <span class="switch-label">仅查看可转换</span>
            <el-switch
              v-model="localFilter.isFiltered"
              :active-value="true"
              :inactive-value="false"
              @change="handleFilterChange"
              class="custom-switch"
              inactive-color="#DCDFE6"
              active-color="#409EFF"
            />
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tt-filter-panel {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
  
  .filter-section {
    display: flex;
    align-items: center;
  }
  
  .control-wrapper {
    display: flex;
    align-items: center;
    
    .control-label {
      font-size: 14px;
      color: #606266;
      margin-right: 12px;
      font-weight: 500;
      white-space: nowrap;
    }
  }
  
  .date-filter-container {
    :deep(.el-date-editor) {
      &.custom-date-picker {
        .el-range-input {
          font-size: 13px;
        }
        
        .el-range-separator {
          color: #909399;
        }
        
        .el-range__icon {
          color: #409EFF;
        }
      }
    }
  }
  
  .filter-switch-container {
    .switch-container {
      display: flex;
      align-items: center;
      cursor: help;
    }
    
    .switch-label {
      font-size: 14px;
      color: #606266;
      margin-right: 8px;
      font-weight: 500;
    }
    
    :deep(.el-switch) {
      &.custom-switch {
        .el-switch__core {
          border-radius: 11px;
        }
      }
    }
  }
}
</style> 