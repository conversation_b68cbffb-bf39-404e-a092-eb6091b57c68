<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
// 导入SOP配置组件
import SOPConfig from './customWorkSpace/SOPConfig.vue'
import WorkspaceSettings from './customWorkSpace/WorkspaceSettings.vue'
import BackgroundKnowledgeConfig from './customWorkSpace/BackgroundKnowledgeConfig.vue'
import RuleConfig from './customWorkSpace/RuleConfig.vue'
import AccessKeyConfig from './customWorkSpace/AccessKeyConfig.vue'
import TagManagement from './customWorkSpace/TagManagement.vue'
import { QuestionFilled, Close, Key } from '@element-plus/icons-vue'
import { isDev, isTest } from '@/shared/utils/env-helper'

// 定义emit
const emit = defineEmits(['close', 'tag-updated'])

// 定义props来接收父组件传递的rgId和misId
const props = defineProps({
  rgId: {
    type: Number,
    required: true
  },
  misId: {
    type: String,
    required: true
  },
  defaultTab: {
    type: String,
    default: 'sop'  // 默认是SOP模板管理
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// 当前选中的模块
const activeModule = ref(props.defaultTab)

// 添加对SOPConfig组件的引用
const sopConfigRef = ref()
const workspaceSettingsRef = ref()
const backgroundKnowledgeConfigRef = ref()
const ruleConfigRef = ref()
const accessKeyConfigRef = ref()
const tagManagementRef = ref()

// 菜单项列表
const menuItems = [
  { id: 'sop', name: 'SOP模板管理', icon: 'Document' },
  { id: 'backgroundKnowledge', name: '自定义背景知识', icon: 'Reading' },
  { id: 'customRule', name: '自定义规则', icon: 'EditPen' },
  { id: 'tagManagement', name: '分类标签管理', icon: 'PriceTag' },
  { id: 'accessKey', name: 'AccessKey管理', icon: 'Key', width: '180px' },
  { id: 'settings', name: '工作空间设置', icon: 'Setting' }
]

// 确保默认选中第一个选项
const setDefaultModule = () => {
  // 如果传入了默认标签并且该标签存在于菜单中，则选中它
  if (props.defaultTab && menuItems.some(item => item.id === props.defaultTab)) {
    activeModule.value = props.defaultTab
  } 
  // 否则选择第一个菜单项
  else if (menuItems.length > 0) {
    activeModule.value = menuItems[0].id
  }
}

// 监听props变化
watch(
  () => [props.rgId, props.misId, props.defaultTab],
  ([newRgId, newMisId, newDefaultTab]) => {
    // 当defaultTab变化时更新当前活动模块
    if (typeof newDefaultTab === 'string' && menuItems.some(item => item.id === newDefaultTab)) {
      activeModule.value = newDefaultTab
    }
    
    // 加载数据
    loadData()
  }
)

// 组件挂载时设置默认选中项并加载数据
onMounted(() => {
  setDefaultModule()
  loadData()
})

// 加载数据的方法
const loadData = () => {
  // 这里可以使用props.rgId和props.misId来请求数据
  // 未来可以添加实际的数据加载逻辑
}

// 暴露fetchData方法，供父组件调用
const fetchData = () => {
  if (activeModule.value === 'sop' && sopConfigRef.value) {
    sopConfigRef.value.fetchLatestSop()
  } else if (activeModule.value === 'backgroundKnowledge' && backgroundKnowledgeConfigRef.value) {
    backgroundKnowledgeConfigRef.value.fetchLatestBackgroundKnowledge()
  } else if (activeModule.value === 'customRule' && ruleConfigRef.value) {
    ruleConfigRef.value.fetchLatestRule()
  } else if (activeModule.value === 'tagManagement' && tagManagementRef.value) {
    tagManagementRef.value.fetchTagList()
  } else if (activeModule.value === 'accessKey' && accessKeyConfigRef.value) {
    accessKeyConfigRef.value.fetchLatestAccessKey()
  } else if (activeModule.value === 'settings' && workspaceSettingsRef.value) {
    workspaceSettingsRef.value.fetchWorkspaces()
  }
}

// 处理关闭事件
const handleClose = () => {
  // 如果当前是标签管理模块，则先触发标签更新事件
  if (activeModule.value === 'tagManagement') {
    emit('tag-updated')
  }
  emit('close')
}

// 处理问号图标点击
const handleHelpClick = () => {
  // 根据环境判断跳转的URL
  const url = isDev() || isTest() 
    ? 'https://aispeech.ai.test.sankuai.com/console/requirement/list'
    : 'https://speech.sankuai.com/console/requirement/list'
  
  // 在新窗口打开链接
  window.open(url, '_blank')
}

// 暴露方法供父组件调用
defineExpose({
  fetchData
})
</script>

<template>
  <div class="custom-workspace">
    <div class="workspace-container">
      <!-- 左侧菜单 -->
      <div class="workspace-sidebar">
        <ul class="sidebar-menu">
          <li 
            v-for="item in menuItems" 
            :key="item.id"
            :class="{ active: activeModule === item.id }"
            @click="activeModule = item.id"
            :style="item.width ? { width: item.width } : {}"
          >
            <el-icon><component :is="item.icon" /></el-icon>
            <span>{{ item.name }}</span>
          </li>
        </ul>
      </div>
      
      <!-- 右侧内容区 -->
      <div class="workspace-content">
        <div class="content-header">
          <div class="header-decor"></div>
          <h2 class="main-title-row">
            <span>{{ menuItems.find(item => item.id === activeModule)?.name }}</span>
            <template v-if="activeModule === 'accessKey'">
              <el-tooltip placement="right" effect="light">
                <template #content>
                  <div style="max-width: 400px; white-space: pre-line;">
                    <b>AccessKey管理说明：</b>
                    <br/><br/>
                    <b>1. 默认AccessKey</b>
                    <br/>系统会自动为您生成一个默认的AccessKey
                    <br/><br/>
                    <b>2. 自定义AccessKey</b>
                    <br/>您可以根据需要自行新增AccessKey，并配置于不同的使用场景
                    <br/><br/>
                    <b>3. 数据访问</b>
                    <br/>通过AccessKey可以访问我们的数据文件
                    <br/><br/>
                    <b>4. 独立统计</b>
                    <br/>不同AccessKey的访问数据次数会被独立统计，方便您进行权限和使用情况的管理
                  </div>
                </template>
                <el-icon class="help-icon" style="margin-left: 8px; cursor: pointer; color: #909399;">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template v-else-if="activeModule === 'backgroundKnowledge'">
              <el-tooltip placement="right" effect="light">
                <template #content>
                  <div style="max-width: 350px; white-space: pre-line;">
                    <b>自定义背景知识</b>用于补充与当前问题相关的背景信息，帮助分析和定位问题。<br/>
                    <br/>
                    <b>填写建议：</b>
                    <br/>1. 相关领域/技术的基本概念
                    <br/>2. 问题发生的环境/条件
                    <br/>3. 已尝试过的解决方案
                    <br/>4. 任何其他有助于分析问题的信息
                    <br/>
                    <br/>
                    <b>示例：</b>
                    <br/>1. 这是一个关于分布式系统的问题，涉及微服务架构
                    <br/>2. 系统使用Kubernetes部署，版本为1.24
                    <br/>3. 已经尝试过重启pod和增加资源配额
                    <br/>4. 错误日志中显示超时错误，主要发生在高峰时段
                  </div>
                </template>
                <el-icon class="help-icon" style="margin-left: 8px; cursor: pointer; color: #909399;">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template v-else-if="activeModule === 'customRule'">
              <el-tooltip placement="right" effect="light">
                <template #content>
                  <div style="max-width: 350px; white-space: pre-line;">
                    <b>自定义规则</b>用于补充对输出的特殊要求，帮助系统更好地按照实际需求进行格式化、用词、内容等方面的定制，确保输出结果更贴合业务规范和操作习惯。
                    <br/><b>填写建议：</b>
                    <br/>1. 对输出格式的特殊要求（如分条列出、每步编号等）
                    <br/>2. 对内容完整性或重点的要求（如必须包含风险提示、注意事项等）
                    <br/>3. 对用词或表达方式的规范（如禁止英文缩写、统一术语等）
                    <br/>4. 对输出内容的限制（如不得包含敏感信息等）
                    <br/>5. 任何其他你希望遵循的定制化要求
                    <br/><br/><b>示例：</b>
                    <br/>SOP输出时每一步操作都要有序号，例如1.、2.、3.，并且每一步后都要有简短说明。
                    <br/>在SOP结尾处必须增加一句"操作前请做好数据备份"作为风险提示。
                    <br/>SOP正文只允许使用中文术语，不允许用英文缩写。
                    <br/>输出内容中不得出现公司员工姓名、手机号等敏感信息。
                  </div>
                </template>
                <el-icon class="help-icon" style="margin-left: 8px; cursor: pointer; color: #909399;">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template v-else-if="activeModule === 'tagManagement'">
              <el-tooltip placement="right" effect="light">
                <template #content>
                  <div style="max-width: 350px; white-space: pre-line;">
                    <b>分类标签管理功能说明：</b>
                    <br/><br/>
                    可以创建和管理语料的分类标签，用于对语料进行分类和筛选。支持新增、编辑、删除标签，帮助您更好地组织和管理语料数据。
                    <br/><br/>
                    <b>主要功能：</b>
                    <br/>1. 新增标签：创建自定义分类标签
                    <br/>2. 编辑标签：修改标签描述信息
                    <br/>3. 删除标签：移除不需要的标签
                    <br/>4. 标签筛选：在语料列表中按标签筛选
                    <br/><br/>
                    <b>使用场景：</b>
                    <br/>• 按业务领域分类（如医美、电脑、线上等）
                    <br/>• 按问题类型分类（如技术问题、流程问题等）
                    <br/>• 按优先级分类（如高优先级、普通等）
                  </div>
                </template>
                <el-icon class="help-icon" style="margin-left: 8px; cursor: pointer; color: #909399;">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </template>
          </h2>
          <el-button class="close-btn" link circle @click="handleClose">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        
        <!-- SOP模板管理内容区域 -->
        <div v-if="activeModule === 'sop'" class="module-container">
          <SOPConfig 
            :rg-id="rgId" 
            :mis-id="misId" 
            ref="sopConfigRef"
            @close="handleClose"
          />
        </div>
        <!-- 自定义背景知识内容区域 -->
        <div v-if="activeModule === 'backgroundKnowledge'" class="module-container">
          <BackgroundKnowledgeConfig
            :rg-id="rgId"
            :mis-id="misId"
            ref="backgroundKnowledgeConfigRef"
            @close="handleClose"
          />
        </div>
        <!-- 自定义规则内容区域 -->
        <div v-if="activeModule === 'customRule'" class="module-container">
          <RuleConfig
            :rg-id="rgId"
            :mis-id="misId"
            ref="ruleConfigRef"
            @close="handleClose"
          />
        </div>
        <!-- AccessKey管理内容区域 -->
        <div v-if="activeModule === 'accessKey'" class="module-container">
          <AccessKeyConfig
            :rg-id="rgId"
            :mis-id="misId"
            ref="accessKeyConfigRef"
          />
        </div>
        <!-- 分类标签管理内容区域 -->
        <div v-if="activeModule === 'tagManagement'" class="module-container">
          <TagManagement
            :rg-id="rgId"
            :mis-id="misId"
            ref="tagManagementRef"
            @tag-updated="$emit('tag-updated')"
          />
        </div>
        <!-- 工作空间设置内容区域 -->
        <div v-if="activeModule === 'settings'" class="module-container">
          <WorkspaceSettings 
            :rg-id="rgId" 
            :mis-id="misId"
            ref="workspaceSettingsRef"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.custom-workspace {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 2500;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0,0,0,0.08);
  .workspace-container {
    box-shadow: 0 12px 48px 0 rgba(0,0,0,0.32), 0 1.5px 6px rgba(0,0,0,0.10);
    border-radius: 16px;
    background: #fff;
    min-width: 1000px;
    width: 1000px;
    max-width: 1000px;
    min-height: 600px;
    display: flex;
    .workspace-sidebar {
      width: 240px;
      background: linear-gradient(180deg, #f7faff 0%, #f0f2f5 100%);
      color: #222;
      display: flex;
      flex-direction: column;
      border-right: 1px solid #e4e7ed;
      border-top-left-radius: 16px;
      border-bottom-left-radius: 16px;
      box-shadow: 2px 0 8px 0 rgba(64,158,255,0.03);
      z-index: 1;
      .sidebar-menu {
        flex: 1;
        padding: 8px 0 0 0;
        margin: 0;
        list-style: none;
        li {
          padding: 14px 20px;
          margin: 8px 12px;
          border-radius: 10px;
          cursor: pointer;
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          background: transparent;
          transition: background 0.2s, color 0.2s;
          white-space: nowrap;
          &:hover {
            background: #e6f0ff;
            color: #409eff;
          }
          &.active {
            background: linear-gradient(90deg, #e6f0ff 0%, #f7faff 100%);
            color: #409eff;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(64,158,255,0.06);
          }
          .el-icon {
            margin-right: 12px;
            font-size: 18px;
            color: inherit;
          }
          span {
            font-size: 16px;
            line-height: 1.6;
          }
        }
      }
    }
    .workspace-content {
      flex: 1;
      background: #f7faff;
      display: flex;
      flex-direction: column;
      border-top-right-radius: 16px;
      border-bottom-right-radius: 16px;
      .content-header {
        display: flex;
        align-items: center;
        padding: 32px 32px 16px 32px;
        background: #f7faff;
        border-bottom: 1px solid #f0f2f5;
        min-height: 56px;
        position: relative;
        .header-decor {
          width: 4px;
          height: 28px;
          background: #409EFF;
          border-radius: 2px;
          margin-right: 16px;
        }
        .main-title-row {
          display: flex;
          align-items: center;
          font-size: 20px;
          font-weight: 600;
          .help-icon {
            font-size: 18px;
            margin-left: 8px;
            cursor: pointer;
            color: #909399;
            &:hover {
              color: #409EFF;
            }
          }
        }
        h2 {
          font-size: 22px;
          font-weight: 700;
          color: #222;
          margin: 0;
          letter-spacing: 0.5px;
        }
        .close-btn {
          position: absolute;
          top: 24px;
          right: 32px;
          z-index: 2;
          font-size: 20px;
          color: #909399;
          &:hover {
            color: #409EFF;
            background: #f2f6fc;
          }
        }
      }
      .module-container {
        padding: 0;
        flex: 1 1 0;
        min-height: 0;
        overflow-y: auto;
        background: #fff;
        margin: 0;
        border-radius: 12px;
        box-shadow: none;
        display: block;
        min-width: 0;
        width: 100%;
      }
    }
  }
}

.settings-container {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  
  .settings-info {
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    
    p {
      margin: 8px 0;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      line-height: 1.5715;
    }
  }
  
  .settings-actions {
    display: flex;
    gap: 12px;
    
    :deep(.el-button) {
      border-radius: 10px;
    }
  }
}

// 确保深层组件的圆角一致
:deep(.el-button) {
  border-radius: 10px;
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 10px;
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .custom-workspace {
    .workspace-container {
      .workspace-sidebar {
        width: 64px;
        
        .sidebar-menu li {
          padding: 12px 0;
          margin: 4px;
          justify-content: center;
          
          span {
            display: none;
          }
          
          .el-icon {
            margin-right: 0;
            font-size: 18px;
          }
        }
      }
    }
  }
}
</style> 