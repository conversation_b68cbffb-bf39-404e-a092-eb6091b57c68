<template>
  <div class="monaco-editor-wrapper" :class="{ 'fullscreen-mode': isFullscreen }">
    <div class="editor-toolbar">
      <div class="editor-title" v-if="isFullscreen">
        {{ title || 'Markdown编辑器' }}
      </div>
      
      <div class="editor-actions">
        <button 
          v-if="showResetButton"
          class="toolbar-btn reset-btn" 
          @click="handleReset" 
          title="重置内容"
          :disabled="isPreviewMode"
          :class="{ 'disabled-btn': isPreviewMode }"
        >
          <el-icon class="el-icon"><Refresh /></el-icon>
          <span class="btn-text">重置</span>
        </button>
        <button class="toolbar-btn fullscreen-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '全屏模式'">
          <el-icon class="el-icon">
            <FullScreen v-if="!isFullscreen" />
            <SwitchButton v-else />
          </el-icon>
          <span class="btn-text">{{ isFullscreen ? '退出全屏' : '全屏' }}</span>
        </button>
      </div>
      
      <div class="preview-switch">
        <span class="switch-label">{{ isPreviewMode ? '预览' : '预览' }}</span>
        <el-switch
          v-model="isPreviewMode"
          @change="togglePreview"
          :active-icon="View"
          :inactive-icon="EditPen"
          class="preview-mode-switch"
          inline-prompt
        />
      </div>
    </div>
    
    <!-- 编辑器容器 -->
    <div 
      v-show="!isPreviewMode"
      ref="editorContainer" 
      class="monaco-editor-container" 
      :style="{ height: isFullscreen ? 'calc(100vh - 40px)' : height }"
    ></div>
    
    <!-- Markdown预览容器 -->
    <div 
      v-show="isPreviewMode"
      class="markdown-preview-container" 
      :style="{ height: isFullscreen ? 'calc(100vh - 40px)' : height }"
    >
      <div class="markdown-preview" v-html="renderedMarkdown"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, computed, defineProps, defineEmits } from 'vue'
import * as monaco from 'monaco-editor'
import { 
  initMonacoEditor as initMonaco, 
  configureMarkdownKeyboardShortcuts 
} from '../../utils/monaco-loader'
import { Refresh, FullScreen, SwitchButton, View, EditPen } from '@element-plus/icons-vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import { ElMessageBox, ElMessage } from 'element-plus'

// 配置marked选项
marked.setOptions({
  breaks: true,        // 将回车转换为<br>
  gfm: true,           // 使用GitHub风格Markdown
  headerIds: true,     // 为标题添加id
  mangle: false,       // 不编码邮箱地址
  smartLists: true,    // 使用更智能的列表行为
  smartypants: false,  // 不使用更智能的标点符号
  xhtml: false         // 不使用xhtml闭合标签
})

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: 'markdown'
  },
  theme: {
    type: String,
    default: 'vs'  // 'vs', 'vs-dark', 'hc-black', 'customMarkdown'
  },
  height: {
    type: String,
    default: '300px'
  },
  title: {
    type: String,
    default: ''
  },
  options: {
    type: Object,
    default: () => ({})
  },
  showResetButton: {
    type: Boolean,
    default: true
  },
  initialContent: {
    type: String,
    default: ''
  },
  defaultPreviewMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const editorContainer = ref<HTMLElement | null>(null)
let editor: monaco.editor.IStandaloneCodeEditor | null = null
let preventTriggerChangeEvent = false
const isFullscreen = ref(false)
const initialContent = ref('') // 真正的初始内容，用于重置功能
const isPreviewMode = ref(false) // 预览模式状态

// 保存初始选项状态
let initialOptions: any = null

// 计算属性：渲染后的Markdown内容
const renderedMarkdown = computed(() => {
  // 使用DOMPurify清理HTML，防止XSS攻击
  return DOMPurify.sanitize(marked(props.modelValue || '') as string);
});

// 切换预览/编辑模式
const togglePreview = () => {
  // 注意：由于我们现在使用v-model绑定isPreviewMode，不需要在这里手动切换状态
  // isPreviewMode.value由el-switch直接控制
  
  // 延迟处理编辑器布局调整，确保DOM已更新
  if (!isPreviewMode.value && editor) {
    // 给Monaco Editor一个更长的延迟时间来确保DOM已完全更新
    setTimeout(() => {
      if (editor) {
        // 如果编辑器实例存在但失效，重新创建
        if (!editor.getModel()) {
          // 重新初始化编辑器
          initEditor();
        } else {
          // 否则只需更新布局
          editor.layout();
        }
      } else {
        // 如果编辑器实例不存在，重新创建
        initEditor();
      }
    }, 200);
  }
}

// 初始化编辑器的功能抽取为单独函数，方便重用
const initEditor = () => {
  if (!editorContainer.value) return;

  // 如果已存在编辑器实例，先销毁
  if (editor) {
    editor.dispose();
  }
  
  // 基本配置选项
  const defaultOptions: monaco.editor.IStandaloneEditorConstructionOptions = {
    value: props.modelValue,
    language: props.language,
    theme: props.language === 'markdown' ? 'customMarkdown' : props.theme,
    automaticLayout: true, // 自动调整布局
    lineNumbers: 'on',
    scrollBeyondLastLine: false,
    minimap: { enabled: true },
    wordWrap: 'on', // 自动换行
    contextmenu: true,
    fontSize: 14,
    tabSize: 2,
    // 启用代码提示功能
    quickSuggestions: {
      other: true,
      comments: true,
      strings: true
    },
    suggestOnTriggerCharacters: true,
    acceptSuggestionOnEnter: 'on',
    tabCompletion: 'on',
    snippetSuggestions: 'top'
  }

  // 合并用户自定义选项
  const mergedOptions = {
    ...defaultOptions,
    ...props.options
  }
  
  // 保存初始选项状态，用于重置
  if (!initialOptions) {
    initialOptions = { ...mergedOptions }
  }

  // 创建编辑器实例
  editor = monaco.editor.create(editorContainer.value, mergedOptions)

  // 为Markdown编辑器添加快捷键支持
  if (props.language === 'markdown' && editor) {
    configureMarkdownKeyboardShortcuts(editor)
  }

  // 监听内容变化事件
  editor.onDidChangeModelContent((event) => {
    if (preventTriggerChangeEvent) return
    
    const value = editor?.getValue() || ''
    emit('update:modelValue', value)
    emit('change', value, event)
  })
  
  // 确保编辑器正确调整大小
  setTimeout(handleResize, 100)
}

// 初始化编辑器
onMounted(() => {
  // 初始化 Monaco Editor 配置
  initMonaco()

  // 保存初始内容，优先使用指定的initialContent，其次使用modelValue
  initialContent.value = props.initialContent || props.modelValue || ''

  // 根据defaultPreviewMode属性设置初始预览模式状态
  if (props.defaultPreviewMode) {
    isPreviewMode.value = true
  }

  // 初始化编辑器
  initEditor();
  
  // 确保编辑器正确调整大小
  window.addEventListener('resize', handleResize)

  // 添加ESC键监听，用于退出全屏
  document.addEventListener('keydown', handleKeyDown)
})

// 处理键盘事件
const handleKeyDown = (e: KeyboardEvent) => {
  // ESC键退出全屏
  if (e.key === 'Escape' && isFullscreen.value) {
    isFullscreen.value = false
  }
}

// 完全重置编辑器内容和状态
const handleReset = () => {
  // 添加确认对话框
  ElMessageBox.confirm(
    '确定要重置编辑器内容吗？这将会还原到修改前的状态。',
    '重置确认',
    {
      confirmButtonText: '确定重置',
      cancelButtonText: '取消',
      type: 'warning',
      draggable: true,
      customClass: 'editor-reset-confirm-dialog'
    }
  )
  .then(() => {
    // 用户点击了确认按钮，执行重置操作
    if (editor) {
      // 防止触发change事件
      preventTriggerChangeEvent = true
      
      // 恢复到初始内容，优先使用指定的initialContent
      const resetContent = props.initialContent || initialContent.value
      editor.setValue(resetContent)
      
      // 重置编辑器状态
      editor.setScrollPosition({ scrollTop: 0, scrollLeft: 0 });
      editor.setPosition({ lineNumber: 1, column: 1 });
      
      // 重置选择范围
      editor.setSelection(new monaco.Range(1, 1, 1, 1));
      
      // 恢复事件触发并更新v-model绑定值
      preventTriggerChangeEvent = false
      emit('update:modelValue', resetContent)
      
      // 提示用户已重置
      ElMessage({
        message: '内容已重置到修改前的状态',
        type: 'success',
        duration: 2000
      })
    } else {
      // 如果在预览模式下，直接更新modelValue
      const resetContent = props.initialContent || initialContent.value
      emit('update:modelValue', resetContent);
      ElMessage({
        message: '内容已重置到修改前的状态',
        type: 'success',
        duration: 2000
      })
    }
  })
  .catch(() => {
    // 用户点击了取消按钮，不执行任何操作
  })
}

// 切换全屏模式
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  // 在下一个事件循环中调整编辑器大小，确保DOM已更新
  setTimeout(() => {
    handleResize()
  }, 100)
}

// 调整编辑器大小
const handleResize = () => {
  if (editor && !isPreviewMode.value) {
    editor.layout()
  }
}

// 在组件卸载前销毁编辑器实例
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('keydown', handleKeyDown)
  if (editor) {
    editor.dispose()
    editor = null
  }
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (editor && newValue !== editor.getValue()) {
    preventTriggerChangeEvent = true
    editor.setValue(newValue || '')
    preventTriggerChangeEvent = false
  }
}, { immediate: true })

watch(() => props.language, (newLanguage) => {
  if (editor) {
    monaco.editor.setModelLanguage(editor.getModel()!, newLanguage)
    
    // 如果切换到Markdown语言，应用Markdown快捷键
    if (newLanguage === 'markdown') {
      configureMarkdownKeyboardShortcuts(editor)
    }
  }
})

watch(() => props.theme, (newTheme) => {
  if (editor) {
    monaco.editor.setTheme(newTheme)
  }
})

// 监听预览模式变化，退出预览模式时重新调整编辑器大小
watch(() => isPreviewMode.value, (newValue) => {
  if (!newValue && editor) {
    setTimeout(() => {
      handleResize();
    }, 100);
  }
});

// 提供获取编辑器实例的方法
defineExpose({
  getEditor: () => editor,
  toggleFullscreen,
  handleReset,
  togglePreview,
  isPreviewMode
})
</script>

<style lang="scss" scoped>
.monaco-editor-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;

  &.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: white;
    border-radius: 0;
  }

  .editor-toolbar {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;

    .editor-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    .editor-actions {
      display: flex;
      gap: 8px;
    }
    
    .preview-switch {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .switch-label {
        font-size: 13px;
        color: #606266;
      }
      
      .preview-mode-switch {
        --el-switch-on-color: #67c23a;
      }
    }

    .toolbar-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      border: none;
      background-color: #e4e7ed;
      border-radius: 4px;
      cursor: pointer;
      padding: 4px 10px;
      color: #606266;
      transition: all 0.2s;
      font-size: 12px;

      .el-icon {
        font-size: 16px;
      }

      .btn-text {
        line-height: 1;
      }

      &:hover {
        background-color: #d0d3d9;
        color: #409EFF;
      }

      &.reset-btn:hover {
        color: #f56c6c;
        background-color: #fef0f0;
      }
      
      &.fullscreen-btn:hover {
        color: #409EFF;
        background-color: #ecf5ff;
      }
      
      &.disabled-btn {
        opacity: 0.5;
        cursor: not-allowed;
        
        &:hover {
          background-color: #e4e7ed;
          color: #606266;
        }
      }
    }
  }
}

.monaco-editor-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  flex: 1;
  
  :deep(.monaco-editor) {
    position: absolute !important;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100% !important;
    height: 100% !important;
  }
  
  :deep(.monaco-editor .overflow-guard) {
    width: 100% !important;
    height: 100% !important;
  }
}

.markdown-preview-container {
  width: 100%;
  overflow: auto;
  position: relative;
  flex: 1;
  background-color: #fff;
  padding: 16px;
  box-sizing: border-box;
  
  .markdown-preview {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #24292e;
    
    // Markdown样式
    :deep(h1) {
      font-size: 2em;
      margin: 0.67em 0;
      padding-bottom: 0.3em;
      border-bottom: 1px solid #eaecef;
    }
    
    :deep(h2) {
      font-size: 1.5em;
      margin: 0.83em 0;
      padding-bottom: 0.3em;
      border-bottom: 1px solid #eaecef;
    }
    
    :deep(h3) {
      font-size: 1.25em;
      margin: 1em 0;
    }
    
    :deep(h4) {
      font-size: 1em;
      margin: 1.33em 0;
    }
    
    :deep(h5) {
      font-size: 0.875em;
      margin: 1.67em 0;
    }
    
    :deep(h6) {
      font-size: 0.85em;
      margin: 2.33em 0;
      color: #6a737d;
    }
    
    :deep(p) {
      margin: 0 0 16px 0;
    }
    
    :deep(ul), :deep(ol) {
      padding-left: 2em;
      margin: 0 0 16px 0;
    }
    
    :deep(li) {
      margin: 0.25em 0;
    }
    
    :deep(a) {
      color: #0366d6;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    :deep(code) {
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      padding: 0.2em 0.4em;
      margin: 0;
      font-size: 85%;
      background-color: rgba(27, 31, 35, 0.05);
      border-radius: 3px;
    }
    
    :deep(pre) {
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      padding: 16px;
      overflow: auto;
      font-size: 85%;
      line-height: 1.45;
      background-color: #f6f8fa;
      border-radius: 3px;
      
      code {
        padding: 0;
        margin: 0;
        font-size: 100%;
        background-color: transparent;
        border-radius: 0;
      }
    }
    
    :deep(blockquote) {
      margin: 0 0 16px 0;
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
    }
    
    :deep(hr) {
      height: 0.25em;
      padding: 0;
      margin: 24px 0;
      background-color: #e1e4e8;
      border: 0;
    }
    
    :deep(table) {
      display: block;
      width: 100%;
      overflow: auto;
      border-spacing: 0;
      border-collapse: collapse;
      margin: 0 0 16px 0;
      
      th, td {
        padding: 6px 13px;
        border: 1px solid #dfe2e5;
      }
      
      tr {
        background-color: #fff;
        border-top: 1px solid #c6cbd1;
        
        &:nth-child(2n) {
          background-color: #f6f8fa;
        }
      }
      
      th {
        font-weight: 600;
      }
    }
    
    :deep(img) {
      max-width: 100%;
      box-sizing: content-box;
      background-color: #fff;
    }
    
    :deep(input[type="checkbox"]) {
      margin-right: 0.5em;
      vertical-align: middle;
    }
  }
}
</style> 