<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, nextTick, type PropType } from 'vue'
import { ElMessage } from 'element-plus'
import { Collection, Star, CollectionTag, Plus } from '@element-plus/icons-vue'
import MonacoEditor from './MonacoEditor.vue'
import ContentQualityScore from './ContentQualityScore.vue'
import { formatTitle } from '../../utils/format'
import httpRequest from '@/utils/httpRequest'
import { debounce } from '../../utils/debounce'

// 定义标签选项接口
interface TagOption {
  id: string
  name: string
}

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({
      title: '',
      content: '',
      originalContent: '',
      taskId: '',
      ticketId: '',
      rgId: 0,
      tagsIds: ''
    })
  },
  currentMisId: {
    type: String,
    default: ''
  },
  currentEmpId: {
    type: String,
    default: ''
  },
  currentTeam: {
    type: [String, Number],
    default: ''
  },
  taskMissingInfo: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'large'
  },
  // 新增props用于控制对话框模式
  mode: {
    type: String as PropType<'convert' | 'detail'>,
    default: 'convert'
  },
  dialogTitle: {
    type: String,
    default: ''
  },
  historyIndex: {
    type: Number,
    default: 1
  },
  rgId: {
    type: [Number, String],
    default: ''
  },
  misId: {
    type: String,
    default: ''
  },
  // 添加contentId属性
  contentId: {
    type: [Number, String],
    default: ''
  },
  // 添加控制是否显示"添加标签"选项的属性
  showAddTagOption: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits([
  'update:modelValue', 
  'update:formData', 
  'save', 
  'compare', 
  'close',
  // 新增detail模式的事件
  'update:visible',
  'refresh',
  'open-tag-management'  // 新增：打开标签管理的事件
])

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => {
    if (props.mode === 'detail') {
      return props.visible || false
    }
    return props.modelValue
  },
  set: (value) => {
    if (props.mode === 'detail') {
      emit('update:visible', value)
    } else {
      emit('update:modelValue', value)
    }
  }
})

// 使用计算属性处理表单数据
const convertFormData = computed({
  get: () => props.formData,
  set: (value) => {
    emit('update:formData', value)
  }
})

// 标签相关数据
const convertTagOptions = ref<TagOption[]>([])
const convertSelectedTagIds = ref<string[]>([])
const convertCurrentTagNames = ref<string[]>([])
const convertIsLoadingTags = ref(false)
const convertDefaultTag = ref<TagOption | null>(null)

// 获取当前任务的标签（通过IDs）
const fetchCurrentTaskTags = async (tagIds: string) => {
  if (!tagIds) {
    convertCurrentTagNames.value = []
    convertSelectedTagIds.value = []
    return
  }
  
  try {
    const ids = tagIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
    if (ids.length === 0) {
      convertCurrentTagNames.value = []
      convertSelectedTagIds.value = []
      return
    }
    
    // 修复参数传递方式，支持后端@RequestParam("ids") List<Long> ids接收
    // 方式1：使用URLSearchParams构造多个同名参数
    const params = new URLSearchParams()
    ids.forEach(id => params.append('ids', id.toString()))
    
    const response = await httpRequest.rawRequestGet(`/rgTags/getTagsByIds?${params.toString()}`)
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      // 提取标签名称用于显示，优先使用tagName字段
      convertCurrentTagNames.value = response.data.map((tag: any) => tag.tagName || tag.name || '其他')
      
      // 提取ID用于选择状态
      convertSelectedTagIds.value = response.data.map((tag: any) => String(tag.id))
      
      // 确保当前标签也在标签选项中（如果不存在则添加）
      response.data.forEach((tag: any) => {
        const tagId = String(tag.id)
        const tagName = tag.tagName || tag.name || '其他'
        
        // 检查标签是否已在选项列表中
        const existingTag = convertTagOptions.value.find(option => option.id === tagId)
        if (!existingTag) {
          // 如果不存在，添加到选项列表
          convertTagOptions.value.push({
            id: tagId,
            name: tagName
          })
          console.log('添加当前任务标签到选项列表:', { id: tagId, name: tagName })
        }
      })
      
      console.log('当前选中的标签IDs:', convertSelectedTagIds.value)
      console.log('当前选中的标签名称:', convertCurrentTagNames.value)
      console.log('所有可选标签:', convertTagOptions.value)
    } else {
      console.warn('获取当前任务标签失败:', response?.msg || '数据格式错误')
      convertCurrentTagNames.value = []
      convertSelectedTagIds.value = []
    }
  } catch (error) {
    console.error('获取当前任务标签出错:', error)
    convertCurrentTagNames.value = []
    convertSelectedTagIds.value = []
  }
}

// 获取值班组下的所有标签
const fetchConvertTagList = async () => {
  const teamId = props.mode === 'detail' ? props.rgId : props.currentTeam
  
  if (!teamId) {
    console.warn('fetchConvertTagList - teamId为空，无法获取标签')
    return
  }
  
  try {
    convertIsLoadingTags.value = true
    const response = await httpRequest.rawRequestGet('/rgTags/listTags', {
      rgId: teamId
    })
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      const formattedTags = response.data.map((tag: any) => {
        // 根据后端返回的RgTagsEntity结构，使用正确的字段名
        const id = tag.id === null ? null : String(tag.id)
        const name = tag.tagName || tag.name || `标签${id || 'default'}`
        
        return {
          id,
          name
        }
      }).filter(tag => tag.name)
      
      // 设置默认标签（第一个标签，id为null）
      if (formattedTags.length > 0 && formattedTags[0].id === null) {
        convertDefaultTag.value = formattedTags[0]
        // 从可选标签中移除默认标签
        convertTagOptions.value = formattedTags.slice(1)
      } else {
        convertDefaultTag.value = null
        convertTagOptions.value = formattedTags
      }
    } else {
      console.warn('获取标签列表失败:', response?.msg || '数据格式错误')
      convertTagOptions.value = []
      convertDefaultTag.value = null
    }
  } catch (error) {
    console.error('获取标签列表出错:', error)
    convertTagOptions.value = []
    convertDefaultTag.value = null
  } finally {
    convertIsLoadingTags.value = false
  }
}

// 处理转换语料标签选择变化
const handleConvertTagSelectionChange = (tagIds: string[]) => {
  // 限制最多选择3个标签
  if (tagIds.length > 3) {
    ElMessage.warning('最多只能选择3个标签')
    tagIds = tagIds.slice(0, 3)
    convertSelectedTagIds.value = tagIds
  } else {
    convertSelectedTagIds.value = tagIds
  }
  
  // 更新convertFormData中的tagsIds
  const selectedTags = convertTagOptions.value.filter(tag => tagIds.includes(tag.id))
  
  let submitTagsIds: string = ''
  
  if (selectedTags.length === 0 && convertDefaultTag.value) {
    // 没有选择任何标签时，提交空字符串，后端会处理为默认标签
    submitTagsIds = ''
  } else {
    // 有选择标签时，提交选择的标签ID
    submitTagsIds = tagIds.join(',')
  }
  
  convertFormData.value = {
    ...convertFormData.value,
    tagsIds: submitTagsIds
  }
}

// 根据标签ID获取标签名称
const getTagNameById = (tagId: string) => {
  // 首先尝试从当前选中标签的索引位置获取标签名
  const index = convertSelectedTagIds.value.indexOf(tagId)
  if (index !== -1 && convertCurrentTagNames.value[index]) {
    return convertCurrentTagNames.value[index]
  }
  
  // 其次从标签选项中查找
  const tag = convertTagOptions.value.find(t => t.id === tagId)
  if (tag) {
    return tag.name
  }
  
  // 最后返回"其他"而不是"未知标签"，保持与API返回数据一致
  return '其他'
}

// 移除单个标签
const handleRemoveTag = (tagId: string) => {
  const newTagIds = convertSelectedTagIds.value.filter(id => id !== tagId)
  handleConvertTagSelectionChange(newTagIds)
}

// 处理标题输入
const handleTitleInput = (val: string) => {
  convertFormData.value = {
    ...convertFormData.value,
    title: formatTitle(val)
  }
}

// 处理保存
const handleSave = async () => {
  
  if (props.mode === 'detail') {
    // detail模式：通过emit事件让父组件处理保存
    emit('save', convertFormData.value)
  } else {
    // convert模式：使用原来的emit方式
    emit('save')
  }
}

// 处理检索对比
const handleCompare = () => {
  // 检查内容是否为空
  if (!convertFormData.value.content || convertFormData.value.content.trim() === '') {
    ElMessage.warning('请先填写内容再进行检索对比')
    return
  }
  
  // 传递完整的表单数据给父组件处理
  emit('compare', {
    ...convertFormData.value,
    content: convertFormData.value.content.trim()
  })
}

// 处理对话框关闭
const handleClose = () => {
  emit('close')
  dialogVisible.value = false
}

// 监听转换语料对话框显示状态 - 根据模式监听不同属性
watch(() => props.mode === 'detail' ? props.visible : props.modelValue, (newVisible) => {
  if (newVisible) {
    // 对话框打开时先获取标签列表
    fetchConvertTagList().then(() => {
      nextTick(() => {
        // 然后获取当前任务的标签信息
        if (convertFormData.value.tagsIds) {
          fetchCurrentTaskTags(convertFormData.value.tagsIds)
        } else {
          // 没有标签时，重置选择状态
          convertSelectedTagIds.value = []
          convertCurrentTagNames.value = []
        }
      })
    }).catch(error => {
      console.error('获取标签列表失败:', error)
    })
  } else {
    // 对话框关闭时重置状态
    convertSelectedTagIds.value = []
    convertCurrentTagNames.value = []
  }
}, { immediate: true })

// 创建防抖版本的保存函数
const debouncedHandleSave = debounce(handleSave, 300)

// 计算对话框标题
const computedDialogTitle = computed(() => {
  if (props.dialogTitle) {
    return props.dialogTitle
  }
  if (props.mode === 'detail') {
    return `语料详情 (来自历史记录#${props.historyIndex})`
  }
  return '转换语料审核'
})

// 计算按钮的加载状态
const isButtonLoading = computed(() => {
  return props.isLoading
})

// 计算缺失信息显示逻辑
const displayTaskMissingInfo = computed(() => {
  if (props.mode === 'detail') {
    return props.taskMissingInfo || []
  } else {
    return convertFormData.value.taskMissingInfo || []
  }
})

// 处理点击"添加标签"选项
const handleAddTagClick = (e: Event) => {
  // 强制阻止所有默认行为和事件传播
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  
  // 确保"添加标签"的空值不会被添加到选中的标签中
  convertSelectedTagIds.value = convertSelectedTagIds.value.filter(tag => tag !== '')
  
  // 强制关闭下拉框
  nextTick(() => {
    const selectComponents = document.querySelectorAll('.el-select')
    selectComponents.forEach((select: any) => {
      if (select.__vue__ && select.__vue__.blur) {
        select.__vue__.blur()
      }
    })
  })
  
  // 直接通知父组件打开标签管理，保留当前对话框状态
  emit('open-tag-management')
  
  return false
}

// 暴露刷新标签列表的方法给父组件
defineExpose({
  refreshTagList: fetchConvertTagList
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="computedDialogTitle"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="convert-dialog"
    :modal-append-to-body="false"
    style="margin-top: 8vh"
    @close="handleClose"
  >
    <template #header>
      <div class="convert-dialog-header">
        <span class="dialog-title">{{ computedDialogTitle }}</span>
        <div class="dialog-actions">
          <el-button 
            @click="handleCompare" 
            :size="size" 
            :disabled="isLoading"
          >
            检索对比
          </el-button>
          <el-button 
            type="primary" 
            @click="debouncedHandleSave" 
            :size="size" 
            :loading="isButtonLoading" 
            :disabled="isButtonLoading"
          >
            {{ props.mode === 'detail' ? '确认保存' : '确定保存' }}
          </el-button>
        </div>
      </div>
    </template>
    
    <div class="convert-dialog-content">
      <div class="content-main">
        <div class="content-section">
          <div class="section-header">
            <div class="header-title">内容标题</div>
            <!-- 添加内容质量评分组件 -->
            <ContentQualityScore 
              :content="convertFormData.content" 
              :visible="dialogVisible" 
            />
          </div>
          <el-input
            :model-value="convertFormData.title"
            :size="size"
            placeholder="请输入标题"
            @input="handleTitleInput"
          />
        </div>

        <div class="content-section">
          <div class="edit-area">
            <div class="section-header">
              <div class="header-title">编辑内容</div>
            </div>
            <!-- 使用Monaco Editor替换textarea -->
            <MonacoEditor
              v-model="convertFormData.content"
              language="markdown"
              height="320px"
              class="convert-corpus-editor"
              :options="{
                wordWrap: 'on',
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                lineNumbers: 'on',
                lineDecorationsWidth: 0,
                folding: true,
                renderLineHighlight: 'all',
                automaticLayout: true
              }"
            />
          </div>
        </div>

        <!-- 标签选择区域 -->
        <div class="content-section tag-management-section">
          <div class="section-header">
            <div class="header-title">
              <el-icon class="header-icon"><Collection /></el-icon>
              标签管理
            </div>
            <div class="tag-limit-tip">最多选择3个标签</div>
          </div>
          
          <!-- 标签选择器 -->
          <div class="tag-selection-area">
            <!-- 显示默认标签提示 -->
            <div v-if="convertDefaultTag && convertSelectedTagIds.length === 0" class="default-tag-display">
              <el-tag type="info" size="default" class="default-tag">
                {{ convertDefaultTag.name }}（默认标签）
              </el-tag>
              <span class="default-tag-tip">未选择其他标签时将使用默认标签</span>
            </div>
            
            <!-- 标签选择器 -->
            <el-select
              :model-value="convertSelectedTagIds"
              @update:model-value="handleConvertTagSelectionChange"
              multiple
              placeholder="请选择标签，支持多选"
              style="width: 100%"
              :loading="convertIsLoadingTags"
              :size="size"
              filterable
              clearable
            >
              <el-option
                v-for="tag in convertTagOptions"
                :key="tag.id"
                :label="tag.name"
                :value="tag.id"
              />
              <!-- 添加"添加标签"选项 -->
              <el-option
                key="add-tag"
                label="📝 添加标签"
                value=""
                style="border-top: 1px solid #e4e7ed;"
                :disabled="!props.showAddTagOption"
                @mousedown.stop.prevent="handleAddTagClick"
                @click.stop.prevent="handleAddTagClick"
                v-if="props.showAddTagOption"
              >
                <div 
                  style="display: flex; align-items: center; color: #409EFF; font-weight: 500; cursor: pointer; pointer-events: auto;" 
                  @mousedown.stop.prevent="handleAddTagClick"
                  @click.stop.prevent="handleAddTagClick"
                >
                  <el-icon style="margin-right: 8px; font-size: 14px;"><Plus /></el-icon>
                  <span>添加标签</span>
                </div>
              </el-option>
            </el-select>
            
            <!-- 已选择标签预览 -->
            <div v-if="convertSelectedTagIds.length > 0" class="selected-tags-preview">
              <div class="preview-title">已选择的标签：</div>
              <div class="selected-tags-list">
                <el-tag
                  v-for="tagId in convertSelectedTagIds"
                  :key="tagId"
                  type="primary"
                  size="default"
                  closable
                  @close="handleRemoveTag(tagId)"
                  class="selected-tag"
                >
                  {{ getTagNameById(tagId) }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="content-side">
        <div class="missing-info">
          <div class="side-title">模型认为缺失的信息 ({{ displayTaskMissingInfo.length }})</div>
          <div class="missing-list" v-if="displayTaskMissingInfo.length">
            <div 
              v-for="(item, index) in displayTaskMissingInfo" 
              :key="index" 
              class="missing-item"
            >
              {{ item }}
            </div>
          </div>
          <div v-else class="no-missing">暂无缺失信息</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.convert-dialog {
  :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  :deep(.el-dialog__headerbtn) {
    top: 20px;
  }

  .convert-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dialog-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .dialog-actions {
      display: flex;
      gap: 12px;

      .el-button {
        font-size: 14px;
        padding: 8px 16px;
      }
    }
  }

  .convert-dialog-content {
    display: flex;
    gap: 20px;
    margin-top: 10px;

    .content-main {
      flex: 1;

      .content-section {
        margin-bottom: 24px;

        .section-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 12px;
        }

        .edit-area {
          .edit-title {
            font-size: 14px;
            font-weight: 500;
            color: #409EFF;
            margin-bottom: 8px;
          }
          
          /* Monaco Editor 样式调整 */
          :deep(.monaco-editor-wrapper) {
            margin-bottom: 20px;
            
            &.fullscreen-mode {
              margin-bottom: 0;
              z-index: 3000;
            }
            
            .editor-toolbar {
              background-color: #f5f7fa;
            }
          }
          
          :deep(.monaco-editor-container) {
            min-height: 320px;
          }
          
          .convert-editor {
            width: 100%;
            display: block;
          }
          
          :deep(.monaco-editor) {
            width: 100% !important;
          }
          
          :deep(.overflow-guard) {
            width: 100% !important;
          }
        }

        :deep(.el-input__wrapper) {
          font-size: 14px;
        }

        :deep(.el-textarea__inner) {
          font-size: 14px;
          line-height: 1.6;
          resize: vertical;
          min-height: 200px;
        }
        
        // 标签相关样式
        .tag-management-section {
          background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
          border: 1px solid #e1e8ff;
          border-radius: 12px;
          padding: 20px;
          position: relative;
          overflow: hidden;
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #409EFF 0%, #67C23A 50%, #E6A23C 100%);
          }
          
          .section-header {
            margin-bottom: 20px;
            
            .header-title {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
              
              .header-icon {
                color: #409EFF;
                font-size: 18px;
              }
            }
            
            .tag-limit-tip {
              background: #ecf5ff;
              color: #409EFF;
              border: 1px solid #b3d8ff;
              padding: 4px 12px;
              border-radius: 16px;
              font-size: 12px;
              font-weight: 500;
            }
          }
        }
        
        .tag-selection-area {
          .selection-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            
            .section-icon {
              color: #409EFF;
              font-size: 16px;
            }
          }
          
          .default-tag-display {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px 16px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e1ecff 100%);
            border-radius: 8px;
            border: 1px solid #b3d8ff;
            
            .default-tag {
              flex-shrink: 0;
              background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
              border-color: #409EFF;
              color: #fff;
              font-weight: 500;
              box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
            }
            
            .default-tag-tip {
              font-size: 13px;
              color: #606266;
              font-weight: 500;
            }
          }
          
          :deep(.el-select) {
            margin-bottom: 16px;
            
            .el-select__wrapper {
              border-radius: 8px;
              border: 2px solid #e4e7ed;
              transition: all 0.3s ease;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
              
              &:hover {
                border-color: #c0c4cc;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
              }
              
              &.is-focused {
                border-color: #409EFF;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
              }
            }
          }
          
          .selected-tags-preview {
            margin-top: 16px;
            padding: 16px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e1ecff 100%);
            border-radius: 8px;
            border: 1px solid #b3d8ff;
            
            .preview-title {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 13px;
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: 12px;
            }
            
            .selected-tags-list {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              
              .selected-tag {
                border-radius: 6px;
                background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
                border: 1px solid #409EFF;
                color: #fff;
                font-weight: 500;
                padding: 0 12px;
                height: 28px;
                display: flex;
                align-items: center;
                box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
                transition: all 0.3s ease;
                
                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
                }
                
                :deep(.el-tag__close) {
                  color: #fff;
                  font-weight: bold;
                  margin-left: 6px;
                  
                  &:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                  }
                }
              }
            }
          }
        }
      }
    }

    .content-side {
      width: 240px;
      flex-shrink: 0;
      border-left: 1px solid #e4e7ed;
      padding-left: 20px;

      .missing-info {
        .side-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          gap: 8px;

          &::after {
            content: attr(data-count);
            background: #e6a23c;
            color: #fff;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: normal;
          }
        }

        .missing-list {
          .missing-item {
            margin-bottom: 8px;
            padding: 8px 12px;
            background: #fdf6ec;
            border-radius: 4px;
            color: #e6a23c;
            font-size: 14px;
            line-height: 1.4;
            border-left: 3px solid #e6a23c;
          }
        }

        .no-missing {
          color: #e6a23c;
          font-size: 14px;
          text-align: center;
          padding: 20px 0;
          background: #fdf6ec;
          border-radius: 4px;
        }
      }
    }
  }
}

/* 添加样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  .header-title {
    font-weight: bold;
    font-size: 15px;
    color: #303133;
  }
}

/* 转换语料对话框编辑器样式 */
.convert-corpus-editor {
  .monaco-editor-wrapper {
    width: 100%;
    min-height: 320px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;

    .monaco-editor-container {
      min-height: 320px;
    }
  }
}

/* 标签相关样式 */
.tag-limit-tip {
  font-size: 12px;
  color: #909399;
}

.default-tag-display {
  margin-bottom: 12px;
  
  .default-tag {
    background-color: #f0f9ff;
    border-color: #b3d8ff;
    color: #409eff;
  }
}

/* 标签选择器样式 */
:deep(.el-select) {
  .el-select__wrapper {
    min-height: 36px;
  }
  
  .el-tag {
    margin: 2px 4px 2px 0;
  }
}
</style> 