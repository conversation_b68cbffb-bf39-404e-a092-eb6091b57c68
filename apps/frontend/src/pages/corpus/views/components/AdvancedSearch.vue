<template>
  <div class="advanced-search" ref="rootRef">
    <div class="search-toggle" @click="toggleAdvancedSearch">
      <span>{{ isExpanded ? '收起高级检索' : '高级检索 >' }}</span>
    </div>
    
    <div v-if="isExpanded" class="search-form" @click.stop>
      <el-form :model="searchForm" label-width="80px" size="default">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="工单ID">
              <el-input v-model="searchForm.ticketId" placeholder="请输入工单ID" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="标题">
              <el-input v-model="searchForm.title" placeholder="请输入标题" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="内容">
              <el-input v-model="searchForm.content" placeholder="请输入内容" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="来源">
              <el-select 
                v-model="searchForm.source" 
                placeholder="请选择来源" 
                clearable
                @visible-change="handleSelectVisible"
              >
                <el-option v-for="(label, value) in sourceOptions" :key="value" :label="label" :value="Number(value)" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="标签">
              <el-select 
                v-model="searchForm.tags" 
                multiple
                placeholder="请选择标签" 
                clearable
                :loading="isLoadingTags"
                @visible-change="handleSelectVisible"
                @remove-tag="handleTagRemove"
                @click.stop
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="2"
              >
                <el-option 
                  v-for="tag in tagOptions" 
                  :key="tag.id" 
                  :label="tag.name" 
                  :value="tag.id" 
                />
                <el-option
                  key="add-tag-advanced"
                  label="📝 添加标签"
                  value=""
                  @click.stop="handleAddTagClick"
                  style="border-top: 1px solid #e4e7ed;"
                  :disabled="true"
                >
                  <div style="display: flex; align-items: center; color: #409EFF; font-weight: 500; cursor: pointer;" @click.stop="handleAddTagClick">
                    <el-icon style="margin-right: 8px; font-size: 14px;"><Plus /></el-icon>
                    <span>添加标签</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人">
              <el-input v-model="searchForm.creator" placeholder="请输入创建人" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD HH:mm:ss"
                :shortcuts="dateShortcuts"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24" style="text-align: right;">
            <el-button @click="resetForm">重置</el-button>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { SOURCE_MAP } from '../../constants'
import httpRequest from '../../../../utils/httpRequest'

// 定义标签选项接口
interface TagOption {
  id: string
  name: string
}

// 定义组件的属性
const props = defineProps<{
  // 可以接收初始搜索条件
  initialSearchParams?: Record<string, any>
  // 可选的获取rgId和misId的属性
  rgId?: number
  misId?: string
}>()

// 定义组件的事件
const emit = defineEmits<{
  // 搜索事件，发送搜索参数
  (e: 'search', params: Record<string, any>): void
  // 面板展开状态变化事件
  (e: 'panel-state-change', expanded: boolean): void
  // 打开标签管理事件
  (e: 'open-tag-management'): void
}>()

// 是否展开高级搜索
const isExpanded = ref(false)
// 标记下拉选择框是否激活
const isSelectActive = ref(false)
// 高级检索根节点ref
const rootRef = ref<HTMLElement | null>(null)

// 标签相关状态
const tagOptions = ref<TagOption[]>([])
const isLoadingTags = ref(false)

// 搜索表单数据
const searchForm = ref({
  ticketId: '',
  title: '',
  content: '',
  source: null as number | null,
  creator: '',
  tags: [] as string[], // 新增：标签ID数组
  startTime: '',
  endTime: ''
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 来源选项
const sourceOptions = computed(() => SOURCE_MAP)

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 获取标签列表
const fetchTagList = async () => {
  if (!props.rgId) return
  
  try {
    isLoadingTags.value = true
    const response = await httpRequest.rawRequestGet('/rgTags/listTags', {
      rgId: props.rgId
    })
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      // 根据实际API返回格式调整映射
      const formattedTags = response.data.map((tag: any) => {
        const id = tag.id === null ? null : String(tag.id || tag.tagId || tag.value || '')
        const name = tag.name || tag.tagName || tag.label || `标签${id || 'default'}`
        
        return {
          id,
          name
        }
      }).filter(tag => tag.name && tag.id !== null) // 过滤掉默认标签和无效数据
      
      tagOptions.value = formattedTags
    } else {
      console.warn('获取标签列表失败:', response?.msg || '数据格式错误')
      tagOptions.value = []
    }
  } catch (error) {
    console.error('获取标签列表出错:', error)
    tagOptions.value = []
  } finally {
    isLoadingTags.value = false
  }
}

// 控制搜索按钮显示/隐藏
const toggleSearchButtonVisibility = (show: boolean) => {
  // 更精确地定位搜索按钮 - 通常搜索按钮会有特定的特征
  // 1. 尝试通过附近的文本内容定位
  const buttons = document.querySelectorAll('button.el-button--primary');
  let searchButton = null;
  
  // 遍历所有主要按钮，查找搜索按钮
  for (let i = 0; i < buttons.length; i++) {
    const button = buttons[i];
    // 检查按钮是否包含搜索图标或文本
    if (
      button.innerHTML.includes('搜索') || 
      button.innerHTML.includes('search') || 
      button.querySelector('.el-icon-search') ||
      // 检查按钮是否在搜索框旁边的位置
      button.closest('.search-area') ||
      // 检查是否有特定的类名或ID
      button.classList.contains('search-btn')
    ) {
      // 找到了搜索按钮
      searchButton = button;
      break;
    }
  }
  
  // 如果上面没找到，尝试定位靠近高级搜索文本的按钮
  if (!searchButton) {
    // 获取当前高级搜索组件的位置
    const rect = rootRef.value?.getBoundingClientRect();
    if (rect) {
      // 查找在高级搜索左侧附近的主要按钮
      for (let i = 0; i < buttons.length; i++) {
        const buttonRect = buttons[i].getBoundingClientRect();
        // 如果按钮在高级搜索的左侧且垂直位置相近
        if (
          buttonRect.right < rect.left && 
          Math.abs(buttonRect.top - rect.top) < 50
        ) {
          searchButton = buttons[i];
          break;
        }
      }
    }
  }
  
  // 设置按钮显示/隐藏
  if (searchButton) {
    if (show) {
      searchButton.style.display = '';
    } else {
      searchButton.style.display = 'none';
    }
  }
}

// 清空外部搜索框内容
const clearSearchInput = () => {
  // 查找外部搜索框 - 通常搜索框会有特定特征
  const searchInputs = document.querySelectorAll('input[type="text"], input[type="search"], input:not([type])');
  let searchInput = null;
  
  // 根据搜索框的特征查找
  for (let i = 0; i < searchInputs.length; i++) {
    const input = searchInputs[i];
    // 检查输入框是否有placeholder包含搜索相关文字
    if (
      input.placeholder?.includes('搜索') ||
      input.placeholder?.includes('search') ||
      input.placeholder?.includes('ID') ||
      input.placeholder?.includes('标题') ||
      input.placeholder?.includes('内容') ||
      // 检查输入框是否在搜索区域
      input.closest('.search-area') ||
      // 检查是否有特定类名
      input.classList.contains('search-input')
    ) {
      searchInput = input;
      break;
    }
  }
  
  // 如果没找到，尝试根据位置关系定位
  if (!searchInput && rootRef.value) {
    const rect = rootRef.value.getBoundingClientRect();
    // 查找在高级搜索左侧附近的输入框
    for (let i = 0; i < searchInputs.length; i++) {
      const inputRect = searchInputs[i].getBoundingClientRect();
      // 如果输入框在高级搜索的左侧且垂直位置相近
      if (
        inputRect.right < rect.left && 
        Math.abs(inputRect.top - rect.top) < 50
      ) {
        searchInput = searchInputs[i];
        break;
      }
    }
  }
  
  // 清空搜索框的值
  if (searchInput) {
    searchInput.value = '';
    // 触发input事件，确保Vue的数据绑定更新
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
  }
}

// 切换高级搜索的显示/隐藏
const toggleAdvancedSearch = () => {
  isExpanded.value = !isExpanded.value
  
  // 如果展开面板，则清空外部搜索框
  if (isExpanded.value) {
    clearSearchInput();
  }
  
  // 控制搜索按钮显示/隐藏
  toggleSearchButtonVisibility(!isExpanded.value)
  // 通知父组件面板状态变化
  emit('panel-state-change', isExpanded.value)
}

// 重置表单
const resetForm = () => {
  searchForm.value = {
    ticketId: '',
    title: '',
    content: '',
    source: null,
    creator: '',
    tags: [], // 重置标签选择
    startTime: '',
    endTime: ''
  }
  dateRange.value = null
}

// 处理搜索
const handleSearch = () => {
  // 构建搜索参数
  const searchParams = {
    ...searchForm.value,
    // 将标签ID数组转换为逗号分隔的字符串（如果有标签选择的话）
    tagsIds: searchForm.value.tags.length > 0 ? searchForm.value.tags.join(',') : undefined
  }
  
  // 移除tags字段，因为后端接收的是tagsIds
  delete (searchParams as any).tags
  
  // 发送搜索事件
  emit('search', searchParams)
}

// 转换日期为中国时区（UTC+8）的时间字符串
const formatDateToChinaTimezone = (date: Date): string => {
  // 创建一个新的日期对象，避免修改原始日期
  const beijingDate = new Date(date.getTime());
  
  // 获取当前时区与UTC的偏移量（分钟）
  const currentTimezoneOffset = beijingDate.getTimezoneOffset();
  
  // 中国时区为UTC+8，即偏移量为-8小时（-480分钟）
  const beijingTimezoneOffset = -480;
  
  // 计算需要调整的分钟数
  const adjustMinutes = beijingTimezoneOffset - currentTimezoneOffset;
  
  // 调整时间
  beijingDate.setMinutes(beijingDate.getMinutes() + adjustMinutes);
  
  // 格式化为YYYY-MM-DD HH:mm:ss
  const year = beijingDate.getFullYear();
  const month = String(beijingDate.getMonth() + 1).padStart(2, '0');
  const day = String(beijingDate.getDate()).padStart(2, '0');
  const hours = String(beijingDate.getHours()).padStart(2, '0');
  const minutes = String(beijingDate.getMinutes()).padStart(2, '0');
  const seconds = String(beijingDate.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 监听日期范围变化，更新开始和结束时间
watch(dateRange, (newVal) => {
  if (newVal) {
    // 设置开始日期为当天的0点
    const startDate = new Date(newVal[0])
    startDate.setHours(0, 0, 0, 0)
    searchForm.value.startTime = formatDateToChinaTimezone(startDate)
    
    // 设置结束日期为当天的23:59:59
    const endDate = new Date(newVal[1])
    endDate.setHours(23, 59, 59, 999)
    searchForm.value.endTime = formatDateToChinaTimezone(endDate)
  } else {
    searchForm.value.startTime = ''
    searchForm.value.endTime = ''
  }
})

// 初始化搜索条件
if (props.initialSearchParams) {
  searchForm.value = {
    ...searchForm.value,
    ...props.initialSearchParams
  }
  
  // 如果有开始和结束时间，设置日期范围
  if (props.initialSearchParams.startTime && props.initialSearchParams.endTime) {
    // 从时间字符串中提取日期部分
    const startDate = props.initialSearchParams.startTime.split(' ')[0]
    const endDate = props.initialSearchParams.endTime.split(' ')[0]
    
    dateRange.value = [
      startDate + ' 00:00:00',
      endDate + ' 23:59:59'
    ]
  }
}

// 处理下拉框显示状态变化
const handleSelectVisible = (visible: boolean) => {
  isSelectActive.value = visible
}

// 处理标签删除
const handleTagRemove = (tagId: string) => {
  // 阻止事件冒泡，防止触发面板收起
  // 这个函数主要是为了标识标签删除事件，实际的删除由Element Plus自动处理
  console.log('标签删除:', tagId)
}

// 处理点击"添加标签"选项
const handleAddTagClick = (e: Event) => {
  // 阻止默认行为和事件冒泡
  e.preventDefault()
  e.stopPropagation()
  
  // 确保"添加标签"的空值不会被添加到选中的标签中
  // 移除可能被意外添加的空字符串值
  searchForm.value.tags = searchForm.value.tags.filter(tag => tag !== '')
  
  // 强制关闭下拉框但保留面板状态
  nextTick(() => {
    const selectComponents = document.querySelectorAll('.el-select')
    selectComponents.forEach((select: any) => {
      if (select.__vue__ && select.__vue__.blur) {
        select.__vue__.blur()
      }
    })
  })
  
  // 直接向父组件发出打开标签管理的事件，不关闭高级搜索面板
  emit('open-tag-management')
}

// 点击区域外自动收起
const handleClickOutside = (e: MouseEvent) => {
  if (!isExpanded.value || isSelectActive.value) return
  
  const target = e.target as HTMLElement
  
  // 特殊处理：检查是否点击的是标签删除按钮或其他Element Plus相关元素
  if (target) {
    // 检查点击的是否是Element Plus标签的关闭按钮或相关元素
    if (
      // 标签关闭按钮
      target.classList.contains('el-tag__close') ||
      target.closest('.el-tag__close') ||
      // 标签本身
      target.classList.contains('el-tag') ||
      target.closest('.el-tag') ||
      // 选择器标签容器
      target.classList.contains('el-select__tags') ||
      target.closest('.el-select__tags') ||
      // 整个选择器
      target.classList.contains('el-select') ||
      target.closest('.el-select') ||
      // 下拉菜单容器
      target.closest('.el-popper') ||
      target.closest('.el-select-dropdown') ||
      target.closest('.el-select-dropdown__item') ||
      // 选项元素
      target.closest('[role="option"]') ||
      target.closest('[role="listbox"]') ||
      // Element Plus的内部元素
      target.closest('.el-scrollbar') ||
      target.closest('.el-select-dropdown__wrap') ||
      // 输入框相关
      target.classList.contains('el-input__inner') ||
      target.closest('.el-input') ||
      target.closest('.el-input__wrapper') ||
      // 清除按钮
      target.classList.contains('el-select__clear') ||
      target.closest('.el-select__clear') ||
      // 下拉箭头
      target.classList.contains('el-select__suffix') ||
      target.closest('.el-select__suffix')
    ) {
      // 如果是标签或选择器相关的操作，不收起面板
      return
    }
  }
  
  if (rootRef.value && !rootRef.value.contains(target)) {
    isExpanded.value = false
    // 控制搜索按钮显示
    toggleSearchButtonVisibility(true)
    // 通知父组件面板状态变化
    emit('panel-state-change', isExpanded.value)
  }
}

// 监听展开状态变化
watch(isExpanded, (val) => {
  // 如果展开面板，则清空外部搜索框
  if (val) {
    clearSearchInput();
    // 如果展开面板且有rgId，获取标签列表
    if (props.rgId && tagOptions.value.length === 0) {
      fetchTagList()
    }
  }
  
  // 控制搜索按钮显示/隐藏
  toggleSearchButtonVisibility(!val)
  // 通知父组件面板状态变化
  emit('panel-state-change', val)
  
  if (val) {
    nextTick(() => {
      document.addEventListener('mousedown', handleClickOutside)
    })
  } else {
    document.removeEventListener('mousedown', handleClickOutside)
  }
})

// 监听rgId变化，重新获取标签列表
watch(() => props.rgId, (newRgId) => {
  if (newRgId) {
    fetchTagList()
  } else {
    // 如果rgId为空，清空标签选项和已选标签
    tagOptions.value = []
    searchForm.value.tags = []
  }
}, { immediate: true })

// 监听标签选择变化，过滤掉空值
watch(() => searchForm.value.tags, (newTags) => {
  // 过滤掉空字符串，防止"添加标签"选项被误选
  const filteredTags = newTags.filter(tag => tag !== '')
  if (filteredTags.length !== newTags.length) {
    searchForm.value.tags = filteredTags
  }
}, { deep: true })

// 组件挂载时确保按钮状态正确
onMounted(() => {
  // 初始化时根据面板状态设置搜索按钮
  if (isExpanded.value) {
    toggleSearchButtonVisibility(false)
  }
})

onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.advanced-search {
  margin: 0;
  display: inline-flex;
  align-items: center;
  height: 32px;
  
  .search-toggle {
    display: inline-block;
    cursor: pointer;
    color: #409EFF;
    font-size: 14px;
    margin-left: -10px;
    padding: 5px 10px;
    background-color: transparent;
    border-radius: 4px;
    transition: all 0.3s;
    line-height: 1.5;
    height: 32px;
    display: flex;
    align-items: center;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  .search-form {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-top: 10px;
    margin-bottom: 16px;
    border: 1px solid #e4e7ed;
    z-index: 100;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: calc(100% + 200px);
    min-width: 900px;
    
    .el-row {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .el-form-item {
      margin-bottom: 16px;
    }
    
    // 确保标签选择器的所有交互不会触发面板收起
    :deep(.el-select) {
      .el-select__tags {
        .el-tag {
          .el-tag__close {
            pointer-events: auto;
          }
        }
      }
    }
  }
}
</style> 