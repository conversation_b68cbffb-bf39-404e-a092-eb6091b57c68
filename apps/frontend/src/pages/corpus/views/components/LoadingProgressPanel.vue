<template>
  <div class="loading-progress-panel" v-if="visible">
    <div class="loading-wrapper">
      <div class="loading-header">
        <span class="title">{{ title }}</span>
        <span class="progress-text">{{ progressText }}</span>
      </div>
      
      <div class="progress-container">
        <div class="progress-bar" :style="{ width: `${progress}%` }"></div>
      </div>
      
      <div class="loading-content">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <div class="loading-message">{{ message }}</div>
      </div>
      
      <div class="loading-tips">
        <div class="tip-item" v-for="(tip, index) in tips" :key="index">
          <el-icon><InfoFilled /></el-icon>
          <span>{{ tip }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, watch, computed, defineProps, defineEmits } from 'vue'
import { Loading, InfoFilled } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '处理中'
  },
  message: {
    type: String,
    default: '请稍候...'
  },
  tips: {
    type: Array as () => string[],
    default: () => ['系统正在处理您的请求，预计需要1-2分钟', '大模型分析语料中，请耐心等待']
  },
  autoIncrement: {
    type: Boolean,
    default: true
  },
  duration: {
    type: Number,
    default: 60000 // 默认假进度时长60秒
  }
})

const emit = defineEmits(['update:visible', 'complete'])

// 假进度条状态
const progress = ref(0)
const progressText = computed(() => `${Math.floor(progress.value)}%`)
let progressTimer: number | null = null

// 开始假进度
const startProgress = () => {
  // 清除任何现有计时器
  stopProgress()
  
  // 重置进度
  progress.value = 5

  if (props.autoIncrement) {
    const incrementInterval = 500 // 每500ms更新一次
    const totalSteps = props.duration / incrementInterval
    const increment = 90 / totalSteps // 最高到90%，留10%给真实完成

    progressTimer = window.setInterval(() => {
      if (progress.value < 90) {
        // 非线性增长，开始快，然后变慢
        const factor = 1 - progress.value / 100
        progress.value += increment * factor * 1.5
      } else {
        // 达到90%后停止自动增长
        stopProgress()
      }
    }, incrementInterval)
  }
}

// 停止假进度
const stopProgress = () => {
  if (progressTimer !== null) {
    clearInterval(progressTimer)
    progressTimer = null
  }
}

// 完成处理
const complete = () => {
  stopProgress()
  progress.value = 100
  
  // 等待进度条动画完成后触发完成事件
  setTimeout(() => {
    emit('complete')
    emit('update:visible', false)
  }, 500)
}

// 重置进度
const reset = () => {
  stopProgress()
  progress.value = 0
}

// 手动设置进度
const setProgress = (value: number) => {
  // 只允许增加进度，不允许减少（防止回缩）
  progress.value = Math.max(progress.value, Math.min(100, value))
}

// 监听visible变化
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 显示时开始进度
    reset()
    startProgress()
  } else {
    // 隐藏时停止进度
    stopProgress()
  }
}, { immediate: true })

// 组件销毁时清理计时器
onBeforeUnmount(() => {
  stopProgress()
})

// 暴露方法
defineExpose({
  complete,
  reset,
  setProgress
})
</script>

<style lang="scss" scoped>
.loading-progress-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  
  .loading-wrapper {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    width: 480px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    
    .loading-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
      
      .progress-text {
        font-size: 14px;
        color: #409EFF;
        font-weight: 600;
      }
    }
    
    .progress-container {
      height: 8px;
      background: #f0f2f5;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 24px;
      
      .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #409EFF, #67c23a);
        border-radius: 4px;
        transition: width 0.3s ease;
        
        &.complete {
          background: linear-gradient(90deg, #67c23a, #67c23a);
        }
        
        &.stalled {
          background: linear-gradient(90deg, #409EFF, #e6a23c);
          animation: pulse 2s infinite;
        }
      }
    }
    
    .loading-content {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      
      .loading-icon {
        font-size: 24px;
        color: #409EFF;
        animation: rotate 1.5s linear infinite;
        margin-right: 16px;
      }
      
      .loading-message {
        font-size: 16px;
        color: #606266;
        flex: 1;
      }
    }
    
    .loading-tips {
      background: #f2f6fc;
      border-radius: 4px;
      padding: 16px;
      
      .tip-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-icon {
          color: #909399;
          margin-right: 8px;
          font-size: 16px;
          margin-top: 2px;
        }
        
        span {
          font-size: 14px;
          color: #909399;
          line-height: 1.5;
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}
</style> 