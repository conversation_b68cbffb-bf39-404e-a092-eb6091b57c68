<template>
  <!-- 使用重构后的ConvertCorpusDialog组件 -->
  <ConvertCorpusDialog
    v-model:visible="dialogVisible"
    :form-data="localData"
    :task-missing-info="localData.taskMissingInfo || []"
    :current-mis-id="props.misId"
    :current-team="props.rgId"
    :is-loading="compareLoading"
    :mode="'detail'"
    :history-index="props.historyIndex"
    :rg-id="props.rgId"
    :mis-id="props.misId"
    :content-id="props.contentId || props.detailData?.contentId || localData.contentId || ''"
    :show-add-tag-option="false"
    @save="handleSave"
    @compare="handleCompare"
    @close="handleClose"
    @update:form-data="handleFormDataUpdate"
  />

  <!-- 调试信息 -->
  <div v-if="false" style="display: none;">
    Debug: rgId={{ props.rgId }}, misId={{ props.misId }}, dialogVisible={{ dialogVisible }}
  </div>

  <!-- 检索对比结果抽屉 -->
  <el-drawer
    v-model="compareDialogVisible"
    title="相似语料检索结果"
    size="40%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="compare-dialog"
    direction="rtl"
    :modal="false"
    :append-to-body="true"
    :destroy-on-close="false"
    @closed="handleCompareClose"
  >
    <div class="compare-header">
      <div class="compare-tip">
        共找到 {{ compareResults.length }} 条相似内容
        <div class="sub-tip">系统根据语义相似度对语料进行了排序，您可以选择一条或多条语料进行删除操作。</div>
      </div>
    </div>

    <div class="compare-list">
      <div v-for="(item, index) in compareResults" :key="index" class="compare-item">
        <div class="compare-item-header">
          <div class="left-section">
            <el-checkbox 
              v-model="item.selected" 
              @change="(val: boolean) => handleItemSelect(item, val)"
            />
            <div class="task-id">{{ item.ticketId || '-' }}</div>
            <div class="similarity-score" :class="getSimilarityClass(item.score)">
              相似度 {{ item.score }}
            </div>
          </div>
          <div class="actions">
            <el-button link type="primary" @click="viewCompareDetail(item)">
              查看详情
            </el-button>
          </div>
        </div>
        <div class="compare-item-content">
          <div class="content-text">{{ item.content }}</div>
        </div>
        <div class="compare-item-footer">
          <div class="update-time">更新时间：{{ item.updateTime || '-' }}</div>
        </div>
      </div>
      
      <div v-if="compareResults.length === 0" class="no-data">
        暂无相似语料
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <div class="selected-count" v-if="selectedItems.length > 0">
          已选择 {{ selectedItems.length }} 项
        </div>
        <div class="footer-buttons">
          <!-- <el-button 
            @click="handleMergeSelected" 
            class="merge-btn"
            :disabled="selectedItems.length === 0"
            :loading="mergeLoading"
          >
            <el-icon><Connection /></el-icon>合并语料
          </el-button> -->
          <el-button 
            @click="handleBatchDelete" 
            type="danger"
            class="delete-btn"
            :disabled="selectedItems.length === 0"
            :loading="deleteLoading"
          >
            <el-icon><Delete /></el-icon>批量删除
          </el-button>
          <el-button @click="compareDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </template>
  </el-drawer>

  <!-- 加载蒙层 -->
  <div v-if="compareLoading" class="loading-overlay">
    <el-icon class="is-loading"><Loading /></el-icon>
    <span>正在检索相似语料...</span>
  </div>

  <!-- 查看详情对话框 -->
  <ViewCorpusDialog
    v-model="viewDialogVisible"
    :corpus-data="currentViewItem"
  />
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, nextTick } from 'vue'
import ViewCorpusDialog from '../ViewCorpusDialog.vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Delete, Connection, Loading } from '@element-plus/icons-vue'
import httpRequest from '../../../../../utils/httpRequest'
import { formatDateTime } from '../../../utils/format'
import { mergeSelectedCorpus } from '../../../utils/corpusManager'
import ConvertCorpusDialog from '../ConvertCorpusDialog.vue'

// 定义常量映射
const SOURCE_MAP = {
  1: '智能客服',
  2: '语料编辑',
  3: '知识转换',
  4: '合并语料',
  5: 'TT群导入',
  6: '用户反馈',
  7: '其他来源'
};

// 定义接口类型
interface CorpusItem {
  title: string;
  content: string;
  taskMissingInfo?: string[];
  ticketId?: string;
  contentId?: number | string;  // 添加contentId字段
  taskId?: string;              // 添加taskId字段
  tagsIds?: string; // 标签ID字符串，逗号分隔
  tagsname?: string; // 新增：标签名称，逗号分隔
}

// 添加API响应类型
interface ApiResponse<T = any> {
  code: number;
  msg: string;
  message?: string;
  data: T;
}

// 定义组件接收的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object as () => CorpusItem | null,
    default: null
  },
  historyIndex: {
    type: Number,
    default: 1
  },
  rgId: {
    type: [Number, String],
    required: true
  },
  misId: {
    type: String,
    required: true
  },
  contentId: {
    type: [Number, String],
    default: ''
  }
})

// 定义emit事件
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'compare', item: CorpusItem): void
  (e: 'save', item: CorpusItem): void
  (e: 'refresh'): void
}>()

// 内部状态变量
const dialogVisible = ref(false)
const localData = ref<CorpusItem>({
  title: '',
  content: '',
  taskMissingInfo: [],
  tagsIds: ''
})

// 检索对比相关变量
const compareDialogVisible = ref(false)
const compareResults = ref<any[]>([])
const compareLoading = ref(false)
const currentCompareItem = ref<any>(null)
const selectedItems = ref<any[]>([])

// 大尺寸常量
const large = 'large'

// 添加合并预览表单
const mergePreviewDialogVisible = ref(false)
const mergePreviewForm = ref({
  title: '',
  content: '',
  taskId: '',
  ticketId: '',
  corpusIdList: [] as string[],
  tagsIds: ''
})

// 添加合并相关状态
const mergeLoading = ref(false)
const deleteLoading = ref(false)

// 查看详情对话框相关变量
const viewDialogVisible = ref(false)
const viewEditor = ref(null)
const currentViewItem = ref<any>({
  title: '',
  content: '',
  source: '',
  misId: '',
  createTime: '',
  updateTime: '',
  backgroundKnowledge: '',
  sop: '',
  rule: ''
})

// 格式化标题函数
const formatTitle = (val: string) => {
  return val?.trim() || ''
}

// 处理检索对比按钮点击
const handleCompare = (data: any) => {
  // 如果没有传递data参数，使用localData
  const contentData = data || localData.value
  
  // 验证内容是否为空
  if (!contentData.content || contentData.content.trim() === '') {
    ElMessage.warning('内容不能为空，请先填写内容再进行检索对比')
    return
  }
  
  // 显示加载状态
  compareLoading.value = true
  
  // 发出对比事件，传递内容、团队ID和用户ID
  emit('compare', {
    ...contentData,
    content: contentData.content.trim(),
    rgId: props.rgId,
    misId: props.misId
  })
}

// 查看对比结果详情
const viewCompareDetail = (row: any) => {
  // 设置当前查看的语料项
  currentViewItem.value = { 
    ...row,
    misId: row.misId || '-',
    title: row.title || '无标题',
    content: row.content || '无内容',
    source: row.source || '-',
    createTime: row.createTime || '-',
    updateTime: row.updateTime || '-'
  }
  
  // 显示对话框
  viewDialogVisible.value = true

  // 在下一个事件循环中等待对话框打开后设置编辑器状态
  setTimeout(() => {
    try {
      // 获取编辑器实例并初始化
      const editor = document.querySelector('.view-corpus-editor')
      if (editor && (editor as any).__vue__) {
        const editorComponent = (editor as any).__vue__
        // 如果编辑器有预览模式，则切换到预览模式
        if (typeof editorComponent.togglePreview === 'function' && !editorComponent.isPreviewMode) {
          editorComponent.togglePreview()
        }
      }
    } catch (error) {
      console.error('初始化编辑器出错:', error)
    }
  }, 300)
}

// 接收对比结果的方法
const setCompareResults = (results: any[]) => {
  // 格式化日期时间并添加选中状态
  compareResults.value = results.map(item => ({
    ...item,
    selected: false,
    updateTime: item.updateTime || '-',
    createTime: item.createTime || '-',
    title: item.title || '无标题',
    content: item.content || '无内容',
    source: item.source || '-'
  }))
  
  compareLoading.value = false
  if (results.length > 0) {
    compareDialogVisible.value = true
  } else {
    ElMessage.info('未找到相似语料')
  }
}

// 暴露方法给父组件
defineExpose({
  setCompareResults
})

// 处理保存按钮点击
const handleSave = (data: any) => {
  // 确保保存前数据包含完整的信息
  const saveData = {
    ...data,
    rgId: props.rgId,
    misId: props.misId
  }
  emit('save', saveData)
  dialogVisible.value = false
}

// 监听props变化，更新内部状态
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.detailData) {
    // 深拷贝详情数据
    localData.value = JSON.parse(JSON.stringify(props.detailData))
    
    console.log('CorpusDetailDialog - 对话框打开，更新localData:', {
      hasDetailData: !!props.detailData,
      detailDataContent: props.detailData?.content?.substring(0, 50) + '...',
      localDataContent: localData.value?.content?.substring(0, 50) + '...',
      contentLength: props.detailData?.content?.length || 0
    })
  }
}, { immediate: true })

// 同时监听detailData的变化
watch(() => props.detailData, (newData) => {
  if (newData && props.visible) {
    localData.value = JSON.parse(JSON.stringify(newData))
    
    console.log('CorpusDetailDialog - detailData变化，更新localData:', {
      hasContent: !!newData.content,
      contentLength: newData.content?.length || 0,
      title: newData.title || '无标题'
    })
  }
}, { deep: true, immediate: true })

// 处理对话框状态变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal)
})

// 处理选择单项
const handleItemSelect = (item: any, selected: boolean) => {
  if (selected) {
    selectedItems.value.push(item)
  } else {
    const index = selectedItems.value.findIndex(i => i.ticketId === item.ticketId)
    if (index !== -1) {
      selectedItems.value.splice(index, 1)
    }
  }
}

// 处理对比对话框关闭
const handleCompareClose = () => {
  // 重置选中状态
  compareResults.value = compareResults.value.map(item => ({
    ...item,
    selected: false
  }))
  selectedItems.value = []
}

// 根据相似度获取样式类
const getSimilarityClass = (score: string) => {
  const numScore = parseInt(score.replace('%', ''))
  if (numScore >= 80) return 'high'
  if (numScore >= 60) return 'medium'
  return 'low'
}

// 处理合并选中项
const handleMergeSelected = async () => {
  // 检查是否有选中的语料
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请至少选择一条相似语料进行合并')
    return
  }

  // 检查是否选择了值班组
  if (!props.rgId) {
    ElMessage.error('未获取到团队ID')
    return
  }

  // 显示加载状态
  compareLoading.value = true
  ElMessage.info({
    message: '正在处理合并请求，请稍候...',
    duration: 2000
  })

  try {
    // 获取MonacoEditor实例中的内容
    let currentContent = localData.value.content || ''
    try {
      // 尝试通过DOM获取编辑器的当前内容
      const editorElement = document.querySelector('.corpus-detail-editor')
      if (editorElement && (editorElement as any).__vue__) {
        const editorComponent = (editorElement as any).__vue__
        if (editorComponent && editorComponent.value) {
          currentContent = editorComponent.value
        }
      }
    } catch (e) {
      console.warn('获取编辑器内容失败，使用默认内容', e)
    }

    // 调用合并语料工具函数，使用正确的参数
    const result = await mergeSelectedCorpus({
      compareSource: 'convert', // 知识转换场景设置为convert
      selectedRows: undefined, // 不需要selectedRows
      selectedCompareRows: selectedItems.value, // 当前选中的相似语料
      currentMisId: props.misId,
      currentTeam: props.rgId as number,
      convertFormData: {
        content: currentContent, // 当前编辑器中的内容
        ticketId: localData.value.ticketId || '' // 当前编辑语料的ticketId
      },
      loadingPanelInstance: null,
      formatTitle
    })

    if (result.success && result.data) {
      // 处理成功结果
      setTimeout(() => {
        compareLoading.value = false
        
        // 预览合并结果
        mergePreviewForm.value = {
          title: result.data?.title || '',
          content: result.data?.content || '',
          taskId: result.data?.taskId || '',
          ticketId: result.data?.ticketId || '',
          corpusIdList: result.data?.corpusIdList || [],
          tagsIds: result.data?.tagsIds || ''
        }
        
        // 显示预览确认框
        ElMessageBox.confirm(
          `<div>
            <div style="margin-bottom: 10px;"><strong>标题:</strong> ${mergePreviewForm.value.title}</div>
            <div style="margin-bottom: 10px;"><strong>内容:</strong></div>
            <div style="white-space: pre-wrap; background-color: #f5f7fa; padding: 10px; border-radius: 4px;">${mergePreviewForm.value.content}</div>
          </div>`,
          '确认合并语料',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确认合并',
            cancelButtonText: '取消',
            type: 'info'
          }
        ).then(async () => {
          // 用户确认合并
          await handleSaveMerge()
        }).catch(() => {
          // 用户取消合并
          ElMessage({
            type: 'info',
            message: '已取消合并'
          })
        })
      }, 500)
    } else {
      compareLoading.value = false
      ElMessage.error(result.error || '合并失败，请重试')
    }
  } catch (error: any) {
    compareLoading.value = false
    console.error('合并语料出错:', error)
    ElMessage.error(`合并语料失败: ${error.message || '未知错误'}`)
  }
}

// 保存合并结果
const handleSaveMerge = async () => {
  // 显示加载指示器
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在保存合并结果...',
    background: 'rgba(255, 255, 255, 0.9)'
  })

  try {
    // 构造请求体
    const requestBody = {
      ticketId: mergePreviewForm.value.ticketId,
      title: mergePreviewForm.value.title,
      content: mergePreviewForm.value.content,
      misId: props.misId,
      rgId: props.rgId,
      corpusIdList: mergePreviewForm.value.corpusIdList,
      tagsIds: mergePreviewForm.value.tagsIds || ''
    }

    const response = await httpRequest.rawRequestPostAsJson('/corpus/saveMergeCorpus', requestBody) as unknown as ApiResponse<any>

    if (response?.code === 0) {
      // 关闭抽屉
      compareDialogVisible.value = false
      
      // 重置选中状态
      selectedItems.value = []
      
      // 关闭加载提示
      loadingInstance.close()
      
      // 通知父组件刷新数据
      emit('refresh')
      
      // 显示成功提示
      ElMessage({
        type: 'success',
        message: '合并成功！',
        duration: 3000
      })
    } else {
      // 关闭加载提示
      loadingInstance.close()
      ElMessage.error(response?.msg || '保存失败')
    }
  } catch (error: any) {
    // 关闭加载提示
    loadingInstance.close()
    console.error('保存合并语料出错:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  }
}

// 处理批量删除选中项
const handleBatchDelete = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要删除的语料')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedItems.value.length} 条语料吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    deleteLoading.value = true
    
    // 获取选中行的ticketId列表
    const ticketIds = selectedItems.value.map(row => row.ticketId)
    
    // 构造URL参数
    const params = {
      misId: props.misId,
      rgId: props.rgId,
      ticketIds: ticketIds.join(',')
    }
    
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value as string)}`)
      .join('&')
    
    // 发送删除请求
    const response = await httpRequest.rawRequestPostAsJson(
      `/corpus/deleteCorpusByTicketIds?${queryString}`,
      null
    ) as unknown as ApiResponse<any>
    
    if (response?.code === 0) {
      ElMessage.success('批量删除成功')
      
      // 从检索结果中移除已删除的项
      compareResults.value = compareResults.value.filter(
        item => !ticketIds.includes(item.ticketId)
      )
      
      // 清空选中项
      selectedItems.value = []
      
      // 通知父组件刷新数据
      emit('refresh')
      
      // 如果删除后没有结果了，关闭对话框
      if (compareResults.value.length === 0) {
        compareDialogVisible.value = false
      }
    } else {
      ElMessage.error(`批量删除失败: ${response?.msg || '未知错误'}`)
    }
  } catch (error: any) {
    // 检查取消操作
    if (error === 'cancel' || error?.toString() === 'Error: cancel' || error?.message === 'cancel') {
      return
    }
    
    console.error('批量删除语料出错:', error)
    ElMessage.error(`批量删除失败: ${error.message || '未知错误'}`)
  } finally {
    deleteLoading.value = false
  }
}

// 处理关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 处理表单数据更新
const handleFormDataUpdate = (updatedData: any) => {
  localData.value = {
    ...localData.value,
    ...updatedData
  }
}

</script>

<style lang="scss" scoped>
/* 检索对比抽屉样式 */
.compare-dialog {
  position: relative;

  :deep(.el-drawer__header) {
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  :deep(.el-drawer__body) {
    padding: 20px;
    padding-bottom: 90px;
    overflow-y: auto;
  }

  :deep(.el-drawer__footer) {
    padding: 0;
  }

  .compare-header {
    margin-bottom: 20px;

    .compare-tip {
      font-size: 16px;
      color: #303133;
      line-height: 1.6;

      .sub-tip {
        margin-top: 8px;
        font-size: 14px;
        color: #909399;
        line-height: 1.5;
      }
    }
  }

  .compare-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .compare-item {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      border: 1px solid #e4e7ed;
      position: relative;
      z-index: 1;

      .compare-item-header {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;

        .left-section {
          display: flex;
          align-items: center;
          gap: 12px;

          :deep(.el-checkbox) {
            margin-right: 0;
            z-index: 2;
          }

          .task-id {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
          }
        }

        .similarity-score {
          display: inline-block;
          padding: 2px 12px;
          border-radius: 12px;
          font-weight: 500;
          font-size: 13px;
          
          &.high {
            background-color: #e8f5e9;
            color: #4caf50;
          }
          
          &.medium {
            background-color: #fff3e0;
            color: #ff9800;
          }
          
          &.low {
            background-color: #ffebee;
            color: #f44336;
          }
        }

        .actions {
          .el-button {
            font-size: 13px;
            color: #409eff;
          }
        }
      }

      .compare-item-content {
        padding: 16px;
        
        .content-text {
          font-size: 14px;
          line-height: 1.6;
          color: #303133;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }
      }

      .compare-item-footer {
        padding: 12px 16px;
        border-top: 1px solid #f0f0f0;
        background: #fafafa;

        .update-time {
          font-size: 13px;
          color: #909399;
        }
      }
    }

    .no-data {
      text-align: center;
      padding: 40px 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px 20px;
    background: #fff;
    border-top: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 10;
    
    .selected-count {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }

    .footer-buttons {
      display: flex;
      gap: 12px;
      
      .merge-btn {
        &.is-disabled {
          color: #c0c4cc;
          background-color: #fff;
          border-color: #e4e7ed;
        }
      }
      
      .delete-btn {
        &.is-disabled {
          color: #c0c4cc;
          background-color: #fff;
          border-color: #e4e7ed;
        }
      }
    }
  }
}

.view-dialog {
  :deep(.el-dialog__header) {
    padding: 16px 20px;
    margin-right: 0;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-dialog__body) {
    padding: 20px 30px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
  }
}

.view-corpus-editor {
  width: 100%;
  min-height: 320px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;

  &:deep(.editor-toolbar) {
    .reset-button {
      display: none;
    }
  }
}

// 加载蒙层样式
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  
  .is-loading {
    font-size: 32px;
    color: #409EFF;
    margin-bottom: 16px;
  }
  
  span {
    font-size: 14px;
    color: #606266;
  }
}

/* 标签管理相关样式 */
.tag-management-section {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border: 1px solid #e1e8ff;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #409EFF 0%, #67C23A 50%, #E6A23C 100%);
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      
      .header-icon {
        color: #409EFF;
        font-size: 18px;
      }
    }
    
    .tag-limit-tip {
      background: #ecf5ff;
      color: #409EFF;
      border: 1px solid #b3d8ff;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

.current-tags-display {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f9f5 0%, #e8f5e8 100%);
  border-radius: 8px;
  border: 1px solid #b3e5c7;
  
  .current-tags-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #2d5016;
    margin-bottom: 12px;
    
    .section-icon {
      color: #67C23A;
      font-size: 16px;
    }
  }
  
  .current-tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .current-tag {
      border-radius: 6px;
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      border: 1px solid #5daf34;
      color: #fff;
      font-weight: 500;
      padding: 0 12px;
      height: 28px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 4px rgba(103, 194, 58, 0.2);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
      }
    }
  }
}

.tag-selection-area {
  .default-tag-display {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e1ecff 100%);
    border-radius: 8px;
    border: 1px solid #b3d8ff;
    
    .default-tag {
      flex-shrink: 0;
      background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
      border-color: #409EFF;
      color: #fff;
      font-weight: 500;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
    }
    
    .default-tag-tip {
      font-size: 13px;
      color: #606266;
      font-weight: 500;
    }
  }
  
  :deep(.el-select) {
    margin-bottom: 16px;
    
    .el-select__wrapper {
      border-radius: 8px;
      border: 2px solid #e4e7ed;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      
      &:hover {
        border-color: #c0c4cc;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
      
      &.is-focused {
        border-color: #409EFF;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }
    
    .el-select__wrapper {
      min-height: 36px;
    }
    
    .el-tag {
      margin: 2px 4px 2px 0;
    }
  }
  
  .selected-tags-preview {
    margin-top: 16px;
    padding: 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e1ecff 100%);
    border-radius: 8px;
    border: 1px solid #b3d8ff;
    
    .preview-title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 12px;
    }
    
    .selected-tags-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .selected-tag {
        border-radius: 6px;
        background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
        border: 1px solid #409EFF;
        color: #fff;
        font-weight: 500;
        padding: 0 12px;
        height: 28px;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
        }
        
        :deep(.el-tag__close) {
          color: #fff;
          font-weight: bold;
          margin-left: 6px;
          
          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
          }
        }
      }
    }
  }
}

</style> 