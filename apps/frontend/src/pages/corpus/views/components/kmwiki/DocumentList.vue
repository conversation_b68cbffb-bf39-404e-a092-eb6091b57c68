<template>
  <div class="document-list-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" @click="handleSwitchToWebsiteUpload">
          <el-icon><Plus /></el-icon>添加文档
        </el-button>
        <el-button type="success" @click="handleRefresh">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button type="danger" @click="handleBatchDelete" :disabled="selectedRows.length === 0">
          <el-icon><Delete /></el-icon>批量删除
        </el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="输入关键词搜索"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch" :icon="Search"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <el-table 
      :data="documentList" 
      border 
      stripe 
      style="width: 100%; margin-top: 15px;"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="35" />
      <el-table-column prop="name" label="文档名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="url" label="URL地址" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <el-link type="primary" :href="row.url" target="_blank" :underline="false">
            {{ row.url }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="autoUpdate" label="自动更新" width="90" align="center">
        <template #default="{ row }">
          <el-tag :type="row.autoUpdate ? 'success' : 'info'">
            {{ row.autoUpdate ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="misId" label="操作人" width="120" />
      <el-table-column prop="createTime" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-popconfirm
            title="确定删除此文档吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            @confirm="handleDelete(row)"
          >
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>
    
    <!-- 添加/编辑文档对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑文档' : '添加文档'" 
      width="600px"
      :before-close="closeDialog"
    >
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="文档名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入文档名称" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="URL地址" prop="url">
          <el-input v-model="formData.url" placeholder="请输入URL地址" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="自动更新" prop="autoUpdate">
          <el-switch v-model="formData.autoUpdate" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, defineProps, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, Delete } from '@element-plus/icons-vue'
import httpRequest from '../../../../../utils/httpRequest'
import { API_PATHS } from '../../../request/api'

// 添加未定义的API路径常量
const DELETE_DOCUMENT = 'km/deleteByDocumentId'

// 格式化日期时间
const formatDateTime = (dateStr: string): string => {
  if (!dateStr) return '--';
  
  try {
    const date = new Date(dateStr);
    // 检查日期是否有效
    if (isNaN(date.getTime())) return dateStr;
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    return dateStr;
  }
}

// 定义学城文档数据接口
interface DocumentItem {
  id: number
  rgId: number
  documentId: string
  name: string
  url: string
  misId: string
  createTime: string
  autoUpdate: number
}

// 定义HTTP响应数据类型
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 定义组件接收的属性
const props = defineProps({
  rgId: {
    type: [Number, String],
    required: true
  },
  misId: {
    type: String,
    required: true
  }
})

// 定义组件发出的事件
const emit = defineEmits(['next-step', 'switch-to-website'])

// 加载状态
const loading = ref(false)

// 搜索关键词
const searchKeyword = ref('')

// 文档列表数据
const documentList = ref<DocumentItem[]>([])

// 分页数据
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  totalPage: 1
})

// 对话框可见性
const dialogVisible = ref(false)

// 是否为编辑模式
const isEdit = ref(false)

// 当前编辑的文档ID
const currentEditId = ref<number | null>(null)

// 表单引用
const formRef = ref<any>(null)

// 表单数据
const formData = reactive({
  name: '',
  url: '',
  autoUpdate: 0
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入文档名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入URL地址', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: 'URL必须以http://或https://开头', trigger: 'blur' }
  ]
}

// 选中的行数据
const selectedRows = ref<any[]>([])

// 获取文档列表数据
const fetchDocumentList = async (isSearch = false) => {
  loading.value = true
  try {
    // 检查是否有有效的rgId和misId
    const rgId = props.rgId
    const misId = props.misId || ''
    
    
    // 检查是否有有效的rgId
    if (rgId === null || rgId === undefined) {
      console.warn('未提供有效的团队ID(rgId)参数，无法获取文档列表')
      ElMessage.warning('缺少必要的团队ID参数，请从语料管理页面跳转进入')
      loading.value = false
      return
    }
    
    // 检查是否有有效的misId
    if (!misId) {
      console.warn('缺少必要的用户ID(misId)参数，无法获取文档列表')
      ElMessage.warning('缺少必要的用户ID参数，请从语料管理页面跳转进入')
      loading.value = false
      return
    }
    
    // 构建请求参数
    const params = {
      rgId,
      misId,
      strMatch: searchKeyword.value.trim(),
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize
    }
    
    // 发送GET请求获取文档列表
    const response = await httpRequest.rawRequestGet(API_PATHS.QUERY_DOCUMENT_BY_RG_ID, params) as unknown as ApiResponse
    
    
    // httpRequest 返回的response直接就是后端返回的数据结构
    // { code: number, message: string, data: object }
    if (response && response.code === 0) {
      // 请求成功
      const responseData = response.data || {}
      documentList.value = responseData.list || []
      pagination.value.total = responseData.total || 0
      pagination.value.totalPage = responseData.totalPage || 1
      pagination.value.pageNum = responseData.pageNum || 1
      pagination.value.pageSize = responseData.pageSize || 10
      
    } else {
      // 请求失败
      const errorMsg = response?.message || '未知错误'
      console.error('获取文档列表失败:', errorMsg)
      ElMessage.error(`获取文档列表失败: ${errorMsg}`)
    }
  } catch (error: any) {
    console.error('获取文档列表出错:', error)
    ElMessage.error(`获取文档列表失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

// 处理添加文档
const handleAddDocument = () => {
  isEdit.value = false
  currentEditId.value = null
  resetForm()
  dialogVisible.value = true
}

// 切换到网站知识上传
const handleSwitchToWebsiteUpload = () => {
  emit('switch-to-website')
}

// 处理编辑文档
const handleEdit = (row: DocumentItem) => {
  isEdit.value = true
  currentEditId.value = row.id
  // 设置表单数据
  formData.name = row.name
  formData.url = row.url
  formData.autoUpdate = row.autoUpdate
  dialogVisible.value = true
}

// 处理删除文档
const handleDelete = async (row: DocumentItem) => {
  try {
    // 开始删除
    loading.value = true
    
    // 获取有效的rgId和misId
    const rgId = props.rgId
    const misId = props.misId || ''
    
    // 检查是否有有效的rgId
    if (rgId === null || rgId === undefined) {
      ElMessage.error('未提供有效的团队ID(rgId)参数')
      loading.value = false
      return
    }
    
    // 构建URL参数
    const url = `${DELETE_DOCUMENT}?rgId=${rgId}&misId=${misId}`
    
    // 调用删除API，发送包含单个documentId的数组作为请求体
    const response = await httpRequest.rawRequestPostAsJson(url, [row.documentId]) as any
    
    if (response && response.code === 0) {
      // 检查是否有删除失败的文档
      if (response.data && response.data.failDoc && response.data.failDoc.length > 0) {
        ElMessage.error('删除失败')
      } else {
        ElMessage.success(response.msg || '删除成功')
      }
      
      // 刷新列表
      await fetchDocumentList()
    } else {
      ElMessage.error(response?.msg || '删除失败，请稍后重试')
    }
  } catch (error: any) {
    console.error('删除文档失败:', error)
    ElMessage.error(error?.message || '删除失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理刷新
const handleRefresh = () => {
  fetchDocumentList()
}

// 处理搜索
const handleSearch = () => {
  pagination.value.pageNum = 1 // 重置到第一页
  fetchDocumentList(true)
}

// 处理分页大小变更
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  fetchDocumentList()
}

// 处理当前页变更
const handleCurrentChange = (val: number) => {
  pagination.value.pageNum = val
  fetchDocumentList()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (isEdit.value) {
          // 编辑模式下，只允许修改自动更新状态
          loading.value = true
          
          // 获取有效的rgId和misId
          const rgId = props.rgId
          const misId = props.misId || ''
          
          // 检查是否有有效的rgId
          if (rgId === null || rgId === undefined) {
            ElMessage.error('未提供有效的团队ID(rgId)参数')
            loading.value = false
            return
          }
          
          // 准备请求参数
          const documentToUpdate = documentList.value.find(item => item.id === currentEditId.value)
          
          if (!documentToUpdate || !documentToUpdate.url) {
            throw new Error('找不到文档URL')
          }
          
          // 准备表单数据，后端使用@RequestParam接收
          const formParams = {
            url: documentToUpdate.url,
            autoUpdate: formData.autoUpdate,
            rgId,
            misId
          }
          
          
          // 使用rawRequestPostAsForm发送表单数据
          const response = await httpRequest.rawRequestPostAsForm(
            API_PATHS.CHANGE_REFRESH_BY_DOCUMENT_ID,
            formParams
          ) as any
          
          if (response && response.code === 0) {
            // 更新本地数据
            const index = documentList.value.findIndex(item => item.id === currentEditId.value)
            if (index > -1) {
              documentList.value[index] = {
                ...documentList.value[index],
                autoUpdate: formData.autoUpdate
              }
            }
            
            ElMessage.success(response.msg || '更新成功')
          } else {
            ElMessage.error(response?.msg || '更新失败，请稍后重试')
          }
        } else {
          // 添加模式保持不变
          // TODO: 实现实际的添加API调用
          
          ElMessage.success('添加成功')
          // 添加到本地列表
          documentList.value.push({
            id: Date.now(),
            rgId: Number(props.rgId),
            documentId: `doc${Math.floor(Math.random() * 1000000)}`,
            name: formData.name,
            url: formData.url,
            misId: String(props.misId),
            createTime: new Date().toLocaleString(),
            autoUpdate: formData.autoUpdate
          })
        }
        
        // 关闭对话框并移除焦点
        closeDialog()
      } catch (error: any) {
        console.error('保存文档失败:', error)
        ElMessage.error(error?.message || '操作失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
  })
}

// 关闭对话框并移除按钮焦点
const closeDialog = () => {
  dialogVisible.value = false
  // 移除所有按钮的焦点
  document.querySelectorAll('button').forEach(btn => {
    btn.blur()
  })
  // 更安全的方式：移除当前活动元素的焦点
  if (document.activeElement instanceof HTMLElement) {
    document.activeElement.blur()
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.name = ''
  formData.url = ''
  formData.autoUpdate = 0
}

// 在组件挂载时获取数据
onMounted(() => {
  console.log('DocumentList组件挂载，当前props:', {
    rgId: props.rgId,
    misId: props.misId
  });
  
  // 仅当有有效参数时发送请求
  if (props.rgId && props.misId) {
    fetchDocumentList();
  } else {
    console.warn('DocumentList组件挂载时缺少参数，等待路由参数更新后自动获取数据');
  }
});

// 监听props变化，当rgId或misId变化时重新获取数据
watch(
  () => [props.rgId, props.misId],
  ([newRgId, newMisId], [oldRgId, oldMisId]) => {
    console.log('DocumentList props变化:', {
      新值: { rgId: newRgId, misId: newMisId },
      旧值: { rgId: oldRgId, misId: oldMisId }
    });
    
    // 只有当新的rgId和misId都有值，且其中之一发生变化时，才重新获取数据
    if (newRgId && newMisId && (newRgId !== oldRgId || newMisId !== oldMisId)) {
      fetchDocumentList();
    }
  }
);

// 处理表格选择改变
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 处理批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的文档')
    return
  }
  
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条文档吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 开始删除
    loading.value = true
    
    // 获取选中文档的documentId列表
    const documentIds = selectedRows.value.map(row => row.documentId)
    
    // 获取有效的rgId和misId
    const rgId = props.rgId
    const misId = props.misId || ''
    
    // 检查是否有有效的rgId
    if (rgId === null || rgId === undefined) {
      ElMessage.error('未提供有效的团队ID(rgId)参数')
      loading.value = false
      return
    }
    
    // 构建URL参数
    const url = `${DELETE_DOCUMENT}?rgId=${rgId}&misId=${misId}`
    
    // 调用删除API，直接发送documentIds数组作为请求体
    const response = await httpRequest.rawRequestPostAsJson(url, documentIds) as any
    
    if (response && response.code === 0) {
      // 检查是否有删除失败的文档
      if (response.data && response.data.failDoc && response.data.failDoc.length > 0) {
        // 有部分文档删除失败
        const failCount = response.data.failDoc.length
        const successCount = response.data.success || 0
        const totalCount = response.data.total || documentIds.length
        
        ElMessage.warning(`${response.msg || '存在未删除成功的文档'}：共 ${totalCount} 个，成功 ${successCount} 个，失败 ${failCount} 个`)
      } else {
        // 全部删除成功
        ElMessage.success(response.msg || '全部删除成功')
      }
      
      // 清空选中行
      selectedRows.value = []
      
      // 刷新列表
      await fetchDocumentList()
    } else {
      ElMessage.error(response?.msg || '删除失败，请稍后重试')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量删除文档失败:', error)
      ElMessage.error(error?.message || '删除失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.document-list-container {
  padding: 20px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #303133;
  }
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    
    .left-actions {
      display: flex;
      gap: 10px;
      
      .el-button {
        margin-left: 0;
      }
    }
    
    .right-actions {
      .search-input {
        width: 300px;
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
    padding: 10px 0;
    z-index: 10;
  }
  
  // 移除表格操作栏按钮焦点状态的蓝色轮廓
  :deep(.el-table) {
    .el-button {
      &:focus-visible {
        outline: none;
        box-shadow: none;
      }
      
      &:focus {
        outline: none;
        box-shadow: none;
      }
    }
  }
}

// 全局移除按钮焦点样式
:deep(.el-button) {
  &:focus-visible, &:focus {
    outline: none !important;
    box-shadow: none !important;
  }
}
</style> 