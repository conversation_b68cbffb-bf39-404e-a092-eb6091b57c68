<template>
  <div class="tips-section">
    <div class="tip-icon">
      <el-icon><InfoFilled /></el-icon>
    </div>
    <div class="tips-content">
      <div class="tip-item">1.学城文档支持上传C2、C3等级文档，且上传需具备管理权限；</div>
      <div class="tip-item">2.用户若无浏览权限，则无法应用知识回答用户问题。</div>
      <div class="tip-item">3.建议按照FAQ格式设置学习标题与内容，提升识别效果</div>
      <div class="tip-item">4.背景知识上传功能需要先创建自定义空间，<a href="https://km.sankuai.com/collabpage/2708110417#b-7db99856020d4bdf90bd97be217abc08" target="_blank" class="reference-link">使用手册</a></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.tips-section {
  display: flex;
  align-items: flex-start;
  background-color: #f0f9ff;
  padding: 12px;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  
  .tip-icon {
    color: #1890ff;
    margin-right: 10px;
    margin-top: 2px;
    flex-shrink: 0;
  }
  
  .tips-content {
    flex: 1;
    min-width: 420px;
    
    .tip-item {
      margin-bottom: 6px;
      font-size: 13px;
      word-break: break-word;
      
      &:last-child {
        margin-bottom: 0;
      }

      .reference-link {
        color: #1890ff;
        text-decoration: underline;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}

@media (max-width: 500px) {
  .tips-section {
    .tips-content {
      min-width: 0;
    }
  }
}
</style> 