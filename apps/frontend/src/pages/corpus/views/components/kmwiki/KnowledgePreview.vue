<template>
  <div class="knowledge-preview-container">
    <div class="preview-header">
      <h3>{{ title }}</h3>
      <div class="header-actions">
        <div class="preview-actions">
          <el-button @click="handlePrevStep" type="default">上一步</el-button>
          <el-button 
            type="primary" 
            @click="handleNextStep"
            :disabled="selectedSegments.length === 0"
          >
            下一步
          </el-button>
        </div>
        <div class="segment-count">
          分段数：{{ segments.length }} | 已选：{{ selectedSegments.length }}
        </div>
        <el-checkbox 
          v-model="isAllSelected"
          @change="handleSelectAll"
          :disabled="!segments.length"
        >
          全选
        </el-checkbox>
      </div>
    </div>
    
    <div class="segments-list">
      <div 
        v-for="(segment, index) in segments" 
        :key="index"
        class="segment-item"
        :class="{ 'selected': isSegmentSelected(index) }"
        @click="handleSegmentClick(segment, index)"
      >
        <div class="segment-header">
          <div class="segment-checkbox">
            <el-checkbox 
              v-model="selectedIndices[index]" 
              @click.stop
            />
          </div>
          <div class="segment-number">{{ String(index + 1).padStart(2, '0') }}</div>
          <div class="segment-type">doc</div>
          <div class="segment-chars">{{ getCharCount(segment) }}字符</div>
        </div>
        
        <div v-if="getSegmentPath(segment)" class="segment-path">
          {{ getSegmentPath(segment) }}
        </div>
        
        <div class="segment-content">
          {{ formatSegmentContent(segment) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 定义组件接收的属性
const props = defineProps({
  title: {
    type: String,
    default: 'FAQ'
  },
  segments: {
    type: Array as () => string[],
    default: () => []
  },
  initialSelectedSegments: {
    type: Array as () => string[],
    default: () => []
  }
})

// 定义事件
const emit = defineEmits<{
  (e: 'select-segment', segment: string, index: number): void
  (e: 'selected-segments-change', selectedSegments: string[]): void
  (e: 'prev-step'): void
  (e: 'next-step'): void
}>()

// 选中的段落索引
const selectedIndices = ref<Record<number, boolean>>({})

// 计算已选中的段落
const selectedSegments = computed(() => {
  return props.segments.filter((_, index) => selectedIndices.value[index])
})

// 在组件挂载时初始化选中状态
onMounted(() => {
  initializeSelectedIndices()
})

// 当segments发生变化时重新初始化选中状态
watch(() => props.segments, () => {
  initializeSelectedIndices()
}, { deep: true })

// 初始化选中状态的函数
const initializeSelectedIndices = () => {
  // 清空当前选中状态
  selectedIndices.value = {}
  
  // 从initialSelectedSegments恢复选中索引
  if (props.initialSelectedSegments && props.initialSelectedSegments.length > 0) {
    props.segments.forEach((segment, index) => {
      if (props.initialSelectedSegments.includes(segment)) {
        selectedIndices.value[index] = true
      }
    })
    
    console.log('已恢复选中段落数量:', Object.values(selectedIndices.value).filter(v => v).length)
  }
}

// 判断段落是否被选中
const isSegmentSelected = (index: number) => {
  return !!selectedIndices.value[index]
}

// 监听选中段落的变化
watch(selectedSegments, (newSegments) => {
  // 向父组件通知选中段落的变化
  emit('selected-segments-change', newSegments)
})

// 获取段落字符计数
const getCharCount = (segment: string): string => {
  return segment.length.toString()
}

// 获取段落路径（换行符前的内容）
const getSegmentPath = (segment: string): string => {
  // 查找第一个换行符
  const firstLineEnd = segment.indexOf('\n')
  
  // 如果找到换行符，返回换行符前的内容作为路径
  if (firstLineEnd !== -1) {
    return segment.substring(0, firstLineEnd).trim()
  }
  
  // 如果没有换行符，返回空字符串（没有路径信息）
  return ''
}

// 获取段落内容（换行符后的内容）
const getSegmentContent = (segment: string): string => {
  // 查找第一个换行符
  const firstLineEnd = segment.indexOf('\n')
  
  // 如果找到换行符，返回换行符后的内容
  if (firstLineEnd !== -1) {
    return segment.substring(firstLineEnd + 1).trim()
  }
  
  // 如果没有换行符，返回原始内容（没有内容部分）
  return segment
}

// 格式化段落内容，处理特殊情况
const formatSegmentContent = (segment: string): string => {
  const content = getSegmentContent(segment)
  
  // 如果内容过长，只显示前150个字符
  if (content.length > 250) {
    return content.substring(0, 250) + '...'
  }
  return content
}

// 处理段落点击事件
const handleSegmentClick = (segment: string, index: number) => {
  // 切换选中状态
  selectedIndices.value[index] = !selectedIndices.value[index]
  
  // 触发点击事件
  emit('select-segment', segment, index)
}

// 处理上一步
const handlePrevStep = () => {
  emit('prev-step')
}

// 处理下一步
const handleNextStep = () => {
  if (selectedSegments.value.length === 0) {
    ElMessage.warning('请至少选择一个知识段落')
    return
  }
  
  emit('next-step')
}

// 是否全选
const isAllSelected = computed(() => {
  if (!props.segments.length) return false
  return props.segments.every((_, index) => selectedIndices.value[index])
})

// 处理全选/取消全选
const handleSelectAll = (checked: boolean) => {
  props.segments.forEach((_, index) => {
    selectedIndices.value[index] = checked
  })
}

// 暴露方法给父组件
defineExpose({
  getSelectedSegments: () => selectedSegments.value
})
</script>

<style lang="scss" scoped>
.knowledge-preview-container {
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  
  .preview-header {
    position: sticky;
    top: 0;
    background-color: #fff;
    padding: 5px 0;
    z-index: 10;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: #303133;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .preview-actions {
        display: flex;
        gap: 8px;
        
        .el-button {
          padding: 6px 12px;
          font-size: 13px;
          height: 32px;
        }
      }
      
      .segment-count {
        font-size: 14px;
        color: #909399;
      }
    }
  }
  
  .segments-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow-y: auto;
    flex: 1;
    
    .segment-item {
      padding: 16px;
      background-color: #f5f7fa;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      
      &:hover {
        background-color: #ecf5ff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      }
      
      &.selected {
        border-color: #409EFF;
        background-color: rgba(64, 158, 255, 0.1);
      }
      
      .segment-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        .segment-checkbox {
          margin-right: 8px;
          
          :deep(.el-checkbox__input) {
            vertical-align: middle;
          }
        }
        
        .segment-number {
          background-color: #f0f2f5;
          color: #606266;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 14px;
          margin-right: 8px;
        }
        
        .segment-type {
          background-color: #e6f7ff;
          color: #1890ff;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 14px;
          margin-right: 8px;
        }
        
        .segment-chars {
          margin-left: auto;
          color: #909399;
          font-size: 13px;
        }
      }
      
      .segment-path {
        color: #409EFF;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        background-color: rgba(64, 158, 255, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
        display: inline-block;
      }
      
      .segment-content {
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
        word-break: break-word;
        white-space: pre-wrap;
      }
    }
  }
}
</style> 