<template>
  <div class="steps-selector">
    <el-button-group>
      <el-button 
        :type="activeStep === 'qa' ? 'primary' : 'default'"
        @click="handleStepChange('qa')"
      >
        FAQ知识上传
      </el-button>
      <el-button 
        :type="activeStep === 'website' ? 'primary' : 'default'"
        @click="handleStepChange('website')"
      >
        背景知识上传
      </el-button>
      <el-button 
        :type="activeStep === 'document' ? 'primary' : 'default'"
        @click="handleStepChange('document')"
      >
        背景知识列表
      </el-button>
    </el-button-group>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  activeStep: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:activeStep'])

const handleStepChange = (step: string) => {
  emit('update:activeStep', step)
}
</script>

<style lang="scss" scoped>
.steps-selector {
  margin: 20px 0;
  display: flex;
  justify-content: center;
  
  .el-button-group {
    .el-button {
      padding: 10px 20px;
      font-size: 15px;
    }
  }
}
</style> 