<template>
  <div class="action-footer">
    <el-button v-if="activeStep > 1" @click="$emit('prev-step')" type="default">上一步</el-button>
    <el-button 
      v-if="!finalStep"
      type="primary" 
      @click="handleNextClick"
      :disabled="isNextDisabled"
    >
      {{ activeStep < 3 ? '下一步' : '确认并完成' }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  activeStep: number,
  isNextDisabled?: boolean,
  finalStep?: boolean
}>()

const emit = defineEmits<{
  (e: 'prev-step'): void
  (e: 'next-step'): void
}>()

// 处理下一步按钮点击
const handleNextClick = () => {
  emit('next-step')
}
</script>

<style lang="scss" scoped>
.action-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 25px;
  width: 100%;
  box-sizing: border-box;
  
  :deep(.el-button) {
    &.is-disabled {
      cursor: not-allowed;
      opacity: 0.7;
    }
  }
}
</style> 