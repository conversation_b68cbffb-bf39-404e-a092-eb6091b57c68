<template>
  <div class="upload-content">
    <div class="website-upload">
      <div class="form-row">
        <div class="form-item website-item">
          <div class="form-label required">学城网址</div>
          <el-input 
            v-model="formData.website" 
            placeholder="请粘贴学城文档网址，部分学城文档由于权限及安全原因可能无法解析" 
            clearable
            @input="updateUrl"
            @blur="validateAndFetchMetadata"
            :loading="loading"
            :class="{ 'is-error': urlFormatError || isUrlParseFailed, 'is-success': isUrlParseSuccess }"
          />
          <div v-if="urlFormatError" class="error-message">{{ urlFormatError }}</div>
          <div v-else-if="isNoPermission" class="error-message">{{ responseMessage || '您没有权限访问该文档' }}</div>
          <div v-else-if="isUrlParseError" class="error-message">{{ responseMessage || 'URL解析失败' }}</div>
          <div v-else-if="isUrlParseSuccess" class="success-message">{{ responseMessage || 'URL解析成功，可以点击下一步继续' }}</div>
        </div>

        <div class="form-item name-item">
          <div class="form-label">文档名称</div>
          <el-input 
            v-model="formData.name" 
            placeholder="" 
            clearable
            :maxlength="200"
            show-word-limit
            disabled
          />
        </div>
        
        <!-- 表格内容勾选框，放在文档名称右侧 -->
        <div class="form-item table-option-item">
          <div class="form-label">文档类型</div>
          <div class="table-checkbox-wrapper">
            <el-checkbox v-model="isTable" class="table-checkbox">
              主体为表格内容
            </el-checkbox>
            <el-tooltip content="针对表格为主的文档，勾选后将以表格形式解析内容" placement="top">
              <el-icon class="help-icon"><question-filled /></el-icon>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 下一步按钮 -->
    <div class="next-step-container">
      <el-button 
        type="primary" 
        @click="handleNextStep"
        :disabled="!isUrlParseSuccess"
        :loading="loading"
      >
        下一步
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed, onMounted } from 'vue'
import httpRequest from '@/utils/httpRequest'
import { API_PATHS } from '@/pages/corpus/request/api'
import { getCurrentUser } from '@/shared/services/userService'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { QuestionFilled } from '@element-plus/icons-vue'

// 定义组件接收的属性
const props = defineProps({
  initialUrl: {
    type: String,
    default: ''
  },
  initialContentId: {
    type: Number,
    default: null
  },
  rgId: {
    type: [Number, String],
    default: null
  },
  misId: {
    type: String,
    default: ''
  },
  initialIsTable: {
    type: Boolean,
    default: false
  }
})

// 表单数据
const formData = reactive({
  website: '',
  name: ''
})

// 存储URL
const url = ref('')

// 加载状态
const loading = ref(false)

// 存储内容ID
const contentId = ref<number | null>(null)

// URL解析状态
const isUrlParseSuccess = ref(false)

// URL解析是否失败
const isUrlParseFailed = ref(false)

// URL格式错误信息
const urlFormatError = ref('')

// 响应消息
const responseMessage = ref('')

// 无权限状态
const isNoPermission = ref(false)

// 是否为表格内容
const isTable = ref(false)

// 计算属性：URL是否解析失败
const isUrlParseError = computed(() => {
  return isUrlParseFailed.value && formData.website.trim() !== '' && !isNoPermission.value;
})

// 定义事件
const emit = defineEmits<{
  (e: 'parse-status-change', success: boolean): void
  (e: 'next-step'): void
}>()

// 初始化数据
onMounted(() => {
  // 如果有初始URL和内容ID，则恢复之前的状态
  if (props.initialUrl) {
    formData.website = props.initialUrl
    url.value = props.initialUrl
    
    if (props.initialContentId) {
      contentId.value = props.initialContentId
      isUrlParseSuccess.value = true
      
      // 重新获取元数据以恢复名称
      validateAndFetchMetadata()
    }
  }
  
  // 恢复文档类型勾选状态
  if (props.initialIsTable) {
    isTable.value = props.initialIsTable
  }
})

// 检查是否为有效的URL
const isValidUrl = (url: string): boolean => {
  // 基本URL格式验证，检查是否包含http(s)协议和域名
  try {
    const urlObj = new URL(url.trim());
    return Boolean(urlObj.protocol && urlObj.host);
  } catch (e) {
    return false;
  }
}

// 校验URL
const validateUrl = (urlStr: string): boolean => {
  if (!urlStr || urlStr.trim() === '') {
    urlFormatError.value = '网址不能为空'
    return false
  }
  
  if (!isValidUrl(urlStr)) {
    urlFormatError.value = '请输入有效的网址格式'
    return false
  }
  
  urlFormatError.value = ''
  return true
}

// 验证URL并获取元数据
const validateAndFetchMetadata = async () => {
  // 先清除之前的错误状态
  urlFormatError.value = ''
  
  // 如果URL为空，不发送请求
  if (!url.value.trim()) {
    contentId.value = null
    isUrlParseSuccess.value = false
    isUrlParseFailed.value = false
    isNoPermission.value = false
    return
  }
  
  // 校验URL格式
  if (!validateUrl(url.value)) {
    // URL格式校验失败，设置状态并返回
    contentId.value = null
    isUrlParseSuccess.value = false
    isUrlParseFailed.value = true
    isNoPermission.value = false
    emit('parse-status-change', false)
    return
  }
  
  // URL格式验证通过，继续获取元数据
  await fetchMetadata()
}

// 获取元数据
const fetchMetadata = async () => {
  try {
    loading.value = true
    isUrlParseFailed.value = false
    isNoPermission.value = false
    
    // 重置响应消息
    responseMessage.value = ''
    
    // 优先使用props中传入的misId
    let misIdToUse = props.misId;
    // 优先使用props中传入的rgId
    let rgIdToUse = props.rgId ? Number(props.rgId) : null;
    
    // 如果props中没有提供misId，则从getCurrentUser获取
    if (!misIdToUse) {
      // 获取当前用户信息
      const userInfo = await getCurrentUser()
      if (!userInfo) {
        ElMessage.error('获取用户信息失败')
        return
      }
      misIdToUse = userInfo.login
    }
    
    // 如果props没有提供rgId，则从当前路由获取
    if (rgIdToUse === null) {
      const route = useRouter().currentRoute.value
      rgIdToUse = route.query.rgId ? Number(route.query.rgId) : null
    }
    
    // 如果没有获取到有效的rgId，则显示警告
    if (rgIdToUse === null) {
      ElMessage.warning('没有获取到有效的团队ID(rgId)参数')
      loading.value = false
      isUrlParseFailed.value = true
      emit('parse-status-change', false)
      return
    }
    
    // 构建请求参数
    const params = {
      url: url.value,
      rgId: rgIdToUse,
      misId: misIdToUse
    }
    
    
    // 发送GET请求获取元数据
    const response = await httpRequest.rawRequestGet(API_PATHS.GET_KM_META_BY_URL, params)
    
    // 保存响应消息
    if (response.msg) {
      responseMessage.value = response.msg
    }
    
    // 处理响应数据
    if (response.code === 0 && response.data) {
      // 首先检查是否有权限问题
      if (response.data.canAdd === false) {
        // 设置为无权限状态
        formData.name = ''
        contentId.value = null
        isUrlParseSuccess.value = false
        isUrlParseFailed.value = false
        isNoPermission.value = true
        
        // 如果没有响应消息但有原因，使用原因作为消息
        if (!responseMessage.value && response.data.reason) {
          responseMessage.value = response.data.reason
        } else if (!responseMessage.value) {
          responseMessage.value = '文档无管理权限，无法添加'
        }
        
        ElMessage.warning(responseMessage.value)
      } 
      // 检查是否有内容标题
      else if (response.data.contentTitle) {
        contentId.value = response.data.contentId || null
        formData.name = response.data.contentTitle
        isUrlParseSuccess.value = true
        isUrlParseFailed.value = false
        isNoPermission.value = false
        
        if (!responseMessage.value) {
          responseMessage.value = '解析学城文档基本信息成功'
        }
        
        ElMessage.success(responseMessage.value)
      } 
      // 没有内容标题或其他问题
      else {
        formData.name = ''
        contentId.value = null
        isUrlParseSuccess.value = false
        isUrlParseFailed.value = true
        isNoPermission.value = false
        
        // 如果没有响应消息但有原因，使用原因作为消息
        if (!responseMessage.value && response.data.reason) {
          responseMessage.value = response.data.reason
        } else if (!responseMessage.value) {
          responseMessage.value = 'URL解析失败，未获取到文档标题'
        }
        
        ElMessage.error(responseMessage.value)
      }
    } else {
      // 解析失败，显示url解析失败
      formData.name = ''
      contentId.value = null
      isUrlParseSuccess.value = false
      isUrlParseFailed.value = true
      isNoPermission.value = false
      
      if (!responseMessage.value) {
        if (response.msg) {
          responseMessage.value = response.msg
        } else {
          responseMessage.value = 'URL解析失败，请检查网址是否有效'
        }
      }
      
      ElMessage.error(responseMessage.value)
    }
    
    // 触发状态更新事件
    emit('parse-status-change', isUrlParseSuccess.value)
  } catch (error) {
    // 解析失败，显示url解析失败
    formData.name = ''
    contentId.value = null
    isUrlParseSuccess.value = false
    isUrlParseFailed.value = true
    isNoPermission.value = false
    responseMessage.value = '请求出错，请稍后重试'
    
    console.error('QaUpload: 获取元数据失败:', error)
    
    // 触发状态更新事件
    emit('parse-status-change', isUrlParseSuccess.value)
  } finally {
    loading.value = false
  }
}

// 处理下一步按钮点击
const handleNextStep = async () => {
  try {
    loading.value = true
    
    // 在进行下一步操作前，先验证URL格式，再重新发送请求验证URL
    if (!validateUrl(url.value)) {
      loading.value = false
      return
    }
    
    // 格式验证通过，继续发送请求验证URL
    await fetchMetadata()
    
    // 验证结果后再判断是否可以进入下一步
    if (!isUrlParseSuccess.value) {
      if (isNoPermission.value) {
        ElMessage.warning('无权限访问该文档，无法进入下一步')
      } else {
        ElMessage.warning('URL解析失败，请检查网址是否正确')
      }
      return
    }
    
    // 验证成功，触发next-step事件
    emit('next-step')
  } catch (error) {
    console.error('QaUpload: 验证URL时发生错误:', error)
    ElMessage.error('验证URL时发生错误，请重试')
  } finally {
    loading.value = false
  }
}

// 暴露给父组件的属性
defineExpose({
  contentId,
  isUrlParseSuccess,
  fetchMetadata,
  formData,
  isTable
})

// 更新URL
const updateUrl = () => {
  url.value = formData.website
  
  // 当URL变化时，重置解析状态
  isUrlParseSuccess.value = false
  isUrlParseFailed.value = false
  isNoPermission.value = false
  contentId.value = null
  urlFormatError.value = ''
  responseMessage.value = ''
}

// 监听website变化
watch(() => formData.website, (newValue) => {
  url.value = newValue
})
</script>

<style lang="scss" scoped>
.upload-content {
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
  
  .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 16px;
    flex-wrap: nowrap;
    overflow-x: auto;
    
    .form-item {
      min-width: 0;
      
      &.website-item {
        flex: 3;
        min-width: 180px;
        max-width: calc(50% - 10px); // 控制最大宽度
      }
      
      &.name-item {
        flex: 2;
        min-width: 120px;
        max-width: calc(30% - 10px); // 控制最大宽度
      }
      
      &.table-option-item {
        flex: 1;
        min-width: 100px;
        max-width: calc(20% - 10px); // 控制最大宽度
        
        // 表格选项样式
        .table-checkbox-wrapper {
          display: flex;
          align-items: center;
          background-color: #f5f7fa;
          border-radius: 4px;
          padding: 0 10px; // 减少内边距
          border: 1px solid #dcdfe6;
          width: 100%;
          height: 32px;
          box-sizing: border-box;
          overflow: hidden; // 防止内容溢出
          
          .table-checkbox {
            margin-right: 4px; // 减少右边距
            white-space: nowrap;
            font-size: 13px; // 稍微减小字体大小
          }
          
          .help-icon {
            margin-left: 2px; // 减少左边距
            color: #909399;
            cursor: pointer;
            font-size: 14px; // 减小图标大小
            flex-shrink: 0;
          }
        }
      }
    }
  }
  
  .form-item {
    margin-bottom: 12px;
    
    .form-label {
      font-weight: bold;
      margin-bottom: 6px;
      display: flex;
      align-items: center;
      height: 21px; // 固定标签高度
      
      &.required:before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }
    
    // 统一输入框容器样式
    .input-wrapper {
      height: 32px;
      box-sizing: border-box;
    }
    
    // 添加WebsiteUpload风格的错误和成功消息样式
    .error-message {
      color: #f56c6c;
      font-size: 12px;
      margin-top: 4px;
      line-height: 1.2;
      min-height: 14px;
    }
    
    .success-message {
      color: #67c23a;
      font-size: 12px;
      margin-top: 4px;
      line-height: 1.2;
      min-height: 14px;
    }
  }
  
  // 添加WebsiteUpload风格的输入框状态样式
  .is-error :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #f56c6c inset;
  }
  
  .is-success :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #67c23a inset;
  }
  
  .next-step-container {
    display: flex;
    justify-content: center;
    margin-top: 25px;
    width: 100%;
  }
}

// 确保复选框文字不会被截断
:deep(.el-checkbox__label) {
  white-space: nowrap;
}

.website-upload .el-input {
  width: 100%;
}

@media (max-width: 500px) {
  .upload-content {
    .form-row {
      flex-direction: row;
      gap: 10px;
      
      .form-item {
        &.website-item,
        &.name-item,
        &.table-option-item {
          min-width: 0;
          flex: 1 0 auto;
        }
      }
    }
  }
}
</style> 