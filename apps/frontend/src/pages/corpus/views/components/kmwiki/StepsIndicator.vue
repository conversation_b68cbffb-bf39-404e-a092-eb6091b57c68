<template>
  <div class="steps-container">
    <div class="custom-steps">
      <div class="step-item" :class="{ 'active': stepStatus.step1.active, 'completed': stepStatus.step1.completed }">
        <div class="step-number">1</div>
        <div class="step-title">{{ stepTitles[0] }}</div>
      </div>
      <div class="step-line" :class="{ 'active': stepStatus.step1.completed }"></div>
      <div class="step-item" :class="{ 'active': stepStatus.step2.active, 'completed': stepStatus.step2.completed }">
        <div class="step-number">2</div>
        <div class="step-title">{{ stepTitles[1] }}</div>
      </div>
      <template v-if="totalSteps === 3">
        <div class="step-line" :class="{ 'active': stepStatus.step2.completed }"></div>
        <div class="step-item" :class="{ 'active': stepStatus.step3.active, 'completed': stepStatus.step3.completed }">
          <div class="step-number">3</div>
          <div class="step-title">{{ stepTitles[2] }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  activeStep: {
    type: Number,
    required: true
  },
  totalSteps: {
    type: Number,
    default: 3
  },
  titles: {
    type: Array as () => string[],
    default: () => []
  }
})

// 默认步骤标题
const defaultTitles = {
  3: ['上传知识', '预处理知识', '学习知识'],
  2: ['上传知识', '处理知识']
}

// 合并自定义标题和默认标题
const stepTitles = computed(() => {
  const defaultTitleList = defaultTitles[props.totalSteps] || defaultTitles[3]
  
  if (props.titles.length === 0) {
    return defaultTitleList
  }
  
  // 使用自定义标题，如果缺少则使用默认标题
  return defaultTitleList.map((title, index) => props.titles[index] || title)
})

// 计算每个步骤的状态
const stepStatus = computed(() => ({
  step1: {
    active: props.activeStep === 1,
    completed: props.activeStep > 1
  },
  step2: {
    active: props.activeStep === 2,
    completed: props.activeStep > 2
  },
  step3: {
    active: props.activeStep === 3,
    completed: props.activeStep > 3
  }
}))
</script>

<style lang="scss" scoped>
.steps-container {
  margin: 20px 0;
  min-width: 320px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
  height: 60px;
  min-height: 60px;
  flex-shrink: 0;
  
  .custom-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    height: 60px;
    
    .step-item {
      text-align: center;
      z-index: 1;
      flex: 0 0 auto;
      height: 60px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      .step-number {
        width: 30px;
        height: 30px;
        line-height: 30px;
        border-radius: 50%;
        font-size: 14px;
        font-weight: bold;
        color: #909399;
        border: 1px solid #d9d9d9;
        background-color: #fff;
        margin: 0 auto 8px;
      }
      
      .step-title {
        font-size: 14px;
        color: #909399;
        white-space: nowrap;
      }
      
      &.active {
        .step-number {
          background-color: #0074d9;
          color: #fff;
          border-color: #0074d9;
        }
        
        .step-title {
          color: #303133;
          font-weight: 500;
        }
      }
      
      &.completed .step-number {
        background-color: #0074d9;
        color: #fff;
        border-color: #0074d9;
      }
    }
    
    .step-line {
      flex: 1;
      min-width: 30px;
      height: 1px;
      background-color: #e0e0e0;
      margin: 0 10px;
      margin-bottom: 22px;
      
      &.active {
        background-color: #0074d9;
      }
    }
  }
}

@media (max-width: 500px) {
  .steps-container {
    margin: 15px 0;
    
    .custom-steps {
      .step-item {
        .step-number {
          width: 26px;
          height: 26px;
          line-height: 26px;
          font-size: 12px;
          margin-bottom: 6px;
        }
        
        .step-title {
          font-size: 12px;
        }
      }
      
      .step-line {
        margin: 0 8px;
        margin-bottom: 18px;
      }
    }
  }
}
</style> 