<template>
  <el-dialog
    v-model="dialogVisible"
    title="语料转换结果"
    width="60%"
    destroy-on-close
    @update:modelValue="handleDialogChange"
  >
    <div v-if="currentResult" class="result-container">
      <div class="result-header">
        <div class="result-header-main">
          <div class="result-title">
            <span class="result-total">历史记录总数: {{ conversionHistory.length }}次</span>
          </div>
          <div class="current-viewing">
            <el-tag size="large" type="info" effect="plain">
              当前查看: 第<span class="highlight">{{ currentHistoryIndex }}</span>条
            </el-tag>
          </div>
        </div>
        <div v-if="conversionHistory.length > 0" class="conversion-history">
          <div class="history-title">转换历史:</div>
          <div class="history-list">
            <div 
              v-for="(history, hIndex) in conversionHistory" 
              :key="hIndex"
              class="history-item"
              :class="{ 'active': history.taskId === currentTaskId }"
              @click="switchHistoryResult(history)"
            >
              <span class="history-index">#{{ hIndex + 1 }}</span>
              <span class="history-date">{{ formatDate(history.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>
      <el-table
        :data="currentResult?.data || []"
        :key="currentTaskId || 'default'"
        style="width: 100%"
        border
        max-height="500px"
      >
        <el-table-column prop="title" label="问题" show-overflow-tooltip />
        <el-table-column label="任务缺失信息" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.taskMissingInfo && row.taskMissingInfo.length">
              <el-tag v-for="(item, index) in row.taskMissingInfo" :key="index" size="small" type="warning" style="margin-right: 5px; margin-bottom: 5px;">
                {{ item }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="{ row }">
            <template v-if="row.title !== '学城QA转换处理失败'">
              <el-button
                type="primary"
                link
                class="view-detail-btn"
                @click="viewResultDetail(row)"
              >
                添加语料
              </el-button>
              <el-button
                type="danger"
                link
                class="view-detail-btn delete-btn"
                @click="handleDeleteCorpus(row)"
              >
                删除语料
              </el-button>
            </template>
            <span v-else class="error-message">转换失败</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>

    <!-- 使用新的详情对话框组件 -->
    <CorpusDetailDialog
      v-model:visible="detailDialogVisible"
      :detail-data="currentDetailItem"
      :history-index="currentHistoryIndex"
      :rg-id="getCurrentRgId()" 
      :mis-id="getCurrentMisId()"
      :content-id="props.contentId"
      @compare="handleDetailCompare"
      @save="handleDetailSave"
      ref="detailDialogRef"
    />
    
    <!-- 添加加载进度面板 -->
    <LoadingProgressPanel
      v-model:visible="loadingPanelVisible"
      ref="loadingPanelInstance"
      title="相似语料检索中"
      message="系统正在处理您的请求..."
      :tips="[
        '正在检索相似的语料',
        '请耐心等待，处理完成后会自动显示结果'
      ]"
      :duration="30000"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, computed, reactive, nextTick, onMounted } from 'vue'
import CorpusDetailDialog from './CorpusDetailDialog.vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import httpRequest from '../../../../../utils/httpRequest'
import { searchSimilarCorpus } from '../../../utils/corpusManager'
import { Document, RefreshRight, Clock, View, Edit, Check } from '@element-plus/icons-vue'
import LoadingProgressPanel from '../../components/LoadingProgressPanel.vue';

// 添加常量映射
const SOURCE_MAP = {
  1: '智能客服',
  2: '语料编辑',
  3: '知识转换',
  4: '合并语料',
  5: 'TT群导入',
  6: '用户反馈',
  7: '其他来源'
};

// 定义接口类型
interface CorpusItem {
  title: string;
  content: string;
  taskMissingInfo?: string[];
  ticketId?: string;
  rgId?: string | number;
  misId?: string;
  contentId?: number;  // 添加contentId字段
  taskId?: string;     // 异步生成任务ID，选填字段
  tagsIds?: string;    // 标签ID字符串，逗号分隔
  tagsname?: string;   // 新增：标签名称，逗号分隔
}

interface CorpusResult {
  total: number;
  data: CorpusItem[];
}

// 添加ApiResponse接口
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

interface ConversionHistoryItem {
  taskId: string;
  timestamp: number;
  result: CorpusResult;
  rgId?: string | number;
  misId?: string;
}

// 定义组件接收的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  result: {
    type: Object as () => CorpusResult | null,
    default: null
  },
  conversionHistory: {
    type: Array as () => ConversionHistoryItem[],
    default: () => []
  },
  currentTaskId: {
    type: String,
    default: ''
  },
  rgId: {
    type: [Number, String],
    required: true
  },
  misId: {
    type: String,
    required: true
  },
  contentId: {
    type: [Number, String],
    default: ''
  }
})

// 定义对外发出的事件
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'view-detail', item: CorpusItem): void
  (e: 'save-corpus', item: CorpusItem): void
  (e: 'delete-corpus', item: { item: CorpusItem, historyIndex: number, updatedHistory: ConversionHistoryItem }): void
  (e: 'update:conversionHistory', value: ConversionHistoryItem[]): void
  (e: 'update:currentTaskId', value: string): void
}>();

// 内部状态变量
const dialogVisible = ref(false)
const currentResult = ref<CorpusResult | null>(null)
const currentTaskId = ref('')
const detailDialogVisible = ref(false)
const currentDetailItem = ref<CorpusItem | null>(null)
const currentConversionHistory = ref<ConversionHistoryItem[]>([])

// 添加相似语料检索相关变量
const compareLoading = ref(false)
const selectedCompareRows = ref<any[]>([])

// 详情对话框引用
const detailDialogRef = ref<any>(null)
const loadingPanelVisible = ref(false)
const loadingPanelInstance = ref<any>(null)

// 获取当前历史记录的rgId
const getCurrentRgId = () => {
  const currentHistory = props.conversionHistory.find(h => h.taskId === props.currentTaskId)
  if (currentHistory?.rgId) {
    return currentHistory.rgId
  }
  return props.rgId || ''
}

// 获取当前历史记录的misId
const getCurrentMisId = () => {
  const currentHistory = props.conversionHistory.find(h => h.taskId === props.currentTaskId)
  if (currentHistory?.misId) {
    return currentHistory.misId
  }
  return props.misId || ''
}

// 添加计算属性获取当前历史记录的索引
const currentHistoryIndex = computed(() => {
  const index = props.conversionHistory.findIndex(h => h.taskId === currentTaskId.value)
  return index >= 0 ? index + 1 : 1
})

// 初始化函数，确保数据正确初始化
const initData = () => {
  // 初始化历史记录
  currentConversionHistory.value = JSON.parse(JSON.stringify(props.conversionHistory || []))
  
  // 确保currentTaskId有效
  if (props.currentTaskId) {
    currentTaskId.value = props.currentTaskId
    
    // 获取当前任务的历史记录
    const currentHistory = props.conversionHistory.find(h => h.taskId === props.currentTaskId)
    if (currentHistory) {
      currentResult.value = JSON.parse(JSON.stringify(currentHistory.result))
    } else if (props.result) {
      currentResult.value = JSON.parse(JSON.stringify(props.result))
    }
  } else if (props.result) {
    currentResult.value = JSON.parse(JSON.stringify(props.result))
  }
  
  console.log('initData:', {
    historyLength: currentConversionHistory.value.length,
    currentTaskId: currentTaskId.value,
    resultLength: currentResult.value?.data?.length
  });
}

// 在组件挂载时初始化数据
onMounted(() => {
  initData()
})

// 监听props变化，更新内部状态
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    console.log('dialog visible changed:', {
      currentResult: currentResult.value?.data?.length,
      history: props.conversionHistory?.length,
      taskId: currentTaskId.value
    });
    
    // 显示对话框时重新初始化数据
    initData()
  }
})

watch(() => props.result, (newVal) => {
  if (newVal) {
    currentResult.value = JSON.parse(JSON.stringify(newVal))
  }
}, { immediate: true })

// 添加对conversionHistory的监听
watch(() => props.conversionHistory, (newVal) => {
  if (newVal) {
    currentConversionHistory.value = JSON.parse(JSON.stringify(newVal))
    
    // 如果当前查看的历史记录在列表中，更新currentResult
    const currentHistory = newVal.find(h => h.taskId === currentTaskId.value)
    if (currentHistory) {
      currentResult.value = JSON.parse(JSON.stringify(currentHistory.result))
    }
  }
}, { deep: true, immediate: true })

watch(() => props.currentTaskId, (newVal) => {
  if (newVal) {
    currentTaskId.value = newVal
    
    // 当taskId变化时，确保显示对应的结果数据
    if (props.conversionHistory && props.conversionHistory.length > 0) {
      const foundHistory = props.conversionHistory.find(h => h.taskId === newVal)
      if (foundHistory) {
        currentResult.value = JSON.parse(JSON.stringify(foundHistory.result))
      }
    }
  }
}, { immediate: true })

// 添加监听currentResult变化
watch(() => currentResult.value, (newVal) => {
  if (newVal) {
    if (newVal.data && newVal.data.length > 0) {
      newVal.data.forEach((item, index) => {
        console.log('监听到currentResult变化:', {
          title: item.title,
          hasContent: !!item.content,
          isFailureItem: item.title === '学城QA转换处理失败'
        });
      });
    }
  }
}, { deep: true })

// 处理对话框状态变化
const handleDialogChange = (value: boolean) => {
  emit('update:visible', value)
}

// 日期格式化函数
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 格式化标题
const formatTitle = (title: string) => {
  return title?.trim() || '';
};

// 切换历史结果
const switchHistoryResult = (history: ConversionHistoryItem) => {
  const historyIndex = props.conversionHistory.findIndex(h => h.taskId === history.taskId) + 1
  
  // 使用深拷贝确保对象引用被更新
  currentResult.value = JSON.parse(JSON.stringify(history.result))
  currentTaskId.value = history.taskId
  
  // 通知父组件当前查看的任务ID已更改
  emit('update:currentTaskId', history.taskId)
  
}

// 查看详情
const viewResultDetail = (row: CorpusItem) => {
  console.log('viewResultDetail called with row:', {
    title: row.title,
    hasContent: !!row.content,
    contentLength: row.content?.length || 0,
    contentPreview: row.content?.substring(0, 100) + '...',
    ticketId: row.ticketId,
    taskId: row.taskId,
    rgId: getCurrentRgId(),
    misId: getCurrentMisId()
  });
  
  // 确保传递完整的数据，包括必要的ID信息
  currentDetailItem.value = {
    ...row,
    rgId: row.rgId || getCurrentRgId(),
    misId: row.misId || getCurrentMisId(),
    contentId: row.contentId || props.contentId
  }
  
  detailDialogVisible.value = true
}

// 添加日期格式化函数
const formatDateTime = (datetime: string) => {
  if (!datetime) return '';
  const date = new Date(datetime);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 处理详情对话框中的检索对比
const handleDetailCompare = async (item: any) => {
  console.log('handleDetailCompare called with item:', {
    hasContent: !!item?.content,
    contentLength: item?.content?.length || 0,
    contentTrimmed: item?.content?.trim?.() || '',
    item: item
  });
  
  // 改进内容验证逻辑
  const content = item?.content?.trim?.() || '';
  if (!content) {
    ElMessage.warning('内容不能为空，请先填写内容再进行检索对比');
    return;
  }
  
  if (!item.rgId) {
    // 如果没有rgId，使用当前历史记录或props中的rgId
    item.rgId = getCurrentRgId();
    if (!item.rgId) {
      ElMessage.warning('未获取到团队ID');
      return;
    }
  }
  
  compareLoading.value = true;
  
  try {
    // 显示加载进度面板
    loadingPanelVisible.value = true;
    
    // 第一阶段进度 - 准备检索
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(15);
    }, 300);
    
    // 第二阶段进度 - 发送请求
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(40);
    }, 600);
    
    // 调用工具函数执行检索
    const result = await searchSimilarCorpus({
      content: content, // 使用验证过的content
      currentTeam: Number(item.rgId),
      ticketId: item.ticketId || '',
      loadingPanelInstance: loadingPanelInstance.value, // 传递加载面板实例
      SOURCE_MAP,
      formatTitle,
      formatDateTime
    });
    
    // 设置进度为90%
    loadingPanelInstance.value?.setProgress(90);
    
    // 完成进度
    loadingPanelInstance.value?.complete();
    
    if (result.success) {
      // 如果有引用实例，调用setCompareResults方法显示检索结果
      if (detailDialogRef.value && typeof detailDialogRef.value.setCompareResults === 'function') {
        detailDialogRef.value.setCompareResults(result.data);
      } else {
        // 如果没有引用或方法不存在
        ElMessage.success(`找到 ${result.data.length} 条相似语料`);
      }
    } else {
      // 显示错误信息
      ElMessage.error(result.error || '检索失败');
    }
  } catch (error: any) {
    console.error('检索失败:', error);
    ElMessage.error(`检索失败: ${error.message || '未知错误'}`);
    loadingPanelVisible.value = false;
  } finally {
    compareLoading.value = false;
  }
};

const handleDetailSave = (item: CorpusItem) => {
  // 获取当前任务的 taskId
  const currentTaskIdValue = currentTaskId.value || props.currentTaskId
  
  // 创建包含 taskId 的语料项
  const corpusItemWithTaskId: CorpusItem = {
    ...item,
    taskId: currentTaskIdValue // 添加当前任务的 taskId
  }
  
  // 向上传递保存事件，让父组件处理实际的HTTP请求
  emit('save-corpus', corpusItemWithTaskId)
  
  // 本地更新当前结果中对应的条目
  if (currentResult.value && currentResult.value.data) {
    const index = currentResult.value.data.findIndex(d => 
      d.title === currentDetailItem.value?.title && 
      d.content === currentDetailItem.value?.content
    )
    
    if (index !== -1) {
      currentResult.value.data[index] = { ...corpusItemWithTaskId }
      ElMessage.success('语料已在本地更新')
    }
  }
}

// 添加删除语料的处理函数
const handleDeleteCorpus = (row: CorpusItem) => {
  ElMessageBox.confirm(
    '确定要删除此条语料记录吗？删除后将从当前历史记录中移除。',
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    // 从当前结果中移除该项
    if (currentResult.value && currentResult.value.data) {
      const index = currentResult.value.data.findIndex(d => 
        d.title === row.title && 
        d.content === row.content
      )
      
      if (index !== -1) {
        // 从数据数组中移除该项
        currentResult.value.data.splice(index, 1)
        
        // 更新total字段
        if (currentResult.value.total > 0) {
          currentResult.value.total -= 1
        }
        
        ElMessage.success('已从当前历史记录中移除')
        
        // 更新历史记录
        const currentHistoryIndex = props.conversionHistory.findIndex(
          h => h.taskId === currentTaskId.value
        )
        
        if (currentHistoryIndex !== -1) {
          // 创建一个新的历史记录对象，更新结果数据
          const updatedHistory = {
            ...props.conversionHistory[currentHistoryIndex],
            result: {
              ...currentResult.value,
              data: [...currentResult.value.data],
              total: currentResult.value.data.length
            }
          }
          
          // 创建更新后的完整历史记录数组
          const updatedConversionHistory = [...props.conversionHistory]
          updatedConversionHistory[currentHistoryIndex] = updatedHistory
          
          // 更新本地历史记录
          currentConversionHistory.value = JSON.parse(JSON.stringify(updatedConversionHistory))
          
          // 更新父组件中的历史记录
          emit('update:conversionHistory', updatedConversionHistory)
          
          // 通知父组件删除事件发生，传递更新后的历史记录和索引
          console.log('删除事件信息:', {
            historyIndex: currentHistoryIndex,
            updatedTotal: currentResult.value.data.length,
            origTotal: props.conversionHistory[currentHistoryIndex].result.total
          });
          
          emit('delete-corpus', {
            item: row,
            historyIndex: currentHistoryIndex,
            updatedHistory: updatedHistory
          })
        }
        
        // 如果删除后结果为空，提示用户
        if (currentResult.value.data.length === 0) {
          ElMessage.info('当前历史记录已清空')
        }
      }
    }
  }).catch(() => {
    // 用户取消删除
  })
}
</script>

<style lang="scss" scoped>
.result-container {
  .result-content {
    margin-bottom: 20px;
  }

  .result-header {
    margin-bottom: 15px;
    font-weight: bold;
  }

  .result-header-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .result-title {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .result-total {
    font-size: 16px;
    font-weight: bold;
  }

  .current-viewing {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .highlight {
      font-weight: bold;
      color: #409EFF;
      font-size: 16px;
      margin: 0 4px;
    }
  }

  .history-info {
    font-size: 12px;
    color: #909399;
  }

  .conversion-history {
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .history-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #303133;
  }

  .history-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .history-item {
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover {
      background-color: #e6f7ff;
      border-color: #409EFF;
    }

    &.active {
      background-color: #e6f7ff;
      border-color: #409EFF;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .history-index {
      font-size: 12px;
      color: #409EFF;
      font-weight: bold;
      background-color: rgba(64, 158, 255, 0.1);
      padding: 0 5px;
      border-radius: 4px;
      min-width: 24px;
      text-align: center;
      display: inline-block;
    }

    .history-date {
      font-size: 13px;
      color: #606266;
      font-weight: 500;
    }
  }

  .dialog-footer {
    text-align: right;
    margin-top: 20px;
  }

  // 移除查看详情按钮的focus状态样式
  :deep(.view-detail-btn) {
    &:focus,
    &:focus-visible {
      outline: none !important;
      box-shadow: none !important;
      border-color: transparent !important;
      background-color: transparent !important;
    }

    &:active {
      outline: none !important;
      box-shadow: none !important;
    }

    &:hover {
      outline: none !important;
      box-shadow: none !important;
    }

    &::after {
      display: none !important;
    }

    // 移除点击后的阴影效果
    &:not(:disabled):active {
      box-shadow: none !important;
    }

    // 移除点击时的背景色
    &:not(:disabled):active {
      background-color: transparent !important;
    }
  }
  
  // 删除按钮特有样式
  :deep(.delete-btn) {
    &:focus,
    &:focus-visible,
    &:hover {
      color: var(--el-color-danger) !important;
    }
    
    &:not(:disabled):active {
      color: var(--el-color-danger-dark-2) !important;
    }
    
    margin-left: 8px;
  }
  
  // 添加转换失败消息的样式
  :deep(.error-message) {
    color: var(--el-color-danger);
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    
    &::before {
      content: "\2716"; // 添加一个 X 符号
      margin-right: 4px;
      font-weight: 700;
    }
  }
}
</style> 