<template>
  <div class="knowledge-process-container">
    <div class="process-header">
      <div class="content-id-title">
        <span class="title-label">文档ID:</span> 
        <span class="title-value">{{ contentId }}</span>
      </div>
      <div class="document-info">
        <div class="doc-name" v-if="documentName">{{ documentName }}</div>
        <div class="doc-source" v-if="documentSource">来源: {{ documentSource }}</div>
      </div>
    </div>
    
    <div class="divider"></div>
    
    <!-- 语料列表展示区域 -->
    <div class="corpus-list-section">
      <div class="corpus-header">
        <span class="corpus-title">语料列表 ({{ props.corpusData.total || 0 }})</span>
        <div class="corpus-actions">
          <span class="selected-count" v-if="Object.values(selectedCorpus).filter(v => v).length > 0">
            已选择 {{ Object.values(selectedCorpus).filter(v => v).length }} 项
          </span>
          <el-checkbox 
            v-model="isAllSelected"
            @change="handleSelectAll"
            :disabled="!props.corpusData.list || props.corpusData.list.length === 0"
          >
            全选
          </el-checkbox>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入搜索关键词"
          clearable
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <div class="action-buttons">
          <el-button 
            type="primary" 
            @click="handleSearch"
            :loading="isSearching"
          >
            搜索
          </el-button>
          <el-button 
            type="danger" 
            :disabled="Object.values(selectedCorpus).filter(v => v).length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>批量删除
          </el-button>
        </div>
      </div>
      
      <div class="corpus-list" v-if="props.corpusData.list && props.corpusData.list.length > 0">
        <div 
          v-for="(item, index) in props.corpusData.list" 
          :key="item.ticketId"
          class="corpus-item"
          :class="{ 'selected': selectedCorpus[item.ticketId] }"
          @click="handleSelect(item.ticketId)"
        >
          <div class="corpus-item-header">
            <el-checkbox 
              v-model="selectedCorpus[item.ticketId]"
              @click.stop
            />
            <span class="corpus-item-number">{{ index + 1 }}</span>
            <span class="corpus-item-title">{{ item.title }}</span>
            <div class="corpus-item-actions">
              <el-button link type="primary" @click.stop="handleView(item)">
                <el-icon :size="16"><View /></el-icon>
              </el-button>
              <el-button link type="primary" @click.stop="handleEdit(item)">
                <el-icon :size="16"><Edit /></el-icon>
              </el-button>
              <el-button link type="danger" @click.stop="handleDelete(item)">
                <el-icon :size="16"><Delete /></el-icon>
              </el-button>
            </div>
          </div>
          <div class="corpus-item-content">
            <div v-html="formatMarkdown(item.content)"></div>
          </div>
          <div class="corpus-item-footer">
            <span class="corpus-item-time">创建时间: {{ formatDate(item.createTime) }}</span>
            <span class="corpus-item-id">ticketId: {{ item.ticketId }}</span>
          </div>
        </div>
      </div>
      
      <div class="empty-corpus" v-else>
        <el-empty description="暂无相关语料数据" />
      </div>
    </div>
  </div>
  
  <!-- 查看语料详情对话框 -->
  <ViewCorpusDialog
    v-model="viewDialogVisible"
    :corpus-data="currentViewItem"
  />
  
  <!-- 编辑语料对话框 - 使用EditCorpusDialog组件 -->
  <EditCorpusDialog
    v-model="editDialogVisible"
    :form-data="editFormData"
    :current-edit-id="currentEditId"
    :current-mis-id="props.misId"
    :current-team="props.rgId"
    :show-add-tag-option="false"
    @update:form-data="editFormData = $event"
    @cancel="handleEditCancel"
    @refresh="handleEditRefresh"
    @open-tag-management="handleOpenTagManagement"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Search, View, Edit, Delete } from '@element-plus/icons-vue'
import httpRequest from '@/utils/httpRequest'
import { API_PATHS } from '@/pages/corpus/request/api'
import ViewCorpusDialog from '@/pages/corpus/views/components/ViewCorpusDialog.vue'
import EditCorpusDialog from '@/pages/corpus/views/components/EditCorpusDialog.vue'

// 定义组件接收的属性
const props = defineProps({
  contentId: {
    type: [Number, String],
    default: ''
  },
  corpusData: {
    type: Object as () => {
      list: Array<{
        ticketId: string;
        title: string;
        content: string;
        type: number;
        source: number;
        misId: string;
        createTime: string;
        updateTime: string;
        contentId: number;
      }>;
      total: number;
      totalPage: number;
      pageSize: number;
      currentPage: number;
    },
    default: () => ({
      list: [],
      total: 0,
      totalPage: 0,
      pageSize: 0,
      currentPage: 0
    })
  },
  rgId: {
    type: [Number, String],
    required: true
  },
  misId: {
    type: String,
    required: true
  }
})

// 定义事件
const emit = defineEmits<{
  (e: 'select-corpus', selectedItems: Array<any>): void
  (e: 'update-corpus-data', data: any): void
  (e: 'open-tag-management'): void
}>()

const documentName = ref('语料库内相关语料')
const documentSource = ref('学城')

// 选中的语料项
const selectedCorpus = ref<Record<string, boolean>>({})

// 是否全选
const isAllSelected = computed(() => {
  if (!props.corpusData.list || props.corpusData.list.length === 0) return false
  return props.corpusData.list.every(item => selectedCorpus.value[item.ticketId])
})

// 处理全选/取消全选
const handleSelectAll = (checked: boolean) => {
  props.corpusData.list.forEach(item => {
    selectedCorpus.value[item.ticketId] = checked
  })
  emitSelectedItems()
}

// 处理单个选择
const handleSelect = (ticketId: string) => {
  selectedCorpus.value[ticketId] = !selectedCorpus.value[ticketId]
  emitSelectedItems()
}

// 发送选中项变更事件
const emitSelectedItems = () => {
  const selectedItems = props.corpusData.list.filter(item => selectedCorpus.value[item.ticketId])
  emit('select-corpus', selectedItems)
}

// 当前查看的语料项
const currentViewItem = ref({
  ticketId: '',
  title: '',
  content: '',
  createTime: '',
  updateTime: '',
  backgroundKnowledge: '',
  sop: '',
  rule: ''
})

// 查看对话框可见性
const viewDialogVisible = ref(false)

// 处理查看按钮点击
const handleView = (item) => {
  // 阻止事件冒泡，避免触发选择事件
  event?.stopPropagation();
  
  // 在显示对话框前先设置当前查看的语料项
  currentViewItem.value = { ...item };
  
  // 显示查看对话框
  nextTick(() => {
    viewDialogVisible.value = true;
    
    // 在对话框打开后，延迟一些时间设置编辑器状态
    setTimeout(() => {
      const editor = document.querySelector('.view-corpus-editor');
      if (editor && editor.__vue__) {
        const editorComponent = editor.__vue__;
        // 如果编辑器组件有 togglePreview 方法并且不在预览模式，则切换到预览模式
        if (typeof editorComponent.togglePreview === 'function' && !editorComponent.isPreviewMode) {
          editorComponent.togglePreview();
        }
      }
    }, 300);
  });
}

// 格式化Markdown内容
const formatMarkdown = (content: string): string => {
  if (!content) return ''
  
  // 简单的Markdown格式转换为HTML
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 加粗
    .replace(/\n/g, '<br/>') // 换行
}

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return ''
  
  try {
    const date = new Date(dateStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  } catch (e) {
    return dateStr
  }
}

// 在组件挂载时初始化
onMounted(() => {
})

// 监听contentId变化
watch(() => props.contentId, (newId) => {
  if (newId) {
  }
})

// 搜索相关变量
const searchKeyword = ref('')
const isSearching = ref(false)

// 处理搜索
const handleSearch = async () => {
  isSearching.value = true
  
  try {
    // 构建查询语料列表的请求参数
    const corpusParams = {
      rgId: props.rgId,
      misId: props.misId,
      contentId: props.contentId,
      pageNum: 1,
      pageSize: 1000,
      strMatch: searchKeyword.value.trim()
    }
    
    // 发送请求获取语料列表数据
    const corpusResponse = await httpRequest.rawRequestGet(API_PATHS.QUERY_CORPUS_LIST_BY_CONTENT_ID, corpusParams)
    
    if (corpusResponse.code === 0) {
      const responseData = corpusResponse.data
      
      if (responseData && typeof responseData === 'object') {
        // 更新语料列表数据
        const updatedData = {
          list: Array.isArray(responseData.list) ? responseData.list : [],
          total: responseData.total || 0,
          totalPage: responseData.totalPage || 1,
          pageSize: responseData.pageSize || 1000,
          currentPage: responseData.currentPage || 1
        }
        // 触发更新事件
        emit('update-corpus-data', updatedData)
        ElMessage.success('搜索完成')
      } else {
        ElMessage.warning('搜索结果数据结构异常')
      }
    } else {
      ElMessage.error(`搜索失败: ${corpusResponse.message}`)
    }
  } catch (error) {
    console.error('搜索时发生错误:', error)
    ElMessage.error('搜索时发生错误，请重试')
  } finally {
    isSearching.value = false
  }
}

// 编辑对话框相关变量
const editDialogVisible = ref(false)
const editFormData = ref({
  title: '',
  content: '',
  tagsname: [],
  tagsIds: ''
})
const currentEditId = ref('')

// 处理编辑语料
const handleEdit = (item) => {
  // 阻止事件冒泡，避免触发选择事件
  event?.stopPropagation();
  
  // 保存当前编辑的语料ID
  currentEditId.value = item.ticketId;
  
  // 设置编辑表单数据
  editFormData.value = {
    title: item.title,
    content: item.content,
    tagsname: item.tagsname || [],
    tagsIds: item.tagsIds || ''
  };
  
  // 显示编辑对话框
  editDialogVisible.value = true;
  
  // 移除焦点
  (document.activeElement as HTMLElement)?.blur();
}

// 处理编辑取消
const handleEditCancel = () => {
  editDialogVisible.value = false
}

// 处理编辑刷新
const handleEditRefresh = async () => {
  // 重新获取语料列表数据
  try {
    const corpusParams = {
      rgId: props.rgId,
      misId: props.misId,
      contentId: props.contentId,
      pageNum: 1,
      pageSize: 1000
    }
    
    const corpusResponse = await httpRequest.rawRequestGet(API_PATHS.QUERY_CORPUS_LIST_BY_CONTENT_ID, corpusParams)
    
    if (corpusResponse.code === 0) {
      const responseData = corpusResponse.data
      
      if (responseData && typeof responseData === 'object') {
        const updatedData = {
          list: Array.isArray(responseData.list) ? responseData.list : [],
          total: responseData.total || 0,
          totalPage: responseData.totalPage || 1,
          pageSize: responseData.pageSize || 1000,
          currentPage: responseData.currentPage || 1
        }
        emit('update-corpus-data', updatedData)
      }
    }
  } catch (error) {
    console.error('刷新语料列表失败:', error)
  }
}

// 处理删除语料
const handleDelete = async (item) => {
  // 阻止事件冒泡，避免触发选择事件
  event?.stopPropagation();
  
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      '确定要删除该语料吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 确保rgId是数字类型
    const rgId = parseInt(props.rgId.toString())
    if (rgId === null) {
      ElMessage.error('未提供有效的团队ID(rgId)')
      return
    }
    
    // 构造URL参数
    const queryParams = `misId=${props.misId}&rgId=${rgId}&ticketIds=${item.ticketId}`
    
    
    // 发送删除请求
    const response = await httpRequest.rawRequestPostAsJson(
      `/corpus/deleteCorpusByTicketIds?${queryParams}`,
      null
    )
    
    
    if (response?.code === 0) {
      ElMessage.success('删除成功')
      
      // 更新语料列表，移除已删除的项
      const updatedData = {
        ...props.corpusData,
        list: props.corpusData.list.filter(corpus => corpus.ticketId !== item.ticketId),
        total: props.corpusData.total - 1
      }
      
      // 触发更新事件
      emit('update-corpus-data', updatedData)
      
      // 如果被删除的语料已被选中，更新选中状态
      if (selectedCorpus.value[item.ticketId]) {
        selectedCorpus.value[item.ticketId] = false
        emitSelectedItems()
      }
    } else {
      ElMessage.error(`删除失败: ${response?.message || '未知错误'}`)
    }
  } catch (error) {
    // 判断是否为取消操作（用户点击取消按钮）
    if (error !== 'cancel' && error?.toString() !== 'Error: cancel' && error?.message !== 'cancel') {
      console.error('删除语料出错:', error)
      ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
    }
  }
}

// 处理批量删除
const handleBatchDelete = async () => {
  const selectedItems = props.corpusData.list.filter(item => selectedCorpus.value[item.ticketId])
  
  if (selectedItems.length === 0) {
    ElMessage.warning('请选择要删除的语料')
    return
  }
  
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedItems.length} 条语料吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 确保rgId是数字类型
    const rgId = parseInt(props.rgId.toString())
    if (rgId === null) {
      ElMessage.error('未提供有效的团队ID(rgId)')
      return
    }
    
    // 获取选中项的ticketId列表
    const ticketIds = selectedItems.map(item => item.ticketId).join(',')
    
    // 构造URL参数
    const queryParams = `misId=${props.misId}&rgId=${rgId}&ticketIds=${ticketIds}`
    
    
    // 发送删除请求
    const response = await httpRequest.rawRequestPostAsJson(
      `/corpus/deleteCorpusByTicketIds?${queryParams}`,
      null
    )
    
    
    if (response?.code === 0) {
      ElMessage.success('批量删除成功')
      
      // 更新语料列表，移除已删除的项
      const updatedData = {
        ...props.corpusData,
        list: props.corpusData.list.filter(corpus => !selectedItems.find(item => item.ticketId === corpus.ticketId)),
        total: props.corpusData.total - selectedItems.length
      }
      
      // 重置选中状态
      selectedItems.forEach(item => {
        selectedCorpus.value[item.ticketId] = false
      })
      
      // 触发更新事件
      emit('update-corpus-data', updatedData)
      emitSelectedItems()
    } else {
      ElMessage.error(`批量删除失败: ${response?.message || '未知错误'}`)
    }
  } catch (error) {
    // 判断是否为取消操作（用户点击取消按钮）
    if (error !== 'cancel' && error?.toString() !== 'Error: cancel' && error?.message !== 'cancel') {
      console.error('批量删除语料出错:', error)
      ElMessage.error(`批量删除失败: ${error.message || '未知错误'}`)
    }
  }
}

// 处理打开标签管理的事件
const handleOpenTagManagement = () => {
  // 向父组件传递事件
  emit('open-tag-management')
}
</script>

<style lang="scss" scoped>
.knowledge-process-container {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .process-header {
    margin-bottom: 16px;
    
    .content-id-title {
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      margin-bottom: 8px;
      
      .title-label {
        color: #909399;
      }
      
      .title-value {
        color: #303133;
        font-weight: 600;
      }
    }
    
    .document-info {
      margin-top: 8px;
      
      .doc-name {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .doc-source {
        font-size: 13px;
        color: #909399;
      }
    }
  }
  
  .divider {
    height: 1px;
    background-color: #f0f2f5;
    margin: 0px 0;
  }
  
  .corpus-list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .corpus-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      
      .corpus-title {
        font-size: 15px;
        font-weight: 600;
        color: #303133;
      }

      .corpus-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .selected-count {
          font-size: 13px;
          color: #409EFF;
          background-color: rgba(64, 158, 255, 0.1);
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
    }
    
    .search-section {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;
      
      .search-input {
        width: 300px;
      }
      
      .action-buttons {
        display: flex;
        gap: 12px;
      }
    }
    
    .corpus-list {
      flex: 1;
      overflow-y: auto;
      min-height: 0;
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding-right: 4px;
      
      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }
      
      .corpus-item {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        background-color: #f8f9fb;
        padding: 12px;
        transition: all 0.2s ease;
        cursor: pointer;
        
        &:hover {
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }

        &.selected {
          border-color: #409EFF;
          background-color: rgba(64, 158, 255, 0.05);
        }
        
        .corpus-item-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          gap: 8px;
          
          :deep(.el-checkbox) {
            margin-right: 0;
          }
          
          .corpus-item-number {
            background-color: #f0f2f5;
            color: #606266;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
          }
          
          .corpus-item-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .corpus-item-actions {
            display: flex;
            align-items: center;
            margin-left: auto;
            
            .el-button {
              padding: 4px;
              
              &:hover {
                color: #409EFF;
                background-color: rgba(64, 158, 255, 0.1);
                border-radius: 4px;
              }
              
              &:focus {
                color: #409EFF;
                background-color: rgba(64, 158, 255, 0.1);
                border-radius: 4px;
                box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
                outline: none;
              }
              
              &:active {
                color: #409EFF;
                background-color: rgba(64, 158, 255, 0.15);
              }
              
              &[type="danger"] {
                &:hover {
                  color: #F56C6C;
                  background-color: rgba(245, 108, 108, 0.1);
                }
                
                &:focus {
                  color: #F56C6C;
                  background-color: rgba(245, 108, 108, 0.1);
                  box-shadow: 0 0 0 1px rgba(245, 108, 108, 0.2);
                }
                
                &:active {
                  color: #F56C6C;
                  background-color: rgba(245, 108, 108, 0.15);
                }
              }
            }
          }
        }
        
        .corpus-item-content {
          padding: 10px;
          background-color: #fff;
          border-radius: 4px;
          margin-bottom: 8px;
          max-height: 200px;
          overflow-y: auto;
          color: #606266;
          font-size: 13px;
          line-height: 1.6;
          
          :deep(strong) {
            color: #303133;
            font-weight: 600;
          }
        }
        
        .corpus-item-footer {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #909399;
          
          .corpus-item-time, .corpus-item-id {
            font-size: 12px;
          }
        }
      }
    }
    
    .empty-corpus {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 0;
    }
  }
}

/* 查看语料对话框中Monaco编辑器的样式 */
.view-corpus-editor {
  width: 100%;
  min-height: 320px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  
  :deep(.editor-toolbar) {
    .reset-btn {
      display: none !important; // 强制隐藏重置按钮
    }
  }
  
  :deep(.monaco-editor-wrapper) {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
}

</style> 