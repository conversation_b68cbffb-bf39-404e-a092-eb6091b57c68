<template>
  <div class="knowledge-learn-container">
    <div class="selected-knowledge-header">
      <h3>已选择的知识段落（{{ selectedSegments.length }}）</h3>
      <div class="selection-actions" v-if="selectedSegments.length > 0">
        <span class="processing-count" v-if="hasProcessingSegments">
          <el-icon class="loading-icon"><Loading /></el-icon>
          {{ processingSegmentsCount }} 项转换中
        </span>
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAllChange"
        >
          全选
        </el-checkbox>
        <span class="selected-count" :class="{ 'has-selected': checkedSegments.length > 0 }">
          已选择 {{ checkedSegments.length }} 项
        </span>
        <el-button
          type="primary"
          size="small"
          :disabled="checkedSegments.length === 0"
          @click="handleBatchConvert"
          :loading="batchConvertLoading"
        >
          批量转换语料
        </el-button>
      </div>
    </div>
    
    <div class="selected-knowledge-list">
      <div 
        v-for="(segment, index) in selectedSegments" 
        :key="index"
        class="knowledge-item"
        :class="{ 'is-checked': checkedSegmentsMap[index] }"
        @click="toggleCheckSegment(index, $event)"
      >
        <div class="knowledge-item-header">
          <el-checkbox 
            v-model="checkedSegmentsMap[index]" 
            @change="handleCheckChange"
          />
          <div class="knowledge-item-number">{{ String(index + 1).padStart(2, '0') }}</div>
          <div class="knowledge-item-type">doc</div>
          <div class="knowledge-item-chars">{{ segment.length }}字符</div>
        </div>
        
        <div v-if="getSegmentPath(segment)" class="knowledge-item-path">
          {{ getSegmentPath(segment) }}
        </div>
        
        <div class="knowledge-item-content">
          {{ getFullSegmentContent(segment) }}
        </div>
        <div class="knowledge-item-actions">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleConvertToCorpus(segment, index)"
            :loading="convertStatus[index]?.loading"
          >
            <el-icon class="el-icon--left"><Document /></el-icon>转换语料
          </el-button>
          <el-button 
            type="success" 
            size="small" 
            class="success-button" 
            @click="showConvertResult(index)"
            :disabled="!convertStatus[index]?.success"
          >
            <el-icon class="el-icon--left"><Check /></el-icon>
            保存语料
            <span class="result-count" :key="`history-count-${convertStatus[index]?.conversionHistory?.length || 0}`">
              (存在{{ convertStatus[index]?.conversionHistory?.length || 0 }}条语料)
            </span>
          </el-button>
          <span 
            class="operation-count" 
            :class="{
              'zero-operations': !(convertStatus[index]?.operationCount), 
              'has-operations': convertStatus[index]?.operationCount > 0
            }"
          >
            {{ convertStatus[index]?.operationCount ? '已处理' : '未处理' }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 上一步按钮 -->
    <div class="learn-actions">
      <el-button @click="handlePrevStep" type="default">上一步</el-button>
      <el-tooltip
        content="请确保所有知识段落都已操作至少一次"
        placement="top"
        :disabled="!hasUnoperatedSegments"
      >
        <el-button @click="handleComplete" type="primary" :disabled="hasUnoperatedSegments">完成</el-button>
      </el-tooltip>
    </div>
  </div>

  <!-- 使用抽取出的结果对话框组件 -->
  <ConversionResultDialog
    v-model:visible="conversionResultDialogVisible"
    :conversion-history="getConversionHistory()"
    @update:conversionHistory="handleUpdateConversionHistory"
    :result="currentResult"
    :current-task-id="currentViewTaskId"
    @update:currentTaskId="handleUpdateCurrentTaskId"
    :rg-id="props.rgId"
    :mis-id="props.misId"
    :content-id="props.contentId"
    @view-detail="handleViewDetail"
    @save-corpus="handleSaveCorpus"
    @delete-corpus="handleDeleteCorpus"
  />

  <!-- 添加查看详情对话框 -->
  <CorpusDetailDialog
    v-model:visible="detailDialogVisible"
    :detail-data="currentDetailItem"
    :history-index="currentHistoryIndex"
    :rg-id="getCurrentRgId()" 
    :mis-id="getCurrentMisId()"
    :content-id="props.contentId"
    @compare="handleDetailCompare"
    @save="handleDetailSave"
    @refresh="handleRefresh"
    ref="detailDialogRef"
  />

  <!-- 语料处理加载进度面板 -->
  <LoadingProgressPanel
    v-model:visible="loadingPanel"
    ref="loadingPanelInstance"
    title="相似语料检索中"
    message="系统正在处理您的请求..."
    :tips="[
      '正在检索相似的语料',
      '请耐心等待，处理完成后会自动显示结果'
    ]"
    :duration="300000"
  />
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, reactive, watch, onUnmounted, computed } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { Document, Check, Loading } from '@element-plus/icons-vue'
import httpRequest from '../../../../../utils/httpRequest'
import { API_PATHS } from '../../../request/api'
import ConversionResultDialog from './ConversionResultDialog.vue'
import CorpusDetailDialog from './CorpusDetailDialog.vue'
import LoadingProgressPanel from '../../components/LoadingProgressPanel.vue'
import { useRouter } from 'vue-router'

// 定义组件接收的属性
const props = defineProps({
  selectedSegments: {
    type: Array as () => string[],
    default: () => [],
    required: true
  },
  contentId: {
    type: Number,
    required: true
  },
  rgId: {
    type: [Number, String],
    required: true
  },
  misId: {
    type: String,
    required: true
  }
})

// 定义事件
const emit = defineEmits<{
  (e: 'prev-step'): void
}>()

// 添加选择相关的变量和函数
const checkedSegmentsMap = reactive<Record<number, boolean>>({})
const selectAll = ref(false)
const isIndeterminate = ref(false)
const batchConvertLoading = ref(false)

// 初始化选择状态
const initCheckStatus = () => {
  props.selectedSegments.forEach((_, index) => {
    checkedSegmentsMap[index] = false
  })
}

// 计算已选中的段落索引数组
const checkedSegments = computed(() => {
  return Object.entries(checkedSegmentsMap)
    .filter(([, checked]) => checked)
    .map(([index]) => parseInt(index))
})

// 计算选择状态
const calculateSelectStatus = () => {
  const checkedCount = checkedSegments.value.length
  const totalCount = props.selectedSegments.length
  
  selectAll.value = checkedCount > 0 && checkedCount === totalCount
  isIndeterminate.value = checkedCount > 0 && checkedCount < totalCount
}

// 处理单个选择变化
const handleCheckChange = () => {
  calculateSelectStatus()
}

// 处理全选变化
const handleSelectAllChange = (val: boolean) => {
  props.selectedSegments.forEach((_, index) => {
    checkedSegmentsMap[index] = val
  })
  isIndeterminate.value = false
}

// 切换单个段落选择状态
const toggleCheckSegment = (index: number, event: Event) => {
  // 确保点击事件不被按钮或复选框捕获
  if (event.target && (event.target as HTMLElement).closest('.el-button, .el-checkbox')) {
    return
  }
  
  // 切换选择状态
  checkedSegmentsMap[index] = !checkedSegmentsMap[index]
  
  // 更新选择状态
  calculateSelectStatus()
}

// 批量转换
const handleBatchConvert = async () => {
  if (checkedSegments.value.length === 0) {
    ElMessage.warning('请至少选择一个知识段落')
    return
  }
  
  batchConvertLoading.value = true
  
  try {
    // 为所有选中的段落创建转换任务的Promise数组
    const conversionPromises = checkedSegments.value.map(index => {
      const segment = props.selectedSegments[index]
      // 调用转换函数但不等待它完成，收集Promise
      return {
        index,
        promise: convertToCorpusWithoutPolling(segment, index)
      }
    })
    
    // 显示批量处理开始的消息
    ElMessage.info(`开始处理 ${checkedSegments.value.length} 个知识段落...`)
    
    // 并行发送所有请求获取任务ID
    const taskResults = await Promise.all(conversionPromises.map(item => item.promise))
    
    // 收集所有成功获取任务ID的索引和任务ID
    const successfulTasks = taskResults
      .map((result, i) => ({
        index: conversionPromises[i].index,
        taskId: result.taskId,
        success: result.success
      }))
      .filter(item => item.success && item.taskId)
    
    // 如果有成功创建的任务，开始并行轮询它们的状态
    if (successfulTasks.length > 0) {
      // 同时开始轮询所有任务，不等待它们全部完成
      const pollingPromises = successfulTasks.map(task => 
        pollTaskStatus(task.taskId, task.index, false)
      )
      
      // 等待所有轮询完成
      await Promise.all(pollingPromises)
      
      // 统计实际成功转换的数量
      const actualSuccessCount = successfulTasks.filter(task => 
        convertStatus[task.index]?.success
      ).length
      
      const totalCount = checkedSegments.value.length
      const failedCount = totalCount - actualSuccessCount
      
      if (failedCount > 0) {
        ElMessage.warning(`批量转换完成：共 ${totalCount} 个段落，成功 ${actualSuccessCount} 个，失败 ${failedCount} 个`)
      } else {
        ElMessage.success(`已成功转换 ${actualSuccessCount} 个知识段落`)
      }
    } else {
      ElMessage.error('批量转换失败，未能成功创建任务')
    }
  } catch (error) {
    console.error('批量转换失败', error)
    ElMessage.error('批量转换过程中发生错误，请检查日志')
  } finally {
    batchConvertLoading.value = false
  }
}

// 只发送转换请求获取任务ID，不进行轮询
const convertToCorpusWithoutPolling = async (segment: string, index: number) => {
  
  // 如果正在加载中，跳过此段落
  if (convertStatus[index]?.loading) {
    return { success: false, taskId: null }
  }
  
  // 设置加载状态
  convertStatus[index] = {
    ...convertStatus[index],
    loading: true,
    polling: false
  }
  
  try {
    // 获取有效的rgId和misId
    const rgId = props.rgId
    const misId = props.misId || ''
    
    // 检查rgId是否有效
    if (rgId === null || rgId === undefined) {
      console.error('未提供有效的团队ID(rgId)')
      ElMessage.error('未提供有效的团队ID(rgId)')
      
      convertStatus[index] = {
        ...convertStatus[index],
        loading: false,
        polling: false
      }
      
      return { success: false, taskId: null }
    }
    
    // 构建URL，将rgId和misId作为URL参数
    const url = `${API_PATHS.SPLIT_CONTENT_TO_CORPUS}?rgId=${rgId}&misId=${encodeURIComponent(misId)}`
    
    // 直接使用原始段落内容，不进行处理
    const segmentContent = segment
    
    // 准备请求参数 - 将段落内容放入data数组中
    const requestBody = {
      data: [segmentContent],  // 将原始段落内容放入数组中
      contentId: props.contentId // 确保contentId是有效的
    }
    
    // 调试信息
    console.log('转换语料请求参数:', {
      url,
      requestBody,
      rgId,
      misId
    })
    
    // 发送请求 - 注意rgId和misId在URL中，不在请求体
    const response = await httpRequest.rawRequestPostAsJson(url, requestBody) as unknown as ApiResponse
    
    if (response && response.code === 0) {
      // 请求成功
      const taskId = response.data
      
      // 记录任务ID
      convertStatus[index] = {
        ...convertStatus[index],
        taskId,
        latestTaskId: taskId
      }
      
      return { success: true, taskId }
    } else {
      // 请求失败
      convertStatus[index] = {
        ...convertStatus[index],
        loading: false,
        polling: false
      }
      
      console.error(`提交任务失败: ${response?.message || '未知错误'}`)
      return { success: false, taskId: null }
    }
  } catch (error) {
    // 处理异常
    console.error('转换语料请求失败', error)
    convertStatus[index] = {
      ...convertStatus[index],
      loading: false,
      polling: false
    }
    
    return { success: false, taskId: null }
  }
}

// 处理转换为语料，添加showMessage参数
const convertToCorpus = async (segment: string, index: number, showMessage = true) => {
  
  // 如果正在加载中，不重复请求
  if (convertStatus[index]?.loading) {
    if (showMessage) {
      ElMessage.info('正在转换中，请稍候...')
    }
    return
  }
  
  // 设置加载状态
  convertStatus[index] = {
    ...convertStatus[index],
    loading: true,
    polling: false
  }
  
  try {
    // 获取有效的rgId和misId
    const rgId = props.rgId
    const misId = props.misId || ''
    
    // 检查rgId是否有效
    if (rgId === null || rgId === undefined) {
      console.error('未提供有效的团队ID(rgId)')
      ElMessage.error('未提供有效的团队ID(rgId)')
      
      convertStatus[index] = {
        ...convertStatus[index],
        loading: false,
        polling: false
      }
      
      return
    }
    
    // 构建URL，将rgId和misId作为URL参数
    const url = `${API_PATHS.SPLIT_CONTENT_TO_CORPUS}?rgId=${rgId}&misId=${encodeURIComponent(misId)}`
    
    // 直接使用原始段落内容，不进行处理
    const segmentContent = segment
    
    // 准备请求参数 - 将段落内容放入data数组中
    const requestBody = {
      data: [segmentContent],  // 将原始段落内容放入数组中
      contentId: props.contentId // 确保contentId是有效的
    }
    
    // 调试信息
    console.log('转换语料请求参数:', {
      url,
      requestBody,
      rgId,
      misId
    })
    
    // 发送请求 - 注意rgId和misId在URL中，不在请求体
    const response = await httpRequest.rawRequestPostAsJson(url, requestBody) as unknown as ApiResponse
    
    if (response && response.code === 0) {
      // 请求成功
      const taskId = response.data
      
      // 记录任务ID
      convertStatus[index] = {
        ...convertStatus[index],
        taskId,
        latestTaskId: taskId,
        polling: true
      }
      
      // 显示提示
      if (showMessage) {
        ElMessage.success('任务提交成功，正在处理中...')
      }
      
      // 启动轮询检查任务状态
      await pollTaskStatus(taskId, index, showMessage)
    } else {
      // 请求失败
      convertStatus[index] = {
        ...convertStatus[index],
        loading: false,
        polling: false
      }
      
      ElMessage.error(`提交任务失败: ${response?.message || '未知错误'}`)
    }
  } catch (error) {
    // 处理异常
    console.error('转换语料请求失败', error)
    convertStatus[index] = {
      ...convertStatus[index],
      loading: false,
      polling: false
    }
    
    ElMessage.error('转换语料请求失败，请稍后重试')
  } finally {
    // 如果没有启动轮询，则认为任务失败
    if (!convertStatus[index].polling && !convertStatus[index].success) {
      if (showMessage) {
        ElMessage.error('转换语料任务执行失败')
      }
    }
  }
}

// 别名，保持原来的函数名不变
const handleConvertToCorpus = convertToCorpus

// 轮询任务状态，添加showMessage参数
const pollTaskStatus = async (taskId: string, index: number, showMessage = true) => {
  if (!taskId) return
  
  // 设置轮询状态
  if (convertStatus[index]) {
    convertStatus[index].polling = true
  }
  
  // 移除轮询次数限制和相关变量
  const pollingInterval = 2000 // 每次轮询间隔2秒
  
  try {
    // 无限轮询，直到获取到结果或出错
    while (true) {
      // 构建URL查询任务状态
      const url = `${API_PATHS.QUERY_KM_QA_BY_TASK_ID}?taskId=${taskId}`
      
      // 发送请求
      const res = await httpRequest.rawRequestGet(url) as unknown as ApiResponse
      
      if (res && res.code === 0) {
        const { data } = res
        
        // 根据返回的total字段判断是否有结果
        if (data && data.total > 0) {
          // 有结果，停止轮询
          
          // 检查是否是失败的转换
          const isFailedConversion = data.data && data.data.length > 0 && 
                                   data.data[0].title === '学城FAQ转换处理失败'
          
          // 更新状态
          if (convertStatus[index]) {
            // 重要：首先更新最新任务ID
            convertStatus[index].latestTaskId = taskId
            
            // 然后设置最新结果数据
            convertStatus[index].result = JSON.parse(JSON.stringify(data))
            
            // 添加到转换历史记录
            if (!convertStatus[index].conversionHistory) {
              convertStatus[index].conversionHistory = []
            }
            
            convertStatus[index].conversionHistory.push({
              taskId: taskId,
              timestamp: Date.now(),
              result: JSON.parse(JSON.stringify(data))
            })
            
            // 更新状态
            convertStatus[index].success = true
            convertStatus[index].loading = false
            convertStatus[index].polling = false
            
            // 只有当返回"学城FAQ转换处理失败"时才增加操作次数
            if (isFailedConversion) {
              convertStatus[index].operationCount += 1
            }
          }
          
          // 显示成功消息
          if (showMessage) {
            if (isFailedConversion) {
              ElMessage.warning('知识段落无法转换为语料，已记录操作')
            } else {
              ElMessage.success(`语料转换成功，共 ${data.data?.length || 0} 条`)
            }
          }
          
          // 成功获取结果，退出轮询
          break
        } else {
          // 还没有结果，等待间隔时间后继续轮询
          await new Promise(resolve => setTimeout(resolve, pollingInterval))
        }
      } else {
        // 请求失败
        console.error('轮询任务状态失败:', res?.message)
        
        if (convertStatus[index]) {
          convertStatus[index].polling = false
          convertStatus[index].loading = false
        }
        
        if (showMessage) {
          ElMessage.error(`查询任务状态失败: ${res?.message || '未知错误'}`)
        }
        
        break
      }
    }
  } catch (error) {
    console.error('轮询任务状态出错:', error)
    
    // 出错时停止轮询
    if (convertStatus[index]) {
      convertStatus[index].polling = false
      convertStatus[index].loading = false
    }
    
    if (showMessage) {
      ElMessage.error('轮询任务状态出错，请刷新页面重试')
    }
  }
}

// 在组件挂载时初始化选择状态
initCheckStatus()

// 监听段落列表变化，重新初始化状态
watch(() => props.selectedSegments, () => {
  initConvertStatus()
  initCheckStatus()
  calculateSelectStatus()
}, { deep: true })

// 初始化时调用
initCheckStatus()

// 定义语料结果类型
interface CorpusItem {
  title: string;
  content: string;
  taskMissingInfo?: string[];
  ticketId?: string;
  rgId?: string | number;
  misId?: string;
  taskId?: string;  // 异步生成任务ID，选填字段
  tagsIds?: string; // 标签ID字符串，逗号分隔
  tagsname?: string[]; // 修改：标签名称数组
}

interface CorpusResult {
  total: number;
  data: CorpusItem[];
}

interface ConversionHistoryItem {
  taskId: string;
  timestamp: number;
  result: CorpusResult;
}

// 定义API响应接口
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 处理转换语料的状态
const convertStatus = reactive<Record<number, {
  loading: boolean,
  success: boolean,
  taskId: string | null,
  latestTaskId: string | null, // 最后一次转换的taskId
  polling: boolean,  // 是否正在轮询
  pollingInterval: number | null,  // 轮询的定时器ID
  result?: any,
  operationCount: number, // 添加操作次数计数器
  conversionHistory: Array<{  // 转换历史记录
    taskId: string,
    timestamp: number,
    result: any
  }>
}>>({})

// 初始化状态
const initConvertStatus = () => {
  props.selectedSegments.forEach((_, index) => {
    if (!convertStatus[index]) {
      convertStatus[index] = {
        loading: false,
        success: false,
        taskId: null,
        latestTaskId: null,
        polling: false,
        pollingInterval: null,
        result: null,
        operationCount: 0, // 初始化操作次数为0
        conversionHistory: []
      }
    }
  })
}

// 在组件挂载时初始化状态
initConvertStatus()

// 获取段落路径（换行符前的内容）
const getSegmentPath = (segment: string): string => {
  // 查找第一个换行符
  const firstLineEnd = segment.indexOf('\n')
  
  // 如果找到换行符，返回换行符前的内容作为路径
  if (firstLineEnd !== -1) {
    return segment.substring(0, firstLineEnd).trim()
  }
  
  // 如果没有换行符，返回空字符串（没有路径信息）
  return ''
}

// 获取段落内容（换行符后的内容）
const getSegmentContent = (segment: string): string => {
  // 查找第一个换行符
  const firstLineEnd = segment.indexOf('\n')
  
  // 如果找到换行符，返回换行符后的内容
  if (firstLineEnd !== -1) {
    return segment.substring(firstLineEnd + 1).trim()
  }
  
  // 如果没有换行符，返回原始内容（没有内容部分）
  return segment
}

// 获取完整的段落内容
const getFullSegmentContent = (segment: string): string => {
  return getSegmentContent(segment)
}

// 获取路由实例
const router = useRouter()

// 处理上一步
const handlePrevStep = () => {
  emit('prev-step')
}

// 处理完成按钮
const handleComplete = () => {
  // 跳转到语料管理系统首页，带上rgId参数
  router.push({
    path: '/corpus/index',
    query: {
      rgId: props.rgId
    }
  })
}

// 结果对话框相关变量
const conversionResultDialogVisible = ref(false)
const currentResult = ref<CorpusResult | null>(null)
const currentConversionHistory = ref<ConversionHistoryItem[]>([])
const currentViewTaskId = ref('')
const detailDialogVisible = ref(false)
const currentDetailItem = ref<CorpusItem | null>(null)
const currentHistoryIndex = ref(0)
const detailDialogRef = ref<{
  setCompareResults: (results: any[]) => void
} | null>(null)

// 添加当前操作的段落索引
const currentSegmentIndex = ref<number | null>(null)

// 显示转换结果
const showConvertResult = (index: number) => {
  // 记录当前操作的段落索引
  currentSegmentIndex.value = index
  
  if (!convertStatus[index]) {
    console.error('转换状态不存在:', index)
    return
  }
  
  if (!convertStatus[index]?.result) {
    console.error('转换结果不存在:', index)
    return
  }
  
  console.log('显示转换结果:', {
    index,
    result: convertStatus[index].result,
    total: convertStatus[index].result?.total,
    history: convertStatus[index].conversionHistory.length,
    latestTaskId: convertStatus[index].latestTaskId
  })
  
  // 先设置当前任务ID，确保显示最新结果
  currentViewTaskId.value = convertStatus[index].latestTaskId || ''
  
  // 设置当前结果（使用深拷贝避免引用问题）
  currentResult.value = JSON.parse(JSON.stringify(convertStatus[index].result))
  
  // 显示对话框
  conversionResultDialogVisible.value = true
}

// 处理查看详情事件
const handleViewDetail = (item: CorpusItem) => {
  currentDetailItem.value = item
  detailDialogVisible.value = true
}

// 添加获取当前rgId和misId的方法
const getCurrentRgId = () => {
  return props.rgId;
}

const getCurrentMisId = () => {
  return props.misId;
}

// 添加处理刷新的方法
const handleRefresh = async () => {
  // 根据需要刷新语料列表或更新状态
  // 暂时不需要刷新转换历史记录
}

// 加载面板相关变量
const loadingPanel = ref(false)
const loadingPanelInstance = ref<any>(null)

// 处理详情对话框中的对比事件
const handleDetailCompare = (item: CorpusItem) => {
  // 处理详情对话框中的对比事件，例如搜索相似语料
  if (detailDialogRef.value) {
    // 显示加载进度面板
    loadingPanel.value = true
    
    // 第一阶段进度 - 准备检索
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(15)
    }, 500)
    
    const params = {
      rgId: props.rgId,
      query: item.content
    }
    
    // 第二阶段进度 - 开始检索处理
    setTimeout(() => {
      loadingPanelInstance.value?.setProgress(45)
    }, 1000)
    
    // 调用搜索相似语料的API
    searchSimilarCorpus(params).then(results => {
      // 设置进度为75%
      loadingPanelInstance.value?.setProgress(75)
      
      // 完成进度
      loadingPanelInstance.value?.complete()
      
      // 延迟关闭加载面板，以便用户看到完成状态
      setTimeout(() => {
        loadingPanel.value = false
        
        if (detailDialogRef.value) {
          // 调用子组件的方法设置比对结果
          detailDialogRef.value.setCompareResults(results)
          
          if (results.length === 0) {
            ElMessage.warning('未找到相似语料')
          } else {
            ElMessage.success(`找到 ${results.length} 条相似语料`)
          }
        }
      }, 500)
    }).catch(error => {
      loadingPanel.value = false
      console.error('搜索相似语料失败:', error)
      ElMessage.error(`搜索相似语料失败: ${error.message || '未知错误'}`)
    })
  }
}

// 处理详情对话框中的保存事件
const handleDetailSave = (item: CorpusItem) => {
  // 处理详情对话框中的保存事件，例如保存编辑后的语料
  const loadingService = ElLoading.service({
    lock: true,
    text: '正在保存...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  // 构造请求参数
  const params = {
    misId: props.misId,
    rgId: props.rgId
  }
  
  // 构造请求体
  const requestBody = {
    title: item.title,
    type: -1,
    content: item.content,
    contentId: props.contentId,
    ...(item.ticketId ? { ticketId: item.ticketId } : {}),
    ...(item.taskId ? { taskId: item.taskId } : {}),  // 新增：如果有 taskId，则包含在请求体中
    ...(item.tagsIds ? { tagsIds: item.tagsIds } : {})  // 新增：如果有 tagsIds，则包含在请求体中
  }
  
  // 构造查询字符串
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value as string)}`)
    .join('&')
  
  // 发送保存请求
  httpRequest.rawRequestPostAsJson(
    `/corpus/addCorpus?${queryString}`,
    requestBody
  ).then((response: any) => {
    loadingService.close()
    const typedResponse = response as unknown as ApiResponse<any>
    if (typedResponse?.code === 0) {
      ElMessage.success('保存成功')
      // 刷新数据
      handleRefresh()
    } else {
      ElMessage.error(`保存失败: ${typedResponse?.message || '未知错误'}`)
    }
  }).catch((error: any) => {
    loadingService.close()
    console.error('保存失败:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  })
}

// 添加搜索相似语料的方法
const searchSimilarCorpus = async (params: { rgId: string | number, query: string }) => {
  try {
    const response = await httpRequest.rawRequestPostAsJson('/review/querySimilarContentWithScore', params) as unknown as ApiResponse<any>
    
    if (response?.code === 0) {
      // 处理结果数据，添加选中状态和格式化时间等
      return (response.data?.data || []).map((item: any) => ({
        ...item,
        selected: false, // 添加选中状态属性
        score: (item.score * 100).toFixed(0) + '%',
        type: item.type === null ? '系统故障' : item.type,
        source: item.source || '未知来源',
        title: item.title || '无标题',
        createTime: formatDateTime(item.createTime),
        updateTime: formatDateTime(item.updateTime),
        tagsname: item.tagsname || [] // 新增：确保标签信息能正确传递
      }))
    } else {
      throw new Error(response?.message || '检索失败')
    }
  } catch (error) {
    console.error('搜索相似语料失败:', error)
    return []
  }
}

// 格式化日期时间
const formatDateTime = (timestamp: number | string | undefined): string => {
  if (!timestamp) return '-'
  
  try {
    const date = new Date(typeof timestamp === 'string' ? parseInt(timestamp) : timestamp)
    
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (e) {
    return '-'
  }
}

// 添加计算属性，计算当前正在转换中的语料数量
const processingSegmentsCount = computed(() => {
  return Object.values(convertStatus).filter(status => status.loading).length
})

// 添加计算属性，判断是否有任何语料正在处理中
const hasProcessingSegments = computed(() => {
  return processingSegmentsCount.value > 0
})

// 处理删除语料事件
const handleDeleteCorpus = (data: { item: any, historyIndex: number, updatedHistory: any }) => {
  
  // 确保currentSegmentIndex有效
  if (data.historyIndex !== -1 && currentSegmentIndex.value !== null) {
    const segmentIndex = currentSegmentIndex.value;
    
    // 更新历史记录
    if (convertStatus[segmentIndex]?.conversionHistory) {
      // 创建历史记录的新副本并更新
      const updatedHistory = [...convertStatus[segmentIndex].conversionHistory];
      
      // 替换历史记录中对应索引的项
      updatedHistory[data.historyIndex] = data.updatedHistory;
      
      // 更新历史记录数组
      convertStatus[segmentIndex].conversionHistory = updatedHistory;
      
      // 如果当前查看的就是这条历史，更新当前结果
      if (currentViewTaskId.value === data.updatedHistory.taskId) {
        // 更新当前结果
        currentResult.value = JSON.parse(JSON.stringify(data.updatedHistory.result));
      }
      
      // 更新历史记录长度，这很关键
      if (convertStatus[segmentIndex].result && convertStatus[segmentIndex].result.data) {
        // 如果当前显示的就是被删除的记录，也要更新主结果
        if (convertStatus[segmentIndex].latestTaskId === data.updatedHistory.taskId) {
          convertStatus[segmentIndex].result = JSON.parse(JSON.stringify(data.updatedHistory.result));
        }
      }
      
      // 增加操作计数
      convertStatus[segmentIndex].operationCount += 1;
      
      // 关键：强制更新UI
      convertStatus[segmentIndex] = { ...convertStatus[segmentIndex] };
      
      console.log('删除语料后更新状态:', {
        segmentIndex, 
        historyCount: convertStatus[segmentIndex].conversionHistory.length,
        historyLabel: '条历史',
        corpusCount: convertStatus[segmentIndex].result?.data?.length,
        corpusLabel: '条语料',
        operationCount: convertStatus[segmentIndex].operationCount,
        operationLabel: '次操作'
      });
    }
  }
}

// 添加currentSegment的定义
const currentSegment = computed(() => {
  if (currentSegmentIndex.value === null) return null;
  return convertStatus[currentSegmentIndex.value];
});

// 添加handleSaveCorpus函数
const handleSaveCorpus = (item: CorpusItem) => {
  // 构造请求参数
  const params = {
    misId: props.misId,
    rgId: props.rgId
  }
  
  // 构造请求体
  const requestBody = {
    title: item.title,
    type: -1,
    content: item.content,
    contentId: props.contentId,
    ...(item.ticketId ? { ticketId: item.ticketId } : {}),
    ...(item.taskId ? { taskId: item.taskId } : {}),  // 新增：如果有 taskId，则包含在请求体中
    ...(item.tagsIds ? { tagsIds: item.tagsIds } : {})  // 新增：如果有 tagsIds，则包含在请求体中
  }
  
  // 构造查询字符串
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value as string)}`)
    .join('&')
  
  const loadingService = ElLoading.service({
    lock: true,
    text: '正在保存...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  // 发送保存请求
  httpRequest.rawRequestPostAsJson(
    `/corpus/addCorpus?${queryString}`,
    requestBody
  ).then((response: any) => {
    loadingService.close()
    const typedResponse = response as unknown as ApiResponse<any>
    
    if (typedResponse?.code === 0) {
      ElMessage.success('保存成功')
      
      // 成功保存语料后增加操作计数，将知识段落标记为已处理
      if (currentSegmentIndex.value !== null) {
        convertStatus[currentSegmentIndex.value].operationCount += 1;
        // 强制更新UI
        convertStatus[currentSegmentIndex.value] = { ...convertStatus[currentSegmentIndex.value] };
      }
      
      // 关闭转换结果对话框
      conversionResultDialogVisible.value = false;
      
      // 刷新数据
      handleRefresh()
    } else {
      ElMessage.error(`保存失败: ${typedResponse?.message || '未知错误'}`)
    }
  }).catch((error: any) => {
    loadingService.close()
    console.error('保存失败:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  })
};

// 添加获取历史记录的方法
const getConversionHistory = () => {
  if (currentSegmentIndex.value === null) return [];
  return convertStatus[currentSegmentIndex.value]?.conversionHistory || [];
};

// 添加更新历史记录的处理函数
const handleUpdateConversionHistory = (val: Array<any>) => {
  if (currentSegmentIndex.value === null) return;
  
  convertStatus[currentSegmentIndex.value].conversionHistory = val;
  // 强制更新
  convertStatus[currentSegmentIndex.value] = { ...convertStatus[currentSegmentIndex.value] };
};

// 添加更新当前任务ID的处理函数
const handleUpdateCurrentTaskId = (val: string) => {
  currentViewTaskId.value = val;
};

// 添加计算属性，检查是否有未操作的段落
const hasUnoperatedSegments = computed(() => {
  // 检查是否有任何段落的操作次数为0
  return Object.values(convertStatus).some(status => status.operationCount === 0)
})
</script>

<style lang="scss" scoped>
.knowledge-learn-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 0; // 移除底部边距
  height: 100%; // 填充父容器高度
  display: flex;
  flex-direction: column;
  overflow: hidden; // 避免整体出现滚动条
  
  .selected-knowledge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-shrink: 0; // 防止头部压缩
    
    h3 {
      margin: 0;
    }
    
    .selection-actions {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .selected-count {
        font-size: 14px;
        color: #909399; // 默认灰色
        min-width: 80px; // 固定最小宽度，防止文字长度不同导致位置变化
        
        &.has-selected {
          color: #409EFF; // 有选中项时显示蓝色
        }
      }
      
      .processing-count {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #E6A23C; // 橙色
        min-width: 90px; // 固定最小宽度
        
        .loading-icon {
          margin-right: 5px;
          animation: loading-rotate 1s linear infinite;
        }
      }
    }
  }
  
  .selected-knowledge-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1; // 占据剩余空间
    overflow-y: auto; // 添加垂直滚动条
    padding-right: 5px;
    min-height: 0; // 确保flex子项可以收缩到小于内容高度
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    
    .knowledge-item {
      padding: 20px;
      background-color: #f5f7fa;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      .knowledge-item-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        
        .knowledge-item-number {
          background-color: #f0f2f5;
          color: #606266;
          padding: 4px 10px;
          border-radius: 4px;
          font-size: 14px;
          margin-right: 10px;
          font-weight: 500;
        }
        
        .knowledge-item-type {
          background-color: #e6f7ff;
          color: #1890ff;
          padding: 4px 10px;
          border-radius: 4px;
          font-size: 14px;
          margin-right: 10px;
          font-weight: 500;
        }
        
        .knowledge-item-chars {
          margin-left: auto;
          color: #909399;
          font-size: 13px;
          background-color: #f5f7fa;
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
      
      .knowledge-item-path {
        color: #409EFF;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 12px;
        background-color: rgba(64, 158, 255, 0.1);
        padding: 6px 10px;
        border-radius: 4px;
        display: inline-block;
        border-left: 3px solid #409EFF;
      }
      
      .knowledge-item-content {
        color: #303133;
        font-size: 15px;
        line-height: 1.8;
        white-space: pre-wrap;
        background-color: #fff;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #ebeef5;
        overflow-x: auto;
      }
      
      .knowledge-item-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 12px;
        padding-right: 5px;
        gap: 10px;
        align-items: center;
        
        .operation-count {
          font-size: 13px;
          padding: 4px 10px;
          border-radius: 4px;
          min-width: 80px;
          text-align: center;
          
          &:empty {
            display: none;
          }
          
          &.zero-operations {
            color: #e6a23c; // 黄色
            background: rgba(230, 162, 60, 0.1);
          }
          
          &.has-operations {
            color: #67c23a; // 绿色
            background: rgba(103, 194, 58, 0.1);
          }
        }
        
        .success-button {
          opacity: 0.9;
          
          &:disabled {
            opacity: 0.6;
          }
          
          .result-count {
            font-size: 12px;
            opacity: 0.9;
            margin-left: 2px;
          }
        }
      }
    }
  }
  
  .learn-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
    padding-top: 15px;
    border-top: 1px solid #f0f2f5;
    flex-shrink: 0; // 防止按钮区域压缩
  }
}

.detail-container {
  padding: 10px;
}

.detail-content {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 60px;
}

.missing-info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 