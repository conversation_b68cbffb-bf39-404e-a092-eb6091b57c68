<template>
  <div class="upload-content">
    <div class="website-upload">
      <div v-for="(row, index) in formRows" :key="index" class="form-row">
        <div class="form-item website-item">
          <div v-if="index === 0" class="form-label required">学城网址</div>
          <el-input 
            v-model="row.website" 
            placeholder="请粘贴学城文档网址，部分学城文档由于权限及安全原因可能无法解析" 
            clearable
            @blur="() => handleWebsiteBlur(index)"
            :class="{ 'is-error': row.errors?.website, 'is-success': row.success?.website }"
          />
          <div v-if="row.errors?.website" class="error-message">{{ row.errors.website }}</div>
          <div v-if="row.success?.website" class="success-message">{{ row.success.website }}</div>
        </div>

        <div class="form-item name-item">
          <div v-if="index === 0" class="form-label required">名称</div>
          <el-input 
            v-model="row.name" 
            placeholder="请输入知识名称" 
            clearable
            :maxlength="200"
            show-word-limit
            @blur="() => handleNameBlur(index)"
            :class="{ 'is-error': row.errors?.name, 'is-success': row.success?.name }"
          />
          <div v-if="row.errors?.name" class="error-message">{{ row.errors.name }}</div>
          <div v-if="row.success?.name" class="success-message">{{ row.success.name }}</div>
        </div>
        
        <div class="form-item update-item">
          <div v-if="index === 0" class="form-label">自动更新</div>
          <div class="switch-container">
            <el-switch 
              v-model="row.autoUpdate" 
              :active-value="1" 
              :inactive-value="0"
            />
          </div>
        </div>

        <div class="form-item delete-item">
          <div v-if="index === 0" class="form-label">&nbsp;</div>
          <el-button type="danger" circle @click="handleDeleteRow(index)">
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>

      <div class="form-action">
        <el-button class="action-btn dashed-btn" @click="handleAddWebsite">
          <el-icon><Plus /></el-icon>
          添加网址
        </el-button>
        <el-button class="action-btn dashed-btn" @click="handleBatchImport">
          <el-icon><RefreshLeft /></el-icon>
          学城目录批量导入
        </el-button>
      </div>
    </div>
    
    <!-- 下一步按钮 -->
    <div class="next-step-container">
      <el-tooltip
        v-if="!hasDefaultPermission"
        content="您没有导入工作空间，请先绑定自定义工作空间"
        placement="top"
        effect="dark"
        trigger="hover"
        popper-class="permission-tooltip"
      >
        <el-button 
          type="primary" 
          @click="handleNextStep"
          :disabled="isNextButtonDisabled"
        >
          下一步
        </el-button>
      </el-tooltip>
      <el-button 
        v-else
        type="primary" 
        @click="handleNextStep"
      >
        下一步
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, inject, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, RefreshLeft, Delete } from '@element-plus/icons-vue'
import httpRequest from '../../../../../utils/httpRequest'
import { API_PATHS } from '../../../request/api'

// 表单行错误类型
interface RowErrors {
  website?: string;
  name?: string;
}

// 表单行成功类型
interface RowSuccess {
  website?: string;
  name?: string;
}

// 表单行类型
interface FormRow {
  website: string;
  name: string;
  autoUpdate: number;
  errors?: RowErrors;
  success?: RowSuccess;
  contentId?: number | null;
}

// 尝试从父组件注入 rgId 和 misId
const injectedRgId = inject('rgId', null)
const injectedMisId = inject('misId', '')

// 定义组件接收的属性
const props = defineProps({
  savedWebsites: {
    type: Array as () => Array<{ website: string; name: string; autoUpdate?: number; contentId?: number | null }>,
    default: () => []
  },
  rgId: {
    type: [Number, String],
    required: false,
    default: null
  },
  misId: {
    type: String,
    required: false,
    default: ''
  }
})

// 表单行数据
const formRows = ref<Array<FormRow>>([
  { website: '', name: '', autoUpdate: 0, errors: {}, success: {}, contentId: null }
])

// 已添加的网址列表
const addedWebsites = ref<Array<{ website: string; name: string; autoUpdate: number; contentId?: number | null }>>([])

// 从路由获取的参数
const rgIdFromRoute = ref<number | null>(null)
const misIdFromRoute = ref<string>('')

// 权限相关状态
const hasDefaultPermission = ref(true) // 默认假设有权限
const isNextButtonDisabled = ref(false) // 下一步按钮是否禁用
const permissionMessage = ref('') // 权限提示信息
const permissionChecked = ref(false) // 是否已检查权限

// 在组件挂载时初始化已添加的网址
onMounted(async () => {
  // 恢复已保存的网址列表
  if (props.savedWebsites && props.savedWebsites.length > 0) {
    
    // 清空当前formRows并用savedWebsites填充
    formRows.value = props.savedWebsites.map(item => ({
      website: item.website || '',
      name: item.name || '',
      autoUpdate: typeof item.autoUpdate === 'number' ? item.autoUpdate : 0,
      errors: {},
      success: {},
      contentId: item.contentId || null
    }))
    
    // 如果没有数据，确保至少有一个空行
    if (formRows.value.length === 0) {
      formRows.value = [{ website: '', name: '', autoUpdate: 0, errors: {}, success: {}, contentId: null }]
    }
    
    
    // 清除所有验证状态，确保UI干净
    await nextTick()
    clearAllMessages()
  }

  // 尝试从全局变量获取数据
  try {
    const globalWindow = window as any
    if (globalWindow.rgId) {
      rgIdFromRoute.value = typeof globalWindow.rgId === 'string' ? parseInt(globalWindow.rgId, 10) : globalWindow.rgId
    }
    
    if (globalWindow.misId) {
      misIdFromRoute.value = globalWindow.misId
    }
  } catch (error) {
    console.error('WebsiteUpload: 从全局变量获取参数失败:', error)
  }

  // 尝试从 localStorage 获取数据
  try {
    const localRgId = localStorage.getItem('rgId')
    const localMisId = localStorage.getItem('misId')
    
    if (localRgId) {
      rgIdFromRoute.value = parseInt(localRgId, 10)
    }
    
    if (localMisId) {
      misIdFromRoute.value = localMisId
    }
  } catch (error) {
    console.error('WebsiteUpload: 从localStorage获取参数失败:', error)
  }

  // 尝试从路由获取 rgId 和 misId
  try {
    // 获取完整的 URL
    const url = window.location.href;
    
    // 处理 hash 模式和 history 模式的路由
    let queryString = ''
    
    if (url.includes('#')) {
      // Hash 模式路由: http://example.com/#/path?param=value
      const hashPart = url.split('#')[1] || ''
      if (hashPart.includes('?')) {
        queryString = hashPart.split('?')[1]
      }
    } else if (url.includes('?')) {
      // History 模式路由: http://example.com/path?param=value
      queryString = url.split('?')[1]
    }
    
    if (queryString) {
      const urlParams = new URLSearchParams(queryString)
      
      // 尝试获取 rgId
      const rgIdParam = urlParams.get('rgId')
      if (rgIdParam) {
        rgIdFromRoute.value = parseInt(rgIdParam, 10)
      } else {
        console.warn('WebsiteUpload: URL中未找到rgId参数')
      }
      
      // 尝试获取 misId
      const misIdParam = urlParams.get('misId')
      if (misIdParam) {
        misIdFromRoute.value = misIdParam
      } else {
        console.warn('WebsiteUpload: URL中未找到misId参数')
      }
    } else {
      console.warn('WebsiteUpload: URL中未找到查询参数部分')
    }
  } catch (error) {
    console.error('WebsiteUpload: 从URL获取参数失败:', error)
  }

  // 检查默认工作空间权限
  await checkDefaultWorkspacePermission()
})

// 检查用户默认工作空间权限
const checkDefaultWorkspacePermission = async () => {
  try {
    // 获取当前用户的misId
    const currentMisId = misIdFromRoute.value || props.misId || injectedMisId || ''
    // 获取rgId
    const rgId = rgIdFromRoute.value || props.rgId || injectedRgId || null
    
    if (!currentMisId) {
      console.error('WebsiteUpload: 无法获取用户misId，跳过权限检查')
      return
    }

    if (rgId === null) {
      console.error('WebsiteUpload: 无法获取rgId，跳过权限检查')
      return
    }
    
    // 调用权限检查API
    const response = await httpRequest.rawRequestGet(API_PATHS.WORKSPACE_CHECK_PERMISSION, {
      modifier: currentMisId,
      rgId: rgId
    })
    
    
    // 检查响应数据
    if (response && response.code === 0) {
      hasDefaultPermission.value = response.data.hasPermission
      
      // 如果没有权限，禁用下一步按钮并设置提示信息
      if (!hasDefaultPermission.value) {
        isNextButtonDisabled.value = true
        // 使用固定的提示信息
        permissionMessage.value = '您没有导入工作空间，请先绑定自定义工作空间'
      }
    } else {
      console.error('WebsiteUpload: 权限检查API返回错误:', response)
    }
  } catch (error) {
    console.error('WebsiteUpload: 检查默认工作空间权限失败:', error)
  } finally {
    permissionChecked.value = true
  }
}

// 定义事件
const emit = defineEmits<{
  (e: 'add-website', data: { website: string; name: string; autoUpdate: number; contentId?: number | null }): void
  (e: 'batch-import'): void
  (e: 'next-step', data: {
    websites: Array<{ website: string; name: string; autoUpdate: number; contentId?: number | null }>,
    rgId: number | null,
    misId: string
  }): void
}>()

// 验证网址是否存在
const checkUrlExists = async (url: string) => {
  try {
    // 优先使用 props，然后是注入的值，最后是从路由获取的值
    const effectiveRgId = props.rgId || injectedRgId || rgIdFromRoute.value
    const effectiveMisId = props.misId || injectedMisId || misIdFromRoute.value
    
    // 打印全部可能的来源
    console.log({
      'props.rgId': props.rgId,
      'injectedRgId': injectedRgId,
      'rgIdFromRoute': rgIdFromRoute.value,
      'effectiveRgId': effectiveRgId,
      'props.misId': props.misId,
      'injectedMisId': injectedMisId,
      'misIdFromRoute': misIdFromRoute.value,
      'effectiveMisId': effectiveMisId
    })
    
    // 检查是否获取到有效的rgId
    if (effectiveRgId === null || effectiveRgId === undefined) {
      console.error('WebsiteUpload: 未提供有效的团队ID(rgId)参数')
      ElMessage.error('未提供有效的团队ID(rgId)参数')
      return { 
        canAdd: false, 
        exists: false, 
        contentTitle: '', 
        message: '未提供有效的团队ID(rgId)参数',
        contentId: null
      }
    }
    
    // 类型转换
    const rgId = typeof effectiveRgId === 'string' ? parseInt(effectiveRgId, 10) : effectiveRgId
    const misId = effectiveMisId || ''
    
    // 检查misId是否有效
    if (!misId) {
      console.warn('WebsiteUpload: 未提供有效的用户ID(misId)参数')
    }

    const response = await httpRequest.rawRequestGet(API_PATHS.CHECK_URL_EXISTS, {
      rgId,
      url: url,
      misId
    }) as any // 添加类型断言
    
    if (response && response.code === 0) {
      
      // 返回结构化数据，包含更多信息
      return {
        canAdd: response.data?.canAdd === true,
        exists: response.data?.exists === true,
        contentTitle: response.data?.contentTitle || '',
        message: response.msg || '',
        contentId: response.data?.contentId || null
      }
    } else {
      const errorMsg = response?.message || response?.msg || '验证网址失败'
      ElMessage.error(errorMsg)
      return { 
        canAdd: false, 
        exists: false, 
        contentTitle: '', 
        message: errorMsg,
        contentId: null
      }
    }
  } catch (error: any) {
    console.error('验证网址失败:', error)
    const errorMsg = `验证网址失败：${error.message || '未知错误'}`
    ElMessage.error(errorMsg)
    return { 
      canAdd: false, 
      exists: false, 
      contentTitle: '', 
      message: errorMsg,
      contentId: null
    }
  }
}

// 验证名称是否存在
const checkNameExists = async (name: string) => {
  try {
    // 优先使用 props，然后是注入的值，最后是从路由获取的值
    const effectiveRgId = props.rgId || injectedRgId || rgIdFromRoute.value
    const effectiveMisId = props.misId || injectedMisId || misIdFromRoute.value
    
    // 检查是否获取到有效的rgId
    if (effectiveRgId === null || effectiveRgId === undefined) {
      console.error('WebsiteUpload: 未提供有效的团队ID(rgId)参数')
      ElMessage.error('未提供有效的团队ID(rgId)参数')
      return { 
        exists: false, 
        canAdd: false,
        message: '未提供有效的团队ID(rgId)参数'
      }
    }
    
    // 类型转换
    const rgId = typeof effectiveRgId === 'string' ? parseInt(effectiveRgId, 10) : effectiveRgId
    const misId = effectiveMisId || ''
    
    const response = await httpRequest.rawRequestGet(API_PATHS.CHECK_NAME_EXISTS, {
      rgId,
      misId,
      name
    }) as any
    
    if (response && response.code === 0) {
      
      // 根据新的理解调整返回逻辑
      // 当data为false时，表示名称不存在，可以添加
      const exists = response.data === true
      const canAdd = response.data === false || response.success === true
      
      return {
        exists,
        canAdd,
        message: response.msg || ''
      }
    } else {
      const errorMsg = response?.message || response?.msg || '验证名称失败'
      console.error(errorMsg)
      return { 
        exists: false, 
        canAdd: true,
        message: errorMsg
      }
    }
  } catch (error: any) {
    console.error('验证名称失败:', error)
    const errorMsg = `验证名称失败：${error.message || '未知错误'}`
    console.error(errorMsg)
    return { 
      exists: false, 
      canAdd: true,
      message: errorMsg
    }
  }
}

// 清除所有行的错误和成功信息
const clearAllMessages = () => {
  formRows.value.forEach(row => {
    row.errors = {}
    row.success = {}
  })
}

// 清除指定行的错误和成功信息
const clearRowMessages = (index: number) => {
  if (formRows.value[index]) {
    formRows.value[index].errors = {}
    formRows.value[index].success = {}
  }
}

// 清除指定行字段的错误和成功信息
const clearFieldMessages = (index: number, field: 'website' | 'name') => {
  const row = formRows.value[index]
  if (row) {
    if (row.errors) row.errors[field] = undefined
    if (row.success) row.success[field] = undefined
  }
}

// 设置行错误
const setRowError = (index: number, field: 'website' | 'name', message: string) => {
  if (!formRows.value[index].errors) {
    formRows.value[index].errors = {}
  }
  formRows.value[index].errors[field] = message
  
  // 清除相同字段的成功信息
  if (formRows.value[index].success) {
    formRows.value[index].success[field] = undefined
  }
}

// 设置行成功
const setRowSuccess = (index: number, field: 'website' | 'name', message: string) => {
  if (!formRows.value[index].success) {
    formRows.value[index].success = {}
  }
  formRows.value[index].success[field] = message
  
  // 清除相同字段的错误信息
  if (formRows.value[index].errors) {
    formRows.value[index].errors[field] = undefined
  }
}

// 处理网址输入框失焦
const handleWebsiteBlur = async (index: number) => {
  const row = formRows.value[index]
  
  // 清除当前字段的错误和成功信息
  clearFieldMessages(index, 'website')
  
  if (!row.website.trim()) {
    setRowError(index, 'website', '请填写网址')
    return
  }
  
  // 检查URL格式
  if (!isValidUrl(row.website.trim())) {
    setRowError(index, 'website', '请输入有效的网址，例如：https://example.com')
    return
  }
  
  // 检查当前输入的网址是否与其他行重复
  const otherWebsites = formRows.value
    .filter((_, i) => i !== index)
    .map(r => r.website.trim())
  
  if (otherWebsites.includes(row.website.trim())) {
    setRowError(index, 'website', '该网址已在表单中存在')
    return
  }
  
  // 设置临时状态，表示正在验证
  setRowSuccess(index, 'website', '正在验证网址...')
  
  try {
    const result = await checkUrlExists(row.website)
    
    if (result.exists) {
      // URL 已存在
      setRowError(index, 'website', '该网址已存在，请更换其他网址')
    } else if (result.canAdd) {
      // URL 可以添加
      setRowSuccess(index, 'website', result.message || '网址验证通过，可以添加')
      
      // 保存contentId
      row.contentId = result.contentId
      
      // 如果有标题且名称字段为空，自动填充名称
      if (result.contentTitle && !row.name.trim()) {
        row.name = result.contentTitle
        // 为名称字段也设置成功状态
        setRowSuccess(index, 'name', '已自动填充名称')
      }
    } else {
      // URL 不可添加
      setRowError(index, 'website', result.message || '该网址无法添加，请检查网址是否有效')
    }
  } catch (error) {
    console.error('网址验证出错:', error)
    setRowError(index, 'website', '网址验证失败，请稍后重试')
  }
}

// 验证URL格式是否有效
const isValidUrl = (url: string) => {
  try {
    const trimmedUrl = url.trim()
    
    // 简单检查是否以http或https开头
    if (!trimmedUrl.match(/^https?:\/\//i)) {
      return false
    }
    
    // 使用URL构造函数验证
    new URL(trimmedUrl)
    
    // 检查域名是否有效
    const domainPattern = /^https?:\/\/([a-z0-9]([a-z0-9-]*[a-z0-9])?\.)+[a-z0-9]([a-z0-9-]*[a-z0-9])?/i
    return domainPattern.test(trimmedUrl)
  } catch (e) {
    return false
  }
}

// 处理名称输入框失焦
const handleNameBlur = async (index: number) => {
  const row = formRows.value[index]
  
  // 清除当前字段的错误和成功信息
  clearFieldMessages(index, 'name')
  
  if (!row.name.trim()) {
    setRowError(index, 'name', '请填写知识名称')
    return
  }
  
  // 检查当前输入的名称是否与其他行重复
  const otherNames = formRows.value
    .filter((_, i) => i !== index)
    .map(r => r.name.trim())
  
  if (otherNames.includes(row.name.trim())) {
    setRowError(index, 'name', '该知识名称已在表单中存在')
    return
  }
  
  // 设置临时状态，表示正在验证
  setRowSuccess(index, 'name', '正在验证名称...')
  
  try {
    const result = await checkNameExists(row.name)
    
    if (result.exists) {
      // 名称已存在
      setRowError(index, 'name', '该名称已存在，请更换其他名称')
    } else if (result.canAdd) {
      // 名称可以添加
      setRowSuccess(index, 'name', result.message || '名称可用')
    } else {
      // 名称不可添加
      setRowError(index, 'name', result.message || '该名称无法使用，请换一个名称')
    }
  } catch (error) {
    console.error('名称验证出错:', error)
    // 如果出错，默认允许使用（减少对用户的阻碍）
    setRowSuccess(index, 'name', '名称可用')
  }
}

// 删除行
const handleDeleteRow = (index: number) => {
  formRows.value.splice(index, 1)
  // 确保至少保留一行
  if (formRows.value.length === 0) {
    formRows.value.push({ website: '', name: '', autoUpdate: 0, errors: {}, success: {}, contentId: null })
  }
}

// 验证表单
const validateForm = async () => {
  // 清除所有错误和成功信息
  clearAllMessages()
  
  let isValid = true
  
  // 检查空值和URL格式
  formRows.value.forEach((row, index) => {
    // 验证网址
    if (!row.website.trim()) {
      setRowError(index, 'website', '请填写网址')
      isValid = false
    } else if (!isValidUrl(row.website.trim())) {
      setRowError(index, 'website', '请输入有效的网址，例如：https://example.com')
      isValid = false
    }
    
    // 验证名称
    if (!row.name.trim()) {
      setRowError(index, 'name', '请填写知识名称')
      isValid = false
    }
  })
  
  if (!isValid) return false
  
  // 检查网址重复
  const websiteMap = new Map<string, number>()
  
  formRows.value.forEach((row, index) => {
    const website = row.website.trim()
    if (websiteMap.has(website)) {
      const firstIndex = websiteMap.get(website)!
      setRowError(index, 'website', '网址不能重复')
      setRowError(firstIndex, 'website', '网址不能重复')
      isValid = false
    } else {
      websiteMap.set(website, index)
    }
  })
  
  // 检查名称重复
  const nameMap = new Map<string, number>()
  
  formRows.value.forEach((row, index) => {
    const name = row.name.trim()
    if (nameMap.has(name)) {
      const firstIndex = nameMap.get(name)!
      setRowError(index, 'name', '知识名称不能重复')
      setRowError(firstIndex, 'name', '知识名称不能重复')
      isValid = false
    } else {
      nameMap.set(name, index)
    }
  })
  
  if (!isValid) return false
  
  // 对每一行进行API验证
  try {
    // 创建验证任务列表
    const validationTasks = formRows.value.map(async (row, index) => {
      // 显示验证中状态
      setRowSuccess(index, 'website', '正在验证网址...')
      setRowSuccess(index, 'name', '正在验证名称...')
      
      // 并行验证网址和名称
      const [urlResult, nameResult] = await Promise.all([
        checkUrlExists(row.website),
        checkNameExists(row.name)
      ])
      
      // 验证网址
      if (!urlResult.canAdd) {
        setRowError(index, 'website', urlResult.message || '网址无法添加')
        return false
      } else {
        row.contentId = urlResult.contentId
        setRowSuccess(index, 'website', urlResult.message || '网址验证通过')
      }
      
      // 验证名称
      if (!nameResult.canAdd) {
        setRowError(index, 'name', nameResult.message || '名称无法使用')
        return false
      } else {
        setRowSuccess(index, 'name', nameResult.message || '名称可用')
      }
      
      return true
    })
    
    // 等待所有验证任务完成
    const results = await Promise.all(validationTasks)
    isValid = results.every(result => result)
    
    if (!isValid) {
      ElMessage.warning('表单验证未通过，请检查错误信息')
    }
    
    return isValid
  } catch (error) {
    console.error('表单验证过程中发生错误:', error)
    ElMessage.error('表单验证过程中发生错误，请稍后重试')
    return false
  }
}

// 添加网址
const handleAddWebsite = async () => {
  // 检查最后一行是否填写完整
  const lastIndex = formRows.value.length - 1
  const lastRow = formRows.value[lastIndex]
  
  // 先清除可能存在的错误和成功信息
  clearRowMessages(lastIndex)
  
  // 检查基本字段
  if (!lastRow.website.trim()) {
    setRowError(lastIndex, 'website', '请先填写网址再添加新行')
    return
  }
  
  if (!lastRow.name.trim()) {
    setRowError(lastIndex, 'name', '请先填写知识名称再添加新行')
    return
  }
  
  // 检查URL格式
  if (!isValidUrl(lastRow.website.trim())) {
    setRowError(lastIndex, 'website', '请输入有效的网址，例如：https://example.com')
    return
  }
  
  // 检查网址或名称是否与已有行重复
  const existingWebsites = formRows.value
    .slice(0, lastIndex)
    .map(row => row.website.trim())
  
  if (existingWebsites.includes(lastRow.website.trim())) {
    setRowError(lastIndex, 'website', '该网址已存在，请输入不同的网址')
    return
  }
  
  const existingNames = formRows.value
    .slice(0, lastIndex)
    .map(row => row.name.trim())
  
  if (existingNames.includes(lastRow.name.trim())) {
    setRowError(lastIndex, 'name', '该名称已存在，请输入不同的名称')
    return
  }
  
  // 所有本地验证通过，设置成功状态
  setRowSuccess(lastIndex, 'website', '网址格式正确')
  setRowSuccess(lastIndex, 'name', '名称可用')
  
  // 添加新行
  formRows.value.push({ website: '', name: '', autoUpdate: 0, errors: {}, success: {}, contentId: null })
  
  // 添加成功提示
  ElMessage.success('已添加新行')
}

// 批量导入
const handleBatchImport = () => {
  emit('batch-import')
}

// 下一步
const handleNextStep = async () => {
  
  // 清除所有错误和成功信息，重新检查
  clearAllMessages()
  
  // 检查数据完整性，但不进行API验证
  let isValid = true
  const incompleteRows = []
  const invalidUrlRows = []
  const duplicateWebsites = new Map()
  const duplicateNames = new Map()
  
  // 检查每行是否填写了必要信息，URL格式是否有效
  for (const [index, row] of formRows.value.entries()) {
    // 检查必填字段
    if (!row.website.trim() || !row.name.trim()) {
      isValid = false
      incompleteRows.push(index + 1)
      continue
    }
    
    // 检查URL格式
    if (!isValidUrl(row.website.trim())) {
      isValid = false
      invalidUrlRows.push(index + 1)
      setRowError(index, 'website', '请输入有效的网址，例如：https://example.com')
      continue
    }
    
    // 检查重复网址
    const websiteKey = row.website.trim().toLowerCase()
    if (duplicateWebsites.has(websiteKey)) {
      isValid = false
      const firstIndex = duplicateWebsites.get(websiteKey)
      setRowError(index, 'website', '网址不能重复')
      setRowError(firstIndex, 'website', '网址不能重复')
    } else {
      duplicateWebsites.set(websiteKey, index)
    }
    
    // 检查重复名称
    const nameKey = row.name.trim().toLowerCase()
    if (duplicateNames.has(nameKey)) {
      isValid = false
      const firstIndex = duplicateNames.get(nameKey)
      setRowError(index, 'name', '知识名称不能重复')
      setRowError(firstIndex, 'name', '知识名称不能重复')
    } else {
      duplicateNames.set(nameKey, index)
    }
  }
  
  // 如果有不完整的行，提示用户
  if (incompleteRows.length > 0) {
    ElMessage.warning(`第 ${incompleteRows.join(', ')} 行的网址和名称不能为空`)
    return
  }
  
  // 如果有无效URL的行，提示用户
  if (invalidUrlRows.length > 0) {
    ElMessage.warning(`第 ${invalidUrlRows.join(', ')} 行的网址格式无效`)
    return
  }
  
  // 如果有重复项且验证不通过，直接提示
  if (!isValid) {
    ElMessage.warning('表单中存在重复的网址或名称，请修改后再提交')
    return
  }
  
  // 获取有效的rgId和misId
  const effectiveRgId = props.rgId || injectedRgId || rgIdFromRoute.value
  const effectiveMisId = props.misId || injectedMisId || misIdFromRoute.value
  
  // 确保有有效的rgId和misId
  if (!effectiveRgId || !effectiveMisId) {
    console.warn('无法获取有效的rgId或misId，可能影响后续处理')
    ElMessage.warning('无法获取有效的组织ID或用户ID，可能影响数据提交')
  }
  
  // 准备提交的数据，过滤掉错误和成功信息
  const submitData = formRows.value.map(row => ({
    website: row.website.trim(),
    name: row.name.trim(),
    autoUpdate: row.autoUpdate,
    contentId: row.contentId
  }))
  
  // 添加rgId和misId到提交数据中
  const finalData = {
    websites: submitData,
    rgId: effectiveRgId ? (typeof effectiveRgId === 'string' ? parseInt(effectiveRgId, 10) : effectiveRgId) : null,
    misId: effectiveMisId || ''
  }
  
  
  // 提交数据
  emit('next-step', finalData)
}

// 暴露给父组件的属性
defineExpose({
  formRows,
  get addedWebsites() {
    // 将formRows转换为仅包含必要数据的格式
    return formRows.value.filter(row => row.website.trim() && row.name.trim()).map(row => ({
      website: row.website,
      name: row.name,
      autoUpdate: row.autoUpdate,
      contentId: row.contentId
    }))
  }
})
</script>

<style lang="scss" scoped>
.upload-content {
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
  
  .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 16px;
    flex-wrap: nowrap;
    overflow-x: auto;
    
    .form-item {
      min-width: 0;
      
      &.website-item {
        flex: 3;
        min-width: 180px;
      }
      
      &.name-item {
        flex: 2;
        min-width: 120px;
      }
      
      &.update-item {
        flex: 1;
        min-width: 100px;
        
        .switch-container {
          height: 32px;
          display: flex;
          align-items: center;
        }
      }
      
      &.delete-item {
        width: 40px;
        flex: none;
      }
    }
  }
  
  .form-item {
    margin-bottom: 12px;
    position: relative;
    
    .form-label {
      font-weight: bold;
      margin-bottom: 6px;
      display: flex;
      align-items: center;
      
      &.required:before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }
    
    .error-message {
      color: #f56c6c;
      font-size: 12px;
      margin-top: 4px;
      line-height: 1.2;
      min-height: 14px;
    }
    
    .success-message {
      color: #67c23a;
      font-size: 12px;
      margin-top: 4px;
      line-height: 1.2;
      min-height: 14px;
    }
  }
  
  .is-error :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #f56c6c inset;
  }
  
  .is-success :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #67c23a inset;
  }
  
  .form-action {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    
    .dashed-btn {
      border: 1px dashed #409eff;
      color: #409eff;
      background-color: transparent;
      border-radius: 20px;
      padding: 8px 15px;
      
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
  
  .next-step-container {
    position: relative;
    margin-top: 20px;
    text-align: center;
  }
}

.website-upload .el-input {
  width: 100%;
}

@media (max-width: 500px) {
  .upload-content {
    .form-row {
      flex-direction: row;
      gap: 10px;
      
      .form-item {
        &.website-item,
        &.name-item,
        &.update-item,
        &.delete-item {
          min-width: 0;
          flex: 1 0 auto;
        }
      }
    }
    
    .form-action {
      flex-direction: row;
      justify-content: flex-start;
      flex-wrap: nowrap;
      overflow-x: auto;
      
      .action-btn {
        flex-shrink: 0;
        min-width: fit-content;
      }
    }
  }
}

.custom-tooltip {
  position: absolute;
  bottom: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 300px;
  text-align: center;
  z-index: 10;
}

.tooltip-content {
  background-color: #333;
  color: #fff;
  padding: 10px;
  border-radius: 4px;
  width: 100%;
  font-size: 14px;
  line-height: 1.4;
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -8px;
  border-width: 8px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

:deep(.permission-tooltip) {
  font-size: 14px;
  line-height: 1.5;
  padding: 10px 12px;
  max-width: 300px;
}
</style> 