<template>
  <div class="upload-methods">
    <el-button 
      class="method-btn" 
      :class="{ 'active': modelValue === 'qa' }" 
      @click="updateValue('qa')"
    >
      导入FAQ
    </el-button>
    <el-button 
      class="method-btn" 
      :class="{ 'active': modelValue === 'website' }" 
      @click="updateValue('website')"
    >
      导入网站
    </el-button>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const updateValue = (type: string) => {
  emit('update:modelValue', type)
}
</script>

<style lang="scss" scoped>
.upload-methods {
  margin-bottom: 16px;
  
  .method-btn.active {
    color: #409eff;
    border-color: #409eff;
    background-color: #ecf5ff;
  }
}
</style> 