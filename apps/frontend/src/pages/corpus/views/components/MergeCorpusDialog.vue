<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue'
import MonacoEditor from './MonacoEditor.vue'
import { ElMessage } from 'element-plus'

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({
      title: '',
      content: '',
      taskId: '',
      ticketId: '',
      corpusIdList: [],
      type: ''
    })
  },
  rgId: {
    type: [Number, String],
    default: 0
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'save'])

// 使用计算属性处理对话框的可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  }
})

// 合并表单数据
const mergeFormData = ref({
  title: '',
  content: '',
  corpusIdList: [],
  type: '-1'
})

// 强制组件刷新的key
const editorKey = ref(0)

// 监听formData变化，更新表单数据
watch([() => props.formData, () => props.modelValue], ([newFormData, newVisible]) => {
  if (newVisible && newFormData) {
    
    mergeFormData.value.title = newFormData.title || ''
    mergeFormData.value.content = newFormData.content || ''
    mergeFormData.value.corpusIdList = newFormData.corpusIdList || []
    mergeFormData.value.type = '-1' // 固定为'-1'
    
    // 刷新编辑器
    setTimeout(() => {
      editorKey.value += 1
    }, 200)
  }
}, { immediate: true, deep: true })

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理保存
const handleSave = () => {
  // 验证表单
  if (!mergeFormData.value.title.trim()) {
    ElMessage.warning('请输入合并后标题')
    return
  }
  
  if (!mergeFormData.value.content.trim()) {
    ElMessage.warning('请输入合并后内容')
    return
  }
  
  if (!mergeFormData.value.corpusIdList || mergeFormData.value.corpusIdList.length === 0) {
    ElMessage.warning('语料ID列表为空，无法合并')
    return
  }
  
  // 发送保存事件
  emit('save', {
    ...mergeFormData.value,
    taskId: props.formData.taskId,
    ticketId: props.formData.ticketId,
    rgId: props.rgId
  })
}
</script>

<template>
  <!-- 合并语料对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="合并语料"
    width="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="merge-corpus-dialog"
  >
    <el-form label-width="100px">
      <el-form-item label="合并后标题" required>
        <el-input
          v-model="mergeFormData.title"
          placeholder="请输入合并后标题"
        />
      </el-form-item>
      <el-form-item label="合并后内容" required>
        <MonacoEditor
          :key="editorKey"
          v-model="mergeFormData.content"
          language="markdown"
          height="400px"
          class="merge-editor"
          :options="{
            wordWrap: 'on',
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            lineNumbers: 'on',
            lineDecorationsWidth: 0,
            folding: true,
            renderLineHighlight: 'all',
            automaticLayout: true
          }"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">确定合并</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.merge-corpus-dialog {
  :deep(.el-dialog) {
    margin-top: 5vh !important;
    min-height: 60vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__header) {
    padding: 20px;
    margin-right: 0;
    border-bottom: 1px solid #dcdfe6;
  }

  :deep(.el-dialog__body) {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  :deep(.el-dialog__footer) {
    padding: 10px 20px 20px;
    border-top: none;
  }

  .merge-editor {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    min-height: 400px;
    
    :deep(.monaco-editor-wrapper) {
      display: block !important;
      height: 400px !important;
      
      .monaco-editor-container {
        width: 100% !important;
        height: calc(100% - 40px) !important;
        min-height: 360px !important;
        display: block !important;
        overflow: visible !important;
      }
    }
  }
}
</style> 