<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import httpRequest from '@/utils/httpRequest'
import MonacoEditor from './MonacoEditor.vue'
import { FORM_RULES } from '../../types'
import { Plus } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  currentMisId: string
  currentTeam: number
  size?: 'default' | 'large' | 'small'
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
  (e: 'open-tag-management'): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default'
})

const emit = defineEmits<Emits>()

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 表单数据
const formData = ref({
  title: '',
  content: '',
  tagsIds: '' // 新增：标签ID字符串，逗号分隔
})

// 表单规则
const rules = FORM_RULES

// 表单ref
const formRef = ref<FormInstance>()

// 提交状态
const isSubmitting = ref(false)

// 标签管理相关的响应式数据
const tagOptions = ref<{ id: string; name: string }[]>([])
const selectedTagIds = ref<string[]>([])
const isLoadingTags = ref(false)
const defaultTag = ref<{ id: string; name: string } | null>(null)
const isInitializing = ref(false)

// 获取标签列表
const fetchTagList = async () => {
  if (!props.currentTeam) return
  
  try {
    isLoadingTags.value = true
    const response = await httpRequest.rawRequestGet('/rgTags/listTags', {
      rgId: props.currentTeam
    })
    
    if (response?.code === 0 && response?.data && Array.isArray(response.data)) {
      // 根据实际API返回格式调整映射
      const formattedTags = response.data.map((tag: any) => {
        const id = tag.id === null ? null : String(tag.id || tag.tagId || tag.value || '')
        const name = tag.name || tag.tagName || tag.label || `标签${id || 'default'}`
        
        return {
          id,
          name
        }
      }).filter(tag => tag.name) // 只过滤掉无名称的数据
      
      // 设置默认标签（第一个标签，id为null）
      if (formattedTags.length > 0 && formattedTags[0].id === null) {
        defaultTag.value = formattedTags[0]
        // 从可选标签中移除默认标签，因为它会自动显示
        tagOptions.value = formattedTags.slice(1)
      } else {
        defaultTag.value = null
        tagOptions.value = formattedTags
      }
      
    } else {
      console.warn('获取标签列表失败:', response?.msg || '数据格式错误')
      tagOptions.value = []
      defaultTag.value = null
    }
  } catch (error) {
    console.error('获取标签列表出错:', error)
    tagOptions.value = []
    defaultTag.value = null
  } finally {
    isLoadingTags.value = false
  }
}

// 处理标签选择变化
const handleTagSelectionChange = (tagIds: string[]) => {
  // 如果正在初始化，不执行更新逻辑
  if (isInitializing.value) return
  
  // 过滤掉空字符串，防止"添加标签"选项被误选
  const filteredTagIds = tagIds.filter(tag => tag !== '')
  
  // 限制最多选择3个标签
  if (filteredTagIds.length > 3) {
    ElMessage.warning('最多只能选择3个标签')
    const limitedTagIds = filteredTagIds.slice(0, 3)
    // 更新选中的标签ID
    selectedTagIds.value = limitedTagIds
  } else {
    selectedTagIds.value = filteredTagIds
  }
  
  // 更新formData中的tagsIds
  const selectedTags = tagOptions.value.filter(tag => selectedTagIds.value.includes(tag.id))
  
  let submitTagsIds: string = ''
  
  if (selectedTags.length === 0 && defaultTag.value) {
    // 没有选择任何标签时，提交空字符串，后端会处理为默认标签
    submitTagsIds = ''
  } else {
    // 有选择标签时，提交选择的标签ID
    submitTagsIds = selectedTagIds.value.join(',')
  }
  
  formData.value.tagsIds = submitTagsIds
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value || isSubmitting.value) return
  
  // 检查是否选择了值班组
  if (!props.currentTeam) {
    ElMessage.error('请先选择值班组')
    return
  }


  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 设置提交状态为true，阻止重复提交
        isSubmitting.value = true
        
        // URL 参数
        const params = {
          misId: props.currentMisId,
          rgId: props.currentTeam
        }
        
        // JSON 请求体
        const bodyData = {
          title: formData.value.title,
          type: -1,
          content: formData.value.content,
          tagsIds: formData.value.tagsIds || '' // 新增：添加标签ID字段
        }
        
        
        const response = await httpRequest.rawRequestPostAsJson(
          `/corpus/addCorpus?misId=${params.misId}&rgId=${params.rgId}`,
          bodyData
        )
        
        if (response?.code === 0) {
          ElMessage({
            type: 'success',
            message: '新增语料成功'
          })
          dialogVisible.value = false
          // 重置表单
          resetForm()
          // 通知父组件刷新列表
          emit('refresh')
        } else {
          ElMessage.error(`新增语料失败: ${response?.msg || '未知错误'}`)
        }
      } catch (error) {
        console.error('新增语料出错:', error)
        ElMessage.error(`新增语料失败: ${error.message || '未知错误'}`)
      } finally {
        // 最后重置提交状态
        isSubmitting.value = false
        ;(document.activeElement as HTMLElement)?.blur()
      }
    } else {
      // 表单验证失败，不需要发请求
      isSubmitting.value = false
    }
  })
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
  ;(document.activeElement as HTMLElement)?.blur()
}

// 重置表单
const resetForm = () => {
  formData.value = {
    title: '',
    content: '',
    tagsIds: ''
  }
  // 重置标签相关状态
  selectedTagIds.value = []
  tagOptions.value = []
  defaultTag.value = null
  isInitializing.value = false
  // 清除表单验证状态
  formRef.value?.clearValidate()
}

// 初始化表单数据
const initForm = () => {
  formData.value = {
    title: '问题：',
    content: `**用户反馈问题**：XXXX

**分析过程**：
1. XXX
2. XXX

**结论**：
1. XXXX

**处理方案**：
1. XXXX
2. XXXX`,
    tagsIds: ''
  }
  // 重置标签选择状态
  selectedTagIds.value = []
}

// 监听对话框可见性
watch(() => dialogVisible.value, async (newVisible) => {
  if (newVisible) {
    // 设置初始化标志
    isInitializing.value = true
    
    // 对话框打开时获取标签列表
    await fetchTagList()
    
    // 等待标签列表加载完成后再初始化选中状态
    await nextTick()
    
    // 初始化选中的标签状态
    const originalTagsIds = formData.value.tagsIds
    if (originalTagsIds) {
      const tagIds = originalTagsIds.split(',').filter(id => id.trim())
      selectedTagIds.value = tagIds
    } else {
      selectedTagIds.value = []
    }
    
    // 完成初始化
    isInitializing.value = false
  } else {
    // 对话框关闭时重置标签状态
    selectedTagIds.value = []
    isInitializing.value = false
  }
})

// 监听selectedTagIds变化，过滤掉空值
watch(selectedTagIds, (newTagIds) => {
  // 过滤掉空字符串，防止"添加标签"选项被误选
  const filteredTagIds = newTagIds.filter(tag => tag !== '')
  if (filteredTagIds.length !== newTagIds.length && !isInitializing.value) {
    selectedTagIds.value = filteredTagIds
  }
}, { deep: true })

// 处理点击"添加标签"选项
const handleAddTagClick = (e: Event) => {
  // 强制阻止所有默认行为和事件传播
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  
  // 确保"添加标签"的空值不会被添加到选中的标签中
  selectedTagIds.value = selectedTagIds.value.filter(tag => tag !== '')
  
  // 强制关闭下拉框
  nextTick(() => {
    const selectComponents = document.querySelectorAll('.el-select')
    selectComponents.forEach((select: any) => {
      if (select.__vue__ && select.__vue__.blur) {
        select.__vue__.blur()
      }
    })
  })
  
  // 直接通知父组件打开标签管理，保留当前对话框状态
  emit('open-tag-management')
  
  return false
}

// 暴露方法给父组件
defineExpose({
  initForm,
  refreshTagList: fetchTagList
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="新增语料"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="问题标题" prop="title">
        <el-input
          v-model="formData.title"
          placeholder="请输入问题标题"
          :size="size"
        />
      </el-form-item>
      
      <!-- 添加标签选择 -->
      <el-form-item label="标签" class="tags-item">
        <!-- 默认标签显示（当没有选择其他标签时） -->
        <div v-if="selectedTagIds.length === 0 && defaultTag" class="default-tag-container">
          <el-tag
            :closable="false"
            type="info"
            class="default-tag"
          >
            {{ defaultTag.name }}
          </el-tag>
        </div>
        
        <el-select
          v-model="selectedTagIds"
          multiple
          :placeholder="`请选择标签（最多3个，已选${selectedTagIds.length}个）`"
          :size="size"
          :loading="isLoadingTags"
          @change="handleTagSelectionChange"
          style="width: 100%"
          clearable
          :multiple-limit="3"
        >
          <el-option
            v-for="tag in tagOptions"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
          >
            {{ tag.name }}
          </el-option>
          <!-- 添加"添加标签"选项 -->
          <el-option
            key="add-tag"
            label="📝 添加标签"
            value=""
            style="border-top: 1px solid #e4e7ed;"
            :disabled="true"
            @mousedown.stop.prevent="handleAddTagClick"
            @click.stop.prevent="handleAddTagClick"
          >
            <div 
              style="display: flex; align-items: center; color: #409EFF; font-weight: 500; cursor: pointer; pointer-events: auto;" 
              @mousedown.stop.prevent="handleAddTagClick"
              @click.stop.prevent="handleAddTagClick"
            >
              <el-icon style="margin-right: 8px; font-size: 14px;"><Plus /></el-icon>
              <span>添加标签</span>
            </div>
          </el-option>
        </el-select>
        <!-- 选择状态提示 -->
        <div v-if="tagOptions.length > 0" style="margin-top: 8px; font-size: 12px; color: #909399;">
          共{{ tagOptions.length }}个标签可选，已选择{{ selectedTagIds.length }}/3个
          <span v-if="defaultTag && selectedTagIds.length === 0">，当前显示默认标签</span>
        </div>
      </el-form-item>
      
      <el-form-item label="语料内容" prop="content">
        <!-- 使用Monaco编辑器替换textarea -->
        <MonacoEditor
          v-model="formData.content"
          language="markdown"
          height="320px"
          :options="{
            wordWrap: 'on',
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            lineNumbers: 'on',
            lineDecorationsWidth: 0,
            folding: true,
            renderLineHighlight: 'all',
            automaticLayout: true
          }"
          class="add-corpus-editor"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel" :size="size" :disabled="isSubmitting">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :size="size" :loading="isSubmitting" :disabled="isSubmitting">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
/* 新增语料对话框中Monaco编辑器的样式 */
.add-corpus-editor {
  width: 100%;
  min-height: 320px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* Monaco Editor样式调整 */
:deep(.monaco-editor-wrapper) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  
  .editor-toolbar {
    background-color: #f7f9fc;
  }
}

/* 确保编辑器宽度正确 */
:deep(.monaco-editor) {
  width: 100% !important;
  
  .overflow-guard {
    width: 100% !important;
  }
}

/* 对话框样式调整 */
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }
}

/* 处理Monaco编辑器在对话框中的显示 */
:deep(.monaco-editor-wrapper) {
  margin: 0;
  
  &.fullscreen {
    z-index: 3001;
  }
}

/* 标签管理样式 */
.tags-item {
  margin-bottom: 16px;
  
  .default-tag-container {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    .default-tag {
      background-color: #f4f4f5;
      border-color: #d3d4d6;
      color: #606266;
      font-size: 12px;
      
      &:hover {
        background-color: #f4f4f5;
        border-color: #d3d4d6;
      }
    }
  }
  
  :deep(.el-form-item__label) {
    color: #606266;
    font-weight: 600;
    font-size: 14px;
    min-width: 80px;
    white-space: nowrap;
    margin-right: 8px;
  }
  
  :deep(.el-form-item__content) {
    flex: 1;
    margin-left: 0 !important;
  }
  
  :deep(.el-select) {
    width: 100%;
    
    .el-select__wrapper {
      width: 100%;
    }
    
    .el-select__tags {
      flex-wrap: wrap;
      max-width: 100%;
    }
    
    .el-tag {
      margin: 2px 4px 2px 0;
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
      font-size: 12px;
      
      &:hover {
        background-color: #bae7ff;
        border-color: #69c0ff;
      }
      
      .el-tag__close {
        background-color: rgba(24, 144, 255, 0.1);
        color: #1890ff;
        border-radius: 50%;
        
        &:hover {
          background-color: #1890ff;
          color: #fff;
        }
      }
    }
  }
}

/* 确保新增对话框内的标签选择下拉框显示在分类标签管理面板下方 */
:deep(.el-select-dropdown) {
  z-index: 2300 !important;
}

:deep(.el-popper) {
  z-index: 2300 !important;
}

/* 确保标签管理相关的所有弹窗都有足够高的z-index */
:deep(.tag-dialog) {
  z-index: 3500 !important;
}

:deep(.tag-add-dialog) {
  z-index: 3500 !important;
}

:deep(.tag-edit-dialog) {
  z-index: 3510 !important;
}
</style> 