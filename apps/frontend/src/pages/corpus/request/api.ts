export const API_PATHS = {
  // 值班组相关
  QUERY_RG_LIST: 'ticket/queryRGList',

  // 语料相关
  QUERY_CORPUS_LIST: 'corpus/queryCorpusListByRgId',
  QUERY_CORPUS_LIST_BY_CONTENT_ID: 'corpus/queryCorpusListByRgIdContentId',
  ADD_CORPUS: 'corpus/addCorpus',
  MODIFY_CORPUS: 'corpus/modifyCorpusByTicketId',
  DELETE_CORPUS: 'corpus/deleteCorpusByTicketIds',
  SAVE_IGNORE_CORPUS: 'corpus/saveIgnoreCorpus',
  SAVE_MERGE_CORPUS: 'corpus/saveMergeCorpus',
  CREATE_MERGE_CORPUS_TASK: 'corpus/createMergeCorpusTask',
  DELETE_CORPUS_BY_TICKET_IDS: 'corpus/deleteCorpusByTicketIds',
  QUERY_LATEST_SOP_BY_RG_ID: 'corpus/queryLatestSopByRgId',
  UPDATE_SOP: 'corpus/updateSop',
  EXPORT_ALL_CORPUS_TO_EXCEL: 'corpus/exportAllCorpusToExcel',

  // TT相关
  QUERY_TT_LIST: 'ticket/queryTTListByRgId',
  QUERY_TT_LIST_ALL: 'ticket/queryTTListByRgIdALL',
  ADD_TT_CONTENT: 'ticket/addTTContentByTTId',
  BATCH_ADD_TT_CONTENT: 'ticket/batchAddTTContentByTTIds',
  QUERY_MODEL_OUTPUT: 'review/queryModelOutputByTaskId',

  // 检索对比相关
  QUERY_SIMILAR_CONTENT: 'review/querySimilarContentWithScore',

  // 转换语料相关
  SAVE_MODIFIED_OUTPUT: 'review/saveModifiedOutput',

  // 任务列表相关
  QUERY_TASK_LIST: 'review/queryModelOutputsByRgId',
  QUERY_PERSONAL_TASK_LIST: 'review/queryModelOutputsByRgIdAndMisId',
  QUERY_KM_QA_BY_TASK_ID: 'review/queryKmQaByTaskId',

  //学城文档相关
  GET_KM_META_BY_URL: 'km/getKmMetaByUrl',
  GET_KM_SPLIT_BY_CONTENT_ID: 'km/getKmSplitByContentId',
  CHECK_URL_EXISTS: 'km/checkUrlExists',
  CHECK_NAME_EXISTS: 'km/checkNameExists',
  IMPORT_WEBSITES: 'km/importWebsites',
  ADD_BATCH_DOCUMENT: 'km/addBatchDocumentByNameAndUrl',
  DELETE_DOCUMENT: 'km/deleteByDocumentId',
  SPLIT_CONTENT_TO_CORPUS: 'km/splitContentToCorpus',
  QUERY_DOCUMENT_BY_RG_ID: 'km/queryDocumentByRgId',
  CHANGE_REFRESH_BY_DOCUMENT_ID: 'km/changeRefreshByDocumentId',

  // 审核页面相关API
  QUERY_MODEL_OUTPUT_BY_TASK_ID: 'review/queryModelOutputByTaskId',
  QUERY_SIMILAR_CONTENT_WITH_SCORE: 'review/querySimilarContentWithScore',

  // 语料统计相关
  QUERY_STAT_BY_APP_ID_AND_DATE: 'statistics/queryStatByAppIdAndDate',
  QUERY_FRIDAY_APP_INFO_LIST: 'statistics/queryFridayAppInfoList',
  QUERY_CONVERSATION_MESSAGES: 'statistics/queryConversationMessages',
  GET_CORPUS_OPERATION_STATUS: 'statistics/getCorpusOperationStatus', // 新增：获取语料服务运营状态报表

  // 工作空间相关API
  WORKSPACE_CONFIG: '/api/workspace/config',
  WORKSPACE_TEST_VALIDATE: '/api/workspace/test/validate',
  WORKSPACE_CONFIG_DELETE: '/api/workspace/config/delete',
  WORKSPACE_CHECK_PERMISSION: '/api/workspace/checkPermission',

  // 自定义背景知识查询接口路径常量
  QUERY_LATEST_BACKGROUND_KNOWLEDGE_BY_RG_ID: 'corpus/queryLatestBackgroundKnowledgeByRgId',
  UPDATE_BACKGROUND_KNOWLEDGE: 'corpus/updateBackgroundKnowledge',
  
  // AccessKey相关API
  ACCESS_KEY_LIST: '/accessKey/list',
  ACCESS_KEY_GENERATE: '/accessKey/generate',
  ACCESS_KEY_DELETE: '/accessKey/delete',
  ACCESS_KEY_UPDATE_NAME: '/accessKey/updateName',

  // 标签管理相关API
  RG_TAGS_LIST: '/rgTags/listTags',
  RG_TAGS_ADD: '/rgTags/addTag',
  RG_TAGS_DELETE: '/rgTags/deleteTag',
  RG_TAGS_UPDATE: '/rgTags/updateTag'
} as const; 