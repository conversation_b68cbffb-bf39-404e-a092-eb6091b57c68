// 语料项接口定义
export interface CorpusItem {
  ticketId: string
  title: string
  content: string
  type: number
  source: number
  misId: string
  createTime: string
  updateTime: string
  isMerged?: boolean
  kbMergedToId?: string
  taskId?: string
  backgroundKnowledge?: string
  sop?: string
  rule?: string
  tagsIds?: string
  tagsname?: string[]
}

// 召回的语料分片信息接口
export interface RecalledCorpusChunkInfo {
  corpusId: string
  parentId: string
  source: number
  title: string
  content: string
}

// 工单详情DTO接口
export interface TicketDetailDTO {
  ticketId: string
  title: string
  content?: string
  assigned?: string
  state?: string
  createdAt?: number
  updatedAt?: number
  // 其他工单相关字段
}

// 应用状态DTO接口
export interface AppStatusDTO {
  appId: string
  appName: string
  beginTime: number
  endTime: number
  conversationCount: number
  userMessageCount: number
  questionList: Array<{
    question: string
    questionTime: number
    questionMessageId: string
    questionConversationId: string
    solved: boolean
  }>
  questionAndRecallInfos: Array<{
    questionContent: string
    questionMessageId: string
    questionConversationId: string
    recalledCorpusChunkInfoList: RecalledCorpusChunkInfo[]
  }>
  questionClusters: Array<{
    questionPattern: string
    questionList: Array<{
      question: string
      questionMessageId: string
      questionConversationId: string
      questionTime: number
      solved: boolean
    }>
  }>
  timeRangeTicketIdList: TicketDetailDTO[]
  relatedRgList: number[]
}

// 值班组列表项接口定义
export interface RGListItem {
  id: number
  ownerName: string
  name: string
  ownerDispalyName: string | null
  createdAt: number
  owner: boolean
}

// TT列表数据接口定义
export interface TTItem {
  groupId: number
  rgId: number
  ticketId: string
  itemName: string
  typeName: string
  name: string
  sla: string
  ticketType: string
  assigned: string
  state: string
  categoryName: string
  updatedAt: number
  updatedBy: string
  createdAt: number
  desc: string
  kbStatus: number
  kbTimestamp: number
  kbUpdateUser: string
}

// 分页配置接口
export interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

// 表单数据接口
export interface FormData {
  title: string
  content: string
}

// 合并表单数据接口
export interface MergeFormData extends FormData {
  type: string
}

// 转换表单数据接口
export interface ConvertFormData extends FormData {
  originalContent: string
  taskId: string
  ticketId: string
  rgId: number
}

// API响应接口
export interface ApiResponse<T> {
  code: number
  msg: string
  data: T
  success: boolean
}

// 语料列表响应数据接口
export interface CorpusResponse {
  code: number
  msg: string
  data: {
    total: number
    totalPage: number
    pageSize: number
    currentPage: number
    list: CorpusItem[]
  }
  success: boolean
}

// 表格列配置接口
export interface TableColumn {
  prop: string
  label: string
  width?: number
  minWidth?: number
  showOverflowTooltip?: boolean
  sortable?: boolean
}

// 语料表格列配置
export const CORPUS_TABLE_COLUMNS: TableColumn[] = [
  { prop: 'ticketId', label: '语料ID', width: 100 },
  { prop: 'title', label: '标题', minWidth: 120, showOverflowTooltip: true },
  { prop: 'content', label: '内容', minWidth: 200, showOverflowTooltip: false },
  { prop: 'source', label: '来源', width: 100 },
  { prop: 'misId', label: '创建人', width: 100 },
  { prop: 'tagsname', label: '标签', width: 150, showOverflowTooltip: true },
  { prop: 'updateTime', label: '更新时间', width: 160 }
];

// TT列表列配置
export const TT_TABLE_COLUMNS: TableColumn[] = [
  { prop: 'ticketId', label: 'TT ID', width: 120 },
  { prop: 'name', label: 'TT标题', showOverflowTooltip: true, sortable: true },
  { prop: 'assigned', label: 'TT处理人', width: 150, minWidth: 150, showOverflowTooltip: true },
  { prop: 'createdAt', label: 'TT创建时间', width: 180, minWidth: 180, showOverflowTooltip: true },
  { prop: 'updatedAt', label: 'TT更新时间', width: 180, minWidth: 180, showOverflowTooltip: true },
  { prop: 'kbStatus', label: '状态', width: 100 },
  { prop: 'kbUpdateUser', label: '入库人', width: 150, minWidth: 100, showOverflowTooltip: true },
  { prop: 'kbTimestamp', label: '语料更新时间', width: 180, minWidth: 180, showOverflowTooltip: true }
];

// 检索结果列配置
export const COMPARE_TABLE_COLUMNS: TableColumn[] = [
  { prop: 'ticketId', label: 'TASK-ID', width: 150 },
  { prop: 'score', label: '相似度', width: 100, sortable: true },
  { prop: 'title', label: '标题', minWidth: 200, showOverflowTooltip: true },
  { prop: 'content', label: '内容', minWidth: 300, showOverflowTooltip: false },
  { prop: 'updateTime', label: '更新时间', width: 160 }
];

// 来源映射
export const SOURCE_MAP: Record<number, string> = {
  0: 'TT/大象群',
  1: '其他',
  2: '手动新增',
  3: '学城',
  4: '合并',
  5: '单聊'
};

// 状态映射类型接口
export interface StatusMapItem {
  label: string
  type: string
}

// 状态映射
export const KB_STATUS_MAP: Record<number, StatusMapItem> = {
  0: { label: '未入库', type: 'info' },     // UNHANDLED
  1: { label: '已入库', type: 'success' },   // HANDLED
  2: { label: '无需入库', type: 'warning' }, // MARKED_AS_IGNORED
  3: { label: '已合并', type: 'primary' },   // MERGED
  4: { label: '待解决', type: 'danger' },     // UNRESOLVED_NO_GROUP
  5: { label: '未建群', type: 'no-group' }     // RESOLVED_NO_GROUP
};

// 表单规则
export const FORM_RULES = {
  title: [{ required: true, message: '请输入问题标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入语料内容', trigger: 'blur' }]
};

// 合并表单规则
export const MERGE_FORM_RULES = {
  title: [{ required: true, message: '请输入合并后标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入合并后内容', trigger: 'blur' }]
};

// 编辑表单规则
export const EDIT_FORM_RULES = {
  title: [{ required: true, message: '请输入问题标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入语料内容', trigger: 'blur' }]
};

// 分页大小选项
export const PAGE_SIZES = [10, 20, 50, 100];

// 语料助手机器人消息项
export interface CorpusBotMessageItem {
  msgId: string;
  fromUid: number;
  fromPubId: string;
  gid: number;
  cts: number;
  type: number;
  message: string;
  msgExt: string;
  fromMis: string;
  fromName: string;
  userOrgId: string;
  userOrgName: string;
  userOrgPath: string;
}

// 语料生成任务统计项
export interface CorpusGenerateTaskItemForStats {
  taskId: string;
  ticketId: string;
  dxGroupId: number;
  taskStatus: number;
  creatorUserDxId: number;
  creatorMisId: string;
  creatorUserName: string;
  creatorOrgId: string;
  creatorOrgName: string;
  creatorOrgPath: string;
  platformId: number;
  taskMessage: string;
  taskSaved: boolean;
  createTime: string;
  updateTime: string;
}

// 值班组信息统计项
export interface CorpusStatsRgInfoItem {
  rgName: string;
  rgId: string;
  rgDesc: string;
  rgOwner: string;
  rgOrgId: string;
  rgOrgName: string;
  rgOrgPath: string;
  timeRangeCorpusCount: number;
  totalCorpusCount: number;
  allRunningStatusTasks: CorpusGenerateTaskItemForStats[]; // 新增：值班组下所有正在进行中的任务
}

// Friday空间信息统计项
export interface CorpusStatsFridaySpaceItem {
  spaceId: string;
  spaceName: string;
  spaceDesc: string | null;
  ctime: number;
  spaceRgIds: string[];
}

// 语料运营状态数据传输对象
export interface CorpusOperationStatusDTO {
  corpusBotMessages: CorpusBotMessageItem[];
  dailyCorpusGenerateTaskCountList: CorpusGenerateTaskItemForStats[];
  corpusStatsRgInfoList: CorpusStatsRgInfoItem[];
  corpusStatsFridaySpaceList: CorpusStatsFridaySpaceItem[];
}
