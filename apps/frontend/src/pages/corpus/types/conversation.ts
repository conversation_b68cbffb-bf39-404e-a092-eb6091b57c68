// 会话消息DTO
export interface FridayConversationMessageDTO {
  messageId: string;
  conversationId: string;
  userId: string;
  userType: string;
  role: string;
  generateType: string;
  message: string;
  status: string;
  addTime: string | number;
  parentMessageId: string;
  appId: string;
  conversationName: string;
}

// 问题项
export interface QuestionItem {
  question: string;
  questionMessageId: string;
  questionTime: number;
  questionConversationId: string;
  solved: boolean;
} 