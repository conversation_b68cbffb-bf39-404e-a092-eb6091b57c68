export interface TaskItem {
  taskId: string;
  ticketId: string;
  taskStatus: number;
  creatorMisId: string;
  createTime: string;
  updateTime: string;
  title?: string;
  content?: string;
  taskMessage?: string;
  taskPlatform?: number;
  platformId?: number;
}

export interface TaskListResponse {
  code: number;
  msg: string;
  data: TaskItem[];
  success: boolean;
}

export const TaskStatusMap = {
  0: { label: '进行中', type: 'warning' },
  1: { label: '成功', type: 'success' },
  2: { label: '失败', type: 'danger' }
}; 