<template>
  <div class="multimodal-container">
    <header class="header">
      <h1>多模态AI：感知世界的新方式</h1>
      <p class="subtitle">了解AI如何像人类一样，通过多种感官理解世界</p>
    </header>

    <section class="content-section">
      <div class="text-block">
        <h2>什么是多模态AI？</h2>
        <p>想象一下，如果AI只能阅读文字，却看不见图像、听不到声音，就像一个只有单一感官的人，它对世界的理解会非常有限。</p>
        <p>多模态AI就像拥有多种感官的人类，它可以同时理解：</p>
        <ul class="feature-list">
          <li>
            <div class="icon">📝</div>
            <div class="feature-desc">
              <strong>文本</strong> - 理解和生成各种语言的文字内容
            </div>
          </li>
          <li>
            <div class="icon">🖼️</div>
            <div class="feature-desc">
              <strong>图像</strong> - 识别物体、场景、人物和视觉内容
            </div>
          </li>
          <li>
            <div class="icon">🔊</div>
            <div class="feature-desc">
              <strong>音频</strong> - 处理语音、音乐和各种声音信号
            </div>
          </li>
          <li>
            <div class="icon">🎬</div>
            <div class="feature-desc">
              <strong>视频</strong> - 理解动态画面和时间序列内容
            </div>
          </li>
        </ul>
      </div>
      
      <div class="illustration">
        <div class="modal-diagram">
          <div class="unimodal">
            <h3>单模态AI</h3>
            <div class="diagram-container">
              <div class="input-box">文本输入</div>
              <div class="arrow">→</div>
              <div class="model-box">AI模型</div>
              <div class="arrow">→</div>
              <div class="output-box">文本输出</div>
            </div>
            <p class="diagram-desc">只能处理单一类型的数据，如纯文本</p>
          </div>
          
          <div class="multimodal">
            <h3>多模态AI</h3>
            <div class="diagram-container">
              <div class="input-group">
                <div class="input-box">文本</div>
                <div class="input-box">图像</div>
                <div class="input-box">音频</div>
              </div>
              <div class="arrow">→</div>
              <div class="model-box complex">多模态AI模型</div>
              <div class="arrow">→</div>
              <div class="output-group">
                <div class="output-box">文本</div>
                <div class="output-box">图像</div>
                <div class="output-box">音频</div>
              </div>
            </div>
            <p class="diagram-desc">可以同时处理多种类型的数据，形成更全面的理解</p>
          </div>
        </div>
      </div>
    </section>

    <section class="content-section">
      <div class="illustration">
        <div class="brain-diagram">
          <div class="brain-img">
            <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path d="M100,20 C140,20 170,50 170,90 C170,130 140,160 100,160 C60,160 30,130 30,90 C30,50 60,20 100,20 Z" fill="none" stroke="#333" stroke-width="2"/>
              <path d="M100,35 C130,35 155,55 155,90 C155,125 130,145 100,145 C70,145 45,125 45,90 C45,55 70,35 100,35 Z" fill="none" stroke="#333" stroke-width="1"/>
              <line x1="40" y1="50" x2="95" y2="50" stroke="#e74c3c" stroke-width="2"/>
              <circle cx="40" cy="50" r="5" fill="#e74c3c"/>
              <text x="100" y="50" font-size="7">视觉区域</text>
              <line x1="40" y1="80" x2="95" y2="80" stroke="#3498db" stroke-width="2"/>
              <circle cx="40" cy="80" r="5" fill="#3498db"/>
              <text x="100" y="80" font-size="7">听觉区域</text>
              <line x1="40" y1="110" x2="95" y2="110" stroke="#2ecc71" stroke-width="2"/>
              <circle cx="40" cy="110" r="5" fill="#2ecc71"/>
              <text x="100" y="110" font-size="7">语言区域</text>
              <line x1="40" y1="140" x2="95" y2="140" stroke="#f39c12" stroke-width="2"/>
              <circle cx="40" cy="140" r="5" fill="#f39c12"/>
              <text x="100" y="140" font-size="7">整合区域</text>
            </svg>
          </div>
          <p class="diagram-desc">多模态AI模仿人类大脑，整合不同感官的信息</p>
        </div>
      </div>
      
      <div class="text-block">
        <h2>为什么多模态AI如此重要？</h2>
        <p>我们人类通过多种感官来认识世界 - 我们看到物体、听到声音、感受触觉、闻到气味，然后做出决策。</p>
        <p>多模态AI正在向这种全面感知能力迈进，通过整合多种数据类型，AI能够：</p>
        <ul>
          <li>更全面地理解情境和内容</li>
          <li>处理复杂的跨媒体任务</li>
          <li>提供更自然、更人性化的交互体验</li>
          <li>在信息不完整的情况下做出更准确的判断</li>
        </ul>
        <blockquote>
          多模态是AI进化的必然方向，使AI能够像人类一样，通过多种感知渠道理解复杂的世界。
        </blockquote>
      </div>
    </section>

    <section class="content-section">
      <div class="text-block">
        <h2>多模态AI的工作原理</h2>
        <p>多模态AI的核心在于它能够处理和融合不同类型的数据，主要通过以下步骤：</p>
        
        <div class="process-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>数据预处理</h3>
              <p>对不同模态的数据（文本、图像、音频等）进行标准化处理，使它们可以被模型理解。</p>
            </div>
          </div>
          
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>特征提取</h3>
              <p>使用专门的神经网络从各种模态中提取关键特征，例如用CNN处理图像，Transformer处理文本。</p>
            </div>
          </div>
          
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>模态融合</h3>
              <p>将不同模态的特征整合在一起，可能是早期融合（在处理前）、晚期融合（在处理后）或混合融合。</p>
            </div>
          </div>
          
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h3>综合理解</h3>
              <p>模型学习不同模态之间的关系和联系，形成对输入内容的整体理解。</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="illustration">
        <div class="fusion-diagram">
          <h3>模态融合方式</h3>
          <div class="fusion-methods">
            <div class="fusion-method">
              <h4>早期融合</h4>
              <div class="method-diagram">
                <div class="input-group">
                  <div class="small-box">文本</div>
                  <div class="small-box">图像</div>
                </div>
                <div class="fusion-point">
                  <div class="arrow">→</div>
                  <div class="fusion-box">融合</div>
                  <div class="arrow">→</div>
                </div>
                <div class="model-box">处理</div>
              </div>
            </div>
            
            <div class="fusion-method">
              <h4>晚期融合</h4>
              <div class="method-diagram">
                <div class="input-group">
                  <div class="process-line">
                    <div class="small-box">文本</div>
                    <div class="arrow">→</div>
                    <div class="small-model">处理</div>
                  </div>
                  <div class="process-line">
                    <div class="small-box">图像</div>
                    <div class="arrow">→</div>
                    <div class="small-model">处理</div>
                  </div>
                </div>
                <div class="fusion-point-late">
                  <div class="arrow">→</div>
                  <div class="fusion-box">融合</div>
                </div>
              </div>
            </div>
          </div>
          <p class="diagram-desc">不同的融合方式适用于不同类型的任务</p>
        </div>
      </div>
    </section>

    <section class="content-section">
      <div class="illustration">
        <div class="applications-grid">
          <div class="app-card">
            <div class="app-icon">🔍</div>
            <h3>智能搜索</h3>
            <p>通过图像和文本一起搜索，找到最相关的内容</p>
          </div>
          <div class="app-card">
            <div class="app-icon">🏥</div>
            <h3>医疗诊断</h3>
            <p>结合影像、病历文本和声音数据进行全面诊断</p>
          </div>
          <div class="app-card">
            <div class="app-icon">🚗</div>
            <h3>自动驾驶</h3>
            <p>整合摄像头、雷达和其他传感器数据做出决策</p>
          </div>
          <div class="app-card">
            <div class="app-icon">🎨</div>
            <h3>创意创作</h3>
            <p>根据文本描述生成图像或从图像生成故事</p>
          </div>
          <div class="app-card">
            <div class="app-icon">🛒</div>
            <h3>个性化推荐</h3>
            <p>分析用户的图像偏好和文本反馈提供更精准推荐</p>
          </div>
          <div class="app-card">
            <div class="app-icon">🗣️</div>
            <h3>虚拟助手</h3>
            <p>能看、能听、能说的全方位智能助手</p>
          </div>
        </div>
      </div>
      
      <div class="text-block">
        <h2>多模态AI的应用场景</h2>
        <p>多模态AI正在各个领域展现出革命性的应用潜力：</p>
        
        <div class="application-example">
          <h3>电商领域</h3>
          <p>多模态AI可以分析客户的浏览历史、购买记录和视觉偏好，提供真正个性化的产品推荐。例如，根据用户曾经交互过的图像或视频中的风格、颜色偏好推荐相似产品。</p>
        </div>
        
        <div class="application-example">
          <h3>视频处理</h3>
          <p>AI可以将视频内容转换为文本，不仅分析语音内容，还能理解视觉线索，提高视频的可搜索性和组织性，方便定位特定信息，并帮助内容本地化和翻译。</p>
        </div>
        
        <div class="application-example">
          <h3>医疗健康</h3>
          <p>结合患者的医学图像、病历文本和语音描述症状，多模态AI可以提供更全面准确的辅助诊断，发现单一模态可能忽略的微妙线索。</p>
        </div>
      </div>
    </section>

    <section class="content-section">
      <div class="text-block">
        <h2>多模态AI的挑战与未来</h2>
        
        <div class="challenges">
          <h3>当前面临的挑战</h3>
          <ul>
            <li>
              <strong>数据整合复杂：</strong> 不同类型的数据需要专门的处理方法，融合预测也很复杂
            </li>
            <li>
              <strong>数据收集与质量：</strong> 收集对齐的多模态数据困难，不同模态的数据质量要求不同
            </li>
            <li>
              <strong>模态建模：</strong> 不同模态间的迁移能力和表示学习还面临技术挑战
            </li>
            <li>
              <strong>计算资源需求：</strong> 处理多模态数据需要更多的计算资源和存储空间
            </li>
          </ul>
        </div>
        
        <div class="future">
          <h3>未来发展趋势</h3>
          <ul>
            <li>
              <strong>数据规模扩大：</strong> 随着更多数据集的加入，模型性能和泛化能力将得到提升
            </li>
            <li>
              <strong>模型结构创新：</strong> 新的架构将更好地处理不同媒体数据的关联和转换
            </li>
            <li>
              <strong>自监督学习：</strong> 减少对大量标记数据的依赖，提高模型学习效率
            </li>
            <li>
              <strong>人机交互革新：</strong> 更自然、直观的交互方式将改变我们使用技术的方式
            </li>
          </ul>
        </div>
        
        <blockquote>
          多模态AI不仅是技术的进步，更是人类与机器交流方式的革命性变革，它让我们离"理解"的本质更近了一步。
        </blockquote>
      </div>
      
      <div class="illustration">
        <div class="future-diagram">
          <svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
            <!-- 时间轴 -->
            <line x1="50" y1="150" x2="350" y2="150" stroke="#333" stroke-width="2"/>
            
            <!-- 过去 -->
            <circle cx="100" cy="150" r="8" fill="#e74c3c"/>
            <text x="100" y="130" text-anchor="middle" font-size="12">单模态AI</text>
            <text x="100" y="180" text-anchor="middle" font-size="10">各自独立发展</text>
            
            <!-- 现在 -->
            <circle cx="200" cy="150" r="8" fill="#3498db"/>
            <text x="200" y="130" text-anchor="middle" font-size="12">多模态AI</text>
            <text x="200" y="180" text-anchor="middle" font-size="10">初步融合</text>
            
            <!-- 未来 -->
            <circle cx="300" cy="150" r="8" fill="#2ecc71"/>
            <text x="300" y="130" text-anchor="middle" font-size="12">通用人工智能</text>
            <text x="300" y="180" text-anchor="middle" font-size="10">深度整合</text>
            
            <!-- 连线 -->
            <line x1="100" y1="150" x2="200" y2="150" stroke="#333" stroke-width="1" stroke-dasharray="5,5"/>
            <line x1="200" y1="150" x2="300" y2="150" stroke="#333" stroke-width="1" stroke-dasharray="5,5"/>
            
            <!-- 上方图标 -->
            <text x="100" y="100" text-anchor="middle" font-size="16">📝 🖼️ 🔊</text>
            <text x="200" y="100" text-anchor="middle" font-size="16">📝+🖼️+🔊</text>
            <text x="300" y="70" text-anchor="middle" font-size="16">🧠</text>
            <text x="300" y="100" text-anchor="middle" font-size="10">类人感知系统</text>
          </svg>
          <p class="diagram-desc">多模态AI是通往通用人工智能的重要一步</p>
        </div>
      </div>
    </section>

    <AiFooter />
  </div>
</template>
<script setup lang="ts">
import AiFooter from '../components/AiFooter.vue';
</script>

<style scoped>
.multimodal-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  color: #333;
  line-height: 1.6;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

h1 {
  font-size: 2.8rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.subtitle {
  font-size: 1.4rem;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

.content-section {
  display: flex;
  margin-bottom: 4rem;
  gap: 2rem;
  align-items: center;
}

@media (max-width: 768px) {
  .content-section {
    flex-direction: column;
  }
}

.text-block, .illustration {
  flex: 1;
}

h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

ul {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

li {
  margin-bottom: 0.5rem;
}

blockquote {
  border-left: 4px solid #3498db;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #555;
}

.feature-list {
  list-style: none;
  padding: 0;
}

.feature-list li {
  display: flex;
  margin-bottom: 1rem;
  align-items: center;
}

.icon {
  font-size: 1.8rem;
  margin-right: 1rem;
  min-width: 40px;
}

.feature-desc {
  flex: 1;
}

/* 图表样式 */
.modal-diagram, .fusion-diagram, .brain-diagram, .future-diagram {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.diagram-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
}

.input-box, .output-box, .model-box {
  padding: 0.8rem;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.model-box {
  background: #3498db;
  color: white;
  padding: 1rem;
}

.model-box.complex {
  padding: 1.2rem;
  background: linear-gradient(135deg, #3498db, #9b59b6);
}

.arrow {
  margin: 0 0.5rem;
  color: #666;
  font-weight: bold;
}

.input-group, .output-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.diagram-desc {
  text-align: center;
  font-size: 0.9rem;
  color: #666;
  margin-top: 1rem;
}

.unimodal, .multimodal {
  margin-bottom: 2rem;
}

/* 流程步骤 */
.process-steps {
  margin-top: 2rem;
}

.step {
  display: flex;
  margin-bottom: 1.5rem;
  align-items: flex-start;
}

.step-number {
  background: #3498db;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  margin-bottom: 0.5rem;
  font-size: 1.3rem;
}

/* 融合方式图表 */
.fusion-methods {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 1rem;
}

.fusion-method {
  flex: 1;
  min-width: 200px;
}

.method-diagram {
  background: white;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.fusion-box {
  background: #e74c3c;
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  text-align: center;
}

.small-box, .small-model {
  padding: 0.5rem;
  border-radius: 4px;
  background: white;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  text-align: center;
  margin-bottom: 0.5rem;
}

.small-model {
  background: #3498db;
  color: white;
}

.process-line {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

/* 应用案例网格 */
.applications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.app-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.2s;
}

.app-card:hover {
  transform: translateY(-5px);
}

.app-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.app-card h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.app-card p {
  font-size: 0.9rem;
  color: #666;
}

.application-example {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.application-example:last-child {
  border-bottom: none;
}

.application-example h3 {
  color: #3498db;
  margin-bottom: 0.5rem;
}

/* 挑战与未来 */
.challenges, .future {
  margin-bottom: 2rem;
}

.challenges h3, .future h3 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.future h3 {
  color: #2ecc71;
}

.footer {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
  color: #666;
}
</style>
