<template>
  <div class="learn-ai-home-page">
    <div class="banner-section">
      <h1>AI基础知识学习中心</h1>
      <p class="subtitle">探索人工智能的基础概念、技术和应用</p>
    </div>

    <div class="content-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="welcome-card">
            <h2>欢迎来到AI学习平台</h2>
            <p>本平台提供丰富的AI学习资源，从基础概念到高级技术，帮助您系统地了解人工智能领域的知识。</p>
            <p>选择下方的学习模块，开始您的AI学习之旅！</p>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="module-cards">
        <el-col :span="24" class="section-header">
          <h2 class="section-title">AI基础知识</h2>
          <el-button 
            type="primary" 
            :text="showAll ? '收起' : '展示全部'"
            @click="toggleShowAll"
          >
            {{ showAll ? '收起' : '展示全部' }}
            <el-icon class="el-icon--right">
              <component :is="showAll ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </el-button>
        </el-col>
        <el-col 
          :xs="24" 
          :sm="12" 
          :md="8" 
          :lg="6" 
          v-for="(module, index) in displayModules" 
          :key="module.path" 
          class="module-col"
        >
          <el-card shadow="hover" class="module-card" @click="navigateTo(module.path)">
            <div class="card-content">
              <el-icon class="module-icon"><component :is="module.icon" /></el-icon>
              <h3>{{ module.title }}</h3>
              <p>{{ module.description }}</p>
            </div>
            <div class="card-footer">
              <el-button type="primary" text>开始学习 <el-icon><ArrowRight /></el-icon></el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="resource-section">
        <el-col :span="24">
          <h2 class="section-title">推荐学习资源</h2>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12">
          <ResourceCard
            title="AI学习文档"
            description="精选公司内外AI基础知识和经验总结，从零开始掌握AI核心概念。"
            :resources="beginnerResources"
            :total="beginnerTotal"
            :current-page="beginnerCurrentPage"
            :page-size="pageSize"
            @refresh="fetchResources"
            @page-change="handleBeginnerPageChange"
          />
        </el-col>
        <el-col :xs="24" :sm="24" :md="12">
          <ResourceCard
            title="AI学习课程"
            description="汇集公司内部AI高质量课程，深入了解AI应用和优秀实践。"
            :resources="advancedResources"
            :total="advancedTotal"
            :current-page="advancedCurrentPage"
            :page-size="pageSize"
            @refresh="fetchResources"
            @page-change="handleAdvancedPageChange"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import ResourceCard from './components/ResourceCard.vue';
import { getAIWikiList, getUserInfo } from '../../aiKnowledgeManager/request';
import { ElMessage } from 'element-plus';
import {
  ArrowRight
} from '@element-plus/icons-vue';

const router = useRouter();
const showAll = ref(false);
const windowWidth = ref(window.innerWidth);

// 资源数据
const beginnerResources = ref<Array<{ 
  icon: string; 
  id: number; 
  title: string; 
  description: string; 
  wikiUrl: string; 
  utime: number;
  star: number;
  top: number;
  type: number;
}>>([]);
const advancedResources = ref<Array<{ 
  icon: string; 
  id: number; 
  title: string; 
  description: string; 
  wikiUrl: string; 
  utime: number;
  star: number;
  top: number;
  type: number;
}>>([]);
const beginnerCurrentPage = ref(1);
const advancedCurrentPage = ref(1);
const pageSize = ref(10);
const beginnerTotal = ref(0);
const advancedTotal = ref(0);

// 获取资源数据
const fetchResources = async () => {
  try {
    // 获取入门指南
    const beginnerResponse = await getAIWikiList({
      pageNo: beginnerCurrentPage.value,
      pageSize: pageSize.value,
      wikiType: 3,
      wikiParam: undefined
    });
    if (beginnerResponse.code === 0 && beginnerResponse.data) {
      beginnerResources.value = beginnerResponse.data.records.map(item => ({
        icon: 'Document',
        id: item.id,
        title: item.title,
        description: item.description,
        utime: item.utime || 0,
        wikiUrl: item.wikiUrl,
        star: item.star || 0,
        top: item.top || 0,
        type: 3
      }));
      beginnerTotal.value = beginnerResponse.data.total || 0;
    }

    // 获取进阶资料
    const advancedResponse = await getAIWikiList({
      pageNo: advancedCurrentPage.value,
      pageSize: pageSize.value,
      wikiType: 4,
      wikiParam: undefined
    });
    if (advancedResponse.code === 0 && advancedResponse.data) {
      advancedResources.value = advancedResponse.data.records.map(item => ({
        icon: 'Document',
        id: item.id,
        title: item.title,
        description: item.description,
        utime: item.utime || 0,
        wikiUrl: item.wikiUrl,
        star: item.star || 0,
        top: item.top || 0,
        type: 4
      }));
      advancedTotal.value = advancedResponse.data.total || 0;
    }
  } catch (error) {
    ElMessage.error('获取资源数据失败');
    console.error('获取资源数据失败:', error);
  }
};

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  fetchResources();
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 计算每行显示的卡片数量
const cardsPerRow = computed(() => {
  const lg = 24 / 6; // lg: 24/6 = 4
  const md = 24 / 8; // md: 24/8 = 3
  const sm = 24 / 12; // sm: 24/12 = 2
  const xs = 24 / 24; // xs: 24/24 = 1

  if (windowWidth.value >= 1200) return lg;
  if (windowWidth.value >= 992) return md;
  if (windowWidth.value >= 768) return sm;
  return xs;
});

// 学习模块数据
const learningModules = ref([
  {
    title: 'AI大模型',
    description: '人工智能大模型是当代AI领域最引人注目的技术进步之一，它们改变了我们与技术交互的方式，并为许多行业带来革命性变化',
    icon: 'Reading',
    path: '/learnAI/aiLargeModel'
  },
  {
    title: '深度学习',
    description: '了解深度学习的三种主要学习方式：监督学习、无监督学习和强化学习',
    icon: 'Reading',
    path: '/learnAI/deepLearning'
  },
  {
    title: 'SFT和COT',
    description: '探索监督微调(SFT)和思维链(COT)在AI训练中的应用',
    icon: 'Reading',
    path: '/learnAI/sftAndcot'
  },
  {
    title: 'RAG',
    description: '了解检索增强生成(RAG)技术如何提高大语言模型的准确性和相关性',
    icon: 'Reading',
    path: '/learnAI/rag'
  },
  {
    title: 'MCP',
    description: '探索多智能体协作平台(MCP)如何实现复杂任务的协作解决',
    icon: 'Reading',
    path: '/learnAI/mcp'
  },
  {
    title: '多模态',
    description: '了解AI如何像人类一样，通过多种感官理解世界',
    icon: 'Reading',
    path: '/learnAI/multimodel'
  },
  {
    title: 'Transformer',
    description: 'Transformer是现代大型语言模型（如GPT、BERT等）的核心架构',
    icon: 'Reading',
    path: '/learnAI/transformer'
  },
  {
    title: '知识蒸馏',
    description: '了解知识蒸馏如何将大模型的"智慧"转移到轻量级模型中，提高AI部署效率',
    icon: 'Reading',
    path: '/learnAI/knowledgeDistillation'
  },
  {
    title: 'Tokens',
    description: '理解AI大模型中tokens的概念和作用，探索其如何影响模型处理文本的方式',
    icon: 'Reading',
    path: '/learnAI/tokens'
  },
  {
    title: '大模型训练过程',
    description: '了解大模型从预训练到微调的完整训练流程',
    icon: 'Reading', 
    path: '/learnAI/trainingProcess'
  }
]);

// 计算要显示的模块
const displayModules = computed(() => {
  return showAll.value ? learningModules.value : learningModules.value.slice(0, cardsPerRow.value);
});

// 切换显示全部/收起
const toggleShowAll = () => {
  showAll.value = !showAll.value;
};

// 导航到指定路径
const navigateTo = (path: string) => {
  router.push(path);
};

// 添加分页变化处理方法
const handleBeginnerPageChange = (page: number) => {
  beginnerCurrentPage.value = page;
  fetchResources();
};

const handleAdvancedPageChange = (page: number) => {
  advancedCurrentPage.value = page;
  fetchResources();
};
</script>

<style lang="scss" scoped>
.learn-ai-home-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .banner-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border-radius: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    
    h1 {
      font-size: 32px;
      color: #303133;
      margin: 0 0 15px 0;
      background: linear-gradient(to right, #409EFF, #36cfc9);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .subtitle {
      font-size: 16px;
      color: #606266;
      margin: 0;
    }
  }
  
  .content-section {
    .welcome-card {
      padding: 20px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      margin-bottom: 20px;
      
      h2 {
        font-size: 20px;
        color: #303133;
        margin-top: 0;
        margin-bottom: 16px;
      }
      
      p {
        color: #606266;
        line-height: 1.6;
        margin-bottom: 10px;
      }
    }
    
    .module-cards { 
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        margin-bottom: 20px;
        
        .section-title {
          margin: 0;
          font-size: 20px;
          color: #303133;
        }
      }
      
      .module-col {
        margin-bottom: 20px;
      }
      
      .module-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        transition: transform 0.3s;
        cursor: pointer;
        
        &:hover {
          transform: translateY(-5px);
        }
        
        .card-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          
          .module-icon {
            font-size: 36px;
            color: #409EFF;
            margin-bottom: 15px;
          }
          
          h3 {
            font-size: 18px;
            margin: 0 0 10px 0;
            color: #303133;
          }
          
          p {
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
            flex: 1;
          }
        }
        
        .card-footer {
          margin-top: 15px;
          text-align: right;
        }
      }
    }
    
    .section-title {
      font-size: 22px;
      color: #303133;
      // margin: 30px 0 20px;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #36cfc9);
        border-radius: 2px;
      }
    }
    
    .resource-section {
      .el-col {
        margin-bottom: 5px;
      }
    }
  }
}

@media (max-width: 768px) {
  .learn-ai-home-page {
    .banner-section {
      padding: 30px 15px;
      
      h1 {
        font-size: 24px;
      }
    }
    
    .resource-section {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style> 