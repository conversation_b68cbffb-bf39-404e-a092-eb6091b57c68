<template>
  <div class="learn-ai-rag-page">
    <div class="rag-container">
      <header class="rag-header">
        <h1>RAG：让AI更聪明的秘密武器</h1>
        <p class="subtitle">检索增强生成（Retrieval Augmented Generation）</p>
      </header>

      <section class="rag-section">
        <h2>什么是RAG？</h2>
        <div class="content-with-image">
          <div class="text-content">
            <p>RAG就像是给AI装上了一个<strong>超级图书馆卡</strong>。</p>
            <p>它不再只依赖自己"脑中"的知识，而是可以<strong>实时查阅外部资料</strong>，找到最相关的信息，然后生成更准确的回答。</p>
          </div>
          <div class="image-container">
            <svg class="illustration" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
              <!-- 大脑和书本的简单插图 -->
              <circle cx="100" cy="100" r="50" fill="none" stroke="#333" stroke-width="2" />
              <path d="M75 80 C85 70, 115 70, 125 80" fill="none" stroke="#333" stroke-width="2" />
              <path d="M75 120 C85 130, 115 130, 125 120" fill="none" stroke="#333" stroke-width="2" />
              <rect x="180" y="70" width="60" height="80" fill="none" stroke="#333" stroke-width="2" />
              <path d="M180 70 C210 50, 210 50, 240 70" fill="none" stroke="#333" stroke-width="2" />
              <line x1="150" y1="100" x2="180" y2="100" stroke="#333" stroke-width="2" stroke-dasharray="5,5" />
            </svg>
          </div>
        </div>
      </section>

      <section class="rag-section">
        <h2>为什么我们需要RAG？</h2>
        <div class="content-with-image">
          <div class="text-content">
            <p>大型语言模型（LLM）有三个主要问题：</p>
            <ul>
              <li><strong>知识有限</strong>：它们的知识在某个时间点被冻结</li>
              <li><strong>幻觉问题</strong>：当不知道答案时，可能会编造信息</li>
              <li><strong>缺乏专业知识</strong>：对特定领域知识的掌握不足</li>
            </ul>
            <p>RAG通过让AI实时查询最新、最相关的信息来解决这些问题。</p>
          </div>
          <div class="image-container">
            <svg class="illustration" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
              <!-- 问题与解决方案的简单插图 -->
              <circle cx="80" cy="70" r="30" fill="none" stroke="#333" stroke-width="2" />
              <text x="80" y="75" text-anchor="middle" font-size="24" fill="#333">?</text>
              <circle cx="80" cy="150" r="30" fill="none" stroke="#333" stroke-width="2" />
              <text x="80" y="155" text-anchor="middle" font-size="24" fill="#333">!</text>
              <rect x="170" y="50" width="80" height="40" rx="5" fill="none" stroke="#333" stroke-width="2" />
              <text x="210" y="75" text-anchor="middle" font-size="12" fill="#333">最新信息</text>
              <rect x="170" y="130" width="80" height="40" rx="5" fill="none" stroke="#333" stroke-width="2" />
              <text x="210" y="155" text-anchor="middle" font-size="12" fill="#333">专业知识</text>
              <line x1="110" y1="70" x2="170" y2="70" stroke="#333" stroke-width="2" />
              <line x1="110" y1="150" x2="170" y2="150" stroke="#333" stroke-width="2" />
            </svg>
          </div>
        </div>
      </section>

      <section class="rag-section">
        <h2>RAG如何工作？</h2>
        <div class="rag-workflow">
          <div class="workflow-step">
            <div class="step-number">1</div>
            <h3>用户提问</h3>
            <svg class="step-illustration" viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
              <rect x="10" y="20" width="100" height="40" rx="20" fill="none" stroke="#333" stroke-width="2" />
              <text x="60" y="45" text-anchor="middle" font-size="12" fill="#333">问题</text>
            </svg>
            <p>用户向AI提出一个问题</p>
          </div>
          <div class="workflow-step">
            <div class="step-number">2</div>
            <h3>检索知识</h3>
            <svg class="step-illustration" viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
              <rect x="10" y="10" width="30" height="40" fill="none" stroke="#333" stroke-width="2" />
              <rect x="45" y="10" width="30" height="40" fill="none" stroke="#333" stroke-width="2" />
              <rect x="80" y="10" width="30" height="40" fill="none" stroke="#333" stroke-width="2" />
              <line x1="60" y1="60" x2="60" y2="70" stroke="#333" stroke-width="2" />
              <circle cx="60" cy="75" r="5" fill="#333" />
            </svg>
            <p>系统搜索相关知识库</p>
          </div>
          <div class="workflow-step">
            <div class="step-number">3</div>
            <h3>增强提示</h3>
            <svg class="step-illustration" viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
              <rect x="10" y="10" width="100" height="40" rx="5" fill="none" stroke="#333" stroke-width="2" />
              <line x1="20" y1="25" x2="100" y2="25" stroke="#333" stroke-width="1" />
              <line x1="20" y1="35" x2="80" y2="35" stroke="#333" stroke-width="1" />
              <rect x="25" y="50" width="70" height="20" rx="3" fill="none" stroke="#333" stroke-width="2" stroke-dasharray="3,3" />
            </svg>
            <p>将检索到的信息加入提示</p>
          </div>
          <div class="workflow-step">
            <div class="step-number">4</div>
            <h3>生成回答</h3>
            <svg class="step-illustration" viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
              <rect x="10" y="20" width="100" height="40" rx="5" fill="none" stroke="#333" stroke-width="2" />
              <line x1="20" y1="35" x2="100" y2="35" stroke="#333" stroke-width="1" />
              <line x1="20" y1="45" x2="80" y2="45" stroke="#333" stroke-width="1" />
              <line x1="20" y1="55" x2="90" y2="55" stroke="#333" stroke-width="1" />
            </svg>
            <p>AI生成基于事实的回答</p>
          </div>
        </div>
      </section>

      <section class="rag-section">
        <h2>RAG的实际应用</h2>
        <div class="applications">
          <div class="application-card">
            <svg class="application-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="50" r="40" fill="none" stroke="#333" stroke-width="2" />
              <rect x="30" y="35" width="40" height="30" fill="none" stroke="#333" stroke-width="2" />
              <line x1="35" y1="45" x2="65" y2="45" stroke="#333" stroke-width="1" />
              <line x1="35" y1="55" x2="55" y2="55" stroke="#333" stroke-width="1" />
            </svg>
            <h3>客户服务</h3>
            <p>可以访问企业最新政策、产品信息和FAQ，提供准确回答</p>
          </div>
          <div class="application-card">
            <svg class="application-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <path d="M30 70 L30 30 L70 30 L70 70 Z" fill="none" stroke="#333" stroke-width="2" />
              <line x1="30" y1="40" x2="70" y2="40" stroke="#333" stroke-width="1" />
              <line x1="40" y1="30" x2="40" y2="70" stroke="#333" stroke-width="1" />
              <circle cx="50" cy="55" r="10" fill="none" stroke="#333" stroke-width="2" />
            </svg>
            <h3>医疗咨询</h3>
            <p>结合最新医学研究和病例，为医生提供决策支持</p>
          </div>
          <div class="application-card">
            <svg class="application-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <rect x="20" y="30" width="60" height="40" fill="none" stroke="#333" stroke-width="2" />
              <path d="M20 30 L50 10 L80 30" fill="none" stroke="#333" stroke-width="2" />
              <rect x="40" y="50" width="20" height="20" fill="none" stroke="#333" stroke-width="2" />
            </svg>
            <h3>教育辅助</h3>
            <p>提供个性化学习体验，回答学生问题并引用权威资料</p>
          </div>
          <div class="application-card">
            <svg class="application-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="40" r="20" fill="none" stroke="#333" stroke-width="2" />
              <path d="M30 75 Q50 60, 70 75" fill="none" stroke="#333" stroke-width="2" />
              <circle cx="43" cy="35" r="3" fill="#333" />
              <circle cx="57" cy="35" r="3" fill="#333" />
              <path d="M40 45 Q50 50, 60 45" fill="none" stroke="#333" stroke-width="2" />
            </svg>
            <h3>个人助手</h3>
            <p>查询实时信息，如天气、新闻或产品评价，提供更有用的建议</p>
          </div>
        </div>
      </section>

      <section class="rag-section">
        <h2>传统LLM与RAG的对比</h2>
        <div class="comparison">
          <div class="comparison-column">
            <h3>传统LLM</h3>
            <svg class="comparison-illustration" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
              <circle cx="75" cy="75" r="50" fill="none" stroke="#333" stroke-width="2" />
              <path d="M50 60 C60 50, 90 50, 100 60" fill="none" stroke="#333" stroke-width="2" />
              <path d="M50 90 C60 100, 90 100, 100 90" fill="none" stroke="#333" stroke-width="2" />
              <circle cx="60" cy="70" r="5" fill="#333" />
              <circle cx="90" cy="70" r="5" fill="#333" />
              <text x="75" y="130" text-anchor="middle" font-size="12" fill="#333">固定知识</text>
            </svg>
            <ul class="comparison-list">
              <li>依赖训练时获得的知识</li>
              <li>知识可能过时</li>
              <li>无法访问专业领域信息</li>
              <li>容易产生"幻觉"</li>
            </ul>
          </div>
          <div class="comparison-column">
            <h3>RAG增强型LLM</h3>
            <svg class="comparison-illustration" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
              <circle cx="75" cy="75" r="50" fill="none" stroke="#333" stroke-width="2" />
              <path d="M50 60 C60 50, 90 50, 100 60" fill="none" stroke="#333" stroke-width="2" />
              <path d="M50 90 C60 100, 90 100, 100 90" fill="none" stroke="#333" stroke-width="2" />
              <circle cx="60" cy="70" r="5" fill="#333" />
              <circle cx="90" cy="70" r="5" fill="#333" />
              <rect x="125" y="50" width="25" height="50" fill="none" stroke="#333" stroke-width="2" />
              <line x1="100" y1="75" x2="125" y2="75" stroke="#333" stroke-width="2" stroke-dasharray="5,3" />
              <text x="75" y="130" text-anchor="middle" font-size="12" fill="#333">动态知识获取</text>
            </svg>
            <ul class="comparison-list">
              <li>可实时访问最新信息</li>
              <li>能查询专业知识库</li>
              <li>回答有明确来源依据</li>
              <li>大幅减少"幻觉"问题</li>
            </ul>
          </div>
        </div>
      </section>

      <section class="rag-section">
        <h2>搭建RAG系统</h2>
        <div class="content-with-image">
          <div class="text-content">
            <p>构建一个RAG系统需要以下组件：</p>
            <ol>
              <li><strong>知识库</strong>：存储文档、数据和信息</li>
              <li><strong>向量数据库</strong>：高效存储和检索嵌入向量</li>
              <li><strong>嵌入模型</strong>：将文本转换为向量表示</li>
              <li><strong>检索引擎</strong>：根据相似度查找相关信息</li>
              <li><strong>大型语言模型</strong>：生成最终回答</li>
            </ol>
          </div>
          <div class="image-container">
            <svg class="illustration" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
              <!-- RAG系统架构图 -->
              <rect x="20" y="20" width="80" height="40" rx="5" fill="none" stroke="#333" stroke-width="2" />
              <text x="60" y="45" text-anchor="middle" font-size="12" fill="#333">知识库</text>
              
              <rect x="20" y="80" width="80" height="40" rx="5" fill="none" stroke="#333" stroke-width="2" />
              <text x="60" y="105" text-anchor="middle" font-size="12" fill="#333">嵌入模型</text>
              
              <rect x="20" y="140" width="80" height="40" rx="5" fill="none" stroke="#333" stroke-width="2" />
              <text x="60" y="165" text-anchor="middle" font-size="12" fill="#333">检索引擎</text>
              
              <rect x="130" y="80" width="80" height="40" rx="5" fill="none" stroke="#333" stroke-width="2" />
              <text x="170" y="105" text-anchor="middle" font-size="10" fill="#333">向量数据库</text>
              
              <rect x="240" y="80" width="40" height="40" rx="20" fill="none" stroke="#333" stroke-width="2" />
              <text x="260" y="105" text-anchor="middle" font-size="12" fill="#333">LLM</text>
              
              <line x1="100" y1="40" x2="130" y2="80" stroke="#333" stroke-width="1" stroke-dasharray="5,3" />
              <line x1="100" y1="100" x2="130" y2="100" stroke="#333" stroke-width="1" />
              <line x1="210" y1="100" x2="240" y2="100" stroke="#333" stroke-width="1" />
              <line x1="100" y1="160" x2="170" y2="120" stroke="#333" stroke-width="1" stroke-dasharray="5,3" />
            </svg>
          </div>
        </div>
      </section>

      <AiFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
import AiFooter from '../components/AiFooter.vue';
// RAG页面组件逻辑
</script>



<style lang="scss" scoped>
.learn-ai-rag-page {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  color: #333;
  line-height: 1.6;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.rag-container {
  max-width: 1200px;
  margin: 0 auto;
}

.rag-header {
  text-align: center;
  margin-bottom: 60px;
  
  h1 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
  }

  .subtitle {
    font-size: 20px;
    font-weight: 400;
    color: #666;
  }
}

.rag-section {
  margin-bottom: 80px;

  h2 {
    font-size: 30px;
    margin-bottom: 30px;
    font-weight: 600;
    position: relative;
    padding-bottom: 10px;
    
    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background: #333;
    }
  }

  p, li {
    font-size: 18px;
    margin-bottom: 15px;
  }

  ul, ol {
    padding-left: 20px;
    margin-bottom: 20px;
  }
}

.content-with-image {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 40px;
  
  .text-content {
    flex: 1;
    min-width: 300px;
  }
  
  .image-container {
    flex: 1;
    min-width: 300px;
    display: flex;
    justify-content: center;
  }
}

.illustration {
  width: 100%;
  max-width: 400px;
  height: auto;
}

.rag-workflow {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
  margin-top: 40px;
  
  .workflow-step {
    flex: 1;
    min-width: 180px;
    text-align: center;
    position: relative;
    
    .step-number {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: #333;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 15px;
      font-weight: bold;
    }
    
    h3 {
      font-size: 18px;
      margin-bottom: 10px;
    }
    
    .step-illustration {
      width: 120px;
      height: 80px;
      margin: 0 auto 15px;
    }
    
    p {
      font-size: 16px;
    }
  }
}

.applications {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
  
  .application-card {
    flex: 1;
    min-width: 180px;
    max-width: 250px;
    padding: 20px;
    border-radius: 8px;
    background: #f8f8f8;
    text-align: center;
    
    .application-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 15px;
    }
    
    h3 {
      font-size: 18px;
      margin-bottom: 10px;
    }
    
    p {
      font-size: 16px;
    }
  }
}

.comparison {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  justify-content: center;
  margin-top: 30px;
  
  .comparison-column {
    flex: 1;
    min-width: 250px;
    text-align: center;
    
    h3 {
      font-size: 20px;
      margin-bottom: 20px;
    }
    
    .comparison-illustration {
      width: 150px;
      height: 150px;
      margin: 0 auto 20px;
    }
    
    .comparison-list {
      text-align: left;
      list-style-type: disc;
      padding-left: 20px;
      
      li {
        margin-bottom: 10px;
        font-size: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .rag-header h1 {
    font-size: 28px;
  }
  
  .rag-header .subtitle {
    font-size: 18px;
  }
  
  .rag-section h2 {
    font-size: 24px;
  }
  
  .content-with-image {
    flex-direction: column;
  }
  
  .comparison {
    flex-direction: column;
  }
  
  .rag-workflow {
    flex-direction: column;
    align-items: center;
  }
  
  .workflow-step {
    max-width: 300px;
  }
}
</style>