<template>
  <el-card class="resource-card">
    <div class="card-header">
      <div class="header-left">
        <h3>{{ title }}</h3>
      </div>
      <el-button 
        type="primary" 
        :icon="Plus"
        circle
        @click="showCreateDialog"
        v-if="userIsAdmin"
      />
    </div>
    <p>{{ description }}</p>
    <ul class="resource-list">
      <li v-for="(item, index) in resources" :key="index" class="resource-item">
        <div class="resource-content" :style="{ maxWidth: `calc(100% - ${100 + (item.top === 1 ? 40 : 0) + (userIsAdmin ? 70 : 0)}px)` }">
          <el-icon><component :is="item.star === 1 ? 'StarFilled' : item.icon" /></el-icon>
          <el-tooltip
            :content="item.title"
            placement="top"
            :show-after="500"
          >
            <span class="resource-title"  @click="openWiki(item.wikiUrl)">{{ item.title }}</span>
          </el-tooltip>
          <el-tooltip
            v-if="item.description"
            :content="item.description"
            placement="top"
            :show-after="500"
          >
            <span class="resource-description">{{ item.description }}</span>
          </el-tooltip>
        </div>
        <div class="resource-actions">
          <el-tag v-if="item.top === 1" type="primary" effect="plain" class="top-tag">
            置顶
          </el-tag>
          <span class="resource-time">{{ formatTime(item.utime) }}</span>
          <el-dropdown trigger="click" @command="handleCommand" v-if="userIsAdmin">
            <el-button type="primary" text>管理</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{ type: 'star', item }">
                  <el-icon><Star /></el-icon>{{ item.star === 1 ? '取消星标' : '星标' }}
                </el-dropdown-item>
                <el-dropdown-item :command="{ type: 'top', item }">
                  <el-icon><Top /></el-icon>{{ item.top === 1 ? '取消置顶' : '置顶' }}
                </el-dropdown-item>
                <el-dropdown-item :command="{ type: 'edit', item }">
                  <el-icon><EditPen /></el-icon>编辑
                </el-dropdown-item>
                <el-dropdown-item :command="{ type: 'delete', item }" class="danger-item">
                  <el-icon><Delete /></el-icon>删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </li>
    </ul>

    <!-- 创建资源弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingItem ? '编辑学习资源' : '创建学习资源'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型">
            <el-option label="AI学习文档" :value="3" />
            <el-option label="AI学习课程" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="Wiki链接" prop="wikiUrl">
          <el-input v-model="form.wikiUrl" placeholder="请输入Wiki链接" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="submitLoading">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading" :disabled="submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="400px"
    >
      <p>确定要删除这个学习资源吗？此操作不可恢复。</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { Plus, EditPen, Delete, Star, Top, StarFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { addAIWiki, updateAIWiki, deleteAIWiki, getUserInfo } from '../../../aiKnowledgeManager/request';
import dayjs from 'dayjs';
import { getMenuTree, MenuItem } from '../../../../shared/services/menuService';

interface ResourceItem {
  icon: string;
  id: number;
  title: string;
  description?: string;
  wikiUrl: string;
  utime: number;
  star: number;
  top: number;
  type: number;
}

interface Props {
  title: string;
  description: string;
  resources: ResourceItem[];
  total: number;
  currentPage: number;
  pageSize: number;
}

const props = defineProps<Props>();

// 判断是否是管理员
const userIsAdmin = ref(false);

// 弹窗相关
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const formRef = ref<FormInstance>();
const editingItem = ref<ResourceItem | null>(null);
const deletingItem = ref<ResourceItem | null>(null);
const submitLoading = ref(false);

// 表单数据
const form = reactive({
  title: '',
  description: '',
  type: 3,
  wikiUrl: ''
});

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' },
    { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  wikiUrl: [
    { required: true, message: '请输入Wiki链接', trigger: 'blur' }
  ]
};

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp * 1000).format('YYYY-MM-DD');
};

// 打开Wiki链接
const openWiki = (url: string) => {
  window.open(url, '_blank');
};

// 显示创建弹窗
const showCreateDialog = () => {
  editingItem.value = null;
  dialogVisible.value = true;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 初始化表单数据
  Object.assign(form, {
    title: '',
    description: '',
    type: props.title === 'AI学习文档' ? 3 : 4,
    wikiUrl: ''
  });
};

// 显示编辑弹窗
const showEditDialog = (item: ResourceItem) => {
  editingItem.value = item;
  dialogVisible.value = true;
  // 填充表单
  form.title = item.title;
  form.description = item.description || '';
  form.type = props.title === 'AI学习文档' ? 3 : 4;
  form.wikiUrl = item.wikiUrl;
};

// 处理下拉菜单命令
const handleCommand = (command: { type: string; item: ResourceItem }) => {
  if (command.type === 'edit') {
    showEditDialog(command.item);
  } else if (command.type === 'delete') {
    deletingItem.value = command.item;
    deleteDialogVisible.value = true;
  } else if (command.type === 'star' || command.type === 'top') {
    handleUpdate(command.item, command.type);
  }
};

// 处理更新操作
const handleUpdate = async (item: ResourceItem, type: 'star' | 'top') => {
  try {
    const res = await updateAIWiki({
      id: item.id,
      title: item.title,
      description: item.description || '',
      type: item.type,
      wikiUrl: item.wikiUrl,
      star: type === 'star' ? (item.star === 1 ? 0 : 1) : item.star,
      top: type === 'top' ? (item.top === 1 ? 0 : 1) : item.top
    });
    
    if (res.code === 0) {
      ElMessage.success(type === 'star' ? (item.star === 1 ? '取消星标成功' : '设为星标成功') : (item.top === 1 ? '取消置顶成功' : '设为置顶成功'));
      // 触发父组件刷新
      emit('refresh');
    } else {
      ElMessage.error(res.message || '操作失败');
    }
  } catch (error) {
    console.error('更新失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// 确认删除
const confirmDelete = async () => {
  if (!deletingItem.value) return;
  
  try {
    const res = await deleteAIWiki(deletingItem.value.id);
    if (res.code === 0) {
      ElMessage.success('删除成功');
      deleteDialogVisible.value = false;
      // 触发父组件刷新
      emit('refresh');
    } else {
      ElMessage.error(res.message || '删除失败');
    }
  } catch (error) {
    console.error('删除资源失败:', error);
    ElMessage.error('删除失败，请稍后重试');
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true;
      try {
        let res;
        if (editingItem.value) {
          // 编辑模式
          res = await updateAIWiki({
            id: editingItem.value.id,
            title: form.title,
            description: form.description,
            type: form.type,
            wikiUrl: form.wikiUrl
          });
        } else {
          // 创建模式
          res = await addAIWiki({
            title: form.title,
            description: form.description,
            type: form.type,
            wikiUrl: form.wikiUrl,
            pbName: '',
            department: '',
            tags: []
          });
        }
        
        if (res.code === 0) {
          ElMessage.success(editingItem.value ? '更新成功' : '创建成功');
          dialogVisible.value = false;
          // 触发父组件刷新
          emit('refresh');
        } else {
          ElMessage.error(res.message || (editingItem.value ? '更新失败' : '创建失败'));
        }
      } catch (error) {
        console.error('操作失败:', error);
        ElMessage.error('操作失败，请稍后重试');
      } finally {
        submitLoading.value = false;
      }
    }
  });
};

// 用户是否拥有管理权限
const getUserIsAdmin = async () => {  
  const userInfo = ref<{name?: string, login?: string}>({});
  try {
    const res = await getUserInfo();
    if (res && res.data) {
      console.log(res.data);
      userInfo.value.name = res.data.name || '';
      userInfo.value.login = res.data.login || '';
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    ElMessage.error('获取用户信息失败');
  }

  try {
    const menuData = await getMenuTree();
    console.log(menuData);
    const menuList = menuData.menuFolderList || [];
    const learnAIMenu = menuList.find(item => item.nodeCode === 'learnAI');
    userIsAdmin.value = learnAIMenu?.attributeList?.administerList?.includes(userInfo.value.login) || false;
  } catch (err) {
    console.error('获取菜单失败:', err);
    ElMessage.error('获取菜单失败，请重试');
  }
};

// 定义事件
const emit = defineEmits<{
  (e: 'refresh'): void;
  (e: 'page-change', page: number): void;
}>();

// 处理页码变化
const handlePageChange = (page: number) => {
  emit('page-change', page);
};

onMounted(() => {
  getUserIsAdmin();
});
</script>

<style lang="scss" scoped>
.resource-card {
  height: 100%;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .header-left {
      // display: flex;
      // align-items: center;
      gap: 20px;
      flex: 1;
      justify-content: center;
      
      h3 {
        font-size: 18px;
        color: #303133;
        margin: 0;
      }
      
      .pagination {
        margin: 0;
        
        :deep(.el-pagination) {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          
          .el-pagination__total {
            display: none;
          }
          
          .el-pagination__sizes {
            display: none;
          }
          
          .el-pagination__jump {
            display: none;
          }
          
          .el-pagination__prev,
          .el-pagination__next {
            background: none;
            border: none;
            padding: 0;
            font-size: 14px;
            color: #606266;
            
            &:hover {
              color: #409EFF;
            }
            
            &.is-disabled {
              color: #c0c4cc;
              cursor: not-allowed;
            }
          }
          
          .el-pager {
            display: flex;
            align-items: center;
            gap: 8px;
            
            li {
              background: none;
              border: none;
              padding: 0;
              font-size: 14px;
              color: #606266;
              
              &:hover {
                color: #409EFF;
              }
              
              &.active {
                color: #409EFF;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
  
  p {
    font-size: 14px;
    color: #606266;
    margin-bottom: 15px;
  }
  
  .resource-list {
    list-style: none;
    padding: 0;
    margin: 0;
    
    .resource-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #ebeef5;
      line-height: 1.5;
      position: relative;
      
      &:last-child {
        border-bottom: none;
      }
      
      .resource-content {
        display: flex;
        align-items: center;
        // width: v-bind('userIsAdmin ? "calc(100% - 180px)" : "calc(100% - 100px)"');

        
        padding-right: 16px;
        overflow: hidden;
        
        @media (max-width: 768px) {
          width: v-bind('userIsAdmin ? "calc(100% - 150px)" : "calc(100% - 80px)"');
        }
        
        .el-icon {
          margin-right: 12px;
          color: #409EFF;
          font-size: 16px;
          flex-shrink: 0;
        }
        
        .resource-title {
          font-size: 15px;
          color: #303133;
          transition: color 0.3s;
          font-weight: 500;
          margin-right: 20px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: calc(100% - 100px);
          cursor: pointer;
          
          @media (max-width: 768px) {
            margin-right: 10px;
            max-width: 100px;
          }

          
            
          
        }

        .resource-title:hover {
          color: #409EFF;
        }
        
        .resource-description {
          font-size: 13px;
          color: #606266;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 1.4;
          min-width: 50px;
          
          @media (max-width: 768px) {
            max-width: 100px;
          }
        }
      }
      
      .resource-actions {
        display: flex;
        align-items: center;
        gap: 16px;
        flex-shrink: 0;
        justify-content: flex-end;
        position: absolute;
        right: 0;
        
        @media (max-width: 768px) {
          gap: 8px;
        }
        
        .top-tag {
          display: flex;
          align-items: center;
          gap: 4px;
          .el-icon {
            font-size: 14px;
          }
        }
        
        .resource-time {
          font-size: 13px;
          color: #909399;
          text-align: right;
          flex-shrink: 0;
          
          @media (max-width: 768px) {
            font-size: 12px;
          }
        }

        :deep(.el-dropdown) {
          min-width: 60px;
          text-align: right;

          .el-dropdown-menu {
            padding: 4px 0;
            
            .el-dropdown-menu__item {
              display: flex;
              align-items: center;
              padding: 8px 16px;
              
              .el-icon {
                margin-right: 8px;
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }
}

:deep(.danger-item) {
  color: var(--el-color-danger);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 