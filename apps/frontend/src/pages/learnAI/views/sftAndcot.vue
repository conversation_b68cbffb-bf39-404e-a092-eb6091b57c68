<template>
  <div class="container">
    <header class="header">
      <h1 class="title">AI大模型中的SFT与COT</h1>
      <p class="subtitle">一个通俗易懂的解释</p>
    </header>
    
    <section class="section">
      <h2 class="section-title">SFT：监督微调</h2>
      <div class="content-block">
        <div class="text-block">
          <p>监督微调（Supervised Fine-Tuning，SFT）是AI大模型训练过程中的重要环节，它就像是给一位博学的老师进行专业培训。</p>
          <p>想象一下：</p>
          <div class="illustration">
            <div class="illustration-text">
              <p>👨‍🏫 基座模型：已经阅读了互联网上海量的文章和书籍的AI</p>
              <p>👩‍🎓 SFT：让AI学习专门针对特定任务的表达方式和回答技巧</p>
            </div>
          </div>
        </div>
        <div class="image-block">
          <div class="image-placeholder">
            <!-- 这里可以放SFT的示意图 -->
            <div class="sketch">
              <div class="sketch-item">
                <div class="sketch-circle big-circle">基座模型</div>
                <div class="arrow">→</div>
                <div class="sketch-circle">SFT模型</div>
              </div>
              <div class="sketch-note">通过高质量的问答数据进行训练</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="content-block">
        <h3 class="sub-title">SFT是如何工作的？</h3>
        <div class="steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>准备好的AI</h4>
              <p>首先，我们有一个已经预训练好的基础模型（比如GPT-3），它已经学习了大量通用知识。</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>收集特定数据</h4>
              <p>然后，我们收集高质量的特定领域的问答数据，比如医疗诊断、法律咨询或编程帮助。</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>进行监督训练</h4>
              <p>使用这些数据对模型进行"微调"，让它学会如何按照我们期望的方式回答问题。</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4>评估与优化</h4>
              <p>不断测试模型的表现，调整训练方法，直到达到我们满意的效果。</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="content-block">
        <h3 class="sub-title">为什么SFT很重要？</h3>
        <div class="benefits">
          <div class="benefit">
            <div class="benefit-icon">🎯</div>
            <div class="benefit-text">
              <h4>提升特定任务表现</h4>
              <p>让模型在特定领域（如医疗、法律）表现更出色</p>
            </div>
          </div>
          <div class="benefit">
            <div class="benefit-icon">🔄</div>
            <div class="benefit-text">
              <h4>灵活适应不同需求</h4>
              <p>同一个基础模型可以针对不同任务进行不同的微调</p>
            </div>
          </div>
          <div class="benefit">
            <div class="benefit-icon">💰</div>
            <div class="benefit-text">
              <h4>节省资源</h4>
              <p>比重新训练一个模型省时省力，成本更低</p>
            </div>
          </div>
          <div class="benefit">
            <div class="benefit-icon">🛡️</div>
            <div class="benefit-text">
              <h4>安全与对齐</h4>
              <p>可以引导模型遵循人类价值观和伦理规范</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section class="section">
      <h2 class="section-title">COT：思维链</h2>
      <div class="content-block">
        <div class="text-block">
          <p>思维链（Chain of Thought，COT）是一种提示技术，让AI像人类一样一步一步地思考问题，而不是直接给出答案。</p>
          <p>简单来说：</p>
          <div class="illustration">
            <div class="illustration-text">
              <p>🤔 没有COT：问题→直接答案</p>
              <p>✨ 使用COT：问题→思考步骤1→思考步骤2→...→最终答案</p>
            </div>
          </div>
        </div>
        <div class="image-block">
          <div class="image-placeholder">
            <!-- 这里可以放COT的示意图 -->
            <div class="sketch">
              <div class="sketch-item">
                <div class="sketch-box">问题</div>
                <div class="arrow">→</div>
                <div class="sketch-box">思考过程</div>
                <div class="arrow">→</div>
                <div class="sketch-box">答案</div>
              </div>
              <div class="sketch-note">让AI显示出思考过程</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="content-block">
        <h3 class="sub-title">COT如何帮助AI思考？</h3>
        <div class="example">
          <div class="example-header">
            <h4>看一个例子：</h4>
          </div>
          <div class="example-content">
            <div class="example-problem">
              <p><strong>问题：</strong> 小明有5个苹果，小红给了他3个苹果，然后他吃了2个苹果并送给小华4个苹果。现在小明还剩几个苹果？</p>
            </div>
            <div class="example-solution">
              <div class="solution-without-cot">
                <h5>没有使用COT：</h5>
                <p>小明还剩2个苹果。</p>
              </div>
              <div class="solution-with-cot">
                <h5>使用COT：</h5>
                <ol>
                  <li>小明一开始有5个苹果</li>
                  <li>小红给了他3个苹果，所以现在小明有5+3=8个苹果</li>
                  <li>小明吃了2个苹果，所以还剩8-2=6个苹果</li>
                  <li>小明送给小华4个苹果，所以最后还剩6-4=2个苹果</li>
                  <li>因此，小明最后还剩2个苹果</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="content-block">
        <h3 class="sub-title">COT的不同应用方式</h3>
        <div class="applications">
          <div class="application">
            <h4>Few-shot COT（少样本思维链）</h4>
            <p>通过提供几个带有思考步骤的例子，引导AI学会如何展示思考过程。</p>
          </div>
          <div class="application">
            <h4>Zero-shot COT（零样本思维链）</h4>
            <p>简单地告诉AI"让我们一步一步思考"，无需提供示例也能激发思维链。</p>
          </div>
          <div class="application">
            <h4>Self-consistency COT（自洽思维链）</h4>
            <p>让AI生成多条思维链，然后选择最一致的答案，类似于"多角度思考"。</p>
          </div>
        </div>
      </div>
      
      <div class="content-block">
        <h3 class="sub-title">为什么COT如此强大？</h3>
        <div class="benefits">
          <div class="benefit">
            <div class="benefit-icon">🧩</div>
            <div class="benefit-text">
              <h4>解决复杂问题</h4>
              <p>通过分解步骤，AI能够处理需要多步推理的复杂问题</p>
            </div>
          </div>
          <div class="benefit">
            <div class="benefit-icon">🔍</div>
            <div class="benefit-text">
              <h4>增强可解释性</h4>
              <p>让用户理解AI是如何得出结论的，增加透明度和信任</p>
            </div>
          </div>
          <div class="benefit">
            <div class="benefit-icon">📊</div>
            <div class="benefit-text">
              <h4>提高准确性</h4>
              <p>逐步推理减少了错误累积，提高了复杂任务的准确性</p>
            </div>
          </div>
          <div class="benefit">
            <div class="benefit-icon">🧠</div>
            <div class="benefit-text">
              <h4>更像人类思考</h4>
              <p>模拟人类的认知过程，产生更自然、更有洞察力的答案</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section class="section">
      <h2 class="section-title">SFT与COT如何结合使用？</h2>
      <div class="content-block">
        <p>当SFT和COT结合时，我们得到了一个既专注于特定领域又能展示清晰思考过程的AI。</p>
        <div class="combination">
          <div class="combination-item">
            <h3>SFT训练思维链能力</h3>
            <p>我们可以通过SFT训练模型生成优质的思维链，让AI学会如何清晰地表达推理过程。</p>
          </div>
          <div class="combination-item">
            <h3>领域专家 + 思路清晰</h3>
            <p>专业领域的SFT模型配合COT技术，即拥有专业知识又能展示清晰的思考步骤。</p>
          </div>
          <div class="combination-item">
            <h3>增强复杂任务能力</h3>
            <p>特别是在数学、编程、逻辑推理等领域，SFT+COT的组合尤为强大。</p>
          </div>
        </div>
      </div>
    </section>
    
    <section class="section">
      <h2 class="section-title">总结</h2>
      <div class="content-block summary">
        <div class="summary-item">
          <h3>SFT（监督微调）</h3>
          <p>让AI通过高质量数据学习特定领域的知识和回答方式，就像为通才提供专业培训。</p>
        </div>
        <div class="summary-item">
          <h3>COT（思维链）</h3>
          <p>引导AI展示思考过程，一步一步地分析和解决问题，而不是直接给出答案。</p>
        </div>
        <div class="summary-item">
          <h3>两者结合的力量</h3>
          <p>当专业知识（SFT）遇上清晰的思考方法（COT），AI能够提供既专业又透明的解决方案。</p>
        </div>
      </div>
    </section>
    
    <AiFooter />
  </div>
</template>

<script setup lang="ts">
import AiFooter from '../components/AiFooter.vue';
// 组件逻辑
</script>

<style lang="scss" scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  color: #333;
  line-height: 1.6;
}

.header {
  text-align: center;
  margin-bottom: 60px;
  
  .title {
    font-size: 3rem;
    margin-bottom: 10px;
    font-weight: 700;
  }
  
  .subtitle {
    font-size: 1.5rem;
    color: #666;
    font-weight: 400;
  }
}

.section {
  margin-bottom: 80px;
  
  .section-title {
    font-size: 2.5rem;
    margin-bottom: 30px;
    font-weight: 600;
    border-bottom: 3px solid #f0f0f0;
    padding-bottom: 10px;
  }
  
  .sub-title {
    font-size: 1.8rem;
    margin: 30px 0 20px;
    font-weight: 500;
  }
}

.content-block {
  margin-bottom: 40px;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  
  .text-block {
    flex: 1;
    min-width: 300px;
    
    p {
      margin-bottom: 15px;
      font-size: 1.1rem;
    }
  }
  
  .image-block {
    flex: 1;
    min-width: 300px;
    
    .image-placeholder {
      background-color: #f9f9f9;
      border-radius: 8px;
      height: 250px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.illustration {
  background-color: #f7f7f7;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  
  .illustration-text {
    p {
      margin-bottom: 10px;
      font-size: 1.1rem;
    }
  }
}

.steps {
  .step {
    display: flex;
    margin-bottom: 25px;
    
    .step-number {
      background-color: #4A90E2;
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 20px;
      flex-shrink: 0;
    }
    
    .step-content {
      h4 {
        font-size: 1.3rem;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      p {
        color: #555;
      }
    }
  }
}

.benefits {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 25px;
  
  .benefit {
    display: flex;
    align-items: flex-start;
    
    .benefit-icon {
      font-size: 2rem;
      margin-right: 15px;
      flex-shrink: 0;
    }
    
    .benefit-text {
      h4 {
        font-size: 1.2rem;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      p {
        color: #555;
        font-size: 0.95rem;
      }
    }
  }
}

.example {
  background-color: #f7f7f7;
  border-radius: 8px;
  padding: 25px;
  
  .example-header {
    margin-bottom: 15px;
    
    h4 {
      font-size: 1.3rem;
      font-weight: 500;
    }
  }
  
  .example-problem {
    margin-bottom: 20px;
    padding: 15px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }
  
  .example-solution {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    
    .solution-without-cot, .solution-with-cot {
      flex: 1;
      min-width: 250px;
      padding: 15px;
      background-color: white;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      
      h5 {
        font-size: 1.1rem;
        margin-bottom: 10px;
        color: #4A90E2;
        font-weight: 500;
      }
      
      ol {
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
        }
      }
    }
  }
}

.applications {
  .application {
    margin-bottom: 20px;
    
    h4 {
      font-size: 1.2rem;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    p {
      color: #555;
    }
  }
}

.combination {
  background-color: #f0f7ff;
  border-radius: 8px;
  padding: 25px;
  margin-top: 20px;
  
  .combination-item {
    margin-bottom: 20px;
    
    h3 {
      font-size: 1.3rem;
      margin-bottom: 8px;
      font-weight: 500;
      color: #2c6ecb;
    }
    
    p {
      color: #444;
    }
  }
}

.summary {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 30px;
  
  .summary-item {
    margin-bottom: 25px;
    
    h3 {
      font-size: 1.5rem;
      margin-bottom: 10px;
      font-weight: 500;
    }
    
    p {
      color: #444;
      font-size: 1.1rem;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 手绘风格的图形
.sketch {
  text-align: center;
  
  .sketch-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
  }
  
  .sketch-circle {
    width: 100px;
    height: 100px;
    border: 2px solid #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    text-align: center;
    font-weight: 500;
    background-color: white;
    
    &.big-circle {
      width: 120px;
      height: 120px;
    }
  }
  
  .sketch-box {
    min-width: 80px;
    padding: 15px;
    border: 2px solid #333;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-weight: 500;
    background-color: white;
  }
  
  .arrow {
    margin: 0 15px;
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  .sketch-note {
    color: #666;
    font-style: italic;
    margin-top: 10px;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .header {
    .title {
      font-size: 2.5rem;
    }
    
    .subtitle {
      font-size: 1.2rem;
    }
  }
  
  .section {
    .section-title {
      font-size: 2rem;
    }
    
    .sub-title {
      font-size: 1.5rem;
    }
  }
  
  .example {
    .example-solution {
      flex-direction: column;
    }
  }
}
</style>