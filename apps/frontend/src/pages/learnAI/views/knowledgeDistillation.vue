<template>
    <div class="learn-ai-knowledge-distillation-page">
      <div class="container">
        <div class="title-section">
          <h1 class="page-title">知识蒸馏：让AI模型更轻盈、更高效</h1>
          <p class="subtitle">通俗易懂的解释与图解</p>
        </div>
  
        <div class="section intro-section">
          <p>知识蒸馏是一种模型压缩技术，让小模型学习大模型的"智慧"，在保持性能的同时大幅降低计算资源需求。这一技术对于将大型AI模型部署到资源受限的设备上至关重要。</p>
        </div>
  
        <!-- 主要区块 -->
        <div class="content-blocks">
          <!-- 知识蒸馏概览 -->
          <div class="content-block">
            <div class="block-header">
              <h2>什么是知识蒸馏？</h2>
              <div class="image-placeholder overview-image">
                <!-- 知识蒸馏总体概念的手绘风格SVG图像 -->
                <svg width="100%" height="100%" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                  <!-- 背景 -->
                  <rect width="800" height="400" fill="#f0f2f5" rx="10" ry="10" />
                  
                  <!-- 大模型（教师模型）-->
                  <g transform="translate(150, 80)">
                    <rect width="180" height="240" rx="10" ry="10" fill="none" stroke="#333" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="1, 0" />
                    <text x="90" y="-20" font-family="Arial" font-size="24" text-anchor="middle" fill="#333">教师模型</text>
                    
                    <!-- 教师模型内部的神经网络图示 -->
                    <g transform="translate(30, 30)">
                      <!-- 第一层神经元 -->
                      <circle cx="0" cy="0" r="10" fill="#409EFF" />
                      <circle cx="0" cy="45" r="10" fill="#409EFF" />
                      <circle cx="0" cy="90" r="10" fill="#409EFF" />
                      <circle cx="0" cy="135" r="10" fill="#409EFF" />
                      <circle cx="0" cy="180" r="10" fill="#409EFF" />
                      
                      <!-- 第二层神经元 -->
                      <circle cx="60" cy="22.5" r="10" fill="#409EFF" />
                      <circle cx="60" cy="67.5" r="10" fill="#409EFF" />
                      <circle cx="60" cy="112.5" r="10" fill="#409EFF" />
                      <circle cx="60" cy="157.5" r="10" fill="#409EFF" />
                      
                      <!-- 第三层神经元 -->
                      <circle cx="120" cy="45" r="10" fill="#409EFF" />
                      <circle cx="120" cy="90" r="10" fill="#409EFF" />
                      <circle cx="120" cy="135" r="10" fill="#409EFF" />
                      
                      <!-- 连接线 - 使用手绘风格的曲线 -->
                      <!-- 第一层到第二层的连接 -->
                      <path d="M10,0 C25,5 40,15 50,22.5" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M10,0 C30,10 40,50 50,67.5" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M10,0 C30,40 40,90 50,112.5" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M10,0 C20,80 35,130 50,157.5" fill="none" stroke="#666" stroke-width="1.5" />
                      
                      <path d="M10,45 C25,35 40,25 50,22.5" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M10,45 C25,50 40,60 50,67.5" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M10,45 C25,70 40,95 50,112.5" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M10,45 C20,90 35,140 50,157.5" fill="none" stroke="#666" stroke-width="1.5" />
                      
                      <!-- 更多连接线省略，保持图形简洁 -->
                      
                      <!-- 第二层到第三层的连接 -->
                      <path d="M70,22.5 C85,25 100,35 110,45" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M70,22.5 C85,45 100,70 110,90" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M70,22.5 C80,70 95,120 110,135" fill="none" stroke="#666" stroke-width="1.5" />
                      
                      <path d="M70,67.5 C85,60 105,50 110,45" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M70,67.5 C85,75 105,85 110,90" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M70,67.5 C85,95 100,120 110,135" fill="none" stroke="#666" stroke-width="1.5" />
                    </g>
                  </g>
                  
                  <!-- 知识蒸馏过程（箭头和软标签） -->
                  <g transform="translate(350, 200)">
                    <path d="M0,0 C40,-20 60,-20 100,0" fill="none" stroke="#333" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                    <polygon points="100,0 90,-8 90,8" fill="#333" />
                    
                    <!-- 软标签气泡 -->
                    <ellipse cx="50" cy="-40" rx="40" ry="25" fill="#ffecb3" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-dasharray="1, 0" />
                    <text x="50" y="-45" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">软标签</text>
                    <text x="50" y="-28" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">[0.8, 0.1, 0.05...]</text>
                    
                    <!-- 蒸馏文字 -->
                    <text x="50" y="25" font-family="Arial" font-size="20" text-anchor="middle" fill="#666">知识蒸馏</text>
                  </g>
                  
                  <!-- 小模型（学生模型）-->
                  <g transform="translate(470, 120)">
                    <rect width="120" height="160" rx="10" ry="10" fill="none" stroke="#333" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="1, 0" />
                    <text x="60" y="-20" font-family="Arial" font-size="24" text-anchor="middle" fill="#333">学生模型</text>
                    
                    <!-- 学生模型内部的小型神经网络图示 -->
                    <g transform="translate(20, 20)">
                      <!-- 第一层神经元 -->
                      <circle cx="0" cy="0" r="8" fill="#36cfc9" />
                      <circle cx="0" cy="60" r="8" fill="#36cfc9" />
                      <circle cx="0" cy="120" r="8" fill="#36cfc9" />
                      
                      <!-- 第二层神经元 -->
                      <circle cx="40" cy="30" r="8" fill="#36cfc9" />
                      <circle cx="40" cy="90" r="8" fill="#36cfc9" />
                      
                      <!-- 第三层神经元 -->
                      <circle cx="80" cy="60" r="8" fill="#36cfc9" />
                      
                      <!-- 连接线 - 使用手绘风格的曲线 -->
                      <path d="M8,0 C20,5 30,22 32,30" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M8,0 C15,30 30,75 32,90" fill="none" stroke="#666" stroke-width="1.5" />
                      
                      <path d="M8,60 C15,50 30,38 32,30" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M8,60 C15,70 30,82 32,90" fill="none" stroke="#666" stroke-width="1.5" />
                      
                      <path d="M8,120 C15,100 25,100 32,90" fill="none" stroke="#666" stroke-width="1.5" />
                      
                      <path d="M48,30 C60,40 70,50 72,60" fill="none" stroke="#666" stroke-width="1.5" />
                      <path d="M48,90 C55,85 65,70 72,60" fill="none" stroke="#666" stroke-width="1.5" />
                    </g>
                  </g>
                  
                  <!-- 移动设备图标 - 手机和智能手表 -->
                  <g transform="translate(650, 170)">
                    <!-- 手机 -->
                    <rect x="0" y="0" width="40" height="80" rx="5" ry="5" fill="none" stroke="#333" stroke-width="2" />
                    <rect x="5" y="10" width="30" height="50" rx="2" ry="2" fill="#eefaff" stroke="#333" stroke-width="1" />
                    <circle cx="20" cy="70" r="5" fill="none" stroke="#333" stroke-width="1" />
                    
                    <!-- 智能手表 -->
                    <rect x="65" y="20" width="30" height="35" rx="5" ry="5" fill="none" stroke="#333" stroke-width="2" />
                    <rect x="70" y="25" width="20" height="25" rx="2" ry="2" fill="#eefaff" stroke="#333" stroke-width="1" />
                    <path d="M65,30 C60,35 60,45 65,50" fill="none" stroke="#333" stroke-width="2" />
                    <path d="M95,30 C100,35 100,45 95,50" fill="none" stroke="#333" stroke-width="2" />
                    
                    <!-- 连接线 -->
                    <path d="M-20,40 C-10,40 -5,20 0,40" fill="none" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M-20,60 C-10,60 -5,80 0,60" fill="none" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </g>
                  
                  <!-- 标题和注解 -->
                  <text x="400" y="370" font-family="Arial" font-size="18" text-anchor="middle" font-style="italic" fill="#666">轻量级模型，相同能力，更少资源</text>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>简单来说</strong>：知识蒸馏就像是"师徒传艺"——一个庞大但经验丰富的教师模型(Teacher Model)将其学到的知识传授给一个小巧的学生模型(Student Model)。</p>
              <p>想象一位资深厨师(大模型)教导一名年轻学徒(小模型)烹饪一道复杂菜肴。厨师不会让学徒从头开始探索所有可能的烹饪方法，而是直接教授经过多年实践证明最有效的技巧和诀窍。</p>
              <div class="highlight-box">
                <p><strong>为什么知识蒸馏如此重要？</strong></p>
                <ul>
                  <li>它能将<strong>大型复杂模型</strong>的能力转移到<strong>轻量级模型</strong>中</li>
                  <li>使AI技术能在<strong>手机、IoT设备</strong>等计算资源有限的环境中运行</li>
                  <li>大幅<strong>降低推理成本</strong>和<strong>能耗</strong></li>
                  <li>加快模型<strong>响应速度</strong>，提升用户体验</li>
                </ul>
              </div>
            </div>
          </div>
  
          <!-- 知识蒸馏的工作原理 -->
          <div class="content-block">
            <div class="block-header">
              <h2>知识蒸馏如何工作？</h2>
              <div class="image-placeholder mechanism-image">
                <!-- 知识蒸馏工作原理的手绘风格SVG图像 -->
                <svg width="100%" height="100%" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                  <!-- 背景 -->
                  <rect width="800" height="400" fill="#f0f2f5" rx="10" ry="10" />
                  
                  <!-- 步骤1：训练教师模型 -->
                  <g transform="translate(100, 80)">
                    <rect width="180" height="100" rx="10" ry="10" fill="#ffecb3" stroke="#333" stroke-width="2" stroke-linecap="round" />
                    <text x="90" y="40" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">步骤1</text>
                    <text x="90" y="70" font-family="Arial" font-size="14" text-anchor="middle" fill="#666">训练教师模型</text>
                    
                    <!-- 数据流入图示 -->
                    <g transform="translate(-50, 50)">
                      <path d="M0,0 C20,-10 30,-10 50,0" fill="none" stroke="#333" stroke-width="2" stroke-linecap="round" />
                      <polygon points="50,0 42,-5 42,5" fill="#333" />
                      <text x="25" y="-15" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">原始数据</text>
                    </g>
                    
                    <!-- 模型图示 -->
                    <g transform="translate(90, 130)">
                      <circle cx="0" cy="0" r="30" fill="#409EFF" stroke="#333" stroke-width="2" />
                      <text x="0" y="5" font-family="Arial" font-size="12" text-anchor="middle" fill="white">大模型</text>
                    </g>
                    
                    <!-- 箭头 -->
                    <path d="M90,200 C90,210 90,220 90,230" fill="none" stroke="#333" stroke-width="2" stroke-linecap="round" />
                    <polygon points="90,230 85,222 95,222" fill="#333" />
                  </g>
                  
                  <!-- 步骤2：提取软标签 -->
                  <g transform="translate(320, 80)">
                    <rect width="180" height="100" rx="10" ry="10" fill="#e6f7ff" stroke="#333" stroke-width="2" stroke-linecap="round" />
                    <text x="90" y="40" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">步骤2</text>
                    <text x="90" y="70" font-family="Arial" font-size="14" text-anchor="middle" fill="#666">提取软标签</text>
                    
                    <!-- 数据流入图示 -->
                    <g transform="translate(-50, 50)">
                      <path d="M0,0 C20,-10 30,-10 50,0" fill="none" stroke="#333" stroke-width="2" stroke-linecap="round" />
                      <polygon points="50,0 42,-5 42,5" fill="#333" />
                      <text x="25" y="-15" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">测试数据</text>
                    </g>
                    
                    <!-- 软标签输出 -->
                    <g transform="translate(90, 130)">
                      <rect width="120" height="60" rx="5" ry="5" fill="white" stroke="#333" stroke-width="2" />
                      <text x="60" y="20" font-family="Arial" font-size="12" text-anchor="middle" fill="#333">软标签集合</text>
                      <text x="60" y="40" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">[0.8, 0.1, 0.05...]</text>
                      <text x="60" y="55" font-family="Arial" font-size="10" text-anchor="middle" fill="#666">[0.7, 0.2, 0.06...]</text>
                    </g>
                    
                    <!-- 箭头 -->
                    <path d="M90,200 C90,210 90,220 90,230" fill="none" stroke="#333" stroke-width="2" stroke-linecap="round" />
                    <polygon points="90,230 85,222 95,222" fill="#333" />
                  </g>
                  
                  <!-- 步骤3：训练学生模型 -->
                  <g transform="translate(540, 80)">
                    <rect width="180" height="100" rx="10" ry="10" fill="#f6ffed" stroke="#333" stroke-width="2" stroke-linecap="round" />
                    <text x="90" y="40" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">步骤3</text>
                    <text x="90" y="70" font-family="Arial" font-size="14" text-anchor="middle" fill="#666">训练学生模型</text>
                    
                    <!-- 学生模型图示 -->
                    <g transform="translate(90, 160)">
                      <circle cx="0" cy="0" r="20" fill="#36cfc9" stroke="#333" stroke-width="2" />
                      <text x="0" y="5" font-family="Arial" font-size="12" text-anchor="middle" fill="white">小模型</text>
                    </g>
                    
                    <!-- 损失函数 -->
                    <g transform="translate(90, 130)">
                      <path d="M-60,0 C-40,-15 -20,-20 0,0 C20,20 40,25 60,0" fill="none" stroke="#ff4d4f" stroke-width="2" stroke-linecap="round" />
                      <text x="0" y="-10" font-family="Arial" font-size="12" text-anchor="middle" fill="#ff4d4f">蒸馏损失</text>
                    </g>
                  </g>
                  
                  <!-- 连接箭头 -->
                  <g>
                    <!-- 步骤1到步骤2 -->
                    <path d="M280,150 C290,150 300,150 310,150" fill="none" stroke="#333" stroke-width="2" stroke-linecap="round" />
                    <polygon points="310,150 302,145 302,155" fill="#333" />
                    
                    <!-- 步骤2到步骤3 -->
                    <path d="M500,150 C510,150 520,150 530,150" fill="none" stroke="#333" stroke-width="2" stroke-linecap="round" />
                    <polygon points="530,150 522,145 522,155" fill="#333" />
                  </g>
                  
                  <!-- 底部结果说明 -->
                  <g transform="translate(400, 350)">
                    <rect width="600" height="30" rx="5" ry="5" fill="#f0f9ff" x="-300" stroke="#333" stroke-width="1" stroke-dasharray="5,5" />
                    <text x="0" y="20" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">学生模型在训练过程中学习大模型的"思考方式"，而不仅仅是最终答案</text>
                  </g>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>如同学习高手的思考过程</strong>：知识蒸馏不仅关注最终答案，更关注模型思考问题的方式。</p>
              <p>设想一个学生不仅要知道2+3=5，还要理解为什么是5，以及老师是如何得出这个答案的。</p>
              <div class="step-box">
                <h3>知识蒸馏的基本步骤：</h3>
                <div class="steps">
                  <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h4>训练教师模型</h4>
                      <p>首先训练一个大型、高性能的教师模型，让它学习解决特定任务</p>
                    </div>
                  </div>
                  <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h4>提取软标签</h4>
                      <p>教师模型不仅提供正确答案，还提供每个可能答案的概率分布（这就是"软标签"）</p>
                    </div>
                  </div>
                  <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h4>训练学生模型</h4>
                      <p>设计一个更小的学生模型，让它学习模仿教师模型的输出概率分布</p>
                    </div>
                  </div>
                </div>
              </div>
              <p>通过这个过程，学生模型不仅学到了"是什么"，还学到了"为什么"和"有多确定"——这些都是蕴含在软标签中的宝贵信息。</p>
            </div>
          </div>
  
          <!-- 软标签的魔力 -->
          <div class="content-block">
            <div class="block-header">
              <h2>软标签：知识蒸馏的核心</h2>
              <div class="image-placeholder soft-labels-image">
                <!-- 软标签概念的手绘风格SVG图像 -->
                <svg width="100%" height="100%" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                  <!-- 背景 -->
                  <rect width="800" height="400" fill="#f0f2f5" rx="10" ry="10" />
                  
                  <!-- 标题 -->
                  <text x="400" y="40" font-family="Arial" font-size="20" text-anchor="middle" fill="#333">软标签 vs 硬标签</text>
                  
                  <!-- 硬标签部分 -->
                  <g transform="translate(150, 100)">
                    <text x="0" y="-30" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">传统硬标签</text>
                    
                    <!-- 图片示例 -->
                    <g transform="translate(-80, 0)">
                      <rect width="160" height="160" rx="5" ry="5" fill="#fff" stroke="#333" stroke-width="2" />
                      
                      <!-- 简单的猫形象 -->
                      <g transform="translate(80, 80)">
                        <!-- 猫脸 -->
                        <circle cx="0" cy="0" r="40" fill="#f9f0e6" stroke="#333" stroke-width="1.5" />
                        
                        <!-- 猫耳朵 -->
                        <path d="M-25,-25 L-40,-50 L-10,-35 Z" fill="#f9f0e6" stroke="#333" stroke-width="1.5" />
                        <path d="M25,-25 L40,-50 L10,-35 Z" fill="#f9f0e6" stroke="#333" stroke-width="1.5" />
                        
                        <!-- 猫眼睛 -->
                        <ellipse cx="-15" cy="-10" rx="8" ry="12" fill="#fff" stroke="#333" stroke-width="1" />
                        <ellipse cx="15" cy="-10" rx="8" ry="12" fill="#fff" stroke="#333" stroke-width="1" />
                        <ellipse cx="-15" cy="-7" rx="4" ry="6" fill="#000" />
                        <ellipse cx="15" cy="-7" rx="4" ry="6" fill="#000" />
                        
                        <!-- 猫鼻子和嘴 -->
                        <path d="M0,5 L8,12 M0,5 L-8,12" fill="none" stroke="#333" stroke-width="1" />
                        <circle cx="0" cy="5" r="4" fill="#ffb3b3" />
                      </g>
                    </g>
                    
                    <!-- 标签部分 -->
                    <g transform="translate(120, 80)">
                      <rect x="-30" y="-60" width="120" height="120" rx="5" ry="5" fill="#fff" stroke="#333" stroke-width="2" />
                      
                      <line x1="-30" y1="-25" x2="90" y2="-25" stroke="#eee" stroke-width="1" />
                      <line x1="-30" y1="10" x2="90" y2="10" stroke="#eee" stroke-width="1" />
                      <line x1="-30" y1="45" x2="90" y2="45" stroke="#eee" stroke-width="1" />
                      
                      <text x="30" y="-40" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">猫</text>
                      <rect x="10" y="-45" width="40" height="10" fill="#409EFF" opacity="0.2" rx="2" ry="2" />
                      
                      <text x="30" y="-5" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">豹</text>
                      <text x="30" y="30" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">狐狸</text>
                      <text x="30" y="65" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">狗</text>
                      
                      <!-- 硬标签值 -->
                      <rect x="60" y="-55" width="20" height="20" rx="3" ry="3" fill="#409EFF" />
                      <text x="70" y="-40" font-family="Arial" font-size="14" text-anchor="middle" fill="white">1</text>
                      
                      <rect x="60" y="-20" width="20" height="20" rx="3" ry="3" fill="#f5f5f5" stroke="#ddd" />
                      <text x="70" y="-5" font-family="Arial" font-size="14" text-anchor="middle" fill="#999">0</text>
                      
                      <rect x="60" y="15" width="20" height="20" rx="3" ry="3" fill="#f5f5f5" stroke="#ddd" />
                      <text x="70" y="30" font-family="Arial" font-size="14" text-anchor="middle" fill="#999">0</text>
                      
                      <rect x="60" y="50" width="20" height="20" rx="3" ry="3" fill="#f5f5f5" stroke="#ddd" />
                      <text x="70" y="65" font-family="Arial" font-size="14" text-anchor="middle" fill="#999">0</text>
                    </g>
                  </g>
                  
                  <!-- 软标签部分 -->
                  <g transform="translate(550, 100)">
                    <text x="0" y="-30" font-family="Arial" font-size="18" text-anchor="middle" fill="#333">知识蒸馏软标签</text>
                    
                    <!-- 图片示例 - 与硬标签部分相同 -->
                    <g transform="translate(-80, 0)">
                      <rect width="160" height="160" rx="5" ry="5" fill="#fff" stroke="#333" stroke-width="2" />
                      
                      <!-- 简单的猫形象 -->
                      <g transform="translate(80, 80)">
                        <!-- 猫脸 -->
                        <circle cx="0" cy="0" r="40" fill="#f9f0e6" stroke="#333" stroke-width="1.5" />
                        
                        <!-- 猫耳朵 -->
                        <path d="M-25,-25 L-40,-50 L-10,-35 Z" fill="#f9f0e6" stroke="#333" stroke-width="1.5" />
                        <path d="M25,-25 L40,-50 L10,-35 Z" fill="#f9f0e6" stroke="#333" stroke-width="1.5" />
                        
                        <!-- 猫眼睛 -->
                        <ellipse cx="-15" cy="-10" rx="8" ry="12" fill="#fff" stroke="#333" stroke-width="1" />
                        <ellipse cx="15" cy="-10" rx="8" ry="12" fill="#fff" stroke="#333" stroke-width="1" />
                        <ellipse cx="-15" cy="-7" rx="4" ry="6" fill="#000" />
                        <ellipse cx="15" cy="-7" rx="4" ry="6" fill="#000" />
                        
                        <!-- 猫鼻子和嘴 -->
                        <path d="M0,5 L8,12 M0,5 L-8,12" fill="none" stroke="#333" stroke-width="1" />
                        <circle cx="0" cy="5" r="4" fill="#ffb3b3" />
                      </g>
                    </g>
                    
                    <!-- 软标签部分 -->
                    <g transform="translate(120, 80)">
                      <rect x="-30" y="-60" width="120" height="120" rx="5" ry="5" fill="#fff" stroke="#333" stroke-width="2" />
                      
                      <line x1="-30" y1="-25" x2="90" y2="-25" stroke="#eee" stroke-width="1" />
                      <line x1="-30" y1="10" x2="90" y2="10" stroke="#eee" stroke-width="1" />
                      <line x1="-30" y1="45" x2="90" y2="45" stroke="#eee" stroke-width="1" />
                      
                      <text x="30" y="-40" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">猫</text>
                      <rect x="10" y="-45" width="40" height="10" fill="#409EFF" opacity="0.2" rx="2" ry="2" />
                      
                      <text x="30" y="-5" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">豹</text>
                      <text x="30" y="30" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">狐狸</text>
                      <text x="30" y="65" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">狗</text>
                      
                      <!-- 软标签值 - 使用渐变长度条 -->
                      <rect x="60" y="-55" width="20" height="20" rx="3" ry="3" fill="#f0f0f0" stroke="#ddd" />
                      <rect x="60" y="-55" width="16" height="20" rx="3" ry="3" fill="#409EFF" />
                      <text x="70" y="-40" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">0.8</text>
                      
                      <rect x="60" y="-20" width="20" height="20" rx="3" ry="3" fill="#f0f0f0" stroke="#ddd" />
                      <rect x="60" y="-20" width="2" height="20" rx="3" ry="3" fill="#409EFF" />
                      <text x="70" y="-5" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">0.1</text>
                      
                      <rect x="60" y="15" width="20" height="20" rx="3" ry="3" fill="#f0f0f0" stroke="#ddd" />
                      <rect x="60" y="15" width="1" height="20" rx="3" ry="3" fill="#409EFF" />
                      <text x="70" y="30" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">0.05</text>
                      
                      <rect x="60" y="50" width="20" height="20" rx="3" ry="3" fill="#f0f0f0" stroke="#ddd" />
                      <rect x="60" y="50" width="0.6" height="20" rx="3" ry="3" fill="#409EFF" />
                      <text x="70" y="65" font-family="Arial" font-size="10" text-anchor="middle" fill="#333">0.03</text>
                    </g>
                  </g>
                  
                  <!-- 连接部分 -->
                  <g transform="translate(400, 170)">
                    <path d="M-80,0 C-30,0 30,0 80,0" fill="none" stroke="#333" stroke-width="2" stroke-dasharray="5,5" />
                    <text x="0" y="-15" font-family="Arial" font-size="14" text-anchor="middle" fill="#666">知识蒸馏</text>
                    <text x="0" y="25" font-family="Arial" font-size="14" text-anchor="middle" font-style="italic" fill="#666">更丰富的知识表示</text>
                  </g>
                  
                  <!-- 温度参数说明 -->
                  <g transform="translate(400, 330)">
                    <rect x="-250" y="-30" width="500" height="60" rx="5" ry="5" fill="white" stroke="#409EFF" stroke-width="1" stroke-dasharray="3,3" />
                    <text x="0" y="-5" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">温度参数(T)控制软标签的"软硬程度"</text>
                    <text x="-200" y="20" font-family="Arial" font-size="14" text-anchor="start" fill="#666">低温：[0.95, 0.03, 0.01, 0.01]</text>
                    <text x="100" y="20" font-family="Arial" font-size="14" text-anchor="start" fill="#666">高温：[0.4, 0.3, 0.2, 0.1]</text>
                  </g>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>如同概率分布的教学</strong>：软标签是知识蒸馏的关键创新，它包含了传统标签所没有的丰富信息。</p>
              
              <div class="example-box">
                <h3>举个例子：图像识别</h3>
                <p>假设有一张猫的图片，传统标签会简单地标记为"猫"(1)，其他类别为0。</p>
                <p>但教师模型可能给出这样的软标签：</p>
                <ul>
                  <li>猫: 0.8</li>
                  <li>豹: 0.1</li>
                  <li>狐狸: 0.05</li>
                  <li>狗: 0.03</li>
                  <li>其他: 0.02</li>
                </ul>
                <p>这告诉学生模型："这很可能是一只猫，但也有一些豹子的特征，甚至有一点点像狐狸和狗。"</p>
              </div>
              
              <p>软标签通常通过调整温度参数(Temperature)来控制分布的"软硬程度"：</p>
              <div class="highlight-box">
                <p><strong>温度参数的作用：</strong></p>
                <ul>
                  <li><strong>高温</strong>：使概率分布更加平滑，差异变小</li>
                  <li><strong>低温</strong>：使概率分布更加集中，差异更明显</li>
                </ul>
                <p>适当的温度设置可以让学生模型获取更有价值的知识。</p>
              </div>
            </div>
          </div>
  
          <!-- 蒸馏损失函数 -->
          <div class="content-block">
            <div class="block-header">
              <h2>蒸馏损失：衡量学习效果</h2>
              <div class="image-placeholder loss-image">
                <!-- 蒸馏损失的手绘风格SVG图像 -->
                <svg width="100%" height="100%" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                  <!-- 背景 -->
                  <rect width="800" height="400" fill="#f0f2f5" rx="10" ry="10" />
                  
                  <!-- 标题 -->
                  <text x="400" y="40" font-family="Arial" font-size="20" text-anchor="middle" fill="#333">蒸馏损失函数的组成</text>
                  
                  <!-- 损失函数组成示意图 -->
                  <g transform="translate(400, 160)">
                    <!-- 总损失计算公式 -->
                    <rect x="-300" y="-60" width="600" height="120" rx="10" ry="10" fill="#fff" stroke="#333" stroke-width="2" />
                    
                    <!-- 公式内容 -->
                    <text x="0" y="0" font-family="Arial" font-size="24" text-anchor="middle" fill="#333">总损失 = α * 蒸馏损失 + (1-α) * 学生损失</text>
                    
                    <!-- 组成部分说明线 -->
                    <path d="M-200,-20 L-200,-100" fill="none" stroke="#666" stroke-width="1.5" stroke-dasharray="4,2" />
                    <path d="M100,-20 L100,-100" fill="none" stroke="#666" stroke-width="1.5" stroke-dasharray="4,2" />
                  </g>
                  
                  <!-- 蒸馏损失部分 -->
                  <g transform="translate(200, 70)">
                    <rect width="200" height="70" rx="8" ry="8" fill="#e6f7ff" stroke="#409EFF" stroke-width="2" />
                    <text x="100" y="30" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">蒸馏损失</text>
                    <text x="100" y="55" font-family="Arial" font-size="14" text-anchor="middle" fill="#666">学生输出与教师软标签的差距</text>
                  </g>
                  
                  <!-- 学生损失部分 -->
                  <g transform="translate(600, 70)">
                    <rect width="200" height="70" rx="8" ry="8" fill="#f6ffed" stroke="#52c41a" stroke-width="2" />
                    <text x="100" y="30" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">学生损失</text>
                    <text x="100" y="55" font-family="Arial" font-size="14" text-anchor="middle" fill="#666">学生输出与真实标签的差距</text>
                  </g>
                  
                  <!-- 权衡因子α说明 -->
                  <g transform="translate(400, 250)">
                    <rect x="-150" y="-30" width="300" height="60" rx="5" ry="5" fill="#f9f0ff" stroke="#722ed1" stroke-width="2" />
                    <text x="0" y="0" font-family="Arial" font-size="16" text-anchor="middle" fill="#333">α是权衡参数 (0 ≤ α ≤ 1)</text>
                    <text x="0" y="25" font-family="Arial" font-size="14" text-anchor="middle" fill="#666">控制两种损失的相对重要性</text>
                  </g>
                  
                  <!-- 滑块示意 -->
                  <g transform="translate(400, 320)">
                    <line x1="-150" y1="0" x2="150" y2="0" stroke="#ddd" stroke-width="6" stroke-linecap="round" />
                    <line x1="-150" y1="0" x2="0" y2="0" stroke="#722ed1" stroke-width="6" stroke-linecap="round" />
                    
                    <circle cx="0" cy="0" r="12" fill="white" stroke="#722ed1" stroke-width="2" />
                    <text x="0" y="35" font-family="Arial" font-size="14" text-anchor="middle" fill="#666">α = 0.5 (平衡点)</text>
                    
                    <text x="-150" y="-15" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">更看重学生损失</text>
                    <text x="150" y="-15" font-family="Arial" font-size="12" text-anchor="middle" fill="#666">更看重蒸馏损失</text>
                  </g>
                  
                  <!-- 底部注释 -->
                  <text x="400" y="380" font-family="Arial" font-size="14" text-anchor="middle" font-style="italic" fill="#666">调整α以在"模仿教师"和"直接学习任务"之间找到最佳平衡点</text>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>如同评分标准</strong>：蒸馏损失函数决定了学生模型学习的方向，通常包含两部分。</p>
              
              <div class="component-box">
                <h3>蒸馏损失 (Distillation Loss)</h3>
                <p>衡量学生模型输出与教师模型软标签之间的差距，通常使用KL散度。</p>
                <p>这相当于："你的思考过程与教师有多接近？"</p>
              </div>
              
              <div class="component-box">
                <h3>学生损失 (Student Loss)</h3>
                <p>衡量学生模型与真实标签之间的差距，通常使用交叉熵损失。</p>
                <p>这相当于："你的最终答案是否正确？"</p>
              </div>
              
              <p>总损失通常是这两种损失的加权组合：</p>
              <div class="highlight-box">
                <p><strong>总损失 = α * 蒸馏损失 + (1-α) * 学生损失</strong></p>
                <p>其中α是一个权衡参数，控制我们更看重"思考过程"还是"最终答案"。</p>
              </div>
            </div>
          </div>
  
          <!-- 知识蒸馏的变体 -->
          <div class="content-block">
            <div class="block-header">
              <h2>知识蒸馏的多种形式</h2>
            </div>
            <div class="block-content">
              <p>随着研究发展，知识蒸馏已经衍生出多种变体和改进：</p>
              
              <div class="variants-grid">
                <div class="variant-item">
                  <h3>响应式蒸馏 (Response-Based)</h3>
                  <p>最基本的形式，学生模型学习教师模型的最终输出概率分布。</p>
                </div>
                <div class="variant-item">
                  <h3>特征蒸馏 (Feature-Based)</h3>
                  <p>学生模型学习教师模型的中间层特征表示，获取更丰富的知识。</p>
                </div>
                <div class="variant-item">
                  <h3>关系蒸馏 (Relation-Based)</h3>
                  <p>学生模型学习输入样本之间的关系，捕捉教师模型对数据结构的理解。</p>
                </div>
                <div class="variant-item">
                  <h3>自蒸馏 (Self-Distillation)</h3>
                  <p>模型作为自己的教师，深层特征指导浅层特征的学习。</p>
                </div>
                <div class="variant-item">
                  <h3>在线蒸馏 (Online Distillation)</h3>
                  <p>教师和学生模型同时训练，互相促进提高。</p>
                </div>
                <div class="variant-item">
                  <h3>数据自由蒸馏 (Data-Free)</h3>
                  <p>在没有原始训练数据的情况下进行知识迁移。</p>
                </div>
              </div>
            </div>
          </div>
  
          <!-- 实际应用 -->
          <div class="content-block">
            <div class="block-header">
              <h2>知识蒸馏在实际中的应用</h2>
            </div>
            <div class="block-content">
              <div class="applications">
                <div class="application-item">
                  <h3>移动设备上的AI</h3>
                  <p>蒸馏后的小模型可以在手机上实现高质量的语音助手、图像处理等功能，无需云端支持。</p>
                </div>
                <div class="application-item">
                  <h3>边缘计算</h3>
                  <p>在IoT设备、传感器网络中部署轻量级AI模型，实现实时数据处理和决策。</p>
                </div>
                <div class="application-item">
                  <h3>大语言模型压缩</h3>
                  <p>如DistilBERT比原始BERT小40%，速度快60%，同时保持BERT性能的97%。</p>
                </div>
                <div class="application-item">
                  <h3>计算机视觉</h3>
                  <p>压缩大型视觉模型，使其能在普通硬件上实时运行，如自动驾驶中的物体检测。</p>
                </div>
                <div class="application-item">
                  <h3>隐私保护</h3>
                  <p>将模型知识蒸馏到客户端设备上的小模型，避免敏感数据传输到云端。</p>
                </div>
              </div>
            </div>
          </div>
  
          <!-- 知识蒸馏的挑战和未来 -->
          <div class="content-block">
            <div class="block-header">
              <h2>挑战与未来发展</h2>
            </div>
            <div class="block-content">
              <p>尽管知识蒸馏已经取得了显著成功，但仍面临一些挑战：</p>
              
              <div class="challenges-box">
                <div class="challenge-item">
                  <h3>教师-学生容量差距</h3>
                  <p>当教师模型与学生模型的容量差距太大时，知识迁移会变得困难。</p>
                </div>
                <div class="challenge-item">
                  <h3>蒸馏设置优化</h3>
                  <p>温度参数、损失权重等超参数的选择对蒸馏效果有重大影响，但优化困难。</p>
                </div>
                <div class="challenge-item">
                  <h3>多模态蒸馏</h3>
                  <p>在涉及多种数据类型（文本、图像、音频）的模型中进行有效蒸馏仍具挑战性。</p>
                </div>
              </div>
  
              <p>未来的发展方向包括：</p>
              <ul class="future-directions">
                <li><strong>自适应蒸馏</strong>：根据任务和数据特性自动调整蒸馏策略</li>
                <li><strong>多教师蒸馏</strong>：从多个专家模型中学习不同领域的知识</li>
                <li><strong>持续蒸馏</strong>：在模型部署后不断更新和改进</li>
                <li><strong>跨模态蒸馏</strong>：在不同数据类型之间进行知识转移</li>
              </ul>
            </div>
          </div>
  
          <!-- 结论 -->
          <div class="content-block conclusion-block">
            <div class="block-header">
              <h2>总结：知识蒸馏的价值</h2>
            </div>
            <div class="block-content">
              <p>知识蒸馏代表了AI民主化的重要一步，让强大的AI能力不再局限于拥有大量计算资源的机构和企业。</p>
              <p>通过知识蒸馏，我们可以：</p>
              <div class="conclusion-points">
                <div class="conclusion-point">
                  <h3>降低资源需求</h3>
                  <p>使AI技术更加经济实惠，节省能源，减少碳排放</p>
                </div>
                <div class="conclusion-point">
                  <h3>拓展应用场景</h3>
                  <p>将AI能力带到更多设备和环境中，从数据中心到边缘设备</p>
                </div>
                <div class="conclusion-point">
                  <h3>加速推理过程</h3>
                  <p>提高AI系统的响应速度，改善用户体验</p>
                </div>
              </div>
              <p>随着大型AI模型规模的持续增长，知识蒸馏将变得越来越重要，成为连接尖端AI研究与广泛实际应用的关键桥梁。</p>
            </div>
          </div>
        </div>
        <AiFooter />
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import AiFooter from '../components/AiFooter.vue';
  // 可以添加任何需要的交互逻辑
  </script>
  
  <style lang="scss" scoped>
  .learn-ai-knowledge-distillation-page {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 120px);
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .title-section {
      text-align: center;
      padding: 40px 20px;
      background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
      
      .page-title {
        font-size: 36px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 15px 0;
        background: linear-gradient(to right, #409EFF, #36cfc9);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      
      .subtitle {
        font-size: 18px;
        color: #606266;
        margin: 0;
      }
    }
    
    .intro-section {
      padding: 20px 40px;
      font-size: 18px;
      line-height: 1.8;
      color: #303133;
      text-align: center;
      max-width: 900px;
      margin: 0 auto;
    }
    
    .content-blocks {
      padding: 20px 40px 60px;
      
      .content-block {
        margin-bottom: 60px;
        
        .block-header {
          margin-bottom: 20px;
          
          h2 {
            font-size: 28px;
            color: #303133;
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
            
            &::after {
              content: '';
              position: absolute;
              bottom: -8px;
              left: 0;
              width: 100%;
              height: 3px;
              background: linear-gradient(to right, #409EFF, #36cfc9);
              border-radius: 3px;
            }
          }
          
          .image-placeholder {
            width: 100%;
            height: 300px;
            margin: 20px 0;
            background-color: #f0f2f5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #909399;
            font-style: italic;
            
            &::after {
              content: "";
            }
          }
        }
        
        .block-content {
          font-size: 16px;
          line-height: 1.8;
          color: #606266;
          
          strong {
            color: #303133;
          }
          
          .highlight-box {
            background-color: #f0f9ff;
            border-left: 4px solid #409EFF;
            padding: 15px 20px;
            border-radius: 4px;
            margin: 20px 0;
            
            p {
              margin-top: 0;
            }
            
            ul, ol {
              margin-bottom: 0;
            }
          }
          
          .step-box {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin: 30px 0;
            
            h3 {
              margin-top: 0;
              margin-bottom: 20px;
              color: #303133;
              font-size: 20px;
            }
            
            .steps {
              display: flex;
              flex-wrap: wrap;
              gap: 20px;
              
              .step {
                flex: 1;
                min-width: 250px;
                display: flex;
                align-items: flex-start;
                
                .step-number {
                  width: 30px;
                  height: 30px;
                  border-radius: 50%;
                  background-color: #409EFF;
                  color: white;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-weight: bold;
                  margin-right: 15px;
                  flex-shrink: 0;
                }
                
                .step-content {
                  h4 {
                    margin-top: 0;
                    margin-bottom: 8px;
                    color: #303133;
                  }
                  
                  p {
                    margin: 0;
                  }
                }
              }
            }
          }
          
          .component-box {
            background-color: #f4f8f9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            
            h3 {
              margin-top: 0;
              color: #303133;
              font-size: 18px;
            }
            
            p {
              margin-bottom: 10px;
            }
            
            ul {
              margin-bottom: 0;
            }
          }
          
          .example-box {
            background-color: #f7f8fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            
            h3 {
              margin-top: 0;
              color: #303133;
              font-size: 18px;
            }
          }
          
          .variants-grid, .applications {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
            
            .variant-item, .application-item {
              background-color: #f7f8fa;
              border-radius: 8px;
              padding: 20px;
              
              h3 {
                margin-top: 0;
                color: #303133;
                font-size: 18px;
              }
              
              p {
                margin-bottom: 0;
              }
            }
          }
          
          .challenges-box {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
            
            .challenge-item {
              background-color: #fff1f0;
              border-left: 4px solid #ff4d4f;
              padding: 15px 20px;
              border-radius: 4px;
              
              h3 {
                margin-top: 0;
                color: #303133;
                font-size: 18px;
              }
              
              p {
                margin-bottom: 0;
              }
            }
          }
          
          .future-directions {
            padding-left: 20px;
            
            li {
              margin-bottom: 10px;
            }
          }
          
          .conclusion-points {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
            
            .conclusion-point {
              background-color: #f2f6fc;
              border-radius: 8px;
              padding: 20px;
              
              h3 {
                margin-top: 0;
                color: #303133;
                font-size: 18px;
              }
              
              p {
                margin-bottom: 0;
              }
            }
          }
        }
        
        .conclusion-block {
          background-color: #f0f9ff;
          border-radius: 8px;
          padding: 30px;
          margin-top: 40px;
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .learn-ai-knowledge-distillation-page {
      .title-section {
        padding: 30px 15px;
        
        .page-title {
          font-size: 28px;
        }
        
        .subtitle {
          font-size: 16px;
        }
      }
      
      .intro-section {
        padding: 15px 20px;
        font-size: 16px;
      }
      
      .content-blocks {
        padding: 15px 20px 40px;
        
        .content-block {
          margin-bottom: 40px;
          
          .block-header {
            h2 {
              font-size: 24px;
            }
            
            .image-placeholder {
              height: 200px;
            }
          }
          
          .step-box {
            .steps {
              flex-direction: column;
              
              .step {
                min-width: auto;
              }
            }
          }
        }
      }
    }
  }
  </style> 