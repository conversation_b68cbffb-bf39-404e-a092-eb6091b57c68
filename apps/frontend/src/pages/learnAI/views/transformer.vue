<template>
  <div class="learn-ai-transformer-page">
    <div class="container">
      <div class="title-section">
        <h1 class="page-title">理解Transformer：AI大模型的核心引擎</h1>
        <p class="subtitle">通俗易懂的图解与解释</p>
      </div>

      <div class="section intro-section">
        <p>Transformer是现代大型语言模型（如GPT、BERT等）的核心架构，2017年由Google研究团队提出。它彻底改变了自然语言处理领域，让AI系统能更好地理解和生成人类语言。</p>
      </div>

      <!-- 主要区块 -->
      <div class="content-blocks">
        <!-- Transformer概览 -->
        <div class="content-block">
          <div class="block-header">
            <h2>什么是Transformer？</h2>
            <div class="image-placeholder overview-image">
              <!-- Transformer总体架构的手绘风格SVG图像 -->
            </div>
          </div>
          <div class="block-content">
            <p><strong>简单来说</strong>：Transformer是一种神经网络架构，专门设计用来处理序列数据（如文本），它能同时关注输入中的所有部分，而不是一个接一个地处理。</p>
            <p>想象你在看一本书时，不是从头到尾按顺序读，而是能立刻理解整个页面的内容，甚至能立即发现页面上不同段落之间的关联。这就是Transformer的魔力。</p>
            <div class="highlight-box">
              <p><strong>为什么Transformer如此特别？</strong></p>
              <ul>
                <li>它能<strong>并行处理</strong>输入序列，而不是一个词一个词地处理</li>
                <li>它能通过<strong>自注意力</strong>机制，捕捉序列中任意位置之间的关联</li>
                <li>它不依赖循环结构，训练速度更快，效果更好</li>
                <li>它是当今几乎所有大型语言模型的基础结构</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 自注意力机制 -->
        <div class="content-block">
          <div class="block-header">
            <h2>自注意力：Transformer的超能力</h2>
            <div class="image-placeholder attention-image">
              <!-- 自注意力机制的手绘风格SVG图像 -->
            </div>
          </div>
          <div class="block-content">
            <p><strong>如同阅读理解高手</strong>：自注意力是Transformer的核心机制，它让模型能够"看到"序列中的所有单词，并确定它们之间的关联强度。</p>
            <p>以这个句子为例：<em>"小明看到了一只狗，它正在公园里奔跑"</em></p>
            <p>当处理"它"这个词时，自注意力机制会：</p>
            <ol>
              <li>计算"它"与句子中每个其他词的关联度</li>
              <li>发现"它"与"狗"的关联最强</li>
              <li>因此理解"它"指的是"狗"，而不是"小明"或"公园"</li>
            </ol>
            <div class="step-box">
              <h3>自注意力的计算步骤：</h3>
              <div class="steps">
                <div class="step">
                  <div class="step-number">1</div>
                  <div class="step-content">
                    <h4>三重变换</h4>
                    <p>将每个词转换为三种不同的向量：查询(Q)、键(K)和值(V)</p>
                  </div>
                </div>
                <div class="step">
                  <div class="step-number">2</div>
                  <div class="step-content">
                    <h4>计算注意力得分</h4>
                    <p>用当前词的Q与所有词的K计算相似度，得到注意力分数</p>
                  </div>
                </div>
                <div class="step">
                  <div class="step-number">3</div>
                  <div class="step-content">
                    <h4>加权汇总</h4>
                    <p>根据注意力分数对所有词的V进行加权求和，得到输出</p>
                  </div>
                </div>
              </div>
            </div>
            <p>通过这个机制，模型可以将重点放在最相关的词上，从而更好地理解上下文。</p>
          </div>
        </div>

        <!-- Transformer架构 -->
        <div class="content-block">
          <div class="block-header">
            <h2>Transformer的架构：编码器与解码器</h2>
            <div class="image-placeholder architecture-image">
              <!-- Transformer编码器-解码器架构的手绘风格SVG图像 -->
            </div>
          </div>
          <div class="block-content">
            <p><strong>如同翻译专家团队</strong>：完整的Transformer由两大部分组成，各有特定职责。</p>
            
            <div class="component-box">
              <h3>编码器 (Encoder)</h3>
              <p><strong>理解输入</strong>：就像一位精通外语的人，仔细阅读并理解源文本的含义。</p>
              <ul>
                <li>包含多层自注意力和前馈神经网络</li>
                <li>处理整个输入序列，捕捉所有上下文信息</li>
                <li>输出是输入序列的深层表示，包含丰富的上下文信息</li>
              </ul>
            </div>
            
            <div class="component-box">
              <h3>解码器 (Decoder)</h3>
              <p><strong>生成输出</strong>：如同作家，根据理解创作新内容。</p>
              <ul>
                <li>也有多层结构，但包含两种注意力：掩码自注意力和编码器-解码器注意力</li>
                <li>逐个生成输出，同时参考已生成的输出和编码器的理解</li>
                <li>掩码机制确保预测下一个词时只看到之前的词，不会"作弊"</li>
              </ul>
            </div>
            
            <p>这种分工合作的架构使Transformer特别适合翻译、摘要、问答等任务，需要先理解再生成的场景。</p>
          </div>
        </div>

        <!-- 位置编码 -->
        <div class="content-block">
          <div class="block-header">
            <h2>位置编码：解决顺序问题</h2>
            <div class="image-placeholder position-image">
              <!-- 位置编码的手绘风格SVG图像 -->
            </div>
          </div>
          <div class="block-content">
            <p><strong>如同页码系统</strong>：由于Transformer并行处理所有词，它本身不知道词的顺序。位置编码解决了这个问题。</p>
            <p>想象一本散页的书被打乱，位置编码就像页码，告诉我们每页在原书中的位置。</p>
            <div class="highlight-box">
              <p>位置编码使用正弦和余弦函数生成一系列值，这些值：</p>
              <ul>
                <li>对每个位置都是唯一的</li>
                <li>相近位置有相似的编码</li>
                <li>允许模型外推到更长的序列</li>
              </ul>
            </div>
            <p>位置编码直接添加到词嵌入中，这样每个词的表示就同时包含了"是什么词"和"在哪个位置"两种信息。</p>
          </div>
        </div>

        <!-- Transformer的影响 -->
        <div class="content-block">
          <div class="block-header">
            <h2>Transformer的影响与发展</h2>
          </div>
          <div class="block-content">
            <p>自2017年问世以来，Transformer架构已经衍生出众多强大的模型：</p>
            <div class="impact-grid">
              <div class="impact-item">
                <h3>BERT</h3>
                <p>由Google开发，专注于理解语言，通过预训练和微调，极大提高了众多NLP任务的表现。</p>
              </div>
              <div class="impact-item">
                <h3>GPT系列</h3>
                <p>由OpenAI开发，专注于生成文本，每一代都更大更强，GPT-4已经展示出接近人类的语言能力。</p>
              </div>
              <div class="impact-item">
                <h3>T5</h3>
                <p>将所有NLP任务统一为文本到文本的转换，简化了模型应用方式。</p>
              </div>
              <div class="impact-item">
                <h3>ViT</h3>
                <p>Vision Transformer，将Transformer应用于图像处理，挑战了CNN的统治地位。</p>
              </div>
            </div>
            <p>如今，Transformer的应用已经远超自然语言处理，扩展到计算机视觉、语音识别、药物发现等众多领域。</p>
          </div>
        </div>

        <!-- 实际应用 -->
        <div class="content-block">
          <div class="block-header">
            <h2>Transformer在实际中的应用</h2>
          </div>
          <div class="block-content">
            <div class="applications">
              <div class="application-item">
                <h3>语言翻译</h3>
                <p>Google翻译等服务使用Transformer大幅提升了翻译质量，特别是对于长句和复杂表达。</p>
              </div>
              <div class="application-item">
                <h3>内容生成</h3>
                <p>从写作助手到代码生成，Transformer使AI能够创作高质量的、连贯的长文本和程序。</p>
              </div>
              <div class="application-item">
                <h3>对话系统</h3>
                <p>聊天机器人和虚拟助手利用Transformer处理上下文，提供更自然的对话体验。</p>
              </div>
              <div class="application-item">
                <h3>信息提取</h3>
                <p>从非结构化文本中提取关键信息，如人名、地点、事件等，用于知识图谱构建。</p>
              </div>
              <div class="application-item">
                <h3>多模态系统</h3>
                <p>结合文本、图像、音频的理解和生成，如DALL-E、GPT-4等。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 总结 -->
        <div class="conclusion-section">
          <h2>为什么Transformer如此重要？</h2>
          <p>Transformer架构的出现是AI发展中的一个重要里程碑，原因有：</p>
          <ul>
            <li><strong>全局上下文</strong>：能够理解长文本中远距离的依赖关系</li>
            <li><strong>高效计算</strong>：能够并行处理，极大加速了训练速度</li>
            <li><strong>可扩展性</strong>：证明了"更大通常意味着更好"的扩展法则</li>
            <li><strong>通用性</strong>：不仅适用于语言，还可用于图像、音频等多种数据类型</li>
          </ul>
          <p>从某种意义上说，Transformer开启了AI的新纪元，让我们离通用人工智能又近了一步。它让机器能够更好地理解人类的语言、意图和知识，为AI与人类的交流创造了新的可能。</p>
        </div>
      </div>
      <AiFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
import AiFooter from '../components/AiFooter.vue';
// 组件逻辑
</script>

<style lang="scss" scoped>
.learn-ai-transformer-page {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  line-height: 1.6;
  padding: 20px;
  background-color: #f9f9f9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.title-section {
  text-align: center;
  margin-bottom: 40px;
  
  .page-title {
    font-size: 2.8rem;
    margin-bottom: 10px;
    font-weight: 700;
  }
  
  .subtitle {
    font-size: 1.2rem;
    color: #666;
  }
}

.intro-section {
  font-size: 1.2rem;
  margin-bottom: 40px;
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.8;
}

.content-blocks {
  display: flex;
  flex-direction: column;
  gap: 60px;
  margin-bottom: 60px;
}

.content-block {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.block-header {
  padding: 25px;
  
  h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    font-weight: 600;
  }
}

.image-placeholder {
  height: 250px;
  background-color: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  border-radius: 8px;
  font-style: italic;
  color: #666;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;

  &.overview-image {
    &:after {
      content: "";
    }
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 600 350' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f0f0f0;stroke:%23333;stroke-width:2%7D.st3%7Bfill:%23fff;stroke:%23333;stroke-width:2%7D.st4%7Bfill:%23f9f9f9;stroke:%23333;stroke-width:2%7D.st5%7Bfill:%23f0f0f0;stroke:%23333;stroke-width:1.5%7D.blue%7Bfill:%23d1e8ff;stroke:%23333;stroke-width:1.5%7D.pink%7Bfill:%23f9d5e5;stroke:%23333;stroke-width:1.5%7D.st6%7Bfill:none;stroke:%23333;stroke-width:1.5;stroke-dasharray:4,3%7D%3C/style%3E%3Cg transform='translate(0 10)'%3E%3Crect x='150' y='40' width='300' height='250' rx='10' ry='10' class='st4'/%3E%3Ctext x='300' y='30' text-anchor='middle' class='st1'%3ETransformer 架构%3C/text%3E%3Crect x='170' y='60' width='120' height='200' rx='5' ry='5' class='st5'/%3E%3Ctext x='230' y='75' text-anchor='middle' class='st1'%3E编码器%3C/text%3E%3Crect x='310' y='60' width='120' height='200' rx='5' ry='5' class='st5'/%3E%3Ctext x='370' y='75' text-anchor='middle' class='st1'%3E解码器%3C/text%3E%3Crect x='180' y='85' width='100' height='35' rx='3' ry='3' class='blue'/%3E%3Ctext x='230' y='107' text-anchor='middle' class='st1'%3E自注意力%3C/text%3E%3Crect x='180' y='130' width='100' height='35' rx='3' ry='3' class='pink'/%3E%3Ctext x='230' y='152' text-anchor='middle' class='st1'%3E前馈网络%3C/text%3E%3Crect x='180' y='175' width='100' height='35' rx='3' ry='3' class='blue'/%3E%3Ctext x='230' y='197' text-anchor='middle' class='st1'%3E自注意力%3C/text%3E%3Crect x='180' y='220' width='100' height='35' rx='3' ry='3' class='pink'/%3E%3Ctext x='230' y='242' text-anchor='middle' class='st1'%3E前馈网络%3C/text%3E%3Crect x='320' y='85' width='100' height='35' rx='3' ry='3' class='blue'/%3E%3Ctext x='370' y='107' text-anchor='middle' class='st1'%3E掩码自注意力%3C/text%3E%3Crect x='320' y='130' width='100' height='35' rx='3' ry='3' class='blue'/%3E%3Ctext x='370' y='152' text-anchor='middle' class='st1'%3E编码器-解码器%3C/text%3E%3Ctext x='370' y='167' text-anchor='middle' class='st1'%3E注意力%3C/text%3E%3Crect x='320' y='175' width='100' height='35' rx='3' ry='3' class='pink'/%3E%3Ctext x='370' y='197' text-anchor='middle' class='st1'%3E前馈网络%3C/text%3E%3Cpath d='M230 270v30M370 270v30' class='st0'/%3E%3Cpath d='M180 320h300' class='st0'/%3E%3Ctext x='230' y='315' text-anchor='middle' class='st1'%3E输入%3C/text%3E%3Ctext x='370' y='315' text-anchor='middle' class='st1'%3E输出%3C/text%3E%3Cpath class='st6' d='M290 152h30'/%3E%3Cpath class='st0' d='M290 107h30'/%3E%3Cpath class='st0' d='M290 197h30'/%3E%3C/g%3E%3C/svg%3E");
  }
  
  &.attention-image {
    &:after {
      content: "";
    }
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 600 350' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f0f0f0;stroke:%23333;stroke-width:2%7D.st3%7Bfill:%23fff;stroke:%23333;stroke-width:2%7D.st4%7Bfill:%23f9d5e5;stroke:%23333;stroke-width:1.5%7D.blue%7Bfill:%23d1e8ff;stroke:%23333;stroke-width:1.5%7D.green%7Bfill:%23d1ffe8;stroke:%23333;stroke-width:1.5%7D.st5%7Bfill:none;stroke:%23666;stroke-width:1;stroke-dasharray:4,2%7D.emphasis%7Bfill:none;stroke:%23f06292;stroke-width:2;stroke-dasharray:none%7D%3C/style%3E%3Cg transform='translate(0 10)'%3E%3Ctext x='300' y='25' text-anchor='middle' class='st1'%3E自注意力机制%3C/text%3E%3Ccircle cx='150' cy='80' r='25' class='st4'/%3E%3Ctext x='150' y='85' text-anchor='middle' class='st1'%3E小明%3C/text%3E%3Ccircle cx='230' cy='80' r='25' class='st4'/%3E%3Ctext x='230' y='85' text-anchor='middle' class='st1'%3E看到%3C/text%3E%3Ccircle cx='310' cy='80' r='25' class='st4'/%3E%3Ctext x='310' y='85' text-anchor='middle' class='st1'%3E一只狗%3C/text%3E%3Ccircle cx='390' cy='80' r='25' class='blue'/%3E%3Ctext x='390' y='85' text-anchor='middle' class='st1'%3E它%3C/text%3E%3Ccircle cx='470' cy='80' r='25' class='st4'/%3E%3Ctext x='470' y='85' text-anchor='middle' class='st1'%3E奔跑%3C/text%3E%3Cpath d='M390 105c-8 40-160 40-240 0' class='st5'/%3E%3Cpath d='M390 105c-8 40-80 40-80 0' class='emphasis'/%3E%3Cpath d='M390 105c-8 40-0 40 0 0' class='st5'/%3E%3Cpath d='M390 105c8 40 80 40 80 0' class='st5'/%3E%3Ctext x='300' y='135' text-anchor='middle' class='st1'%3E'它'与'一只狗'的关联最强%3C/text%3E%3Crect x='100' y='160' width='120' height='40' rx='5' ry='5' class='blue'/%3E%3Ctext x='160' y='185' text-anchor='middle' class='st1'%3E查询(Q)%3C/text%3E%3Crect x='250' y='160' width='120' height='40' rx='5' ry='5' class='green'/%3E%3Ctext x='310' y='185' text-anchor='middle' class='st1'%3E键(K)%3C/text%3E%3Crect x='400' y='160' width='120' height='40' rx='5' ry='5' class='st4'/%3E%3Ctext x='460' y='185' text-anchor='middle' class='st1'%3E值(V)%3C/text%3E%3Cpath d='M160 200v25M310 200v25M460 200v25' class='st0'/%3E%3Crect x='100' y='225' width='360' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='280' y='250' text-anchor='middle' class='st1'%3E计算注意力得分并加权汇总%3C/text%3E%3Cpath d='M280 265v25' class='st0'/%3E%3Ccircle cx='280' cy='310' r='25' class='st3'/%3E%3Ctext x='280' y='315' text-anchor='middle' class='st1'%3E输出%3C/text%3E%3C/g%3E%3C/svg%3E");
  }
  
  &.architecture-image {
    &:after {
      content: "";
    }
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 600 350' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f0f0f0;stroke:%23333;stroke-width:2%7D.st3%7Bfill:%23fff;stroke:%23333;stroke-width:2%7D.enc%7Bfill:%23d1e8ff;stroke:%23333;stroke-width:2%7D.dec%7Bfill:%23f9d5e5;stroke:%23333;stroke-width:2%7D.st4%7Bfill:none;stroke:%23666;stroke-width:1.5;stroke-dasharray:none%7D%3C/style%3E%3Cg transform='translate(0 10)'%3E%3Ctext x='300' y='25' text-anchor='middle' class='st1'%3E编码器-解码器架构%3C/text%3E%3Crect x='120' y='50' width='160' height='220' rx='10' ry='10' class='enc'/%3E%3Ctext x='200' y='75' text-anchor='middle' class='st1'%3E编码器%3C/text%3E%3Crect x='320' y='50' width='160' height='220' rx='10' ry='10' class='dec'/%3E%3Ctext x='400' y='75' text-anchor='middle' class='st1'%3E解码器%3C/text%3E%3Ccircle cx='200' cy='120' r='30' class='st3'/%3E%3Ctext x='200' y='125' text-anchor='middle' class='st1'%3E理解输入%3C/text%3E%3Ccircle cx='200' cy='200' r='30' class='st3'/%3E%3Ctext x='200' y='195' text-anchor='middle' class='st1'%3E捕捉上下文%3C/text%3E%3Ctext x='200' y='210' text-anchor='middle' class='st1'%3E信息%3C/text%3E%3Ccircle cx='400' cy='120' r='30' class='st3'/%3E%3Ctext x='400' y='125' text-anchor='middle' class='st1'%3E生成输出%3C/text%3E%3Ccircle cx='400' cy='200' r='30' class='st3'/%3E%3Ctext x='400' y='195' text-anchor='middle' class='st1'%3E参考编码器%3C/text%3E%3Ctext x='400' y='210' text-anchor='middle' class='st1'%3E理解%3C/text%3E%3Cpath d='M240 120h30c5 0 10 5 10 10v60c0 5-5 10-10 10h-30' class='st0'/%3E%3Cpath d='M240 200h30c5 0 10-5 10-10v-60c0-5-5-10-10-10h-30' class='st0'/%3E%3Cpath d='M280 120h40M280 200h40' class='st4'/%3E%3Cpath d='M200 270v30M400 270v30' class='st0'/%3E%3Crect x='150' y='300' width='100' height='30' rx='5' ry='5' class='st2'/%3E%3Ctext x='200' y='320' text-anchor='middle' class='st1'%3E输入序列%3C/text%3E%3Crect x='350' y='300' width='100' height='30' rx='5' ry='5' class='st2'/%3E%3Ctext x='400' y='320' text-anchor='middle' class='st1'%3E输出序列%3C/text%3E%3C/g%3E%3C/svg%3E");
  }
  
  &.position-image {
    &:after {
      content: "";
    }
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 600 350' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f0f0f0;stroke:%23333;stroke-width:2%7D.st3%7Bfill:%23fff;stroke:%23333;stroke-width:2%7D.tok%7Bfill:%23d1e8ff;stroke:%23333;stroke-width:1.5%7D.pos%7Bfill:%23f9d5e5;stroke:%23333;stroke-width:1.5%7D.emb%7Bfill:%23d1ffe8;stroke:%23333;stroke-width:1.5%7D.arr%7Bfill:none;stroke:%23333;stroke-width:1.5;stroke-dasharray:none%7D%3C/style%3E%3Cg transform='translate(0 10)'%3E%3Ctext x='300' y='25' text-anchor='middle' class='st1'%3E位置编码%3C/text%3E%3Crect x='100' y='50' width='400' height='70' rx='5' ry='5' class='st3'/%3E%3Ctext x='300' y='70' text-anchor='middle' class='st1'%3E问题：Transformer如何知道词的顺序？%3C/text%3E%3Ctext x='300' y='95' text-anchor='middle' class='st1'%3E并行处理使模型失去了词序信息%3C/text%3E%3Crect x='120' y='140' width='80' height='40' rx='5' ry='5' class='tok'/%3E%3Ctext x='160' y='165' text-anchor='middle' class='st1'%3E词嵌入%3C/text%3E%3Crect x='120' y='190' width='80' height='40' rx='5' ry='5' class='pos'/%3E%3Ctext x='160' y='215' text-anchor='middle' class='st1'%3E位置编码%3C/text%3E%3Cpath d='M160 230v20' class='st0'/%3E%3Ccircle cx='160' cy='270' r='20' class='emb'/%3E%3Ctext x='160' y='275' text-anchor='middle' class='st1'%3E+%3C/text%3E%3Cpath d='M160 290v20' class='st0'/%3E%3Crect x='110' y='310' width='100' height='30' rx='5' ry='5' class='emb'/%3E%3Ctext x='160' y='330' text-anchor='middle' class='st1'%3E带位置信息的嵌入%3C/text%3E%3Ctext x='320' y='165' text-anchor='start' class='st1'%3E• 表示'是什么词'%3C/text%3E%3Ctext x='320' y='215' text-anchor='start' class='st1'%3E• 表示'在哪个位置'%3C/text%3E%3Ctext x='320' y='330' text-anchor='start' class='st1'%3E• 包含词义和位置的完整信息%3C/text%3E%3Cpath d='M220 165h90M220 215h90M220 330h90' class='arr'/%3E%3Ctext x='300' y='270' text-anchor='middle' class='st1'%3E使用正弦和余弦函数%3C/text%3E%3Ctext x='300' y='290' text-anchor='middle' class='st1'%3E为每个位置生成唯一编码%3C/text%3E%3C/g%3E%3C/svg%3E");
  }
}

.block-content {
  padding: 0 25px 25px;
  
  p {
    margin-bottom: 15px;
    line-height: 1.8;
  }
  
  ul, ol {
    padding-left: 20px;
    margin-bottom: 15px;
    
    li {
      margin-bottom: 8px;
      position: relative;
      line-height: 1.6;
    }
  }
  
  ol li {
    margin-left: 5px;
  }
}

.highlight-box {
  background-color: #f8f8f8;
  border-left: 4px solid #3498db;
  padding: 15px 20px;
  margin: 20px 0;
  border-radius: 0 8px 8px 0;
  
  p {
    margin-bottom: 10px;
  }
  
  ul {
    margin-bottom: 0;
  }
}

.component-box {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f8f8;
  border-radius: 8px;
  
  h3 {
    font-size: 1.4rem;
    margin-bottom: 10px;
    color: #444;
  }
  
  ul {
    margin-bottom: 0;
  }
}

.step-box {
  margin: 25px 0;
  
  h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
  }
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step {
  display: flex;
  gap: 15px;
  align-items: flex-start;
  
  .step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #3498db;
    color: white;
    border-radius: 50%;
    font-weight: bold;
  }
  
  .step-content {
    flex: 1;
    
    h4 {
      font-size: 1.2rem;
      margin-bottom: 5px;
      color: #333;
    }
    
    p {
      margin-bottom: 0;
      color: #555;
    }
  }
}

.impact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.impact-item {
  background-color: #f8f8f8;
  padding: 20px;
  border-radius: 8px;
  
  h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #333;
  }
  
  p {
    margin-bottom: 0;
    color: #555;
  }
}

.applications {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.application-item {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eee;
  
  h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #333;
  }
  
  p {
    margin-bottom: 0;
    color: #555;
  }
}

.conclusion-section {
  max-width: 800px;
  margin: 0 auto 40px;
  background-color: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
  }
  
  ul {
    padding-left: 20px;
    margin-bottom: 20px;
    
    li {
      margin-bottom: 10px;
      position: relative;
      line-height: 1.7;
    }
  }
  
  p:last-child {
    margin-bottom: 0;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .title-section .page-title {
    font-size: 2.2rem;
  }
  
  .image-placeholder {
    height: 180px;
  }
  
  .impact-grid, .applications {
    grid-template-columns: 1fr;
  }
}
</style>
