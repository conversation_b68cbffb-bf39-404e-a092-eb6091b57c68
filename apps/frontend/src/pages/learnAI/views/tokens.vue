<template>
    <div class="learn-ai-tokens-page">
      <div class="container">
        <div class="title-section">
          <h1 class="page-title">理解Tokens：AI大模型的基本单位</h1>
          <p class="subtitle">通俗易懂的图解与解释</p>
        </div>
  
        <div class="section intro-section">
          <p>在大型语言模型（如GPT、Claude、LLaMA）的世界中，"token"是一个核心概念。理解tokens的工作方式，可以帮助我们更好地与AI交流，并了解这些强大模型的局限性和能力。</p>
        </div>
  
        <!-- 主要区块 -->
        <div class="content-blocks">
          <!-- Token概览 -->
          <div class="content-block">
            <div class="block-header">
              <h2>什么是Token？</h2>
              <div class="image-placeholder overview-image">
                <!-- Token概念图-现代风格 -->
                <svg width="400" height="200" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
                  <g>
                    <!-- 文本框 -->
                    <rect x="20" y="20" width="360" height="60" rx="6" ry="6" fill="#f8f9fa" stroke="#ddd" stroke-width="1.5" />
                    <!-- 原始文本 -->
                    <text x="30" y="55" font-family="Arial, sans-serif" font-size="18" fill="#333" font-weight="500">Hello, how are you today?</text>
                    
                    <!-- 箭头 -->
                    <path d="M200,90 L200,120" stroke="#555" stroke-width="2" marker-end="url(#arrow)" />
                    
                    <!-- Token框 -->
                    <rect x="20" y="130" width="70" height="40" rx="4" ry="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1.5" />
                    <rect x="100" y="130" width="40" height="40" rx="4" ry="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1.5" />
                    <rect x="150" y="130" width="50" height="40" rx="4" ry="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1.5" />
                    <rect x="210" y="130" width="50" height="40" rx="4" ry="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1.5" />
                    <rect x="270" y="130" width="60" height="40" rx="4" ry="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1.5" />
                    <rect x="340" y="130" width="40" height="40" rx="4" ry="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1.5" />
                    
                    <!-- Token文本 -->
                    <text x="55" y="155" font-family="Arial, sans-serif" font-size="16" fill="#1976d2" text-anchor="middle" font-weight="500">Hello</text>
                    <text x="120" y="155" font-family="Arial, sans-serif" font-size="16" fill="#1976d2" text-anchor="middle" font-weight="500">,</text>
                    <text x="175" y="155" font-family="Arial, sans-serif" font-size="16" fill="#1976d2" text-anchor="middle" font-weight="500">how</text>
                    <text x="235" y="155" font-family="Arial, sans-serif" font-size="16" fill="#1976d2" text-anchor="middle" font-weight="500">are</text>
                    <text x="300" y="155" font-family="Arial, sans-serif" font-size="16" fill="#1976d2" text-anchor="middle" font-weight="500">you</text>
                    <text x="360" y="155" font-family="Arial, sans-serif" font-size="16" fill="#1976d2" text-anchor="middle" font-weight="500">?</text>
                  </g>
                  
                  <!-- 箭头标记定义 -->
                  <defs>
                    <marker id="arrow" viewBox="0 0 10 10" refX="5" refY="5"
                      markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                      <path d="M 0 0 L 10 5 L 0 10 z" fill="#555" />
                    </marker>
                  </defs>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>简单来说</strong>：Token是文本被分割成的最小单位，AI模型用它来处理和理解语言。不同于我们通常理解的"词"，token可以是一个单词、单词的一部分，甚至是一个标点符号。</p>
              <p>想象一个孩子正在学习拼音或者英语字母表，他们将单词拆解成更小的部分来理解。AI模型的token化过程与此类似，但更加复杂和高效。</p>
              <div class="highlight-box">
                <p><strong>为什么需要Token？</strong></p>
                <ul>
                  <li>它让AI可以用<strong>统一的方式</strong>处理不同语言的文本</li>
                  <li>它能将<strong>无限的词汇表</strong>转换为<strong>有限的token集合</strong></li>
                  <li>它是让模型理解和生成文本的基础</li>
                  <li>它决定了模型能处理多少内容（模型的"上下文窗口"）</li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- Token的工作原理 -->
          <div class="content-block">
            <div class="block-header">
              <h2>Token是如何工作的？</h2>
              <div class="image-placeholder mechanism-image">
                <!-- Token工作原理示意图-现代风格 -->
                <svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                  <g>
                    <!-- 标题 -->
                    <text x="70" y="30" font-family="Arial, sans-serif" font-size="16" fill="#333" text-anchor="start" font-weight="600">Token分词过程</text>
                    
                    <!-- 第一步：原始输入 -->
                    <rect x="30" y="50" width="340" height="40" rx="5" ry="5" fill="#f5f5f5" stroke="#ddd" stroke-width="1.5" />
                    <text x="40" y="75" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="500">输入: "人工智能正在改变世界"</text>
                    
                    <!-- 处理步骤箭头 -->
                    <path d="M200,100 L200,130" stroke="#666" stroke-width="2" marker-end="url(#arrow2)" />
                    <text x="210" y="115" font-family="Arial, sans-serif" font-size="12" fill="#666" font-weight="400">BPE算法处理</text>
                    
                    <!-- 第二步：分词结果 -->
                    <rect x="30" y="140" width="340" height="120" rx="5" ry="5" fill="#f5f5f5" stroke="#ddd" stroke-width="1.5" />
                    <text x="40" y="165" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="600">分词结果:</text>
                    
                    <!-- 分词结果展示 -->
                    <rect x="60" y="180" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="80" y="200" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">人</text>
                    
                    <rect x="110" y="180" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="130" y="200" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">工</text>
                    
                    <rect x="160" y="180" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="180" y="200" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">智</text>
                    
                    <rect x="210" y="180" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="230" y="200" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">能</text>
                    
                    <rect x="60" y="220" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="80" y="240" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">正</text>
                    
                    <rect x="110" y="220" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="130" y="240" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">在</text>
                    
                    <rect x="160" y="220" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="180" y="240" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">改</text>
                    
                    <rect x="210" y="220" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="230" y="240" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">变</text>
                    
                    <rect x="260" y="220" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="280" y="240" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">世</text>
                    
                    <rect x="310" y="220" width="40" height="30" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="330" y="240" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="500">界</text>
                    
                    <!-- 说明箭头，修改为更合适的提示 -->
                    <path d="M300,160 L340,160" stroke="#666" stroke-width="1.5" marker-end="url(#arrow2)" />
                    <text x="320" y="145" font-family="Arial, sans-serif" font-size="12" fill="#666" font-weight="500">共10个token</text>
                  </g>
                  
                  <!-- 箭头标记定义 -->
                  <defs>
                    <marker id="arrow2" viewBox="0 0 10 10" refX="5" refY="5"
                      markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                      <path d="M 0 0 L 10 5 L 0 10 z" fill="#666" />
                    </marker>
                  </defs>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>如同拼图游戏</strong>：Token化是将文本分解成模型能理解的小块的过程。这些"小块"可以是完整的单词、单词的一部分或标点符号。</p>
              
              <div class="step-box">
                <h3>Token化的三个步骤：</h3>
                <div class="steps">
                  <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h4>预处理</h4>
                      <p>规范化文本（如去除多余空格、统一大小写）</p>
                    </div>
                  </div>
                  <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h4>分词</h4>
                      <p>将文本切分成子词单元（通常使用BPE或WordPiece等算法）</p>
                    </div>
                  </div>
                  <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h4>转换为ID</h4>
                      <p>将每个token映射到模型词汇表中的唯一数字ID</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="highlight-box">
                <p><strong>重要算法：字节对编码(BPE)</strong></p>
                <p>大多数现代AI模型使用BPE算法进行分词。它的工作方式是：</p>
                <ol>
                  <li>从单个字符开始，观察哪些字符对经常一起出现</li>
                  <li>将这些频繁出现的字符对合并成新的token</li>
                  <li>重复此过程，直到达到预定的词汇量大小</li>
                </ol>
                <p>这种方法能同时处理常见词和罕见词，提高了模型的效率和灵活性</p>
              </div>
              
              <p>不同语言的token化效率不同。例如，英语中常见单词可能只需要1-2个token，而像中文这样的语言，每个字符通常是一个独立的token。</p>
            </div>
          </div>
          
          <!-- 不同语言的Token比较 -->
          <div class="content-block">
            <div class="block-header">
              <h2>不同语言的Token效率</h2>
              <div class="image-placeholder language-image">
                <!-- 不同语言token比较-现代风格 -->
                <svg width="400" height="350" viewBox="0 0 400 350" xmlns="http://www.w3.org/2000/svg">
                  <g>
                    <!-- 标题 -->
                    <text x="100" y="30" font-family="Arial, sans-serif" font-size="16" fill="#333" text-anchor="start" font-weight="600">同一句话的Token数量比较</text>
                    
                    <!-- 英语示例 -->
                    <rect x="50" y="50" width="300" height="60" rx="5" ry="5" fill="#f8f9fa" stroke="#ddd" stroke-width="1.5" />
                    <text x="60" y="70" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="600">英语:</text>
                    <text x="110" y="70" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="400">"Artificial intelligence is changing the world"</text>
                    
                    <!-- 英语tokens -->
                    <rect x="60" y="85" width="60" height="20" rx="3" ry="3" fill="#e3f2fd" stroke="#1565c0" stroke-width="1.5" />
                    <text x="90" y="100" font-family="Arial, sans-serif" font-size="10" fill="#1565c0" text-anchor="middle" font-weight="500">Artificial</text>
                    
                    <rect x="125" y="85" width="60" height="20" rx="3" ry="3" fill="#e3f2fd" stroke="#1565c0" stroke-width="1.5" />
                    <text x="155" y="100" font-family="Arial, sans-serif" font-size="10" fill="#1565c0" text-anchor="middle" font-weight="500">intelligence</text>
                    
                    <rect x="190" y="85" width="25" height="20" rx="3" ry="3" fill="#e3f2fd" stroke="#1565c0" stroke-width="1.5" />
                    <text x="202" y="100" font-family="Arial, sans-serif" font-size="10" fill="#1565c0" text-anchor="middle" font-weight="500">is</text>
                    
                    <rect x="220" y="85" width="50" height="20" rx="3" ry="3" fill="#e3f2fd" stroke="#1565c0" stroke-width="1.5" />
                    <text x="245" y="100" font-family="Arial, sans-serif" font-size="10" fill="#1565c0" text-anchor="middle" font-weight="500">changing</text>
                    
                    <rect x="275" y="85" width="30" height="20" rx="3" ry="3" fill="#e3f2fd" stroke="#1565c0" stroke-width="1.5" />
                    <text x="290" y="100" font-family="Arial, sans-serif" font-size="10" fill="#1565c0" text-anchor="middle" font-weight="500">the</text>
                    
                    <rect x="310" y="85" width="40" height="20" rx="3" ry="3" fill="#e3f2fd" stroke="#1565c0" stroke-width="1.5" />
                    <text x="330" y="100" font-family="Arial, sans-serif" font-size="10" fill="#1565c0" text-anchor="middle" font-weight="500">world</text>
                    
                    <text x="360" y="95" font-family="Arial, sans-serif" font-size="12" fill="#1565c0" text-anchor="middle" font-weight="600">7个</text>
                    
                    <!-- 中文示例 -->
                    <rect x="50" y="130" width="300" height="60" rx="5" ry="5" fill="#f8f9fa" stroke="#ddd" stroke-width="1.5" />
                    <text x="60" y="150" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="600">中文:</text>
                    <text x="110" y="150" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="400">"人工智能正在改变世界"</text>
                    
                    <!-- 中文tokens -->
                    <rect x="60" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="70" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">人</text>
                    
                    <rect x="85" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="95" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">工</text>
                    
                    <rect x="110" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="120" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">智</text>
                    
                    <rect x="135" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="145" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">能</text>
                    
                    <rect x="160" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="170" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">正</text>
                    
                    <rect x="185" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="195" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">在</text>
                    
                    <rect x="210" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="220" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">改</text>
                    
                    <rect x="235" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="245" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">变</text>
                    
                    <rect x="260" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="270" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">世</text>
                    
                    <rect x="285" y="165" width="20" height="20" rx="3" ry="3" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="295" y="180" font-family="Arial, sans-serif" font-size="10" fill="#2e7d32" text-anchor="middle" font-weight="500">界</text>
                    
                    <text x="360" y="175" font-family="Arial, sans-serif" font-size="12" fill="#2e7d32" text-anchor="middle" font-weight="600">10个</text>
                    
                    <!-- 代码示例 -->
                    <rect x="50" y="210" width="300" height="60" rx="5" ry="5" fill="#f8f9fa" stroke="#ddd" stroke-width="1.5" />
                    <text x="60" y="230" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="600">代码:</text>
                    <text x="110" y="230" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="400">for (let i = 0; i < 10; i++) { }</text>
                    
                    <!-- 代码tokens -->
                    <rect x="60" y="245" width="25" height="20" rx="3" ry="3" fill="#fff3e0" stroke="#e65100" stroke-width="1.5" />
                    <text x="72" y="260" font-family="Arial, sans-serif" font-size="10" fill="#e65100" text-anchor="middle" font-weight="500">for</text>
                    
                    <rect x="90" y="245" width="15" height="20" rx="3" ry="3" fill="#fff3e0" stroke="#e65100" stroke-width="1.5" />
                    <text x="97" y="260" font-family="Arial, sans-serif" font-size="10" fill="#e65100" text-anchor="middle" font-weight="500">(</text>
                    
                    <rect x="110" y="245" width="25" height="20" rx="3" ry="3" fill="#fff3e0" stroke="#e65100" stroke-width="1.5" />
                    <text x="122" y="260" font-family="Arial, sans-serif" font-size="10" fill="#e65100" text-anchor="middle" font-weight="500">let</text>
                    
                    <rect x="140" y="245" width="15" height="20" rx="3" ry="3" fill="#fff3e0" stroke="#e65100" stroke-width="1.5" />
                    <text x="147" y="260" font-family="Arial, sans-serif" font-size="10" fill="#e65100" text-anchor="middle" font-weight="500">i</text>
                    
                    <rect x="160" y="245" width="15" height="20" rx="3" ry="3" fill="#fff3e0" stroke="#e65100" stroke-width="1.5" />
                    <text x="167" y="260" font-family="Arial, sans-serif" font-size="10" fill="#e65100" text-anchor="middle" font-weight="500">=</text>
                    
                    <rect x="180" y="245" width="15" height="20" rx="3" ry="3" fill="#fff3e0" stroke="#e65100" stroke-width="1.5" />
                    <text x="187" y="260" font-family="Arial, sans-serif" font-size="10" fill="#e65100" text-anchor="middle" font-weight="500">0</text>
                    
                    <rect x="200" y="245" width="15" height="20" rx="3" ry="3" fill="#fff3e0" stroke="#e65100" stroke-width="1.5" />
                    <text x="207" y="260" font-family="Arial, sans-serif" font-size="10" fill="#e65100" text-anchor="middle" font-weight="500">;</text>
                    
                    <!-- 省略其他代码token，太多了 -->
                    <text x="285" y="260" font-family="Arial, sans-serif" font-size="10" fill="#e65100" text-anchor="middle" font-weight="400">...</text>
                    
                    <text x="360" y="255" font-family="Arial, sans-serif" font-size="12" fill="#e65100" text-anchor="middle" font-weight="600">20个</text>
                    
                    <!-- 结论 -->
                    <rect x="50" y="290" width="300" height="40" rx="5" ry="5" fill="#ede7f6" stroke="#4527a0" stroke-width="1.5" />
                    <text x="65" y="315" font-family="Arial, sans-serif" font-size="14" fill="#4527a0" font-weight="500">结论: 不同类型的内容，token数量差异很大！</text>
                  </g>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>如同不同语言的字母表</strong>：不同语言在token化过程中有着显著的效率差异。这对使用AI模型处理多语言内容非常重要。</p>
              
              <div class="language-comparison">
                <div class="comparison-item">
                  <h3>英语</h3>
                  <p><strong>效率较高</strong>：常用英语单词通常只需要1-2个token</p>
                  <p>例如，"hello"是1个token，"understanding"可能是2个token（"understand"+"ing"）</p>
                  <p>这意味着处理相同长度的内容，英语消耗的token往往较少</p>
                </div>
                
                <div class="comparison-item">
                  <h3>中文、日语和汉字语言</h3>
                  <p><strong>效率较低</strong>：通常每个汉字都是一个独立的token</p>
                  <p>例如，"你好"是2个token（"你"+"好"）</p>
                  <p>这意味着同样长度的内容，中文可能会消耗2-3倍的token数量</p>
                </div>
                
                <div class="comparison-item">
                  <h3>代码和特殊内容</h3>
                  <p><strong>效率不稳定</strong>：代码中的特殊符号、缩进、换行都会被视为独立的token</p>
                  <p>常见的编程关键词（如"function"、"for"、"if"）通常是单个token</p>
                  <p>但代码中的变量名、符号（如"{"、";"）都可能是独立token，使代码的token效率较低</p>
                </div>
              </div>
              
              <div class="highlight-box">
                <p><strong>实用知识</strong>：了解token效率差异有助于：</p>
                <ul>
                  <li>估算API调用成本（因为大多数API按token计费）</li>
                  <li>优化提示词的长度，特别是在使用多语言时</li>
                  <li>理解为什么同样长度的文本，不同语言的处理速度和成本会有差异</li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- 上下文窗口 -->
          <div class="content-block">
            <div class="block-header">
              <h2>上下文窗口：AI的"注意力"范围</h2>
              <div class="image-placeholder context-image">
                <!-- 上下文窗口示意图-现代风格 -->
                <svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
                  <g>
                    <!-- 大模型标题 -->
                    <text x="200" y="25" font-family="Arial, sans-serif" font-size="16" fill="#333" text-anchor="middle" font-weight="600">大语言模型的上下文窗口</text>
                    
                    <!-- 上下文窗口框 -->
                    <rect x="50" y="40" width="300" height="150" rx="10" ry="10" fill="#f5f5f5" stroke="#bbb" stroke-width="1.5" stroke-dasharray="5,3" />
                    <text x="70" y="65" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="600">用户提问:</text>
                    
                    <!-- 文本内容 -->
                    <text x="70" y="90" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">计算物理中的数值方法有哪些?</text>
                    <text x="70" y="115" font-family="Arial, sans-serif" font-size="14" fill="#333" font-weight="600">AI回复:</text>
                    <text x="70" y="140" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">计算物理中常见的数值方法包括:</text>
                    <text x="70" y="160" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">1. 有限差分法</text>
                    <text x="70" y="180" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">2. 蒙特卡洛方法 ...</text>
                    
                    <!-- 窗口标注 -->
                    <text x="200" y="210" font-family="Arial, sans-serif" font-size="14" fill="#e91e63" text-anchor="middle" font-weight="500">☝️ 模型同时可见的内容范围（例如8K tokens）</text>
                    
                    <!-- 模型表示 -->
                    <ellipse cx="370" cy="120" rx="20" ry="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1.5" />
                    <text x="370" y="125" font-family="Arial, sans-serif" font-size="12" fill="#1976d2" text-anchor="middle" font-weight="600">AI</text>
                    
                    <!-- 箭头 -->
                    <path d="M320,120 L340,120" stroke="#1976d2" stroke-width="2" marker-end="url(#arrow4)" />
                  </g>
                  
                  <!-- 箭头标记定义 -->
                  <defs>
                    <marker id="arrow4" viewBox="0 0 10 10" refX="5" refY="5"
                      markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                      <path d="M 0 0 L 10 5 L 0 10 z" fill="#1976d2" />
                    </marker>
                  </defs>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>如同记忆范围</strong>：上下文窗口是AI模型能够"看到"和"记住"的文本范围，以token为单位计算。</p>
              
              <div class="highlight-box">
                <p><strong>为什么上下文窗口很重要？</strong></p>
                <ul>
                  <li>它决定了模型能处理多长的对话或文档</li>
                  <li>它限制了模型能引用的历史信息范围</li>
                  <li>它影响了模型处理长文本任务（如摘要、分析）的能力</li>
                  <li>不同模型的上下文窗口大小差异很大（从4K到1M tokens不等）</li>
                </ul>
              </div>
              
              <p>上下文窗口的大小是AI模型架构的核心参数之一。随着技术发展，这个窗口不断扩大：</p>
              
              <div class="context-evolution">
                <div class="evolution-item">
                  <h4>早期模型 (2020-2021)</h4>
                  <p>GPT-3：2048 tokens (约1500个英文单词)</p>
                </div>
                <div class="evolution-item">
                  <h4>中期模型 (2022-2023)</h4>
                  <p>GPT-3.5：4096 tokens (约3000个英文单词)</p>
                  <p>Claude 1：8-9K tokens</p>
                </div>
                <div class="evolution-item">
                  <h4>当前模型 (2023-2024)</h4>
                  <p>GPT-4：128K tokens (约10万个英文单词)</p>
                  <p>Claude 3：200K tokens</p>
                  <p>某些专业模型：1M+ tokens</p>
                </div>
              </div>
              
              <p>更大的上下文窗口意味着模型可以：</p>
              <ul>
                <li>处理和分析更长的文档（如研究论文、书籍章节）</li>
                <li>记住更长时间的对话历史，保持连贯性</li>
                <li>同时考虑更多的背景信息，生成更相关的回应</li>
              </ul>
            </div>
          </div>
          
          <!-- Token的实际应用和限制 -->
          <div class="content-block">
            <div class="block-header">
              <h2>Token的实际应用与限制</h2>
              <div class="image-placeholder applications-image">
                <!-- 应用场景示意图-现代风格 -->
                <svg width="400" height="280" viewBox="0 0 400 280" xmlns="http://www.w3.org/2000/svg">
                  <g>
                    <!-- 标题 -->
                    <text x="200" y="30" font-family="Arial, sans-serif" font-size="16" fill="#333" text-anchor="middle" font-weight="600">Token应用与限制</text>
                    
                    <!-- 应用场景面板 -->
                    <rect x="30" y="50" width="160" height="200" rx="8" ry="8" fill="#e8f5e9" stroke="#2e7d32" stroke-width="1.5" />
                    <text x="110" y="75" font-family="Arial, sans-serif" font-size="14" fill="#2e7d32" text-anchor="middle" font-weight="600">有效应用</text>
                    
                    <!-- 应用场景图标和文字 -->
                    <circle cx="60" cy="100" r="8" fill="#4caf50" />
                    <text x="80" y="104" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">优化提示词结构</text>
                    
                    <circle cx="60" cy="130" r="8" fill="#4caf50" />
                    <text x="80" y="134" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">估算API调用成本</text>
                    
                    <circle cx="60" cy="160" r="8" fill="#4caf50" />
                    <text x="80" y="164" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">优化多语言处理</text>
                    
                    <circle cx="60" cy="190" r="8" fill="#4caf50" />
                    <text x="80" y="194" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">切分长文档处理</text>
                    
                    <circle cx="60" cy="220" r="8" fill="#4caf50" />
                    <text x="80" y="224" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">理解模型能力边界</text>
                    
                    <!-- 限制面板 -->
                    <rect x="210" y="50" width="160" height="200" rx="8" ry="8" fill="#ffebee" stroke="#c62828" stroke-width="1.5" />
                    <text x="290" y="75" font-family="Arial, sans-serif" font-size="14" fill="#c62828" text-anchor="middle" font-weight="600">主要限制</text>
                    
                    <!-- 限制图标和文字 -->
                    <circle cx="240" cy="100" r="8" fill="#f44336" />
                    <text x="260" y="104" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">上下文长度有限</text>
                    
                    <circle cx="240" cy="130" r="8" fill="#f44336" />
                    <text x="260" y="134" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">某些语言效率低</text>
                    
                    <circle cx="240" cy="160" r="8" fill="#f44336" />
                    <text x="260" y="164" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">单词可能被拆分</text>
                    
                    <circle cx="240" cy="190" r="8" fill="#f44336" />
                    <text x="260" y="194" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">计算成本随长度增加</text>
                    
                    <circle cx="240" cy="220" r="8" fill="#f44336" />
                    <text x="260" y="224" font-family="Arial, sans-serif" font-size="12" fill="#333" font-weight="400">API费用基于token计算</text>
                  </g>
                </svg>
              </div>
            </div>
            <div class="block-content">
              <p><strong>在实际应用中</strong>，理解token的工作原理可以帮助我们更有效地使用AI模型。</p>
              
              <div class="practical-applications">
                <h3>实用技巧</h3>
                
                <div class="tip-item">
                  <h4>提示词优化</h4>
                  <p>使提示词简洁明了，减少不必要的装饰性语言</p>
                  <p>例如："我需要你帮我非常详细地分析这篇文章的主题和风格特点" → "分析文章主题和风格"</p>
                </div>
                
                <div class="tip-item">
                  <h4>成本估算</h4>
                  <p>粗略估计：1000个汉字约等于1500-2000个token，1000个英文单词约等于1300-1500个token</p>
                  <p>这有助于预估API调用成本和模型处理能力</p>
                </div>
                
                <div class="tip-item">
                  <h4>处理长文档</h4>
                  <p>将长文档分段处理，使用摘要或关键信息链接各个部分</p>
                  <p>使用最新的长上下文模型处理整个文档</p>
                </div>
              </div>
              
              <div class="highlight-box">
                <p><strong>AI使用者应该了解的token限制</strong></p>
                <ul>
                  <li><strong>理解不完整</strong>：超出上下文窗口的内容完全不可见，模型无法引用</li>
                  <li><strong>费用增加</strong>：更长的输入和输出意味着更高的API使用成本</li>
                  <li><strong>速度降低</strong>：token数量增加会影响模型响应速度</li>
                  <li><strong>截断风险</strong>：达到token上限时，模型可能会突然停止生成</li>
                </ul>
              </div>
              
              <p>了解token机制有助于设计更高效的AI系统架构，例如：</p>
              <ul>
                <li>实现有效的知识检索系统，将相关信息提供给AI而不浪费token</li>
                <li>设计分层对话管理系统，保留重要上下文而丢弃不重要细节</li>
                <li>建立更经济高效的AI应用，平衡性能与成本</li>
              </ul>
            </div>
          </div>
          
          <!-- 总结 -->
          <div class="conclusion-section">
            <h2>为什么理解Token很重要？</h2>
            <p>Token是大型语言模型的基础构建块，理解它们的工作方式可以：</p>
            <ul>
              <li><strong>更好地使用AI工具</strong>：优化提示词，提高交互效率</li>
              <li><strong>理解模型的局限性</strong>：知道什么时候模型可能表现不佳</li>
              <li><strong>优化资源使用</strong>：降低API使用成本，提高处理效率</li>
              <li><strong>深入了解AI原理</strong>：token是了解现代AI如何工作的第一步</li>
            </ul>
            <p>随着AI技术的不断发展，token化方法可能会进一步改进，但理解这一基本概念将持续帮助我们更有效地与AI系统交互。</p>
          </div>
        </div>
        <AiFooter />
      </div>
      
    </div>
  </template>
  
  <script setup lang="ts">
  import AiFooter from '../components/AiFooter.vue';
  // 组件逻辑
  </script>
  
  <style lang="scss" scoped>
  .learn-ai-tokens-page {
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: #333;
    line-height: 1.6;
    padding: 20px;
    background-color: #f9f9f9;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .title-section {
    text-align: center;
    margin-bottom: 40px;
    
    .page-title {
      font-size: 2.8rem;
      margin-bottom: 10px;
      font-weight: 700;
      background: linear-gradient(135deg, #2c3e50 0%, #4ca1af 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .subtitle {
      font-size: 1.4rem;
      color: #666;
      margin: 0;
    }
  }
  
  .intro-section {
    font-size: 1.1rem;
    max-width: 900px;
    margin: 0 auto 40px;
    text-align: center;
    
    p {
      margin-bottom: 1.5rem;
    }
  }
  
  .content-blocks {
    .content-block {
      margin-bottom: 60px;
      background-color: #fff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      
      .block-header {
        h2 {
          padding: 20px 30px;
          margin: 0;
          font-size: 1.8rem;
          background: linear-gradient(135deg, #f5f7fa 0%, #e8f3ff 100%);
          border-bottom: 1px solid #eaeaea;
        }
        
        .image-placeholder {
          padding: 20px;
          display: flex;
          justify-content: center;
          background-color: #f9f9f9;
          
          svg {
            max-width: 100%;
            height: auto;
          }
        }
      }
      
      .block-content {
        padding: 30px;
        
        p {
          margin-bottom: 1.2rem;
          font-size: 1.05rem;
        }
        
        .highlight-box {
          background-color: #f0f7ff;
          border-left: 4px solid #4e89e8;
          padding: 20px;
          margin: 25px 0;
          border-radius: 0 6px 6px 0;
          
          p {
            margin-top: 0;
          }
          
          ul {
            margin-bottom: 0;
            padding-left: 20px;
            
            li {
              margin-bottom: 8px;
              
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
        
        .step-box {
          margin: 30px 0;
          
          h3 {
            margin-bottom: 20px;
            font-size: 1.3rem;
          }
          
          .steps {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            
            .step {
              flex: 1;
              min-width: 200px;
              margin: 0 10px 20px;
              display: flex;
              
              .step-number {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background-color: #4e89e8;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                margin-right: 12px;
                flex-shrink: 0;
              }
              
              .step-content {
                h4 {
                  margin: 0 0 8px;
                  font-size: 1.1rem;
                }
                
                p {
                  margin: 0;
                  font-size: 0.95rem;
                  color: #666;
                }
              }
            }
          }
        }
        
        .language-comparison {
          margin: 25px 0;
          
          .comparison-item {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 6px;
            
            h3 {
              margin-top: 0;
              margin-bottom: 10px;
              font-size: 1.2rem;
            }
            
            p {
              margin: 8px 0;
              font-size: 1rem;
            }
          }
        }
        
        .context-evolution {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin: 20px 0;
          
          .evolution-item {
            flex: 1;
            min-width: 200px;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            
            h4 {
              margin-top: 0;
              margin-bottom: 10px;
              font-size: 1.1rem;
              color: #333;
            }
            
            p {
              margin: 8px 0;
              font-size: 0.95rem;
              color: #555;
            }
          }
        }
        
        .practical-applications {
          margin: 25px 0;
          
          h3 {
            margin-bottom: 20px;
            font-size: 1.3rem;
          }
          
          .tip-item {
            background-color: #f9f9f9;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 6px;
            border-left: 4px solid #4caf50;
            
            h4 {
              margin-top: 0;
              margin-bottom: 10px;
              font-size: 1.1rem;
              color: #2e7d32;
            }
            
            p {
              margin: 8px 0;
              font-size: 1rem;
              
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
  
  .conclusion-section {
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    
    h2 {
      color: #2c3e50;
      font-size: 1.8rem;
      margin-top: 0;
      margin-bottom: 20px;
    }
    
    p {
      font-size: 1.1rem;
      margin-bottom: 1.2rem;
    }
    
    ul {
      padding-left: 20px;
      
      li {
        margin-bottom: 10px;
        font-size: 1.05rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .title-section {
      .page-title {
        font-size: 2.2rem;
      }
      
      .subtitle {
        font-size: 1.2rem;
      }
    }
    
    .content-blocks {
      .content-block {
        .block-header {
          h2 {
            font-size: 1.5rem;
            padding: 15px 20px;
          }
        }
        
        .block-content {
          padding: 20px;
        }
      }
    }
    
    .step-box .steps {
      flex-direction: column;
      
      .step {
        margin-bottom: 15px;
      }
    }
  }
  </style> 