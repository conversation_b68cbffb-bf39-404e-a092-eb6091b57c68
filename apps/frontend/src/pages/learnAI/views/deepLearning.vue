<template>
  <div class="learn-ai-deep-learning-page">
    <div class="container">
      <div class="title-section">
        <h1 class="page-title">深度学习的三种学习方式</h1>
        <p class="subtitle">通俗易懂的解释与对比</p>
      </div>

      <div class="section intro-section">
        <p>深度学习是人工智能的一种，它模仿人类大脑的神经网络结构来学习和理解世界。以下是三种主要的学习方式：</p>
      </div>

      <div class="learning-types">
        <!-- 监督学习 -->
        <div class="learning-card">
          <div class="card-header">
            <h2>监督学习</h2>
            <div class="image-placeholder supervised-image">
              <!-- 这里会放置监督学习的SVG插图 -->
            </div>
          </div>
          <div class="card-content">
            <p><strong>就像老师教学生</strong> - 我们提供问题和正确答案。</p>
            <p>想象一个孩子在学习识别动物。你向他展示一张猫的图片并告诉他："这是猫"。展示狗的图片并说："这是狗"。通过大量的例子，孩子学会了识别猫和狗。</p>
            <p>监督学习也是如此工作的：</p>
            <ul>
              <li>我们提供<strong>输入数据</strong>（如图片）</li>
              <li>以及对应的<strong>正确标签</strong>（如"猫"或"狗"）</li>
              <li>模型学习将输入与正确输出关联起来</li>
              <li>学习完成后，它可以对新的、从未见过的数据进行预测</li>
            </ul>
            <p><strong>常见应用：</strong>图像识别、垃圾邮件过滤、语音识别、疾病诊断</p>
          </div>
        </div>

        <!-- 无监督学习 -->
        <div class="learning-card">
          <div class="card-header">
            <h2>无监督学习</h2>
            <div class="image-placeholder unsupervised-image">
              <!-- 这里会放置无监督学习的SVG插图 -->
            </div>
          </div>
          <div class="card-content">
            <p><strong>就像探险家发现模式</strong> - 没有老师，只有观察。</p>
            <p>想象你走进一个装满各种水果的房间，但你从来没见过水果，也不知道它们的名字。你会自然地根据颜色、形状和大小将它们分组 - 红色圆形的放一起，黄色长条的放一起。</p>
            <p>无监督学习就是这样工作的：</p>
            <ul>
              <li>只提供<strong>输入数据</strong>，没有标签或答案</li>
              <li>模型自己<strong>寻找数据中的结构和模式</strong></li>
              <li>它会发现相似性并进行分组或降维</li>
              <li>模型不知道这些组叫什么名字，它只知道它们是不同的</li>
            </ul>
            <p><strong>常见应用：</strong>客户分群、异常检测、推荐系统、数据压缩</p>
          </div>
        </div>

        <!-- 强化学习 -->
        <div class="learning-card">
          <div class="card-header">
            <h2>强化学习</h2>
            <div class="image-placeholder reinforcement-image">
              <!-- 这里会放置强化学习的SVG插图 -->
            </div>
          </div>
          <div class="card-content">
            <p><strong>就像训练宠物</strong> - 通过奖励和惩罚学习。</p>
            <p>想象你在训练一只狗。它做对了，你给它小零食；做错了，不给奖励。狗会慢慢学会哪些行为能获得奖励。</p>
            <p>强化学习也是这样工作的：</p>
            <ul>
              <li>模型（称为<strong>代理</strong>）在环境中采取行动</li>
              <li>每个行动都会得到<strong>奖励或惩罚</strong></li>
              <li>代理的目标是<strong>最大化长期总奖励</strong></li>
              <li>它通过尝试和错误来学习最佳策略</li>
            </ul>
            <p><strong>常见应用：</strong>游戏AI（如AlphaGo）、自动驾驶、机器人控制、资源管理</p>
          </div>
        </div>
      </div>

      <div class="comparison-section">
        <h2>三种学习方式的对比</h2>
        <div class="comparison-table">
          <div class="table-header">
            <div class="table-cell">特点</div>
            <div class="table-cell">监督学习</div>
            <div class="table-cell">无监督学习</div>
            <div class="table-cell">强化学习</div>
          </div>
          <div class="table-row">
            <div class="table-cell">数据需求</div>
            <div class="table-cell">标记数据（输入+正确答案）</div>
            <div class="table-cell">无标记数据（只有输入）</div>
            <div class="table-cell">环境反馈（奖励信号）</div>
          </div>
          <div class="table-row">
            <div class="table-cell">目标</div>
            <div class="table-cell">学习映射函数</div>
            <div class="table-cell">发现数据结构</div>
            <div class="table-cell">学习最佳策略</div>
          </div>
          <div class="table-row">
            <div class="table-cell">人类类比</div>
            <div class="table-cell">有老师指导的学习</div>
            <div class="table-cell">自我探索和发现</div>
            <div class="table-cell">通过尝试和错误学习</div>
          </div>
          <div class="table-row">
            <div class="table-cell">挑战</div>
            <div class="table-cell">需要大量标记数据</div>
            <div class="table-cell">结果难以评估</div>
            <div class="table-cell">训练不稳定、耗时</div>
          </div>
        </div>
      </div>

      <div class="real-world-section">
        <h2>现实世界中的应用</h2>
        <div class="applications">
          <div class="application-item">
            <h3>监督学习</h3>
            <p>智能手机上的人脸识别、医疗诊断、天气预报、语音转文字</p>
          </div>
          <div class="application-item">
            <h3>无监督学习</h3>
            <p>网购推荐系统、顾客分群、欺诈检测、新闻文章分类</p>
          </div>
          <div class="application-item">
            <h3>强化学习</h3>
            <p>下棋机器人、自动驾驶汽车、智能温控系统、工业自动化</p>
          </div>
        </div>
      </div>

      <div class="conclusion-section">
        <h2>总结</h2>
        <p>深度学习的三种学习方式各有优缺点，适用于不同场景：</p>
        <ul>
          <li><strong>监督学习</strong>：当你有明确的标记数据和目标时使用</li>
          <li><strong>无监督学习</strong>：当你想探索数据结构但没有标记时使用</li>
          <li><strong>强化学习</strong>：当你需要在复杂环境中学习决策时使用</li>
        </ul>
        <p>实际应用中，这三种学习方式常常结合使用，创造更强大的AI系统。</p>
      </div>
      <AiFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
import AiFooter from '../components/AiFooter.vue';
// 组件逻辑
</script>

<style lang="scss" scoped>
.learn-ai-deep-learning-page {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  line-height: 1.6;
  padding: 20px;
  background-color: #f9f9f9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.title-section {
  text-align: center;
  margin-bottom: 40px;
  
  .page-title {
    font-size: 2.8rem;
    margin-bottom: 10px;
    font-weight: 700;
  }
  
  .subtitle {
    font-size: 1.2rem;
    color: #666;
  }
}

.intro-section {
  font-size: 1.2rem;
  margin-bottom: 40px;
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.8;
}

.learning-types {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 50px;
  justify-content: center;
}

.learning-card {
  flex: 1;
  min-width: 300px;
  max-width: 380px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  }
}

.card-header {
  padding: 20px;
  
  h2 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    font-weight: 600;
  }
}

.image-placeholder {
  height: 180px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  margin-bottom: 10px;
}

/* SVG背景图案 */
.supervised-image {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 300' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f0f0f0;stroke:%23333;stroke-width:2%7D.st3%7Bfill:%23fff;stroke:%23333;stroke-width:2%7D.st4%7Bfill:none;stroke:%23666;stroke-width:1;stroke-dasharray:5,3%7D%3C/style%3E%3Cg transform='translate(0 10)'%3E%3Cpath d='M145 130l-10-10H88c-3 0-4 1-4 3v30c0 2 1 3 4 3h90c3 0 4-1 4-3v-20c0-2-1-3-4-3' class='st2'/%3E%3Ctext x='120' y='142' text-anchor='middle' class='st1'%3E输入数据%3C/text%3E%3Cpath d='M355 130l-10-10h-47c-3 0-4 1-4 3v30c0 2 1 3 4 3h90c3 0 4-1 4-3v-20c0-2-1-3-4-3' class='st2'/%3E%3Ctext x='330' y='142' text-anchor='middle' class='st1'%3E输出结果%3C/text%3E%3Cpath d='M255 80c-30 0-55 40-55 70s25 70 55 70 55-40 55-70-25-70-55-70z' class='st3'/%3E%3Ccircle cx='235' cy='130' r='8' class='st0'/%3E%3Ccircle cx='280' cy='130' r='8' class='st0'/%3E%3Cpath d='M240 160c5 5 25 5 30 0' class='st0'/%3E%3Ctext x='255' y='190' text-anchor='middle' class='st1'%3E模型%3C/text%3E%3Cpath d='M170 130h30m110 0h30' class='st0'/%3E%3Cpath d='M265 50c0-12-9-20-20-20h-90c-11 0-20 8-20 20 0 8 4 15 10 18' class='st4'/%3E%3Ctext x='205' y='40' text-anchor='middle' class='st1'%3E标记的数据集%3C/text%3E%3Cpath d='M288 45l-8-15-8 15' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.unsupervised-image {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 300' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f0f0f0;stroke:%23333;stroke-width:2%7D.st3%7Bfill:%23fff;stroke:%23333;stroke-width:2%7D.cl1%7Bfill:%23f9d5e5;stroke:%23333;stroke-width:1.5%7D.cl2%7Bfill:%23eeeeee;stroke:%23333;stroke-width:1.5%7D.cl3%7Bfill:%23d1e8ff;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 10)'%3E%3Cpath d='M255 80c-30 0-55 40-55 70s25 70 55 70 55-40 55-70-25-70-55-70z' class='st3'/%3E%3Ctext x='255' y='130' text-anchor='middle' class='st1'%3E无监督%3C/text%3E%3Ctext x='255' y='150' text-anchor='middle' class='st1'%3E学习模型%3C/text%3E%3Cpath d='M178 126v40m155-40v40' class='st0'/%3E%3Cg transform='translate(-45 -15)'%3E%3Ccircle cx='150' cy='90' r='12' class='cl1'/%3E%3Ccircle cx='120' cy='130' r='12' class='cl1'/%3E%3Ccircle cx='170' cy='120' r='12' class='cl1'/%3E%3Ccircle cx='130' cy='160' r='12' class='cl1'/%3E%3Ccircle cx='160' cy='155' r='12' class='cl1'/%3E%3C/g%3E%3Cg transform='translate(60 -15)'%3E%3Ccircle cx='350' cy='90' r='12' class='cl1'/%3E%3Ccircle cx='390' cy='100' r='12' class='cl3'/%3E%3Ccircle cx='370' cy='120' r='12' class='cl1'/%3E%3Ccircle cx='410' cy='130' r='12' class='cl3'/%3E%3Ccircle cx='385' cy='155' r='12' class='cl3'/%3E%3Ccircle cx='350' cy='145' r='12' class='cl1'/%3E%3Ccircle cx='350' cy='120' r='12' class='cl2'/%3E%3Ccircle cx='390' cy='160' r='12' class='cl2'/%3E%3Ccircle cx='350' cy='170' r='12' class='cl2'/%3E%3C/g%3E%3Cpath d='M340 90c20-15 40-15 55 0M340 80c25-20 50-20 70 0' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.reinforcement-image {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 300' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f0f0f0;stroke:%23333;stroke-width:2%7D.st3%7Bfill:%23fff;stroke:%23333;stroke-width:2%7D%3C/style%3E%3Cg transform='translate(0 10)'%3E%3Cpath d='M250 50c-75 0-135 60-135 135 0 25 8 50 20 70' class='st0'/%3E%3Cpath d='M178 220h144c16 0 30-15 30-34 0-19-14-34-30-34h-34c-11 0-20-10-20-23s9-23 20-23h85' class='st0'/%3E%3Ccircle cx='250' cy='100' r='30' class='st3'/%3E%3Ctext x='250' y='105' text-anchor='middle' class='st1'%3E代理%3C/text%3E%3Crect x='330' y='160' width='80' height='60' rx='5' ry='5' class='st2'/%3E%3Ctext x='370' y='190' text-anchor='middle' class='st1'%3E环境%3C/text%3E%3Cpath d='M250 130l-10 15h20z' class='st0'/%3E%3Cpath d='M250 145v25m-35 15l20-20m50 20l-20-20' class='st0'/%3E%3Cpath d='M150 185c-15 0-25 20-5 30 20 10 5 30-10 30' class='st0'/%3E%3Ctext x='133' y='215' text-anchor='middle' class='st1'%3E奖励%3C/text%3E%3Cpath d='M350 150l15-15m0 15l-15-15' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.card-content {
  padding: 0 20px 20px;
  
  p, ul {
    margin-bottom: 15px;
  }

  ul {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      position: relative;
      
      &:before {
        content: "•";
        position: absolute;
        left: -15px;
        color: #666;
      }
    }
  }
}

.comparison-section {
  margin-bottom: 50px;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-align: center;
  }
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
}

.table-header {
  display: flex;
  background-color: #f2f2f2;
  
  .table-cell {
    font-weight: 600;
    background-color: #eee;
  }
}

.table-row {
  display: flex;
  border-bottom: 1px solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
}

.table-cell {
  flex: 1;
  padding: 15px;
  border-right: 1px solid #eee;
  
  &:first-child {
    font-weight: 600;
    background-color: #f9f9f9;
  }
  
  &:last-child {
    border-right: none;
  }
}

.real-world-section {
  margin-bottom: 50px;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    text-align: center;
  }
}

.applications {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

.application-item {
  flex: 1;
  min-width: 250px;
  max-width: 350px;
  background-color: #fff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
  
  h3 {
    font-size: 1.4rem;
    margin-bottom: 10px;
    color: #444;
  }
  
  p {
    color: #666;
    line-height: 1.5;
  }
}

.conclusion-section {
  max-width: 800px;
  margin: 0 auto;
  background-color: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
  }
  
  ul {
    padding-left: 20px;
    margin-bottom: 20px;
    
    li {
      margin-bottom: 10px;
      position: relative;
      
      &:before {
        content: "•";
        position: absolute;
        left: -15px;
        color: #666;
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .learning-card {
    max-width: 100%;
  }
  
  .table-header, .table-row {
    flex-direction: column;
  }
  
  .table-cell {
    border-right: none;
    border-bottom: 1px solid #eee;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .title-section .page-title {
    font-size: 2.2rem;
  }
}
</style>