<template>
  <div class="mcp-container">
    <header class="header">
      <h1 class="title">MCP: 模型上下文协议</h1>
      <p class="subtitle">让AI与世界对话的桥梁</p>
    </header>

    <section class="section">
      <h2 class="section-title">什么是MCP？</h2>
      <div class="content-block">
        <div class="text-block">
          <p>模型上下文协议（Model Context Protocol，MCP）是一种开放标准，它使AI模型能够访问外部信息和工具。简单来说：</p>
          <div class="illustration">
            <div class="illustration-text">
              <p>没有MCP的AI像是一个被<strong>困在盒子里的智者</strong>—只能依靠自己已有的知识</p>
              <p>有了MCP的AI则像是一个<strong>可以查阅图书馆和使用工具的助手</strong>—能够获取最新信息并采取行动</p>
            </div>
          </div>
        </div>
        <div class="image-block">
          <div class="image-placeholder">
            <svg class="illustration-svg" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
              <!-- AI在盒子里 vs AI与世界连接 -->
              <rect x="50" y="70" width="80" height="80" rx="5" fill="none" stroke="#333" stroke-width="2"/>
              <circle cx="90" cy="110" r="25" fill="none" stroke="#333" stroke-width="2"/>
              <text x="90" y="115" text-anchor="middle" font-size="12">AI</text>
              <text x="90" y="170" text-anchor="middle" font-size="10">没有MCP</text>
              
              <rect x="170" y="70" width="80" height="80" rx="5" fill="none" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>
              <circle cx="210" cy="110" r="25" fill="none" stroke="#333" stroke-width="2"/>
              <text x="210" y="115" text-anchor="middle" font-size="12">AI</text>
              
              <rect x="260" y="70" width="20" height="20" fill="none" stroke="#333" stroke-width="2"/>
              <rect x="260" y="100" width="20" height="20" fill="none" stroke="#333" stroke-width="2"/>
              <rect x="260" y="130" width="20" height="20" fill="none" stroke="#333" stroke-width="2"/>
              
              <line x1="235" y1="110" x2="260" y2="110" stroke="#333" stroke-width="1"/>
              <line x1="235" y1="110" x2="260" y2="80" stroke="#333" stroke-width="1"/>
              <line x1="235" y1="110" x2="260" y2="140" stroke="#333" stroke-width="1"/>
              
              <text x="210" y="170" text-anchor="middle" font-size="10">有MCP</text>
            </svg>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <h2 class="section-title">为什么MCP很重要？</h2>
      <div class="content-block">
        <div class="problems">
          <h3 class="sub-title">传统AI的三大局限</h3>
          <div class="problem">
            <div class="problem-icon">📚</div>
            <div class="problem-text">
              <h4>知识截止日期</h4>
              <p>AI只知道训练时的信息，无法获取最新知识</p>
            </div>
          </div>
          <div class="problem">
            <div class="problem-icon">💭</div>
            <div class="problem-text">
              <h4>"幻觉"问题</h4>
              <p>当不确定时，AI可能会编造看似合理但实际错误的信息</p>
            </div>
          </div>
          <div class="problem">
            <div class="problem-icon">🔒</div>
            <div class="problem-text">
              <h4>封闭系统</h4>
              <p>无法访问用户特定数据或执行实际操作</p>
            </div>
          </div>
        </div>
        
        <div class="solutions">
          <h3 class="sub-title">MCP如何解决这些问题</h3>
          <div class="solution">
            <div class="solution-icon">🔄</div>
            <div class="solution-text">
              <h4>实时信息</h4>
              <p>可以查询最新数据，永不过时</p>
            </div>
          </div>
          <div class="solution">
            <div class="solution-icon">✓</div>
            <div class="solution-text">
              <h4>事实依据</h4>
              <p>依靠可靠来源回答问题，减少"编造"信息</p>
            </div>
          </div>
          <div class="solution">
            <div class="solution-icon">🔓</div>
            <div class="solution-text">
              <h4>开放互动</h4>
              <p>可以访问特定数据并执行实际操作</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <h2 class="section-title">MCP是如何工作的？</h2>
      <div class="workflow">
        <div class="workflow-illustration">
          <svg class="workflow-svg" viewBox="0 0 600 200" xmlns="http://www.w3.org/2000/svg">
            <!-- 用户 -->
            <circle cx="100" cy="100" r="30" fill="none" stroke="#333" stroke-width="2"/>
            <text x="100" y="105" text-anchor="middle" font-size="12">用户</text>
            
            <!-- AI模型 -->
            <circle cx="300" cy="100" r="40" fill="none" stroke="#333" stroke-width="2"/>
            <text x="300" y="105" text-anchor="middle" font-size="12">AI模型</text>
            
            <!-- MCP服务器 -->
            <rect x="430" y="70" width="80" height="60" rx="5" fill="none" stroke="#333" stroke-width="2"/>
            <text x="470" y="105" text-anchor="middle" font-size="12">MCP服务器</text>
            
            <!-- 数据源 -->
            <rect x="500" y="40" width="20" height="30" fill="none" stroke="#333" stroke-width="1"/>
            <rect x="530" y="40" width="20" height="30" fill="none" stroke="#333" stroke-width="1"/>
            <rect x="500" y="130" width="20" height="30" fill="none" stroke="#333" stroke-width="1"/>
            <rect x="530" y="130" width="20" height="30" fill="none" stroke="#333" stroke-width="1"/>
            
            <!-- 交互箭头 -->
            <line x1="130" y1="100" x2="260" y2="100" stroke="#333" stroke-width="1"/>
            <polygon points="255,95 260,100 255,105" fill="#333"/>
            <text x="195" y="90" text-anchor="middle" font-size="10">1. 提问</text>
            
            <line x1="340" y1="90" x2="430" y2="90" stroke="#333" stroke-width="1"/>
            <polygon points="425,85 430,90 425,95" fill="#333"/>
            <text x="385" y="80" text-anchor="middle" font-size="10">2. 请求信息</text>
            
            <line x1="490" y1="70" x2="510" y2="40" stroke="#333" stroke-width="1"/>
            <line x1="490" y1="70" x2="540" y2="40" stroke="#333" stroke-width="1"/>
            <text x="520" y="30" text-anchor="middle" font-size="10">3. 查询数据</text>
            
            <line x1="430" y1="110" x2="340" y2="110" stroke="#333" stroke-width="1"/>
            <polygon points="345,115 340,110 345,105" fill="#333"/>
            <text x="385" y="130" text-anchor="middle" font-size="10">4. 返回信息</text>
            
            <line x1="260" y1="100" x2="130" y2="100" stroke="#333" stroke-width="1" stroke-dasharray="5,5"/>
            <polygon points="135,105 130,100 135,95" fill="#333"/>
            <text x="195" y="120" text-anchor="middle" font-size="10">5. 生成回答</text>
          </svg>
        </div>
        <div class="workflow-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-text">
              <h4>用户提问</h4>
              <p>用户向AI提出问题或请求</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-text">
              <h4>AI评估需求</h4>
              <p>AI识别需要额外信息或需要执行操作</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-text">
              <h4>MCP请求</h4>
              <p>AI通过MCP向外部服务器请求信息或操作</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-text">
              <h4>服务器响应</h4>
              <p>MCP服务器处理请求并返回结果</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">5</div>
            <div class="step-text">
              <h4>生成回答</h4>
              <p>AI整合信息生成更精确的回答</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <h2 class="section-title">MCP的实际应用</h2>
      <div class="applications">
        <div class="application-card">
          <div class="application-icon">📊</div>
          <h3>数据分析师</h3>
          <p>AI可以查询公司最新数据库，生成实时报告和图表</p>
        </div>
        <div class="application-card">
          <div class="application-icon">📝</div>
          <h3>内容创作</h3>
          <p>AI可以访问最新事实和数据，创建准确的文章和报告</p>
        </div>
        <div class="application-card">
          <div class="application-icon">💼</div>
          <h3>客户服务</h3>
          <p>AI可以查询客户历史、产品信息和政策，提供个性化支持</p>
        </div>
        <div class="application-card">
          <div class="application-icon">👨‍💻</div>
          <h3>开发助手</h3>
          <p>AI可以访问代码库、文档和API，提供精确的编程帮助</p>
        </div>
      </div>
    </section>

    <section class="section">
      <h2 class="section-title">MCP与自主AI代理</h2>
      <div class="content-block">
        <div class="text-block">
          <p>MCP是构建自主AI代理的基础设施，它使AI能够：</p>
          <ul class="feature-list">
            <li><strong>连接多个系统</strong>：跨不同平台和工具进行无缝工作</li>
            <li><strong>访问专业知识</strong>：从特定领域数据库获取深度知识</li>
            <li><strong>执行复杂流程</strong>：完成需要多个步骤的任务</li>
            <li><strong>持续学习</strong>：通过获取最新信息不断更新知识</li>
          </ul>
        </div>
        <div class="agent-diagram">
          <svg class="agent-svg" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
            <!-- 中心AI代理 -->
            <circle cx="150" cy="100" r="40" fill="none" stroke="#333" stroke-width="2"/>
            <text x="150" y="105" text-anchor="middle" font-size="14">AI代理</text>
            
            <!-- 连接的系统 -->
            <rect x="50" y="30" width="60" height="30" rx="5" fill="none" stroke="#333" stroke-width="2"/>
            <text x="80" y="50" text-anchor="middle" font-size="10">数据库</text>
            
            <rect x="50" y="140" width="60" height="30" rx="5" fill="none" stroke="#333" stroke-width="2"/>
            <text x="80" y="160" text-anchor="middle" font-size="10">知识库</text>
            
            <rect x="230" y="30" width="60" height="30" rx="5" fill="none" stroke="#333" stroke-width="2"/>
            <text x="260" y="50" text-anchor="middle" font-size="10">APIs</text>
            
            <rect x="230" y="140" width="60" height="30" rx="5" fill="none" stroke="#333" stroke-width="2"/>
            <text x="260" y="160" text-anchor="middle" font-size="10">工具</text>
            
            <!-- 连接线 -->
            <line x1="150" y1="60" x2="80" y2="30" stroke="#333" stroke-width="1"/>
            <line x1="150" y1="140" x2="80" y2="170" stroke="#333" stroke-width="1"/>
            <line x1="190" y1="90" x2="260" y2="60" stroke="#333" stroke-width="1"/>
            <line x1="190" y1="110" x2="260" y2="140" stroke="#333" stroke-width="1"/>
            
            <!-- MCP标签 -->
            <text x="110" y="45" text-anchor="middle" font-size="8" transform="rotate(-30, 110, 45)">MCP</text>
            <text x="110" y="155" text-anchor="middle" font-size="8" transform="rotate(30, 110, 155)">MCP</text>
            <text x="230" y="75" text-anchor="middle" font-size="8" transform="rotate(30, 230, 75)">MCP</text>
            <text x="230" y="125" text-anchor="middle" font-size="8" transform="rotate(-30, 230, 125)">MCP</text>
          </svg>
        </div>
      </div>
    </section>

    <section class="section conclusion">
      <h2 class="section-title">未来展望</h2>
      <p>MCP代表了AI发展的新方向——从封闭的模型向开放的、与世界互动的智能系统转变。随着MCP标准的完善和普及，我们将看到：</p>
      <ul class="future-list">
        <li>更精确、更有用的AI应用</li>
        <li>AI能够执行更复杂的任务流程</li>
        <li>更强的个性化和定制化能力</li>
        <li>AI与现有系统的无缝集成</li>
      </ul>
      <p class="final-thought">MCP不仅仅是一项技术，它是AI从"会说话"到"能行动"的关键一步。</p>
    </section>
    
    <AiFooter />
  </div>
</template>

<script setup lang="ts">
import AiFooter from '../components/AiFooter.vue';
// RAG页面组件逻辑
</script>

<style scoped>
.mcp-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.title {
  font-size: 2.8rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 1.4rem;
  color: #666;
  font-weight: normal;
}

.section {
  margin-bottom: 60px;
}

.section-title {
  font-size: 2rem;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 30px;
}

.content-block {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 30px;
}

.text-block {
  flex: 1;
  min-width: 300px;
}

.image-block {
  flex: 1;
  min-width: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.illustration-svg,
.workflow-svg,
.agent-svg {
  width: 100%;
  height: 100%;
}

.illustration {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin: 20px 0;
}

.illustration-text p {
  margin: 10px 0;
  line-height: 1.4;
}

.problems, .solutions {
  flex: 1;
  min-width: 300px;
}

.sub-title {
  font-size: 1.4rem;
  margin-bottom: 20px;
  color: #444;
}

.problem, .solution, .step {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.problem-icon, .solution-icon {
  font-size: 2rem;
  margin-right: 15px;
  min-width: 40px;
  text-align: center;
}

.problem-text, .solution-text, .step-text {
  flex: 1;
}

.problem-text h4, .solution-text h4, .step-text h4 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
}

.workflow {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.workflow-illustration {
  height: 250px;
}

.workflow-steps {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-between;
}

.step {
  flex: 1;
  min-width: 150px;
}

.step-number {
  background-color: #333;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  font-weight: bold;
}

.applications {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-between;
}

.application-card {
  flex: 1;
  min-width: 200px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.application-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.application-card h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
}

.feature-list, .future-list {
  padding-left: 20px;
}

.feature-list li, .future-list li {
  margin-bottom: 10px;
}

.agent-diagram {
  flex: 1;
  min-width: 300px;
  height: 250px;
}

.conclusion {
  background-color: #f9f9f9;
  padding: 30px;
  border-radius: 5px;
}

.final-thought {
  font-size: 1.2rem;
  font-style: italic;
  margin-top: 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .content-block, .problems, .solutions, .workflow, .applications {
    flex-direction: column;
  }
  
  .workflow-steps {
    flex-direction: column;
  }
  
  .title {
    font-size: 2.2rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
}
</style>
