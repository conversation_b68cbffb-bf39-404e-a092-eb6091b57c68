<template>
  <div class="learn-ai-training-process-page">
    <div class="container">
      <div class="title-section">
        <h1 class="page-title">AI大模型：完整训练过程</h1>
      </div>

      <div class="section intro-section">
        <p>大模型的训练是一项复杂而精细的工程，涉及数据、算法、硬件和工程化等多个层面的挑战。让我们一起深入了解AI大模型从无到有的完整训练过程。</p>
      </div>

      <!-- 训练过程概览 -->
      <div class="content-section">
        <div class="section-header">
          <h2>训练过程概览</h2>
        </div>
        <div class="section-content">
          <div class="image-section">
            <div class="hand-drawn-image training-overview">
              <!-- 训练过程概览的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>训练一个AI大模型就像建造一座精密的高楼，需要经过多个环环相扣的阶段，每个阶段都至关重要：</p>
            
            <ol>
              <li><strong>数据收集与预处理</strong>：为模型准备"学习材料"，就像为学生准备教科书</li>
              <li><strong>模型架构设计</strong>：确定模型的"大脑结构"，决定它如何学习和思考</li>
              <li><strong>预训练阶段</strong>：让模型通过海量数据获取基础知识，类似于基础教育</li>
              <li><strong>微调阶段</strong>：针对特定任务进行专业训练，就像专业技能培训</li>
              <li><strong>评估与优化</strong>：测试模型能力并不断改进，如考试与复习</li>
              <li><strong>部署与应用</strong>：将训练好的模型投入实际使用，开始"工作"</li>
            </ol>
            
            <p>这个过程需要数百名工程师和研究人员的协作，消耗巨大的计算资源，有时甚至需要数月时间才能完成一个大模型的训练。</p>
          </div>
        </div>
      </div>

      <!-- 数据收集与预处理 -->
      <div class="content-section">
        <div class="section-header">
          <h2>第一阶段：数据收集与预处理</h2>
        </div>
        <div class="section-content reverse">
          <div class="image-section">
            <div class="hand-drawn-image data-preparation">
              <!-- 数据收集与预处理的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>数据是大模型的"食粮"，决定了模型能学到什么样的知识和能力。这个阶段包括：</p>
            
            <ul>
              <li><strong>大规模数据收集</strong>：从互联网、书籍、论文等来源获取海量文本、图像等数据</li>
              <li><strong>数据清洗</strong>：去除低质量、重复、有害或敏感内容，就像筛选食材</li>
              <li><strong>数据标准化</strong>：将不同来源、不同格式的数据转换为统一格式</li>
              <li><strong>数据增强</strong>：有时需要通过技术手段扩充数据量，提高多样性</li>
              <li><strong>数据分片</strong>：将庞大的数据集分割成小批次，便于训练处理</li>
            </ul>
            
            <p>对于当代大模型，训练数据集通常包含数十亿甚至数万亿个词汇，总量可达数TB或更多。例如，GPT-3的训练数据就包含了约45TB的文本数据，相当于数百万本书的内容。</p>
            
            <p>数据质量直接影响模型表现——"垃圾进，垃圾出"这一原则在AI训练中尤为重要。因此，工程师们会投入大量精力确保数据的质量、多样性和代表性。</p>
          </div>
        </div>
      </div>

      <!-- 模型架构设计 -->
      <div class="content-section">
        <div class="section-header">
          <h2>第二阶段：模型架构设计</h2>
        </div>
        <div class="section-content">
          <div class="image-section">
            <div class="hand-drawn-image model-architecture">
              <!-- 模型架构设计的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>模型架构是AI的"骨架和神经系统"，决定了它如何处理信息、学习模式和形成理解。</p>
            
            <ul>
              <li><strong>选择基础架构</strong>：当前大多数大模型基于Transformer架构，依靠"注意力机制"来理解上下文关系</li>
              <li><strong>确定模型规模</strong>：包括层数、每层神经元数量、注意力头数等，这些因素共同决定了模型的参数量</li>
              <li><strong>设计学习目标</strong>：例如，语言模型常用"预测下一个词"作为学习目标</li>
              <li><strong>优化算法选择</strong>：决定模型如何从错误中学习和调整</li>
              <li><strong>特殊功能设计</strong>：如多模态能力、长文本处理等特性的架构支持</li>
            </ul>
            
            <p>架构设计需要平衡多种因素：计算效率、内存需求、表达能力和可扩展性。好的架构能够让模型用最少的计算资源达到最佳性能。</p>
            
            <p>大模型的突破往往源于架构创新，如2017年Transformer的出现彻底改变了NLP领域。随着研究推进，我们看到更多创新出现，如MoE（专家混合）、Flash Attention等，不断提升模型效率。</p>
          </div>
        </div>
      </div>

      <!-- 预训练阶段 -->
      <div class="content-section">
        <div class="section-header">
          <h2>第三阶段：预训练阶段</h2>
        </div>
        <div class="section-content reverse">
          <div class="image-section">
            <div class="hand-drawn-image pre-training">
              <!-- 预训练阶段的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>预训练是大模型获取通用知识和能力的阶段，也是最耗费计算资源的环节：</p>
            
            <ul>
              <li><strong>自监督学习</strong>：模型通过预测被遮盖的文本或下一个词汇来学习语言规律，无需人工标注</li>
              <li><strong>分布式训练</strong>：将训练任务分配到数百甚至数千个GPU或TPU上并行计算</li>
              <li><strong>梯度累积</strong>：处理超大批次数据时，通过累积多个小批次的梯度来模拟大批次训练</li>
              <li><strong>混合精度训练</strong>：结合不同数值精度，平衡计算速度和精度</li>
              <li><strong>检查点保存</strong>：定期保存训练状态，防止意外中断导致进度丢失</li>
            </ul>
            
            <p>预训练阶段的数据通常是无标签的通用文本，模型通过自我学习掌握语言知识。这个过程可能持续数周甚至数月，消耗数百万计算小时。例如，GPT-3的训练耗费了约3640个GPU年的计算资源，相当于3640个GPU连续工作一年。</p>
            
            <p>随着训练进行，模型会经历多个阶段：最初学习基本语法和词汇，然后逐渐掌握复杂概念、常识推理，最终形成系统化的世界知识网络。这个过程就像人类从婴儿到成年的认知发展过程。</p>
          </div>
        </div>
      </div>

      <!-- 微调阶段 -->
      <div class="content-section">
        <div class="section-header">
          <h2>第四阶段：微调阶段</h2>
        </div>
        <div class="section-content">
          <div class="image-section">
            <div class="hand-drawn-image fine-tuning">
              <!-- 微调阶段的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>微调是让通用大模型适应特定任务或领域的过程，就像专业教育让通才成为专才：</p>
            
            <ul>
              <li><strong>监督微调(SFT)</strong>：使用高质量人工标注数据，训练模型生成理想输出</li>
              <li><strong>人类反馈的强化学习(RLHF)</strong>：让模型根据人类偏好调整行为，提升有用性、安全性和诚实度</li>
              <li><strong>指令微调</strong>：训练模型理解并遵循各种自然语言指令</li>
              <li><strong>领域适应</strong>：在特定领域数据上微调，提升专业能力，如医疗、法律等</li>
              <li><strong>低秩适应(LoRA)</strong>：只调整部分参数，大幅降低计算资源需求，同时保持效果</li>
            </ul>
            
            <p>微调阶段使用的数据量远小于预训练，但质量要求更高，通常需要专业人员精心准备或标注。与预训练相比，微调的计算成本低得多，可能只需要几天或几周。</p>
            
            <p>微调过程中，需要注意避免"灾难性遗忘"问题——模型可能在学习新知识的同时忘记之前学到的内容。解决这一问题的方法包括正则化技术、渐进式学习等。</p>
            
            <p>最新的大模型研发趋势是进行多轮、多阶段的微调，如先进行SFT，再进行RLHF，最后进行偏好校准，逐步提升模型性能和对齐程度。</p>
          </div>
        </div>
      </div>

      <!-- 评估与优化 -->
      <div class="content-section">
        <div class="section-header">
          <h2>第五阶段：评估与优化</h2>
        </div>
        <div class="section-content reverse">
          <div class="image-section">
            <div class="hand-drawn-image evaluation">
              <!-- 评估与优化的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>训练完成后，需要全面评估模型性能并进行持续优化：</p>
            
            <ul>
              <li><strong>能力评估</strong>：在各种任务上测试模型，如问答、摘要、推理、代码生成等</li>
              <li><strong>标准基准测试</strong>：使用MMLU、HumanEval等业界认可的测试集进行客观评估</li>
              <li><strong>安全评估</strong>：测试模型对有害、误导性或敏感指令的响应</li>
              <li><strong>偏见与公平性分析</strong>：检测模型是否存在性别、种族或其他形式的偏见</li>
              <li><strong>人工评估</strong>：让人类评估者对模型回答进行主观评价</li>
              <li><strong>红队测试</strong>：专业团队尝试"攻击"模型，找出安全漏洞</li>
            </ul>
            
            <p>评估结果会指导进一步优化，可能需要：</p>
            
            <ul>
              <li><strong>模型蒸馏</strong>：将大模型知识转移到更小模型中，提高效率</li>
              <li><strong>量化压缩</strong>：降低参数精度，减小模型体积，加速推理</li>
              <li><strong>选择性更新</strong>：只优化表现不佳的能力，保留已有优势</li>
              <li><strong>数据增强</strong>：为弱项能力补充训练数据</li>
            </ul>
            
            <p>评估与优化是一个持续循环的过程，随着模型部署和使用，会不断收集用户反馈，进一步完善模型能力。</p>
          </div>
        </div>
      </div>

      <!-- 部署与应用 -->
      <div class="content-section">
        <div class="section-header">
          <h2>第六阶段：部署与应用</h2>
        </div>
        <div class="section-content">
          <div class="image-section">
            <div class="hand-drawn-image deployment">
              <!-- 部署与应用的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>模型训练完成后，需要部署到生产环境中才能发挥实际价值：</p>
            
            <ul>
              <li><strong>模型服务化</strong>：将模型封装为API服务，便于应用调用</li>
              <li><strong>推理优化</strong>：使用KV缓存、批处理等技术提高响应速度</li>
              <li><strong>硬件适配</strong>：针对不同硬件(GPU、CPU、专用芯片)优化代码</li>
              <li><strong>扩展性设计</strong>：构建能处理变化流量的弹性架构</li>
              <li><strong>监控与日志</strong>：实时跟踪模型性能、资源使用和异常情况</li>
              <li><strong>版本控制</strong>：管理不同版本模型，支持平滑升级和回滚</li>
            </ul>
            
            <p>大模型的部署面临多方面挑战：</p>
            
            <ul>
              <li><strong>推理延迟</strong>：如何在保证质量的同时提供快速响应</li>
              <li><strong>成本控制</strong>：大模型推理成本高昂，需要精细优化</li>
              <li><strong>隐私保护</strong>：确保用户数据安全，防止信息泄露</li>
              <li><strong>长期运维</strong>：持续监控模型表现，处理异常情况</li>
            </ul>
            
            <p>大模型上线后，还需建立用户反馈收集机制，了解实际使用体验，为后续迭代提供依据。这形成了一个完整闭环：用户反馈→数据收集→模型更新→再部署。</p>
          </div>
        </div>
      </div>

      <!-- 训练挑战与解决方案 -->
      <div class="content-section">
        <div class="section-header">
          <h2>训练挑战与解决方案</h2>
        </div>
        <div class="challenge-cards">
          <div class="challenge-card">
            <div class="card-icon computing-icon"></div>
            <h3>计算资源挑战</h3>
            <p><strong>挑战</strong>：训练大模型需要数千GPU，成本可达数百万美元</p>
            <p><strong>解决方案</strong>：</p>
            <ul>
              <li>分布式训练架构，跨数千设备并行计算</li>
              <li>混合精度训练，平衡精度和速度</li>
              <li>梯度累积和梯度检查点，优化内存使用</li>
              <li>模型并行和数据并行相结合的训练策略</li>
            </ul>
          </div>
          
          <div class="challenge-card">
            <div class="card-icon data-icon"></div>
            <h3>数据质量挑战</h3>
            <p><strong>挑战</strong>：获取足够多、高质量、多样化且无偏见的数据</p>
            <p><strong>解决方案</strong>：</p>
            <ul>
              <li>多源数据融合，确保多样性</li>
              <li>自动化与人工相结合的数据筛选流程</li>
              <li>数据去重和去噪技术</li>
              <li>合成数据生成，弥补稀缺样本</li>
              <li>公平性评估与偏见缓解算法</li>
            </ul>
          </div>
          
          <div class="challenge-card">
            <div class="card-icon convergence-icon"></div>
            <h3>训练稳定性挑战</h3>
            <p><strong>挑战</strong>：大规模训练容易不稳定，出现梯度爆炸或消失</p>
            <p><strong>解决方案</strong>：</p>
            <ul>
              <li>梯度裁剪，防止异常梯度</li>
              <li>学习率预热与衰减策略</li>
              <li>层标准化和残差连接稳定训练</li>
              <li>正则化技术减少过拟合</li>
              <li>优化器选择与调优</li>
            </ul>
          </div>
          
          <div class="challenge-card">
            <div class="card-icon engineering-icon"></div>
            <h3>工程化挑战</h3>
            <p><strong>挑战</strong>：管理大规模训练任务，处理故障和中断</p>
            <p><strong>解决方案</strong>：</p>
            <ul>
              <li>自动化训练流水线</li>
              <li>分布式任务调度系统</li>
              <li>故障检测与恢复机制</li>
              <li>训练过程可视化监控</li>
              <li>DevOps实践应用于AI训练</li>
            </ul>
          </div>
          
          <div class="challenge-card">
            <div class="card-icon alignment-icon"></div>
            <h3>人类对齐挑战</h3>
            <p><strong>挑战</strong>：让模型输出符合人类偏好、价值观和意图</p>
            <p><strong>解决方案</strong>：</p>
            <ul>
              <li>指令微调和上下文学习</li>
              <li>基于人类反馈的强化学习(RLHF)</li>
              <li>对抗训练减少有害输出</li>
              <li>价值观和安全机制嵌入</li>
              <li>多样化评估者参与反馈</li>
            </ul>
          </div>
          
          <div class="challenge-card">
            <div class="card-icon evaluation-icon"></div>
            <h3>评估难题</h3>
            <p><strong>挑战</strong>：全面评估模型能力，客观比较不同模型</p>
            <p><strong>解决方案</strong>：</p>
            <ul>
              <li>多维度评估指标体系</li>
              <li>标准化基准测试套件</li>
              <li>人机结合的评估流程</li>
              <li>持续评估与在线监控</li>
              <li>开放透明的评估报告</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 结论 -->
      <div class="content-section conclusion-section">
        <div class="conclusion-card">
          <h2>结语：大模型训练的艺术与科学</h2>
          <p>大模型训练是一个融合了数据科学、分布式计算、神经网络理论和软件工程的复杂过程。这不仅是一项技术挑战，也是一门艺术，需要研究者和工程师不断调整、优化和创新。</p>
          
          <p>随着技术进步，大模型训练也在不断演化：从更高效的算法到更强大的硬件，从更智能的数据处理到更精细的人类对齐技术。未来，我们可能会看到训练过程变得更加高效、更加节能、更加智能化。</p>
          
          <p>无论技术如何变化，大模型训练的核心仍然是让机器理解和生成人类知识的过程。通过精心设计的训练流程，我们将人类几千年的智慧结晶浓缩到数字形式，创造出能够理解、思考和创造的人工智能系统。这既是科技的壮举，也是人类智慧的延伸和放大。</p>
        </div>
      </div>

      <AiFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
import AiFooter from '../components/AiFooter.vue';
// 组件逻辑
</script>

<style lang="scss" scoped>
.learn-ai-training-process-page {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  line-height: 1.6;
  padding: 20px;
  background-color: #f9f9f9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.title-section {
  text-align: center;
  margin-bottom: 40px;
  
  .page-title {
    font-size: 2.8rem;
    margin-bottom: 10px;
    font-weight: 700;
  }
}

.intro-section {
  font-size: 1.2rem;
  margin-bottom: 40px;
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.8;
}

.content-section {
  margin-bottom: 60px;
  
  .section-header {
    margin-bottom: 30px;
    
    h2 {
      font-size: 2rem;
      font-weight: 600;
      position: relative;
      display: inline-block;
      
      &:after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: #5c6ac4;
        border-radius: 3px;
      }
    }
  }
  
  .section-content {
    display: flex;
    flex-direction: column-reverse;
    gap: 30px;
    
    @media (min-width: 768px) {
      flex-direction: row;
      align-items: center;
      
      &.reverse {
        flex-direction: row-reverse;
      }
    }
  }
  
  .text-section {
    flex: 1;
    
    p, ul, ol {
      margin-bottom: 20px;
      font-size: 1.1rem;
    }
    
    ul, ol {
      padding-left: 20px;
      
      li {
        margin-bottom: 10px;
      }
    }
  }
  
  .image-section {
    flex: 1;
    display: flex;
    justify-content: center;
  }
  
  .hand-drawn-image {
    width: 100%;
    max-width: 450px;
    height: 300px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
  }
}

.challenge-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.challenge-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
  }
  
  h3 {
    font-size: 1.4rem;
    margin: 15px 0;
    font-weight: 600;
  }
  
  p {
    margin-bottom: 10px;
  }
  
  ul {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      font-size: 0.95rem;
    }
  }
}

.card-icon {
  width: 60px;
  height: 60px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.conclusion-section {
  .conclusion-card {
    background-color: #fff;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    
    h2 {
      font-size: 1.8rem;
      margin-bottom: 25px;
      position: relative;
      display: inline-block;
      
      &:after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80%;
        height: 3px;
        background-color: #5c6ac4;
        border-radius: 3px;
      }
    }
    
    p {
      font-size: 1.1rem;
      line-height: 1.8;
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

/* SVG手绘图 */
.training-overview {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:12px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23dee2e6;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D.g2%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D.g3%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D.g4%7Bfill:%23ebfbee;stroke:%23333;stroke-width:1.5%7D.g5%7Bfill:%23fff9db;stroke:%23333;stroke-width:1.5%7D.g6%7Bfill:%23f1f3f5;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 中央流程图 --%3E%3Cpath d='M250 30v280' class='st0'/%3E%3C!-- 第一阶段：数据收集与预处理 --%3E%3Crect x='120' y='50' width='130' height='30' rx='5' ry='5' class='g1'/%3E%3Ctext x='185' y='70' text-anchor='middle' class='st1'%3E数据收集与预处理%3C/text%3E%3Cpath d='M185 80v20' class='st0'/%3E%3C!-- 第二阶段：模型架构设计 --%3E%3Crect x='120' y='100' width='130' height='30' rx='5' ry='5' class='g2'/%3E%3Ctext x='185' y='120' text-anchor='middle' class='st1'%3E模型架构设计%3C/text%3E%3Cpath d='M185 130v20' class='st0'/%3E%3C!-- 第三阶段：预训练 --%3E%3Crect x='120' y='150' width='130' height='30' rx='5' ry='5' class='g3'/%3E%3Ctext x='185' y='170' text-anchor='middle' class='st1'%3E预训练阶段%3C/text%3E%3Cpath d='M185 180v20' class='st0'/%3E%3C!-- 第四阶段：微调 --%3E%3Crect x='120' y='200' width='130' height='30' rx='5' ry='5' class='g4'/%3E%3Ctext x='185' y='220' text-anchor='middle' class='st1'%3E微调阶段%3C/text%3E%3Cpath d='M185 230v20' class='st0'/%3E%3C!-- 第五阶段：评估与优化 --%3E%3Crect x='120' y='250' width='130' height='30' rx='5' ry='5' class='g5'/%3E%3Ctext x='185' y='270' text-anchor='middle' class='st1'%3E评估与优化%3C/text%3E%3Cpath d='M185 280v20' class='st0'/%3E%3C!-- 第六阶段：部署与应用 --%3E%3Crect x='120' y='300' width='130' height='30' rx='5' ry='5' class='g6'/%3E%3Ctext x='185' y='320' text-anchor='middle' class='st1'%3E部署与应用%3C/text%3E%3C!-- 右侧元素 --%3E%3Ccircle cx='320' cy='65' r='25' class='st2'/%3E%3Ctext x='320' y='68' text-anchor='middle' class='st1'%3E数据%3C/text%3E%3Cpath d='M295 65h-45' class='st0'/%3E%3Cpath d='M330 85l20 20' class='st0'/%3E%3Ccircle cx='370' cy='125' r='25' class='st3'/%3E%3Ctext x='370' y='128' text-anchor='middle' class='st1'%3E架构%3C/text%3E%3Cpath d='M345 125h-95' class='st0'/%3E%3Cpath d='M380 145l20 20' class='st0'/%3E%3Crect x='370' y='175' width='50' height='40' rx='5' ry='5' class='st4'/%3E%3Ctext x='395' y='198' text-anchor='middle' class='st1'%3E预训练%3C/text%3E%3Cpath d='M370 195h-120' class='st0'/%3E%3Cpath d='M390 215v20' class='st0'/%3E%3Crect x='370' y='235' width='50' height='40' rx='5' ry='5' class='st4'/%3E%3Ctext x='395' y='258' text-anchor='middle' class='st1'%3E微调%3C/text%3E%3Cpath d='M370 255h-120' class='st0'/%3E%3Cpath d='M370 295h-120' class='st0'/%3E%3Ctext x='350' y='315' text-anchor='middle' class='st1'%3E用户应用%3C/text%3E%3C!-- 左侧元素 --%3E%3Cpath d='M120 65H90c-10 0-20 10-20 20s10 20 20 20h30' class='st0'/%3E%3Ctext x='70' y='95' text-anchor='middle' class='st1'%3E互联网%3C/text%3E%3Crect x='40' y='125' width='50' height='35' rx='5' ry='5' class='st3'/%3E%3Ctext x='65' y='148' text-anchor='middle' class='st1'%3EBrain%3C/text%3E%3Cpath d='M90 142h30' class='st0'/%3E%3Cpath d='M70 160v90' class='st0'/%3E%3Ccircle cx='70' cy='270' r='20' class='st2'/%3E%3Ctext x='70' y='275' text-anchor='middle' style='font-size:18px'%3E?%3C/text%3E%3Cpath d='M90 270h30' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.data-preparation {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:12px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23dee2e6;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D.g2%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 数据来源 --%3E%3Cpath d='M50 80c-10-5-10-15 0-20s20-5 30 0 10 15 0 20-20 5-30 0z' class='st2'/%3E%3Ctext x='65' y='80' text-anchor='middle' class='st1'%3E互联网%3C/text%3E%3Cpath d='M80 90v20' class='st0'/%3E%3Crect x='30' y='120' width='70' height='30' rx='5' ry='5' class='st3'/%3E%3Ctext x='65' y='140' text-anchor='middle' class='st1'%3E书籍%3C/text%3E%3Cpath d='M65 150v20' class='st0'/%3E%3Cpath d='M120 80c-10-5-10-15 0-20s20-5 30 0 10 15 0 20-20 5-30 0z' class='st2'/%3E%3Ctext x='135' y='80' text-anchor='middle' class='st1'%3E学术论文%3C/text%3E%3Cpath d='M120 90v20' class='st0'/%3E%3Crect x='100' y='120' width='70' height='30' rx='5' ry='5' class='st3'/%3E%3Ctext x='135' y='140' text-anchor='middle' class='st1'%3E音频数据%3C/text%3E%3Cpath d='M135 150v20' class='st0'/%3E%3C!-- 数据收集 --%3E%3Crect x='50' y='180' width='130' height='40' rx='5' ry='5' class='g1'/%3E%3Ctext x='115' y='205' text-anchor='middle' class='st1'%3E大规模数据收集%3C/text%3E%3Cpath d='M180 200h30' class='st0'/%3E%3C!-- 数据清洗 --%3E%3Crect x='210' y='180' width='100' height='40' rx='5' ry='5' class='g1'/%3E%3Ctext x='260' y='205' text-anchor='middle' class='st1'%3E数据清洗%3C/text%3E%3Cpath d='M310 200h30' class='st0'/%3E%3C!-- 数据标准化 --%3E%3Crect x='340' y='180' width='100' height='40' rx='5' ry='5' class='g1'/%3E%3Ctext x='390' y='205' text-anchor='middle' class='st1'%3E数据标准化%3C/text%3E%3C!-- 数据清洗过程 --%3E%3Cpath d='M250 220v30' class='st0'/%3E%3Cpath d='M210 260h100' class='st0'/%3E%3Cpath d='M210 250v20M310 250v20' class='st0'/%3E%3Ctext x='260' y='270' text-anchor='middle' class='st1'%3E去除低质量数据%3C/text%3E%3Cpath d='M260 260v-20' class='st0'/%3E%3Cpath d='M240 240h40' class='st0'/%3E%3Cpath d='M240 240l-10 10M280 240l10 10' class='st0'/%3E%3Ctext x='230' y='235' text-anchor='middle' class='st1'%3E✓%3C/text%3E%3Ctext x='290' y='235' text-anchor='middle' class='st1'%3E✗%3C/text%3E%3C!-- 最终训练数据 --%3E%3Cpath d='M390 220v60' class='st0'/%3E%3Crect x='320' y='280' width='140' height='40' rx='5' ry='5' class='g2'/%3E%3Ctext x='390' y='305' text-anchor='middle' class='st1'%3E预处理后的训练数据%3C/text%3E%3C!-- 数据量指示 --%3E%3Cpath d='M50 280c30 30 80 40 120 40h150' class='st0'/%3E%3Ctext x='120' y='305' text-anchor='middle' class='st1'%3E数十亿词汇%3C/text%3E%3Ctext x='240' y='325' text-anchor='middle' class='st1'%3E数TB数据量%3C/text%3E%3C/g%3E%3C/svg%3E");
}

.model-architecture {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:11px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23dee2e6;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D.g2%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D.g3%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- Transformer架构 --%3E%3Crect x='150' y='60' width='200' height='220' rx='10' ry='10' class='st2'/%3E%3Ctext x='250' y='45' text-anchor='middle' class='st1' style='font-size:14px'%3ETransformer架构%3C/text%3E%3C!-- 输入嵌入 --%3E%3Crect x='175' y='80' width='150' height='25' rx='5' ry='5' class='g1'/%3E%3Ctext x='250' y='95' text-anchor='middle' class='st1'%3E输入嵌入层%3C/text%3E%3C!-- 位置编码 --%3E%3Crect x='175' y='115' width='150' height='25' rx='5' ry='5' class='g2'/%3E%3Ctext x='250' y='130' text-anchor='middle' class='st1'%3E位置编码%3C/text%3E%3C!-- 多头注意力 --%3E%3Crect x='175' y='150' width='150' height='40' rx='5' ry='5' class='g3'/%3E%3Ctext x='250' y='173' text-anchor='middle' class='st1'%3E多头自注意力机制%3C/text%3E%3C!-- 前馈神经网络 --%3E%3Crect x='175' y='200' width='150' height='30' rx='5' ry='5' class='st4'/%3E%3Ctext x='250' y='220' text-anchor='middle' class='st1'%3E前馈神经网络%3C/text%3E%3C!-- 层标准化 --%3E%3Crect x='175' y='240' width='150' height='25' rx='5' ry='5' class='st3'/%3E%3Ctext x='250' y='255' text-anchor='middle' class='st1'%3E层标准化%3C/text%3E%3C!-- N层符号 --%3E%3Cpath d='M140 175c-5 30-5 60 0 90' class='st0'/%3E%3Cpath d='M140 175h10m-10 90h10' class='st0'/%3E%3Ctext x='130' y='220' text-anchor='middle' class='st1'%3EN层%3C/text%3E%3C!-- 跳跃连接 --%3E%3Cpath d='M360 170c10 0 20-5 20-15s-10-15-20-15h-20' class='st0'/%3E%3Cpath d='M360 170h-20' class='st0'/%3E%3Cpath d='M360 215c10 0 20-5 20-15s-10-15-20-15h-20' class='st0'/%3E%3Cpath d='M360 215h-20' class='st0'/%3E%3Ctext x='390' y='160' text-anchor='middle' class='st1'%3E跳跃连接%3C/text%3E%3C!-- 输入输出 --%3E%3Cpath d='M250 30v50' class='st0'/%3E%3Ctext x='250' y='25' text-anchor='middle' class='st1'%3E输入序列%3C/text%3E%3Cpath d='M250 280v30' class='st0'/%3E%3Ctext x='250' y='325' text-anchor='middle' class='st1'%3E输出表示%3C/text%3E%3C!-- 架构参数说明 --%3E%3Ctext x='75' y='90' text-anchor='middle' class='st1'%3E模型规模因素:%3C/text%3E%3Ctext x='80' y='110' text-anchor='middle' class='st1'%3E- 层数%3C/text%3E%3Ctext x='100' y='130' text-anchor='middle' class='st1'%3E- 隐藏层维度%3C/text%3E%3Ctext x='90' y='150' text-anchor='middle' class='st1'%3E- 注意力头数%3C/text%3E%3Ctext x='400' y='90' text-anchor='middle' class='st1'%3E特性设计:%3C/text%3E%3Ctext x='400' y='110' text-anchor='middle' class='st1'%3E- 长文本处理%3C/text%3E%3Ctext x='400' y='130' text-anchor='middle' class='st1'%3E- 多模态能力%3C/text%3E%3Ctext x='400' y='250' text-anchor='middle' class='st1'%3E参数规模:%3C/text%3E%3Ctext x='400' y='270' text-anchor='middle' class='st1'%3E小型: 数亿%3C/text%3E%3Ctext x='400' y='290' text-anchor='middle' class='st1'%3E大型: 数千亿%3C/text%3E%3C/g%3E%3C/svg%3E");
}

.pre-training {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:12px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 输入数据 --%3E%3Crect x='50' y='40' width='120' height='30' rx='5' ry='5' class='st2'/%3E%3Ctext x='110' y='60' text-anchor='middle' class='st1'%3E海量文本数据%3C/text%3E%3Cpath d='M110 70v30' class='st0'/%3E%3C!-- 预训练目标 --%3E%3Crect x='20' y='100' width='100' height='60' rx='5' ry='5' class='g1'/%3E%3Ctext x='70' y='130' text-anchor='middle' class='st1'%3E预测下一个词%3C/text%3E%3Cpath d='M120 130h50' class='st0'/%3E%3Crect x='120' y='100' width='100' height='60' rx='5' ry='5' class='g1'/%3E%3Ctext x='170' y='130' text-anchor='middle' class='st1'%3E遮盖词预测%3C/text%3E%3Cpath d='M220 130h30' class='st0'/%3E%3C!-- 模型训练 --%3E%3Crect x='250' y='105' width='70' height='50' rx='5' ry='5' class='st4'/%3E%3Ctext x='285' y='135' text-anchor='middle' class='st1'%3E模型%3C/text%3E%3Cpath d='M320 130h30' class='st0'/%3E%3C!-- 分布式训练 --%3E%3Crect x='350' y='50' width='100' height='160' rx='5' ry='5' class='st3'/%3E%3Ctext x='400' y='70' text-anchor='middle' class='st1'%3E分布式训练%3C/text%3E%3Crect x='370' y='85' width='60' height='25' rx='3' ry='3' class='st2'/%3E%3Ctext x='400' y='100' text-anchor='middle' class='st1'%3EGPU 1%3C/text%3E%3Crect x='370' y='120' width='60' height='25' rx='3' ry='3' class='st2'/%3E%3Ctext x='400' y='135' text-anchor='middle' class='st1'%3EGPU 2%3C/text%3E%3Crect x='370' y='155' width='60' height='25' rx='3' ry='3' class='st2'/%3E%3Ctext x='400' y='170' text-anchor='middle' class='st1'%3EGPU N%3C/text%3E%3Cpath d='M400 145v10' class='st0'/%3E%3Ctext x='405' y='152' text-anchor='middle' class='st1'%3E...%3C/text%3E%3C!-- 训练时间和资源 --%3E%3Cpath d='M400 210v40' class='st0'/%3E%3Crect x='350' y='250' width='100' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='400' y='270' text-anchor='middle' class='st1'%3E训练周期:%3C/text%3E%3Ctext x='400' y='285' text-anchor='middle' class='st1'%3E数周至数月%3C/text%3E%3C!-- 阶段进度 --%3E%3Cpath d='M285 155v95' class='st0'/%3E%3Crect x='235' y='250' width='100' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='285' y='270' text-anchor='middle' class='st1'%3E学习进度:%3C/text%3E%3Ctext x='285' y='285' text-anchor='middle' class='st1'%3E逐渐形成能力%3C/text%3E%3C!-- 学习曲线 --%3E%3Cpath d='M50 250h150' class='st0'/%3E%3Cpath d='M50 250v-150' class='st0'/%3E%3Cpath d='M50 250c10-10 30-50 50-70s30-30 50-40 50-20 100-25' class='st0'/%3E%3Ctext x='125' y='220' text-anchor='middle' class='st1'%3E学习曲线%3C/text%3E%3Ctext x='50' y='270' text-anchor='middle' class='st1'%3E开始%3C/text%3E%3Ctext x='200' y='270' text-anchor='middle' class='st1'%3E训练时间%3C/text%3E%3Ctext x='30' y='170' text-anchor='middle' class='st1'%3E能力%3C/text%3E%3C/g%3E%3C/svg%3E");
}

.fine-tuning {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:12px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23ebfbee;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D.g2%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D.g3%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 预训练模型 --%3E%3Crect x='60' y='70' width='120' height='60' rx='10' ry='10' class='st2'/%3E%3Ctext x='120' y='105' text-anchor='middle' class='st1'%3E预训练模型%3C/text%3E%3Cpath d='M180 100h50' class='st0'/%3E%3C!-- 微调类型 --%3E%3Crect x='230' y='40' width='100' height='40' rx='5' ry='5' class='g1'/%3E%3Ctext x='280' y='65' text-anchor='middle' class='st1'%3E监督微调(SFT)%3C/text%3E%3Cpath d='M280 80v20' class='st0'/%3E%3Crect x='230' y='110' width='100' height='40' rx='5' ry='5' class='g2'/%3E%3Ctext x='280' y='135' text-anchor='middle' class='st1'%3ERLHF%3C/text%3E%3Cpath d='M280 150v20' class='st0'/%3E%3Crect x='230' y='180' width='100' height='40' rx='5' ry='5' class='g3'/%3E%3Ctext x='280' y='205' text-anchor='middle' class='st1'%3E领域适应%3C/text%3E%3Cpath d='M330 100h40' class='st0'/%3E%3C!-- 微调数据 --%3E%3Crect x='140' y='180' width='80' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='180' y='205' text-anchor='middle' class='st1'%3E高质量数据%3C/text%3E%3Cpath d='M200 180v-30' class='st0'/%3E%3Cpath d='M200 150h30' class='st0'/%3E%3C!-- 人类反馈 --%3E%3Cellipse cx='180' cy='260' rx='30' ry='20' class='st4'/%3E%3Ctext x='180' y='265' text-anchor='middle' class='st1'%3E人类反馈%3C/text%3E%3Cpath d='M210 260h20' class='st0'/%3E%3Cpath d='M230 260v-60' class='st0'/%3E%3C!-- 微调后的模型 --%3E%3Crect x='370' y='70' width='80' height='60' rx='10' ry='10' class='st2'/%3E%3Ctext x='410' y='95' text-anchor='middle' class='st1'%3E微调后的%3C/text%3E%3Ctext x='410' y='110' text-anchor='middle' class='st1'%3E专用模型%3C/text%3E%3C!-- 微调优势 --%3E%3Cpath d='M410 130v20' class='st0'/%3E%3Crect x='340' y='150' width='140' height='30' rx='5' ry='5' class='st3'/%3E%3Ctext x='410' y='170' text-anchor='middle' class='st1'%3E↓计算资源需求%3C/text%3E%3Cpath d='M410 180v10' class='st0'/%3E%3Crect x='340' y='190' width='140' height='30' rx='5' ry='5' class='st3'/%3E%3Ctext x='410' y='210' text-anchor='middle' class='st1'%3E↓训练时间(数天)%3C/text%3E%3Cpath d='M410 220v10' class='st0'/%3E%3Crect x='340' y='230' width='140' height='30' rx='5' ry='5' class='st3'/%3E%3Ctext x='410' y='250' text-anchor='middle' class='st1'%3E↑领域专业能力%3C/text%3E%3C!-- LoRA技术 --%3E%3Cpath d='M410 260v20' class='st0'/%3E%3Crect x='360' y='280' width='100' height='30' rx='5' ry='5' class='g1'/%3E%3Ctext x='410' y='300' text-anchor='middle' class='st1'%3ELoRA技术%3C/text%3E%3Cpath d='M360 295h-60' class='st0'/%3E%3Ctext x='300' y='305' text-anchor='end' class='st1'%3E参数高效微调%3C/text%3E%3C/g%3E%3C/svg%3E");
}

.evaluation {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:12px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D.g2%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D.g3%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D.g4%7Bfill:%23ebfbee;stroke:%23333;stroke-width:1.5%7D.g5%7Bfill:%23fff9db;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 训练好的模型 --%3E%3Crect x='50' y='60' width='100' height='70' rx='10' ry='10' class='st2'/%3E%3Ctext x='100' y='95' text-anchor='middle' class='st1'%3E训练好的%3C/text%3E%3Ctext x='100' y='115' text-anchor='middle' class='st1'%3E模型%3C/text%3E%3Cpath d='M150 95h30' class='st0'/%3E%3C!-- 评估类型 --%3E%3Crect x='180' y='30' width='110' height='30' rx='5' ry='5' class='g1'/%3E%3Ctext x='235' y='50' text-anchor='middle' class='st1'%3E能力评估%3C/text%3E%3Cpath d='M235 60v5' class='st0'/%3E%3Crect x='180' y='65' width='110' height='30' rx='5' ry='5' class='g2'/%3E%3Ctext x='235' y='85' text-anchor='middle' class='st1'%3E标准基准测试%3C/text%3E%3Cpath d='M235 95v5' class='st0'/%3E%3Crect x='180' y='100' width='110' height='30' rx='5' ry='5' class='g3'/%3E%3Ctext x='235' y='120' text-anchor='middle' class='st1'%3E安全评估%3C/text%3E%3Cpath d='M235 130v5' class='st0'/%3E%3Crect x='180' y='135' width='110' height='30' rx='5' ry='5' class='g4'/%3E%3Ctext x='235' y='155' text-anchor='middle' class='st1'%3E偏见分析%3C/text%3E%3Cpath d='M235 165v5' class='st0'/%3E%3Crect x='180' y='170' width='110' height='30' rx='5' ry='5' class='g5'/%3E%3Ctext x='235' y='190' text-anchor='middle' class='st1'%3E人工评估%3C/text%3E%3Cpath d='M290 95h30' class='st0'/%3E%3C!-- 评估结果 --%3E%3Crect x='320' y='65' width='130' height='60' rx='5' ry='5' class='st3'/%3E%3Ctext x='385' y='85' text-anchor='middle' class='st1'%3E评估报告%3C/text%3E%3Cline x1='350' y1='95' x2='420' y2='95' class='st0'/%3E%3Ctext x='365' y='110' text-anchor='middle' class='st1'%3E优势:%3C/text%3E%3Ctext x='385' y='110' text-anchor='middle' class='st1'%3E✓✓✓%3C/text%3E%3Ctext x='365' y='125' text-anchor='middle' class='st1'%3E劣势:%3C/text%3E%3Ctext x='385' y='125' text-anchor='middle' class='st1'%3E✗✗%3C/text%3E%3Cpath d='M385 125v25' class='st0'/%3E%3C!-- 优化需求 --%3E%3Cpath d='M325 185c0 10 10 10 15 0s10 0 15 0 10 0 15 0 10 0 15 0 10 0 15 0 10 10 15 0' class='st0'/%3E%3Ctext x='385' y='170' text-anchor='middle' class='st1'%3E优化需求%3C/text%3E%3C!-- 优化方法 --%3E%3Crect x='315' y='200' width='70' height='40' rx='5' ry='5' class='g1'/%3E%3Ctext x='350' y='225' text-anchor='middle' class='st1'%3E模型蒸馏%3C/text%3E%3Crect x='315' y='250' width='70' height='40' rx='5' ry='5' class='g3'/%3E%3Ctext x='350' y='275' text-anchor='middle' class='st1'%3E数据增强%3C/text%3E%3Crect x='395' y='200' width='70' height='40' rx='5' ry='5' class='g2'/%3E%3Ctext x='430' y='225' text-anchor='middle' class='st1'%3E量化压缩%3C/text%3E%3Crect x='395' y='250' width='70' height='40' rx='5' ry='5' class='g4'/%3E%3Ctext x='430' y='275' text-anchor='middle' class='st1'%3E选择性更新%3C/text%3E%3C!-- 调优循环 --%3E%3Cpath d='M100 130v100' class='st0'/%3E%3Cpath d='M100 230h190' class='st0'/%3E%3Cpath d='M290 230v20' class='st0'/%3E%3Crect x='240' y='250' width='100' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='290' y='275' text-anchor='middle' class='st1'%3E持续优化循环%3C/text%3E%3Cpath d='M50 95h-20' class='st0'/%3E%3Cpath d='M30 95c-20 0-20 175 0 175' class='st0'/%3E%3Cpath d='M30 270h210' class='st0'/%3E%3Ctext x='135' y='255' text-anchor='middle' class='st1'%3E反馈优化%3C/text%3E%3C/g%3E%3C/svg%3E");
}

.deployment {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:12px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23dee2e6;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D.g2%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D.g3%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D.g4%7Bfill:%23ebfbee;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 优化后的模型 --%3E%3Crect x='60' y='100' width='100' height='70' rx='10' ry='10' class='st2'/%3E%3Ctext x='110' y='125' text-anchor='middle' class='st1'%3E优化后的%3C/text%3E%3Ctext x='110' y='145' text-anchor='middle' class='st1'%3E模型%3C/text%3E%3Cpath d='M160 135h30' class='st0'/%3E%3C!-- 部署流程 --%3E%3Crect x='190' y='110' width='80' height='50' rx='5' ry='5' class='g1'/%3E%3Ctext x='230' y='140' text-anchor='middle' class='st1'%3E模型服务化%3C/text%3E%3Cpath d='M270 135h30' class='st0'/%3E%3Crect x='300' y='70' width='140' height='130' rx='5' ry='5' class='st3'/%3E%3Ctext x='370' y='90' text-anchor='middle' class='st1'%3E部署环境%3C/text%3E%3C!-- 服务器图标 --%3E%3Crect x='320' y='100' width='100' height='20' rx='3' ry='3' class='st4'/%3E%3Ctext x='370' y='115' text-anchor='middle' class='st1'%3E服务器 1%3C/text%3E%3Crect x='320' y='130' width='100' height='20' rx='3' ry='3' class='st4'/%3E%3Ctext x='370' y='145' text-anchor='middle' class='st1'%3E服务器 2%3C/text%3E%3Crect x='320' y='160' width='100' height='20' rx='3' ry='3' class='st4'/%3E%3Ctext x='370' y='175' text-anchor='middle' class='st1'%3E服务器...%3C/text%3E%3C!-- 负载均衡 --%3E%3Cellipse cx='230' cy='70' rx='40' ry='20' class='g2'/%3E%3Ctext x='230' y='75' text-anchor='middle' class='st1'%3E负载均衡%3C/text%3E%3Cpath d='M230 90v20' class='st0'/%3E%3Cpath d='M265 70h35v30' class='st0'/%3E%3C!-- 监控系统 --%3E%3Crect x='185' y='200' width='90' height='40' rx='5' ry='5' class='g3'/%3E%3Ctext x='230' y='225' text-anchor='middle' class='st1'%3E监控系统%3C/text%3E%3Cpath d='M230 200v-40' class='st0'/%3E%3Cpath d='M275 220h25v-20' class='st0'/%3E%3C!-- 用户应用 --%3E%3Crect x='60' y='40' width='80' height='40' rx='5' ry='5' class='g4'/%3E%3Ctext x='100' y='65' text-anchor='middle' class='st1'%3E网页应用%3C/text%3E%3Crect x='150' y='40' width='80' height='40' rx='5' ry='5' class='g4'/%3E%3Ctext x='190' y='65' text-anchor='middle' class='st1'%3E移动应用%3C/text%3E%3Cpath d='M100 80v20' class='st0'/%3E%3Cpath d='M190 80c0 10-10 30-60 30' class='st0'/%3E%3C!-- 反馈循环 --%3E%3Cpath d='M370 200v40' class='st0'/%3E%3Crect x='300' y='240' width='140' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='370' y='265' text-anchor='middle' class='st1'%3E用户反馈收集%3C/text%3E%3Cpath d='M300 260h-200' class='st0'/%3E%3Cpath d='M100 260v-90' class='st0'/%3E%3Ctext x='200' y='280' text-anchor='middle' class='st1'%3E持续改进闭环%3C/text%3E%3C!-- 版本控制 --%3E%3Crect x='40' y='200' width='80' height='40' rx='5' ry='5' class='g1'/%3E%3Ctext x='80' y='225' text-anchor='middle' class='st1'%3E版本控制%3C/text%3E%3Cpath d='M120 220h65' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

/* 图标 */
.computing-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Crect x='25' y='30' width='50' height='30' rx='3' ry='3' class='st1'/%3E%3Cpath d='M30 40h40M35 50h10m10 0h10' class='st0'/%3E%3Crect x='20' y='60' width='60' height='10' rx='2' ry='2' class='st1'/%3E%3Cpath d='M30 70v5m40-5v5M40 75h20' class='st0'/%3E%3Cpath d='M35 30l-5-10m35 10l5-10M45 20h10' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.data-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Cpath d='M30 30h40v40H30z' class='st1'/%3E%3Cpath d='M30 40h40M30 50h40M30 60h40M40 30v40M50 30v40M60 30v40' class='st0'/%3E%3Cpath d='M50 20l20 10M50 20l-20 10M30 70l20 10m0 0l20-10' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.convergence-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Crect x='20' y='20' width='60' height='60' rx='3' ry='3' class='st1'/%3E%3Cpath d='M20 50h60' class='st0'/%3E%3Cpath d='M50 20v60' class='st0'/%3E%3Cpath d='M20 20l60 60M80 20L20 80' class='st0'/%3E%3Ccircle cx='50' cy='50' r='10' class='st0'/%3E%3Ccircle cx='50' cy='50' r='5' fill='%23333'/%3E%3C/g%3E%3C/svg%3E");
}

.engineering-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Cpath d='M40 25c-10 0-15 5-15 15v20c0 10 5 15 15 15h20c10 0 15-5 15-15V40c0-10-5-15-15-15H40z' class='st1'/%3E%3Cpath d='M35 45c5 0 5 5 10 5s5-5 10-5 5 5 10 5' class='st0'/%3E%3Cpath d='M35 55c5 0 5 5 10 5s5-5 10-5 5 5 10 5' class='st0'/%3E%3Cpath d='M35 75v-40h30v40' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.alignment-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23ebfbee;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Ccircle cx='35' cy='50' r='15' class='st1'/%3E%3Ccircle cx='65' cy='50' r='15' class='st1'/%3E%3Cpath d='M50 50h0' class='st0'/%3E%3Cpath d='M35 40c0-5 5-5 5-10M40 60c0 5-5 5-5 10M65 40c0-5-5-5-5-10M60 60c0 5 5 5 5 10' class='st0'/%3E%3Cpath d='M28 50c0-4 3-7 7-7s7 3 7 7-3 7-7 7-7-3-7-7zM58 50c0-4 3-7 7-7s7 3 7 7-3 7-7 7-7-3-7-7z' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.evaluation-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23fff9db;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Crect x='30' y='30' width='40' height='50' rx='3' ry='3' class='st1'/%3E%3Cpath d='M40 45h20M40 55h20M40 65h10' class='st0'/%3E%3Cpath d='M40 20v10h20V20M30 30l10-10M60 30l10-10M70 30v40' class='st0'/%3E%3Cpath d='M40 80l-10 5v-5h10z' class='st0'/%3E%3Cpath d='M30 80V40' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}
</style> 