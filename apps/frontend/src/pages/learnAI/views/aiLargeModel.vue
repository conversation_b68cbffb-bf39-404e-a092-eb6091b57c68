<template>
  <div class="learn-ai-large-model-page">
    <div class="container">
      <div class="title-section">
        <h1 class="page-title">AI大模型：概念与作用</h1>
      </div>

      <div class="section intro-section">
        <p>人工智能大模型是当代AI领域最引人注目的技术进步之一，它们改变了我们与技术交互的方式，并为许多行业带来革命性变化。让我们一起探索这个迷人的AI世界。</p>
      </div>

      <!-- 什么是大模型 -->
      <div class="content-section">
        <div class="section-header">
          <h2>什么是AI大模型？</h2>
        </div>
        <div class="section-content">
          <div class="image-section">
            <div class="hand-drawn-image large-model-concept">
              <!-- 大模型概念的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p><strong>AI大模型</strong>是拥有数十亿甚至数千亿参数的大规模神经网络，通过海量数据训练而成。想象一个拥有惊人记忆力和学习能力的"大脑"，能够理解和生成人类语言、识别图像、创作艺术，甚至编写代码。</p>
            
            <p>这些模型之所以被称为"大"，不仅是因为它们的参数数量庞大，还因为：</p>
            
            <ul>
              <li><strong>训练数据规模惊人</strong>：通常基于互联网上的海量文本、图像和其他信息</li>
              <li><strong>计算资源需求巨大</strong>：训练过程可能需要数百台高性能计算机运行数周或数月</li>
              <li><strong>能力极其广泛</strong>：从写诗到编程，从翻译到创作艺术，样样精通</li>
            </ul>
            
            <p>简单来说，这就像是一个接受了互联网上几乎所有知识训练的"超级学生"，并且能够灵活地应用这些知识解决各种问题。</p>
          </div>
        </div>
      </div>

      <!-- 大模型如何工作 -->
      <div class="content-section">
        <div class="section-header">
          <h2>大模型如何工作？</h2>
        </div>
        <div class="section-content reverse">
          <div class="image-section">
            <div class="hand-drawn-image large-model-working">
              <!-- 大模型工作原理的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>大模型的工作原理可以比喻为一个超级预测机器，就像是在猜测文本或图像中"接下来会是什么"：</p>
            
            <ol>
              <li><strong>预训练阶段</strong>：模型通过阅读互联网上的大量文本或观看大量图像来学习语言、知识和世界的工作方式，就像一个孩子吸收信息一样</li>
              <li><strong>模式识别</strong>：模型学会识别单词、句子、图像中的模式和关系，并存储在其数十亿参数中</li>
              <li><strong>预测下一步</strong>：给定一个输入（如半个句子），模型能够预测最有可能的下一个单词或内容</li>
              <li><strong>微调阶段</strong>：经过预训练后，模型会通过更有针对性的训练变得更有用、更安全</li>
            </ol>
            
            <p>想象你玩"接下来会说什么"的游戏：如果我说"我喜欢吃苹..."，你很可能会猜"果"。大模型就是以极其复杂的方式进行这样的预测，但范围涵盖了几乎所有人类知识领域。</p>
          </div>
        </div>
      </div>

      <!-- 大模型的发展历程 -->
      <div class="content-section">
        <div class="section-header">
          <h2>大模型的发展历程</h2>
        </div>
        <div class="section-content">
          <div class="image-section">
            <div class="hand-drawn-image large-model-history">
              <!-- 大模型发展历程的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>AI大模型并非凭空出现，而是经历了多年的技术积累和突破：</p>
            
            <ul>
              <li><strong>2017年：Transformer架构诞生</strong><br/>谷歌团队发布了革命性的Transformer架构，通过"注意力机制"彻底改变了机器处理序列数据的方式</li>
              
              <li><strong>2018年：BERT和GPT-1</strong><br/>谷歌的BERT和OpenAI的GPT-1展示了预训练语言模型的强大潜力，参数量达到数亿级别</li>
              
              <li><strong>2020年：GPT-3震撼登场</strong><br/>拥有1750亿参数的GPT-3展示了惊人的语言能力，引发了大模型时代的到来</li>
              
              <li><strong>2021-2022年：多模态模型兴起</strong><br/>DALL-E、Stable Diffusion等模型将AI扩展到图像生成领域，实现文本到图像的转换</li>
              
              <li><strong>2023年至今：GPT-4及更多突破</strong><br/>更大、更智能的模型不断涌现，能力边界持续扩展，ChatGPT等产品让AI大模型走入大众视野</li>
            </ul>
            
            <p>大模型的发展史就是参数规模不断扩大、能力不断提升的历史，从最初的几千万参数到如今的数千亿参数，计算能力和数据量的指数级增长推动了这一进步。</p>
          </div>
        </div>
      </div>

      <!-- 大模型的主要作用 -->
      <div class="content-section">
        <div class="section-header">
          <h2>大模型的主要作用</h2>
        </div>
        <div class="section-content reverse">
          <div class="image-section">
            <div class="hand-drawn-image large-model-functions">
              <!-- 大模型作用的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>AI大模型的应用范围极其广泛，正在改变多个行业的工作方式。主要作用包括：</p>
            
            <ul>
              <li><strong>自然语言处理</strong>：理解和生成人类语言，支持聊天机器人、智能客服和内容创作</li>
              
              <li><strong>知识获取与问答</strong>：回答各类问题，提供信息检索，成为知识助手</li>
              
              <li><strong>内容创作</strong>：写作文章、诗歌、代码，辅助人类创作过程</li>
              
              <li><strong>翻译与跨语言交流</strong>：打破语言障碍，实现几乎所有语言之间的实时翻译</li>
              
              <li><strong>编程辅助</strong>：生成代码、调试问题、解释复杂概念，加速软件开发</li>
            </ul>
            
            <p>几乎在每个需要处理语言、分析信息或创造内容的领域，大模型都在发挥越来越重要的作用。</p>
          </div>
        </div>
      </div>

      <!-- 大模型在行业中的应用 -->
      <div class="content-section">
        <div class="section-header">
          <h2>大模型在行业中的应用</h2>
        </div>
        <div class="application-cards">
          <div class="application-card">
            <div class="card-icon education-icon"></div>
            <h3>教育领域</h3>
            <ul>
              <li>个性化学习助手，根据学生能力定制教学内容</li>
              <li>自动化批改作业，提供详细的反馈和建议</li>
              <li>创建交互式教学内容，增强学习体验</li>
              <li>为教师提供备课和教学资源支持</li>
            </ul>
          </div>
          
          <div class="application-card">
            <div class="card-icon healthcare-icon"></div>
            <h3>医疗健康</h3>
            <ul>
              <li>辅助医生诊断，提供潜在疾病的参考信息</li>
              <li>分析医学文献，总结最新研究成果</li>
              <li>生成医学报告，减轻医护人员文书负担</li>
              <li>提供健康咨询和初步筛查服务</li>
            </ul>
          </div>
          
          <div class="application-card">
            <div class="card-icon business-icon"></div>
            <h3>商业与金融</h3>
            <ul>
              <li>智能客服，处理客户查询和投诉</li>
              <li>市场分析，洞察消费者行为和趋势</li>
              <li>自动化报告生成，节省人力资源</li>
              <li>风险评估和欺诈检测，增强安全性</li>
            </ul>
          </div>
          
          <div class="application-card">
            <div class="card-icon creative-icon"></div>
            <h3>创意产业</h3>
            <ul>
              <li>辅助内容创作，从故事大纲到完整文本</li>
              <li>生成图像和艺术作品，激发创作灵感</li>
              <li>音乐创作和声音设计，拓展艺术表达</li>
              <li>游戏开发中的角色对话和剧情设计</li>
            </ul>
          </div>
          
          <div class="application-card">
            <div class="card-icon science-icon"></div>
            <h3>科研与工程</h3>
            <ul>
              <li>加速科学发现，提出研究假设</li>
              <li>分析复杂数据集，发现隐藏模式</li>
              <li>辅助软件开发，自动生成代码</li>
              <li>优化工程设计，模拟不同方案效果</li>
            </ul>
          </div>
          
          <div class="application-card">
            <div class="card-icon social-icon"></div>
            <h3>社会服务</h3>
            <ul>
              <li>多语言交流，打破语言障碍</li>
              <li>支持残障人士，如视觉描述和语音转文字</li>
              <li>紧急情况信息处理和响应</li>
              <li>公共服务信息查询和引导</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 大模型的挑战与未来 -->
      <div class="content-section">
        <div class="section-header">
          <h2>大模型的挑战与未来</h2>
        </div>
        <div class="section-content">
          <div class="image-section">
            <div class="hand-drawn-image large-model-future">
              <!-- 大模型未来的SVG手绘图 -->
            </div>
          </div>
          <div class="text-section">
            <p>尽管大模型带来了令人惊叹的能力，但它们也面临着一系列重要挑战：</p>
            
            <ul>
              <li><strong>偏见与公平性</strong>：模型可能从训练数据中继承社会偏见，需要持续改进以确保公平</li>
              <li><strong>事实准确性</strong>：模型有时会生成看似合理但实际不正确的信息（"幻觉"问题）</li>
              <li><strong>隐私与安全</strong>：需要确保用户数据安全，并防止模型被滥用于有害目的</li>
              <li><strong>透明度与可解释性</strong>：理解模型如何做出决策仍然是一个挑战</li>
              <li><strong>计算资源消耗</strong>：训练和运行大模型需要大量能源和计算资源</li>
            </ul>
            
            <p>未来发展方向主要包括：</p>
            
            <ul>
              <li><strong>多模态整合</strong>：结合文本、图像、声音、视频等多种输入输出能力</li>
              <li><strong>更高效的模型</strong>：通过蒸馏等技术创建更小、更快但同样强大的模型</li>
              <li><strong>个性化与定制</strong>：针对特定领域和用户需求进行优化</li>
              <li><strong>增强推理能力</strong>：提高模型的逻辑推理和问题解决能力</li>
              <li><strong>更好的安全机制</strong>：开发更有效的对齐和安全技术</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 结论 -->
      <div class="content-section conclusion-section">
        <div class="conclusion-card">
          <h2>结语：AI大模型与人类的共同未来</h2>
          <p>AI大模型代表了人工智能领域的重大突破，它们不仅仅是技术工具，更是人类智慧的延伸。这些模型通过理解和生成人类语言、分析复杂数据、创造内容，帮助我们解决从日常到专业的各种挑战。</p>
          
          <p>重要的是，大模型最终是为了增强而非替代人类能力。它们的真正价值在于如何帮助人类更高效地工作、学习和创造，解放我们的时间和精力，专注于更有创意和更有意义的任务。</p>
          
          <p>随着技术的不断进步，大模型将变得更强大、更安全、更易于使用。我们正站在AI发展的新起点，未来的可能性令人兴奋。通过负责任地开发和使用这些工具，我们有机会创造一个人类和AI和谐共生、共同繁荣的未来。</p>
        </div>
      </div>

      <AiFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
import AiFooter from '../components/AiFooter.vue';
// 组件逻辑
</script>

<style lang="scss" scoped>
.learn-ai-large-model-page {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  line-height: 1.6;
  padding: 20px;
  background-color: #f9f9f9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.title-section {
  text-align: center;
  margin-bottom: 40px;
  
  .page-title {
    font-size: 2.8rem;
    margin-bottom: 10px;
    font-weight: 700;
  }
  
  .subtitle {
    font-size: 1.2rem;
    color: #666;
  }
}

.intro-section {
  font-size: 1.2rem;
  margin-bottom: 40px;
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.8;
}

.content-section {
  margin-bottom: 60px;
  
  .section-header {
    margin-bottom: 30px;
    
    h2 {
      font-size: 2rem;
      font-weight: 600;
      position: relative;
      display: inline-block;
      
      &:after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: #5c6ac4;
        border-radius: 3px;
      }
    }
  }
  
  .section-content {
    display: flex;
    flex-direction: column-reverse;
    gap: 30px;
    
    @media (min-width: 768px) {
      flex-direction: row;
      align-items: center;
      
      &.reverse {
        flex-direction: row-reverse;
      }
    }
  }
  
  .text-section {
    flex: 1;
    
    p, ul, ol {
      margin-bottom: 20px;
      font-size: 1.1rem;
    }
    
    ul, ol {
      padding-left: 20px;
      
      li {
        margin-bottom: 10px;
      }
    }
  }
  
  .image-section {
    flex: 1;
    display: flex;
    justify-content: center;
  }
  
  .hand-drawn-image {
    width: 100%;
    max-width: 450px;
    height: 300px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
  }
}

/* SVG手绘图 */
.large-model-concept {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23dee2e6;stroke:%23333;stroke-width:1.5%7D.st5%7Bfill:none;stroke:%23333;stroke-width:1;stroke-dasharray:5,3%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 大脑轮廓 --%3E%3Cpath d='M250 30c-66 0-120 54-120 120 0 22 6 43 16 60a119 119 0 0080 58c8 1 16 2 24 2 66 0 120-54 120-120S316 30 250 30z' class='st2'/%3E%3C!-- 大脑褶皱 --%3E%3Cpath d='M190 70c10-8 30-15 40-5m40-5c20-15 50-10 65 10m-5 30c20-5 40 0 50 15m-5 30c15 0 35 10 35 25m-30 40c15 10 20 30 15 45m-45 25c0 15-10 25-25 30m-45 0c-15-5-25-15-25-30m-45-20c-15-10-20-25-15-40m-25-40c-15-15-15-35-5-50m15-40c-5-20 5-35 20-40' class='st0'/%3E%3C!-- 神经网络节点 --%3E%3Ccircle cx='200' cy='100' r='8' class='st3'/%3E%3Ccircle cx='270' cy='90' r='8' class='st3'/%3E%3Ccircle cx='330' cy='120' r='8' class='st3'/%3E%3Ccircle cx='350' cy='180' r='8' class='st3'/%3E%3Ccircle cx='320' cy='240' r='8' class='st3'/%3E%3Ccircle cx='250' cy='270' r='8' class='st3'/%3E%3Ccircle cx='180' cy='250' r='8' class='st3'/%3E%3Ccircle cx='150' cy='190' r='8' class='st3'/%3E%3Ccircle cx='160' cy='130' r='8' class='st3'/%3E%3Ccircle cx='250' cy='150' r='15' class='st4'/%3E%3C!-- 连接线 --%3E%3Cpath d='M200 100l45 45m70-55l-65 60m80 30l-65 60m20 60l-55-60m-70 10l45-55m-30-60l20 40m-10 60l40-95' class='st0'/%3E%3C!-- 数据流 --%3E%3Cpath d='M80 170c-10 0-15 10-15 20s5 20 15 20h30' class='st0'/%3E%3Cpath d='M110 170v40' class='st0'/%3E%3Ctext x='95' y='195' text-anchor='middle' class='st1'%3E输入%3C/text%3E%3Cpath d='M110 190l35-20' class='st5'/%3E%3Cpath d='M420 170c10 0 15 10 15 20s-5 20-15 20h-30' class='st0'/%3E%3Cpath d='M390 170v40' class='st0'/%3E%3Ctext x='405' y='195' text-anchor='middle' class='st1'%3E输出%3C/text%3E%3Cpath d='M390 190l-35-20' class='st5'/%3E%3C!-- 参数标注 --%3E%3Cpath d='M250 20c0-10 15-10 15 0m0 0v20' class='st0'/%3E%3Ctext x='265' y='15' text-anchor='middle' class='st1'%3E数十亿参数%3C/text%3E%3Cpath d='M250 320c0 10-15 10-15 0m0 0v-20' class='st0'/%3E%3Ctext x='235' y='335' text-anchor='middle' class='st1'%3E海量训练数据%3C/text%3E%3C/g%3E%3C/svg%3E");
}

.large-model-working {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:14px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23f1f3f5;stroke:%23333;stroke-width:1.5%7D.st5%7Bfill:%23dee2e6;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 标题 --%3E%3Ctext x='250' y='30' text-anchor='middle' class='st1' style='font-size:16px;font-weight:bold'%3E大模型预测过程%3C/text%3E%3C!-- 输入文本框 --%3E%3Crect x='60' y='60' width='380' height='60' rx='10' ry='10' class='st2'/%3E%3Ctext x='70' y='85' class='st1'%3E输入: 我喜欢吃苹%3C/text%3E%3Ctext x='190' y='85' class='st1' style='font-style:italic'%3E...?%3C/text%3E%3Ctext x='70' y='110' class='st1' style='font-style:italic'%3E模型开始预测下一个词...%3C/text%3E%3C!-- Transformer层 --%3E%3Crect x='100' y='150' width='300' height='80' rx='8' ry='8' class='st3'/%3E%3Ctext x='250' y='176' text-anchor='middle' class='st1'%3ETransformer 注意力机制%3C/text%3E%3Ctext x='250' y='196' text-anchor='middle' class='st1'%3E(分析上下文关系)%3C/text%3E%3Cpath d='M250 120v30' class='st0'/%3E%3C!-- 预测步骤 --%3E%3Crect x='60' y='260' width='120' height='60' rx='8' ry='8' class='st4'/%3E%3Ctext x='120' y='290' text-anchor='middle' class='st1'%3E苹果: 95%25%3C/text%3E%3Crect x='190' y='260' width='120' height='60' rx='8' ry='8' class='st4'/%3E%3Ctext x='250' y='290' text-anchor='middle' class='st1'%3E苹子: 3%25%3C/text%3E%3Crect x='320' y='260' width='120' height='60' rx='8' ry='8' class='st4'/%3E%3Ctext x='380' y='290' text-anchor='middle' class='st1'%3E苹...?: 2%25%3C/text%3E%3C!-- 连接线 --%3E%3Cpath d='M120 230v30m130 0v-30m130 0v30' class='st0'/%3E%3Crect x='85' y='230' width='330' height='1' class='st0'/%3E%3Cpath d='M250 230v-30' class='st0'/%3E%3C!-- 输出 --%3E%3Cpath d='M120 320v10' class='st0'/%3E%3Cpath d='M380 320c0 10-260 10-260 0' class='st0'/%3E%3Cpath d='M250 330v10' class='st0'/%3E%3Crect x='150' y='340' width='200' height='30' rx='5' ry='5' class='st5'/%3E%3Ctext x='250' y='360' text-anchor='middle' class='st1'%3E输出: 我喜欢吃苹果%3C/text%3E%3C/g%3E%3C/svg%3E");
}

.large-model-history {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:13px%7D.st2%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23f1f3f5;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 时间轴 --%3E%3Cpath d='M70 170h360' class='st0'/%3E%3C!-- 2017 --%3E%3Ccircle cx='100' cy='170' r='10' class='st2'/%3E%3Ctext x='100' y='150' text-anchor='middle' class='st1'%3E2017%3C/text%3E%3Cpath d='M100 190v30' class='st0'/%3E%3Crect x='60' y='220' width='80' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='100' y='240' text-anchor='middle' class='st1'%3ETransformer%3C/text%3E%3Ctext x='100' y='255' text-anchor='middle' class='st1'%3E架构诞生%3C/text%3E%3C!-- 线条到2017上方 --%3E%3Cpath d='M100 140c0-30 25-40 50-40h200c25 0 50 10 50 40' class='st0'/%3E%3Ctext x='250' y='80' text-anchor='middle' class='st1'%3E大模型发展时间线%3C/text%3E%3C!-- 2018 --%3E%3Ccircle cx='175' cy='170' r='10' class='st2'/%3E%3Ctext x='175' y='150' text-anchor='middle' class='st1'%3E2018%3C/text%3E%3Cpath d='M175 190v30' class='st0'/%3E%3Crect x='135' y='220' width='80' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='175' y='240' text-anchor='middle' class='st1'%3EBERT/GPT-1%3C/text%3E%3Ctext x='175' y='255' text-anchor='middle' class='st1'%3E数亿参数%3C/text%3E%3C!-- 2020 --%3E%3Ccircle cx='250' cy='170' r='10' class='st2'/%3E%3Ctext x='250' y='150' text-anchor='middle' class='st1'%3E2020%3C/text%3E%3Cpath d='M250 190v30' class='st0'/%3E%3Crect x='210' y='220' width='80' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='250' y='240' text-anchor='middle' class='st1'%3EGPT-3%3C/text%3E%3Ctext x='250' y='255' text-anchor='middle' class='st1'%3E1750亿参数%3C/text%3E%3C!-- 2021-2022 --%3E%3Ccircle cx='325' cy='170' r='10' class='st2'/%3E%3Ctext x='325' y='150' text-anchor='middle' class='st1'%3E2021-22%3C/text%3E%3Cpath d='M325 190v30' class='st0'/%3E%3Crect x='285' y='220' width='80' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='325' y='240' text-anchor='middle' class='st1'%3EDALL-E%3C/text%3E%3Ctext x='325' y='255' text-anchor='middle' class='st1'%3E多模态时代%3C/text%3E%3C!-- 2023+ --%3E%3Ccircle cx='400' cy='170' r='10' class='st2'/%3E%3Ctext x='400' y='150' text-anchor='middle' class='st1'%3E2023+%3C/text%3E%3Cpath d='M400 190v30' class='st0'/%3E%3Crect x='360' y='220' width='80' height='40' rx='5' ry='5' class='st3'/%3E%3Ctext x='400' y='240' text-anchor='middle' class='st1'%3EGPT-4%3C/text%3E%3Ctext x='400' y='255' text-anchor='middle' class='st1'%3E持续进化%3C/text%3E%3C!-- 大脑图标 --%3E%3Cellipse cx='400' cy='300' rx='20' ry='15' class='st4'/%3E%3Cpath d='M390 290c2-2 8-2 10 0m-5 0v5m10 10c2-2 0-8-3-10m-14 0c-3 2-5 8-3 10' class='st0'/%3E%3Cpath d='M380 290l15 15m10-15l-15 15' class='st0'/%3E%3C!-- 参数增长曲线 --%3E%3Cpath d='M100 120c30-5 75-10 75-10 50-10 75-25 75-25s20-20 75-35c20-5 75-10 75-10' class='st0'/%3E%3Ctext x='175' y='55' text-anchor='middle' class='st1'%3E参数规模指数增长%3C/text%3E%3Cpath d='M175 65l-20 20' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.large-model-functions {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:13px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23dee2e6;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D.g2%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D.g3%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D.g4%7Bfill:%23ebfbee;stroke:%23333;stroke-width:1.5%7D.g5%7Bfill:%23fff9db;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 中心AI模型 --%3E%3Cellipse cx='250' cy='160' rx='70' ry='50' class='st2'/%3E%3Ctext x='250' y='165' text-anchor='middle' class='st1'%3EAI大模型%3C/text%3E%3C!-- 连接线 --%3E%3Cpath d='M180 160h-30m170 0h30m-100-50v-35m0 135v35m-60-85l-40-40m160 0l-40 40m-80 45l-40 40m160 0l-40-40' class='st0'/%3E%3C!-- 自然语言处理 --%3E%3Crect x='75' y='145' width='75' height='30' rx='5' ry='5' class='g1'/%3E%3Ctext x='112' y='165' text-anchor='middle' class='st1'%3E语言处理%3C/text%3E%3C!-- 知识获取 --%3E%3Crect x='100' y='75' width='75' height='30' rx='5' ry='5' class='g2'/%3E%3Ctext x='137' y='95' text-anchor='middle' class='st1'%3E知识获取%3C/text%3E%3C!-- 内容创作 --%3E%3Crect x='212' y='50' width='75' height='30' rx='5' ry='5' class='g3'/%3E%3Ctext x='250' y='70' text-anchor='middle' class='st1'%3E内容创作%3C/text%3E%3C!-- 翻译 --%3E%3Crect x='325' y='75' width='75' height='30' rx='5' ry='5' class='g4'/%3E%3Ctext x='362' y='95' text-anchor='middle' class='st1'%3E翻译%3C/text%3E%3C!-- 编程辅助 --%3E%3Crect x='350' y='145' width='75' height='30' rx='5' ry='5' class='g5'/%3E%3Ctext x='387' y='165' text-anchor='middle' class='st1'%3E编程辅助%3C/text%3E%3C!-- 其他应用 --%3E%3Crect x='325' y='245' width='75' height='30' rx='5' ry='5' class='g1'/%3E%3Ctext x='362' y='265' text-anchor='middle' class='st1'%3E图像生成%3C/text%3E%3Crect x='212' y='270' width='75' height='30' rx='5' ry='5' class='g2'/%3E%3Ctext x='250' y='290' text-anchor='middle' class='st1'%3E数据分析%3C/text%3E%3Crect x='100' y='245' width='75' height='30' rx='5' ry='5' class='g3'/%3E%3Ctext x='137' y='265' text-anchor='middle' class='st1'%3E决策支持%3C/text%3E%3C!-- 手绘人物 --%3E%3Cpath d='M50 200c0 5 5 15 10 15s10-10 10-15m-15-5c5 0 10 0 15 0m-17 15c2 5 15 5 17 0' class='st0'/%3E%3Ccircle cx='55' cy='185' r='3' class='st0'/%3E%3Ccircle cx='65' cy='185' r='3' class='st0'/%3E%3Cpath d='M430 200c0 5 5 15 10 15s10-10 10-15m-15-5c5 0 10 0 15 0m-17 15c2 5 15 5 17 0' class='st0'/%3E%3Ccircle cx='435' cy='185' r='3' class='st0'/%3E%3Ccircle cx='445' cy='185' r='3' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.application-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.application-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
  }
  
  h3 {
    font-size: 1.4rem;
    margin: 15px 0;
    font-weight: 600;
  }
  
  ul {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      font-size: 0.95rem;
    }
  }
}

.card-icon {
  width: 60px;
  height: 60px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 行业图标 - 手绘风格SVG */
.education-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23f1f3f5;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Cpath d='M50 20l35 15-35 15-35-15z' class='st1'/%3E%3Cpath d='M28 42v20c0 5 10 10 22 10s22-5 22-10V42' class='st0'/%3E%3Cpath d='M85 35v25' class='st0'/%3E%3Cpath d='M85 60c0 2.5-2 4.5-4.5 4.5S76 62.5 76 60s2-4.5 4.5-4.5S85 57.5 85 60z' class='st1'/%3E%3C/g%3E%3C/svg%3E");
}

.healthcare-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Ccircle cx='50' cy='50' r='30' class='st1'/%3E%3Cpath d='M50 30v40M30 50h40' class='st0'/%3E%3Cpath d='M75 25c5-5 10-5 15 0M80 20c-5-5-15-5-20 0' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.business-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Crect x='30' y='40' width='40' height='30' rx='3' ry='3' class='st1'/%3E%3Crect x='40' y='30' width='20' height='10' rx='2' ry='2' class='st1'/%3E%3Cpath d='M45 55h10M30 75h40' class='st0'/%3E%3Cpath d='M40 70v10M60 70v10' class='st0'/%3E%3Cpath d='M20 25c0 10 10 25 10 25M80 25c0 10-10 25-10 25' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.creative-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Ccircle cx='50' cy='45' r='25' class='st1'/%3E%3Cpath d='M30 45c0-11 9-20 20-20M40 25c5-5 15-5 20 0M70 45c0 11-9 20-20 20M60 65c-5 5-15 5-20 0' class='st0'/%3E%3Cpath d='M50 70v10M40 80h20' class='st0'/%3E%3Ccircle cx='40' cy='40' r='3' class='st0'/%3E%3Ccircle cx='60' cy='40' r='3' class='st0'/%3E%3Cpath d='M43 50c4 3 10 3 14 0' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.science-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Cpath d='M40 20h20v15c0 10 10 15 10 25v20H30V60c0-10 10-15 10-25V20z' class='st1'/%3E%3Cpath d='M30 65h40M40 20h20' class='st0'/%3E%3Cpath d='M45 30c0 5 5 5 5 10M55 30c0 5-5 5-5 10M45 50c0 5 5 5 5 10M55 50c0 5-5 5-5 10' class='st0'/%3E%3Cpath d='M40 80v5c0 2.5 5 5 10 5s10-2.5 10-5v-5' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.social-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfill:%23ebfbee;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg%3E%3Ccircle cx='30' cy='40' r='10' class='st1'/%3E%3Ccircle cx='70' cy='40' r='10' class='st1'/%3E%3Ccircle cx='50' cy='70' r='10' class='st1'/%3E%3Cpath d='M37 46l7 15M63 46l-7 15M30 50v20M70 50v20' class='st0'/%3E%3Cpath d='M20 25c-5-5-5-10 0-15s10-5 15 0' class='st0'/%3E%3Cpath d='M65 25c5-5 5-10 0-15s-10-5-15 0' class='st0'/%3E%3C/g%3E%3C/svg%3E");
}

.large-model-future {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 340' style='max-width:100%25;height:auto'%3E%3Cstyle%3E.st0%7Bfill:none;stroke:%23333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round%7D.st1%7Bfont-family:sans-serif;font-size:13px%7D.st2%7Bfill:%23f8f9fa;stroke:%23333;stroke-width:1.5%7D.st3%7Bfill:%23e9ecef;stroke:%23333;stroke-width:1.5%7D.st4%7Bfill:%23dee2e6;stroke:%23333;stroke-width:1.5%7D.g1%7Bfill:%23e7f5ff;stroke:%23333;stroke-width:1.5%7D.g2%7Bfill:%23fff4e6;stroke:%23333;stroke-width:1.5%7D.g3%7Bfill:%23f3f0ff;stroke:%23333;stroke-width:1.5%7D%3C/style%3E%3Cg transform='translate(0 5)'%3E%3C!-- 远景地平线 --%3E%3Cpath d='M50 220c30-10 60-10 100-5s80 5 100 0 80-10 150-5' class='st0'/%3E%3C!-- 太阳/未来 --%3E%3Ccircle cx='400' cy='100' r='40' class='st3'/%3E%3Ctext x='400' y='105' text-anchor='middle' class='st1'%3E未来%3C/text%3E%3Cpath d='M370 70l-15-15m90 15l15-15m-45-25v-15m-45 70l-15 15m90-15l15 15' class='st0'/%3E%3C!-- 机器人与人 --%3E%3Ccircle cx='150' cy='180' r='20' class='g1'/%3E%3Cpath d='M140 175c2-2 8-2 10 0m-5 0v5m10 10c2-2 0-8-3-10m-14 0c-3 2-5 8-3 10' class='st0'/%3E%3Cpath d='M150 200v20m-10 0h20' class='st0'/%3E%3Ccircle cx='200' cy='180' r='20' class='g2'/%3E%3Cpath d='M190 175c2-2 8-2 10 0m-5 0v5m10 10c2-2 0-8-3-10m-14 0c-3 2-5 8-3 10' class='st0'/%3E%3Cpath d='M200 200v20m-10 0h20' class='st0'/%3E%3C!-- 连接线 --%3E%3Cpath d='M170 170c10-10 20-10 30 0' class='st0'/%3E%3C!-- 云朵/挑战 --%3E%3Cpath d='M100 100c-10-5-20 0-20 10s5 15 15 15h30c10 0 15-5 15-15s-5-15-15-15c-5 0-15 0-25 5' class='st2'/%3E%3Ctext x='110' y='115' text-anchor='middle' class='st1'%3E挑战%3C/text%3E%3C!-- 平台/机会 --%3E%3Crect x='250' y='160' width='80' height='40' rx='5' ry='5' class='g3'/%3E%3Ctext x='290' y='185' text-anchor='middle' class='st1'%3E机会%3C/text%3E%3C!-- 阶梯 --%3E%3Cpath d='M250 200h80m-80 5h80m-80 5h80m-80 5h80m-80 5h80' class='st0'/%3E%3C!-- 问号和感叹号 --%3E%3Ctext x='80' y='70' class='st1' style='font-size:24px;font-weight:bold'%3E?%3C/text%3E%3Ctext x='110' y='85' class='st1' style='font-size:20px;font-weight:bold'%3E?%3C/text%3E%3Ctext x='300' y='130' class='st1' style='font-size:24px;font-weight:bold'%3E!%3C/text%3E%3Ctext x='330' y='145' class='st1' style='font-size:20px;font-weight:bold'%3E!%3C/text%3E%3C!-- 齿轮/创新 --%3E%3Ccircle cx='250' cy='100' r='25' class='st4'/%3E%3Cpath d='M250 75v50m-25-25h50m-35-35l35 35m0-35l-35 35' class='st0'/%3E%3Ctext x='250' y='105' text-anchor='middle' class='st1'%3E创新%3C/text%3E%3C/g%3E%3C/svg%3E");
}

.conclusion-section {
  .conclusion-card {
    background-color: #fff;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    
    h2 {
      font-size: 1.8rem;
      margin-bottom: 25px;
      position: relative;
      display: inline-block;
      
      &:after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80%;
        height: 3px;
        background-color: #5c6ac4;
        border-radius: 3px;
      }
    }
    
    p {
      font-size: 1.1rem;
      line-height: 1.8;
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style> 