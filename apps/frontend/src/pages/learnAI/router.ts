import { RouteRecordRaw } from 'vue-router';

const userRoutes: Array<RouteRecordRaw> = [
  {
    path: '/learnAI',
    name: 'learnAIHome',
    component: () => import('./views/index.vue'),
    meta: {
      title: 'AI基础知识',
      icon: 'Cpu'
    }
  },
  {
    path: '/learnAI/aiLargeModel',
    name: 'aiLargeModel',
    component: () => import('./views/aiLargeModel.vue'),
    meta: {
      title: 'AI大模型',
      icon: 'Reading'
    }
  },
  {
    path: '/learnAI/deepLearning',
    name: 'deepLearning',
    component: () => import('./views/deepLearning.vue'),
    meta: {
      title: '深度学习',
      icon: 'AI'
    }
  },
  {
    path: '/learnAI/sftAndcot',
    name: 'sftAndcot',
    component: () => import('./views/sftAndcot.vue'),
    meta: {
      title: 'SFT和COT',
      icon: 'AI'
    }
  },
  {
    path: '/learnAI/rag',
    name: 'rag',
    component: () => import('./views/rag.vue'),
    meta: {
        title: 'RAG',
        icon: 'AI'
      }
  },
  {
    path: '/learnAI/mcp',
    name: 'mcp',
    component: () => import('./views/mcp.vue'),
    meta: {
        title: 'MCP',
        icon: 'AI'
      }
  },
  {
    path: '/learnAI/transformer',
    name: 'transformer',
    component: () => import('./views/transformer.vue'),
    meta: {
        title: 'Transformer',
        icon: 'AI'
      }
  },
  {
    path: '/learnAI/multimodel',
    name: 'multimodel',
    component: () => import('./views/multimodel.vue'),
    meta: {
        title: 'Multimodel',
        icon: 'AI'
      }
  },
  {
    path: '/learnAI/knowledgeDistillation',
    name: 'knowledgeDistillation',
    component: () => import('./views/knowledgeDistillation.vue'),
    meta: {
        title: '知识蒸馏',
        icon: 'AI'
      }
  },
  {
    path: '/learnAI/tokens',
    name: 'Tokens',
    component: () => import('./views/tokens.vue'),
    meta: {
      title: 'Tokens概念',
      icon: 'Reading'
    }
  },
  {
    path: '/learnAI/trainingProcess',
    name: 'trainingProcess',
    component: () => import('./views/trainingProcess.vue'),
    meta: {
      title: '大模型训练过程',
      icon: 'AI'
    }
  },
  {
    path: '/learnAI/memory',
    name: 'memory',
    component: () => import('./views/memory.vue'),
    meta: {
      title: 'Memory',
      icon: 'AI'
    }
  }
];
export default userRoutes;




