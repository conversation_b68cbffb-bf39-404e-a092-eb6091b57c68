<template>
  <div class="ai-learning">
    <div class="section-header">
      <h2 class="section-title">AI分享</h2>
      <div class="button-group">
        <!-- <el-button type="primary" text class="action-btn">
          <el-icon><Plus /></el-icon>添加资源
        </el-button> -->
        <el-button type="info" text class="action-btn" @click="handleViewMore">
          更多<el-icon class="right-icon"><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
    <div class="learning-container">
      <div class="resource-list">
        <div v-for="(item, index) in resources" :key="index" class="resource-item">
          <el-card shadow="hover" class="resource-card" @click="handleResourceClick(item)">
            <div class="resource-content" :style="{ maxWidth: `calc(100% - ${100 + (item.star === 1 ? 20 : 0) + (item.top === 1 ? 20 : 0) + (item.isNew ? 20 : 0)}px)` }">
              <div class="title-container" :ref="(el) => { if (el) titleContainers[index] = el as HTMLElement }">
                <el-tag size="small" effect="plain" class="resource-tag">{{ item.tag }}</el-tag>
                <el-tooltip
                  v-if="isTitleOverflow(index, item.title, item)"
                  :content="item.title"
                  placement="top"
                  :show-after="300"
                  :hide-after="0"
                >
                  <h3 class="resource-title">{{ item.title }}</h3>
                </el-tooltip>
                <h3 v-else class="resource-title">{{ item.title }}</h3>
              </div>
              <p class="resource-desc" :style="{ width: `calc(100% + ${100 + (item.star === 1 ? 20 : 0) + (item.top === 1 ? 20 : 0) + (item.isNew ? 20 : 0)}px)` }">{{ item.description }}</p>
            </div>
            <div class="date">
              <el-tag v-if="item.star === 1" size="small" type="warning" effect="plain" class="status-tag">
                <el-icon><StarFilled /></el-icon>
              </el-tag>
              <el-tag v-if="item.top === 1" size="small" type="primary" effect="plain" class="status-tag">
                置顶
              </el-tag>
              <el-tag v-if="item.isNew" size="small" type="danger" effect="plain" class="new-tag">new</el-tag>
              {{ item.date }}
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue';
import {ArrowRight, StarFilled, Top} from '@element-plus/icons-vue';
import {useRouter} from 'vue-router';
import {getAIWikiList} from '../../../pages/aiKnowledgeManager/request';
import type {AIWiki} from '../../../pages/aiKnowledgeManager/types';
import {ElMessage} from 'element-plus';

const router = useRouter();
const pageNo = ref(1);
const pageSize = ref(6);
const loading = ref(false);

// 学习资源
interface Resource {
  id: string;
  title: string;
  description: string;
  date: string;
  tag: string;
  wikiUrl: string;
  isNew?: boolean;
  star?: number;
  top?: number;
}

// 处理资源点击事件
const handleResourceClick = (item: Resource) => {
  if (item.wikiUrl) {
    window.open(item.wikiUrl, '_blank');
  }
};

// 处理查看更多点击事件
const handleViewMore = () => {
  router.push('/aiWorkshop/aiCodingShare');
};

// 默认学习资源数据
const resources = ref<Resource[]>([]);

// 判断是否为最近3天内的内容
const isWithinThreeDays = (timestamp: number): boolean => {
  const now = new Date();
  const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
  const itemDate = new Date(timestamp * 1000);
  return itemDate >= threeDaysAgo;
};

// 获取AI Wiki列表
const fetchAIWikiList = async () => {
  try {
    loading.value = true;
    const res = await getAIWikiList({
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      wikiType: 5, // AI分享类型
    });
    if (res.code === 0 && res.data?.records) {
      resources.value = res.data.records.map((item: AIWiki) => ({
        id: item.id.toString(),
        title: item.title,
        description: item.description,
        date: item.utime ? formatTimestamp(item.utime) : '',
        tag: item.tags?.length ? item.tags[0] : '基础',
        wikiUrl: item.wikiUrl,
        isNew: item.ctime ? isWithinThreeDays(item.ctime) : false,
        star: item.star,
        top: item.top
      }));
    }
  } catch (error) {
    ElMessage.error('列表加载失败');
    console.error('获取AI Wiki列表失败:', error);
    // 加载失败时显示默认数据
    resources.value = [
      {
        id: '1',
        title: '深度学习',
        description: '深度学习是人工智能的一种，它模仿人类大脑的神经网络结构来学习和理解世界。',
        date: '',
        tag: '基础',
        wikiUrl: 'https://km.sankuai.com',
        isNew: false
      }
    ];
  } finally {
    loading.value = false;
  }
};

// 格式化时间戳为YYYY-MM-DD
const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 判断标题是否超出
const titleContainers = ref<Record<number, HTMLElement | null>>({});

const isTitleOverflow = (index: number, title: string, item: Resource): boolean => {
  const containerElement = titleContainers.value[index];
  if (!containerElement) return false;

  // 获取当前容器中的标签元素宽度
  const tagElement = containerElement.querySelector('.resource-tag');
  const tagWidth = tagElement ? tagElement.getBoundingClientRect().width : 0;

  // 计算状态标签的宽度
  let statusTagsWidth = 0;
  if (item.star === 1) statusTagsWidth += 24; // 星标标签宽度
  if (item.top === 1) statusTagsWidth += 32; // 置顶标签宽度
  if (item.isNew) statusTagsWidth += 32; // new标签宽度

  // 计算标题可用宽度（容器宽度减去标签宽度和间距）
  const containerWidth = containerElement.getBoundingClientRect().width;
  const availableWidth = containerWidth - tagWidth - statusTagsWidth - 12; // 增加间距

  // 创建临时标题元素测量宽度
  const titleElement = document.createElement('h3');
  titleElement.className = 'resource-title';
  titleElement.style.visibility = 'hidden';
  titleElement.style.position = 'absolute';
  titleElement.style.whiteSpace = 'nowrap';
  titleElement.textContent = title;
  document.body.appendChild(titleElement);

  const titleWidth = titleElement.offsetWidth;
  document.body.removeChild(titleElement);
  return titleWidth > availableWidth;
};

// 页面挂载时获取数据
onMounted(() => {
  fetchAIWikiList();
});
</script>

<script lang="ts">
export default {
  name: 'AIWikis'
};
</script>

<style lang="scss" scoped>
.ai-learning {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .button-group {
      display: flex;
      gap: 4px;
      .action-btn {
        padding: 4px 6px;
        .right-icon {
          margin-left: 2px;
        }
      }
    }
  }
  .section-title {
    font-size: 15px;
    font-weight: 600;
    margin: 0;
    color: #303133;
    position: relative;
    padding-left: 8px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 12px;
      background: linear-gradient(to bottom, #409EFF, #36cfc9);
      border-radius: 2px;
    }
  }
  .learning-container {
    .resource-list {
      display: flex;
      flex-direction: column;
      gap: 5px;
      .resource-item {
        height: 65px !important;
        .resource-card {
          border-radius: 6px;
          // transition: all 0.2s ease;
          position: relative;
          cursor: pointer;
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          }
          :deep(.el-card__body) {
            display: flex;
            padding: 6px 8px;
          }
          .date {
            position: absolute;
            top: 6px;
            right: 8px;
            font-size: 11px;
            color: #909399;
            display: flex;
            align-items: center;
            gap: 4px;
            .status-tag {
              height: 16px;
              padding: 0 4px;
              line-height: 14px;
              font-size: 11px;
              border-radius: 2px;
              .el-icon {
                font-size: 12px;
              }
            }
            .new-tag {
              height: 16px;
              padding: 0 4px;
              line-height: 14px;
              font-size: 11px;
              border-radius: 2px;
            }
            &::before {
              content: "\e78f"; /* 使用Element Plus的时钟图标 */
              font-family: 'element-icons' !important;
              margin-right: 3px;
              font-size: 10px;
            }
          }
          .resource-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            max-width: calc(100% - 160px); // 减去日期区域的宽度
            .title-container {
              display: flex;
              align-items: center;
              gap: 4px;
              margin-bottom: 1px;
              min-width: 0; // 确保flex子元素可以正确收缩
            }
            .resource-tag {
              border-radius: 2px;
              padding: 0 4px;
              height: 16px;
              line-height: 14px;
              font-size: 11px;
              flex-shrink: 0;
              &.el-tag--plain {
                background-color: #ecf5ff;
                border-color: #d9ecff;
                color: #409EFF;
              }
            }
            .resource-title {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin: 0;
              padding: 0;
              position: relative;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              flex: 1;
              min-width: 0; // 确保文本可以正确收缩
            }
            .resource-desc {
              font-size: 12px;
              color: #606266;
              margin: 0;
              margin-top: 5px;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              // width: calc(100% + 160px);
            }
          }
        }
      }
    }
  }
}
</style>