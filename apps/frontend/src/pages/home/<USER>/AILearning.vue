<template>
  <div class="ai-learning">
    <div class="section-header">
      <h2 class="section-title">AI基础知识</h2>
      <div class="button-group">
        <!-- <el-button type="primary" text class="action-btn">
          <el-icon><Plus /></el-icon>添加资源
        </el-button> -->
        <el-button type="info" text class="action-btn" @click="handleViewMore">
          更多<el-icon class="right-icon"><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
    
    <div class="learning-container">
      <div class="resource-list">
        <div v-for="(item, index) in learningResources" :key="index" class="resource-item">
          <el-card shadow="hover" class="resource-card" @click="handleResourceClick(item)">
            <!-- <span class="date">{{ item.date }}</span> -->
            <!-- <div class="resource-icon" :style="{ backgroundColor: item.bgColor }">
              <el-icon :size="16">
                <component :is="item.icon" />
              </el-icon>
            </div> -->
            <div class="resource-content">
              <div class="title-container" :ref="el => titleContainers[index] = el">
                <el-tag size="small" effect="plain" class="resource-tag">{{ item.tag }}</el-tag>
                <el-tooltip
                    v-if="isTitleOverflow(index, item.title)"
                    :content="item.title"
                    placement="top"
                    :show-after="300"
                    :hide-after="0"
                >
                  <h3 class="resource-title">{{ item.title }}</h3>
                </el-tooltip>
                <h3 v-else class="resource-title">{{ item.title }}</h3>
              </div>
              <p class="resource-desc">{{ item.description }}</p>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ArrowRight } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 学习资源
interface LearningResource {
  id: string;
  title: string;
  description: string;
  date: string;
  tag: string;
  route: string;
}

// 处理资源点击事件
const handleResourceClick = (item: LearningResource) => {
  if (item.route) {
    router.push(item.route);
  }
};

// 处理查看更多点击事件
const handleViewMore = () => {
  router.push('/learnAI');
};

// 判断标题是否超出
const titleContainers = ref<Record<number, HTMLElement | null>>({});

const isTitleOverflow = (index: number, title: string): boolean => {
  const containerElement = titleContainers.value[index];
  if (!containerElement) return false;

  // 获取当前容器中的标签元素宽度
  const tagElement = containerElement.querySelector('.resource-tag');
  const tagWidth = tagElement ? tagElement.getBoundingClientRect().width : 0;

  // 计算标题可用宽度（容器宽度减去标签宽度和间距）
  const containerWidth = containerElement.getBoundingClientRect().width;
  const availableWidth = containerWidth - tagWidth - 4; // 4px 是 gap 的值

  // 创建临时标题元素测量宽度
  const titleElement = document.createElement('h3');
  titleElement.className = 'resource-title';
  titleElement.style.visibility = 'hidden';
  titleElement.style.position = 'absolute';
  titleElement.style.whiteSpace = 'nowrap';
  titleElement.textContent = title;
  document.body.appendChild(titleElement);

  const titleWidth = titleElement.offsetWidth;
  document.body.removeChild(titleElement);
  return titleWidth > availableWidth;
};

// 默认学习资源数据
const learningResources = ref<LearningResource[]>([
  {
    id: '1',
    title: '深度学习',
    description: '深度学习是人工智能的一种，它模仿人类大脑的神经网络结构来学习和理解世界。',
    date: '',
    tag: '基础',
    route: '/learnAI/deepLearning'
  },
  {
    id: '2',
    title: 'SFT和CoT',
    description: '监督微调（Supervised Fine-Tuning，SFT）是AI大模型训练过程中的重要环节，它就像是给一位博学的老师进行专业培训。',
    date: '',
    tag: '基础',
    route: '/learnAI/sftAndcot'
  },
  {
    id: '3',
    title: 'RAG：让AI更聪明的秘密武器',
    description: 'RAG就像是给AI装上了一个超级图书馆卡。它不再只依赖自己"脑中"的知识，而是可以实时查阅外部资料，找到最相关的信息，然后生成更准确的回答。',
    date: '',
    tag: '基础',
    route: '/learnAI/rag'
  },
  {
    id: '4',
    title: 'MCP: 模型上下文协议',
    description: '没有MCP的AI像是一个被困在盒子里的智者—只能依靠自己已有的知识有了MCP的AI则像是一个可以查阅图书馆和使用工具的助手—能够获取最新信息并采取行动',
    date: '',
    tag: '基础',
    route: '/learnAI/mcp'
  },
  {
    id: '5',
    title: '多模态AI：感知世界的新方式',
    description: '想象一下，如果AI只能阅读文字，却看不见图像、听不到声音，就像一个只有单一感官的人，它对世界的理解会非常有限。多模态AI就像拥有多种感官的人类，它可以同时理解文字、图像和声音，从而获得更全面、更丰富的信息。',
    date: '',
    tag: '基础',
    route: '/learnAI/multimodel'
  },
  {
    id: '6',
    title: '理解Transformer：AI大模型的核心引擎',
    description: 'Transformer是现代大型语言模型（如GPT、BERT等）的核心架构，2017年由Google研究团队提出。它彻底改变了自然语言处理领域，让AI系统能更好地理解和生成人类语言。',
    date: '',
    tag: '基础',
    route: '/learnAI/transformer'
  },
]);
</script>

<script lang="ts">
export default {
  name: 'AILearning'
};
</script>

<style lang="scss" scoped>
.ai-learning {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .button-group {
      display: flex;
      gap: 4px;
      
      .action-btn {
        padding: 4px 6px;
        
        .right-icon {
          margin-left: 2px;
        }
      }
    }
  }
  
  .section-title {
    font-size: 15px;
    font-weight: 600;
    margin: 0;
    color: #303133;
    position: relative;
    padding-left: 8px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 12px;
      background: linear-gradient(to bottom, #409EFF, #36cfc9);
      border-radius: 2px;
    }
  }
  
  .learning-container {
    .resource-list {
      display: flex;
      flex-direction: column;
      gap: 5px;
      
      .resource-item {
        height: 65px !important;
        .resource-card {
          border-radius: 6px;
          // transition: all 0.2s ease;
          position: relative;
          cursor: pointer;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          }
          
          :deep(.el-card__body) {
            display: flex;
            padding: 6px 8px;
          }
          
          .date {
            position: absolute;
            top: 6px;
            right: 8px;
            font-size: 11px;
            color: #909399;
            display: flex;
            align-items: center;
            
            &::before {
              content: "\e78f"; /* 使用Element Plus的时钟图标 */
              font-family: 'element-icons' !important;
              margin-right: 3px;
              font-size: 10px;
            }
          }
          
          .resource-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            heignt: 100%;
            max-width: calc(100%);
            
            .title-container {
              display: flex;
              align-items: center;
              gap: 4px;
              margin-bottom: 1px;
              min-width: 0; // 确保flex子元素可以正确收缩
            }
            
            .resource-tag {
              border-radius: 2px;
              padding: 0 4px;
              height: 16px;
              line-height: 14px;
              font-size: 11px;
              flex-shrink: 0;
              
              &.el-tag--plain {
                background-color: #ecf5ff;
                border-color: #d9ecff;
                color: #409EFF;
              }
            }
            
            .resource-title {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin: 0;
              padding: 0;
              position: relative;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              flex: 1;
              min-width: 0; // 确保文本可以正确收缩
            }
            
            .resource-desc {
              font-size: 12px;
              color: #606266;
              margin: 0;
              margin-top: 5px;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
</style> 