<template>
  <div class="home-page">
    <!-- 欢迎信息区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>履约平台技术部AI门户</h1>
          <p class="welcome-message"><b>该平台作为履约平台技术部AI相关的门户<br>包含AI产品、AI助手、AI学习资料或分享、AI coding、AI资讯动态等内容<br>帮助您更好地完成工作</b></p>
        </div>
      </div>
    </div>

    <!-- 应用分类组件 -->
    <!-- <component :is="AppCategories.default || AppCategories" /> -->

    <!-- 加急建设中标签 -->
    <!-- <div class="building-section">
      <div class="building-tag">
        <el-icon><Van /></el-icon>
        <span>加急建设中，敬请期待</span>
      </div>
    </div> -->

    <!-- 探索实践和学习资讯区域 -->
    <div class="bottom-section">
      <div class="section-col">
        <component :is="AILearning.default || AILearning" />
      </div>
      <div class="section-col">
        <component :is="AIProducts.default || AIProducts" />
      </div>
      <div class="section-col">
        <component :is="AIWikis.default || AIWikis" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AILearning from '../components/AILearning.vue';
import AIProducts from '../components/AIProducts.vue';
import AIWikis from '../components/AIWikis.vue';

</script>

<style lang="scss" scoped>
.home-page {
  padding: 0 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 160px);
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #303133;
    position: relative;
    padding-left: 12px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: linear-gradient(to bottom, #409EFF, #36cfc9);
      border-radius: 2px;
    }
  }
  
  .welcome-section {
    margin-bottom: 20px;
    min-height: 380px;
    height: 38vh;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    background: url('@/assets/images/home-img-2.png') no-repeat center center;
    background-size: cover;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(80deg, rgba(4, 113, 225, 0.7) 20%, rgba(38, 109, 246, 0.6) 70%, rgba(7, 113, 220, 0.5) 100%);
    }
    
    .welcome-content {
      position: relative;
      z-index: 1;
      height: 100%;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .welcome-text {
        max-width: 800px;
        text-align: center;
        padding: 0 40px;
        
        h1 {
          margin: 0 0 20px 0;
          font-size: 45px;
          font-weight: 600;
          color: #ffffff;
        }
        
        .welcome-message {
          font-size: 20px;
          line-height: 1.6;
          color: #ffffff;
          margin-bottom: 30px;
          opacity: 0.9;
        }
      }
    }
  }
  
  .building-section {
    margin-top: 20px;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 0;
    
    .building-tag {
      display: flex;
      align-items: center;
      background-color: #fef6e4;
      border: 1px solid #fddf90;
      color: #e6a23c;
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 4px;
      margin-bottom: 0;
      
      .el-icon {
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }
  
  .bottom-section {
    display: flex;
    gap: 20px;
    margin-top: 5px;
    padding: 10px 0 0 0;
    
    .section-col {
      flex: 1;
      width: calc(100% / 3);
      background-color: #fff;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      height: 460px;
      overflow-y: auto;
      
      /* 定制滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(144, 147, 153, 0.3);
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
      }
    }
  }
}

@media (max-width: 1200px) {
  .bottom-section {
    flex-wrap: wrap;
    
    .section-col {
      width: calc(50% - 10px);
      flex: 0 0 calc(50% - 10px);
      
      &:last-child {
        width: 100%;
        flex: 0 0 100%;
        margin-top: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .home-page {
    .welcome-section {
      .welcome-card {
        .welcome-content {
          flex-direction: column;
          max-height: none;
          
          .welcome-text {
            padding: 20px;
          }
          
          .welcome-image {
            width: 100%;
            padding: 0 20px 20px;
          }
        }
      }
    }
    
    .bottom-section {
      flex-direction: column;
      
      .section-col {
        width: 100%;
        flex: 0 0 100%;
        margin-bottom: 20px;
        height: auto;
        max-height: 480px;
        
        &:last-child {
          margin-top: 0;
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>