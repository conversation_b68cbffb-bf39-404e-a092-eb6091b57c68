<template>
  <div class="app-categories">
    <!-- 应用分类功能区 -->
    <div class="favorites-section">
      <div class="categories-container">
        <div class="section-header">
          <div class="title-container">
            <h2 class="section-title">应用分类</h2>
          </div>
          <div class="header-buttons">
            <div class="guide-text">
              <p>操作说明：点击应用图标可跳转菜单或外部链接，可自定义添加分类和应用，数据存储在本地</p>
            </div>
            <el-button-group>
              <el-button type="warning" text @click="showResetConfirm">
                <el-icon><RefreshRight /></el-icon>重置
              </el-button>
              <el-button type="primary" text @click="showAddCategoryDialog" :key="`add-category-${Date.now()}`">
                <el-icon><Plus /></el-icon>添加分类
              </el-button>
            </el-button-group>
          </div>
        </div>
        
        <div v-if="!hasCategories" class="no-categories">
          <el-empty description="暂无分类" />
        </div>
        
        <div v-else class="categories-row">
          <div v-for="category in categories" :key="category.id" class="category-section">
            <div class="category-header">
              <h3 class="category-title">{{ category.name }}</h3>
              <div class="header-actions">
                <el-button
                  v-if="!isDefaultCategory(category.id)"
                  class="action-btn"
                  type="danger"
                  text
                  @click="deleteCategory(category.id)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
            
            <div class="items-container">
              <div 
                v-for="item in getCategoryItems(category.id)" 
                :key="item.id" 
                class="favorite-card"
                @click="navigateTo(item.path)"
              >
                <el-tooltip 
                  :content="item.description || '暂无描述'" 
                  placement="top" 
                  :show-after="300"
                  :hide-after="0"
                >
                  <div class="card-icon" :style="{ 
                    color: item.color, 
                    borderColor: `${item.color}80`, 
                    backgroundColor: `${item.color}15` 
                  }">
                    <el-icon>
                      <component :is="item.icon" />
                    </el-icon>
                  </div>
                </el-tooltip>
                
                <div class="card-content">
                  <div class="favorite-title">{{ item.title }}</div>
                  <div class="favorite-desc">{{ item.description }}</div>
                </div>
                
                <div class="card-actions" @click.stop>
                  <el-dropdown @command="handleItemAction($event, item, category.id)">
                    <el-button type="primary" text size="small">
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="edit">编辑</el-dropdown-item>
                        <el-dropdown-item command="delete">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
              
              <!-- 添加应用按钮 -->
              <div 
                class="favorite-card add-card" 
                @click="showAddItemDialog(category.id)"
                :key="`add-item-${category.id}-${Date.now()}`"
              >
                <div class="card-icon add-icon">
                  <el-icon><Plus /></el-icon>
                </div>
                <div class="card-content">
                  <div class="favorite-title">添加应用</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加分类对话框 -->
    <el-dialog v-model="categoryDialogVisible" title="添加分类" width="30%" @closed="resetNewCategory">
      <el-form>
        <el-form-item label="分类名称">
          <el-input v-model="newCategory.name" placeholder="请输入分类名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelCategoryDialog">取消</el-button>
        <el-button type="primary" @click="addCategory">确定</el-button>
      </template>
    </el-dialog>

    <!-- 添加/编辑应用对话框 -->
    <el-dialog v-model="itemDialogVisible" :title="isEditingItem ? '编辑应用' : '添加应用'" width="40%" @closed="resetCurrentItem">
      <el-form :model="currentItem" :rules="itemRules" ref="itemFormRef" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="currentItem.title" placeholder="请输入标题" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input v-model="currentItem.description" placeholder="请输入描述" />
        </el-form-item>
        
        <el-form-item label="路径" prop="path">
          <el-input v-model="currentItem.path" placeholder="请输入路径">
            <template #append>
              <el-select v-model="selectedMenu" placeholder="选择菜单" @change="handleMenuChange" style="width: 120px;">
                <div v-for="menu in menuList" :key="menu.nodeId">
                  <!-- 有子菜单的情况 -->
                  <el-option-group v-if="menu.childrenMenuCount > 0" :label="menu.nodeName">
                    <el-option 
                      v-for="subMenu in menu.childrenMenuList" 
                      :key="subMenu.nodeId"
                      :label="subMenu.nodeName"
                      :value="convertMenuUrlToPath(subMenu.menuURL)"
                    />
                  </el-option-group>
                  
                  <!-- 没有子菜单的情况 -->
                  <el-option 
                    v-else 
                    :label="menu.nodeName" 
                    :value="convertMenuUrlToPath(menu.menuURL)" 
                  />
                </div>
              </el-select>
            </template>
          </el-input>
          <div class="path-hint">支持输入内部路由路径或外部URL（以http/https开头）</div>
        </el-form-item>
        
        <el-divider class="simple-divider" />
        
        <el-form-item label="图标" prop="icon">
          <el-select v-model="currentItem.icon" style="width: 100%;">
            <el-option 
              v-for="icon in availableIcons" 
              :key="icon" 
              :label="icon" 
              :value="icon"
            >
              <div style="display: flex; align-items: center;">
                <el-icon><component :is="icon" /></el-icon>
                <span style="margin-left: 8px;">{{ icon }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="图标颜色" prop="color">
          <el-color-picker v-model="currentItem.color" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelItemDialog">取消</el-button>
        <el-button type="primary" @click="saveItem">确定</el-button>
      </template>
    </el-dialog>

    <!-- 删除分类确认对话框 -->
    <el-dialog v-model="deleteCategoryDialogVisible" title="删除分类" width="30%">
      <span>确定要删除"{{ categoryToDelete?.name }}"分类吗？该分类下的所有应用也会被删除。</span>
      <template #footer>
        <el-button @click="deleteCategoryDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDeleteCategory">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { 
  Plus, MoreFilled, Delete, 
  Document, Compass, Goods, Star, Setting, User, PieChart, List, 
  Edit, View, Search, House, Link, Calendar, Bell, Briefcase, 
  ChatLineSquare, Collection, Connection, CreditCard, Cpu,
  DataAnalysis, DataBoard, Discount, Finished, Flag,
  FolderOpened, Grid, Guide, HelpFilled, HomeFilled, Key, Location,
  Message, Monitor, Operation, Platform, Promotion, Reading,
  School, Service, Ship, Shop, Ticket, TrendCharts, Wallet,
  RefreshRight, Van
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getMenuTree, convertMenuUrlToPath, MenuItem } from '../../../shared/services/menuService';

const router = useRouter();

// 分类数据
interface Category {
  id: string;
  name: string;
}

// 应用项目数据
interface FavoriteItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  path: string;
  categoryId: string;
}

// 可用图标列表
const availableIcons = [
  'Document', 'Compass', 'Goods', 'Star', 'Setting', 'User', 'PieChart', 'List',
  'Edit', 'View', 'Search', 'House', 'Link', 'Calendar', 'Bell', 'Briefcase',
  'ChatLineSquare', 'Collection', 'Connection', 'CreditCard', 'Cpu',
  'DataAnalysis', 'DataBoard', 'Discount', 'Finished', 'Flag',
  'FolderOpened', 'Grid', 'Guide', 'HelpFilled', 'HomeFilled', 'Key', 'Location',
  'Message', 'Monitor', 'Operation', 'Platform', 'Promotion', 'Reading',
  'School', 'Service', 'Ship', 'Shop', 'Ticket', 'TrendCharts', 'Wallet'
];

// 选中的菜单路径
const selectedMenu = ref('');

// 默认分类
const defaultCategories: Category[] = [
  { id: 'scope', name: '我的收藏' },
  { id: 'product', name: '提效工具' }
];

// 默认应用项目
const defaultItems: FavoriteItem[] = [
  { 
    id: '3',
    title: 'AI基础知识', 
    description: '基础AI知识学习资源', 
    icon: 'Cpu', 
    color: '#E6A23C',
    path: '/learnAI',
    categoryId: 'scope'
  },
  { 
    id: '4',
    title: '范围工具', 
    description: '绘制配送范围', 
    icon: 'Location', 
    color: '#F56C6C',
    path: 'https://peisong.sankuai.com/api/deliverygrid/public/draw_poly/index.html',
    categoryId: 'product'
  }
];

// 表单验证规则
const itemRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '请输入路径', trigger: 'blur' }
  ]
};

// 状态管理
const categories = ref<Category[]>([]);
const favoriteItems = ref<FavoriteItem[]>([]);
const categoryDialogVisible = ref(false);
const itemDialogVisible = ref(false);
const isEditingItem = ref(false);
const currentCategoryId = ref('');
const newCategory = ref<Omit<Category, 'id'>>({ name: '' });
const itemFormRef = ref();
const currentItem = ref<Omit<FavoriteItem, 'id' | 'categoryId'>>({
  title: '',
  description: '',
  icon: 'Star',
  color: '#409EFF',
  path: ''
});
const deleteCategoryDialogVisible = ref(false);
const categoryToDelete = ref<Category | null>(null);

// 是否有分类
const hasCategories = computed(() => categories.value.length > 0);

// 菜单数据
const menuList = ref<MenuItem[]>([]);
const loading = ref(false);
const error = ref(false);

// 本地存储键名
const STORAGE_KEY_PREFIX = 'app_home_v2_';
const STORAGE_KEY_CATEGORIES = `${STORAGE_KEY_PREFIX}categories`;
const STORAGE_KEY_ITEMS = `${STORAGE_KEY_PREFIX}items`;

// 获取菜单数据
const fetchMenuData = async () => {
  loading.value = true;
  error.value = false;
  
  try {
    const menuData = await getMenuTree();
    menuList.value = menuData.menuFolderList || [];
    loading.value = false;
  } catch (err) {
    console.error('获取菜单失败:', err);
    error.value = true;
    loading.value = false;
    ElMessage.error('获取菜单失败，请重试');
  }
};

// 初始化数据
onMounted(() => {
  // 从本地存储加载数据，如果没有则使用默认数据
  const savedCategories = localStorage.getItem(STORAGE_KEY_CATEGORIES);
  const savedItems = localStorage.getItem(STORAGE_KEY_ITEMS);
  
  if (savedCategories) {
    categories.value = JSON.parse(savedCategories);
  } else {
    categories.value = [...defaultCategories];
  }
  
  if (savedItems) {
    favoriteItems.value = JSON.parse(savedItems);
  } else {
    favoriteItems.value = [...defaultItems];
  }

  // 清理旧缓存
  cleanupOldCache();
  
  // 获取菜单数据
  fetchMenuData();
});

// 清理旧缓存
const cleanupOldCache = () => {
  // 删除旧版本的缓存
  const oldKeys = ['favoriteCategories', 'favoriteItems'];
  oldKeys.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
    }
  });
};

// 保存数据到本地存储
const saveToLocalStorage = () => {
  localStorage.setItem(STORAGE_KEY_CATEGORIES, JSON.stringify(categories.value));
  localStorage.setItem(STORAGE_KEY_ITEMS, JSON.stringify(favoriteItems.value));
};

// 获取指定分类的应用项目
const getCategoryItems = (categoryId: string) => {
  return favoriteItems.value.filter(item => item.categoryId === categoryId);
};

// 显示添加分类对话框
const showAddCategoryDialog = () => {
  if (categories.value.length >= 6) {
    ElMessage.warning('最多只能添加6个分类');
    return;
  }
  
  newCategory.value = { name: '' };
  categoryDialogVisible.value = true;
};

// 添加分类
const addCategory = () => {
  if (!newCategory.value.name.trim()) {
    ElMessage.warning('分类名称不能为空');
    return;
  }
  
  if (categories.value.length >= 6) {
    ElMessage.warning('最多只能添加6个分类');
    return;
  }
  
  const id = `custom_${Date.now()}`;
  categories.value.push({
    id,
    name: newCategory.value.name
  });
  
  saveToLocalStorage();
  categoryDialogVisible.value = false;
};

// 处理菜单选择变更
const handleMenuChange = (path: string) => {
  currentItem.value.path = path;
  selectedMenu.value = '';
};

// 显示添加应用项目对话框
const showAddItemDialog = (categoryId: string) => {
  resetCurrentItem();
  isEditingItem.value = false;
  currentCategoryId.value = categoryId;
  itemDialogVisible.value = true;
};

// 显示编辑应用项目对话框
const showEditItemDialog = (item: FavoriteItem, categoryId: string) => {
  resetCurrentItem();
  isEditingItem.value = true;
  currentCategoryId.value = categoryId;
  currentItem.value = {
    title: item.title,
    description: item.description,
    icon: item.icon,
    color: item.color,
    path: item.path
  };
  itemDialogVisible.value = true;
};

// 保存应用项目
const saveItem = async () => {
  if (!itemFormRef.value) return;
  
  try {
    await itemFormRef.value.validate();
    
    if (isEditingItem.value) {
      // 编辑现有项目
      const index = favoriteItems.value.findIndex(item => 
        item.title === currentItem.value.title && item.categoryId === currentCategoryId.value
      );
      
      if (index !== -1) {
        favoriteItems.value[index] = {
          ...favoriteItems.value[index],
          ...currentItem.value
        };
      }
    } else {
      // 添加新项目
      favoriteItems.value.push({
        id: `item_${Date.now()}`,
        ...currentItem.value,
        categoryId: currentCategoryId.value
      });
    }
    
    saveToLocalStorage();
    itemDialogVisible.value = false;
    ElMessage.success(isEditingItem.value ? '应用编辑成功' : '应用添加成功');
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 处理应用项目操作
const handleItemAction = (command: string, item: FavoriteItem, categoryId: string) => {
  if (command === 'edit') {
    showEditItemDialog(item, categoryId);
  } else if (command === 'delete') {
    ElMessageBox.confirm(
      '确定要删除这个应用吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      const index = favoriteItems.value.findIndex(i => i.id === item.id);
      if (index !== -1) {
        favoriteItems.value.splice(index, 1);
        saveToLocalStorage();
        ElMessage.success('删除成功');
      }
    }).catch(() => {});
  }
};

// 导航到指定路径
const navigateTo = (path: string) => {
  if (path.startsWith('http://') || path.startsWith('https://')) {
    // 如果是外部URL，则在新窗口打开
    window.open(path, '_blank');
  } else {
    // 否则使用路由导航
    router.push(path);
  }
};

// 判断是否为默认分类
const isDefaultCategory = (categoryId: string) => {
  return defaultCategories.some(category => category.id === categoryId);
};

// 显示删除分类对话框
const deleteCategory = (categoryId: string) => {
  const category = categories.value.find(c => c.id === categoryId);
  if (category) {
    categoryToDelete.value = category;
    deleteCategoryDialogVisible.value = true;
  }
};

// 确认删除分类
const confirmDeleteCategory = () => {
  if (!categoryToDelete.value) return;
  
  const index = categories.value.findIndex(c => c.id === categoryToDelete.value?.id);
  if (index !== -1) {
    // 删除该分类下的所有应用
    const categoryId = categoryToDelete.value.id;
    favoriteItems.value = favoriteItems.value.filter(item => item.categoryId !== categoryId);
    
    // 删除分类
    categories.value.splice(index, 1);
    saveToLocalStorage();
    ElMessage.success('分类删除成功');
  }
  
  deleteCategoryDialogVisible.value = false;
};

// 重置新分类
const resetNewCategory = () => {
  newCategory.value = { name: '' };
};

// 重置当前项目
const resetCurrentItem = () => {
  currentItem.value = {
    title: '',
    description: '',
    icon: 'Star',
    color: '#409EFF',
    path: ''
  };
  currentCategoryId.value = '';
  isEditingItem.value = false;
  selectedMenu.value = '';
};

// 取消添加分类对话框
const cancelCategoryDialog = () => {
  categoryDialogVisible.value = false;
  resetNewCategory();
};

// 取消添加/编辑应用对话框
const cancelItemDialog = () => {
  itemDialogVisible.value = false;
  resetCurrentItem();
};

// 显示重置确认对话框
const showResetConfirm = () => {
  ElMessageBox.confirm(
    '确定要重置应用分类和应用列表吗？所有自定义内容将被清除，恢复到默认状态。',
    '重置确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    resetToDefault();
  }).catch(() => {});
};

// 重置到默认状态
const resetToDefault = () => {
  // 清除本地存储
  localStorage.removeItem(STORAGE_KEY_CATEGORIES);
  localStorage.removeItem(STORAGE_KEY_ITEMS);
  
  // 重置为默认数据
  categories.value = [...defaultCategories];
  favoriteItems.value = [...defaultItems];
  
  ElMessage.success('应用分类已重置为默认状态');
};
</script>

<script lang="ts">
export default {
  name: 'AppCategories'
};
</script>

<style lang="scss" scoped>
.app-categories {
  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #303133;
    position: relative;
    padding-left: 12px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: linear-gradient(to bottom, #409EFF, #36cfc9);
      border-radius: 2px;
    }
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .title-container {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .header-buttons {
      display: flex;
      align-items: center;
      gap: 5px;
      
      .guide-text {
        margin-right: 15px;
        
        p {
          margin: 0;
          font-size: 12px;
          color: #909399;
          line-height: 1.4;
        }
      }
    }
  }
  
  .favorites-section {
    margin-bottom: 15px;
    
    .categories-container {
      background-color: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      
      .no-categories {
        padding: 30px 0;
      }
      
      .categories-row {
        display: flex;
        flex-wrap: nowrap;
        gap: 16px;
        overflow-x: auto;
        padding-bottom: 6px;
        
        &::-webkit-scrollbar {
          height: 4px;
        }
        
        &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
        }
        
        &::-webkit-scrollbar-track {
          background-color: rgba(0, 0, 0, 0.05);
          border-radius: 4px;
        }
      }
    }
    
    .category-section {
      flex: 0 0 calc((100% - 64px) / 5);
      min-width: 200px;
      max-width: 230px;
      height: 210px;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
      border: 1px solid #ebeef5;
      overflow: hidden;
      
      .category-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
        height: 32px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
        
        .category-title {
          font-size: 13px;
          font-weight: 500;
          color: #303133;
          margin: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex: 1;
        }
        
        .header-actions {
          display: flex;
          align-items: center;
          gap: 5px;
          
          .action-btn {
            padding: 0;
            margin: 0;
            height: 24px;
            width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
      
      .el-divider {
        margin: 0;
      }
      
      .items-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 6px;
        flex: 1;
        align-content: flex-start;
        padding: 12px 8px;
        overflow-y: auto;
        
        &::-webkit-scrollbar {
          width: 4px;
        }
        
        &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
        }
        
        &::-webkit-scrollbar-track {
          background-color: rgba(0, 0, 0, 0.05);
          border-radius: 4px;
        }
      }
    }
    
    .favorite-card {
      background-color: transparent;
      border-radius: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      box-shadow: none;
      position: relative;
      width: 60px;
      height: 70px;
      margin: 0;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: none;
      }
      
      &.add-card {
        .add-icon {
          background-color: rgba(144, 147, 153, 0.1);
          color: #909399;
          border: 1px dashed rgba(144, 147, 153, 0.5);
        }
      }
      
      .card-icon {
        width: 44px;
        height: 44px;
        border-radius: 8px;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid;
        
        .el-icon {
          font-size: 22px;
        }
      }
      
      .card-content {
        text-align: center;
        padding: 0;
        width: 100%;
        
        .favorite-title {
          font-size: 12px;
          font-weight: normal;
          color: #606266;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
          margin-bottom: 0;
        }
        
        .favorite-desc {
          display: none;
        }
      }
      
      .card-actions {
        position: absolute;
        top: 0;
        right: 0;
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 1;
      }
      
      &:hover .card-actions {
        opacity: 1;
      }
    }
  }
}

@media (max-width: 1200px) {
  .app-categories {
    .favorites-section {
      .category-section {
        flex: 0 0 calc((100% - 64px) / 5);
      }
    }
  }
}

@media (max-width: 992px) {
  .app-categories {
    .favorites-section {
      .category-section {
        flex: 0 0 calc((100% - 48px) / 3);
      }
    }
  }
}

@media (max-width: 768px) {
  .app-categories {
    .favorites-section {
      .categories-row {
        flex-wrap: nowrap;
        overflow-x: auto;
      }
      
      .category-section {
        flex: 0 0 80%;
        min-width: 200px;
      }
    }
  }
}

.path-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.simple-divider {
  margin: 16px 0;
  
  :deep(.el-divider__text) {
    display: none;
  }
}

:deep(.el-divider) {
  background-color: #ebeef5;
}
</style> 