<template>
  <div class="ai-exploration">
    <div class="section-header">
      <h2 class="section-title">AI探索实践</h2>
      <div class="button-group">
        <el-button type="primary" text class="action-btn">
          <el-icon><Plus /></el-icon>添加实践
        </el-button>
        <el-button type="info" text class="action-btn">
          更多<el-icon class="right-icon"><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
    
    <div class="exploration-container">
      <div class="resource-list">
        <div v-for="(item, index) in explorationItems" :key="index" class="resource-item">
          <el-card shadow="hover" class="resource-card">
            <span class="date">{{ item.date }}</span>
            <div class="card-content">
              <div class="title-container">
                <el-tag size="small" effect="plain" :type="item.tagType || ''" class="resource-tag">{{ item.status }}</el-tag>
                <h3 class="resource-title">{{ item.title }}</h3>
              </div>
              <p class="resource-desc">{{ item.description }}</p>
              <div class="team-name">{{ item.team }}</div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Plus, Opportunity, SetUp, ArrowRight } from '@element-plus/icons-vue';

// 探索实践项目
interface ExplorationItem {
  id: string;
  title: string;
  description: string;
  status: string;
  tagType?: string;
  date: string;
  team: string;
}

// 默认探索实践数据
const explorationItems = ref<ExplorationItem[]>([
  {
    id: '1',
    title: '对话式收款模型应用实践',
    description: '基于GPT模型构建智能对话收银系统，提升用户体验与业务效率',
    status: '进行中',
    tagType: 'warning',
    date: '2025-03-15',
    team: '智能算法团队'
  },
  {
    id: '2',
    title: '运力智能调度优化',
    description: '利用强化学习算法优化运力调度策略，提升配送效率与准时率',
    status: '已完成',
    tagType: 'success',
    date: '2025-02-28',
    team: '调度优化中心'
  },
  {
    id: '3',
    title: '属约场景智能决策系统',
    description: '智能感知环境变动，自动调整资源分配策略，提升用户满意度',
    status: '进行中',
    tagType: 'warning',
    date: '2025-03-10',
    team: '展约策略组'
  }
]);
</script>

<script lang="ts">
export default {
  name: 'AIExploration'
};
</script>

<style lang="scss" scoped>
.ai-exploration {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .button-group {
      display: flex;
      gap: 4px;
      
      .action-btn {
        padding: 4px 6px;
        
        .right-icon {
          margin-left: 2px;
        }
      }
    }
  }
  
  .section-title {
    font-size: 15px;
    font-weight: 600;
    margin: 0;
    color: #303133;
    position: relative;
    padding-left: 8px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 12px;
      background: linear-gradient(to bottom, #409EFF, #36cfc9);
      border-radius: 2px;
    }
  }
  
  .exploration-container {
    .resource-list {
      display: flex;
      flex-direction: column;
      gap: 0px;
      
      .resource-item {
        height: 80px !important;
        .resource-card {
          border-radius: 6px;
          transition: all 0.2s ease;
          position: relative;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          }
          
          :deep(.el-card__body) {
            padding: 6px 8px;
          }
          
          .date {
            position: absolute;
            top: 6px;
            right: 8px;
            font-size: 11px;
            color: #909399;
            display: flex;
            align-items: center;
            
            &::before {
              content: "\e78f"; /* 使用Element Plus的时钟图标 */
              font-family: 'element-icons' !important;
              margin-right: 3px;
              font-size: 10px;
            }
          }
          
          .card-content {
            .title-container {
              display: flex;
              align-items: center;
              gap: 4px;
              margin-bottom: 1px;
            }
            
            .resource-tag {
              border-radius: 2px;
              padding: 0 4px;
              height: 16px;
              line-height: 14px;
              font-size: 11px;
              flex-shrink: 0;
            }
            
            .resource-title {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin: 0;
              padding: 0;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: calc(100% - 70px);
            }
            
            .resource-desc {
              font-size: 12px;
              color: #606266;
              margin: 0 0 2px 0;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            
            .team-name {
              color: #409EFF;
              font-size: 11px;
              font-weight: 500;
              display: flex;
              align-items: center;
              margin-top: 1px;
              
              &::before {
                content: '';
                display: inline-block;
                width: 4px;
                height: 4px;
                background-color: #409EFF;
                border-radius: 50%;
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
  }
}
</style> 