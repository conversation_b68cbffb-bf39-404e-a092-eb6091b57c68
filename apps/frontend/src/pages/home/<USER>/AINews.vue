<template>
  <div class="ai-news">
    <div class="section-header">
      <h2 class="section-title">AI资讯</h2>
      <div class="button-group">
        <el-button type="primary" text class="action-btn">
          <el-icon><Plus /></el-icon>添加资讯
        </el-button>
        <el-button type="info" text class="action-btn">
          更多<el-icon class="right-icon"><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
    
    <div class="news-container">
      <div class="resource-list">
        <div v-for="(item, index) in newsResources" :key="index" class="resource-item">
          <el-card shadow="hover" class="resource-card">
            <span class="date">{{ item.date }}</span>
            <div class="resource-icon" :style="{ backgroundColor: item.bgColor }">
              <el-icon :size="16">
                <component :is="item.icon" />
              </el-icon>
            </div>
            <div class="resource-content">
              <div class="title-container">
                <el-tag size="small" effect="plain" type="info" class="resource-tag">{{ item.source }}</el-tag>
                <h3 class="resource-title">{{ item.title }}</h3>
              </div>
              <p class="resource-desc">{{ item.description }}</p>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Plus, Bell, ChatLineSquare, ArrowRight } from '@element-plus/icons-vue';

// 新闻资源
interface NewsResource {
  id: string;
  title: string;
  description: string;
  source: string;
  date: string;
  icon: string;
  bgColor: string;
}

// 默认新闻资源数据
const newsResources = ref<NewsResource[]>([
  {
    id: '1',
    title: 'GPT-5模型正式发布',
    description: 'OpenAI发布新一代大语言模型，多模态能力大幅提升',
    source: 'AI技术前沿',
    date: '2025-03-20',
    icon: 'Bell',
    bgColor: 'rgba(64, 158, 255, 0.1)'
  },
  {
    id: '2',
    title: '国内多模态AI技术取得突破',
    description: '国内研究团队在多模态理解与生成领域实现重要进展',
    source: '科技日报',
    date: '2025-03-18',
    icon: 'ChatLineSquare',
    bgColor: 'rgba(103, 194, 58, 0.1)'
  },
  {
    id: '3',
    title: 'AI助力外卖配送效率提升40%',
    description: '智能调度算法在餐饮配送领域的落地应用成效显著',
    source: '行业观察',
    date: '2025-03-15',
    icon: 'Bell',
    bgColor: 'rgba(230, 162, 60, 0.1)'
  }
]);
</script>

<script lang="ts">
export default {
  name: 'AINews'
};
</script>

<style lang="scss" scoped>
.ai-news {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .button-group {
      display: flex;
      gap: 4px;
      
      .action-btn {
        padding: 4px 6px;
        
        .right-icon {
          margin-left: 2px;
        }
      }
    }
  }
  
  .section-title {
    font-size: 15px;
    font-weight: 600;
    margin: 0;
    color: #303133;
    position: relative;
    padding-left: 8px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 12px;
      background: linear-gradient(to bottom, #409EFF, #36cfc9);
      border-radius: 2px;
    }
  }
  
  .news-container {
    .resource-list {
      display: flex;
      flex-direction: column;
      gap: 0px;
      
      .resource-item {
        height: 60px !important;
        .resource-card {
          border-radius: 6px;
          transition: all 0.2s ease;
          position: relative;
        
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          }
          
          :deep(.el-card__body) {
            display: flex;
            padding: 6px 8px;
          }
          
          .date {
            position: absolute;
            top: 6px;
            right: 8px;
            font-size: 11px;
            color: #909399;
            display: flex;
            align-items: center;
            
            &::before {
              content: "\e78f"; /* 使用Element Plus的时钟图标 */
              font-family: 'element-icons' !important;
              margin-right: 3px;
              font-size: 10px;
            }
          }
          
          .resource-icon {
            width: 30px;
            height: 30px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            color: #409EFF;
            box-shadow: 0 1px 4px rgba(64, 158, 255, 0.1);
            flex-shrink: 0;
          }
          
          .resource-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: calc(100% - 80px);
            
            .title-container {
              display: flex;
              align-items: center;
              gap: 4px;
              margin-bottom: 1px;
            }
            
            .resource-tag {
              border-radius: 2px;
              padding: 0 4px;
              height: 16px;
              line-height: 14px;
              font-size: 11px;
              flex-shrink: 0;
              
              &.el-tag--info {
                background-color: #f4f4f5;
                border-color: #e9e9eb;
                color: #909399;
              }
            }
            
            .resource-title {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin: 0;
              padding: 0;
              position: relative;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 100%;
            }
            
            .resource-desc {
              font-size: 12px;
              color: #606266;
              margin: 0;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
</style> 