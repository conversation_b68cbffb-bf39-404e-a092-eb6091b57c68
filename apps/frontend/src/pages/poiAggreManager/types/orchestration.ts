/**
 * 工作流服务编排响应接口定义
 */
export interface Response<T> {
  /** 响应代码，0表示成功 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data: T;
  /** 任务结果映射 */
  workflowContext?: WorkflowContext;
}

/**
 * 工作流上下文
 * 注1：taskResultMap的key为任务别名，value为任务执行结果
 * 注2：taskExceptionMap的key为任务别名，value为任务执行异常信息
 * 注3：transactionMap的key为Transaction ID，value为Transaction对象
 * 注4：envMap的key为环境变量名（包含：任务别名、任务别名+IsException、输出配置中间变量名、环境变量名、parmas即入参、__var__+jsonPathExpr.toMD5即表达式中的jsonpath的替代key），value为环境变量值
 */
export interface WorkflowContext {
  /** 入参JSON */
  paramJson: string;
  /** 工作流 */
  workflow: Workflow;
  /** 配置选项 */
  option?: any;
  /** 任务结果映射表 */
  taskResultMap?: Record<string, any>;
  /** 任务异常映射表 */
  taskExceptionMap?: Record<string, any>;
  /** 环境变量映射表 */
  envMap?: Record<string, any>;
  /** 过程Transaction */
  transactionMap?: Record<string, Transaction>;
  /** 调用任务映射表 */
  invokerTaskMap?: Record<string, any>;
}

/**
 * 节点执行过程
 */
export interface Transaction {
  /** Transaction 名称 */
  name: string;
  /** 任务执行是否完成 */
  completed?: boolean;
  /** 任务执行结果数据 */
  data?: any;
  /** 执行时长（微秒） */
  durationInMicros?: number;
  /** 执行时长（毫秒） */
  durationInMillis?: number;
  /** 原始执行时长（微秒） */
  rawDurationInMicros?: number;
  /** 执行状态码 */
  status?: string;
  /** 执行是否成功 */
  success?: boolean;
  /** 执行时间戳 */
  timestamp?: number;
  /** 执行类型 */
  type?: string;
}

/**
 * 工作流类型
 */
export interface Workflow {
  /** 工作流ID */
  id?: string;
  /** 工作流名称 */
  name: string;
  /** 工作流描述 */
  description?: string;
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 是否快速失败 */
  failFast?: boolean;
  /** 重试选项 */
  retry?: RetryOption;
  /** 所有任务映射 */
  taskMap?: Record<string, Task>;
  /** 输出定义 */
  outputs: Outputs;
  /** 输入参数配置 */
  inputParams?: ParamConfig[];
}

/**
 * 任务类型
 */
export interface Task {
  /** 任务别名 */
  alias: string;
  /** 任务类型 */
  taskType: string;
  /** 任务描述 */
  description?: string;
  /** 被调服务的URL */
  url?: string;
  /** 请求方法 */
  method?: string;
  /** 远程服务Appkey */
  remoteAppkey?: string;
  /** 远程服务端口 */
  remoteServerPort?: string;
  /** 任务超时时间 */
  timeout?: number;
  /** 是否忽略异常 */
  ignoreException?: boolean;
  /** 任务调用开关表达式 */
  switch?: string;            // 用于前端展示
  switchExpression?: string;  // 用于后端保存
  /** 缓存选项 */
  cacheOption?: CacheOption;
  /** 请求参数 */
  inputs?: string;
  /** 请求参数的额外信息 */
  inputsExtra?: Record<string, string>;
  /** 请求鉴权信息 */
  secret?: string;
  /** 指定泳道 */
  swimlane?: string;
  /** 指定cell */
  cell?: string;
  /** 指定liteSet */
  liteSet?: string;
  /** 指定调用端appkey */
  appkey?: string;
  /** 指定调用IP端口 */
  ipPorts?: string;
  /** 依赖任务映射 */
  dependencyTaskMap?: Record<string, Task>;
  /** 执行层级 */
  level?: number;
}

/**
 * 输出
 */
export interface Outputs {
  /** 类型 */
  type: string;
  /** 输出条件表达式 */
  switch?: string;            // 用于前端展示
  switchExpression?: string;  // 用于后端保存
  /** 变量 */
  variables?: Record<string, string>;
  /** 转换脚本 */
  transform: string;
}

/**
 * 缓存选项
 */
export interface CacheOption {
  /** 写入后过期时间（秒） */
  expireAfterWriteSeconds: number;
  /** 写入后刷新时间（秒） */
  refreshAfterWriteSeconds: number;
  /** 最大缓存数量 */
  maximumSize: number;
}

/**
 * 重试选项
 */
export interface RetryOption {
  /** 最大重试次数 */
  maxAttempts: number;
  /** 重试延迟（毫秒） */
  delay: number;
}

/**
 * 节点类型枚举
 */
export enum NodeType {
  START = 'START',
  END = 'END',
  TASK = 'TASK'
}

/**
 * 前端可视化节点类型
 */
export interface VisualNode extends Task {
  /** 节点ID */
  id: string;
  /** 节点类型 */
  type: string;
  /** 节点位置X坐标 */
  x?: number;
  /** 节点位置Y坐标 */
  y?: number;
  /** 节点文本 */
  text?: string;
  /** 节点宽度 */
  width?: number;
  /** 节点高度 */
  height?: number;
  /** 节点状态 */
  status?: 'idle' | 'running' | 'success' | 'failed';
  /** 特殊节点类型 */
  nodeType?: NodeType;
  /** 输入参数 (仅用于开始节点) */
  inputParams?: Record<string, any>;
}

/**
 * 前端可视化边类型
 */
export interface VisualEdge {
  /** 边ID */
  id: string;
  /** 源节点ID */
  sourceNodeId: string;
  /** 目标节点ID */
  targetNodeId: string;
  /** 边类型 */
  type: string;
}

/**
 * 执行选项
 */
export interface ExecutionOption {
  /** 线程池大小 */
  threadPoolSize?: number;
  /** 是否记录执行历史 */
  recordHistory?: boolean;
}

/**
 * 参数配置
 */
export interface ParamConfig {
  /** 参数名称 */
  name: string;
  /** 参数描述 */
  description?: string;
  /** 参数类型 */
  type: string; // 'string' | 'number' | 'boolean' | 'object' | 'array'
  /** 默认值 */
  defaultValue?: any;
  /** 是否必填 */
  required?: boolean;
}

/**
 * 开始节点配置
 */
export interface StartNodeConfig {
  /** 输入参数配置 */
  params: ParamConfig[];
  /** 参数示例 */
  example?: Record<string, any>;
}

/**
 * 结束节点配置
 */
export interface EndNodeConfig {
  /** 输出配置 */
  outputs: any;
} 