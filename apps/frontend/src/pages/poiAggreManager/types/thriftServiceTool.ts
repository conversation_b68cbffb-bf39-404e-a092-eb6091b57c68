// 服务接口信息
export interface ServiceInterface {
  serviceName: string;
  ifaceName: string;
  implName: string;
}

// Rocket主机信息
export interface RocketHost {
  id: string | number;
  ip: string;
  name?: string;
  port?: number;
  status?: string;
}

export interface RocketHostResponse {
  hosts: RocketHost[];
}

export interface ServiceInfo {
  port: string;
  serviceIfaceInfos: ServiceInterface[];
  status?: string;
}

export interface ServiceInterfaceResponse {
  appkey: string;
  serviceInfo: ServiceInfo[];
  env: string;
  swimlane: string;
  startTime: string;
  version: string;
}

// 服务方法信息
export interface ServiceMethod {
  serviceName: string;
  methods: string[];
}

export interface ServiceMethodResponse {
  appkey: string;
  serviceMethods: ServiceMethod[];
  version: string;
}

// 方法参数JSON Schema
export interface MethodInfo {
  name: string;
  parameterTypes: string[];
  returnType: string;
  parameters: any[];
}

export interface TypeInfo {
  id: any;
  type: string;
  items: any[];
  enums: any[];
  $ref: any;
  properties: Record<string, any>;
  typeBuilderName: string;
}

// 返回类型字段信息
export interface FieldInfo {
  type: string;
  simpleType: string;
  description?: string;
  required?: boolean;
  isPrimitive?: boolean;
  isCollection?: boolean;
  elementType?: string;
  nestedFields?: Record<string, FieldInfo>;
  exampleValue?: any;
}

// 返回类型结构
export interface ReturnType {
  typeName: string;
  simpleTypeName: string;
  description?: string;
  isPrimitive?: boolean;
  isCollection?: boolean;
  isEnum?: boolean;
  enumValues?: string[];
  fields?: Record<string, FieldInfo>;
  elementType?: string;
  exampleValue?: any;
}

export interface JsonSchemaResponse {
  canonicalName: any;
  codeSource: any;
  methods: MethodInfo[];
  types: TypeInfo[];
  uniqueId: string;
  returnType?: ReturnType;
} 