export interface PageResult<T> {
  total: number
  records: T[]
}

export interface QueryParams {
  pageNo: number
  pageSize: number
  [key: string]: any
}

// 通用变更对象
export interface McmChangeItem {
  changeDescription: string;
  requestBaseUrl: string;
  requestUri: string;
  changeBefore: any;
  changeAfter: Partial<FieldMetadataItem>;
  requestData: any;
}

// 查询场景相关类型定义
export type SceneType = '1' | '2'

export const SCENE_TYPE_OPTIONS = [
  { label: '强依赖', value: 1 },
  { label: '弱依赖', value: 2 }
] as const

export const SCENE_LEVEL_OPTIONS = Array.from({ length: 10 }, (_, i) => ({
  label: `P${i}`,
  value: i
}))

export const APP_TYPE_OPTIONS = [
  { label: 'SET', value: 1 },
  { label: '中心', value: 2 },
  { label: 'SET & 中心', value: 3 }
] as const

export interface SceneItem {
  id: number
  sceneName: string
  sceneDescription: string
  sceneCode?: string
  appkeys: string[]
  appType: number
  sceneLevel: number
  dependencyType: number
  administrator: string[]
  fieldCodes: string[]
  prdWikis: string[]
  peakPeriod: string
  estimateQps: number
  actualQps: number
  valid: number
  remark: string
  opName: string
  opMis: string
  ctime?: number
  utime?: number
}

export interface SceneQueryParams extends QueryParams {
  sceneCode?: string
  sceneName?: string
  sceneLevel?: number
  valid?: number
}

// 字段元数据相关类型定义
export type FieldType = '1' | '2' | '3' | '4'

export const FIELD_TYPE_OPTIONS: { label: string; value: FieldType }[] = [
  { label: 'String', value: '1' },
  { label: 'Boolean', value: '2' },
  { label: 'Long', value: '3' },
  { label: 'Double', value: '4' }
]

export interface FieldMetadataItem {
  id?: number
  fieldCode: string
  fieldProperty: string
  fieldName: string
  description: string
  type: FieldType
  defaultValue: string
  dependentFields: string[]
  syncedField: boolean
  valid: number | undefined
  status: number | undefined
  opName?: string
  opMis?: string
  ctime?: number
  utime?: number
  lionConfigDescription?: string
  handlerType?: number
}

export interface FieldMetadataQueryParams extends QueryParams {
  fieldCode?: string
  fieldName?: string
  type?: FieldType
  handlerType?: number
}

// 服务编排相关类型定义
export interface DSLConfigItem {
  id?: number
  dslName: string
  dslDescription: string
  dsl: string
  fieldCodes: string[]
  thrownException: boolean
  dslPreHeatParam: string
  valid: number | undefined
  status: number | undefined
  opName?: string
  opMis?: string
  ctime?: number
  utime?: number
  lionConfigDescription?: string
  option?: Record<string, any>
  supportQps?: number
  actualQps?: number
}

export interface DSLConfigQueryParams extends QueryParams {
  dslName?: string
  valid?: number
}


// 同步字段元数据相关类型定义
export interface SyncMetadataItem {
  id?: number
  fieldCode: string | undefined
  fieldName: string | undefined
  description: string | undefined
  type: FieldType | undefined
  fieldProperty: string | undefined
  defaultValue: string | undefined
  valid: number | undefined
  status: number | undefined
  sync2QueryField: boolean | undefined
  dependentFields: string[] | undefined
  opName?: string
  opMis?: string
  ctime?: number
  utime?: number
  lionConfigDescription?: string
}

export interface SyncMetadataQueryParams extends QueryParams {
  fieldCode?: string
  fieldName?: string
  type?: FieldType
  valid?: number
}

// 同步配置相关类型定义
export interface SyncConfigItem {
  id?: number
  syncTenant?: number
  syncScene?: number
  syncName?: string
  syncDescription?: string
  lionConfigDescription?: string
  syncFieldCodes?: string[]
  type?: number
  isPoiOuter?: number | boolean
  totalPoiConsistencyCheck?: string
  mafkaConsumeConfig?: string
  queryDataConfig?: string
  queryChangeIdsConfig?: string
  dtsSyncConfig?: string
  valid?: number
  status?: number
  opName?: string
  opMis?: string
  ctime?: number
  utime?: number
}

export interface SyncConfigQueryParams extends QueryParams {
  syncScene?: number
  syncName?: string
  type?: number
  valid?: number
}

export const SYNC_TYPE_OPTIONS = [
  { label: 'Mafka', value: 1 },
  { label: 'DTS', value: 2 }
] as const

export enum SyncTypeEnum {
  MAFKA = 1,
  DTS = 2
}

export const POI_OUTER_OPTIONS = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
] as const

// 任务状态枚举
export enum TaskStatus {
  PASSED = 0,    // 通过
  PROCESSING = 1, // 进行中
  REVOKED = 2,   // 撤销
  REJECTED = 3,  // 驳回
  EXCEPTION = -1 // 异常
}

// 任务类型枚举
export enum TaskType {
  QUERY_FIELD = 1,    // 查询字段
  SERVICE_ORCHESTRATION = 2, // 服务编排
  SYNC_FIELD = 3,     // 同步字段
  SYNC_CONFIG = 4,     // 同步配置
  ACCESS_GUIDE = 5     // 接入指引
}

// 任务对象接口
export interface TaskItem {
  id: number
  taskType: TaskType
  taskName: string
  mcmUrl: string
  mcmEventUuid: string
  status: TaskStatus
  valid: number
  opName: string
  opMis: string
  ctime: number
  utime: number
}

// 任务查询参数接口
export interface TaskQueryParams extends QueryParams {
  taskType?: TaskType
  status?: TaskStatus
  personalTask?: number  // 0-全部任务，1-我的任务
}

/**
 * 需求信息表单数据接口
 */
export interface RequirementInfo {
  /** 需求/PRD链接 */
  prdLink: string;
  /** 需求分析结果 */
  analysisResult: string;
  /** 技术方案Wiki链接 */
  wikiLink: string;
}

// 定义接入指引数据接口，整合所有相关数据
export interface AccessGuideData {
  // 需求信息
  requirementInfo: RequirementInfo;
  // 接入方式
  accessWay: string;
  // 同步字段（可能有多个）
  syncFieldList?: SyncMetadataItem[];
  // 查询字段（可能有多个）
  queryFieldList?: FieldMetadataItem[];
  // 是否提供查询功能
  provideQueryService?: boolean;
  // 同步配置
  syncConfig?: SyncConfigItem;
  // DSL配置
  dslConfig?: DSLConfigItem;
  // DTS订阅URL - 注意：在AccessGuide.vue中此字段直接在accessGuideData对象上，而不是在顶层类型中
  dtsSubscriptionUrl: string; 
  // Mafka配置 - 注意：同上
  mafkaConfig?: any; 
  // Crane URL信息 - 注意：同上
  craneUrlInfo?: string;
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}