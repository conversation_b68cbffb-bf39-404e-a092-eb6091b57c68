/**
 * 服务编排工具函数
 */
import { ElMessage } from 'element-plus';


/**
 * 清理过期的DSL可视化数据
 * 移除超过30分钟未使用的数据
 */
export const cleanupObsoleteDslData = (): void => {
  try {
    // 查找并清理所有过期的DSL数据
    const keysToRemove = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      
      // 只处理我们的DSL键
      if (key && key.startsWith('dsl_visualizer_')) {
        // 从键名中提取时间戳，格式: dsl_visualizer_TIMESTAMP_RANDOM
        const parts = key.split('_');
        if (parts.length >= 3) {
          const timestamp = parseInt(parts[2], 10);
          
          // 如果时间戳无效或已过期（超过30分钟），标记为删除
          if (isNaN(timestamp) || Date.now() - timestamp > 30 * 60 * 1000) {
            keysToRemove.push(key);
          }
        } else {
          // 格式不正确的键也删除
          keysToRemove.push(key);
        }
      }
    }
    
    // 删除所有过期或无效的键
    if (keysToRemove.length > 0) {
      console.log(`正在清理${keysToRemove.length}个过期的DSL数据...`);
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
      console.log('过期DSL数据清理完成');
    }
  } catch (error) {
    console.error('清理过期DSL数据时出错:', error);
  }
};

/**
 * 跳转到服务编排可视化页面
 * @param dslContent DSL内容
 * @param options 跳转选项
 */
export const goToOrchestrationVisualizer = (
  dslContent: string, 
  options: {
    hideDslEditor?: boolean; // 是否隐藏DSL编辑器
    closeCurrentDialog?: () => void; // 关闭当前对话框的函数
    newTab?: boolean; // 是否在新标签页打开
  } = {}
): void => {
  console.log('goToOrchestrationVisualizer 被调用，DSL长度:', dslContent?.length || 0);
  
  if (!dslContent || dslContent.trim() === '') {
    console.error('错误：传入的DSL内容为空');
    ElMessage.error('无法跳转: DSL内容为空');
    return;
  }
  
  // 默认选项
  const { 
    hideDslEditor = true, 
    closeCurrentDialog, 
    newTab = true 
  } = options;
  
  console.log('跳转选项:', { hideDslEditor, newTab, hasCloseDialogFn: !!closeCurrentDialog });
  
  // 如果有关闭当前对话框的函数，调用它
  if (closeCurrentDialog) {
    try {
      closeCurrentDialog();
      console.log('已关闭调用方对话框');
    } catch (closeError) {
      console.error('关闭对话框时出错:', closeError);
    }
  }
  
  // 生成唯一键，使用时间戳和随机数确保唯一性
  const storageKey = `dsl_visualizer_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  console.log('生成的localStorage键:', storageKey);
  
  // 使用localStorage临时存储DSL数据
  try {
    // 清理可能存在的旧数据
    cleanupObsoleteDslData();
    
    // 检查DSL数据大小
    const dataSize = new Blob([dslContent]).size;
    console.log(`DSL数据大小: ${dataSize} 字节 (${Math.round(dataSize/1024*100)/100} KB)`);
    
    // 存储当前DSL数据
    localStorage.setItem(storageKey, dslContent);
    console.log('DSL数据已成功存储到localStorage');
    
    // 验证存储是否成功
    const storedData = localStorage.getItem(storageKey);
    if (!storedData) {
      console.error('警告：数据存储后无法验证，可能没有成功写入');
    } else if (storedData.length !== dslContent.length) {
      console.warn(`警告：存储的数据长度(${storedData.length})与原始数据(${dslContent.length})不一致`);
    } else {
      console.log('数据存储验证成功');
    }
    
    // 构建目标URL，只传递标识键，不再传递hideDslEditor参数（使用默认隐藏）
    const url = `/#/poiAggreManage/tools/orchestrationVisualizer?dslKey=${storageKey}`;
    console.log('跳转URL:', url);
    
    // 根据选项决定打开方式
    if (newTab) {
      const newWindow = window.open(url, '_blank');
      if (!newWindow) {
        console.error('无法打开新窗口，可能被浏览器拦截器阻止');
        ElMessage.warning('无法打开新窗口，请检查浏览器是否阻止了弹出窗口');
      } else {
        console.log('已在新标签页打开服务编排可视化页面');
      }
    } else {
      console.log('即将在当前页面加载服务编排可视化...');
      window.location.href = url;
    }
    
    console.log('已跳转到服务编排可视化页面（使用localStorage传递DSL）');
  } catch (error) {
    // 记录具体错误信息
    console.error('localStorage操作失败:', error);
    
    // 尝试确定失败原因
    let errorType = '未知错误';
    let errorDetails = '';
    
    if (error instanceof DOMException && error.name === 'QuotaExceededError') {
      errorType = '存储配额超出';
      errorDetails = '浏览器本地存储空间已满';
    } else if (error instanceof Error && error.name === 'SecurityError') {
      errorType = '安全性错误';
      errorDetails = '浏览器可能禁止了localStorage访问';
    }
    
    console.error(`本地存储失败 [${errorType}]: ${errorDetails}`, error);
    ElMessage.warning(`本地存储失败(${errorType})，将使用URL传参方式`);
    
    // 使用URL参数方式作为备选方案
    try {
      const encodedDsl = encodeURIComponent(dslContent);
      console.log(`URL编码后DSL长度: ${encodedDsl.length} 字符`);
      
      // 如果编码后数据过大，可能会导致URL过长
      if (encodedDsl.length > 2000) {
        console.warn(`警告：编码后DSL长度(${encodedDsl.length})可能超出URL长度限制`);
      }
      
      const url = `/#/poiAggreManage/tools/orchestrationVisualizer?dsl=${encodedDsl}`;
      
      if (newTab) {
        const newWindow = window.open(url, '_blank');
        if (!newWindow) {
          console.error('无法打开新窗口，可能被浏览器拦截器阻止');
          ElMessage.warning('无法打开新窗口，请检查浏览器是否阻止了弹出窗口');
        }
      } else {
        window.location.href = url;
      }
      
      console.log('已使用URL参数方式跳转到服务编排可视化页面');
    } catch (urlError) {
      console.error('使用URL参数方式也失败:', urlError);
      ElMessage.error('跳转失败，无法传递DSL数据');
    }
  }
};

/**
 * 为DSL数据生成可视化键，不进行页面跳转
 * @param dslContent DSL内容
 * @returns 成功时返回dslKey，失败时返回null
 */
export const getVisualDslKey = (dslContent: string): string | null => {
  console.log('getVisualDslKey 被调用，DSL长度:', dslContent?.length || 0);
  
  if (!dslContent || dslContent.trim() === '') {
    console.error('错误：传入的DSL内容为空');
    ElMessage.error('无法生成KEY: DSL内容为空');
    return null;
  }
  
  // 生成唯一键，使用时间戳和随机数确保唯一性
  const storageKey = `dsl_visualizer_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  console.log('生成的localStorage键:', storageKey);
  
  // 使用localStorage临时存储DSL数据
  try {
    // 清理可能存在的旧数据
    cleanupObsoleteDslData();
    
    // 检查DSL数据大小
    const dataSize = new Blob([dslContent]).size;
    console.log(`DSL数据大小: ${dataSize} 字节 (${Math.round(dataSize/1024*100)/100} KB)`);
    
    // 存储当前DSL数据
    localStorage.setItem(storageKey, dslContent);
    console.log('DSL数据已成功存储到localStorage');
    
    // 验证存储是否成功
    const storedData = localStorage.getItem(storageKey);
    if (!storedData) {
      console.error('警告：数据存储后无法验证，可能没有成功写入');
      return null;
    } else if (storedData.length !== dslContent.length) {
      console.warn(`警告：存储的数据长度(${storedData.length})与原始数据(${dslContent.length})不一致`);
      return null;
    } else {
      console.log('数据存储验证成功');
      return storageKey;
    }
  } catch (error) {
    // 记录具体错误信息
    console.error('localStorage操作失败:', error);
    
    // 尝试确定失败原因
    let errorType = '未知错误';
    let errorDetails = '';
    
    if (error instanceof DOMException && error.name === 'QuotaExceededError') {
      errorType = '存储配额超出';
      errorDetails = '浏览器本地存储空间已满';
    } else if (error instanceof Error && error.name === 'SecurityError') {
      errorType = '安全性错误';
      errorDetails = '浏览器可能禁止了localStorage访问';
    }
    
    console.error(`本地存储失败 [${errorType}]: ${errorDetails}`, error);
    ElMessage.warning(`本地存储失败(${errorType})`);
    
    return null;
  }
};

/**
 * 处理多行字符串，移除共同的缩进
 * @param text 需要处理的文本
 * @returns 移除缩进后的文本
 */
export const removeIndentation = (text: string): string => {
  if (!text || !text.includes('\n')) return text;
  
  // 处理转义的换行符
  if (text.includes('\\n')) {
    text = text.replace(/\\n/g, '\n');
  }
  
  // 分割成行
  const lines = text.split('\n');
  
  // 找出共同的缩进空格数
  let minIndent = Infinity;
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.trim() === '') continue; // 跳过空行
    
    const indent = line.search(/\S/); // 找到第一个非空格字符的位置
    if (indent !== -1 && indent < minIndent) {
      minIndent = indent;
    }
  }
  
  // 如果有共同缩进，移除它
  if (minIndent !== Infinity && minIndent > 0) {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].length >= minIndent) {
        lines[i] = lines[i].substring(minIndent);
      }
    }
  }
  
  // 移除首尾的空行
  let startIndex = 0;
  let endIndex = lines.length - 1;
  
  // 找到第一个非空行的索引
  while (startIndex <= endIndex && lines[startIndex].trim() === '') {
    startIndex++;
  }
  
  // 找到最后一个非空行的索引
  while (endIndex >= startIndex && lines[endIndex].trim() === '') {
    endIndex--;
  }
  
  // 只保留非空行范围
  const trimmedLines = lines.slice(startIndex, endIndex + 1);
  
  return trimmedLines.join('\n');
};

/**
 * 格式化JSON数据或多行字符串
 * @param value 要格式化的值
 * @returns 格式化后的字符串
 */
export const showFormat = (value: any): string => {
  if (!value) return '';
  
  if (typeof value === 'string') {
    try {
      // 尝试解析为JSON
      const parsedValue = JSON.parse(value);
      if (typeof parsedValue === 'object') {
        let formattedValue = JSON.stringify(parsedValue, null, 2);
        if (formattedValue.includes("\\n")) {
          formattedValue = formattedValue.replace(/\\n/g, '\n');
        }
        return formattedValue;
      } else {
        // 处理多行字符串，去除每行开头的缩进空格
        let cleanValue = parsedValue.replace(/\\n/g, '\n');
        return removeIndentation(cleanValue);
      }
    } catch (e) {
      // 不是JSON格式，可能是直接的多行字符串，尝试处理缩进
      return removeIndentation(value);
    }
  }
  return JSON.stringify(value, null, 2);
};

/**
 * 为多行文本的每一行添加指定数量的空格
 * @param text 需要处理的文本
 * @param spaceCount 每行前添加的空格数量，默认为8
 * @returns 处理后的文本
 */
export const writeFormat = (text: string, spaceCount: number = 8): string => {
  console.log('writeFormat text', text);
  if (text && text.trim() !== '') {
    // 检查trim后是否以{或[开头且以}或]结尾
    const trimmedInput = text.trim();
    const isLikelyJson = (trimmedInput.startsWith('{') && trimmedInput.endsWith('}')) || 
                        (trimmedInput.startsWith('[') && trimmedInput.endsWith(']'));
                        
    if (isLikelyJson) {
      // 看起来像JSON，直接使用
      console.log('isLikelyJson', isLikelyJson);
      return text;
    } else {
      if (text.includes('\n')) {
        // 处理字符串输入，添加每行前的缩进空格
        console.log('多行文本');
        const lines = text.split('\n');
        const indentedLines = lines.map(line => ' '.repeat(spaceCount) + line);
        const indentedValue = indentedLines.join('\n');
        return "\n" + indentedValue + '\n' + ' '.repeat(spaceCount-2);
      } else {  
        console.log('单行文本');
        return text;
      }
    }
  }
  return '';
};
