import ELK from 'elkjs/lib/elk.bundled.js';
import { VisualNode, VisualEdge, NodeType, Task, Workflow } from '../types/orchestration';
import { ElMessage } from 'element-plus';

// ============== 布局和ELK配置相关 ==============
// 布局配置选项
export interface LayoutConfig {
  key: string;
  name: string;
  options: Record<string, string>;
}

// 布局配置预设
export const layoutConfigs: LayoutConfig[] = [
  {
    key: 'wideSpacing',
    name: '大纵向间距',
    options: {
      'elk.algorithm': 'layered',
      'elk.direction': 'RIGHT',
      'elk.spacing.nodeNode': '150',                       // 节点间距(纵向距离)
      'elk.layered.spacing.nodeNodeBetweenLayers': '150',  // 层间距(横向距离)
      'elk.layered.crossingMinimization.strategy': 'LAYER_SWEEP',
      'elk.layered.nodePlacement.strategy': 'NETWORK_SIMPLEX',
      'elk.aspectRatio': '2.0',
      'elk.padding': '[150, 150, 150, 150]',
      'elk.edgeRouting': 'ORTHOGONAL',
      'elk.layered.considerModelOrder.strategy': 'NODES_AND_EDGES',
      'elk.layered.thoroughness': '7',
      'elk.layered.layering.strategy': 'LONGEST_PATH',
      'elk.layered.cycleBreaking.strategy': 'DEPTH_FIRST'
    }
  }
];

// 创建ELK布局引擎实例
export const createElkInstance = () => new ELK();

// 使用ELK布局算法计算节点位置
export const calculateElkLayout = async (
  elk: any,
  taskMap: Record<string, Task>
): Promise<Record<string, { x: number, y: number }>> => {
  // 构建ELK图模型
  const nodes = [
    { id: 'start', width: 200, height: 80 },
    ...Object.values(taskMap).map(task => ({
      id: task.alias,
      width: 200,
      height: 80
    })),
    { id: 'end', width: 200, height: 80 }
  ];
  
  // 构建边
  const edges = [
    // 依赖关系边
    ...Object.values(taskMap).flatMap(task => {
      if (!task.dependencyTaskMap) return [];
      return Object.keys(task.dependencyTaskMap).map(depAlias => ({
        id: `${depAlias}-${task.alias}`,
        sources: [depAlias],
        targets: [task.alias]
      }));
    }),
    
    // 开始节点到没有依赖的节点
    ...Object.values(taskMap)
      .filter(t => !t.dependencyTaskMap || Object.keys(t.dependencyTaskMap).length === 0)
      .map(task => ({
        id: `start-${task.alias}`,
        sources: ['start'],
        targets: [task.alias]
      })),
    
    // 叶子节点到结束节点
    ...Object.values(taskMap)
      .filter(task => !Object.values(taskMap).some(otherTask => 
        otherTask.dependencyTaskMap && 
        Object.keys(otherTask.dependencyTaskMap).includes(task.alias)
      ))
      .map(task => ({
        id: `${task.alias}-end`,
        sources: [task.alias],
        targets: ['end']
      }))
  ];
  
  // 默认使用大纵向间距配置
  const layoutOptions = {
    ...layoutConfigs[0].options,
    // 添加防止节点重叠的配置
    'elk.layered.compaction.postCompaction.strategy': 'VERTICAL',
    'elk.layered.nodePlacement.bk.fixedAlignment': 'BALANCED',
    'elk.layered.compaction.connectedComponents': 'true'
  };
  
  // 图配置
  const graph = {
    id: 'workflow',
    children: nodes,
    edges: edges,
    layoutOptions: layoutOptions
  };
  
  try {
    // 执行布局计算
    const elkGraph = await elk.layout(graph);
    
    // 返回计算好的节点位置
    const positions: Record<string, {x: number, y: number}> = {};
    
    elkGraph.children?.forEach((node: any) => {
      // 添加100的水平偏移，让图表从左侧开始
      const x = (node.x || 0) + 100;
      const y = (node.y || 0) + 100;
      positions[node.id] = { x, y };
    });
    
    return positions;
  } catch (error) {
    console.error('ELK布局计算出错:', error);
    // 发生错误时返回空对象
    return {};
  }
};

// ============== 连线和边相关 ==============
// 获取边路径
export const getEdgePath = (
  edge: VisualEdge, 
  nodes: VisualNode[], 
  startNode: VisualNode,
  endNode: VisualNode,
  graphSize: { minX: number, minY: number }
) => {
  let sourceNode: VisualNode | undefined;
  let targetNode: VisualNode | undefined;
  
  // 查找源节点和目标节点
  if (edge.sourceNodeId === 'start') {
    sourceNode = startNode;
  } else if (edge.sourceNodeId === 'end') {
    sourceNode = endNode;
  } else {
    sourceNode = nodes.find(n => n.alias === edge.sourceNodeId);
  }
  
  if (edge.targetNodeId === 'start') {
    targetNode = startNode;
  } else if (edge.targetNodeId === 'end') {
    targetNode = endNode;
  } else {
    targetNode = nodes.find(n => n.alias === edge.targetNodeId);
  }
  
  if (!sourceNode || !targetNode || sourceNode.x === undefined || sourceNode.y === undefined || 
      targetNode.x === undefined || targetNode.y === undefined) {
    return '';
  }
  
  // 判断是否是特殊节点
  const isSourceSpecial = sourceNode.nodeType === NodeType.START || sourceNode.nodeType === NodeType.END;
  const isTargetSpecial = targetNode.nodeType === NodeType.START || targetNode.nodeType === NodeType.END;
  
  // 特殊节点尺寸
  const sourceNodeWidth = isSourceSpecial ? 280 : 200;
  const sourceNodeHeight = isSourceSpecial ? 120 : 100; // 修改为100px匹配实际节点高度
  const targetNodeWidth = isTargetSpecial ? 280 : 200;
  const targetNodeHeight = isTargetSpecial ? 120 : 100; // 修改为100px匹配实际节点高度
  
  // 计算源节点和目标节点的坐标（相对于SVG坐标系）
  const sourceNodeX = sourceNode.x - graphSize.minX;
  const sourceNodeY = sourceNode.y - graphSize.minY;
  const targetNodeX = targetNode.x - graphSize.minX;
  const targetNodeY = targetNode.y - graphSize.minY;
  
  // 处理transform偏移
  let sourceTransformX = 0;
  let sourceTransformY = 0;
  let targetTransformX = 0;
  let targetTransformY = 0;
  
  if (isSourceSpecial) {
    sourceTransformX = -40;
    sourceTransformY = -20;
  }
  
  if (isTargetSpecial) {
    targetTransformX = -40;
    targetTransformY = -20;
  }
  
  // 计算实际节点坐标（考虑transform偏移）
  const adjustedSourceX = sourceNodeX + sourceTransformX;
  const adjustedSourceY = sourceNodeY + sourceTransformY;
  const adjustedTargetX = targetNodeX + targetTransformX;
  const adjustedTargetY = targetNodeY + targetTransformY;
  
  // 判断目标节点相对于源节点的位置
  const isTargetAbove = adjustedTargetY + targetNodeHeight/2 < adjustedSourceY;
  const isTargetBelow = adjustedTargetY > adjustedSourceY + sourceNodeHeight;
  const isTargetLeft = adjustedTargetX + targetNodeWidth < adjustedSourceX;
  
  // 根据节点的相对位置决定连接点位置
  let sourceX, sourceY, targetX, targetY;
  
  if (isTargetLeft) {
    // 目标在源节点左侧（ELK布局可能出现这种情况）
    sourceX = adjustedSourceX; // 源节点左侧
    sourceY = adjustedSourceY + sourceNodeHeight / 2; // 源节点中点
    
    targetX = adjustedTargetX + targetNodeWidth; // 目标节点右侧
    targetY = adjustedTargetY + targetNodeHeight / 2; // 目标节点中点
  } else {
    // 默认情况，目标在源节点右侧
    sourceX = adjustedSourceX + sourceNodeWidth; // 源节点右侧
    
    if (isTargetAbove) {
      // 目标在上方，从右上角附近出发
      sourceY = adjustedSourceY + sourceNodeHeight * 0.25;
    } else if (isTargetBelow) {
      // 目标在下方，从右下角附近出发
      sourceY = adjustedSourceY + sourceNodeHeight * 0.75;
    } else {
      // 目标接近水平方向，从中点出发
      sourceY = adjustedSourceY + sourceNodeHeight / 2;
    }
    
    targetX = adjustedTargetX; // 目标节点左侧
    targetY = adjustedTargetY + targetNodeHeight / 2; // 目标节点中点
  }
  
  // 创建贝塞尔曲线路径
  const controlX1 = sourceX + (targetX - sourceX) / 3;
  const controlY1 = sourceY;
  const controlX2 = targetX - (targetX - sourceX) / 3;
  const controlY2 = targetY;
  
  return `M ${sourceX} ${sourceY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${targetX} ${targetY}`;
};

// 判断是否是特殊边（与开始或结束节点相关的边）
export const isSpecialEdge = (edge: VisualEdge) => {
  return edge.sourceNodeId === 'start' || edge.targetNodeId === 'end' || edge.sourceNodeId === 'end' || edge.targetNodeId === 'start';
};

// 获取边的颜色
export const getEdgeStrokeColor = (edge: VisualEdge, nodes: VisualNode[]) => {
  // 查找关联节点
  const sourceNode = nodes.find(n => n.alias === edge.sourceNodeId);
  
  // 如果是Calculate类型节点发出的边，使用淡色
  if (sourceNode && sourceNode.taskType === 'Calculate') {
    return '#C0C6CC'; // 更淡的颜色
  }
  
  return '#B8BDC6'; // 默认颜色
};

// 获取边箭头标记函数
export const getEdgeMarker = (edge: VisualEdge) => {
  // 特殊边使用特殊箭头
  if (isSpecialEdge(edge)) {
    return 'url(#arrowhead-special)';
  }
  // 普通边使用标准箭头
  return 'url(#arrowhead)';
};

// ============== 图形大小计算相关 ==============
// 计算图形尺寸
export const calculateGraphSize = (taskNodes: VisualNode[], startNode: VisualNode, endNode: VisualNode) => {
  const allNodes = [...taskNodes, startNode, endNode];
  
  // 默认画布大小
  const defaultSize = {
    width: 2000,
    height: 2000,
    minX: -1000,
    minY: -1000,
    maxX: 1000,
    maxY: 1000,
    center: { x: 0, y: 0 }
  };
  
  if (allNodes.length === 0) {
    return defaultSize;
  }
  
  // 找出节点布局的边界
  let minX = Number.MAX_VALUE;
  let minY = Number.MAX_VALUE;
  let maxX = Number.MIN_VALUE;
  let maxY = Number.MIN_VALUE;
  
  // 计算所有节点的边界，包括当前拖动的节点
  allNodes.forEach(node => {
    const x = node.x || 0;
    const y = node.y || 0;
    // 使用固定的节点尺寸进行计算
    const nodeWidth = 200;
    const nodeHeight = 80;
    
    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + nodeWidth);
    maxY = Math.max(maxY, y + nodeHeight);
  });
  
  // 防止极值错误
  if (!isFinite(minX) || !isFinite(minY) || !isFinite(maxX) || !isFinite(maxY)) {
    return defaultSize;
  }
  
  // 添加足够大的边距确保所有内容可见，包括可能的负坐标
  const padding = 300;
  // 确保画布大小足够大，能覆盖所有节点，无论正负坐标
  const width = Math.max(maxX - minX, 1000) + padding * 2;
  const height = Math.max(maxY - minY, 800) + padding * 2;
  
  // 图形中心点
  const centerX = minX + (maxX - minX) / 2;
  const centerY = minY + (maxY - minY) / 2;
  
  return {
    width,
    height,
    minX: minX - padding,
    minY: minY - padding,
    maxX: maxX + padding,
    maxY: maxY + padding,
    center: { x: centerX, y: centerY }
  };
};

// 计算最佳视图参数
export const calculateIdealView = (
  graphSize: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
    width: number;
    height: number;
    center: { x: number; y: number; }
  }, 
  containerWidth: number, 
  containerHeight: number
) => {
  // 图形尺寸
  const size = graphSize;
  const graphWidth = size.maxX - size.minX;
  const graphHeight = size.maxY - size.minY;
  
  // 计算合适的缩放比
  const scaleX = (containerWidth - 40) / graphWidth;
  const scaleY = (containerHeight - 40) / graphHeight;
  const scale = Math.min(Math.min(scaleX, scaleY), 1) * 1.1; // 缩小一点，预留边距
  
  // 计算使画布居中的偏移量
  const offsetX = (containerWidth / 2) - ((size.minX + size.maxX) / 2) * scale;
  const offsetY = (containerHeight / 2) - ((size.minY + size.maxY) / 2) * scale;
  
  return {
    zoom: scale,
    position: {
      x: offsetX,
      y: offsetY
    }
  };
};

// ============== 工作流图生成相关 ==============
// 创建工作流视觉节点和边
export const buildGraphData = async (
  workflow: Workflow | null,
  elk: any,
  nodePositions: Record<string, { x: number, y: number }> = {}
) => {
  if (!workflow?.taskMap) {
    return {
      nodes: [],
      edges: [],
      taskNodes: [],
      startNode: null,
      endNode: null
    };
  }
  
  console.log('buildGraphData');
  
  // 设置开始和结束节点
  const startNode: VisualNode = {
    id: 'start',
    alias: 'start',
    taskType: 'start',
    type: 'StartNode',
    nodeType: NodeType.START,
    x: 50,
    y: 100,
    text: '开始',
    status: 'idle'
  };
  
  const endNode: VisualNode = {
    id: 'end',
    alias: 'end',
    taskType: 'end',
    type: 'EndNode',
    nodeType: NodeType.END,
    x: 50,
    y: 300,
    text: '结束',
    status: 'idle'
  };
  
  const taskMap = workflow.taskMap;
  const newNodes: VisualNode[] = [];
  const newEdges: VisualEdge[] = [];
  
  // 计算层级 (仍然需要它来创建边)
  const levelMap = new Map<number, Task[]>();
  Object.values(taskMap).forEach(task => {
    const level = task.level || 0;
    if (!levelMap.has(level)) {
      levelMap.set(level, []);
    }
    levelMap.get(level)?.push(task);
  });
  
  // 使用ELK布局计算节点位置
  let elkPositions: Record<string, {x: number, y: number}> = {};
  try {
    elkPositions = await calculateElkLayout(elk, taskMap);
  } catch (error) {
    console.error('使用ELK布局失败:', error);
    ElMessage.warning('布局计算失败，使用默认布局');
  }
  
  // 设置开始节点位置
  if (elkPositions['start']) {
    startNode.x = elkPositions['start'].x;
    startNode.y = elkPositions['start'].y;
  } else {
    // 使用默认位置
    startNode.x = 100;
    startNode.y = 180;
  }
  
  // 层级排序
  const sortedLevels = Array.from(levelMap.keys()).sort();
  
  // 为每个任务创建视觉节点
  sortedLevels.forEach(level => {
    const tasks = levelMap.get(level) || [];
    // 默认x坐标计算 (如果ELK布局失败)
    const defaultXPos = 100 + 100 + level * 300;
    
    tasks.forEach((task, index) => {
      let nodeX, nodeY;
      
      // 首先检查是否有保存的位置
      if (nodePositions[task.alias]) {
        nodeX = nodePositions[task.alias].x;
        nodeY = nodePositions[task.alias].y;
      } else if (elkPositions[task.alias]) {
        // 使用ELK计算的位置
        nodeX = elkPositions[task.alias].x;
        nodeY = elkPositions[task.alias].y;
      } else {
        // 使用默认位置
        nodeX = defaultXPos;
        nodeY = 180 + index * 400; // 默认间距
      }
      
      // 创建节点
      const node: VisualNode = {
        ...task,
        id: task.alias,
        type: task.taskType,
        x: nodeX,
        y: nodeY,
        text: task.alias,
        status: 'idle'
      };
      
      newNodes.push(node);
    });
  });

  // 设置结束节点位置
  if (elkPositions['end']) {
    endNode.x = elkPositions['end'].x;
    endNode.y = elkPositions['end'].y;
  } else {
    // 计算默认结束节点位置
    const lastLevelWidth = sortedLevels.length > 0 ? 100 + 100 + (Math.max(...sortedLevels) + 1) * 300 : 350;
    
    // 找出所有叶子节点，计算它们的平均Y位置
    const leafTasks = Object.values(taskMap).filter(task => {
      return !Object.values(taskMap).some(otherTask => 
        otherTask.dependencyTaskMap && Object.keys(otherTask.dependencyTaskMap).includes(task.alias)
      );
    });
    
    let endNodeY;
    if (leafTasks.length > 0) {
      // 计算所有叶子节点的平均Y位置
      const leafYPositions = leafTasks.map(task => {
        const position = nodePositions[task.alias] || elkPositions[task.alias];
        return position ? position.y : 0;
      });
      
      endNodeY = leafYPositions.reduce((sum, y) => sum + y, 0) / leafYPositions.length;
    } else {
      // 没有叶子节点时，使用默认位置
      endNodeY = 180 + 150;
    }
    
    endNode.x = lastLevelWidth;
    endNode.y = endNodeY;
  }
  
  // 创建边，使用与原实现相同的逻辑
  // 根据依赖关系创建边
  Object.values(taskMap).forEach(task => {
    if (task.dependencyTaskMap) {
      Object.keys(task.dependencyTaskMap).forEach(depAlias => {
        const edge: VisualEdge = {
          id: `${depAlias}-${task.alias}`,
          sourceNodeId: depAlias,
          targetNodeId: task.alias,
          type: 'polyline'
        };
        newEdges.push(edge);
      });
    }
  });
  
  // 如果存在任务，添加从开始节点到第一层任务的连接
  if (sortedLevels.length > 0) {
    const firstLevelTasks = levelMap.get(sortedLevels[0]) || [];
    firstLevelTasks.forEach(task => {
      // 如果任务没有依赖，才添加从开始节点的连接
      if (!task.dependencyTaskMap || Object.keys(task.dependencyTaskMap).length === 0) {
        newEdges.push({
          id: `start-${task.alias}`,
          sourceNodeId: 'start',
          targetNodeId: task.alias,
          type: 'polyline'
        });
      }
    });
    
    // 为所有叶子节点添加到结束节点的连接
    const leafTasks = Object.values(taskMap).filter(task => {
      return !Object.values(taskMap).some(otherTask => 
        otherTask.dependencyTaskMap && Object.keys(otherTask.dependencyTaskMap).includes(task.alias)
      );
    });
    
    leafTasks.forEach(task => {
      newEdges.push({
        id: `${task.alias}-end`,
        sourceNodeId: task.alias,
        targetNodeId: 'end',
        type: 'polyline'
      });
    });
  } else {
    // 如果没有任务，直接连接开始和结束节点
    newEdges.push({
      id: 'start-end',
      sourceNodeId: 'start',
      targetNodeId: 'end',
      type: 'polyline'
    });
  }
  
  // 更新开始和结束节点状态
  const taskNodes = newNodes.filter(node => !node.nodeType);
  
  return {
    nodes: newNodes,
    edges: newEdges,
    taskNodes,
    startNode,
    endNode
  };
};

// 更新节点状态但不重建图形
export const updateNodesWithoutRebuild = (nodes: VisualNode[], workflow: Workflow, executionStatus: Record<string, string>) => {
  if (!workflow?.taskMap) return;
  
  nodes.forEach(node => {
    if (node.alias && workflow.taskMap && workflow.taskMap[node.alias]) {
      const updatedTask = workflow.taskMap[node.alias];
      
      // 更新节点的各种属性
      node.description = updatedTask.description;
      node.taskType = updatedTask.taskType;
      node.type = updatedTask.taskType;
      node.text = updatedTask.alias;
      node.status = (executionStatus[node.alias] || 'idle') as 'idle' | 'running' | 'success' | 'failed';
      
      // 复制其他可能变更的属性
      if ('swimlane' in updatedTask) node.swimlane = updatedTask.swimlane;
      if ('switch' in updatedTask) node.switch = updatedTask.switch;
      if ('switchExpression' in updatedTask) node.switchExpression = updatedTask.switchExpression;
      
      // 确保所有任务的元数据都被更新
      Object.keys(updatedTask).forEach(key => {
        // 排除与位置相关的属性和特殊处理过的属性
        if (!['x', 'y', 'level', 'position', 'dependencyTaskMap', 'alias', 'id', 'name'].includes(key)) {
          if (key in node) {
            (node as any)[key] = (updatedTask as any)[key];
          }
        }
      });
    }
  });
};

// 判断是否需要重新渲染流程图
export const shouldRerenderWorkflow = (newWorkflow: Workflow, oldWorkflow: Workflow | null): boolean => {
  // 如果其中一个为null，则需要重新渲染
  if (!oldWorkflow) {
    console.log('旧工作流为空，需要重新渲染');
    return true;
  }
  if (!newWorkflow) {
    console.log('新工作流为空，需要重新渲染');
    return true;
  }
  
  // 检查任务节点数量是否一致
  const prevTaskMap = oldWorkflow.taskMap || {};
  const newTaskMap = newWorkflow.taskMap || {};
  const prevTasksCount = Object.keys(prevTaskMap).length;
  const newTasksCount = Object.keys(newTaskMap).length;
  
  // 节点数量不一致，需要重新渲染
  if (prevTasksCount !== newTasksCount) {
    console.log('节点数量不一致，从', prevTasksCount, '变为', newTasksCount);
    return true;
  }
  
  // 检查节点别名是否完全一致
  const prevAliases = Object.keys(prevTaskMap).sort();
  const newAliases = Object.keys(newTaskMap).sort();
  if (!arraysEqual(prevAliases, newAliases)) {
    console.log('节点别名不一致');
    return true;
  }
  
  // 检查每个节点的依赖关系是否一致（边是否相同）
  for (const alias of prevAliases) {
    const prevTask = prevTaskMap[alias];
    const newTask = newTaskMap[alias];
    
    // 检查依赖关系
    const prevDeps = prevTask.dependencyTaskMap || {};
    const newDeps = newTask.dependencyTaskMap || {};
    const prevDepsAliases = Object.keys(prevDeps).sort();
    const newDepsAliases = Object.keys(newDeps).sort();
    
    // 依赖数量或依赖别名不一致，需要重新渲染
    if (!arraysEqual(prevDepsAliases, newDepsAliases)) {
      console.log(`节点 ${alias} 的依赖关系发生变化`);
      return true;
    }
  }
  
  // 节点数量和边都一致，不需要重新渲染
  console.log('节点和边都没有变化，无需重新渲染');
  return false;
};

// 比较两个排序后的数组是否完全相等
export const arraysEqual = (arr1: string[], arr2: string[]): boolean => {
  if (arr1.length !== arr2.length) {
    return false;
  }
  
  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) {
      return false;
    }
  }
  
  return true;
};

// 格式化参数键列表展示
export const formatParamKeys = (params: Record<string, any>): string => {
  const keys = Object.keys(params);
  if (keys.length === 0) return '无参数';
  
  // 如果参数较多，只显示前3个，后面用省略号
  if (keys.length <= 3) {
    return keys.join(', ');
  } else {
    return `${keys.slice(0, 3).join(', ')}...等${keys.length}个参数`;
  }
};

// 调整缩放后的位置，保持中心点不变
export const adjustPositionAfterZoom = (
  zoomDelta: number, 
  zoom: number, 
  position: { x: number, y: number },
  containerWidth: number,
  containerHeight: number
) => {
  // 计算中心点
  const centerX = containerWidth / 2 - position.x;
  const centerY = containerHeight / 2 - position.y;
  
  // 调整位置以保持中心点不变
  position.x -= centerX * zoomDelta / zoom;
  position.y -= centerY * zoomDelta / zoom;
  
  return position;
};

// 获取节点图标
export const getNodeIcon = (taskType: string) => {
  const typeIconMap: Record<string, string> = {
    ThriftGeneric: 'Connection',
    Squirrel: 'DataAnalysis',
    Calculate: 'SetUp',
    Default: 'Service'
  };
  return typeIconMap[taskType] || typeIconMap.Default;
}; 