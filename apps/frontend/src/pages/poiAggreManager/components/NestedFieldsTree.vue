<template>
  <div class="nested-fields-container">
    <div 
      v-for="(field, fieldName) in fields || {}" 
      :key="fieldName" 
      class="field-item"
      :class="{ 'has-children': hasNestedFields(field) }"
    >
      <div 
        class="field-header" 
        :style="getIndentStyle(level)"
        @click="hasNestedFields(field) ? toggleField(fieldName) : null"
        :class="{ 'clickable': hasNestedFields(field) }"
      >
        <div class="field-expand-icon" v-if="hasNestedFields(field)">
          <el-icon v-if="isExpanded(fieldName)"><ArrowDown /></el-icon>
          <el-icon v-else><ArrowRight /></el-icon>
        </div>
        <div class="field-expand-icon placeholder" v-else></div>
        
        <span class="field-name">{{ fieldName }}</span>
        
        <div class="field-tags">
          <el-tag :type="getFieldTypeTag(field).type" size="small">
            {{ getFieldTypeTag(field).text }}
          </el-tag>
          <el-tag v-if="field.required" size="small" type="danger">必需</el-tag>
        </div>
      </div>
      
      <div class="field-details" :style="getIndentStyle(level + 1)">
        <div class="field-type">
          类型: {{ field.simpleType || field.type }}
          <span v-if="field.isCollection && field.elementType" class="element-type">
            &lt;{{ field.elementType.split('.').pop() }}&gt;
          </span>
        </div>
        <div v-if="field.description" class="field-desc">{{ field.description }}</div>
        <div v-if="field.exampleValue !== undefined" class="field-example">
          示例: {{ JSON.stringify(field.exampleValue) }}
        </div>
      </div>
      
      <!-- 嵌套字段展示 -->
      <div v-if="hasNestedFields(field) && isExpanded(fieldName)" class="nested-fields">
        <NestedFieldsTree 
          :fields="field.nestedFields" 
          :level="level + 1"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, defineComponent } from 'vue';
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue';
import type { FieldInfo } from '../types/thriftServiceTool';

export default defineComponent({
  name: 'NestedFieldsTree',
  components: {
    ArrowDown,
    ArrowRight
  },
  props: {
    fields: {
      type: Object as () => Record<string, FieldInfo> | undefined,
      default: undefined
    },
    level: {
      type: Number,
      required: true
    }
  },
  setup() {
    const expandedFields = ref(new Set<string>());

    const toggleField = (fieldName: string) => {
      if (expandedFields.value.has(fieldName)) {
        expandedFields.value.delete(fieldName);
      } else {
        expandedFields.value.add(fieldName);
      }
    };

    const isExpanded = (fieldName: string) => {
      return expandedFields.value.has(fieldName);
    };

    const hasNestedFields = (field: FieldInfo) => {
      return field.nestedFields && Object.keys(field.nestedFields).length > 0;
    };

    const getFieldTypeTag = (field: FieldInfo) => {
      if (field.isPrimitive) return { type: 'success', text: '基本类型' };
      if (field.isCollection) return { type: 'warning', text: '集合类型' };
      return { type: 'info', text: '对象类型' };
    };

    const getIndentStyle = (level: number) => {
      return {
        paddingLeft: `${level * 20}px`
      };
    };

    return {
      expandedFields,
      toggleField,
      isExpanded,
      hasNestedFields,
      getFieldTypeTag,
      getIndentStyle
    };
  }
});
</script>

<style lang="scss" scoped>
.nested-fields-container {
  .field-item {
    border-bottom: 1px solid #f5f7fa;
    
    &:last-child {
      border-bottom: none;
    }
    
    &.has-children {
      .field-header.clickable {
        cursor: pointer;
        
        &:hover {
          background-color: #f0f2f5;
        }
      }
    }
    
    .field-header {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background-color: #fafbfc;
      transition: background-color 0.2s;
      
      .field-expand-icon {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #409eff;
        
        &.placeholder {
          width: 16px;
        }
        
        .el-icon {
          font-size: 12px;
        }
      }
      
      .field-name {
        font-weight: 500;
        color: #303133;
        font-size: 14px;
        flex: 1;
      }
      
      .field-tags {
        display: flex;
        gap: 4px;
        
        .el-tag {
          font-size: 12px;
        }
      }
    }
    
    .field-details {
      padding: 4px 12px 8px 12px;
      background-color: #ffffff;
      
      .field-type {
        margin: 2px 0;
        font-size: 13px;
        color: #409eff;
        font-weight: 500;
        
        .element-type {
          color: #909399;
          font-weight: normal;
        }
      }
      
      .field-desc {
        margin: 2px 0;
        font-size: 13px;
        color: #909399;
      }
      
      .field-example {
        margin: 2px 0;
        font-size: 12px;
        color: #67c23a;
        font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
        word-break: break-all;
      }
    }
    
    .nested-fields {
      background-color: #f8f9fa;
      border-top: 1px solid #e9ecef;
      
      .nested-fields-container {
        .field-item {
          border-bottom: 1px solid #e9ecef;
          
          .field-header {
            background-color: #f8f9fa;
          }
          
          .field-details {
            background-color: #ffffff;
          }
        }
      }
    }
  }
}
</style> 