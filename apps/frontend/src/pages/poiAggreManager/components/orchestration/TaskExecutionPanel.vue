<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="emit('update:visible', $event)"
    :title="executionTask.task ? `节点执行详情: ${executionTask.task.alias}` : '节点执行详情'"
    direction="rtl"
    size="30%"
    :before-close="handleClose"
  >
    <template v-if="executionTask.task && executionTask">
      <div class="content-section">
        <!-- 1. 基本执行信息 -->
        <h3 class="section-title">基本执行信息</h3>
        <table class="info-table">
          <tbody>
            <tr class="info-row">
              <td class="info-label">节点</td>
              <td class="info-value">{{ executionTask.task.alias }}</td>
            </tr>
            <tr class="info-row">
              <td class="info-label">类型</td>
              <td class="info-value">{{ executionTask.task.taskType }}</td>
            </tr>
            <tr class="info-row">
              <td class="info-label">是否执行</td>
              <td class="info-value">
                <el-tag :type="executionStatusType" size="small">
                  {{ executionTask.invokeTask.switchExpressionResult || true }}
                </el-tag>
              </td>
            </tr>
            <tr class="info-row">
              <td class="info-label">无异常</td>
              <td class="info-value">
                <el-tag :type="executionStatusType" size="small">
                  {{ !executionTask.envMap[executionTask.task.alias+'IsException'] }}
                </el-tag>
              </td>
            </tr>
            <tr class="info-row">
              <td class="info-label">执行状态</td>
              <td class="info-value">
                <el-tag :type="executionStatusType" size="small">
                  {{ executionStatusText }}
                </el-tag>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- 1.5 输入信息 -->
        <div>
          <h3 class="section-title">输入信息</h3>
          <div class="result-container">
            <div v-if="executionTask.invokeTask">
              <ContentViewer :content="executionTask.invokeTask.inputsNode" title="输入参数">
                <pre class="result-code"><code>{{ formatJson(executionTask.invokeTask.inputsNode) }}</code></pre>
              </ContentViewer>
            </div>
            <div v-else>
              <div class="empty-info">无可用输入参数</div>
            </div>
          </div>
        </div>
        
        <!-- 2. 执行结果 -->
        <h3 class="section-title">执行结果</h3>
        <div class="result-container">
          <div class="result-header" v-if="resultTypeInfo">
            结果数据类型：
            <el-tag size="small" type="info" class="result-type-tag">
              {{ resultTypeInfo }}
            </el-tag>
          </div>
          <ContentViewer :content="executionTask.taskResult" title="执行结果">
            <pre class="result-code"><code>{{ formatJson(executionTask.taskResult) }}</code></pre>
          </ContentViewer>
        </div>
        
        <!-- 3. 异常信息 -->
        <div v-if="hasTaskException">
          <h3 class="section-title">异常信息</h3>
          <div class="result-container">
            <ContentViewer :content="executionTask.taskException" title="异常信息" :isJson="false">
              <pre class="result-code error"><code>{{ formatTaskException }}</code></pre>
            </ContentViewer>
          </div>
        </div>
        
        <!-- 4. 执行监控 -->
        <div v-if="hasTaskTransaction">
          <h3 class="section-title">执行监控</h3>
          <div class="result-container">
            <div class="transaction-info">
              <!-- 执行状态信息 -->
              <div class="transaction-status">
                <span class="transaction-name">{{ executionTask.taskTransaction.name || '事务' }}</span>
                <el-tag size="small" :type="getStatusType(executionTask.taskTransaction)" class="mr-2">
                  {{ executionTask.taskTransaction.success ? '成功' : '失败' }}
                </el-tag>
                <span class="transaction-time">
                  耗时: {{ executionTask.taskTransaction.durationInMillis || 0 }}ms
                </span>
              </div>
              
              <!-- 执行详情表格 -->
              <table class="info-table mt-2">
                <tbody>
                  <tr class="info-row" v-if="executionTask.taskTransaction.status !== undefined">
                    <td class="info-label">状态码</td>
                    <td class="info-value">
                      {{ executionTask.taskTransaction.status }}
                    </td>
                  </tr>
                  <tr class="info-row">
                    <td class="info-label">执行结果</td>
                    <td class="info-value">
                      {{ executionTask.taskTransaction.success ? '成功' : '失败' }}
                    </td>
                  </tr>
                  <tr class="info-row" v-if="executionTask.taskTransaction.durationInMicros">
                    <td class="info-label">耗时(微秒)</td>
                    <td class="info-value">{{ executionTask.taskTransaction.durationInMicros }}</td>
                  </tr>
                  <tr class="info-row" v-if="executionTask.taskTransaction.rawDurationInMicros && executionTask.taskTransaction.rawDurationInMicros > 0">
                    <td class="info-label">原始耗时</td>
                    <td class="info-value">{{ executionTask.taskTransaction.rawDurationInMicros }}微秒</td>
                  </tr>
                  <tr class="info-row" v-if="executionTask.taskTransaction.timestamp">
                    <td class="info-label">执行时间</td>
                    <td class="info-value">
                      {{ formatTimestamp(executionTask.taskTransaction.timestamp) }}
                    </td>
                  </tr>
                </tbody>
              </table>
              
              <!-- 执行结果数据 -->
              <div v-if="executionTask.taskTransaction.data" class="mt-2">
                <div class="transaction-subtitle">执行结果数据:</div>
                <ContentViewer :content="executionTask.taskTransaction.data" title="执行结果数据">
                  <pre class="result-code"><code>{{ formatJson(executionTask.taskTransaction.data) }}</code></pre>
                </ContentViewer>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 5. 环境变量 -->
        <div>
          <h3 class="section-title">环境变量（全局共享）</h3>
          <div class="env-variables-container">
            <table class="dependency-table">
              <thead>
                <tr class="table-header">
                  <th class="header-cell" style="width: 40%">变量名</th>
                  <th class="header-cell" style="width: 60%">值</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(value, key) in executionTask.envMap" :key="key" class="table-row">
                  <td class="table-cell">{{ key }}</td>
                  <td class="table-cell">
                    <div class="code-container" style="min-height: 24px; max-height: 60px; margin: 0;">
                      <ContentViewer :content="value === undefined || value === null ? '-' : value" :title="`变量 ${key} 的值`">
                        <pre><code>{{ value === undefined || value === null ? '-' : formatJson(value) }}</code></pre>
                      </ContentViewer>
                    </div>
                  </td>
                </tr>
                <tr v-if="!executionTask.envMap || Object.keys(executionTask.envMap).length === 0" class="table-row">
                  <td class="table-cell" colspan="2" style="text-align: center;">无环境变量</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </template>
    <el-empty v-else description="暂无执行结果"></el-empty>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';
import { Task, Transaction } from '../../types/orchestration';
import { ElMessage } from 'element-plus';
import ContentViewer from '../common/ContentViewer.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  executionTask: {
    type: Object,
    default: () => ({
      task: {} as Task,
      invokeTask: {} as Task,
      taskResult: {},
      taskException: {},
      taskTransaction: {} as Transaction,
      envMap: {},
      executionStatus: {}
    })
  }
});

const emit = defineEmits(['update:visible', 'close']);

// 执行状态
const executionStatusType = computed(() => {
  if (!props.executionTask.taskResult) return 'info';
  if (props.executionTask.taskException && Object.keys(props.executionTask.taskException).length > 0) return 'danger';
  return 'success';
});

const executionStatusText = computed(() => {
  if (!props.executionTask.taskResult || Object.keys(props.executionTask.taskResult).length === 0) return '未执行';
  if (props.executionTask.taskException && Object.keys(props.executionTask.taskException).length > 0) return '执行失败';
  return '执行成功';
});

// 格式化JSON数据
const formatJson = (value: any): string => {
  console.log('formatJson', value);
  console.log('typeof value', typeof value);
  
  // 处理字符数组形式的JSON字符串
  if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
    // 快速检查是否可能是字符数组对象 (检查前几个键是否为连续数字)
    const keys = Object.keys(value);
    const isCharArray = keys.length > 0 && 
                        keys[0] === '0' && 
                        (keys.length === 1 || keys[1] === '1') &&
                        (keys.length <= 2 || keys[2] === '2');
    
    if (isCharArray) {
      try {
        // 将对象值直接合并为字符串，这比join效率更高
        let jsonString = '';
        for (let i = 0; i < keys.length; i++) {
          if (value[i] !== undefined) {
            jsonString += value[i];
          }
        }
        
        // 检查是否是合法的JSON
        if ((jsonString.startsWith('{') && jsonString.endsWith('}')) || 
            (jsonString.startsWith('[') && jsonString.endsWith(']'))) {
          console.log('转换后的字符串:', jsonString);
          const parsed = JSON.parse(jsonString);
          return JSON.stringify(parsed, null, 2);
        }
      } catch (e) {
        console.error('字符数组转换失败:', e);
      }
    }
    
    // 如果不是字符数组或转换失败，按普通对象处理
    return JSON.stringify(value, null, 2);
  }
  
  if (typeof value === 'string') {
    try {
      const parsed = JSON.parse(value);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      return value;
    }
  }
  return JSON.stringify(value, null, 2);
};

// 获取结果类型信息
const resultTypeInfo = computed(() => {
  // 如果没有结果，返回null
  if (!props.executionTask.taskResult || Object.keys(props.executionTask.taskResult).length === 0) {
    return null;
  }
  
  const result = props.executionTask.taskResult;
  
  // 根据结果的基本类型判断
  if (typeof result === 'object') {
    if (Array.isArray(result)) {
      return `数组 [${result.length}项]`;
    } else {
      return `对象`;
    }
  }
  
  return `${typeof result}`;
});

// 关闭面板
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 异常信息处理
const hasTaskException = computed(() => {
  if (!props.executionTask.taskException) return false;
  return Object.keys(props.executionTask.taskException).length > 0;
});

const formatTaskException = computed(() => {
  if (!props.executionTask.taskException) return '';
  
  // 判断exception是字符串还是对象
  if (typeof props.executionTask.taskException === 'string') {
    return props.executionTask.taskException;
  }
  
  // 返回整个异常对象的格式化字符串
  return formatJson(props.executionTask.taskException);
});

// 执行过程处理
const hasTaskTransaction = computed(() => {
  if (!props.executionTask.taskTransaction) return false;
  return Object.keys(props.executionTask.taskTransaction).length > 0;
});

// 获取状态类型
const getStatusType = (txn: any): string => {
  if (!txn) return 'info';
  if (txn.success === true) return 'success';
  if (txn.success === false) return 'danger';
  return 'info';
};

// 格式化时间戳
const formatTimestamp = (timestamp: number): string => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleString();
};
</script>

<style lang="scss" scoped>
.content-section {
  padding: 0 0 10px 0;
  margin: 0;
}

.section-title {
  margin: 10px 0 5px 0;
  padding: 3px 8px;
  font-size: 12px;
  font-weight: bold;
  color: #409eff;
  background-color: #f0f6ff;
  border-left: 2px solid #409eff;
  display: flex;
  align-items: center;
  
  &:first-child {
    margin-top: 0;
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  
  .info-row {
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .info-label {
      width: 80px;
      padding: 5px;
      color: #606266;
      background-color: #f5f7fa;
      font-weight: 500;
    }
    
    .info-value {
      padding: 5px;
      color: #303133;
    }
  }
}

.result-container {
  margin: 5px 0;
  max-height: 400px;
  overflow: auto;
  
  .result-header {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 4px;
    font-size: 14px;
    color: #606266;
    
    .result-type-tag {
      font-size: 11px;
      padding: 0 5px;
      height: 20px;
      line-height: 18px;
    }
  }
  
  .result-code {
    margin: 5px 0;
    padding: 5px;
    background-color: #f5f7fa;
    border-radius: 4px;
    overflow: auto;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.error {
  color: #f56c6c;
}

.transaction-info {
  padding: 5px;
  
  .transaction-status {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
  
  .transaction-name {
    font-weight: 500;
    margin-right: 8px;
    color: #303133;
  }
  
  .transaction-time {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
  }
  
  .transaction-subtitle {
    font-size: 12px;
    font-weight: 500;
    margin: 5px 0;
    color: #606266;
  }
}

.mt-2 {
  margin-top: 8px;
}

.mr-2 {
  margin-right: 8px;
}

:deep(.el-drawer) {
  .el-drawer__header {
    margin: 0 !important;
    padding: 2px 6px !important;
    min-height: 28px !important;
    height: auto !important;
    box-sizing: border-box !important;
    border-bottom: 1px solid #e4e7ed !important;
    display: flex !important;
    align-items: center !important;
  }
  
  .el-drawer__title {
    font-size: 16px !important;
    line-height: 1 !important;
    color: #333 !important;
    margin: 0 !important;
    font-weight: 500 !important;
  }
  
  .el-drawer__close-btn {
    padding: 2px !important;
    margin: 0 !important;
  }
  
  .el-drawer__body {
    padding: 0 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }
}

.empty-info {
  padding: 10px;
  color: #909399;
  font-size: 12px;
  text-align: center;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.env-variables-container {
  margin: 2px 0;
  max-height: 300px;
  overflow: auto;
}

.dependency-table {
  margin: 3px 0;
  border: 1px solid #ebeef5;
  width: 100%;
  font-size: 11px;
  
  .table-header {
    background-color: #f5f7fa;
    font-weight: bold;
    border-bottom: 1px solid #ebeef5;
    
    .header-cell {
      padding: 3px 6px;
      text-align: left;
    }
  }
  
  .table-row {
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .table-cell {
      padding: 2px 6px;
      border-right: 1px solid #ebeef5;
      
      &:last-child {
        border-right: none;
      }
    }
  }
}

.code-container {
  background-color: #f5f7fa;
  border-radius: 2px;
  padding: 4px;
  max-height: 150px;
  overflow: auto;
  margin: 2px 0;
  font-size: 11px;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  code {
    font-family: Consolas, Monaco, monospace;
    font-size: 11px;
    color: #333;
  }
}
</style>

<script lang="ts">
export default {
  name: 'TaskExecutionPanel'
};
</script> 