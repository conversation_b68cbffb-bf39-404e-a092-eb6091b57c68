<template>
  <div class="dsl-editor-container">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <span class="editor-status" :class="isEditing ? 'status-editing' : 'status-readonly'">
          {{ isEditing ? '编辑模式' : '只读模式' }}
        </span>
      </div>
      <div class="toolbar-right">
        <el-button-group v-if="isEditing">
          <el-button 
            type="primary" 
            size="small" 
            @click="copyCode"
            :icon="CopyDocument"
          >
            复制
          </el-button>
          <el-button 
            type="success" 
            size="small" 
            @click="confirmCode"
            :icon="Check"
          >
            确定
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="editor-main">
      <!-- Monaco编辑器容器 -->
      <div ref="editorContainer" class="monaco-container"></div>
      
      <!-- 加载提示 -->
      <div v-if="isLoading" class="editor-loading">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>Monaco编辑器加载中...</span>
      </div>
      
      <!-- 加载失败提示 -->
      <div v-if="loadFailed" class="editor-load-failed">
        <el-icon class="failed-icon"><WarningFilled /></el-icon>
        <span>编辑器加载失败，请刷新重试</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, watch, onBeforeUnmount, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Document, Check, Loading, WarningFilled, CopyDocument } from '@element-plus/icons-vue';
// 导入复制工具方法
import { copyToClipboard, selectEditorContent } from '../../../../shared/utils/copyUtils';
// 直接导入monaco编辑器，使用最简单的方式
import * as monaco from 'monaco-editor';

// 防止Worker加载错误
if ((window as any).monaco) {
  // 如果已经存在全局monaco实例，不需要再配置
  console.log('monaco已全局初始化');
} else {
  // 配置Monaco编辑器的环境
  (self as any).MonacoEnvironment = {
    // 使用空的noop worker，避免加载外部worker
    getWorker: function() {
      function noopWorker() {
        // 创建一个无操作worker，只返回空结果
        onmessage = function(e) {
          // 解构消息数据
          const { id, method } = e.data;
          // 回复空结果
          postMessage({
            id: id,
            result: [],
            error: undefined
          });
        };
      }
      
      // 创建worker脚本
      const blob = new Blob(['(' + noopWorker.toString() + ')()'], { type: 'application/javascript' });
      return new Worker(URL.createObjectURL(blob));
    }
  };
}

// 禁用JSON验证，确保不需要worker也能正常编辑
try {
  monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
    validate: false,  // 禁用验证
    allowComments: true,  // 允许注释
    schemaValidation: 'ignore'  // 忽略schema验证
  });
} catch (e) {
  console.warn('禁用monaco json验证功能失败:', e);
}

const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['confirm']);

const editorContainer = ref<HTMLElement | null>(null);
const dslValue = ref(props.modelValue);
const isLoading = ref(true);
const loadFailed = ref(false);
let editor: monaco.editor.IStandaloneCodeEditor | null = null;

// 同步外部属性变更到内部状态
watch(() => props.modelValue, (newVal) => {
  dslValue.value = newVal;
  
  // 如果编辑器已创建且值发生变化，更新编辑器内容
  if (editor && editor.getValue() !== newVal) {
    editor.setValue(newVal);
  }
}, { immediate: true });

// 同步编辑状态
watch(() => props.isEditing, (newVal) => {
  if (editor) {
    editor.updateOptions({ readOnly: !newVal });
  }
});

// 创建和初始化Monaco编辑器
const createEditor = () => {
  if (!editorContainer.value) return;
  
  isLoading.value = true;
  loadFailed.value = false;
  
  try {
    // 使用monaco创建编辑器
    editor = monaco.editor.create(editorContainer.value, {
      value: dslValue.value,
      language: 'json',
      theme: 'vs',
      automaticLayout: true,
      // 配置缩略图，增大尺寸
      minimap: { 
        enabled: false,
        scale: 2,         // 缩放比例，默认为1
        maxColumn: 80,     // 显示的最大列数，默认为120
        renderCharacters: true,  // 是否渲染字符而不是颜色块
        showSlider: 'always',   // 总是显示滑块
        side: 'right'     // 位置，默认在右侧
      },
      lineNumbers: 'on',
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      readOnly: !props.isEditing,
      fontSize: 12,
      tabSize: 2,
      scrollbar: {
        verticalScrollbarSize: 8,
        horizontalScrollbarSize: 8
      },
      // 修复初次渲染宽度问题
      dimension: {
        width: editorContainer.value?.clientWidth || 800,
        height: editorContainer.value?.clientHeight || 600
      },
      // 添加支持换行符的显示设置
      renderWhitespace: 'all',
      // 添加对转义字符的更好支持
      unicodeHighlight: {
        nonBasicASCII: false,
        invisibleCharacters: false,
        ambiguousCharacters: false,
        includeComments: false,
        includeStrings: false,
      }
    });
    
    // 监听内容变化事件
    editor.onDidChangeModelContent(() => {
      if (editor) {
        dslValue.value = editor.getValue();
      }
    });
    
    // 添加快捷键
    editor.addCommand(monaco.KeyMod.Alt | monaco.KeyCode.KeyF, copyCode);
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, confirmCode);
    
    isLoading.value = false;
    console.log('Monaco编辑器加载成功');
  } catch (error) {
    console.error('Monaco编辑器初始化失败:', error);
    loadFailed.value = true;
    isLoading.value = false;
    ElMessage.error('编辑器初始化失败，请检查控制台错误信息');
  }
};

// 确定代码
const confirmCode = () => {
  if (!editor) {
    ElMessage.warning('编辑器尚未初始化完成');
    return;
  }
  
  try {
    // 验证JSON格式是否正确
    const value = editor.getValue();
    emit('confirm', value);
    ElMessage.success('DSL代码已确定');
  } catch (e) {
    ElMessage.error('JSON格式错误，请修正后再确定');
  }
};

// 复制代码到剪贴板
const copyCode = async () => {
  if (!editor) {
    ElMessage.warning('编辑器尚未初始化完成');
    return;
  }
  
  try {
    const value = editor.getValue();
    const success = await copyToClipboard(value, 'DSL代码已复制到剪贴板');
    
    // 如果复制失败，帮助用户选中所有内容以便手动复制
    if (!success && editor) {
      selectEditorContent(editor);
    }
  } catch (e) {
    console.error('复制操作失败:', e);
    ElMessage.error('复制失败，请手动选择并复制');
    // 帮助用户选中内容
    if (editor) {
      selectEditorContent(editor);
    }
  }
};

// 组件挂载时创建编辑器
onMounted(() => {
  nextTick(() => {
    try {
      createEditor();
      
      // 监听窗口大小变化，调整编辑器尺寸
      window.addEventListener('resize', handleResize);
    } catch (error) {
      console.error('创建编辑器失败:', error);
      loadFailed.value = true;
      isLoading.value = false;
    }
  });
});

// 处理窗口大小变化
const handleResize = () => {
  if (!editor || !editorContainer.value) return;
  
  editor.layout({
    width: editorContainer.value?.clientWidth || 800,
    height: editorContainer.value?.clientHeight || 600
  });
};

// 组件卸载前销毁编辑器实例，避免内存泄漏
onBeforeUnmount(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleResize);
  
  // 销毁编辑器
  if (editor) {
    editor.dispose();
    editor = null;
  }
});
</script>

<script lang="ts">
export default {
  name: 'DslCodeEditor'
};
</script>

<style lang="scss" scoped>
.dsl-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 12px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    height: 36px;
    z-index: 1;
  }
  
  .editor-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    
    &.status-editing {
      background-color: #f0f9eb;
      color: #67c23a;
    }
    
    &.status-readonly {
      background-color: #f4f4f5;
      color: #909399;
    }
  }
  
  .editor-main {
    flex: 1;
    height: calc(100% - 28px);
    overflow: hidden;
    position: relative;
    
    .monaco-container {
      width: 100%;
      height: 100%;
    }
    
    .editor-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 10;
      
      .loading-icon {
        font-size: 24px;
        color: #409EFF;
        margin-bottom: 8px;
        animation: rotating 2s linear infinite;
      }
      
      span {
        color: #606266;
        font-size: 14px;
      }
    }
    
    .editor-load-failed {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: #f8f8f8;
      z-index: 10;
      
      .failed-icon {
        font-size: 24px;
        color: #F56C6C;
        margin-bottom: 8px;
      }
      
      span {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式调整
@media (max-width: 768px) {
  .dsl-editor-container {
    .editor-toolbar {
      flex-direction: column;
      height: auto;
      padding: 4px 8px;
      
      .toolbar-left, .toolbar-right {
        width: 100%;
        margin-bottom: 4px;
      }
    }
    
    .editor-main {
      height: calc(100% - 60px);
    }
  }
}
</style> 