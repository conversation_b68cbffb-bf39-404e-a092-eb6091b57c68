<template>
  <div class="workflow-graph-wrapper">
    <div :class="['main-content', { 'with-editor': showDslEditor }]">
      <div class="graph-panel" :style="{ width: graphPanelWidth }">
        <div v-show="workflowInternal" v-loading="internalDslLoading" class="graph-container">
          <!-- 使用WorkflowGraph组件 -->
          <workflow-graph
            ref="workflowGraphRef"
            :workflow="workflowInternal"
            :execution-status="executionStatus"
            :is-editing="isEditing"
            :showDslEditor="showDslEditor"
            @node-click="handleNodeClick"
            @start-node-click="handleStartNodeClick"
            @end-node-click="handleEndNodeClick"
            @workflow-info-update="handleWorkflowInfoUpdate"
            @execution-indicator-click="handleExecutionIndicatorClick"
            @toggle-dsl-editor="handleToggleDslEditor"
          />
          
          <!-- 添加节点的悬浮按钮，只在编辑模式下显示 -->
          <div v-if="isEditing && workflowInternal" class="add-node-button">
            <el-tooltip content="添加新节点" placement="top">
              <el-button type="primary" circle @click="handleAddNode">
                <el-icon><Plus /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div v-if="!workflowInternal" class="empty-state">
          <el-empty>
            <template #description>
              <p>请点击【DSL编辑】开始服务编排</p>
            </template>
            <div style="display: flex; gap: 10px;">
              <el-button type="primary" @click="startEditingDsl">开始编辑DSL</el-button>
              <el-button type="info" @click="openSampleSelector">选择示例</el-button>
            </div>
          </el-empty>
        </div>
      </div>
      
      <!-- 拖动分隔条 -->
      <div 
        v-if="showDslEditor" 
        class="resizer"
        :style="{ left: resizerPosition }"
        @mousedown="startResize"
        @dblclick="resetPanelWidths"
      >
        <div class="resizer-handle">
          <el-icon><DArrowLeft /></el-icon>
          <el-icon><DArrowRight /></el-icon>
        </div>
      </div>
      
      <div v-if="showDslEditor" class="editor-panel" :style="{ width: editorPanelWidth }">
        <div class="editor-container">
          <!-- 使用v-bind和v-on代替v-model -->
          <dsl-code-editor
            v-if="showDslEditor"
            :modelValue="dslJsonInternal"
            @update:modelValue="handleDslUpdate"
            :isEditing="isEditing"
            style="flex: 1; height: 100%"
            @confirm="handleDslConfirm"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { DArrowLeft, DArrowRight, Plus } from '@element-plus/icons-vue';
import { ApiResponse } from '../../../../shared/types/response';
// 导入组件
import WorkflowGraph from './WorkflowGraph.vue';
import DslCodeEditor from './DslCodeEditor.vue';
import { parseDsl } from '../../request/orchestration';

// 使用any类型代替具体类型，避免导入错误
type Task = any;
type Workflow = any;

const props = defineProps({
  workflow: {
    type: Object as () => Workflow | null,
    default: null
  },
  dslJson: {
    type: String,
    required: true
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  executionStatus: {
    type: Object as () => Record<string, string>,
    default: () => ({})
  },
  showDslEditor: {
    type: Boolean,
    default: false
  },
  dslLoading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits([
  'node-click', 
  'start-node-click', 
  'end-node-click', 
  'execution-indicator-click', 
  'workflow-info-update',
  'update:dslJson',
  'dsl-confirm',
  'start-editing-dsl',
  'open-sample-selector',
  'reset-workflow-graph',
  'toggle-dsl-editor',
  'add-node'
]);

// 内部状态
const workflowGraphRef = ref<any>(null);
const dslJsonInternal = ref(props.dslJson);
const workflowInternal = ref<Workflow | null>(props.workflow);
const internalDslLoading = ref(props.dslLoading);

// 内部解析DSL的方法
const parseDslInternal = async (dsl: string) => {
  if (!dsl) {
    workflowInternal.value = null;
    return;
  }
  
  try {
    // 设置加载状态
    internalDslLoading.value = true;
    
    console.log('开始调用后端解析接口...');
    console.log('发送到后端的DSL数据长度:', dsl.length);
    
    // 调用API解析DSL
    const responseData = await parseDsl(dsl);
    console.log('后端解析返回的响应类型:', typeof responseData);
    console.log('后端解析返回的响应结构:', Object.keys(responseData));
    
    // 确保响应数据格式正确
    if (!responseData) {
      console.error('解析DSL响应为空');
      ElMessage.error('解析DSL响应为空');
      workflowInternal.value = null;
      return;
    }
    
    // 处理非标准响应（可能是HTTP错误）
    if ((responseData as any).status && (responseData as any).status !== 200) {
      console.error('解析DSL HTTP错误:', responseData);
      ElMessage.error(`解析DSL请求失败: ${(responseData as any).status} ${(responseData as any).statusText || ''}`);
      workflowInternal.value = null;
      return;
    }
    
    // 尝试解析响应数据
    const response = responseData as unknown as ApiResponse<Workflow>;
    console.log('解析DSL响应状态码:', response.code);
    console.log('解析DSL响应消息:', response.message);
    
    if (response.code === 0 && response.data) {
      console.log('DSL解析成功，数据结构:', Object.keys(response.data));
      console.log('工作流名称:', response.data.name);
      console.log('任务数量:', response.data.taskMap ? Object.keys(response.data.taskMap).length : 0);
      
      // 获取新的工作流数据
      const newWorkflow = response.data;
      
      // 检查当前是否已有工作流对象，使用引用替换确保Vue响应式更新
      if (workflowInternal.value) {
        workflowInternal.value = { ...newWorkflow };
        console.log('完全替换工作流对象，确保变更被监听到');
      } else {
        // 首次加载时直接赋值
        console.log('首次加载工作流，直接赋值');
        workflowInternal.value = newWorkflow;
      }
      
      // 确保工作流对象的关键字段存在
      if (workflowInternal.value) {
        if (!workflowInternal.value.taskMap) {
          console.warn('工作流缺少taskMap字段，初始化为空对象');
          workflowInternal.value.taskMap = {};
        }
        
        if (!workflowInternal.value.name) {
          console.warn('工作流缺少name字段，设置默认名称');
          workflowInternal.value.name = '未命名工作流';
        }
        
        // 检查任务数量
        const taskCount = Object.keys(workflowInternal.value.taskMap).length;
        console.log(`工作流包含 ${taskCount} 个任务`);
        
        if (taskCount === 0) {
          console.warn('工作流不包含任何任务');
        }
      }
      
      // 延迟适应视图，确保组件已完全渲染
      nextTick(() => {
        if (workflowGraphRef.value && typeof workflowGraphRef.value.fitView === 'function') {
          workflowGraphRef.value.fitView();
          console.log('已适应工作流视图');
        }
      });
      
      ElMessage.success('DSL解析成功');
    } else {
      // 详细记录错误信息
      const errorMsg = response.message || '未知错误';
      console.error('DSL解析失败详情:', {
        code: response.code,
        message: errorMsg,
        response: JSON.stringify(response).substring(0, 500) + '...'
      });
      ElMessage.error(`DSL解析失败: ${errorMsg}`);
      workflowInternal.value = null;
    }
  } catch (error: any) {
    // 更详细地处理错误信息
    const errorMessage = error?.message || '未知错误';
    console.error('解析DSL过程发生错误:', error);
    console.error('错误类型:', error?.name);
    console.error('错误详情:', errorMessage);
    console.error('错误栈:', error?.stack);
    ElMessage.error(`DSL解析失败: ${errorMessage}`);
    workflowInternal.value = null;
  } finally {
    // 结束加载状态
    internalDslLoading.value = false;
  }
};

// 同步外部dslJson到内部状态
watch(() => props.dslJson, (newVal) => {
  dslJsonInternal.value = newVal;
  
  // 如果外部没有提供workflow，则需要解析dslJson
  if (!props.workflow && newVal) {
    parseDslInternal(newVal);
  }
}, { immediate: true });

// 同步外部workflow到内部状态
watch(() => props.workflow, (newVal) => {
  workflowInternal.value = newVal;
}, { immediate: true });

// 同步外部loading状态到内部状态
watch(() => props.dslLoading, (newVal) => {
  internalDslLoading.value = newVal;
}, { immediate: true });

// 监听内部dslJson的变化并向外部发射更新事件
watch(() => dslJsonInternal.value, (newVal) => {
  emit('update:dslJson', newVal);
  
  // 如果外部没有提供workflow，并且DSL内容变化，则自动重新解析
  if (!props.workflow && newVal && newVal !== props.dslJson) {
    console.log('DSL内容已更新，自动重新解析');
    parseDslInternal(newVal);
  }
});

// #region 面板拖动分隔相关功能
// 拖动分隔条相关状态管理
const isResizing = ref(false);
const startX = ref(0);
const startLeftWidth = ref(0);
const graphWidthPercent = ref(50); // 初始图形面板宽度为50%

// 计算graph-panel和editor-panel的宽度
const graphPanelWidth = computed(() => {
  if (!props.showDslEditor) return '100%';
  return `${graphWidthPercent.value}%`;
});

const editorPanelWidth = computed(() => {
  return `${100 - graphWidthPercent.value}%`;
});

// 计算分隔条位置
const resizerPosition = computed(() => {
  return `calc(${graphWidthPercent.value}% - 4px)`;
});

// 开始拖动
const startResize = (event: MouseEvent) => {
  isResizing.value = true;
  startX.value = event.clientX;
  
  // 获取当前graph-panel的宽度
  const leftPanel = document.querySelector('.graph-panel');
  if (leftPanel) {
    startLeftWidth.value = leftPanel.clientWidth;
  }
  
  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', stopResize);
  
  // 更改鼠标样式
  document.body.style.cursor = 'col-resize';
  
  // 添加阻止选择类
  document.body.classList.add('resizing');
};

// 拖动过程
const handleMouseMove = (event: MouseEvent) => {
  if (!isResizing.value) return;
  
  // 计算移动距离
  const deltaX = event.clientX - startX.value;
  
  // 获取容器宽度
  const container = document.querySelector('.main-content');
  if (!container) return;
  
  const containerWidth = container.clientWidth;
  
  // 计算新的左侧宽度
  const newLeftWidth = startLeftWidth.value + deltaX;
  
  // 计算左侧宽度百分比，限制在20%~80%之间
  let newWidthPercent = Math.round((newLeftWidth / containerWidth) * 100);
  newWidthPercent = Math.max(20, Math.min(80, newWidthPercent));
  
  // 更新宽度百分比
  graphWidthPercent.value = newWidthPercent;
  
  // 在拖动时触发画布适应
  if (workflowGraphRef.value && typeof workflowGraphRef.value.fitView === 'function') {
    workflowGraphRef.value.fitView();
  }
};

// 停止拖动
const stopResize = () => {
  isResizing.value = false;
  
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', stopResize);
  
  // 恢复鼠标样式
  document.body.style.cursor = '';
  
  // 移除阻止选择类
  document.body.classList.remove('resizing');
  
  // 重新适应画布
  setTimeout(() => {
    if (workflowGraphRef.value && typeof workflowGraphRef.value.fitView === 'function') {
      workflowGraphRef.value.fitView();
    }
  }, 100);
};

// 双击分隔条重置宽度
const resetPanelWidths = () => {
  graphWidthPercent.value = 50; // 重置为50%
  
  // 重新适应画布
  setTimeout(() => {
    if (workflowGraphRef.value && typeof workflowGraphRef.value.fitView === 'function') {
      workflowGraphRef.value.fitView();
    }
  }, 100);
};
// #endregion

// #region 事件处理函数
// 处理节点点击
const handleNodeClick = (task: Task) => {
  emit('node-click', task);
};

// 处理开始节点点击
const handleStartNodeClick = () => {
  emit('start-node-click');
};

// 处理结束节点点击
const handleEndNodeClick = () => {
  emit('end-node-click');
};

// 处理工作流信息更新
const handleWorkflowInfoUpdate = (data: any) => {
  emit('workflow-info-update', data);
};

// 处理执行指示器点击
const handleExecutionIndicatorClick = (task: Task) => {
  emit('execution-indicator-click', task);
};

// 处理DSL确认
const handleDslConfirm = (dsl: string) => {
  dslJsonInternal.value = dsl;
  emit('dsl-confirm', dsl);
};

// 处理DSL更新
const handleDslUpdate = (dsl: string) => {
  dslJsonInternal.value = dsl;
  emit('update:dslJson', dsl);
};

// 处理开始编辑DSL
const startEditingDsl = () => {
  emit('start-editing-dsl');
};

// 处理打开示例选择器
const openSampleSelector = () => {
  emit('open-sample-selector');
};

// 处理切换DSL编辑器
const handleToggleDslEditor = (show: boolean) => {
  emit('toggle-dsl-editor', show);
};

// 处理添加新节点
const handleAddNode = () => {
  emit('add-node');
};
// #endregion

// 暴露方法
defineExpose({
  // 重置工作流图
  resetWorkflowGraph: () => {
    if (workflowGraphRef.value && typeof workflowGraphRef.value.resetNodeStatus === 'function') {
      workflowGraphRef.value.resetNodeStatus();
      
      nextTick(() => {
        if (typeof workflowGraphRef.value.fitView === 'function') {
          workflowGraphRef.value.fitView();
        }
      });
    }
  },
  // 调整视图适应内容
  fitView: () => {
    if (workflowGraphRef.value && typeof workflowGraphRef.value.fitView === 'function') {
      workflowGraphRef.value.fitView();
    }
  },
  // 更新工作流图（不重建）
  updateWithoutRebuild: (workflow: Workflow) => {
    if (workflowGraphRef.value && typeof workflowGraphRef.value.updateWithoutRebuild === 'function') {
      workflowGraphRef.value.updateWithoutRebuild(workflow);
    }
  },
  // 更新输入参数
  updateInputParams: (params: any) => {
    if (workflowGraphRef.value && typeof workflowGraphRef.value.updateInputParams === 'function') {
      workflowGraphRef.value.updateInputParams(params);
    }
  }
});

// 组件挂载时
onMounted(() => {
  // 延迟适应视图，确保组件已完全渲染
  nextTick(() => {
    if (workflowGraphRef.value && typeof workflowGraphRef.value.fitView === 'function') {
      workflowGraphRef.value.fitView();
    }
  });
});

// 监视showDslEditor的变化
watch(() => props.showDslEditor, (newVal) => {
  // 当编辑器状态变化时，特别是隐藏DSL编辑器时，需要重新调整流程图
  nextTick(() => {
    // 给DOM一点时间进行更新，但不要太长时间，避免卡顿感
    setTimeout(() => {
      if (workflowGraphRef.value) {
        // 使用更高效的方式更新而不是完全重建
        if (!newVal && props.workflow && typeof workflowGraphRef.value.fitView === 'function') {
          // 直接适应视图而不是先更新再适应
          workflowGraphRef.value.fitView();
          console.log('DSL编辑器状态变化，调整流程图视图');
        }
      }
    }, 50); // 减少延迟时间，提高响应性
  });
});

// 监视workflow的变化，当工作流数据更新时重新渲染图表
watch(() => props.workflow, (newVal, oldVal) => {
  if (newVal) {
    // 工作流对象更新时，确保正确显示
    nextTick(() => {
      setTimeout(() => {
        // 更新图表
        if (workflowGraphRef.value && typeof workflowGraphRef.value.updateWithoutRebuild === 'function') {
          workflowGraphRef.value.updateWithoutRebuild(newVal);
          
          // 然后适应视图
          if (typeof workflowGraphRef.value.fitView === 'function') {
            workflowGraphRef.value.fitView();
            console.log('工作流数据变化，重新调整视图');
          }
        }
      }, 200);
    });
  }
}, { deep: true });

// 监视workflowGraphRef的变化，来控制重绘逻辑
// 使用一个防抖函数减少频繁调用
const debounce = (func: Function, wait: number) => {
  let timeout: number | null = null;
  return function(...args: any[]) {
    // @ts-ignore
    const context = this;
    if (timeout) window.clearTimeout(timeout);
    timeout = window.setTimeout(() => {
      timeout = null;
      func.apply(context, args);
    }, wait);
  };
};

// 防抖后的fitView函数
const debouncedFitView = debounce(() => {
  if (workflowGraphRef.value && typeof workflowGraphRef.value.fitView === 'function') {
    workflowGraphRef.value.fitView();
  }
}, 100);

// 优化展示和隐藏编辑器的性能
watch(() => props.showDslEditor, (newVal) => {
  // 使用防抖函数避免短时间内多次调用fitView
  debouncedFitView();
}, { immediate: false });
</script>

<style lang="scss" scoped>
.workflow-graph-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  
  .main-content {
    display: flex;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    position: relative; /* 添加相对定位，用于分隔条定位 */
    
    &.with-editor {
      .graph-panel {
        width: 50%;
        border-right: none;
      }
      
      .editor-panel {
        width: 50%;
      }
    }
    
    .graph-panel, .editor-panel {
      height: 100%;
      overflow: hidden;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      background-color: #fff;
      transition: width 0.15s ease; /* 加快过渡速度 */
      // 确保内容不会超出面板边界
      position: relative;
      
      // 启用硬件加速但不用过多的3D变换，减少渲染负担
      will-change: width;
    }
    
    .graph-container, .editor-container {
      height: 100%;
      width: 100%;
      // 确保容器正确显示
      position: relative;
    }
    
    // 添加节点按钮样式
    .add-node-button {
      position: absolute;
      right: 20px;
      bottom: 20px;
      z-index: 100;
      // 加入视觉悬浮效果
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
      border-radius: 50%;
      // 透明度和过渡效果
      opacity: 0.9;
      transition: all 0.3s ease;
      
      .el-button {
        width: 52px;
        height: 52px;
        font-size: 20px;
      }
      
      &:hover {
        opacity: 1;
        transform: scale(1.05);
      }
    }
    
    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      background-color: #fafafa;
      
      p {
        margin-bottom: 15px;
      }
    }
  }
  
  // 拖动分隔条相关样式
  .resizer {
    position: absolute;
    top: 0;
    width: 8px;
    height: 100%;
    cursor: col-resize;
    z-index: 10;
    background-color: #F5F7FA;
    transition: background-color 0.15s, left 0.15s; /* 加快过渡速度 */
    
    &:hover,
    &:active {
      background-color: #E4E7ED;
    }

    .resizer-handle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      gap: 5px;
      
      .el-icon {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

// 添加全局样式，防止拖动时文本被选中
:global(.resizing) {
  user-select: none !important;
  cursor: col-resize !important;
  
  * {
    cursor: col-resize !important;
  }
}
</style>

<script lang="ts">
export default {
  name: 'WorkflowGraphWrapper'
};
</script> 