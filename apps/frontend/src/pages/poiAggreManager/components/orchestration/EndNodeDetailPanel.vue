<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="emit('update:visible', $event)"
    title="结果配置"
    direction="rtl"
    size="500px"
    :before-close="handleClose"
  >
    <div class="end-node-detail-panel">
      <div class="content-section">
        <!-- 1. 输出定义 -->
        <h3 class="section-title">输出定义</h3>
        <table class="info-table">
          <tbody>
            <tr class="info-row">
              <td class="info-label">输出类型</td>
              <td class="info-value">
                <template v-if="!isEditing">{{ outputs?.type || '未设置' }}</template>
                <el-select v-else v-model="outputsForm.type" class="full-width-select">
                  <el-option label="Map" value="map" />
                  <el-option label="List" value="list" />
                  <el-option label="Object-含基本常量、String、枚举、以及其他任意对象" value="object" />
                  <!-- <el-option label="NULL" value="null" /> -->
                </el-select>
              </td>
            </tr>
            <tr class="info-row">
              <td class="info-label">输出条件</td>
              <td class="info-value">
                <template v-if="!isEditing">
                  <div class="code-container" v-if="outputs?.switch">
                    <ContentViewer :content="outputs.switch" title="输出条件" :isJson="false">
                      <pre><code>{{ outputs.switch }}</code></pre>
                    </ContentViewer>
                  </div>
                  <span v-else>未设置</span>
                </template>
                <el-input 
                  v-else 
                  v-model="outputsForm.switch" 
                  type="textarea" 
                  :rows="2" 
                  placeholder="输出条件表达式"
                />
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 2. 变量定义 -->
        <h3 class="section-title">变量定义</h3>
        <div v-if="!isEditing && (!variablesData || variablesData.length === 0)" class="mb-2">
          <el-alert
            type="info"
            show-icon
            :closable="false"
            title="未设置变量"
          />
        </div>
        <template v-else-if="!isEditing">
          <table class="dependency-table">
            <thead>
              <tr class="table-header">
                <th class="header-cell" :style="showVariablesValues ? 'width: 25%' : 'width: 40%'">变量名(小写字母开头)</th>
                <th class="header-cell" :style="showVariablesValues ? 'width: 40%' : 'width: 60%'">表达式</th>
                <th class="header-cell" style="width: 35%" v-if="showVariablesValues">值</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in variablesData" :key="item.name" class="table-row">
                <td class="table-cell">{{ item.name }}</td>
                <td class="table-cell">
                  <ContentViewer :content="item.expression" :title="`变量 ${item.name} 的表达式`" :isJson="false">
                    {{ item.expression }}
                  </ContentViewer>
                </td>
                <td class="table-cell" v-if="showVariablesValues">
                  <div class="code-container" style="min-height: 24px; max-height: 60px; margin: 0;">
                    <ContentViewer :content="executionResult?.workflowContext?.envMap?.[item.name] === undefined || executionResult?.workflowContext?.envMap?.[item.name] === null ? '-' : executionResult?.workflowContext?.envMap?.[item.name]" :title="`变量 ${item.name} 的值`">
                      <pre><code>{{ executionResult?.workflowContext?.envMap?.[item.name] === undefined || executionResult?.workflowContext?.envMap?.[item.name] === null ? '-' : showFormat(executionResult?.workflowContext?.envMap?.[item.name]) }}</code></pre>
                    </ContentViewer>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </template>
        <div v-else class="variables-edit">
          <el-table :data="variablesFormData" border size="small">
            <el-table-column label="变量名" width="160">
              <template #default="{ row }">
                <el-input v-model="row.name" placeholder="变量名(小写字母开头)" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="表达式">
              <template #default="{ row }">
                <el-input v-model="row.expression" placeholder="表达式" size="small" />
              </template>
            </el-table-column>
            <el-table-column width="80">
              <template #default="{ $index }">
                <div class="button-group">
                  <el-button type="primary" circle size="small" @click="addVariable" class="mr-1">
                    <el-icon><Plus /></el-icon>
                  </el-button>
                  <el-button type="danger" circle size="small" @click="removeVariable($index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
            </el-table>
          <div v-if="variablesFormData.length === 0" class="add-variable-row">
            <el-button type="primary" size="small" @click="addVariable">添加第一个变量</el-button>
          </div>
        </div>
            
        <!-- 3. 转换表达式 -->
        <h3 class="section-title">转换表达式</h3>
        <div v-if="!isEditing">
          <el-alert
            v-if="!outputs?.transform"
            type="info"
            show-icon
            :closable="false"
            title="未设置转换表达式"
          />
          <div v-else class="code-container">
            <ContentViewer :content="outputs.transform" title="转换表达式" :isJson="false">
              <pre><code>{{ showFormat(outputs.transform) }}</code></pre>
            </ContentViewer>
          </div>
        </div>
        <div v-else>
          <el-input 
            v-model="outputsForm.transform" 
            type="textarea" 
            :rows="4" 
            placeholder="转换表达式"
            class="code-input"
          />
        </div>
        
        <!-- 4. 执行结果 -->
        <template v-if="executionResult">
        <h3 class="section-title">执行结果</h3>
          <el-alert
            :type="executionResult?.code === 0 ? 'success' : 'error'"
            :title="executionResult?.code === 0 ? '执行成功' : '执行出错'"
            :description="executionResult?.code === 0 ? '' : executionResult?.message"
            show-icon
            :closable="false"
            class="mb-2"
          />
          
      
          <h3 class="section-title section-title-sub">输出数据</h3>
          <div class="code-container">
            <ContentViewer :content="executionResult.data" title="输出数据">
              <pre><code>{{ showFormat(executionResult.data) }}</code></pre>
            </ContentViewer>
          </div>
          
          <!-- 5. 环境变量信息 -->
          <h3 class="section-title section-title-sub">环境变量（全局共享）</h3>
          <div class="env-variables-container">
            <table class="dependency-table">
              <thead>
                <tr class="table-header">
                  <th class="header-cell" style="width: 40%">变量名</th>
                  <th class="header-cell" style="width: 60%">值</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(value, key) in executionResult.workflowContext?.envMap || {}" :key="key" class="table-row">
                  <td class="table-cell">{{ key }}</td>
                  <td class="table-cell">
                    <div class="code-container" style="min-height: 24px; max-height: 60px; margin: 0;">
                      <ContentViewer :content="value === undefined || value === null ? '-' : value" :title="`变量 ${key} 的值`">
                        <pre><code>{{ value === undefined || value === null ? '-' : showFormat(value) }}</code></pre>
                      </ContentViewer>
                    </div>
                  </td>
                </tr>
                <tr v-if="!executionResult.workflowContext?.envMap || Object.keys(executionResult.workflowContext?.envMap).length === 0" class="table-row">
                  <td class="table-cell" colspan="2" style="text-align: center;">无环境变量</td>
                </tr>
              </tbody>
            </table>
          </div>
        </template>
      </div>

      <div class="drawer-footer">
        <template v-if="isEditing">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="confirm">确认</el-button>
        </template>
        <template v-else>
          <el-button v-if="canEdit" @click="handleEdit" type="primary">编辑</el-button>
        </template>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, computed, watch } from 'vue';
import { Delete, Plus } from '@element-plus/icons-vue';
import { Response, Outputs } from '../../types/orchestration';
import { ElMessage } from 'element-plus';
import ContentViewer from '../common/ContentViewer.vue';
import { showFormat, writeFormat } from '../../utils/orchestrationUtils';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  outputs: {
    type: Object as () => Outputs | null,
    default: null
  },
  executionResult: {
    type: Object as () => Response<any> | null,
    default: null
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  canEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'close', 'confirm', 'cancel', 'edit']);

// 将变量对象(Record<string, string>)转换为可编辑的数组
const variablesData = computed(() => {
  if (!props.outputs?.variables) {
    return [];
  }
  
  try {
    // 直接使用Object.entries处理对象
    return Object.entries(props.outputs.variables).map(([name, expression]) => ({
      name,
      expression: String(expression)
    }));
  } catch (error) {
    console.error('处理变量失败:', error);
    return [];
  }
});

// 判断是否显示值列
const showVariablesValues = computed(() => {
  return !!(props.executionResult && props.executionResult.workflowContext && props.executionResult.workflowContext.envMap);
});

// 简单定义输出表单对象
const outputsForm = reactive({
  type: 'map',
  switch: '',
  switchExpression: '',
  transform: ''
});

const variablesFormData = ref<Array<{name: string, expression: string}>>([]);

watch(() => props.outputs, (newOutputs) => {
  if (newOutputs) {
    console.log('接收到的outputs:', JSON.stringify(newOutputs));
    
    outputsForm.type = newOutputs.type || 'map';
    
    // 优先使用switch字段，不存在再使用switchExpression
    outputsForm.switch = newOutputs.switch || newOutputs.switchExpression || '';
    console.log('设置switch字段:', outputsForm.switch);
    
    // 始终保持switchExpression字段与switch同步
    outputsForm.switchExpression = outputsForm.switch;
    console.log('设置switchExpression字段:', outputsForm.switchExpression);
    
    outputsForm.transform = showFormat(newOutputs.transform) || '';
    
    variablesFormData.value = [];
    
    if (newOutputs.variables) {
      try {
        // 直接使用Object.entries处理对象
        Object.entries(newOutputs.variables).forEach(([name, expression]) => {
          variablesFormData.value.push({ name, expression: String(expression) });
        });
      } catch (error) {
        console.error('处理变量失败:', error);
      }
    }
  }
}, { immediate: true });

const addVariable = () => {
  variablesFormData.value.push({ name: '', expression: '' });
};

const removeVariable = (index: number) => {
  variablesFormData.value.splice(index, 1);
};

const handleEdit = () => {
  emit('edit');
};

const confirm = () => {
  console.log('variablesFormData.value', variablesFormData.value);
  
  // 验证变量名格式
  const invalidNames = variablesFormData.value
    .filter(item => item.name && !item.name.match(/^[a-z][\w\d]*$/))
    .map(item => item.name);
  
  if (invalidNames.length > 0) {
    ElMessage.error(`以下变量名不符合规范(必须以小写字母开头): ${invalidNames.join(', ')}`);
    return;
  }
  
  // 验证表达式是否为空
  const emptyExpressions = variablesFormData.value
    .filter(item => item.name && (!item.expression || item.expression.trim() === ''))
    .map(item => item.name);
  
  if (emptyExpressions.length > 0) {
    ElMessage.error(`以下变量的表达式不能为空: ${emptyExpressions.join(', ')}`);
    return;
  }
  
  // 直接从数组创建Record<string, string>对象
  const variablesObj = variablesFormData.value.reduce((acc, item) => {
    if (item.name && item.expression) {
      acc[item.name] = item.expression;
    }
    return acc;
  }, {} as Record<string, string>);
  
  // 使用强类型断言，转为Outputs类型
  const outputs = {
    type: outputsForm.type,
    switch: outputsForm.switch,
    switchExpression: outputsForm.switch, // 始终使用switch的值作为switchExpression
    variables: variablesObj,
    transform: writeFormat(outputsForm.transform, 6)
  } as unknown as Outputs;
  
  // 输出确认的输出对象，帮助调试
  console.log('确认的输出:', JSON.stringify(outputs));
  
  emit('confirm', outputs);
};

const cancel = () => {
  emit('cancel');
};

const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};
</script>

<script lang="ts">
export default {
  name: 'EndNodeDetailPanel'
};
</script>

<style lang="scss" scoped>
// 插入全局样式到document头部，确保覆盖element-plus默认样式
:deep(.el-drawer) {
  .el-drawer__header {
    margin: 0 !important;
    padding: 2px 6px !important;
    min-height: 28px !important;
    height: auto !important;
    box-sizing: border-box !important;
    border-bottom: 1px solid #e4e7ed !important;
    display: flex !important;
    align-items: center !important;
  }
  
  .el-drawer__title {
    font-size: 16px !important;
    line-height: 1 !important;
    color: #333 !important;
    margin: 0 !important;
    font-weight: 500 !important;
  }
  
  .el-drawer__close-btn {
    padding: 2px !important;
    margin: 0 !important;
  }
  
  .el-drawer__body {
    padding: 0 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }
}

.end-node-detail-panel {
  padding: 0;
}

// 整体内容区域
.content-section {
  padding: 0 0 50px 0;
  margin: 0;
}

// 移除节点标题的额外空间
.section-title {
  margin: 4px 2px;
  padding: 3px 6px;
  font-size: 12px;
  font-weight: bold;
  color: #409eff;
  background-color: #f0f6ff;
  border-radius: 0;
  border-left: 2px solid #409eff;
  display: flex;
  align-items: center;
  
  &:before {
    display: none; // 移除伪元素
  }
  
  &:first-child {
    margin-top: 0;
    border-top: none;
  }
  
  &.section-title-sub {
    margin: 2px 2px 1px 2px;
    padding: 2px 6px;
    font-size: 11px;
    background-color: #f7faff;
    border-left: 1px solid #66b1ff;
    box-shadow: none;
    color: #66b1ff;
  }
}

// 底部按钮区域
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 6px 10px;
  background: #fff;
  border-top: 1px solid #ebeef5;
  text-align: right;
  z-index: 999;
  width: 100%;
  box-sizing: border-box;
  height: 42px;
  
  :deep(.el-button) {
    padding: 5px 12px;
    font-size: 12px;
    margin-left: 8px;
    height: 28px;
    line-height: 18px;
  }
}

// 表格样式表单
.info-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  font-size: 12px;
  
  .info-row {
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:nth-child(odd) {
      background-color: #fafafa;
    }
    
    .info-label {
      width: 70px;
      padding: 4px 6px;
      color: #606266;
      background-color: #f5f7fa;
      font-weight: 500;
      text-align: right;
      vertical-align: middle;
  font-size: 11px;
}

    .info-value {
      padding: 4px 6px;
      color: #303133;
      vertical-align: middle;
      line-height: 1.3;
      word-break: break-all;
      font-size: 11px;
      
      .el-input__wrapper, 
      .el-textarea__wrapper, 
      .el-select {
        box-shadow: none;
      }
      
      .el-input__wrapper {
        padding: 0 6px;
      }
      
      // 设置输入控件高度
      .el-input__wrapper,
      .el-input__inner {
        height: 22px !important;
        line-height: 22px !important;
      }
      
      .el-textarea__inner {
        min-height: auto !important;
        line-height: 1.2 !important;
      }
      
      .el-select {
        width: 100%;
        .el-input {
          height: 22px !important;
        }
      }
    }
  }
}

// 依赖表格样式
.dependency-table {
  margin: 3px 0;
  border: 1px solid #ebeef5;
  width: 100%;
  font-size: 11px;
  
  .table-header {
    background-color: #f5f7fa;
    font-weight: bold;
    border-bottom: 1px solid #ebeef5;
    
    .header-cell {
      padding: 3px 6px;
      text-align: left;
    }
  }
  
  .table-row {
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .table-cell {
      padding: 2px 6px;
      border-right: 1px solid #ebeef5;
      
      &:last-child {
        border-right: none;
      }
    }
  }
}

// 代码容器
.code-container {
  background-color: #f5f7fa;
  border-radius: 2px;
  padding: 4px;
  max-height: 150px;
  overflow: auto;
  margin: 2px 0;
  font-size: 11px;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  code {
    font-family: Consolas, Monaco, monospace;
    font-size: 11px;
    color: #333;
  }
}

// 代码编辑器输入
.code-input {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  
  :deep(textarea) {
    line-height: 1.3;
    font-size: 11px;
    padding: 2px 4px;
  }
}

.empty-content {
  padding: 2px 0;
  text-align: center;
  font-size: 11px;
}

.variables-edit {
  margin: 4px 2px;
}

.button-group {
  display: flex;
  justify-content: flex-end;
}

.mr-1 {
  margin-right: 4px;
}

// 添加底部额外按钮样式
.add-variable-row {
  margin-top: 4px;
  text-align: center;
}

// Alert组件样式
:deep(.el-alert) {
  padding: 2px 4px !important;
  margin: 3px 0;
  
  .el-alert__content {
    padding: 0 2px !important;
    font-size: 11px;
  }
  
  .el-alert__icon {
    font-size: 11px;
    padding: 0 2px;
  }
}

.mb-2 {
  margin-bottom: 3px;
}

.mt-2 {
  margin-top: 3px;
}

// 设置表格样式为小型
:deep(.el-table--small) {
  font-size: 11px;
  
  .el-input__inner {
    height: 22px !important;
  }
  
  th, td {
    padding: 2px 0 !important;
  }
}

.full-width-select {
  width: 100%;
}

.env-variables-container {
  margin: 2px 0;
  max-height: 300px;
  overflow: auto;
}
</style> 