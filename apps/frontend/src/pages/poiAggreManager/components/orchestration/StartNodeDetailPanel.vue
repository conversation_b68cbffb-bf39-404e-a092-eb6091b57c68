<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="emit('update:visible', $event)"
    title="入参配置"
    direction="rtl"
    size="500px"
    :before-close="handleClose"
  >
    <div class="start-node-detail-panel">
      <div class="content-section">
        <h3 class="section-title">工作流入参</h3>
        <el-form label-position="top">
          <el-form-item label="输入参数 (JSON格式)">
            <el-input 
              v-model="paramJson" 
              type="textarea" 
              :rows="10" 
              placeholder="{ &quot;key&quot;: &quot;value&quot; }"
            />
          </el-form-item>
        </el-form>
        <br />

        <h3 class="section-title">参数示例模板</h3>
        <el-alert
          type="info"
          show-icon
          :closable="false"
          title="点击模板将内容复制到输入参数框"
          class="mb-2"
        />
        
        <div class="template-list">
          <div class="template-item" @click="applyTemplate(template1)">
            <div class="template-header">
              <el-tag size="small" type="primary">聚合查询</el-tag>
              <span class="template-name">查询-简单</span>
            </div>
            <div class="template-content">
              <ContentViewer :content="template1" title="查询-简单模板" asTooltip>
                <pre><code>{{ formatJson(template1) }}</code></pre>
              </ContentViewer>
            </div>
          </div>
          
          <div class="template-item" @click="applyTemplate(template2)">
            <div class="template-header">
              <el-tag size="small" type="primary">聚合查询</el-tag>
              <span class="template-name">查询-依赖信息</span>
            </div>
            <div class="template-content">
              <ContentViewer :content="template2" title="查询-依赖信息模板" asTooltip>
                <pre><code>{{ formatJson(template2) }}</code></pre>
              </ContentViewer>
            </div>
          </div>
          
          <div class="template-item" @click="applyTemplate(template3)">
            <div class="template-header">
              <el-tag size="small" type="primary">聚合查询</el-tag>
              <span class="template-name">同步-获取信息</span>
            </div>
            <div class="template-content">
              <ContentViewer :content="template3" title="同步-获取信息模板" asTooltip>
                <pre><code>{{ formatJson(template3) }}</code></pre>
              </ContentViewer>
            </div>
          </div>
          
          <div class="template-item" @click="applyTemplate(template4)">
            <div class="template-header">
              <el-tag size="small" type="primary">聚合查询</el-tag>
              <span class="template-name">同步-获取变更ids</span>
            </div>
            <div class="template-content">
              <ContentViewer :content="template4" title="同步-获取变更ids模板" asTooltip>
                <pre><code>{{ formatJson(template4) }}</code></pre>
              </ContentViewer>
            </div>
          </div>
        </div>
      </div>

      <div class="drawer-footer">
        <template v-if="isEditing">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="confirmParams">确定</el-button>
        </template>
        <template v-else>
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="confirmParams">确定参数</el-button>
          <el-button v-if="showExecutionForm" type="success" @click="execute">执行工作流</el-button>
        </template>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import ContentViewer from '../common/ContentViewer.vue';

interface InputParam {
  name: string;
  type: string;
  description?: string;
  required?: boolean;
  defaultValue?: any;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  inputParams: {
    type: Array as () => InputParam[],
    default: () => []
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  showExecutionForm: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'close', 'cancel', 'execute', 'confirm-params']);

const paramJson = ref('{\n  \n}');
const inputParams = ref<InputParam[]>([]);

// 模板数据
const template1 = {
  poiIds: [689312, 1283670, 1282960, 1195767, 19999024886, 19999024890],
  fieldsSet: ["wm_logistics_type", "wm_label_ids", "wm_sub_wm_poi_type"]
};

const template2 = {
  poiIds: [1909564, 689312, 1283670, 1282960, 1195767, 19999024886, 19999024890],
  fieldsSet: ["wm_logistics_type", "wm_label_ids", "wm_sub_wm_poi_type", "second_delivery_category_name"],
  aggreMap: {
    "1909564": {
      poiId: 1909564,
      aggreInfoExist: false,
      customerTagLevels: null,
      autoSendLeadTime: 0,
      autoSendStatus: 0,
      customerOperationPlan: null,
      dispatchConfig: null,
      contractLatestInfo: null,
      customerOperationLeadTime: null,
      bmPoiBaseBo: {
        id: 12317948543,
        poiId: 1909564,
        customerSourceType: 1,
        poiName: "医药B2C下线测试勿动",
        customerId: 0,
        poiContactName: "测试",
        poiContactPhone: "13194618646",
        poiContactEmail: null,
        cityIdLevelTwo: 110100,
        bmCityId: 0,
        poiAddress: "北京市朝阳区北苑22号楼687室",
        poiAddressDetail: "",
        longitude: *********,
        latitude: ********,
        picUrl: "http://p0.inf.test.sankuai.com/qua/d61783925b9aa32dc50386e684d5e275100915.png",
        outerId: "",
        online: 1,
        open: 3,
        hkPoiType: 0,
        companyCustomerId: 0,
        hkCategory: 0,
        hkSecondCategory: 0,
        ctime: **********,
        utime: **********,
        valid: 1
      },
      bmPoiDeliveryBaseBo: null,
      bmPoiDeliveryTimeBo: null,
      bmPoiDeliveryCapacityBo: null,
      brandId: 0,
      settleId: 0,
      settleAccountName: null,
      businessBrandIds: null,
      shopFlagForSpecialTime: 0,
      settleMode: 0,
      settleAssetAccountId: 0,
      settleAssetAccountType: 0,
      customerBusinessTypeIds: null,
      checkFetchMealCode: false,
      wmAggreInfoExist: false,
      wmLogisticsTypes: null,
      wmLabelIds: null,
      wmSubWmPoiType: 0,
      wmOwnerType: 0,
      wmAgentId: 0,
      wmAorId: 0,
      wmLogisticsIds: null,
      wmBrandType: 0,
      result: {}
    }
  }
};

// 添加新模板
const template3 = {
  outerIds: [689312, 1283670, 1282960, 1195767],
  scene: 40001
};

const template4 = {
  limit: 100,
  startTime: **********,
  endTime: **********,
  scrollId: 689312
};

// 监听props.inputParams变化，更新本地inputParams
watch(() => props.inputParams, (newVal) => {
  if (newVal) {
    inputParams.value = JSON.parse(JSON.stringify(newVal));
  }
}, { immediate: true });

// 在工作流图中显示参数的标志
const hasParams = ref(false);

// 应用模板
const applyTemplate = (template: any): void => {
  paramJson.value = JSON.stringify(template, null, 2);
  ElMessage.success('已应用模板');
};

// 确定参数
const confirmParams = () => {
  try {
    // 验证JSON格式
    JSON.parse(paramJson.value);
    
    // 更新本地状态
    hasParams.value = true;
    
    // 发送事件通知父组件更新开始节点显示，传递解析后的对象和原始JSON字符串
    emit('confirm-params', JSON.parse(paramJson.value), paramJson.value);
    
    // 关闭侧边栏
    ElMessage.success('参数已确定');
    handleClose();
  } catch (error) {
    ElMessage.error('参数格式不正确，请检查JSON格式');
    return false;
  }
  return true;
};

// 格式化JSON
const formatJson = (value: any): string => {
  try {
    return JSON.stringify(value, null, 2);
  } catch (e) {
    return '{}';
  }
};

const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const cancel = () => {
  emit('cancel');
};

const execute = () => {
  try {
    // 先验证JSON格式
    const parsedParams = JSON.parse(paramJson.value);
    
    // 先确定参数（传递解析后的对象和原始JSON字符串）
    emit('confirm-params', parsedParams, paramJson.value);
    
    // 再执行工作流（直接传递JSON字符串）
    emit('execute', paramJson.value);
    
    // 关闭侧边栏
    handleClose();
  } catch (error) {
    ElMessage.error('参数格式不正确，请检查JSON格式');
  }
};
</script>

<script lang="ts">
export default {
  name: 'StartNodeDetailPanel'
};
</script>

<style lang="scss" scoped>
// 插入全局样式到document头部，确保覆盖element-plus默认样式
:deep(.el-drawer) {
  .el-drawer__header {
    margin: 0 !important;
    padding: 2px 6px !important;
    min-height: 28px !important;
    height: auto !important;
    box-sizing: border-box !important;
    border-bottom: 1px solid #e4e7ed !important;
    display: flex !important;
    align-items: center !important;
  }
  
  .el-drawer__title {
    font-size: 16px !important;
    line-height: 1 !important;
    color: #333 !important;
    margin: 0 !important;
    font-weight: 500 !important;
  }
  
  .el-drawer__close-btn {
    padding: 2px !important;
    margin: 0 !important;
  }
  
  .el-drawer__body {
    padding: 0 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }
}

.start-node-detail-panel {
  padding: 0;
}

// 整体内容区域
.content-section {
  padding: 0 0 50px 0;
  margin: 0;
}

// 移除节点标题的额外空间
.section-title {
  margin: 4px 2px;
  padding: 3px 6px;
  font-size: 12px;
  font-weight: bold;
  color: #409eff;
  background-color: #f0f6ff;
  border-radius: 0;
  border-left: 2px solid #409eff;
  display: flex;
  align-items: center;
  
  &:before {
    display: none; // 移除伪元素
  }
  
  &:first-child {
    margin-top: 0;
    border-top: none;
  }
}

// 底部按钮区域
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 6px 10px;
  background: #fff;
  border-top: 1px solid #ebeef5;
  text-align: right;
  z-index: 999;
  width: 100%;
  box-sizing: border-box;
  height: 42px;
  
  :deep(.el-button) {
    padding: 5px 12px;
    font-size: 12px;
    margin-left: 8px;
    height: 28px;
    line-height: 18px;
  }
}

// 代码容器
.code-container {
  background-color: #f5f7fa;
  border-radius: 0;
  padding: 3px;
  max-height: 200px;
  overflow: auto;
  margin: 0 2px 5px 2px;
  font-size: 11px;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  code {
    font-family: Consolas, Monaco, monospace;
    font-size: 11px;
    color: #333;
  }
}

// 模板列表样式
.template-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 0 2px;
}

.template-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-color: #c6e2ff;
  }
}

.template-header {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  .template-name {
    margin-left: 8px;
    font-size: 12px;
    font-weight: 500;
  }
}

.template-content {
  padding: 6px;
  background-color: #fff;
  max-height: 120px;
  overflow: auto;
  
  pre {
    margin: 0;
    padding: 0;
    
    code {
      font-family: Consolas, Monaco, monospace;
      font-size: 11px;
      line-height: 1.4;
      color: #333;
    }
  }
}

// Alert组件样式
:deep(.el-alert) {
  padding: 3px 6px !important;
  margin: 0 2px 5px 2px;
  
  .el-alert__content {
    padding: 0 3px !important;
    font-size: 12px;
  }
  
  .el-alert__icon {
    font-size: 12px;
    padding: 0 2px;
  }
}

.mb-2 {
  margin-bottom: 3px;
}

.mt-2 {
  margin-top: 3px;
}
</style> 