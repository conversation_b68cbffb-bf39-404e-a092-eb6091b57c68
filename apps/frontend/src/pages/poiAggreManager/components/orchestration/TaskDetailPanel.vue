<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="emit('update:visible', $event)"
    :title="task ? `节点详情: ${task.alias}` : '节点详情'"
    direction="rtl"
    size="30%"
    :before-close="handleClose"
  >
    <template v-if="task">
      <div class="content-section">
        <!-- 1. 基本信息 -->
        <h3 class="section-title">基本信息</h3>
        <table class="info-table">
          <tbody>
            <tr class="info-row">
              <td class="info-label">别名</td>
              <td class="info-value">
                <template v-if="!isEditing">{{ task.alias }}</template>
                <el-input v-else v-model="taskForm.alias" placeholder="请输入任务别名" />
              </td>
            </tr>
            <tr class="info-row">
              <td class="info-label">类型</td>
              <td class="info-value">
                <template v-if="!isEditing">{{ task.taskType }}</template>
                <el-select v-else v-model="taskForm.taskType" size="small" class="full-width-select">
                  <el-option label="计算" value="Calculate" />
                  <el-option label="Thrift" value="ThriftGeneric" />
                  <el-option label="Squirrel" value="Squirrel" />
                </el-select>
              </td>
            </tr>
            <tr class="info-row">
              <td class="info-label">描述</td>
              <td class="info-value">
                <template v-if="!isEditing">
                  {{ task.description || '-' }}
                </template>
                <el-input v-else v-model="taskForm.description" size="small" placeholder="请输入任务描述" />
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- 2. 依赖关系 -->
        <h3 class="section-title">依赖关系</h3>
        <div v-if="!task.dependencyTaskMap || Object.keys(task.dependencyTaskMap).length === 0" class="mb-2">
          <el-alert
            type="info"
            show-icon
            :closable="false"
            title="无依赖节点"
          />
        </div>
        <table v-else class="dependency-table">
          <thead>
            <tr class="table-header">
              <th class="header-cell" style="width: 40%">节点别名</th>
              <th class="header-cell" style="width: 40%">节点类型</th>
              <th class="header-cell" style="width: 20%">执行层级</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in dependenciesData" :key="item.alias" class="table-row">
              <td class="table-cell">{{ item.alias }}</td>
              <td class="table-cell">{{ item.taskType }}</td>
              <td class="table-cell">{{ item.level }}</td>
            </tr>
          </tbody>
        </table>
        
        <!-- 3. 调用信息 -->
        <template v-if="isEditing ? taskForm.taskType !== 'Calculate' : task?.taskType !== 'Calculate'">
          <h3 class="section-title">调用信息</h3>
          <table class="info-table">
            <tbody>
              <tr class="info-row">
                <td class="info-label">URL</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    <div class="copy-content">
                      <span class="content-text">{{ task.url || '-' }}</span>
                      <el-button v-if="task.url" class="copy-button" type="primary" link @click="handleCopy(task.url)">
                        <el-icon><DocumentCopy /></el-icon>
                      </el-button>
                    </div>
                  </template>
                  <el-input v-else v-model="taskForm.url" placeholder="请输入服务URL" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">方法名</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    <div class="copy-content">
                      <span class="content-text">{{ task.method || '-' }}</span>
                      <el-button v-if="task.method" class="copy-button" type="primary" link @click="handleCopy(task.method)">
                        <el-icon><DocumentCopy /></el-icon>
                      </el-button>
                    </div>
                  </template>
                  <el-input v-else v-model="taskForm.method" placeholder="请输入方法名" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">远程Appkey</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    <div class="copy-content">
                      <span class="content-text">{{ task.remoteAppkey || '-' }}</span>
                      <el-button v-if="task.remoteAppkey" class="copy-button" type="primary" link @click="handleCopy(task.remoteAppkey)">
                        <el-icon><DocumentCopy /></el-icon>
                      </el-button>
                    </div>
                  </template>
                  <el-input v-else v-model="taskForm.remoteAppkey" placeholder="请输入远程Appkey" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">远程端口</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    {{ task.remoteServerPort || '-' }}
                  </template>
                  <el-input v-else v-model="taskForm.remoteServerPort" placeholder="请输入远程服务端口" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">超时时间</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    {{ task.timeout || '-' }} ms
                  </template>
                  <el-input-number v-else v-model="taskForm.timeout" :min="0" :step="10" style="width: 100%" />
                </td>
              </tr>
            </tbody>
          </table>
        </template>
        
        <!-- 4. 执行条件 -->
        <h3 class="section-title">执行条件</h3>
        <div v-if="isEditing" class="compact-form">
          <div class="condition-label">执行条件表达式:</div>
          <el-input
            v-model="taskForm.switchExpression"
            type="textarea"
            :rows="2"
            placeholder="请输入执行条件表达式"
            class="condition-input"
          />
        </div>
        <div v-else>
          <el-alert
            v-if="!task.switchExpression"
            type="info"
            show-icon
            :closable="false"
            title="无执行条件，默认执行"
          />
          <div v-else class="code-container">
            <ContentViewer :content="task.switchExpression" title="执行条件表达式" :isJson="false">
              <pre><code>{{ task.switchExpression }}</code></pre>
            </ContentViewer>
          </div>
        </div>
        
        <!-- 5. 输入参数 -->
        <h3 class="section-title">输入参数</h3>
        <div v-if="isEditing" class="compact-form">
          <div class="param-label">输入参数:</div>
          <el-input
            v-model="taskForm.inputsJson"
            type="textarea"
            :rows="3"
            placeholder="请输入JSON格式的输入参数"
            class="code-input"
          />
          
          <div class="param-label mt-1">参数类型信息:</div>
          <el-input
            v-model="taskForm.inputsExtraJson"
            type="textarea"
            :rows="2"
            placeholder="请输入JSON格式的输入参数类型信息"
            class="code-input"
          />
        </div>
        <div v-else>
          <el-alert
            v-if="!task.inputs"
            type="info"
            show-icon
            :closable="false"
            title="无输入参数"
          />
          <template v-else>
            <div class="code-container">
              <ContentViewer :content="task.inputs" title="输入参数">
                <pre><code>{{ showFormat(task.inputs) }}</code></pre>
              </ContentViewer>
            </div>
            <div v-if="task.inputsExtra" class="mt-1">
              <div class="subtitle">输入类型信息:</div>
              <div class="code-container">
                <ContentViewer :content="task.inputsExtra" title="输入类型信息">
                  <pre><code>{{ showFormat(task.inputsExtra) }}</code></pre>
                </ContentViewer>
              </div>
            </div>
          </template>
        </div>

        <!-- 6. 行为控制 -->
        <h3 class="section-title">行为控制</h3>
        <table class="info-table">
          <tbody>
            <tr class="info-row">
              <td class="info-label">忽略异常</td>
              <td class="info-value">
                <template v-if="!isEditing">
                  {{ task.ignoreException ? '是' : '否' }}
                </template>
                <el-switch v-else v-model="taskForm.ignoreException" />
              </td>
            </tr>
            <tr class="info-row">
              <td class="info-label">启用缓存</td>
              <td class="info-value">
                <template v-if="!isEditing">
                  {{ task.cacheOption ? '是' : '否' }}
                </template>
                <el-checkbox v-else v-model="enableCache">启用结果缓存</el-checkbox>
              </td>
            </tr>
            <tr class="info-row" v-if="isEditing && enableCache">
              <td class="info-label">缓存配置</td>
              <td class="info-value">
                <div class="cache-option">
                  <div class="cache-option-row">
                    <span class="cache-option-label">过期时间(秒):</span>
                    <el-input-number 
                      v-model="taskForm.cacheOption.expireAfterWriteSeconds" 
                      :min="1" 
                      :step="60" 
                      size="small"
                      controls-position="right"
                      class="compact-number"
                    />
                  </div>
                  <div class="cache-option-row">
                    <span class="cache-option-label">刷新时间(秒):</span>
                    <el-input-number 
                      v-model="taskForm.cacheOption.refreshAfterWriteSeconds" 
                      :min="1" 
                      :step="30" 
                      size="small"
                      controls-position="right"
                      class="compact-number"
                    />
                  </div>
                  <div class="cache-option-row">
                    <span class="cache-option-label">最大缓存数:</span>
                    <el-input-number 
                      v-model="taskForm.cacheOption.maximumSize" 
                      :min="1" 
                      :step="1" 
                      size="small"
                      controls-position="right"
                      class="compact-number"
                    />
                  </div>
                </div>
              </td>
            </tr>
            <tr class="info-row" v-else-if="!isEditing && task.cacheOption">
              <td class="info-label">缓存详情</td>
              <td class="info-value">
                <div class="cache-info">
                  <el-tag size="small" type="info" class="mr-2">过期: {{ task.cacheOption.expireAfterWriteSeconds }}秒</el-tag>
                  <el-tag size="small" type="info" class="mr-2">刷新: {{ task.cacheOption.refreshAfterWriteSeconds }}秒</el-tag>
                  <el-tag size="small" type="info">最大项数: {{ task.cacheOption.maximumSize }}</el-tag>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 7. 服务治理 -->
        <template v-if="isEditing ? taskForm.taskType !== 'Calculate' : task?.taskType !== 'Calculate'">
          <h3 class="section-title">服务治理</h3>
          <table class="info-table">
            <tbody>
              <tr class="info-row">
                <td class="info-label">请求鉴权</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    {{ task.secret || '-' }}
                  </template>
                  <el-input v-else v-model="taskForm.secret" placeholder="请输入鉴权信息" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">泳道</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    {{ task.swimlane || '-' }}
                  </template>
                  <el-input v-else v-model="taskForm.swimlane" placeholder="请输入泳道信息" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">Cell</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    {{ task.cell || '-' }}
                  </template>
                  <el-input v-else v-model="taskForm.cell" placeholder="请输入Cell信息" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">liteSet</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    {{ task.liteSet || '-' }}
                  </template>
                  <el-input v-else v-model="taskForm.liteSet" placeholder="请输入liteSet信息" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">调用端Appkey</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    {{ task.appkey || '-' }}
                  </template>
                  <el-input v-else v-model="taskForm.appkey" placeholder="请输入调用端Appkey" />
                </td>
              </tr>
              <tr class="info-row">
                <td class="info-label">IP端口列表</td>
                <td class="info-value">
                  <template v-if="!isEditing">
                    {{ task.ipPorts || '-' }}
                  </template>
                  <el-input v-else v-model="taskForm.ipPorts" placeholder="请输入IP端口列表" />
                </td>
              </tr>
            </tbody>
          </table>
        </template>
      </div>

      <div class="drawer-footer">
        <template v-if="isEditing">
          <el-button @click="handleCancel" type="default" size="small">取消</el-button>
          <el-button @click="handleConfirm" type="primary" size="small">确定</el-button>
        </template>
        <template v-else>
          <el-button v-if="onEdit" @click="handleEdit" type="primary" size="small">编辑</el-button>
          <el-button v-if="onExecute" @click="handleExecute" type="success" size="small">执行</el-button>
        </template>
      </div>

      <template v-if="isEditing && task">
        <div class="actions-section">
          <el-divider />
          <div class="danger-zone">
            <h3 class="section-title danger">危险操作</h3>
            <div class="danger-actions">
              <el-popconfirm
                title="确定要删除此节点吗？此操作不可撤销！"
                @confirm="handleDeleteNode"
                confirm-button-text="确定删除"
                cancel-button-text="取消"
                confirm-button-type="danger"
              >
                <template #reference>
                  <el-button type="danger" :icon="Delete">删除节点</el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>
        </div>
      </template>
    </template>
    <el-empty v-else description="请选择一个节点查看详情"></el-empty>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, reactive } from 'vue';
import { Task, CacheOption } from '../../types/orchestration';
import { DocumentCopy, Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import ContentViewer from '../common/ContentViewer.vue';
import { showFormat, writeFormat } from '../../utils/orchestrationUtils';
import { copyToClipboard as copyTextToClipboard } from '../../../../shared/utils/copyUtils';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object as () => Task | null,
    default: null
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  onEdit: {
    type: Function,
    default: undefined
  },
  onExecute: {
    type: Function,
    default: undefined
  }
});

const emit = defineEmits(['update:visible', 'close', 'confirm', 'delete-node']);

const enableCache = ref(false);

// 任务表单数据
const taskForm = reactive({
  // 基本信息
  alias: '',
  taskType: 'ThriftGeneric',
  description: '',
  
  // 服务调用信息
  url: '',
  method: '',
  remoteAppkey: '',
  remoteServerPort: '',
  
  // 执行控制
  timeout: 1000,
  ignoreException: false,
  switch: '',         // 前端显示用，但在表单中不需要
  switchExpression: '',
  
  // 缓存配置
  cacheOption: {
    expireAfterWriteSeconds: 1800,
    refreshAfterWriteSeconds: 120,
    maximumSize: 1
  } as CacheOption,
  
  // 请求参数 - 对应Task的inputs和inputsExtra
  inputsJson: '',     // 字符串形式，会被解析为inputs
  inputsExtraJson: '', // 字符串形式，会被解析为inputsExtra
  
  // 服务治理
  secret: '',
  swimlane: '',
  cell: '',
  liteSet: '',
  appkey: '',
  ipPorts: ''
});

// 依赖关系数据
const dependenciesData = computed(() => {
  if (!props.task?.dependencyTaskMap) {
    return [];
  }
  return Object.values(props.task.dependencyTaskMap).map(task => ({
    alias: task.alias,
    taskType: task.taskType,
    level: task.level
  }));
});

// 监听任务变化，初始化表单
watch(() => props.task, (newTask) => {
  if (newTask) {
    // 基本信息
    taskForm.alias = newTask.alias || '';
    taskForm.taskType = newTask.taskType || 'ThriftGeneric';
    taskForm.description = newTask.description || '';
    
    // 服务调用信息
    taskForm.url = newTask.url || '';
    taskForm.method = newTask.method || '';
    taskForm.remoteAppkey = newTask.remoteAppkey || '';
    taskForm.remoteServerPort = newTask.remoteServerPort || '';
    
    // 执行控制
    taskForm.timeout = newTask.timeout || 1000;
    taskForm.ignoreException = newTask.ignoreException || false;
    taskForm.switch = newTask.switch || '';
    taskForm.switchExpression = newTask.switchExpression || '';
    
    // 处理缓存选项
    if (newTask.cacheOption) {
      enableCache.value = true;
      taskForm.cacheOption.expireAfterWriteSeconds = newTask.cacheOption.expireAfterWriteSeconds;
      taskForm.cacheOption.refreshAfterWriteSeconds = newTask.cacheOption.refreshAfterWriteSeconds;
      taskForm.cacheOption.maximumSize = newTask.cacheOption.maximumSize;
    } else {
      enableCache.value = false;
    }
    
    // 处理inputs和inputsExtra的JSON格式化
    taskForm.inputsJson = showFormat(newTask.inputs);
    
    taskForm.inputsExtraJson = showFormat(newTask.inputsExtra);
    
    // 服务治理
    taskForm.secret = newTask.secret || '';
    taskForm.swimlane = newTask.swimlane || '';
    taskForm.cell = newTask.cell || '';
    taskForm.liteSet = newTask.liteSet || '';
    taskForm.appkey = newTask.appkey || '';
    taskForm.ipPorts = newTask.ipPorts || '';
  }
}, { immediate: true, deep: true });

// 复制到剪贴板
const handleCopy = async (text: string) => {
  if (!text) return;
  await copyTextToClipboard(text, '已复制到剪贴板');
};

// 关闭面板
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 点击编辑按钮
const handleEdit = () => {
  if (props.onEdit && props.task) {
    props.onEdit(props.task);
  }
};

// 点击执行按钮
const handleExecute = () => {
  if (props.onExecute && props.task) {
    props.onExecute(props.task);
  }
};

// 取消编辑
const handleCancel = () => {
  // 重置表单为当前任务数据
  if (props.task) {
    taskForm.alias = props.task.alias || '';
    taskForm.taskType = props.task.taskType || 'ThriftGeneric';
    taskForm.description = props.task.description || '';
    // ... 重置其他字段
  }
  handleClose();
};

// 确认
const handleConfirm = async () => {
  try {
    // 构建任务对象基础结构
    const task: Partial<Task> = {
      alias: taskForm.alias,
      taskType: taskForm.taskType
    };
    
    // 只添加非空字段
    if (taskForm.description) task.description = taskForm.description;
    if (taskForm.url) task.url = taskForm.url;
    if (taskForm.method) task.method = taskForm.method;
    if (taskForm.remoteAppkey) task.remoteAppkey = taskForm.remoteAppkey;
    if (taskForm.remoteServerPort) task.remoteServerPort = taskForm.remoteServerPort;
    if (taskForm.timeout && taskForm.timeout != 1000) task.timeout = taskForm.timeout;
    
    // 布尔类型特殊处理
    task.ignoreException = taskForm.ignoreException;
    
    if (taskForm.switchExpression) {
      task.switch = taskForm.switchExpression;
      task.switchExpression = taskForm.switchExpression;
    }
    
    // 缓存选项
    if (enableCache.value) {
      task.cacheOption = taskForm.cacheOption;
    }
    
    // 输入参数
    task.inputs = writeFormat(taskForm.inputsJson, 8);
    
    // 类型转换后的输入参数额外信息
    if (taskForm.inputsExtraJson && taskForm.inputsExtraJson.trim() !== '') {
      try {
        // 确保是有效的JSON对象
        const inputsExtraObj = JSON.parse(taskForm.inputsExtraJson);
        if (typeof inputsExtraObj === 'object' && inputsExtraObj !== null) {
          task.inputsExtra = inputsExtraObj;
        }
      } catch (err) {
        console.error('解析inputsExtra失败:', err);
        ElMessage.error('参数类型信息JSON格式错误');
        return;
      }
    }
    
    // 服务治理相关字段
    if (taskForm.secret) task.secret = taskForm.secret;
    if (taskForm.swimlane) task.swimlane = taskForm.swimlane;
    if (taskForm.cell) task.cell = taskForm.cell;
    if (taskForm.liteSet) task.liteSet = taskForm.liteSet;
    if (taskForm.appkey) task.appkey = taskForm.appkey;
    if (taskForm.ipPorts) task.ipPorts = taskForm.ipPorts;
    
    // 向上发送确认事件
    emit('confirm', task as Task);
    
    // 关闭面板
    emit('update:visible', false);
  } catch (error) {
    console.error('任务确认失败:', error);
    ElMessage.error('输入参数或类型信息的JSON格式不正确');
  }
};

// 删除节点
const handleDeleteNode = () => {
  // 发射删除节点事件
  emit('delete-node', props.task?.alias);
  // 关闭当前面板
  handleClose();
};
</script>

<script lang="ts">
export default {
  name: 'TaskDetailPanel'
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer) {
  .el-drawer__header {
    margin: 0 !important;
    padding: 2px 6px !important;
    min-height: 28px !important;
    height: auto !important;
    box-sizing: border-box !important;
    border-bottom: 1px solid #e4e7ed !important;
    display: flex !important;
    align-items: center !important;
  }
  
  .el-drawer__title {
    font-size: 16px !important;
    line-height: 1 !important;
    color: #333 !important;
    margin: 0 !important;
    font-weight: 500 !important;
  }
  
  .el-drawer__close-btn {
    padding: 2px !important;
    margin: 0 !important;
  }
  
  .el-drawer__body {
    padding: 0 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }
}

// 整体内容区域
.content-section {
  padding: 0 0 50px 0; // 调整底部内边距，为footer留出足够空间
  margin: 0;
}

// 节点标题样式
.section-title {
  margin: 10px 0 0 0; // 减小上边距
  padding: 3px 8px; // 减小内边距
  font-size: 12px; // 减小字体
  font-weight: bold;
  color: #409eff;
  background-color: #f0f6ff;
  border-left: 2px solid #409eff; // 减小左侧边框
  display: flex;
  align-items: center;
  
  &:before {
    display: none;
  }
  
  &:first-child {
    margin-top: 0;
  }
}

// 表格样式表单
.info-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  font-size: 12px;
  
  .info-row {
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:nth-child(odd) {
      background-color: #fafafa;
    }
    
    .info-label {
      width: 70px; // 从80px减小到70px
      padding: 4px 6px;
      color: #606266;
      background-color: #f5f7fa;
      font-weight: 500;
      text-align: right;
      vertical-align: middle;
      font-size: 11px;
    }
    
    .info-value {
      padding: 4px 6px; // 减小内边距
      color: #303133;
      vertical-align: middle;
      line-height: 1.3;
      word-break: break-all;
      font-size: 11px; // 减小字体
      
      .el-input__wrapper, 
      .el-textarea__wrapper, 
      .el-select {
        box-shadow: none;
      }
      
      .el-input__wrapper {
        padding: 0 6px;
      }
      
      .el-input-number {
        width: 100%;
        .el-input-number__decrease,
        .el-input-number__increase {
          width: 20px; // 减小增减按钮宽度
          height: 22px; // 减小增减按钮高度
        }
      }
      
      // 设置输入控件高度
      .el-input__wrapper,
      .el-input__inner {
        height: 22px !important; // 减小输入框高度
        line-height: 22px !important;
      }
      
      .el-textarea__inner {
        min-height: auto !important;
        line-height: 1.2 !important;
      }
      
      .el-select {
        width: 100%;
        .el-input {
          height: 22px !important;
        }
      }
      
      .el-switch {
        height: 16px !important; // 减小开关高度
        .el-switch__core {
          height: 16px !important;
        }
      }
      
      .el-checkbox {
        height: 16px !important; // 减小复选框高度
        .el-checkbox__label {
          font-size: 11px; // 减小复选框文字大小
        }
      }
    }
    
    .info-half-row {
      display: flex;
      .info-label {
        width: 60px; // 减小标签宽度
      }
    }
  }
}

// 带复制按钮的内容样式
.copy-content {
  display: flex;
  align-items: center;
  
  .content-text {
    flex: 1;
    word-break: break-word;
    font-size: 11px; // 减小字体
  }
  
  .copy-button {
    flex-shrink: 0;
    margin-left: 2px; // 减小边距
    font-size: 12px; // 减小图标
    padding: 1px; // 减小内边距
    
    .el-icon {
      font-size: 12px; // 减小图标
    }
  }
}

// 底部按钮区域
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 6px 10px;
  background: #fff;
  border-top: 1px solid #ebeef5;
  text-align: right;
  z-index: 999;
  width: 100%;
  box-sizing: border-box;
  height: 42px;
  
  :deep(.el-button) {
    padding: 5px 12px;
    font-size: 12px;
    margin-left: 8px;
    height: 28px;
    line-height: 18px;
  }
}

// 代码容器
.code-container {
  background-color: #f5f7fa;
  border-radius: 2px;
  padding: 4px; // 减小内边距
  max-height: 150px; // 减小最大高度
  overflow: auto;
  margin: 2px 0; // 减小外边距
  font-size: 11px; // 减小字体
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  code {
    font-family: Consolas, Monaco, monospace;
    font-size: 11px; // 减小字体
    color: #333;
  }
}

// 代码编辑器输入
.code-input {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  
  :deep(textarea) {
    line-height: 1.3;
    font-size: 11px;
    padding: 2px 4px;
  }
}

// 辅助文本样式
.subtitle {
  font-size: 11px; // 减小字体
  font-weight: 500;
  margin: 3px 0; // 减小外边距
  color: #606266;
}

// 依赖表格样式
.dependency-table {
  margin: 3px 0; // 减小外边距
  border: 1px solid #ebeef5;
  width: 100%;
  font-size: 11px; // 减小字体
  
  .table-header {
    background-color: #f5f7fa;
    font-weight: bold;
    border-bottom: 1px solid #ebeef5;
    
    .header-cell {
      padding: 3px 6px; // 减小内边距
      text-align: left;
    }
  }
  
  .table-row {
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .table-cell {
      padding: 2px 6px; // 减小内边距
      border-right: 1px solid #ebeef5;
      
      &:last-child {
        border-right: none;
      }
    }
  }
}

// Alert组件样式
:deep(.el-alert) {
  padding: 2px 4px !important;
  margin: 3px 0;
  
  .el-alert__content {
    padding: 0 2px !important;
    font-size: 11px;
  }
  
  .el-alert__icon {
    font-size: 11px;
    padding: 0 2px;
  }
}

// 表单样式重置
:deep(.el-form) {
  margin: 0;
  padding: 0;
  
  .el-form-item {
    margin: 2px 0 !important;
  }
}

// 缩小内部边距
:deep(.el-textarea__inner) {
  padding: 2px 4px;
}

.mb-2 {
  margin-bottom: 3px; // 减小外边距
}

.mt-2 {
  margin-top: 3px; // 减小外边距
}

.ml-2 {
  margin-left: 3px; // 减小外边距
}

.mr-2 {
  margin-right: 3px; // 减小外边距
}

.cache-info {
  display: flex;
  flex-wrap: wrap;
  gap: 3px; // 减小间距
  
  :deep(.el-tag) {
    height: 20px; // 减小标签高度
    padding: 0 4px; // 减小内边距
    font-size: 10px; // 减小字体
  }
}

.cache-option {
  display: flex;
  flex-direction: column;
  gap: 5px; // 增加行间距
  width: 100%;
  
  .cache-option-row {
    display: flex;
    align-items: center;
    width: 100%;
    
    .cache-option-label {
      width: 90px; // 固定标签宽度
      font-size: 11px;
      font-weight: 500;
      margin: 0;
      color: #606266;
      text-align: right;
      padding-right: 6px;
    }
    
    .compact-number {
      flex: 1; // 让输入框填充剩余空间
      max-width: 120px; // 限制最大宽度
    }
  }
}

.compact-form {
  display: flex;
  flex-wrap: wrap;
  gap: 3px; // 减小间距
}

.condition-label {
  width: 100%;
  font-size: 11px; // 减小字体
  font-weight: 500;
  margin: 0;
  color: #606266;
}

.param-label {
  width: 100%;
  font-size: 11px; // 减小字体
  font-weight: 500;
  margin: 0;
  color: #606266;
}

/* 额外添加样式 */
.mt-1 {
  margin-top: 2px;
}

.condition-input {
  width: 100%;
}

/* 缩小代码输入区域 */
:deep(.el-textarea__inner) {
  font-size: 11px !important;
  line-height: 1.2 !important;
  padding: 2px 4px !important;
}

/* 统一所有输入控件大小 */
:deep(.el-input__inner),
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  font-size: 11px !important;
  height: 22px !important;
  line-height: 22px !important;
}

/* 调整下拉选择的样式 */
:deep(.el-select) {
  .el-input__wrapper {
    padding: 0 6px !important;
    height: 22px !important;
  }
  
  .el-input__inner {
    height: 22px !important;
    line-height: 22px !important;
  }
  
  .el-input__suffix {
    height: 22px !important;
    line-height: 22px !important;
  }
}

/* 减小表单元素高度 */
:deep(.el-form-item) {
  margin-bottom: 2px !important;
}

/* 缩小alert样式 */
:deep(.el-alert--info) {
  font-size: 10px;
  padding: 2px 4px;
}

// 新增紧凑下拉框样式
.compact-select {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    padding: 0 5px !important;
    height: 22px !important;
  }
  
  :deep(.el-input__inner) {
    height: 22px !important;
    line-height: 22px !important;
    font-size: 11px !important;
  }
  
  :deep(.el-input__suffix) {
    height: 22px !important;
    line-height: 22px !important;
    
    .el-select__caret {
      line-height: 22px !important;
      height: 22px !important;
      font-size: 12px !important;
    }
  }
}

/* 添加全宽选择框样式 */
.full-width-select {
  width: 100%;
}

// 新增操作区样式
.actions-section {
  padding: 10px 0;
  margin-top: 20px;
}

.danger-zone {
  margin-top: 10px;
  padding: 15px;
  border-radius: 4px;
  background-color: rgba(245, 108, 108, 0.1);
  border: 1px dashed #f56c6c;
}

.section-title.danger {
  color: #f56c6c;
}

.danger-actions {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
