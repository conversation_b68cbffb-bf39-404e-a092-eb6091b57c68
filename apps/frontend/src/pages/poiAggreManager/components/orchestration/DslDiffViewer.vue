<template>
  <div class="dsl-diff-viewer">
    <div class="viewer-header">
      <h4>DSL变更对比</h4>
    </div>
    
    <div ref="monacoContainer" class="monaco-container"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import * as monaco from 'monaco-editor';

// 定义属性
const props = defineProps({
  originalDsl: {
    type: String,
    required: true
  },
  modifiedDsl: {
    type: String,
    required: true
  }
});

// DOM引用
const monacoContainer = ref<HTMLElement | null>(null);

// 编辑器实例
let diffEditor: monaco.editor.IStandaloneDiffEditor | null = null;

// 创建或更新diff编辑器
const createOrUpdateDiffEditor = () => {
  if (!monacoContainer.value) return;
  
  // 若编辑器已存在则销毁
  if (diffEditor) {
    diffEditor.dispose();
  }
  
  // 处理DSL文本
  let formattedOriginal = props.originalDsl || '';
  let formattedModified = props.modifiedDsl || '';
  
  // 预先检查是否完全相同
  const isDifferent = formattedOriginal !== formattedModified;
  console.log('预检查差异结果:', isDifferent ? '存在差异' : '完全相同');
  
  if (!isDifferent) {
    // 尝试更细粒度地检查差异
    const originalLines = formattedOriginal.split('\n');
    const modifiedLines = formattedModified.split('\n');
    
    // 逐行检查差异
    for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
      const origLine = originalLines[i] || '';
      const modLine = modifiedLines[i] || '';
      if (origLine !== modLine) {
        console.log(`第${i+1}行存在差异:`, {
          原始: origLine,
          修改: modLine
        });
      }
    }
  }
  
  // 创建编辑器模型
  const originalModel = monaco.editor.createModel(formattedOriginal, 'json');
  const modifiedModel = monaco.editor.createModel(formattedModified, 'json');
  
  // 创建diff编辑器
  diffEditor = monaco.editor.createDiffEditor(monacoContainer.value, {
    readOnly: true,
    automaticLayout: true,
    lineNumbers: 'on',
    scrollBeyondLastLine: false,
    minimap: { enabled: false },
    renderSideBySide: true,
    originalEditable: false,
    wordWrap: 'on',
    theme: 'vs',
    contextmenu: false,
    folding: true,
    fontFamily: 'Menlo, Monaco, "Courier New", monospace',
    fontSize: 14,
    lineHeight: 20,
    scrollbar: {
      useShadows: false,
      verticalScrollbarSize: 10,
      horizontalScrollbarSize: 10,
      alwaysConsumeMouseWheel: false
    },
    diffWordWrap: 'on',
    ignoreTrimWhitespace: false, // 不忽略空白字符，提高差异检测精度
    renderIndicators: true,      // 在变更的行上显示指示器
    renderOverviewRuler: true,   // 显示概览标尺以突显差异
    maxComputationTime: 10000,    // 增加计算时间上限至10秒
    maxFileSize: 1024 * 1024 * 5 // 支持最大5MB的文件
  });
  
  // 设置模型
  diffEditor.setModel({
    original: originalModel,
    modified: modifiedModel
  });
  
  // 添加特定样式
  const container = monacoContainer.value;
  if (container) {
    const elements = container.querySelectorAll('.monaco-editor');
    elements.forEach(el => {
      (el as HTMLElement).style.border = '1px solid #dcdfe6';
      (el as HTMLElement).style.borderRadius = '4px';
    });
  }
  
  // 优化大文件的差异计算
  setTimeout(() => {
    if (diffEditor) {
      // 重新计算差异
      (diffEditor as any).updateOptions({
        enableSplitViewResizing: true,
        renderSideBySide: true
      });
      
      // 刷新布局
      diffEditor.layout();
      
      // 获取差异
      const changes = diffEditor.getLineChanges();
      if (changes && changes.length > 0) {
        const firstChange = changes[0];
        diffEditor.revealLineInCenter(firstChange.modifiedStartLineNumber);
        console.log(`找到${changes.length}处差异，第一处在第${firstChange.modifiedStartLineNumber}行`);
        
        // 手动添加字符级差异检测
        manualCharacterDiffHighlight(changes);
      } else {
        console.log('编辑器API未检测到差异，尝试使用额外方法...');
        // 如果编辑器没有检测到差异，但预检查发现差异，则手动标记
        if (isDifferent) {
          forceHighlightDifferences();
        }
      }
    }
  }, 1000);
};

// 强制标记差异
const forceHighlightDifferences = () => {
  if (!diffEditor) return;
  
  // 获取编辑器实例
  const originalEditor = diffEditor.getOriginalEditor();
  const modifiedEditor = diffEditor.getModifiedEditor();
  
  // 获取文本内容
  const originalText = originalEditor.getModel()?.getValue() || '';
  const modifiedText = modifiedEditor.getModel()?.getValue() || '';
  
  // 文本比较，查找差异位置
  const originalLines = originalText.split('\n');
  const modifiedLines = modifiedText.split('\n');
  
  let originalDecorations: monaco.editor.IModelDeltaDecoration[] = [];
  let modifiedDecorations: monaco.editor.IModelDeltaDecoration[] = [];
  
  // 逐行对比找出差异
  for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
    const origLine = originalLines[i] || '';
    const modLine = modifiedLines[i] || '';
    
    if (origLine !== modLine) {
      // 标记不同的行
      if (i < originalLines.length) {
        originalDecorations.push({
          range: new monaco.Range(i + 1, 1, i + 1, origLine.length + 1),
          options: {
            isWholeLine: true,
            className: 'custom-diff-line-delete',
            overviewRuler: {
              color: '#ff0000',
              position: monaco.editor.OverviewRulerLane.Right
            }
          }
        });
      }
      
      if (i < modifiedLines.length) {
        modifiedDecorations.push({
          range: new monaco.Range(i + 1, 1, i + 1, modLine.length + 1),
          options: {
            isWholeLine: true,
            className: 'custom-diff-line-insert',
            overviewRuler: {
              color: '#00ff00',
              position: monaco.editor.OverviewRulerLane.Right
            }
          }
        });
      }
      
      // 字符级别差异标记
      if (i < originalLines.length && i < modifiedLines.length) {
        // 逐字符比较，标记字符级别差异
        for (let j = 0; j < Math.min(origLine.length, modLine.length); j++) {
          if (origLine[j] !== modLine[j]) {
            // 从不同的字符开始到行尾都标记
            originalDecorations.push({
              range: new monaco.Range(i + 1, j + 1, i + 1, j + 2),
              options: {
                inlineClassName: 'char-diff-delete'
              }
            });
            
            modifiedDecorations.push({
              range: new monaco.Range(i + 1, j + 1, i + 1, j + 2),
              options: {
                inlineClassName: 'char-diff-insert'
              }
            });
          }
        }
      }
    }
  }
  
  // 应用装饰器
  if (originalDecorations.length > 0) {
    originalEditor.deltaDecorations([], originalDecorations);
  }
  
  if (modifiedDecorations.length > 0) {
    modifiedEditor.deltaDecorations([], modifiedDecorations);
  }
  
  console.log(`强制标记了 ${originalDecorations.length + modifiedDecorations.length} 处差异`);
  
  // 如果有差异，滚动到第一处差异
  if (originalDecorations.length > 0 || modifiedDecorations.length > 0) {
    const firstLineWithDiff = Math.min(
      originalDecorations.length > 0 ? originalDecorations[0].range.startLineNumber : Number.MAX_SAFE_INTEGER,
      modifiedDecorations.length > 0 ? modifiedDecorations[0].range.startLineNumber : Number.MAX_SAFE_INTEGER
    );
    
    if (firstLineWithDiff < Number.MAX_SAFE_INTEGER) {
      diffEditor.revealLineInCenter(firstLineWithDiff);
    }
  }
};

// 手动添加字符级差异高亮
const manualCharacterDiffHighlight = (lineChanges: monaco.editor.ILineChange[]) => {
  if (!diffEditor) return;
  
  const originalEditor = diffEditor.getOriginalEditor();
  const modifiedEditor = diffEditor.getModifiedEditor();
  
  const originalText = originalEditor.getModel()?.getValue() || '';
  const modifiedText = modifiedEditor.getModel()?.getValue() || '';
  
  const originalLines = originalText.split('\n');
  const modifiedLines = modifiedText.split('\n');
  
  let originalDecorations: monaco.editor.IModelDeltaDecoration[] = [];
  let modifiedDecorations: monaco.editor.IModelDeltaDecoration[] = [];
  
  // 对每个行变更进行处理
  lineChanges.forEach(change => {
    // 对于行变动，添加字符级别的比较
    if (change.originalEndLineNumber > 0 && change.modifiedEndLineNumber > 0) {
      for (let i = change.originalStartLineNumber; i <= change.originalEndLineNumber; i++) {
        const origLineIdx = i - 1;
        if (origLineIdx < originalLines.length) {
          const origLine = originalLines[origLineIdx];
          
          // 对应的修改行
          const modLineIdx = change.modifiedStartLineNumber + (i - change.originalStartLineNumber) - 1;
          if (modLineIdx >= 0 && modLineIdx < modifiedLines.length) {
            const modLine = modifiedLines[modLineIdx];
            
            // 寻找字符差异
            for (let j = 0; j < Math.min(origLine.length, modLine.length); j++) {
              if (origLine[j] !== modLine[j]) {
                // 标记原始版本中的字符
                originalDecorations.push({
                  range: new monaco.Range(i, j + 1, i, j + 2),
                  options: {
                    inlineClassName: 'char-diff-delete'
                  }
                });
                
                // 标记修改版本中的字符
                modifiedDecorations.push({
                  range: new monaco.Range(modLineIdx + 1, j + 1, modLineIdx + 1, j + 2),
                  options: {
                    inlineClassName: 'char-diff-insert'
                  }
                });
              }
            }
          }
        }
      }
    }
  });
  
  // 应用装饰器
  if (originalDecorations.length > 0) {
    originalEditor.deltaDecorations([], originalDecorations);
  }
  
  if (modifiedDecorations.length > 0) {
    modifiedEditor.deltaDecorations([], modifiedDecorations);
  }
  
  console.log(`添加了 ${originalDecorations.length + modifiedDecorations.length} 处字符级差异标记`);
};

// 增强差异检测的辅助函数
const highlightDifferences = () => {
  if (!diffEditor) return;
  
  // 获取当前的差异信息
  const diff = diffEditor.getLineChanges();
  if (!diff || diff.length === 0) {
    console.log('未检测到差异或差异检测尚未完成');
    
    // 尝试使用强制标记方法
    if (props.originalDsl !== props.modifiedDsl) {
      console.log('使用强制标记方法查找差异');
      forceHighlightDifferences();
    }
    return;
  }
  
  // 对大文件特殊处理
  if (props.originalDsl.length > 5000 || props.modifiedDsl.length > 5000) {
    console.log('处理大文件差异:', diff.length);
    
    // 尝试通过编辑器API直接设置装饰器来高亮差异
    if (diffEditor.getOriginalEditor && diffEditor.getModifiedEditor) {
      const originalEditor = diffEditor.getOriginalEditor();
      const modifiedEditor = diffEditor.getModifiedEditor();
      
      // 添加自定义装饰器以增强差异显示
      diff.forEach(change => {
        if (change.originalEndLineNumber > 0) {
          originalEditor.deltaDecorations([], [{
            range: new monaco.Range(
              change.originalStartLineNumber,
              1,
              change.originalEndLineNumber,
              1
            ),
            options: {
              isWholeLine: true,
              className: 'custom-diff-line-delete',
              overviewRuler: {
                color: '#ff0000',
                position: monaco.editor.OverviewRulerLane.Right
              }
            }
          }]);
        }
        
        if (change.modifiedEndLineNumber > 0) {
          modifiedEditor.deltaDecorations([], [{
            range: new monaco.Range(
              change.modifiedStartLineNumber,
              1,
              change.modifiedEndLineNumber,
              1
            ),
            options: {
              isWholeLine: true,
              className: 'custom-diff-line-insert',
              overviewRuler: {
                color: '#00ff00',
                position: monaco.editor.OverviewRulerLane.Right
              }
            }
          }]);
        }
      });
    }
  } else {
    console.log('检测到差异:', diff.length);
  }
};

// 监听属性变化
watch([() => props.originalDsl, () => props.modifiedDsl], () => {
  if (diffEditor) {
    createOrUpdateDiffEditor();
    // 等待差异计算完成后执行高亮（对大文件增加等待时间）
    const delayTime = (props.originalDsl?.length || 0) > 10000 ? 2000 : 500;
    setTimeout(highlightDifferences, delayTime);
  }
});

// 组件挂载时
onMounted(() => {
  // 初始化编辑器
  console.log('DslDiffViewer组件已挂载');
  console.log('原始DSL长度:', props.originalDsl?.length || 0);
  console.log('修改后DSL长度:', props.modifiedDsl?.length || 0);
  
  // 创建编辑器
  if (monacoContainer.value) {
    createOrUpdateDiffEditor();
    // 等待差异计算完成后执行高亮（对大文件增加等待时间）
    const delayTime = (props.originalDsl?.length || 0) > 10000 ? 2000 : 500;
    setTimeout(highlightDifferences, delayTime);
  }
});

// 组件卸载前清理
onBeforeUnmount(() => {
  if (diffEditor) {
    diffEditor.dispose();
    diffEditor = null;
  }
});
</script>

<style scoped>
.dsl-diff-viewer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  margin-bottom: 10px;
}

.monaco-container {
  flex: 1;
  width: 100%;
  height: calc(100% - 50px);
  min-height: 400px;
  overflow: hidden;
}

/* 覆盖monaco-editor的一些样式 */
:deep(.monaco-editor .margin) {
  background-color: #f5f7fa;
}

:deep(.monaco-editor .lines-content) {
  background-color: #fff;
}

/* 增强差异显示的样式 */
:deep(.monaco-editor .line-insert),
:deep(.monaco-editor .char-insert) {
  background-color: rgba(155, 255, 155, 0.3) !important;
}

:deep(.monaco-editor .line-delete),
:deep(.monaco-editor .char-delete) {
  background-color: rgba(255, 155, 155, 0.3) !important;
}

/* 自定义差异装饰器样式 */
:deep(.custom-diff-line-insert) {
  background-color: rgba(0, 255, 0, 0.1) !important;
  border-left: 3px solid #2cbb40 !important;
}

:deep(.custom-diff-line-delete) {
  background-color: rgba(255, 0, 0, 0.1) !important;
  border-left: 3px solid #f56c6c !important;
}

/* 小地图样式优化 */
:deep(.monaco-editor .minimap) {
  opacity: 0.8;
}

/* 差异概览标尺样式 */
:deep(.monaco-editor .decorationsOverviewRuler) {
  opacity: 1;
}

/* 字符级别差异样式 */
:deep(.char-diff-delete) {
  background-color: #ffd3d3 !important;
  color: #9a0000 !important;
  border-radius: 2px;
  font-weight: bold;
}

:deep(.char-diff-insert) {
  background-color: #d3ffd3 !important;
  color: #007500 !important;
  border-radius: 2px;
  font-weight: bold;
}
</style>

<script lang="ts">
export default {
  name: 'DslDiffViewer'
};
</script> 