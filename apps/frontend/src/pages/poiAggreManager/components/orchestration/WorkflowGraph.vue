<template>
  <div class="workflow-graph-container">
    <!-- 使用抽取出来的工作流信息面板组件 -->
    <workflow-info-overlay
      :workflow="workflow"
      :isEditing="workflowInfoEditing"
      :editable="props.isEditing"
      @update:isEditing="workflowInfoEditing = $event"
      @update="handleWorkflowInfoUpdate"
    />
    
    <div class="graph-toolbar">
      <div class="toolbar-left">
        <el-tag>{{ workflow?.name || '未命名工作流' }}</el-tag>
        <span class="graph-info">
          共 {{ taskNodeCount }} 个任务
        </span>
      </div>
      <div class="toolbar-right">
        <el-button-group>
          <el-button size="small" @click="zoomIn" :disabled="!canZoomIn" title="放大">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button size="small" @click="zoomOut" :disabled="!canZoomOut" title="缩小">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button size="small" @click="fitView" title="适应画布">
            <el-icon><Aim /></el-icon>
          </el-button>
          <el-button size="small" @click="reapplyLayout" title="重新布局" :disabled="isLayouting">
            <el-icon><RefreshRight /></el-icon>
          </el-button>
          <el-button 
            size="small" 
            :type="props.showDslEditor ? 'primary' : ''" 
            @click="toggleDslEditor" 
            title="显示/隐藏DSL编辑器"
          >
            <el-icon><Document /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <!-- 画板容器：可视区域，固定大小 -->
    <div class="graph-container"
         @mousedown="startDrag"
         @mousemove="onDrag"
         @mouseup="endDrag"
         @mouseleave="endDrag">
      <div v-if="!nodes.length" class="empty-graph">
        <el-empty description="暂无可视化数据">
          <template #description>
          </template>
        </el-empty>
      </div>
      
      <!-- 画布：可滚动区域，大于可视区域 -->
      <div v-else class="canvas-area" 
           :style="{ 
             transform: `translate(${position.x}px, ${position.y}px)`,
           }">
        
        <!-- 流程图：所有节点和连线的容器，随内容大小调整 -->
        <div class="workflow-diagram" :style="{transform: `scale(${zoom})`, transformOrigin: '0 0'}">
          <!-- 重要：设置整个图的相对容器，使节点和连线共享同一坐标系 -->
          <div class="diagram-content" :style="{
            position: 'absolute',
            left: `${graphSize.minX}px`,
            top: `${graphSize.minY}px`,
            width: `${graphSize.width}px`,
            height: `${graphSize.height}px`
          }">
            <!-- 连线层 -->
            <svg class="edges-layer" width="100%" height="100%">
              <g>
                <path 
                  v-for="edge in computedEdges" 
                  :key="edge.id"
                  :d="getEdgePath(edge)"
                  :class="['edge-path', `edge-${edge.type}`, isSpecialEdge(edge) ? 'edge-special' : '']"
                  :stroke="getEdgeStrokeColor(edge)"
                  :stroke-width="2"
                  :stroke-dasharray="isSpecialEdge(edge) ? '5,5' : 'none'"
                  fill="none"
                  :marker-end="getEdgeMarker(edge)"
                />
              </g>
              <!-- 箭头标记定义 -->
              <defs>
                <marker
                  id="arrowhead"
                  viewBox="0 0 10 10"
                  refX="9"
                  refY="5"
                  markerUnits="strokeWidth"
                  markerWidth="8"
                  markerHeight="8"
                  orient="auto"
                >
                  <path d="M 0 0 L 10 5 L 0 10 z" fill="#B8BDC6" />
                </marker>
                <marker
                  id="arrowhead-special"
                  viewBox="0 0 10 10"
                  refX="9"
                  refY="5"
                  markerUnits="strokeWidth"
                  markerWidth="8"
                  markerHeight="8"
                  orient="auto"
                >
                  <path d="M 0 0 L 10 5 L 0 10 z" fill="#8395A7" />
                </marker>
              </defs>
            </svg>
            
            <!-- 开始节点 - 修改为更大更明显 -->
            <div 
              class="simple-node node-type-start special-node"
              :class="{ 'suppress-transition': isAnyNodeDragging }"
              @click="handleSpecialNodeClick('start')"
              :style="{
                left: `${startNode?.x ? startNode.x - graphSize.minX : 0}px`,
                top: `${startNode?.y ? startNode.y - graphSize.minY : 0}px`,
                width: '280px',
                height: '120px',
                transform: 'translate(-40px, -20px)'
              }"
            >
              <div class="node-header">
                <el-icon class="special-icon"><Star /></el-icon>
                <span class="node-title">开始</span>
                <span class="node-type-tag tag-special">入参</span>
              </div>
              <div class="node-body">
                <div class="node-type">输入参数</div>
                <div v-if="hasInputParams" class="node-desc params-list">
                  {{ formatParamKeys(inputParams) }}
                </div>
                <div v-else class="node-desc">点击配置输入参数</div>
              </div>
            </div>
            
            <!-- 任务节点 -->
            <div
              v-for="node in taskNodes"
              :key="node.id"
              class="simple-node"
              :class="[
                `node-type-${node.taskType}`, 
                node.status,
                node.taskType === 'Calculate' ? 'node-type-calculate' : '',
                currentDragNode && currentDragNode.id === node.id ? 'special-dragging' : '',
                isAnyNodeDragging && (!currentDragNode || currentDragNode.id !== node.id) ? 'suppress-transition' : ''
              ]"
              @click="handleNodeClick(node)"
              :style="{
                left: `${node?.x ? node.x - graphSize.minX : 0}px`,
                top: `${node?.y ? node.y - graphSize.minY : 0}px`,
                width: '200px',
                height: '100px',
                marginBottom: '10px'
              }"
              @mousedown.stop="startNodeDrag($event, node)"
            >
              <div class="node-header">
                <span class="node-title">{{ node.alias }}</span>
                <span class="node-type-tag" :class="`tag-${node.taskType}`">{{ node.taskType }}</span>
              </div>
              <div class="node-body">
                <div v-if="node.description" class="node-desc">{{ node.description }}</div>
                <div v-else class="node-desc empty-desc">无描述</div>
                <!-- 添加执行状态指示器 -->
                <div 
                  v-if="node.status && node.status !== 'idle'"
                  class="execution-indicator"
                  :class="`status-${node.status}`"
                  @click.stop="handleExecutionIndicatorClick(node)"
                >
                  <el-icon v-if="node.status === 'running'"><Loading /></el-icon>
                  <el-icon v-else-if="node.status === 'success'"><Check /></el-icon>
                  <el-icon v-else-if="node.status === 'failed'"><Close /></el-icon>
                </div>
              </div>
            </div>
            
            <!-- 结束节点 - 修改为更大更明显 -->
            <div 
              class="simple-node node-type-end special-node"
              :class="{ 'suppress-transition': isAnyNodeDragging }"
              @click="handleSpecialNodeClick('end')"
              :style="{
                left: `${endNode?.x ? endNode.x - graphSize.minX : 0}px`,
                top: `${endNode?.y ? endNode.y - graphSize.minY : 0}px`,
                width: '280px',
                height: '120px',
                transform: 'translate(-40px, -20px)'
              }"
            >
              <div class="node-header">
                <el-icon class="special-icon"><Finished /></el-icon>
                <span class="node-title">结束</span>
                <span class="node-type-tag tag-special">结果</span>
              </div>
              <div class="node-body">
                <div class="node-type">输出结果</div>
                <div v-if="hasOutputConfig" class="node-desc">已配置输出</div>
                <div v-else class="node-desc">点击配置输出</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { Workflow, Task, VisualNode, VisualEdge, NodeType } from '../../types/orchestration';
import { ElMessage } from 'element-plus';
import { ZoomIn, ZoomOut, Aim, Star, Finished, Connection, DataAnalysis, SetUp, Service, RefreshRight, Edit, Check, Close, Loading, Document } from '@element-plus/icons-vue';
import WorkflowInfoOverlay from './WorkflowInfoOverlay.vue';
import {
  createElkInstance,
  layoutConfigs,
  getEdgePath as utilGetEdgePath,
  isSpecialEdge as utilIsSpecialEdge,
  getEdgeStrokeColor as utilGetEdgeStrokeColor,
  getEdgeMarker as utilGetEdgeMarker,
  calculateGraphSize as utilCalculateGraphSize,
  calculateIdealView as utilCalculateIdealView,
  buildGraphData,
  updateNodesWithoutRebuild,
  shouldRerenderWorkflow,
  formatParamKeys as utilFormatParamKeys,
  adjustPositionAfterZoom as utilAdjustPositionAfterZoom
} from '../../utils/workflowGraphUtils';

const props = defineProps({
  workflow: {
    type: Object as () => Workflow | null,
    default: null
  },
  executionStatus: {
    type: Object as () => Record<string, string>,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  showDslEditor: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits([
  'node-click', 
  'start-node-click', 
  'end-node-click', 
  'workflow-info-update', 
  'execution-indicator-click',
  'toggle-dsl-editor'
]);

const nodes = ref<VisualNode[]>([]);
const edges = ref<VisualEdge[]>([]);
const nodePositions = ref<Record<string, { x: number, y: number }>>({});
const zoom = ref(1);

// 工作流信息面板编辑状态
const workflowInfoEditing = ref(false);

// 拖拽状态
const isDragging = ref(false);
const dragStartX = ref(0);
const dragStartY = ref(0);
const position = ref({ x: 0, y: 0 });
const initialPosition = ref({ x: 0, y: 0 });

// 节点拖拽状态
const isDraggingNode = ref(false);
const currentDragNode = ref<VisualNode | null>(null);
const nodeDragStartX = ref(0);
const nodeDragStartY = ref(0);
const nodeInitialPosition = ref({ x: 0, y: 0 });

// 添加一个新的状态标记当前是否有节点正在拖动
const isAnyNodeDragging = ref(false);

// 添加一个标记变量，表示是否是拖拽
const wasDragging = ref(false);

// 特殊节点
const startNode = ref<VisualNode>({
  id: 'start',
  alias: 'start',
  taskType: 'start',
  type: 'StartNode',
  nodeType: NodeType.START,
  x: 50,
  y: 100,
  text: '开始',
  status: 'idle'
});

const endNode = ref<VisualNode>({
  id: 'end',
  alias: 'end',
  taskType: 'end',
  type: 'EndNode',
  nodeType: NodeType.END,
  x: 50,
  y: 300,
  text: '结束',
  status: 'idle'
});

// 输入参数状态
const inputParams = ref<Record<string, any>>({});
const hasInputParams = computed(() => Object.keys(inputParams.value).length > 0);

// 计算属性
const taskNodes = ref<VisualNode[]>([]);
const taskNodeCount = ref(0);
const canZoomIn = computed(() => zoom.value < 2);
const canZoomOut = computed(() => zoom.value > 0.5);
const hasOutputConfig = computed(() => props.workflow?.outputs && Object.keys(props.workflow.outputs).length > 0);

// 添加布局状态追踪
const isLayouting = ref(false);

// 添加ELK布局引擎实例
const elk = ref(createElkInstance());

// 画布拖拽相关函数
const startDrag = (e: MouseEvent) => {
  if (isDraggingNode.value) return;
  
  isDragging.value = true;
  dragStartX.value = e.clientX;
  dragStartY.value = e.clientY;
  initialPosition.value = { ...position.value };
};

// 节点拖拽
const startNodeDrag = (e: MouseEvent, node: VisualNode) => {
  e.stopPropagation();
  isDraggingNode.value = true;
  isAnyNodeDragging.value = true; // 标记开始拖动
  wasDragging.value = false; // 初始化拖拽标记
  currentDragNode.value = node;
  nodeDragStartX.value = e.clientX;
  nodeDragStartY.value = e.clientY;
  nodeInitialPosition.value = { 
    x: node.x ?? 0, 
    y: node.y ?? 0 
  };
};

// 拖拽中
const onDrag = (e: MouseEvent) => {
  if (isDraggingNode.value && currentDragNode.value) {
    // 阻止事件冒泡，防止拖动节点时触发画布拖动
    e.stopPropagation();
    e.preventDefault(); // 阻止默认行为
    
    const dx = e.clientX - nodeDragStartX.value;
    const dy = e.clientY - nodeDragStartY.value;
    
    // 判断是否有真实的拖动发生（超过5像素）
    if (Math.abs(dx) > 5 || Math.abs(dy) > 5) {
      wasDragging.value = true; // 标记为拖拽状态
    }
    
    // 应用缩放比例，保证拖动感觉一致
    const scaledDx = dx / zoom.value;
    const scaledDy = dy / zoom.value;
    
    // 更新当前拖动节点的位置
    currentDragNode.value.x = nodeInitialPosition.value.x + scaledDx;
    currentDragNode.value.y = nodeInitialPosition.value.y + scaledDy;
    
    return;
  }
  
  if (isDragging.value) {
    const dx = e.clientX - dragStartX.value;
    const dy = e.clientY - dragStartY.value;
    
    position.value = {
      x: initialPosition.value.x + dx,
      y: initialPosition.value.y + dy
    };
  }
};

// 拖拽结束
const endDrag = () => {
  if (isDraggingNode.value && currentDragNode.value && currentDragNode.value.alias) {
    // 拖动结束时，更新节点位置缓存
    nodePositions.value[currentDragNode.value.alias] = {
      x: currentDragNode.value.x || 0,
      y: currentDragNode.value.y || 0
    };
  }
  
  isDraggingNode.value = false;
  isDragging.value = false;
  
  // 存储当前拖拽的节点，用于后续判断是否展开详情
  currentDragNode.value = null;
  
  // 延迟更长一点，确保DOM完全更新
  setTimeout(() => {
    isAnyNodeDragging.value = false;
    // 拖拽标记延迟重置，确保点击事件处理完毕
    setTimeout(() => {
      wasDragging.value = false;
    }, 50);
  }, 50);
};

// 缩放控制
const zoomIn = () => {
  if (canZoomIn.value) {
    zoom.value += 0.1;
    
    // 获取图表容器的宽高
    const container = document.querySelector('.graph-container');
    if (container) {
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      
      // 使用工具函数调整位置
      position.value = utilAdjustPositionAfterZoom(0.1, zoom.value, position.value, containerWidth, containerHeight);
    }
  }
};

const zoomOut = () => {
  if (canZoomOut.value) {
    zoom.value -= 0.1;
    
    // 获取图表容器的宽高
    const container = document.querySelector('.graph-container');
    if (container) {
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      
      // 使用工具函数调整位置
      position.value = utilAdjustPositionAfterZoom(-0.1, zoom.value, position.value, containerWidth, containerHeight);
    }
  }
};

// 单独的适应按钮功能
const fitView = () => {
  autoFit();
};

// 修改图形大小计算，避免在拖动节点时重新计算其他节点位置
const graphSize = ref({
  width: 2000,
  height: 2000,
  minX: -1000,
  minY: -1000,
  maxX: 1000,
  maxY: 1000,
  center: { x: 0, y: 0 }
});

// 计算最佳视图参数
const calculateIdealView = () => {
  const container = document.querySelector('.graph-container');
  if (!container || !props.workflow?.taskMap) {
    return null;
  }
  
  // 容器尺寸
  const containerWidth = container.clientWidth;
  const containerHeight = container.clientHeight;
  
  // 使用工具函数计算理想视图
  return utilCalculateIdealView(graphSize.value, containerWidth, containerHeight);
};

// 自动适应大小
const autoFit = () => {
  nextTick(() => {
    const idealView = calculateIdealView();
    if (!idealView) return;
    
    zoom.value = idealView.zoom;
    position.value = idealView.position;
  });
};

// 重新应用布局方法
const reapplyLayout = async () => {
  if (!props.workflow?.taskMap || isLayouting.value) return;
  
  isLayouting.value = true;
  ElMessage.info('正在计算布局...');
  console.log('reapplyLayout');
  
  try {
    // 清除所有已保存的节点位置，除了用户明确拖动过的节点
    nodePositions.value = {};
    
    // 重新应用布局算法
    await buildGraph();
    
    ElMessage.success('布局计算完成');
  } catch (error) {
    console.error('布局计算失败:', error);
    ElMessage.error('布局计算失败，请重试');
  } finally {
    isLayouting.value = false;
  }
};

// 获取边的路径
const getEdgePath = (edge: VisualEdge) => {
  return utilGetEdgePath(edge, nodes.value, startNode.value, endNode.value, graphSize.value);
};

// 判断是否是特殊边
const isSpecialEdge = (edge: VisualEdge) => {
  return utilIsSpecialEdge(edge);
};

// 获取边的颜色
const getEdgeStrokeColor = (edge: VisualEdge) => {
  return utilGetEdgeStrokeColor(edge, nodes.value);
};

// 获取边箭头标记
const getEdgeMarker = (edge: VisualEdge) => {
  return utilGetEdgeMarker(edge);
};

// 计算所有边（包括依赖关系）
const computedEdges = ref<VisualEdge[]>([]);

// 更新计算边的方法
const updateComputedEdges = () => {
  computedEdges.value = edges.value;
};

// 修改节点点击处理函数，增加拖拽判断
const handleNodeClick = (node: VisualNode) => {
  // 如果是在拖拽后的点击，不触发详情面板
  if (wasDragging.value) {
    return;
  }
  
  // 正常的点击处理
  if (props.workflow?.taskMap) {
    const task = props.workflow.taskMap[node.alias];
    if (task) {
      emit('node-click', task);
    }
  }
};

// 修改特殊节点点击处理，也增加拖拽判断
const handleSpecialNodeClick = (nodeType: 'start' | 'end') => {
  // 如果是在拖拽后的点击，不触发操作
  if (wasDragging.value) {
    return;
  }
  
  // 正常的点击处理
  if (nodeType === 'start') {
    emit('start-node-click', { nodeType: 'start' });
  } else if (nodeType === 'end') {
    emit('end-node-click', { nodeType: 'end' });
  }
};

// 添加执行状态指示器点击处理
const handleExecutionIndicatorClick = (node: VisualNode) => {
  // 发出执行指示器点击事件
  if (props.workflow?.taskMap) {
    const task = props.workflow.taskMap[node.alias];
    if (task) {
      emit('execution-indicator-click', task);
    }
  }
};

// 修改构建图形函数，使用工具函数构建图形
const buildGraph = async () => {
  console.log('buildGraph');
  if (!props.workflow?.taskMap) {
    clearGraph();
    return;
  }
  
  // 设置布局计算状态
  isLayouting.value = true;
  
  try {
    // 使用工具函数生成图形数据
    const { nodes: newNodes, edges: newEdges, taskNodes: newTaskNodes, startNode: newStartNode, endNode: newEndNode } = 
      await buildGraphData(props.workflow, elk.value, nodePositions.value);
    
    // 更新数据
    nodes.value = newNodes;
    edges.value = newEdges;
    
    // 更新特殊节点
    if (newStartNode) Object.assign(startNode.value, newStartNode);
    if (newEndNode) Object.assign(endNode.value, newEndNode);
    
    // 更新计算属性
    taskNodes.value = newTaskNodes || [];
    taskNodeCount.value = taskNodes.value.length;
    updateComputedEdges();
    
    // 计算图形尺寸
    graphSize.value = utilCalculateGraphSize(taskNodes.value, startNode.value, endNode.value);
  } catch (error) {
    console.error('构建图形失败:', error);
    ElMessage.error('构建图形失败');
  } finally {
    // 重置布局计算状态
    isLayouting.value = false;
    
    // 视图适配
    nextTick(() => {
      fitView();
    });
  }
};

// 清空图形
const clearGraph = () => {
  console.log('clearGraph清空图形');
  nodes.value = [];
  edges.value = [];
  taskNodes.value = [];
  taskNodeCount.value = 0;
  computedEdges.value = [];
  console.log('已清空图形');
};

// 格式化参数键列表展示
const formatParamKeys = (params: Record<string, any>): string => {
  return utilFormatParamKeys(params);
};

// 监听工作流变更 
watch(() => props.workflow, (newWorkflow, oldWorkflow) => {
  // 添加调试信息，查看新旧工作流的引用和数据
  console.log('WorkflowGraph watch props.workflow - 引用比较:', newWorkflow === oldWorkflow);
  
  // 如果新工作流不存在，直接清空图形
  if (!newWorkflow) {
    console.log('新工作流不存在，直接清空图形');
    clearGraph();
    return;
  }
  
  // 判断是否需要重新构建图表
  const needRebuild = shouldRerenderWorkflow(newWorkflow, oldWorkflow || null);
  console.log('是否需要重建图形:', needRebuild);
  
  if (needRebuild) {
    // 重新构建图表
    console.log('重新构建图表');
    buildGraph();
  } else if (oldWorkflow) {
    // 如果不需要重建，只更新节点属性
    console.log('不需要重建图表，只更新节点属性');
    updateWithoutRebuild(newWorkflow);
  }
}, {
  immediate: true,  // 修改为true，确保初始化时执行
  deep: false
});

watch(() => props.executionStatus, () => {
  // 更新节点状态
  nodes.value.forEach(node => {
    if (props.executionStatus[node.alias]) {
      node.status = props.executionStatus[node.alias] as 'idle' | 'running' | 'success' | 'failed';
    }
  });
}, { deep: true });

onMounted(() => {
  // 确保在DOM渲染完成后再计算
  nextTick(() => {
    autoFit();
  });
});

// 处理工作流信息更新
const handleWorkflowInfoUpdate = (workflowInfo: any) => {
  // 直接传递工作流信息更新
  emit('workflow-info-update', workflowInfo);
};

// 监听全局编辑状态变化
watch(() => props.isEditing, (newValue) => {
  // 当全局编辑状态关闭时，工作流信息面板也应该退出编辑状态
  if (!newValue && workflowInfoEditing.value) {
    workflowInfoEditing.value = false;
  }
});

// 添加更新状态但不重新构建图表的方法
const updateWithoutRebuild = (newWorkflow: Workflow) => {
  console.log('更新状态但不重新构建图表');
  
  // 使用工具函数更新节点但不重建图表
  updateNodesWithoutRebuild(nodes.value, newWorkflow, props.executionStatus);
  
  // 更新开始节点和结束节点相关的数据
  if (newWorkflow.inputParams) {
    inputParams.value = newWorkflow.inputParams || {};
  }
  
  // 执行轻量级的视图更新，确保视图重新渲染
  nextTick(() => {
    // 触发视图刷新
    taskNodes.value = [...nodes.value.filter(node => !node.nodeType)];
    taskNodeCount.value = taskNodes.value.length;
    
    // 强制刷新视图
    setTimeout(() => {
      // 触发额外的视图更新，修复可能的渲染问题
      const tempNodes = [...nodes.value];
      nodes.value = [];
      nextTick(() => {
        nodes.value = tempNodes;
        console.log('强制刷新视图完成');
      });
    }, 50);
  });
};

// 提供给父组件调用的方法
defineExpose({
  fitView,
  updateInputParams,
  updateWithoutRebuild,
  resetNodeStatus
});

// 更新输入参数并改变开始节点显示
function updateInputParams(params: Record<string, any>) {
  inputParams.value = params;
  // 触发重绘
  nextTick(() => {
    fitView();
  });
}

// 重置所有节点的执行状态
function resetNodeStatus() {
  // 重置所有任务节点的状态
  nodes.value.forEach(node => {
    node.status = 'idle';
  });
  
  // 重置特殊节点
  if (startNode.value) startNode.value.status = 'idle';
  if (endNode.value) endNode.value.status = 'idle';
  
  // 触发重绘
  nextTick(() => {
    // 更新视图
  });
}

// 添加显示/隐藏DSL按钮到工具栏中，实现处理函数
const toggleDslEditor = () => {
  emit('toggle-dsl-editor', !props.showDslEditor);
};
</script>

<script lang="ts">
export default {
  name: 'WorkflowGraph'
};
</script>

<style lang="scss" scoped>
.workflow-graph-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  .graph-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
    height: 36px;
    
    .graph-info {
      margin-left: 10px;
      font-size: 12px;
      color: #606266;
    }
  }
  
  /* 画板容器：可视区域 */
  .graph-container {
    position: relative;
    width: 100%;
    flex: 1;
    overflow: hidden;
    cursor: grab;
    
    &:active {
      cursor: grabbing;
    }
    
    .empty-graph {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      padding: 20px;
    }
    
    /* 画布：可滚动内容区 */
    .canvas-area {
      position: absolute;
      width: 100%;
      height: 100%;
      transition: transform 0.05s ease;
      
      /* 流程图：节点和连线的容器 */
      .workflow-diagram {
        position: relative;
        
        /* 图内容容器 */
        .diagram-content {
          position: relative;
          
          /* 连线层 */
          .edges-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
            
            .edge-path {
              transition: stroke 0.3s;
              
              &:hover {
                stroke: #409eff;
                stroke-width: 2.5;
              }
            }
          }
        }
      }
    }
  }
  
  /* 节点样式 */
  .simple-node {
    position: absolute;
    width: 200px;
    min-height: 80px;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: visible; /* 确保指示器可以完整显示 */
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
    
    &.suppress-transition {
      transition: none;
    }
    
    .node-header {
      padding: 6px 10px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 7px 7px 0 0; /* 添加内部顶部圆角，匹配外部圆角 */
      
      .node-title {
        flex: 1;
        font-weight: 500;
        font-size: 14px;
        margin-right: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .node-type-tag {
        font-size: 12px;
        padding: 3px 8px;
        border-radius: 12px;
        white-space: nowrap;
        background-color: #ebeef5;
        color: #606266;
        font-weight: 500;
        margin-left: 4px;
        
        &.tag-ThriftGeneric {
          background-color: #d9ecff;
          color: #1989fa;
        }
        
        &.tag-Squirrel {
          background-color: #faecd8;
          color: #e6a23c;
        }
        
        &.tag-Calculate {
          background-color: #ebeef5;
          color: #606266;
        }
        
        &.tag-special {
          background-color: rgba(255, 255, 255, 0.85);
          color: currentColor;
          font-weight: 500;
        }
        
        /* 开始节点和结束节点的标签样式 */
        .node-type-start & {
          &.tag-special {
            background-color: rgba(217, 236, 255, 0.6);
            color: #1989fa;
          }
        }
        
        .node-type-end & {
          &.tag-special {
            background-color: rgba(225, 243, 216, 0.6);
            color: #67c23a;
          }
        }
      }
    }
    
    .node-body {
      padding: 8px 10px;
      height: calc(100% - 32px); /* 减去header的高度，使背景填充剩余空间 */
      display: flex;
      flex-direction: column;
      position: relative; /* 为执行状态指示器添加相对定位 */
      
      .node-desc {
        font-size: 12px;
        color: #606266;
        margin-top: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
        
        &.empty-desc {
          font-style: italic;
          color: #909399;
        }
        
        &.params-list {
          font-size: 11px;
          color: #409EFF;
          background-color: rgba(64, 158, 255, 0.1);
          border-radius: 2px;
          padding: 2px 4px;
          margin-top: 5px;
          white-space: nowrap;
          max-width: 200px;
        }
      }
      
      /* 执行状态指示器样式 */
      .execution-indicator {
        position: absolute;
        right: 8px;
        bottom: 8px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 100; /* 确保指示器在节点上方 */
        transition: all 0.3s;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        border: 2px solid #fff;
        
        &:hover {
          transform: scale(1.1);
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
        }
        
        &.status-running {
          background-color: #e6a23c;
          color: white;
          animation: pulse 2s infinite;
        }
        
        &.status-success {
          background-color: #67c23a;
          color: white;
        }
        
        &.status-failed {
          background-color: #f56c6c;
          color: white;
        }
        
        .el-icon {
          font-size: 16px;
        }
      }
    }
    
    /* 节点状态样式 */
    &.running {
      border-color: #e6a23c;
      
      .node-header {
        background-color: #fdf6ec;
        color: #e6a23c;
      }
    }
    
    &.success {
      border-color: #67c23a;
      
      .node-header {
        background-color: #f0f9eb;
        color: #67c23a;
      }
    }
    
    &.failed {
      border-color: #f56c6c;
      
      .node-header {
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }
    
    /* 特殊节点样式 */
    &.node-type-start {
      border-color: #409eff;
      border-width: 3px;
      z-index: 20;
      overflow: hidden; /* 特殊节点保持overflow: hidden */
      
      .node-header {
        background-color: #ecf5ff;
        color: #409eff;
        font-size: 18px;
        padding: 10px 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 5px 5px 0 0; /* 调整为和外部圆角匹配 */
        
        .special-icon {
          font-size: 24px;
          margin-right: 8px;
        }
        
        .node-title {
          flex: 1;
          font-weight: 500;
          margin-right: 8px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .node-body {
        background-color: #f6faff;
        padding: 12px;
        height: calc(100% - 49px); /* 减去特殊节点header的高度 */
        border-radius: 0 0 5px 5px; /* 添加底部内圆角 */
        
        .node-type {
          font-size: 14px;
          font-weight: 500;
        }
        
        .node-desc {
          font-size: 13px;
          margin-top: 5px;
        }
      }
    }
    
    &.node-type-end {
      border-color: #67c23a;
      border-width: 3px;
      z-index: 20;
      overflow: hidden; /* 特殊节点保持overflow: hidden */
      
      .node-header {
        background-color: #f0f9eb;
        color: #67c23a;
        font-size: 18px;
        padding: 10px 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 5px 5px 0 0; /* 调整为和外部圆角匹配 */
        
        .special-icon {
          font-size: 24px;
          margin-right: 8px;
        }
        
        .node-title {
          flex: 1;
          font-weight: 500;
          margin-right: 8px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .node-body {
        background-color: #f8fcf5;
        padding: 12px;
        height: calc(100% - 49px); /* 减去特殊节点header的高度 */
        border-radius: 0 0 5px 5px; /* 添加底部内圆角 */
        
        .node-type {
          font-size: 14px;
          font-weight: 500;
        }
        
        .node-desc {
          font-size: 13px;
          margin-top: 5px;
        }
      }
    }
    
    &.special-node {
      z-index: 20;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
    
    /* 任务类型样式 */
    &.node-type-ThriftGeneric {
      border-left: 5px solid #409eff;
      box-shadow: 0 3px 10px rgba(64, 158, 255, 0.15);
      
      .node-header {
        background-color: #ecf5ff;
        color: #409eff;
      }
    }
    
    &.node-type-Calculate {
      border-left: 3px solid #c0c4cc;
      box-shadow: 0 3px 10px rgba(64, 158, 255, 0.15);
      
      .node-header {
        background-color: #f8f8f8;
        color: #909399;
      }

    }
    
    &.node-type-Squirrel {
      border-left: 5px solid #e6a23c;
      box-shadow: 0 3px 10px rgba(230, 162, 60, 0.15);
      
      .node-header {
        background-color: #fdf6ec;
        color: #e6a23c;
      }

    }
  }
  
  /* 边线样式 */
  .edge-path {
    transition: stroke 0.3s;
    
    &:hover {
      stroke: #409eff;
      stroke-width: 2.5;
    }
    
    &.edge-special {
      stroke-dasharray: 5, 5;
    }
  }
  
  /* 拖动中的节点样式 */
  .special-dragging {
    z-index: 1000;
    opacity: 0.9;
    cursor: grabbing;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }
}

/* 添加脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>