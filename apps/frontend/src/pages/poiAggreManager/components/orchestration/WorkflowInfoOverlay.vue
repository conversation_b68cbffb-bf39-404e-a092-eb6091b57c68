<template>
  <div class="workflow-info-overlay">
    <div class="info-section">
      <div class="info-header">
        <h3>工作流信息</h3>
        <div class="edit-buttons">
          <el-tooltip v-if="!internalIsEditing" content="编辑工作流信息" placement="top" :hide-after="1000">
            <el-button type="primary" link @click="toggleEditMode" :disabled="!editable">
              <el-icon><Edit /></el-icon>
            </el-button>
          </el-tooltip>
          <template v-else>
            <el-tooltip content="确定修改" placement="top" :hide-after="1000">
              <el-button type="success" link @click="confirm">
                <el-icon><Check /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="取消修改" placement="top" :hide-after="1000">
              <el-button type="danger" link @click="cancel">
                <el-icon><Close /></el-icon>
              </el-button>
            </el-tooltip>
          </template>
        </div>
      </div>
      <div class="info-content">
        <el-form label-position="left" size="small" label-width="85px">
          <el-form-item label="工作流名称">
            <el-input v-if="internalIsEditing" v-model="workflowForm.name" placeholder="请输入工作流名称" />
            <span v-else>{{ workflow?.name || '未命名工作流' }}</span>
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-if="internalIsEditing" v-model="workflowForm.description" type="textarea" :rows="2" placeholder="请输入工作流描述" />
            <span v-else>{{ workflow?.description || '无描述' }}</span>
          </el-form-item>
          <el-form-item label="超时设置">
            <el-input-number v-if="internalIsEditing" v-model="workflowForm.timeout" :min="1" :step="10" />
            <span v-else>{{ workflow?.timeout || 1000 }}毫秒</span>
          </el-form-item>
          <el-form-item label="快速失败">
            <el-switch v-if="internalIsEditing" v-model="workflowForm.failFast" />
            <el-tag v-else :type="workflow?.failFast ? 'success' : 'info'" size="small">
              {{ workflow?.failFast ? '是' : '否' }}
            </el-tag>
          </el-form-item>
          <el-form-item label="重试设置">
            <div v-if="internalIsEditing" class="retry-inputs">
              <div class="retry-input-with-label">
                <span class="retry-label">最大重试次数</span>
                <el-input-number v-model="getRetryForm.maxAttempts" :min="0" :max="10" controls-position="right" @change="handleRetryChange" />
              </div>
              <div class="retry-input-with-label">
                <span class="retry-label">重试延迟(毫秒)</span>
                <el-input-number v-model="getRetryForm.delay" :min="0" :max="100" controls-position="right" @change="handleRetryChange" />
              </div>
            </div>
            <span v-else>{{ workflow?.retry ? `最大重试 ${workflow.retry.maxAttempts} 次，延迟 ${workflow.retry.delay} 毫秒` : '未设置重试' }}</span>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Edit, Check, Close } from '@element-plus/icons-vue';
import { Workflow, RetryOption } from '../../types/orchestration';

const props = defineProps({
  workflow: {
    type: Object as () => Workflow | null,
    default: null
  },
  editable: {
    type: Boolean,
    default: false
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update', 'update:isEditing']);

// 内部编辑状态变量
const internalIsEditing = ref(props.isEditing);

// 监听props.isEditing变化，同步到内部状态
watch(() => props.isEditing, (newValue) => {
  internalIsEditing.value = newValue;
}, { immediate: true });

// 表单数据，retry默认为null
const workflowForm = reactive({
  name: '',
  description: '',
  timeout: 1000,
  failFast: true,
  retry: null as RetryOption | null
});

// 用于编辑时显示的retry表单数据
const retryForm = reactive({
  maxAttempts: 1,
  delay: 10
});

// 获取retry表单数据的计算属性
const getRetryForm = computed(() => {
  return {
    get maxAttempts() {
      return retryForm.maxAttempts;
    },
    set maxAttempts(value) {
      retryForm.maxAttempts = value;
    },
    get delay() {
      return retryForm.delay;
    },
    set delay(value) {
      retryForm.delay = value;
    }
  };
});

// 处理retry变更，确保创建retry对象
const handleRetryChange = () => {
  if (workflowForm.retry === null) {
    workflowForm.retry = {
      maxAttempts: retryForm.maxAttempts,
      delay: retryForm.delay
    };
  } else {
    workflowForm.retry.maxAttempts = retryForm.maxAttempts;
    workflowForm.retry.delay = retryForm.delay;
  }
};

// 切换编辑模式
const toggleEditMode = () => {
  emit('update:isEditing', true);
};

// 监听workflow变化，更新表单
watch(() => props.workflow, (newWorkflow) => {
  if (newWorkflow) {
    workflowForm.name = newWorkflow.name || '';
    workflowForm.description = newWorkflow.description || '';
    workflowForm.timeout = newWorkflow.timeout || 1000;
    workflowForm.failFast = newWorkflow.failFast !== undefined ? newWorkflow.failFast : true;
    
    // 设置retry为null或者复制原有值
    if (newWorkflow.retry) {
      workflowForm.retry = { ...newWorkflow.retry };
      retryForm.maxAttempts = newWorkflow.retry.maxAttempts;
      retryForm.delay = newWorkflow.retry.delay;
    } else {
      workflowForm.retry = null;
      retryForm.maxAttempts = 1;
      retryForm.delay = 10;
    }
  }
}, { immediate: true, deep: true });

// 确定更改
const confirm = () => {
  if (!props.workflow) return;
  
  // 组装更新数据，保持retry的null状态或对象状态
  const updateData = {
    name: workflowForm.name,
    description: workflowForm.description,
    timeout: workflowForm.timeout,
    failFast: workflowForm.failFast,
    retry: workflowForm.retry
  };
  
  // 触发更新事件
  emit('update', updateData);
  
  // 退出编辑模式
  emit('update:isEditing', false);
  
  ElMessage.success('工作流信息已更新');
};

// 取消编辑
const cancel = () => {
  // 重置表单
  if (props.workflow) {
    workflowForm.name = props.workflow.name || '';
    workflowForm.description = props.workflow.description || '';
    workflowForm.timeout = props.workflow.timeout || 1000;
    workflowForm.failFast = props.workflow.failFast !== undefined ? props.workflow.failFast : true;
    
    // 重置retry状态
    if (props.workflow.retry) {
      workflowForm.retry = { ...props.workflow.retry };
      retryForm.maxAttempts = props.workflow.retry.maxAttempts;
      retryForm.delay = props.workflow.retry.delay;
    } else {
      workflowForm.retry = null;
      retryForm.maxAttempts = 1;
      retryForm.delay = 10;
    }
  }
  
  // 退出编辑模式
  emit('update:isEditing', false);
};
</script>

<script lang="ts">
export default {
  name: 'WorkflowInfoOverlay'
};
</script>

<style lang="scss" scoped>
.workflow-info-overlay {
  position: absolute;
  top: 60px;
  left: 20px;
  width: 320px;
  z-index: 100;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid #ebeef5;
  
  .info-section {
    padding: 0;
    
    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 15px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      
      h3 {
        margin: 0;
        font-size: 15px;
        color: #303133;
      }
      
      .edit-buttons {
        display: flex;
        gap: 5px;
        
        .el-button {
          padding: 4px 8px;
        }
        
        /* 添加禁用状态的样式 */
        .el-button.is-disabled {
          opacity: 0.4;
          cursor: not-allowed;
        }
      }
    }
    
    .info-content {
      padding: 10px 15px;
      max-height: 300px;
      overflow-y: auto;
      
      .el-form-item {
        margin-bottom: 8px;
        
        :deep(.el-form-item__label) {
          color: #606266;
          font-weight: 500;
        }
      }
      
      .retry-inputs {
        display: flex;
        gap: 10px;
        
        .el-input-number {
          width: 100%;
          flex: 1;
        }
        
        .retry-input-with-label {
          display: flex;
          flex-direction: column;
          flex: 1;
          
          .retry-label {
            min-width: 90px;
            font-size: 12px;
            color: #606266;
            margin-bottom: 5px;
          }
        }
      }
    }
  }
}
</style> 