<template>
  <div class="component-by-type">
    <!-- 字符串类型 -->
    <el-input 
      v-if="isStringType"
      v-model="localValue"
      size="small"
      placeholder="请输入字符串"
      clearable
      @change="updateValue"
    />
    
    <!-- 数字类型 -->
    <el-input-number
      v-else-if="isNumberType"
      v-model="localValue"
      :controls="true"
      :precision="getPrecision()"
      size="small"
      style="width: 100%"
      @change="updateValue"
    />
    
    <!-- 布尔类型 -->
    <el-radio-group
      v-else-if="isBooleanType"
      v-model="localValue"
      size="small"
      @change="updateValue"
    >
      <el-radio-button :label="true">是</el-radio-button>
      <el-radio-button :label="false">否</el-radio-button>
    </el-radio-group>
    
    <!-- 日期时间类型 -->
    <el-date-picker
      v-else-if="isDateType"
      v-model="localValue"
      size="small"
      :type="getDatePickerType()"
      format="YYYY-MM-DD HH:mm:ss"
      value-format="YYYY-MM-DD HH:mm:ss"
      style="width: 100%"
      placeholder="请选择日期时间"
      @change="updateValue"
    />
    
    <!-- 枚举类型 -->
    <el-select
      v-else-if="isEnumType"
      v-model="localValue"
      placeholder="请选择"
      size="small"
      style="width: 100%"
      @change="updateValue"
    >
      <el-option
        v-for="item in node.enums"
        :key="item"
        :label="item"
        :value="item"
      />
    </el-select>
    
    <!-- 对象类型 -->
    <div 
      v-else-if="isObjectType" 
      class="complex-type"
    >
      <el-tag size="small" type="primary">{ 对象 }</el-tag>
    </div>
    
    <!-- 数组类型 -->
    <div 
      v-else-if="isArrayType" 
      class="complex-type"
    >
      <el-tag size="small" type="danger">[ 数组 ]</el-tag>
    </div>
    
    <!-- Map类型 -->
    <div 
      v-else-if="isMapType" 
      class="complex-type"
    >
      <el-tag size="small" type="warning">{ 键值对 }</el-tag>
    </div>
    
    <!-- 默认输入框 -->
    <el-input 
      v-else 
      v-model="localValue"
      size="small"
      placeholder="请输入值"
      clearable
      @change="updateValue"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// 组件名称定义
defineOptions({
  name: 'ComponentByType'
});

// 节点类型
interface SchemaNodeType {
  id: string;
  name: string;
  path: string;
  type: string;
  value: any;
  description?: string;
  expanded: boolean;
  checked: boolean;
  children: SchemaNodeType[];
  enums?: string[];
}

// 组件属性
const props = defineProps<{
  node: SchemaNodeType;
}>();

// 事件
const emit = defineEmits(['update:value']);

// 本地值
const localValue = ref(props.node.value);

// 监听节点值变化
watch(() => props.node.value, (newVal) => {
  localValue.value = newVal;
}, { immediate: true });

// 类型判断
const isStringType = computed(() => {
  const type = props.node.type.toLowerCase();
  return type === 'string' || type.includes('string') || type.includes('char');
});

const isNumberType = computed(() => {
  const type = props.node.type.toLowerCase();
  return type === 'number' || 
         type === 'int' || 
         type === 'integer' || 
         type === 'long' || 
         type.includes('int') || 
         type.includes('long') || 
         type.includes('double') || 
         type.includes('float');
});

const isBooleanType = computed(() => {
  const type = props.node.type.toLowerCase();
  return type === 'boolean' || type.includes('boolean');
});

const isDateType = computed(() => {
  const type = props.node.type.toLowerCase();
  return type.includes('date') || type.includes('time');
});

const isEnumType = computed(() => {
  return props.node.type === 'enum' && Array.isArray(props.node.enums);
});

const isObjectType = computed(() => {
  return props.node.type === 'object';
});

const isArrayType = computed(() => {
  return props.node.type === 'array';
});

const isMapType = computed(() => {
  return props.node.type.includes('Map<') || props.node.type.startsWith('java.util.Map');
});

// 获取数字精度
const getPrecision = () => {
  const type = props.node.type.toLowerCase();
  
  if (type.includes('double') || type.includes('float')) {
    return 2; // 浮点数显示两位小数
  }
  
  return 0; // 整数不显示小数位
};

// 获取日期选择器类型
const getDatePickerType = () => {
  const type = props.node.type.toLowerCase();
  
  if (type.includes('datetime')) {
    return 'datetime';
  } else if (type.includes('date')) {
    return 'date';
  } else if (type.includes('time')) {
    return 'time';
  }
  
  return 'datetime'; // 默认日期时间类型
};

// 更新值
const updateValue = () => {
  props.node.value = localValue.value;
  emit('update:value', props.node.path, localValue.value);
};
</script>

<style lang="scss" scoped>
.component-by-type {
  width: 100%;
  
  .complex-type {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 0 10px;
    background-color: transparent;
    border-radius: 4px;
    color: #909399;
    font-family: monospace;
    font-size: 13px;
    cursor: default;
    user-select: none;
  }
}
</style> 