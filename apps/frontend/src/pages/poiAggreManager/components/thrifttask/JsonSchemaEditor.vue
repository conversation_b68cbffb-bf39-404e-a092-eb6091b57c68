<template>
  <div class="json-editor-container">
    <!-- 左侧编辑区域 -->
    <div class="edit-panel">
      <div class="edit-panel-header">
        <h3 class="panel-title">方法参数编辑器</h3>
        <div class="panel-actions">
          <el-button size="small" text @click="selectAll">全选</el-button>
          <el-button size="small" text @click="deselectAll">全不选</el-button>
          <el-button size="small" text @click="expandAll">全部展开</el-button>
          <el-button size="small" text @click="collapseAll">全部折叠</el-button>
        </div>
      </div>
      <div class="edit-panel-content">
        <!-- 根节点 -->
        <div v-if="rootNode" class="root-node">
          <!-- 根节点展开/折叠图标 -->
          <div class="node-toggle" @click="toggleRootNode">
            <el-icon v-if="rootNode.expanded"><CaretBottom /></el-icon>
            <el-icon v-else><CaretRight /></el-icon>
          </div>
          
          <!-- 根节点信息 -->
          <div class="root-name">
            <el-checkbox v-model="rootNode.checked" @click.stop @change="handleRootCheckChange"></el-checkbox>
            <span class="method-title">方法参数列表：</span>
            {{ rootNode.name }}
          </div>
          <div class="root-type">{{ getTypeDisplayName(rootNode.type) }}</div>
        </div>
        
        <!-- 字段列表 -->
        <div v-if="rootNode && rootNode.expanded" class="fields-container">
          <node-tree-item
            v-for="field in rootNode.children"
            :key="field.id"
            :node="field"
            :level="1"
            @update:value="handleUpdateValue"
            @checked:change="handleChildCheckChange"
          />
        </div>
        
        <!-- 无数据提示 -->
        <div v-if="!rootNode || !rootNode.children || rootNode.children.length === 0" class="empty-data">
          <el-empty description="暂无参数" />
        </div>
      </div>
    </div>
    
    <!-- 右侧JSON预览区域 -->
    <div class="preview-panel">
      <div class="preview-panel-header">
        <h3 class="panel-title">JSON预览</h3>
        <div class="panel-actions">
          <el-button size="small" text @click="copyJson">
            <el-icon><Document /></el-icon> 复制
          </el-button>
          <el-button size="small" text @click="formatJson">
            <el-icon><Operation /></el-icon> 格式化
          </el-button>
        </div>
      </div>
      <div class="preview-panel-content">
        <!-- 分别显示每个参数的JSON -->
        <template v-if="paramJsonList.length > 0">
          <div v-for="(json, index) in paramJsonList" :key="index" class="param-json-container">
            <div class="param-json-header">
              <span class="param-json-title">{{ rootNode && rootNode.children[index] ? rootNode.children[index].name : `参数${index + 1}` }}</span>
              <el-button size="small" text @click="copySingleJson(index)">
                <el-icon><Document /></el-icon> 复制
              </el-button>
            </div>
            <pre class="json-preview">{{ formatJsonString(json) }}</pre>
          </div>
        </template>
        <pre v-else class="json-preview">{}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, defineExpose } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  CaretBottom, 
  CaretRight, 
  Setting, 
  Document, 
  Operation,
  InfoFilled
} from '@element-plus/icons-vue';
import NodeTreeItem from './NodeTreeItem.vue';

// 组件名称定义
defineOptions({
  name: 'JsonSchemaEditor'
});

// 定义字段类型
interface SchemaNodeType {
  id: string;
  name: string;
  path: string;
  type: string;
  value: any;
  description?: string;
  expanded: boolean;
  checked: boolean;
  children: SchemaNodeType[];
  enums?: string[];
}

// 属性定义
const props = defineProps({
  schema: {
    type: Object,
    required: true
  }
});

// 根节点
const rootNode = ref<SchemaNodeType | null>(null);

// JSON预览 - 修改为数组，每个参数一个JSON对象
const paramJsonList = ref<any[]>([]);
const currentJson = ref<any>({}); // 保留原有变量以兼容现有逻辑
const formattedJson = computed(() => {
  try {
    // 这仍然保留原来的格式化JSON的计算属性，用于复制全部
    return JSON.stringify(currentJson.value, null, 2);
  } catch (e) {
    return '{}';
  }
});

// 获取类型显示名称
const getTypeDisplayName = (type: string): string => {
  if (!type) return '未知类型';
  
  // 判断是否为 List 类型
  const isListType = (t: string): boolean => {
    return t.includes('List<') || 
           t.startsWith('java.util.List') || 
           t.endsWith('List') || 
           t.endsWith('ArrayList') ||
           t.toLowerCase() === 'list';
  };
  
  // 判断是否为 Set 类型
  const isSetType = (t: string): boolean => {
    return t.includes('Set<') || 
           t.startsWith('java.util.Set') || 
           t.endsWith('Set') || 
           t.endsWith('HashSet') ||
           t.toLowerCase() === 'set';
  };
  
  // 判断是否为 Map 类型
  const isMapType = (t: string): boolean => {
    return t.includes('Map<') || 
           t.startsWith('java.util.Map') || 
           t.endsWith('Map') || 
           t.endsWith('HashMap') ||
           t.toLowerCase() === 'map';
  };
  
  // 处理泛型类型
  if (type.includes('<')) {
    const baseType = type.split('<')[0];
    const genericType = type.match(/<(.+)>/)?.[1] || '';
    
    // 简化类名
    let simpleBaseType = baseType;
    if (baseType.includes('.')) {
      simpleBaseType = baseType.split('.').pop() || '';
    }
    
    let simpleGenericType = genericType;
    if (genericType.includes('.')) {
      simpleGenericType = genericType.split('.').pop() || '';
    }
    
    // 处理特定类型
    if (isListType(simpleBaseType)) {
      return `List<${simpleGenericType}>`;
    }
    
    if (isSetType(simpleBaseType)) {
      return `Set<${simpleGenericType}>`;
    }
    
    if (isMapType(simpleBaseType) && genericType.includes(',')) {
      const keyValueTypes = genericType.split(',').map(t => t.trim());
      const keyType = keyValueTypes[0].split('.').pop() || keyValueTypes[0];
      const valueType = keyValueTypes[1].split('.').pop() || keyValueTypes[1];
      return `Map<${keyType},${valueType}>`;
    }
    
    return `${simpleBaseType}<${simpleGenericType}>`;
  }
  
  // 简化Java类型名称
  if (type.includes('.')) {
    const parts = type.split('.');
    const simpleName = parts[parts.length - 1];
    
    // 检查简化名称是否为集合类型
    if (isListType(simpleName)) {
      return 'List';
    }
    
    if (isSetType(simpleName)) {
      return 'Set';
    }
    
    if (isMapType(simpleName)) {
      return 'Map';
    }
    
    return simpleName;
  }
  
  // 检查原始类型
  if (isListType(type)) {
    return 'List';
  }
  
  if (isSetType(type)) {
    return 'Set';
  }
  
  if (isMapType(type)) {
    return 'Map';
  }
  
  // 基本类型映射
  const typeMap: Record<string, string> = {
    'object': 'object',
    'array': 'array',
    'string': 'string',
    'int': 'number',
    'integer': 'number',
    'long': 'number',
    'double': 'number',
    'float': 'number',
    'boolean': 'boolean',
    'enum': 'enum',
    'char': 'string'
  };
  
  return typeMap[type.toLowerCase()] || type;
};

// 获取参数显示名称
const getParamDisplayName = (type: string): string => {
  // 处理泛型类型，如java.util.Set<java.lang.String>
  if (type.includes('<') && type.includes('>')) {
    const baseType = type.split('<')[0];
    const genericType = type.match(/<(.+)>/)?.[1] || '';
    
    // 获取基础类型的简化名称
    const baseClassName = baseType.split('.').pop() || baseType;
    
    // 获取泛型参数的简化名称
    const genericClassName = genericType.split('.').pop() || genericType;
    
    // 映射基本类型
    const typeMap: Record<string, string> = {
      'Set': '集合',
      'List': '列表',
      'Map': '映射'
    };
    
    const mappedBaseType = typeMap[baseClassName] || baseClassName;
    
    // 返回格式化的名称，不再添加"参数"后缀
    return `${genericClassName}${mappedBaseType}`;
  }
  
  // 非泛型类型的处理
  const className = type.split('.').pop() || type;
  
  // 映射基本类型
  const typeMap: Record<string, string> = {
    'int': '整数',
    'long': '长整数',
    'double': '浮点数',
    'float': '浮点数',
    'boolean': '布尔值',
    'String': '字符串',
    'char': '字符',
    'Integer': '整数',
    'Long': '长整数',
    'Double': '浮点数',
    'Float': '浮点数',
    'Boolean': '布尔值'
  };
  
  // 不再添加"参数"后缀
  return typeMap[className] || className;
};

// 获取类型标签类型
const getTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'string': 'info',
    'number': 'success',
    'int': 'success',
    'integer': 'success',
    'long': 'success',
    'double': 'success',
    'float': 'success',
    'boolean': 'warning',
    'object': 'primary',
    'array': 'danger',
    'enum': 'warning'
  };
  
  // 将类型转为小写以匹配
  const lowerType = type.toLowerCase();
  
  // 返回匹配的标签类型或默认为'info'
  return typeMap[lowerType] || 'info';
};

// 判断是否为基本类型
const isBasicType = (type: string): boolean => {
  if (!type) return true;
  
  const lowerType = type.toLowerCase();
  
  // 处理Java全限定名
  let baseTypeName = type;
  if (type.includes('.')) {
    baseTypeName = type.split('.').pop()?.toLowerCase() || '';
  }
  
  // 基本类型列表
  const basicTypes = [
    'string', 'number', 'boolean', 'int', 'integer', 'long', 
    'float', 'double', 'short', 'byte', 'char', 'boolean',
    'date', 'datetime', 'time'
  ];
  
  // 检查是否为枚举类型
  if (type.startsWith('java.lang.Enum') || baseTypeName === 'enum') {
    return true;
  }
  
  return basicTypes.includes(lowerType) || basicTypes.includes(baseTypeName);
};

// 判断是否为Map类型
const isMapType = (type: string): boolean => {
  return type.includes('Map<') || 
         type.startsWith('java.util.Map') || 
         type.endsWith('Map') || 
         type.endsWith('HashMap') ||
         type.toLowerCase() === 'map';
};

// 从节点构建JSON
const buildJsonFromNode = (node: SchemaNodeType): any => {
  try {
    if (!node) {
      console.warn('buildJsonFromNode: 节点为空');
      return undefined;
    }
    
    console.log('从节点构建JSON:', node.name, node.type, node.checked);
    
    if (!node.checked) {
      console.log('节点未选中，跳过:', node.name);
      return undefined;
    }
    
    // 处理叶子节点
    if (!node.children || node.children.length === 0) {
      console.log('处理叶子节点:', node.name, '值:', node.value);
      
      // 处理枚举类型
      if (node.type === 'enum') {
        return node.value;
      }
      
      return node.value;
    }
    
    // 处理数组或集合类型
    const isListType = node.type === 'array' || 
                       node.type.includes('List<') || 
                       node.type.startsWith('java.util.List') ||
                       node.type.includes('Set<') || 
                       node.type.startsWith('java.util.Set');
    
    if (isListType) {
      console.log('处理数组/集合节点:', node.name);
      
      // 如果没有子节点，返回空数组
      if (!node.children.length) {
        return [];
      }
      
      // 如果有自定义值（来自NodeTreeItem中的arrayItems），直接使用
      if (Array.isArray(node.value) && node.value.length > 0) {
        // 遍历并递归处理每个数组项
        const result = [];
        
        for (const item of node.value) {
          // 如果是复杂对象，递归处理，否则直接使用值
          if (typeof item === 'object' && item !== null) {
            // 找到对应的数组项模板
            const template = node.children[0]; // 通常只有一个子节点作为模板
            if (template) {
              // 创建一个临时节点来递归处理
              const tempNode = JSON.parse(JSON.stringify(template));
              tempNode.value = item;
              result.push(buildJsonFromNode(tempNode));
            } else {
              result.push(item);
            }
          } else {
            result.push(item);
          }
        }
        
        return result;
      }
      
      // fallback: 使用子节点模板创建示例数组项
      const template = node.children[0];
      if (template) {
        const itemValue = buildJsonFromNode(template);
        return itemValue !== undefined ? [itemValue] : [];
      }
      
      return [];
    }
    
    // 处理Map类型
    if (isMapType(node.type)) {
      console.log('处理Map节点:', node.name);
      
      // 如果有自定义Map对象值，直接使用
      if (typeof node.value === 'object' && node.value !== null && Object.keys(node.value).length > 0) {
        console.log('使用已有Map值:', node.value);
        return node.value;
      }
      
      // 否则返回空对象
      return {};
    }
    
    // 处理普通对象类型
    if (node.type === 'object') {
      console.log('处理对象节点:', node.name);
      const result: Record<string, any> = {};
      
      // 只处理选中的子节点
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach(child => {
          try {
            if (!child) return;
            
            if (child.checked) {
              const childValue = buildJsonFromNode(child);
              if (childValue !== undefined) {
                console.log('添加对象属性:', child.name, '值:', childValue);
                result[child.name] = childValue;
              }
            } else {
              console.log('跳过未选中的子节点:', child.name);
            }
          } catch (error) {
            console.error(`处理对象子节点${child?.name}出错:`, error);
          }
        });
      }
      
      console.log('对象结果:', result);
      return result;
    }
    
    // 默认返回节点值
    console.log('返回默认值:', node.name, node.value);
    return node.value;
  } catch (error) {
    console.error('buildJsonFromNode出错:', error, '节点:', node);
    return undefined;
  }
};

// 更新JSON预览 - 修改为分别构建每个参数的JSON
const updateJsonPreview = () => {
  if (!rootNode.value || !rootNode.value.children || rootNode.value.children.length === 0) {
    console.warn('updateJsonPreview: rootNode为空或没有子节点');
    paramJsonList.value = [];
    currentJson.value = {};
    return;
  }
  
  try {
    // 清空参数JSON列表
    paramJsonList.value = [];
    
    // 遍历每个根节点的子节点（方法参数）
    rootNode.value.children.forEach(paramNode => {
      if (paramNode.checked) {
        // 为每个参数构建单独的JSON
        const paramJson = buildJsonFromNode(paramNode);
        if (paramJson !== undefined) {
          paramJsonList.value.push(paramJson);
        }
      }
    });
    
    // 为了兼容原有逻辑，也更新currentJson
    // 将所有参数合并为一个对象
    const combinedJson: Record<string, any> = {};
    rootNode.value.children.forEach((paramNode, index) => {
      if (paramNode.checked) {
        combinedJson[paramNode.name] = paramJsonList.value[index];
      }
    });
    
    currentJson.value = combinedJson;
  } catch (error) {
    console.error('构建JSON失败:', error);
    paramJsonList.value = [];
    currentJson.value = {};
  }
};

// 创建默认根节点
const createDefaultRootNode = () => {
  rootNode.value = {
    id: 'root',
    name: '未知方法',
    path: 'root',
    type: 'object',
    value: {},
    expanded: true,
    checked: true,
    children: [{
      id: 'default_param',
      name: '请求参数',
      path: 'root.default_param',
      type: 'object',
      value: {},
      expanded: true,
      checked: true,
      children: []
    }]
  };
  updateJsonPreview();
};

// 获取类型默认值
const getDefaultValueForType = (type: string): any => {
  if (!type) return null;
  
  switch (type.toLowerCase()) {
    case 'string':
      return '';
    case 'number':
    case 'int':
    case 'integer':
    case 'long':
      return 0;
    case 'double':
    case 'float':
      return 0.0;
    case 'boolean':
      return false;
    case 'object':
      return {};
    case 'array':
      return [];
    default:
      return null;
  }
};

// 从types中查找类型定义
const findTypeDefinition = (typeName: string, schema: any): any => {
  if (!schema?.types || !Array.isArray(schema.types)) {
    console.warn('Schema中没有types数组或格式不正确', schema);
    return null;
  }
  
  // 规范化类型名称，移除空格
  const normalizedTypeName = typeName.trim();
  
  // 处理泛型类型，提取基本类型名
  let baseTypeName = normalizedTypeName;
  if (normalizedTypeName.includes('<')) {
    baseTypeName = normalizedTypeName.split('<')[0].trim();
  }
  
  // 获取简化类名（不含包名）
  let simpleClassName = baseTypeName;
  if (baseTypeName.includes('.')) {
    simpleClassName = baseTypeName.split('.').pop() || '';
  }
  
  console.log('查找类型定义:', normalizedTypeName, '基本类型:', baseTypeName, '简化类名:', simpleClassName);
  
  // 1. 尝试精确匹配完整类型名
  let typeInfo = schema.types.find((t: any) => 
    t && t.type && t.type.trim() === normalizedTypeName
  );
  
  // 2. 如果找不到，尝试匹配不含泛型的类名
  if (!typeInfo && normalizedTypeName !== baseTypeName) {
    typeInfo = schema.types.find((t: any) => 
      t && t.type && t.type.trim() === baseTypeName
    );
  }
  
  // 3. 如果还找不到，尝试匹配简化类名（不含包名的类名）
  if (!typeInfo) {
    typeInfo = schema.types.find((t: any) => {
      if (!t || !t.type) return false;
      const tClassName = t.type.split('.').pop() || '';
      return tClassName === simpleClassName;
    });
  }
  
  // 4. 最后尝试部分匹配（类名结尾部分匹配）
  if (!typeInfo) {
    typeInfo = schema.types.find((t: any) => {
      if (!t || !t.type) return false;
      return t.type.endsWith('.' + simpleClassName);
    });
  }
  
  if (typeInfo) {
    console.log('找到类型定义:', normalizedTypeName, '→', typeInfo.type);
  } else {
    console.warn('未找到类型定义:', normalizedTypeName);
  }
  
  return typeInfo;
};

// 递归创建编辑器节点
const createEditorNode = (
  id: string,
  name: string,
  type: string,
  schema: any,
  initialValue?: any,
  parentPath: string = ''
): SchemaNodeType | null => {
  try {
    if (!type) {
      console.warn('类型为空, id:', id, 'name:', name);
      return null;
    }
    
    console.log('创建节点:', id, name, type);
    
    const path = parentPath ? `${parentPath}.${id}` : id;
    
    // 创建基本节点
    const node: SchemaNodeType = {
      id,
      name,
      path,
      type,
      value: initialValue ?? getDefaultValueForType(type),
      description: '',
      expanded: true,
      checked: true,
      children: []
    };
    
    // 处理基本类型
    if (isBasicType(type)) {
      console.log('处理基本类型:', type);
      return node;
    }
    
    // 处理集合类型 (Set, List)
    if (type.includes('Set<') || type.includes('List<') ||
        type.startsWith('java.util.Set') || type.startsWith('java.util.List')) {
      console.log('处理集合类型:', type);
      
      // 保留原始类型，但在内部当作数组处理
      const originalType = type;
      node.type = originalType;
      node.value = [];
      
      // 提取集合元素类型
      const genericMatch = type.match(/<(.+)>/);
      if (genericMatch && genericMatch[1]) {
        const elementType = genericMatch[1].trim();
        console.log('集合元素类型:', elementType);
        
        // 递归处理元素类型
        const elementTypeInfo = findTypeDefinition(elementType, schema);
        const elementNode = createEditorNode(
          'item',
          '元素',
          elementType,
          schema,
          undefined,
          path
        );
        
        if (elementNode) {
          node.children.push(elementNode);
        }
      }
      
      return node;
    }
    
    // 处理Map类型
    if (type.includes('Map<') || type.startsWith('java.util.Map')) {
      console.log('处理Map类型:', type);
      
      // 保留原始类型，不再转为object
      const originalType = type;
      node.type = originalType;
      node.value = {};
      
      // 提取键值类型
      const genericMatch = type.match(/<(.+),\s*(.+)>/);
      if (genericMatch && genericMatch[2]) {
        const keyType = genericMatch[1].trim();
        const valueType = genericMatch[2].trim();
        console.log('Map键类型:', keyType, '值类型:', valueType);
        
        // 递归处理值类型
        const valueTypeInfo = findTypeDefinition(valueType, schema);
        
        // 创建键的描述节点（不添加到子节点）
        const keyNode = createEditorNode(
          'key',
          '键类型',
          keyType,
          schema,
          undefined,
          path
        );
        
        // 创建值的描述节点
        const valueNode = createEditorNode(
          'value',
          '值类型',
          valueType,
          schema,
          undefined,
          path
        );
        
        if (keyNode) {
          node.children.push(keyNode);
        }
        
        if (valueNode) {
          node.children.push(valueNode);
        }
      }
      
      return node;
    }
    
    // 处理复杂对象类型
    console.log('尝试处理复杂对象类型:', type);
    const typeInfo = findTypeDefinition(type, schema);
    
    if (typeInfo) {
      console.log('找到复杂对象类型定义:', type, typeInfo);
      
      // 检查是否为枚举类型 - 支持多种枚举值字段名称
      const enumValues = typeInfo.enums || typeInfo.enumConstants || [];
      if (Array.isArray(enumValues) && enumValues.length > 0) {
        console.log('处理枚举类型:', type, '枚举值:', enumValues);
        node.type = 'enum';
        node.enums = enumValues;
        node.value = enumValues.length > 0 ? enumValues[0] : '';
        node.description = typeInfo.description || '';
        return node;
      }
      
      node.type = 'object';
      node.description = typeInfo.description || '';
      
      // 处理字段 - 先检查fields字段
      if (typeInfo.fields && typeof typeInfo.fields === 'object') {
        console.log('处理对象字段(fields)，字段数量:', Object.keys(typeInfo.fields).length);
        
        Object.entries(typeInfo.fields).forEach(([fieldName, fieldInfo]: [string, any]) => {
          if (!fieldInfo || !fieldInfo.type) {
            console.warn('字段信息不完整:', fieldName, fieldInfo);
            return;
          }
          
          console.log('处理字段:', fieldName, '类型:', fieldInfo.type);
          
          // 递归创建字段节点
          const fieldNode = createEditorNode(
            fieldName,
            fieldName,
            fieldInfo.type,
            schema,
            undefined,
            path
          );
          
          if (fieldNode) {
            fieldNode.description = fieldInfo.description || '';
            node.children.push(fieldNode);
          } else {
            console.warn('创建字段节点失败:', fieldName);
          }
        });
        
        console.log('对象字段处理完成，子节点数量:', node.children.length);
      } 
      // 再检查properties字段（作为fields的备选）
      else if (typeInfo.properties && typeof typeInfo.properties === 'object') {
        console.log('处理对象字段(properties)，字段数量:', Object.keys(typeInfo.properties).length);
        
        Object.entries(typeInfo.properties).forEach(([fieldName, fieldInfo]: [string, any]) => {
          // 跳过内部使用的字段
          if (fieldName === '__isset_bit_vector' || fieldName === 'optionals') {
            return;
          }
          
          if (!fieldInfo || !fieldInfo.type) {
            console.warn('字段信息不完整:', fieldName, fieldInfo);
            return;
          }
          
          console.log('处理字段:', fieldName, '类型:', fieldInfo.type);
          
          // 递归创建字段节点
          const fieldNode = createEditorNode(
            fieldName,
            fieldName,
            fieldInfo.type,
            schema,
            undefined,
            path
          );
          
          if (fieldNode) {
            fieldNode.description = fieldInfo.description || '';
            node.children.push(fieldNode);
      } else {
            console.warn('创建字段节点失败:', fieldName);
          }
        });
        
        console.log('对象字段处理完成，子节点数量:', node.children.length);
      } else if (typeInfo.genericParameters) {
        // 处理泛型参数
        console.log('处理泛型参数:', typeInfo.genericParameters);
        
        // 尝试从genericParameters中创建子节点
        if (Array.isArray(typeInfo.genericParameters)) {
          typeInfo.genericParameters.forEach((param: any, index: number) => {
            if (param && param.type) {
              console.log('处理泛型参数:', index, param.type);
              
              const paramNode = createEditorNode(
                `param_${index}`,
                param.name || `参数${index}`,
                param.type,
                schema,
                undefined,
                path
              );
              
              if (paramNode) {
                node.children.push(paramNode);
              }
            }
          });
        }
      } else {
        console.warn('对象类型没有fields或properties字段:', typeInfo);
        
        // 尝试使用superClass继续查找字段
        if (typeInfo.superClass) {
          console.log('尝试从父类获取字段:', typeInfo.superClass);
          const superClassInfo = findTypeDefinition(typeInfo.superClass, schema);
          
          if (superClassInfo && (superClassInfo.fields || superClassInfo.properties)) {
            const fieldsSource = superClassInfo.fields || superClassInfo.properties;
            Object.entries(fieldsSource).forEach(([fieldName, fieldInfo]: [string, any]) => {
              if (!fieldInfo || !fieldInfo.type) return;
              
              const fieldNode = createEditorNode(
                fieldName,
                fieldName,
                fieldInfo.type,
                schema,
                undefined,
                path
              );
              
              if (fieldNode) {
                node.children.push(fieldNode);
              }
            });
          }
        }
      }
    } else {
      // 对于未找到类型定义的复杂对象，尝试从schema中的其他信息构建
      console.warn('未找到类型定义，尝试从schema构建:', type);
      
      // 如果类型名包含类名，可能是一个Java类
      if (type.includes('.')) {
        node.type = 'object';
        // 如果是Java类，尝试根据类名在types中查找
        const simpleClassName = type.split('.').pop() || '';
        const possibleMatch = schema.types?.find((t: any) => {
          if (!t || !t.type) return false;
          return t.type.split('.').pop() === simpleClassName;
        });
        
        if (possibleMatch) {
          console.log('通过简化类名找到类型定义:', simpleClassName);
          // 检查properties字段
          if (possibleMatch.properties && typeof possibleMatch.properties === 'object') {
            Object.entries(possibleMatch.properties).forEach(([fieldName, fieldInfo]: [string, any]) => {
              // 跳过内部使用的字段
              if (fieldName === '__isset_bit_vector' || fieldName === 'optionals') {
                return;
              }

              if (!fieldInfo || !fieldInfo.type) return;
              
              const fieldNode = createEditorNode(
                fieldName,
                fieldName,
                fieldInfo.type,
                schema,
                undefined,
                path
              );
              
              if (fieldNode) {
                node.children.push(fieldNode);
              }
            });
          }
          // 检查fields字段
          else if (possibleMatch.fields && typeof possibleMatch.fields === 'object') {
            Object.entries(possibleMatch.fields).forEach(([fieldName, fieldInfo]: [string, any]) => {
              if (!fieldInfo || !fieldInfo.type) return;
              
              const fieldNode = createEditorNode(
                fieldName,
                fieldName,
                fieldInfo.type,
                schema,
                undefined,
                path
              );
              
              if (fieldNode) {
                node.children.push(fieldNode);
              }
            });
          }
        }
      }
    }
    
    return node;
  } catch (error) {
    console.error('创建编辑器节点出错:', error);
    return null;
  }
};

// 解析Schema
const parseSchema = (schema: any) => {
  try {
    if (!schema) {
      console.warn('schema为空');
      createDefaultRootNode();
      return;
    }
    
    console.log('开始解析schema:', schema);
    
    const methods = schema.methods || [];
    if (!methods.length) {
      console.warn('schema中没有methods数组或为空');
      createDefaultRootNode();
      return;
    }
    
    const firstMethod = methods[0];
    if (!firstMethod) {
      console.warn('没有找到方法信息');
      createDefaultRootNode();
      return;
    }
    
    const methodName = firstMethod.name || 'unknown';
    const parameterTypes = firstMethod.parameterTypes || [];
    
    console.log('解析到方法:', methodName, '参数类型:', parameterTypes);
    
    // 创建根节点 - 使用更具描述性的名称
    const root: SchemaNodeType = {
      id: 'root',
      name: firstMethod.methodSignature || methodName,
      path: 'root',
      type: 'object',
      value: {},
      expanded: true,
      checked: true,
      children: []
    };
    
    // 解析参数类型
    if (parameterTypes.length > 0) {
      parameterTypes.forEach((paramType: string, index: number) => {
        if (!paramType) return;
        
        console.log('处理参数类型:', paramType);
        
        // 创建参数节点
        const paramNode = createEditorNode(
          `param_${index}`,
          `${getParamDisplayName(paramType)}`,
          paramType,
          schema,
          undefined,
          'root'
        );
        
        if (paramNode) {
          root.children.push(paramNode);
        }
      });
    }
    
    console.log('创建的根节点:', root);
    rootNode.value = root;
    updateJsonPreview();
  } catch (error) {
    console.error('解析schema出错:', error);
    createDefaultRootNode();
  }
};

// 监听schema变化
watch(() => props.schema, (newVal) => {
  try {
    console.log('schema变化:', newVal);
    if (!newVal) {
      console.warn('schema为空，创建默认根节点');
      // 创建默认根节点
      rootNode.value = {
        id: 'root',
        name: 'root',
        path: 'root',
        type: 'object',
        value: {},
        expanded: true,
        checked: true,
        children: []
      };
      return;
    }
    parseSchema(newVal);
  } catch (error) {
    console.error('处理schema变化时发生错误:', error);
    // 创建默认根节点
    rootNode.value = {
      id: 'root',
      name: 'root',
      path: 'root',
      type: 'object',
      value: {},
      expanded: true,
      checked: true,
      children: []
    };
  }
}, { deep: true, immediate: true });

// 切换根节点展开/折叠
const toggleRootNode = () => {
  if (rootNode.value) {
    rootNode.value.expanded = !rootNode.value.expanded;
  }
};

// 切换节点展开状态
const toggleNodeExpand = (node: SchemaNodeType) => {
  node.expanded = !node.expanded;
};

// 判断是否为复杂类型
const isComplexType = (node: SchemaNodeType): boolean => {
  return node.type === 'object' || node.type === 'array';
};

// 更新值
const handleUpdateValue = (path: string, value: any) => {
  console.log('处理值更新:', path, value);
  
  // 通过路径查找并更新节点
  if (path && rootNode.value) {
    const pathParts = path.split('.');
    let currentNode = rootNode.value;
    
    // 跳过root节点
    for (let i = 1; i < pathParts.length; i++) {
      const part = pathParts[i];
      
      // 处理数组索引，例如: root.param_0[0]
      if (part.includes('[') && part.includes(']')) {
        const name = part.substring(0, part.indexOf('['));
        const indexStr = part.substring(part.indexOf('[') + 1, part.indexOf(']'));
        const index = parseInt(indexStr);
        
        // 找到对应的数组节点
        const arrayNode = currentNode.children.find(child => child.name === name || child.id === name);
        
        if (!arrayNode) {
          console.warn('未找到数组节点:', name);
          break;
        }
        
        // 确保节点值是数组
        if (!Array.isArray(arrayNode.value)) {
          arrayNode.value = [];
        }
        
        // 确保数组有足够的元素
        while (arrayNode.value.length <= index) {
          arrayNode.value.push(null);
        }
        
        // 更新特定索引的数组元素值
        arrayNode.value[index] = value;
        
        console.log('更新数组元素:', name, index, value);
        break;
      } 
      // 处理Map键，例如: root.param_0["key1"]
      else if (part.includes('[') && part.includes(']') && part.includes('"')) {
        const name = part.substring(0, part.indexOf('['));
        const keyMatch = part.match(/\["([^"]+)"\]/);
        const key = keyMatch ? keyMatch[1] : '';
        
        // 找到对应的Map节点
        const mapNode = currentNode.children.find(child => child.name === name || child.id === name);
        
        if (!mapNode) {
          console.warn('未找到Map节点:', name);
          break;
        }
        
        // 确保节点值是对象
        if (typeof mapNode.value !== 'object' || mapNode.value === null) {
          mapNode.value = {};
        }
        
        // 更新特定键的Map元素值
        if (key) {
          mapNode.value[key] = value;
          console.log('更新Map元素:', name, key, value);
        }
        
        break;
      }
      // 常规属性
      else {
        const child = currentNode.children.find(child => child.name === part || child.id === part);
        
        if (!child) {
          console.warn('未找到子节点:', part);
          break;
        }
        
        if (i === pathParts.length - 1) {
          // 这是最后一个路径部分，更新值
          child.value = value;
          console.log('更新节点值:', part, value);
        } else {
          // 继续向下查找
          currentNode = child;
        }
      }
    }
  }
  
  // 更新JSON预览
  updateJsonPreview();
};

// 展开所有节点
const expandAll = () => {
  if (!rootNode.value) return;
  expandOrCollapseRecursive(rootNode.value, true);
};

// 折叠所有节点
const collapseAll = () => {
  if (!rootNode.value) return;
  rootNode.value.expanded = true; // 保持根节点展开
  rootNode.value.children.forEach(child => {
    expandOrCollapseRecursive(child, false);
  });
};

// 递归展开或折叠
const expandOrCollapseRecursive = (node: SchemaNodeType, expand: boolean) => {
  node.expanded = expand;
  
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      expandOrCollapseRecursive(child, expand);
    });
  }
};

// 复制JSON
const copyJson = () => {
  try {
    const jsonStr = JSON.stringify(currentJson.value, null, 2);
    console.log('复制JSON:', jsonStr);
    
    // 检查clipboard API是否可用
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(jsonStr)
        .then(() => {
          ElMessage.success('JSON已复制到剪贴板');
        })
        .catch(err => {
          console.error('使用clipboard API复制失败:', err);
          // 回退方法：使用textarea元素复制
          fallbackCopyToClipboard(jsonStr);
        });
    } else {
      // 直接使用回退方法
      console.log('不支持clipboard API，使用回退方法');
      fallbackCopyToClipboard(jsonStr);
    }
  } catch (error) {
    console.error('复制JSON失败:', error);
    ElMessage.error('复制失败，请手动复制');
  }
};

// 回退复制方法（用于clipboard API不可用的情况）
const fallbackCopyToClipboard = (text: string) => {
  try {
    // 创建一个临时textarea元素
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 设置样式使其不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    
    // 选择文本并复制
    textArea.focus();
    textArea.select();
    const successful = document.execCommand('copy');
    
    // 删除临时元素
    document.body.removeChild(textArea);
    
    if (successful) {
      ElMessage.success('JSON已复制到剪贴板');
    } else {
      ElMessage.warning('无法复制，请手动复制');
    }
  } catch (err) {
    console.error('回退复制方法失败:', err);
    ElMessage.error('复制失败，请手动复制');
  }
};

// 格式化JSON
const formatJson = () => {
  try {
    currentJson.value = JSON.parse(JSON.stringify(currentJson.value));
    ElMessage.success('JSON已格式化');
  } catch (error) {
    console.error('格式化JSON失败:', error);
    ElMessage.error('格式化失败');
  }
};

// 全选节点
const selectAll = () => {
  if (!rootNode.value) return;
  
  // 递归设置所有节点的勾选状态
  setCheckedStateRecursive(rootNode.value, true);
  
  // 强制所有NodeTreeItem组件更新其内部状态
  const nodeRefs = document.querySelectorAll('.node-tree-item');
  
  // 强制重新计算JSON
  setTimeout(() => {
    // 1. 使用DOM事件通知所有组件更新
    document.dispatchEvent(new CustomEvent('updateArrayItems', { detail: { checked: true } }));
    
    // 2. 更新JSON预览 
    updateJsonPreview();
  }, 50);
};

// 全不选节点
const deselectAll = () => {
  if (!rootNode.value) return;
  
  // 递归设置所有节点的勾选状态
  setCheckedStateRecursive(rootNode.value, false);
  
  // 强制所有NodeTreeItem组件更新其内部状态
  const nodeRefs = document.querySelectorAll('.node-tree-item');
  
  // 强制重新计算JSON
  setTimeout(() => {
    // 1. 使用DOM事件通知所有组件更新
    document.dispatchEvent(new CustomEvent('updateArrayItems', { detail: { checked: false } }));
    
    // 2. 更新JSON预览
    updateJsonPreview();
  }, 50);
};

// 递归设置选中状态 - 修改为深度递归处理所有节点
const setCheckedStateRecursive = (node: SchemaNodeType, checked: boolean) => {
  // 设置当前节点勾选状态
  node.checked = checked;
  
  // 处理子节点
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      setCheckedStateRecursive(child, checked);
    });
  }
  
  // 特殊处理数组、集合和Map值
  // 这些可能保存在节点的value属性中，而不是children
  if (Array.isArray(node.value)) {
    // 尝试处理数组/集合项
    if (typeof node.value === 'object') {
      // 遍历每个数组项，可能包含勾选状态
      for (let i = 0; i < node.value.length; i++) {
        const item = node.value[i];
        if (item && typeof item === 'object' && 'checked' in item) {
          item.checked = checked;
          
          // 递归处理嵌套项
          if (item.children && item.children.length > 0) {
            item.children.forEach((subChild: any) => {
              if (typeof subChild === 'object' && subChild !== null) {
                setCheckedStateRecursive(subChild, checked);
              }
            });
          }
        }
      }
    }
  } else if (node.type && (node.type.includes('Map<') || node.type.toLowerCase().includes('map'))) {
    // 尝试处理Map条目
    if (typeof node.value === 'object' && node.value !== null) {
      Object.keys(node.value).forEach(key => {
        const value = node.value[key];
        if (value && typeof value === 'object' && 'checked' in value) {
          value.checked = checked;
          
          // 递归处理嵌套项
          if (value.children && value.children.length > 0) {
            value.children.forEach((subChild: any) => {
              if (typeof subChild === 'object' && subChild !== null) {
                setCheckedStateRecursive(subChild, checked);
              }
            });
          }
        }
      });
    }
  }
};

// 添加处理根节点勾选状态变化的方法
const handleRootCheckChange = (value: boolean) => {
  console.log('根节点勾选状态变化:', value);
  if (rootNode.value) {
    rootNode.value.checked = value;
    // 递归设置所有子节点的勾选状态
    setCheckedStateRecursive(rootNode.value, value);
    // 更新JSON预览
    updateJsonPreview();
  }
};

// 处理子节点的勾选状态变化
const handleChildCheckChange = (path: string, checked: boolean, node: SchemaNodeType) => {
  console.log('子节点勾选状态变化:', path, checked);
  // 更新JSON预览
  updateJsonPreview();
};

// 暴露方法给父组件
defineExpose({
  buildJsonFromSchema: () => {
    console.log('构建JSON，当前节点:', rootNode.value);
    if (!rootNode.value) return {};
    
    try {
      const json = buildJsonFromNode(rootNode.value);
      console.log('构建的JSON结果:', json);
      return json;
    } catch (error) {
      console.error('构建JSON出错:', error);
      ElMessage.error('构建参数JSON失败');
      return {};
    }
  },
  expandAll,
  collapseAll,
  selectAll,
  deselectAll
});

// 组件挂载时
onMounted(() => {
  parseSchema(props.schema);
  
  // 延迟处理，确保schema完全解析
  setTimeout(() => {
    if (rootNode.value && (!rootNode.value.children || rootNode.value.children.length === 0)) {
      console.log('创建默认根节点，因为解析后没有子节点');
      createDefaultRootNode();
    }
  }, 500);
});

// 格式化单个JSON字符串
const formatJsonString = (json: any): string => {
  try {
    return JSON.stringify(json, null, 2);
  } catch (e) {
    return '{}';
  }
};

// 复制单个参数的JSON
const copySingleJson = (index: number) => {
  try {
    if (index >= 0 && index < paramJsonList.value.length) {
      const jsonStr = JSON.stringify(paramJsonList.value[index], null, 2);
      
      // 检查clipboard API是否可用
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(jsonStr)
          .then(() => {
            ElMessage.success('JSON已复制到剪贴板');
          })
          .catch(err => {
            console.error('使用clipboard API复制失败:', err);
            // 回退方法：使用textarea元素复制
            fallbackCopyToClipboard(jsonStr);
          });
      } else {
        // 直接使用回退方法
        console.log('不支持clipboard API，使用回退方法');
        fallbackCopyToClipboard(jsonStr);
      }
    }
  } catch (error) {
    console.error('复制单个JSON失败:', error);
    // 避免嵌套try-catch，直接在这里尝试回退方法
    try {
      if (index >= 0 && index < paramJsonList.value.length) {
        fallbackCopyToClipboard(JSON.stringify(paramJsonList.value[index], null, 2));
      } else {
        ElMessage.error('复制失败，请手动复制');
      }
    } catch (e) {
      ElMessage.error('复制失败，请手动复制');
    }
  }
};
</script>

<style lang="scss" scoped>
.json-editor-container {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  
  .edit-panel,
  .preview-panel {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      
      .panel-title {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }
      
      .panel-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    &-content {
      flex: 1;
      overflow: auto;
      padding: 10px;
    }
  }
  
  .edit-panel {
    flex: 6;
    border-right: 1px solid #dcdfe6;
    
    .root-node {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px;
      padding-left: 8px;
      border-radius: 6px;
      background-color: #ecf5ff;
      border: 1px solid #d9ecff;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      
      .node-toggle {
        margin-right: 10px;
        cursor: pointer;
        color: #409EFF;
        font-size: 16px;
        user-select: none;
      }
      
      .root-name {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: #303133;
        margin-right: 10px;
        user-select: none;
        
        .method-title {
          color: #409EFF;
          font-weight: 600;
          margin-right: 4px;
        }
      }
      
      .root-type {
        color: #909399;
        font-size: 13px;
        padding: 3px 8px;
        background-color: #fff;
        border-radius: 12px;
        border: 1px solid #dcdfe6;
        user-select: none;
      }
    }
    
    .fields-container {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding-left: 0;
      
      // 防止文本选择
      :deep(.node-toggle),
      :deep(.node-header),
      :deep(.node-name),
      :deep(.node-type) {
        user-select: none;
      }
    }
    
    .empty-data {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      background-color: #f8f8f8;
      border-radius: 4px;
    }
  }
  
  .preview-panel {
    flex: 4;
    background-color: #f8f8f8;
    
    .json-preview {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
      font-size: 13px;
      color: #303133;
      white-space: pre-wrap;
      word-break: break-all;
      padding: 8px;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #ebeef5;
      height: calc(100% - 20px);
      overflow: auto;
    }
    
    // 参数JSON容器样式
    .param-json-container {
      margin-bottom: 16px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .param-json-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background-color: #ecf5ff;
        border-bottom: 1px solid #d9ecff;
        
        .param-json-title {
          font-weight: 500;
          color: #409EFF;
        }
      }
      
      .json-preview {
        height: auto;
        max-height: 300px;
        margin: 0;
        border: none;
        border-radius: 0;
      }
    }
  }
}
</style> 