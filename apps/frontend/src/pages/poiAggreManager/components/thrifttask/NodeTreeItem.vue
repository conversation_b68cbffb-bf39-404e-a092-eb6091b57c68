<template>
  <div class="node-tree-item" :style="{ marginLeft: level > 1 ? '20px' : '0' }">
    <div class="node-header" @click="toggleExpand">
      <!-- 展开/折叠图标 -->
      <div v-if="isComplexType" class="node-toggle">
        <el-icon v-if="node.expanded"><CaretBottom /></el-icon>
        <el-icon v-else><CaretRight /></el-icon>
      </div>
      <div v-else class="node-toggle-placeholder"></div>
      
      <!-- 节点信息 -->
      <div class="node-info">
        <el-checkbox v-model="node.checked" @click.stop @change="handleCheckChange"></el-checkbox>
        <span class="node-name">{{ node.name }}</span>
        <el-tag 
          size="small" 
          :type="getTypeTagType(node.type)"
          class="node-type"
        >
          {{ getTypeDisplayName(node.type) }}
        </el-tag>
        
        <!-- 描述信息 -->
        <el-tooltip 
          v-if="node.description"
          :content="node.description" 
          placement="top"
          class="node-description"
        >
          <el-icon><InfoFilled /></el-icon>
        </el-tooltip>
        
        <el-input 
          v-if="node.type === 'enum' && node.enums"
          v-model="node.value"
          size="small"
          placeholder="请选择"
          @change="handleValueChange"
          class="key-input"
        >
          <el-option 
            v-for="item in node.enums"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-input>
      </div>
      
      <!-- 复杂类型操作按钮 -->
      <div v-if="isComplexType" class="node-value complex-actions" @click.stop>
        <!-- 数组类型添加按钮 -->
        <template v-if="node.type === 'array' || isListType || isSetType">
          <span class="empty-tip" v-if="arrayItems.length === 0">点击添加元素</span>
          <div class="array-summary" v-else>
            <el-tag size="small" type="info">{{ arrayItems.length }}个元素</el-tag>
          </div>
          <el-button 
            type="primary" 
            size="small" 
            plain
            circle
            @click.stop="addArrayItem"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </template>
        
        <!-- Map类型添加按钮 -->
        <template v-else-if="isMapType">
          <span class="empty-tip" v-if="mapEntries.length === 0">点击添加键值对</span>
          <div class="array-summary" v-else>
            <el-tag size="small" type="info">{{ mapEntries.length }}个键值对</el-tag>
          </div>
          <el-button 
            type="primary" 
            size="small" 
            plain
            circle
            @click.stop="addMapEntry"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </template>
      </div>
      
      <!-- 值编辑区域 - 统一宽度和右对齐 -->
      <div v-if="!isComplexType" class="node-value" @click.stop>
        <!-- 枚举类型 -->
        <el-select 
          v-if="node.type === 'enum' && node.enums"
          v-model="node.value"
          size="small"
          placeholder="请选择"
          @change="handleValueChange"
        >
          <el-option 
            v-for="item in node.enums"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        
        <!-- 布尔类型 -->
        <div v-else-if="node.type.toLowerCase() === 'boolean'" class="boolean-wrapper">
        <el-switch
          v-model="node.value"
          @change="handleValueChange"
        />
        </div>
        
        <!-- 数字类型 -->
        <el-input-number
          v-else-if="isNumericType"
          v-model="node.value"
          size="small"
          :controls="false"
          @change="handleValueChange"
        />
        
        <!-- 日期和时间类型 -->
        <el-date-picker
          v-else-if="isDateType"
          v-model="node.value"
          size="small"
          :type="getDatePickerType()"
          format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择日期时间"
          @change="handleValueChange"
        />
        
        <!-- 其他类型（字符串等） -->
        <el-input
          v-else
          v-model="node.value"
          size="small"
          :placeholder="'请输入' + node.name"
          @change="handleValueChange"
        />
      </div>
    </div>
    
    <!-- 子节点 -->
    <div v-if="isComplexType && node.expanded" class="node-children">
      <!-- Map类型展示 -->
      <template v-if="isMapType">
        <div 
          v-for="(entry, index) in mapEntries" 
          :key="index"
          class="map-entry"
        >
          <div class="node-header map-header">
            <!-- 展开/折叠图标占位 -->
            <div class="node-toggle-placeholder"></div>
            
            <!-- 键值对信息 -->
            <div class="node-info">
              <el-checkbox v-model="entry.valueNode.checked" @click.stop @change="(val) => handleMapEntryCheckChange(val, entry)"></el-checkbox>
              <span class="node-name">键:</span>
              <!-- 键输入框 -->
              <el-input 
                v-model="entry.key" 
                size="small" 
                placeholder="输入键名"
                @change="updateMapEntries"
                class="key-input"
              />
            </div>
            
            <!-- 值输入区域 -->
            <div class="node-value" @click.stop>
              <!-- 基本类型值 -->
              <template v-if="!isComplexTypeNode(entry.valueNode)">
                <component-by-type 
                  :node="entry.valueNode"
                  @update:value="handleMapValueChange"
                  @checked:change="(path, checked, node) => emit('checked:change', path, checked, node)"
                />
              </template>
              <!-- 复杂类型值 -->
              <template v-else>
                <div class="complex-type-summary" @click.stop="toggleMapValueExpand(entry.valueNode)">
                  <el-tag size="small" :type="getTypeTagType(entry.valueNode.type)">
                    {{ getTypeDisplayName(entry.valueNode.type) }}
                  </el-tag>
                  <span v-if="entry.valueNode.expanded" class="expand-icon"><el-icon><CaretBottom /></el-icon></span>
                  <span v-else class="expand-icon"><el-icon><CaretRight /></el-icon></span>
                </div>
              </template>
            </div>
            
            <!-- 删除按钮 -->
            <el-button 
              type="danger" 
              size="small" 
              plain
              circle
              @click.stop="removeMapEntry(index)"
              class="delete-button"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          
          <!-- 复杂类型值展开内容 -->
          <div v-if="isComplexTypeNode(entry.valueNode) && entry.valueNode.expanded" class="array-item-expanded">
            <node-tree-item
              :node="entry.valueNode"
              :level="level + 1"
              @update:value="handleMapValueChange"
              @checked:change="(path, checked, node) => emit('checked:change', path, checked, node)"
            />
          </div>
        </div>
      </template>
      
      <!-- 数组项展示 -->
      <template v-else-if="node.type === 'array' || isListType || isSetType">
        <div 
          v-for="(item, index) in arrayItems" 
          :key="index"
          class="array-item"
        >
          <div class="node-header" @click="isComplexTypeNode(item) ? toggleArrayItemExpand(item) : null">
            <!-- 展开/折叠图标 -->
            <div v-if="isComplexTypeNode(item)" class="node-toggle">
              <el-icon v-if="item.expanded"><CaretBottom /></el-icon>
              <el-icon v-else><CaretRight /></el-icon>
            </div>
            <div v-else class="node-toggle-placeholder"></div>
            
            <!-- 节点信息 -->
            <div class="node-info">
              <el-checkbox v-model="item.checked" @click.stop @change="(val) => handleArrayItemCheckChange(val, item)"></el-checkbox>
              <span class="node-name array-index">{{ index }}:</span>
              <el-tag 
                size="small" 
                :type="getTypeTagType(item.type)"
                class="node-type"
              >
                {{ getTypeDisplayName(item.type) }}
              </el-tag>
            </div>
            
            <!-- 值编辑区域 -->
            <div v-if="!isComplexTypeNode(item)" class="node-value" @click.stop>
              <component-by-type 
                :node="item"
                @update:value="handleChildValueChange"
                @checked:change="(path, checked, node) => emit('checked:change', path, checked, node)"
              />
            </div>
            
            <!-- 复杂类型操作按钮 -->
            <div v-else class="node-value complex-actions" @click.stop>
              <!-- 子集合类型 -->
              <template v-if="item.type === 'array' || isListTypeNode(item) || isSetTypeNode(item)">
                <span class="empty-tip" v-if="!item.value || item.value.length === 0">点击添加子元素</span>
                <div class="array-summary" v-else>
                  <el-tag size="small" type="info">{{ item.value.length }}个元素</el-tag>
                </div>
              </template>
            </div>
            
            <!-- 删除按钮 -->
            <el-button 
              type="danger" 
              size="small" 
              plain
              circle
              @click.stop="removeArrayItem(index)"
              class="delete-button"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          
          <!-- 复杂类型数组项展开内容 -->
          <div v-if="isComplexTypeNode(item) && item.expanded" class="array-item-expanded">
          <node-tree-item
            :node="item"
            :level="level + 1"
            @update:value="handleChildValueChange"
            @checked:change="(path, checked, node) => emit('checked:change', path, checked, node)"
          />
        </div>
        </div>
      </template>
      
      <!-- 对象子属性展示 -->
      <template v-else>
        <node-tree-item
          v-for="child in node.children"
          :key="child.id"
          :node="child"
          :level="level + 1"
          @update:value="handleChildValueChange"
          @checked:change="(path, checked, node) => emit('checked:change', path, checked, node)"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, toRaw, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  CaretBottom, 
  CaretRight, 
  Plus, 
  Minus, 
  Delete, 
  InfoFilled
} from '@element-plus/icons-vue';
import ComponentByType from './ComponentByType.vue';

// 组件名称定义
defineOptions({
  name: 'NodeTreeItem'
});

// 定义节点类型接口
interface SchemaNodeType {
  id: string;
  name: string;
  path: string;
  type: string;
  value: any;
  description?: string;
  expanded: boolean;
  checked: boolean;
  children: SchemaNodeType[];
  enums?: string[];
  typeBuilderName?: string;
}

// Map条目接口
interface MapEntry {
  key: string;
  valueNode: SchemaNodeType;
}

// 属性定义
const props = defineProps<{
  node: SchemaNodeType;
  level: number;
}>();

// 事件定义
const emit = defineEmits<{
  (e: 'update:value', path: string, value: any): void;
  (e: 'checked:change', path: string, checked: boolean, node: SchemaNodeType): void;
}>();

// 数组项
const arrayItems = ref<SchemaNodeType[]>([]);

// Map条目
const mapEntries = ref<MapEntry[]>([]);

// 计算属性
const isComplexType = computed(() => {
  const result = isComplexTypeNode(props.node);
  console.log(`节点 ${props.node.name} 类型为 ${props.node.type}, isComplexType = ${result}`);
  return result;
});

const isNumericType = computed(() => {
  const type = props.node.type.toLowerCase();
  return ['number', 'int', 'integer', 'long', 'double', 'float'].includes(type);
});

const isDateType = computed(() => {
  const type = props.node.type.toLowerCase();
  return type.includes('date') || type.includes('time');
});

const isMapType = computed(() => {
  const result = isRealMapType(props.node.type);
  console.log(`节点 ${props.node.name} 类型为 ${props.node.type}, isMapType = ${result}`);
  return result;
});

const isListType = computed(() => {
  const result = isRealListType(props.node.type);
  console.log(`节点 ${props.node.name} 类型为 ${props.node.type}, isListType = ${result}`);
  return result;
});

const isSetType = computed(() => {
  const result = isRealSetType(props.node.type);
  console.log(`节点 ${props.node.name} 类型为 ${props.node.type}, isSetType = ${result}`);
  return result;
});

// 根据类型获取日期选择器类型
const getDatePickerType = () => {
  const type = props.node.type.toLowerCase();
  if (type.includes('datetime')) return 'datetime';
  if (type.includes('date')) return 'date';
  if (type.includes('time')) return 'time';
  return 'datetime';
};

// 判断是否为List类型
const isRealListType = (type: string): boolean => {
  return type.includes('List<') || 
         type.startsWith('java.util.List') || 
         type.endsWith('List') || 
         type.endsWith('ArrayList') ||
         type.toLowerCase() === 'list';
};

// 判断是否为Set类型
const isRealSetType = (type: string): boolean => {
  return type.includes('Set<') || 
         type.startsWith('java.util.Set') || 
         type.endsWith('Set') || 
         type.endsWith('HashSet') ||
         type.toLowerCase() === 'set';
};

// 判断是否为Map类型
const isRealMapType = (type: string): boolean => {
  if (!type) return false;
  
  // 测试各种可能的Map类型表示形式
  const mapPatterns = [
    /Map<.+>/i,                       // 泛型Map
    /^java\.util\.Map/i,              // Java完全限定名
    /Map$/i,                          // 以Map结尾
    /HashMap$/i,                      // 以HashMap结尾
    /LinkedHashMap$/i,                // 以LinkedHashMap结尾
    /TreeMap$/i,                      // 以TreeMap结尾
    /^Map$/i,                         // 精确匹配Map
    /^map$/i                          // 小写map
  ];
  
  // 检查是否匹配任何一种模式
  const isMap = mapPatterns.some(pattern => pattern.test(type));
  console.log(`检查是否为Map类型: ${type} => ${isMap}`);
  return isMap;
};

// 获取类型标签类型
const getTypeTagType = (type: string): string => {
  // 首先检查特殊集合类型
  if (isRealListType(type) || isRealSetType(type)) {
    return 'danger';  // 集合类型使用红色标签
  }
  
  if (isRealMapType(type)) {
    return 'primary'; // Map类型使用蓝色标签
  }
  
  // 基本类型映射
  const typeMap: Record<string, string> = {
    'string': 'info',
    'number': 'success',
    'int': 'success',
    'integer': 'success',
    'long': 'success',
    'double': 'success',
    'float': 'success',
    'boolean': 'warning',
    'object': 'primary',
    'array': 'danger',
    'enum': 'warning',
    'date': 'info',
    'datetime': 'info',
    'time': 'info'
  };
  
  // 将类型转为小写以匹配
  const lowerType = type.toLowerCase();
  
  // 返回匹配的标签类型或默认为'info'
  return typeMap[lowerType] || 'info';
};

// 获取类型显示名称
const getTypeDisplayName = (type: string): string => {
  if (!type) return '未知类型';
  
  // 处理泛型类型
  if (type.includes('<')) {
    const baseType = type.split('<')[0];
    const genericType = type.match(/<(.+)>/)?.[1] || '';
    
    // 显示简化的泛型名称
    let simpleBaseType = baseType;
    if (baseType.includes('.')) {
      simpleBaseType = baseType.split('.').pop() || '';
    }
    
    let simpleGenericType = genericType;
    if (genericType.includes('.')) {
      simpleGenericType = genericType.split('.').pop() || '';
    }
    
    // 如果是Map类型，处理键值类型
    if (isRealMapType(simpleBaseType) && genericType.includes(',')) {
      const keyValueTypes = genericType.split(',').map(t => t.trim());
      const keyType = keyValueTypes[0].split('.').pop() || keyValueTypes[0];
      const valueType = keyValueTypes[1].split('.').pop() || keyValueTypes[1];
      return `Map<${keyType},${valueType}>`;
    }
    
    // 如果是List或Set类型，保留原始标识
    if (isRealListType(simpleBaseType)) {
      return `List<${simpleGenericType}>`;
    }
    
    if (isRealSetType(simpleBaseType)) {
      return `Set<${simpleGenericType}>`;
    }
    
    return `${simpleBaseType}<${simpleGenericType}>`;
  }
  
  // 简化Java类型名称
  if (type.includes('.')) {
    const parts = type.split('.');
    const simpleName = parts[parts.length - 1];
    
    // 检查简化名称是否为List或Set类型
    if (isRealListType(simpleName)) {
      return 'List';
    }
    
    if (isRealSetType(simpleName)) {
      return 'Set';
    }
    
    if (isRealMapType(simpleName)) {
      return 'Map';
    }
    
    return simpleName;
  }
  
  // 检查原始类型名是否为List或Set类型
  if (isRealListType(type)) {
    return 'List';
  }
  
  if (isRealSetType(type)) {
    return 'Set';
  }
  
  if (isRealMapType(type)) {
    return 'Map';
  }
  
  // 基本类型映射
  const typeMap: Record<string, string> = {
    'string': '字符串',
    'number': '数字',
    'int': '整数',
    'integer': '整数',
    'long': '长整数',
    'double': '浮点数',
    'float': '浮点数',
    'boolean': '布尔值',
    'object': '对象',
    'array': '数组',
    'enum': '枚举',
    'date': '日期',
    'datetime': '日期时间',
    'time': '时间'
  };
  
  return typeMap[type.toLowerCase()] || type;
};

// 切换展开状态
const toggleExpand = () => {
  if (isComplexType.value) {
    props.node.expanded = !props.node.expanded;
  }
};

// 处理值变更
const handleValueChange = () => {
  emit('update:value', props.node.path, props.node.value);
};

// 处理子节点值变更
const handleChildValueChange = (path: string, value: any) => {
  emit('update:value', path, value);
};

// 创建数组项模板
const createArrayItemTemplate = (): SchemaNodeType => {
  // 如果有子节点模板，使用它
  if (props.node.children && props.node.children.length > 0) {
    const template = props.node.children[0];
    
    // 深拷贝模板以创建新项
    return JSON.parse(JSON.stringify(template));
  }
  
  // 创建默认模板
  return {
    id: `item_${Date.now()}`,
    name: '元素',
    path: `${props.node.path}[${arrayItems.value.length}]`,
    type: 'string',
    value: '',
    expanded: true,
    checked: true,
    children: []
  };
};

// 添加数组项
const addArrayItem = (event: Event) => {
  event.stopPropagation();
  
  const newItem = createArrayItemTemplate();
  
  // 更新路径
  newItem.path = `${props.node.path}[${arrayItems.value.length}]`;
  
  // 确保父节点展开
  props.node.expanded = true;
  
  // 添加到数组
  arrayItems.value.push(newItem);
  
  // 更新原始节点值数组
  updateNodeValue();
};

// 删除数组项
const removeArrayItem = (index: number) => {
  arrayItems.value.splice(index, 1);
  
  // 更新所有数组项的路径
  arrayItems.value.forEach((item, idx) => {
    item.path = `${props.node.path}[${idx}]`;
  });
  
  // 更新原始节点值数组
  updateNodeValue();
};

// 更新节点值
const updateNodeValue = () => {
  // 提取每个数组项的值构建数组
  const values = arrayItems.value
    .filter(item => item.checked) // 只处理选中的数组项
    .map(item => {
    if (item.type === 'object' || item.type === 'array') {
      // 复杂类型使用空对象或空数组
      return item.type === 'object' ? {} : [];
    }
    return item.value;
  });
  
  // 更新节点值 - 但不修改原始数组项
  props.node.value = values;
  
  // 通知父组件
  emit('update:value', props.node.path, values);
};

// 创建Map值模板
const createMapValueTemplate = (): SchemaNodeType => {
  // 如果有值模板子节点，使用它
  if (props.node.children && props.node.children.length > 1) {
    const valueTemplate = props.node.children[1]; // 通常第二个子节点是值模板
    
    // 深拷贝模板以创建新项
    return JSON.parse(JSON.stringify(valueTemplate));
  }
  
  // 创建默认模板
  return {
    id: `map_value_${Date.now()}`,
    name: '值',
    path: `${props.node.path}.value`,
    type: 'string',
    value: '',
    expanded: true,
    checked: true,
    children: []
  };
};

// 添加Map条目
const addMapEntry = (event: Event) => {
  event.stopPropagation();
  
  const newValueNode = createMapValueTemplate();
  
  // 生成唯一键名
  const defaultKey = `key_${mapEntries.value.length + 1}`;
  
  // 更新值节点路径
  newValueNode.path = `${props.node.path}["${defaultKey}"]`;
  
  // 确保父节点展开
  props.node.expanded = true;
  
  // 添加到Map条目
  mapEntries.value.push({
    key: defaultKey,
    valueNode: newValueNode
  });
  
  // 更新原始节点值对象
  updateMapValue();
};

// 删除Map条目
const removeMapEntry = (index: number) => {
  // 直接物理删除Map条目
  mapEntries.value.splice(index, 1);
  
  // 更新原始节点值对象
  updateMapValue();
};

// 处理Map值变更
const handleMapValueChange = () => {
  updateMapValue();
};

// 更新Map值
const updateMapValue = () => {
  // 构建Map对象
  const mapObject: Record<string, any> = {};
  
  mapEntries.value
    .filter(entry => entry.valueNode.checked) // 只处理选中的Map条目
    .forEach(entry => {
      if (entry.key && entry.key.trim()) {
        if (entry.valueNode.type === 'object' || entry.valueNode.type === 'array') {
          // 复杂类型使用空对象或空数组
          mapObject[entry.key] = entry.valueNode.type === 'object' ? {} : [];
        } else {
          mapObject[entry.key] = entry.valueNode.value;
        }
      }
    });
  
  // 更新节点值 - 但不修改原始Map条目
  props.node.value = mapObject;
  
  // 通知父组件
  emit('update:value', props.node.path, mapObject);
};

// 更新所有Map条目
const updateMapEntries = () => {
  // 更新所有条目的路径
  mapEntries.value.forEach(entry => {
    entry.valueNode.path = `${props.node.path}["${entry.key}"]`;
  });
  
  // 更新Map值
  updateMapValue();
};

// 判断节点是否为复杂类型
const isComplexTypeNode = (node: SchemaNodeType): boolean => {
  const type = node.type;
  
  // 基本复杂类型
  if (type === 'object' || type === 'array') {
    return true;
  }
  
  // Map类型检查
  if (isRealMapType(type)) {
    return true;
  }
  
  // 集合类型检查
  if (isRealListType(type) || isRealSetType(type)) {
    return true;
  }
  
  // 检查类型构建器
  if (node.typeBuilderName && 
     (node.typeBuilderName.toLowerCase().includes('maptypebuilder') ||
      node.typeBuilderName.toLowerCase().includes('listtypebuilder') ||
      node.typeBuilderName.toLowerCase().includes('settypebuilder') ||
      node.typeBuilderName.toLowerCase().includes('collectiontypebuilder'))) {
    return true;
  }
  
  return false;
};

// 判断节点是否为List类型
const isListTypeNode = (node: SchemaNodeType): boolean => {
  return isRealListType(node.type);
};

// 判断节点是否为Set类型
const isSetTypeNode = (node: SchemaNodeType): boolean => {
  return isRealSetType(node.type);
};

// 切换数组项展开/折叠
const toggleArrayItemExpand = (item: SchemaNodeType) => {
  item.expanded = !item.expanded;
};

// 切换Map值展开/折叠
const toggleMapValueExpand = (node: SchemaNodeType) => {
  node.expanded = !node.expanded;
};

// 组件挂载时初始化
onMounted(() => {
  console.log('组件挂载，节点类型:', props.node.type);
  
  // 判断是集合类型（数组、List、Set）
  if (props.node.type === 'array' || isListType.value || isSetType.value) {
    console.log('初始化集合类型节点:', props.node.name);
    // 如果节点值已经是数组，根据数组长度创建对应的数组项
    if (Array.isArray(props.node.value) && props.node.value.length > 0) {
      props.node.value.forEach((value, index) => {
        const template = createArrayItemTemplate();
        template.path = `${props.node.path}[${index}]`;
        template.value = value;
        arrayItems.value.push(template);
      });
    }
  } 
  // 判断是Map类型
  else if (isMapType.value) {
    console.log('初始化Map类型节点:', props.node.name, props.node.type);
    // 检查是否有子节点模板（必须有键和值模板）
    const hasTemplates = props.node.children && props.node.children.length >= 2;
    if (hasTemplates) {
      console.log('Map节点有子节点模板:', props.node.children);
    }
    
    // 如果节点值是对象，将其转换为Map条目
    if (typeof props.node.value === 'object' && props.node.value !== null) {
      Object.entries(props.node.value).forEach(([key, value]) => {
        const valueTemplate = createMapValueTemplate();
        valueTemplate.path = `${props.node.path}["${key}"]`;
        valueTemplate.value = value;
        
        mapEntries.value.push({
          key,
          valueNode: valueTemplate
        });
      });
    }
  }
  
  // 监听全局全选/全不选事件
  document.addEventListener('updateArrayItems', ((event: CustomEvent) => {
    const detail = event.detail || {};
    const checked = detail.checked !== undefined ? detail.checked : true;
    
    console.log('收到全局更新事件:', checked, props.node.name, props.node.type);
    
    // 处理集合元素
    if (props.node.type === 'array' || isListType.value || isSetType.value) {
      // 更新所有数组项的勾选状态
      arrayItems.value.forEach(item => {
        item.checked = checked;
        
        // 递归处理子项
        if (item.children && item.children.length > 0) {
          const setChildChecked = (node: SchemaNodeType) => {
            node.checked = checked;
            if (node.children && node.children.length > 0) {
              node.children.forEach(setChildChecked);
            }
          };
          item.children.forEach(setChildChecked);
        }
      });
      
      // 更新数组值
      updateNodeValue();
    }
    // 处理Map元素
    else if (isMapType.value) {
      // 更新所有Map条目的勾选状态
      mapEntries.value.forEach(entry => {
        entry.valueNode.checked = checked;
        
        // 递归处理子项
        if (entry.valueNode.children && entry.valueNode.children.length > 0) {
          const setChildChecked = (node: SchemaNodeType) => {
            node.checked = checked;
            if (node.children && node.children.length > 0) {
              node.children.forEach(setChildChecked);
            }
          };
          entry.valueNode.children.forEach(setChildChecked);
        }
      });
      
      // 更新Map值
      updateMapValue();
    }
  }) as EventListener);
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  document.removeEventListener('updateArrayItems', null as any);
});

// 处理勾选状态变化
const handleCheckChange = (value: boolean) => {
  console.log('节点勾选状态变化:', props.node.name, value);
  
  // 递归设置所有子节点的勾选状态
  setChildrenCheckedState(value);
  
  // 更新节点值，但不删除元素
  handleValueChange();
  
  // 通知父组件勾选状态变化
  emit('checked:change', props.node.path, value, props.node);
};

// 递归设置子节点的勾选状态
const setChildrenCheckedState = (checked: boolean) => {
  if (props.node.children && props.node.children.length > 0) {
    props.node.children.forEach(child => {
      child.checked = checked;
      // 递归设置子节点
      if (child.children && child.children.length > 0) {
        const setChildChecked = (node: SchemaNodeType) => {
          node.checked = checked;
          if (node.children && node.children.length > 0) {
            node.children.forEach(setChildChecked);
          }
        };
        child.children.forEach(setChildChecked);
      }
    });
  }
  
  // 处理数组项
  arrayItems.value.forEach(item => {
    item.checked = checked;
    // 递归设置
    if (item.children && item.children.length > 0) {
      const setChildChecked = (node: SchemaNodeType) => {
        node.checked = checked;
        if (node.children && node.children.length > 0) {
          node.children.forEach(setChildChecked);
        }
      };
      item.children.forEach(setChildChecked);
    }
  });
  
  // 处理Map条目
  mapEntries.value.forEach(entry => {
    entry.valueNode.checked = checked;
    // 递归设置
    if (entry.valueNode.children && entry.valueNode.children.length > 0) {
      const setChildChecked = (node: SchemaNodeType) => {
        node.checked = checked;
        if (node.children && node.children.length > 0) {
          node.children.forEach(setChildChecked);
        }
      };
      entry.valueNode.children.forEach(setChildChecked);
    }
  });
};

// 处理Map条目勾选状态变化
const handleMapEntryCheckChange = (value: boolean, entry: MapEntry) => {
  console.log('Map条目勾选状态变化:', entry.key, value);
  
  // 递归设置子节点勾选状态
  if (entry.valueNode.children && entry.valueNode.children.length > 0) {
    const setChildChecked = (node: SchemaNodeType) => {
      node.checked = value;
      if (node.children && node.children.length > 0) {
        node.children.forEach(setChildChecked);
      }
    };
    entry.valueNode.children.forEach(setChildChecked);
  }
  
  // 更新Map值，但不删除元素
  updateMapValue();
  
  // 通知父组件勾选状态变化
  emit('checked:change', entry.valueNode.path, value, entry.valueNode);
};

// 处理数组项勾选状态变化
const handleArrayItemCheckChange = (value: boolean, item: SchemaNodeType) => {
  console.log('数组项勾选状态变化:', item.path, value);
  
  // 递归设置子节点勾选状态
  if (item.children && item.children.length > 0) {
    const setChildChecked = (node: SchemaNodeType) => {
      node.checked = value;
      if (node.children && node.children.length > 0) {
        node.children.forEach(setChildChecked);
      }
    };
    item.children.forEach(setChildChecked);
  }
  
  // 更新节点值，但不删除元素
  updateNodeValue();
  
  // 通知父组件勾选状态变化
  emit('checked:change', item.path, value, item);
};

// 监听节点值变化
watch(() => props.node.value, (newVal) => {
  if (isMapType.value && typeof newVal === 'object' && newVal !== null) {
    // Map类型值变化，更新条目
    const currentKeys = mapEntries.value.map(entry => entry.key);
    const newKeys = Object.keys(newVal);
    
    // 添加新键
    newKeys.forEach(key => {
      if (!currentKeys.includes(key)) {
        const valueTemplate = createMapValueTemplate();
        valueTemplate.path = `${props.node.path}["${key}"]`;
        valueTemplate.value = newVal[key];
        
        mapEntries.value.push({
          key,
          valueNode: valueTemplate
        });
      }
    });
    
    // 更新现有值
    mapEntries.value.forEach(entry => {
      if (newKeys.includes(entry.key)) {
        entry.valueNode.value = newVal[entry.key];
      }
    });
    
    // 注意：不要删除已有的条目，只更新值
    // 让勾选框控制在JSON中是否包含该条目
    // 原先的代码会物理删除不存在的键：
    // mapEntries.value = mapEntries.value.filter(entry => newKeys.includes(entry.key));
  }
}, { deep: true });
</script>

<style lang="scss" scoped>
.node-tree-item {
  margin-bottom: 6px;
  
  .node-header {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    background-color: transparent;
    cursor: pointer;
    transition: background-color 0.2s ease;
    user-select: none;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    .node-toggle,
    .node-toggle-placeholder {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;
    }
    
    .node-info {
      flex: 1;
      display: flex;
      align-items: center;
      user-select: none;
      
      .node-name {
        margin: 0 8px;
        user-select: none;
        
        &.array-index {
          font-weight: 500;
          color: #606266;
        }
      }
      
      .node-type {
        margin-right: 8px;
        user-select: none;
      }
      
      .node-description {
        color: #909399;
      }
      
      .key-input {
        width: 180px;
        max-width: 180px;
        margin-right: 4px;
      }
    }
    
    .node-value {
      width: 300px;
      display: flex;
      justify-content: space-between;
      
      .el-input,
      .el-input-number,
      .el-select {
        width: 100%;
    }
    
      &.complex-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
        gap: 8px;
      }
      
      .empty-tip {
        color: #909399;
        font-size: 12px;
      }
      
      .boolean-wrapper {
        width: 100%;
        display: flex;
        justify-content: flex-end;
      }
    }
    
    .delete-button {
      margin-left: 8px;
    }
  }
  
  .node-children {
    margin-top: 4px;
    
    .array-item {
      margin: 8px 0;
      margin-left: 20px;
      border-left: none;
      padding-left: 0;
      
      .array-item-expanded {
        margin-top: 4px;
        padding-left: 20px;
      }
    }
    
    .map-entry {
      margin: 8px 0;
      border-left: none;
      padding-left: 0;
      margin-left: 20px;
    }
    
    .map-entry-header {
      display: none !important;
    }
    
    .map-entry-key, 
    .map-entry-value {
      display: none !important;
    }
  }
}

.map-value-expanded {
  margin-top: 4px;
  padding-left: 20px;
}

// 为有删除按钮的节点添加特殊处理
.array-item .node-value,
.map-entry .node-value {
  width: 268px !important;  // 更精确的宽度计算
}

// 键输入框样式
.key-input {
  width: 180px !important;
  max-width: 180px !important;
}
</style> 