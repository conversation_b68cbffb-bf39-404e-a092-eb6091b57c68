<template>
  <div>
    <!-- 触发查看的元素 -->
    <div class="content-trigger" @click="showDialog = true">
      <slot></slot>
      <el-icon class="expand-icon" v-if="!asTooltip"><Expand /></el-icon>
    </div>
    
    <!-- 弹窗形式 -->
    <el-dialog
      v-if="!asTooltip"
      v-model="showDialog"
      :title="title"
      :width="width"
      append-to-body
      destroy-on-close
    >
      <div class="content-viewer-dialog-body">
        <div :class="['content-wrapper', isJson ? 'code-content' : 'text-content']">
          <pre v-if="isJson"><code>{{ formattedContent }}</code></pre>
          <template v-else>{{ content }}</template>
        </div>
      </div>
      <template #footer>
        <el-button type="primary" @click="copyContent">复制内容</el-button>
        <el-button @click="showDialog = false">关闭</el-button>
      </template>
    </el-dialog>
    
    <!-- tooltip形式 - 修改为最新的用法 -->
    <el-popover
      v-if="asTooltip"
      trigger="hover"
      :width="400"
      popper-class="content-viewer-tooltip"
    >
      <template #default>
        <div :class="['content-wrapper', isJson ? 'code-content code-content-tooltip' : 'text-content-tooltip']">
          <pre v-if="isJson"><code>{{ formattedContent }}</code></pre>
          <template v-else>{{ formattedContent }}</template>
        </div>
      </template>
      <template #reference>
        <span class="tooltip-trigger">
          <slot></slot>
        </span>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import { Expand } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { showFormat } from '../../utils/orchestrationUtils';

const props = defineProps({
  content: {
    type: [String, Object, Array, Number, Boolean],
    required: true
  },
  title: {
    type: String,
    default: '内容详情'
  },
  width: {
    type: String,
    default: '60%'
  },
  asTooltip: {
    type: Boolean,
    default: false
  },
  isJson: {
    type: Boolean,
    default: true
  }
});

const showDialog = ref(false);

// 格式化内容
const formattedContent = computed(() => {
  return showFormat(props.content);
});

// 复制内容到剪贴板
const copyContent = () => {
  const textToCopy = showFormat(props.content);
  
  try {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(textToCopy)
        .then(() => {
          ElMessage.success('内容已复制到剪贴板');
        })
        .catch(() => {
          fallbackCopyToClipboard(textToCopy);
        });
    } else {
      fallbackCopyToClipboard(textToCopy);
    }
  } catch (e) {
    ElMessage.error('复制失败，请手动复制');
  }
};

// 兼容性复制方法
const fallbackCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  
  textArea.style.position = 'fixed';
  textArea.style.top = '0';
  textArea.style.left = '0';
  textArea.style.width = '2em';
  textArea.style.height = '2em';
  textArea.style.padding = '0';
  textArea.style.border = 'none';
  textArea.style.outline = 'none';
  textArea.style.boxShadow = 'none';
  textArea.style.background = 'transparent';
  
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  
  try {
    const successful = document.execCommand('copy');
    if (successful) {
      ElMessage.success('内容已复制到剪贴板');
    } else {
      ElMessage.error('复制失败，请手动复制');
    }
  } catch (err) {
    ElMessage.error('复制失败，请手动复制');
  }
  
  document.body.removeChild(textArea);
};
</script>

<style lang="scss" scoped>
.content-trigger {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  width: 100%;
  
  &:hover {
    .expand-icon {
      visibility: visible;
      opacity: 1;
    }
  }
  
  .expand-icon {
    margin-left: 4px;
    font-size: 14px;
    color: #409eff;
    transition: all 0.2s;
    visibility: hidden;
    opacity: 0;
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.content-viewer-dialog-body {
  max-height: 70vh;
  overflow: auto;
  padding: 0 10px;
  
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
}

.content-wrapper {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px;
  overflow: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
}

.code-content {
  font-family: Consolas, Monaco, monospace;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  code {
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
    color: #333;
  }
  
  &.code-content-tooltip {
    max-height: 400px;
    max-width: 400px;
    background-color: transparent;
    
    code {
      color: #ffffff;
    }
  }
}

.text-content {
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.5;
}

.text-content-tooltip {
  max-height: 400px;
  max-width: 400px;
  background-color: transparent;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.5;
  color: #ffffff;
}

:deep(.content-viewer-tooltip) {
  font-size: 12px;
  max-width: 400px !important;
  
  .el-tooltip__content {
    word-break: break-all;
    white-space: pre-wrap;
  }
}

.tooltip-trigger {
  display: inline-block;
  width: 100%;
  cursor: pointer;
}
</style>

<script lang="ts">
export default {
  name: 'ContentViewer'
};
</script> 