<template>
  <div class="json-field-item" :class="`level-${level}`">
    <!-- 对象类型 -->
    <template v-if="field.type === 'object'">
      <div class="field-header" @click="toggleExpand">
        <div class="field-row">
          <div class="field-name" v-if="!isArrayItem">
            <el-input 
              v-model="field.key" 
              placeholder="属性名" 
              :readonly="props.readonlyKey && props.level === 0"
              @click.stop 
            />
          </div>
          <div class="field-index" v-else>
            {{ arrayIndex }}
          </div>
          <div class="field-type">
            <el-select v-model="field.type" placeholder="类型" @click.stop>
              <el-option label="字符串" value="string" />
              <el-option label="脚本" value="script" />
              <el-option label="数字" value="number" />
              <el-option label="布尔值" value="boolean" />
              <el-option label="对象" value="object" />
              <el-option label="数组" value="array" />
              <el-option label="空值" value="null" />
            </el-select>
          </div>
          <div class="field-value">
            <div class="complex-field">
              <div class="complex-field-display">
                <el-icon>
                  <component :is="field.expanded ? ArrowDown : ArrowRight" />
                </el-icon>
                <span class="field-type-label">对象</span>
                <span class="field-count">{{ Object.keys(field.objectValue || {}).length }} 个属性</span>
              </div>
              <div>
                <el-button type="primary" text @click.stop="toggleExpand">
                  {{ field.expanded ? '收起' : '展开' }}
                </el-button>
                <el-button type="primary" text @click.stop="addObjectProperty">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <div class="field-actions">
            <el-button type="danger" text @click.stop="$emit('remove')" class="delete-button">
              <el-icon><Minus /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 对象内容折叠区域 - 仅在展开时渲染内容 -->
      <div v-if="field.expanded" class="object-content" :class="`content-level-${level}`">
        <template v-if="isContentLoaded">
        <JsonFieldRecursive
          v-for="(subField, propIndex) in objectToArray(field.objectValue)"
          :key="propIndex"
          :field="subField"
          :level="level + 1"
          :readonly-key="false"
          @remove="removeObjectProperty(propIndex)"
          @edit-object="$emit('edit-object', $event)"
          @edit-array="$emit('edit-array', $event)"
        />
        </template>
        <div v-else class="content-loading">
          <el-skeleton :rows="3" animated />
        </div>
      </div>
    </template>
    
    <!-- 数组类型 -->
    <template v-else-if="field.type === 'array'">
      <div class="field-header" @click="toggleExpand">
        <div class="field-row">
          <div class="field-name" v-if="!isArrayItem">
            <el-input 
              v-model="field.key" 
              placeholder="属性名" 
              :readonly="props.readonlyKey && props.level === 0"
              @click.stop 
            />
          </div>
          <div class="field-index" v-else>
            {{ arrayIndex }}
          </div>
          <div class="field-type">
            <el-select v-model="field.type" placeholder="类型" @click.stop>
              <el-option label="字符串" value="string" />
              <el-option label="脚本" value="script" />
              <el-option label="数字" value="number" />
              <el-option label="布尔值" value="boolean" />
              <el-option label="对象" value="object" />
              <el-option label="数组" value="array" />
              <el-option label="空值" value="null" />
            </el-select>
          </div>
          <div class="field-value">
            <div class="complex-field">
              <div class="complex-field-display">
                <el-icon>
                  <component :is="field.expanded ? ArrowDown : ArrowRight" />
                </el-icon>
                <span class="field-type-label">数组</span>
                <span class="field-count">{{ (field.arrayValue || []).length }} 个元素</span>
              </div>
              <div>
                <el-button type="primary" text @click.stop="toggleExpand">
                  {{ field.expanded ? '收起' : '展开' }}
                </el-button>
                <el-button type="primary" text @click.stop="addArrayItem">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <div class="field-actions">
            <el-button type="danger" text @click.stop="$emit('remove')" class="delete-button">
              <el-icon><Minus /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 数组内容折叠区域 - 仅在展开时渲染内容 -->
      <div v-if="field.expanded" class="array-content" :class="`content-level-${level}`">
        <template v-if="isContentLoaded">
        <JsonFieldRecursive
          v-for="(item, itemIndex) in field.arrayValue"
          :key="itemIndex"
          :field="item"
          :level="level + 1"
          :is-array-item="true"
          :array-index="itemIndex"
          :readonly-key="false"
          @remove="removeArrayItem(itemIndex)"
          @edit-object="$emit('edit-object', $event)"
          @edit-array="$emit('edit-array', $event)"
        />
        </template>
        <div v-else class="content-loading">
          <el-skeleton :rows="3" animated />
        </div>
      </div>
    </template>
    
    <!-- 其他简单类型 -->
    <template v-else>
      <div class="field-row simple-field">
        <div class="field-name" v-if="!isArrayItem">
          <el-input 
            v-model="field.key" 
            placeholder="属性名" 
            :readonly="props.readonlyKey && props.level === 0"
          />
        </div>
        <div class="field-index" v-else>
          {{ arrayIndex }}
        </div>
        <div class="field-type">
          <el-select v-model="field.type" placeholder="类型">
            <el-option label="字符串" value="string" />
            <el-option label="脚本" value="script" />
            <el-option label="数字" value="number" />
            <el-option label="布尔值" value="boolean" />
            <el-option label="对象" value="object" />
            <el-option label="数组" value="array" />
            <el-option label="空值" value="null" />
          </el-select>
        </div>
        <div class="field-value">
          <!-- 根据类型显示不同的值编辑器 -->
          <template v-if="field.type === 'string'">
            <el-input v-model="field.stringValue" placeholder="请输入字符串值" />
          </template>
          
          <!-- 脚本类型专用编辑器 -->
          <template v-else-if="field.type === 'script'">
            <div class="script-editor-container">
              <el-input
                v-model="scriptValue"
                type="textarea"
                :rows="4"
                placeholder="请输入脚本内容，支持换行和制表符"
                class="script-editor"
                @keydown.tab.prevent="handleTabKey"
                @keydown="handleScriptKeydown"
                @focus="handleScriptFocus"
                @blur="handleScriptBlur"
              />
              <div class="script-editor-actions">
                <el-button type="primary" size="small" @click="openScriptEditor">
                  <el-icon class="el-icon--left"><EditPen /></el-icon>
                  在大窗口中编辑
                </el-button>
              </div>
            </div>
            
            <!-- 脚本编辑卡片对话框 -->
            <el-dialog
              v-model="scriptDialogVisible"
              title="脚本编辑"
              width="80%"
              destroy-on-close
              class="script-editor-dialog"
            >
              <div class="vscode-style-editor">
                <div class="line-numbers">
                  <div v-for="i in getLineCount(tempScriptValue)" :key="i" class="line-number">{{ i }}</div>
                </div>
                <el-input
                  v-model="tempScriptValue"
                  type="textarea"
                  :rows="20"
                  placeholder="请输入脚本内容，支持换行和制表符"
                  class="dialog-script-editor"
                  @keydown.tab.prevent="handleDialogTabKey"
                  @input="updateLineNumbers"
                />
              </div>
              <template #footer>
                <span class="dialog-footer">
                  <el-button @click="scriptDialogVisible = false">取消</el-button>
                  <el-button type="primary" @click="confirmScriptEdit">确定</el-button>
                </span>
              </template>
            </el-dialog>
          </template>
          
          <template v-else-if="field.type === 'number'">
            <el-input-number v-model="field.numberValue" :controls="false" placeholder="请输入数字值" style="width: 100%;" />
          </template>
          
          <template v-else-if="field.type === 'boolean'">
            <el-select v-model="field.booleanValue" placeholder="选择布尔值" style="width: 100%;">
              <el-option label="true" :value="true" />
              <el-option label="false" :value="false" />
            </el-select>
          </template>
          
          <template v-else>
            <div class="null-field">
              <span>null</span>
            </div>
          </template>
        </div>
        <div class="field-actions">
          <el-button type="danger" text @click.stop="$emit('remove')" class="delete-button">
            <el-icon><Minus /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, withDefaults, computed, ref, watch, nextTick } from 'vue';
import { Delete, Plus, ArrowDown, ArrowRight, EditPen, Minus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

interface JsonField {
  key: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'null' | 'script';
  stringValue: string;
  numberValue: number | null;
  booleanValue: boolean;
  objectValue: Record<string, JsonField>;
  arrayValue: JsonField[];
  expanded?: boolean;
}

const props = withDefaults(defineProps<{
  field: JsonField;
  level?: number;
  isArrayItem?: boolean;
  arrayIndex?: number;
  readonlyKey?: boolean;
}>(), {
  level: 0,
  isArrayItem: false,
  arrayIndex: -1,
  readonlyKey: false
});

const emit = defineEmits<{
  (e: 'remove'): void;
  (e: 'edit-object', field: JsonField): void;
  (e: 'edit-array', field: JsonField): void;
}>();

// 用于延迟加载内容
const isContentLoaded = ref(false);

// 切换展开/折叠状态
const toggleExpand = () => {
  props.field.expanded = !props.field.expanded;
  
  // 当展开时，延迟加载内容以优化性能
  if (props.field.expanded) {
    isContentLoaded.value = false;
    setTimeout(() => {
      isContentLoaded.value = true;
    }, 100); // 短暂延迟，让UI先更新
  }
};

// 监听初始展开状态
watch(() => props.field.expanded, (newVal) => {
  if (newVal === true && !isContentLoaded.value) {
    setTimeout(() => {
      isContentLoaded.value = true;
    }, 100);
  }
}, { immediate: true });

// 添加对象属性
const addObjectProperty = () => {
  const newProperty: JsonField = {
    key: '',
    type: 'string',
    stringValue: '',
    numberValue: null,
    booleanValue: false,
    objectValue: {},
    arrayValue: [],
    expanded: false
  };
  
  // 由于Vue的响应式限制，需要使用临时key设置新属性
  const tempKey = `temp_${Date.now()}`;
  props.field.objectValue[tempKey] = newProperty;
};

// 移除对象属性
const removeObjectProperty = (index: number) => {
  const properties = objectToArray(props.field.objectValue);
  const property = properties[index];
  
  if (property) {
    // 删除属性并创建新对象触发响应式更新
    const newObjectValue: Record<string, JsonField> = {};
    let keyToRemove = '';
    
    // 找到要删除的key（可能是真实key或临时key）
    Object.entries(props.field.objectValue).forEach(([key, value], i) => {
      if (i === index) {
        keyToRemove = key;
      }
    });
    
    // 创建不包含要删除key的新对象
    if (keyToRemove) {
      Object.entries(props.field.objectValue).forEach(([key, value]) => {
        if (key !== keyToRemove) {
          newObjectValue[key] = value;
        }
      });
      props.field.objectValue = newObjectValue;
    }
  }
};

// 添加数组元素
const addArrayItem = () => {
  const newItem: JsonField = {
    key: props.field.arrayValue.length.toString(),
    type: 'string',
    stringValue: '',
    numberValue: null,
    booleanValue: false,
    objectValue: {},
    arrayValue: [],
    expanded: false
  };
  
  props.field.arrayValue.push(newItem);
};

// 移除数组元素
const removeArrayItem = (index: number) => {
  if (props.field.arrayValue && index >= 0 && index < props.field.arrayValue.length) {
    props.field.arrayValue.splice(index, 1);
    
    // 更新数组索引
    props.field.arrayValue.forEach((item, idx) => {
      item.key = idx.toString();
    });
  }
};

// 将对象转换为数组，以便渲染
const objectToArray = (obj: Record<string, JsonField>): JsonField[] => {
  return Object.values(obj);
};

// 根据层级计算缩进样式
const levelIndent = computed(() => {
  return {
    paddingLeft: props.level > 0 ? `${props.level * 20}px` : '0',
    borderLeft: props.level > 0 ? '2px dashed #e4e7ed' : 'none'
  };
});

// 脚本编辑器撤销/重做历史
const scriptHistory = ref<string[]>([]);
const scriptHistoryIndex = ref(-1);
const isUndoRedoAction = ref(false);

// 脚本值的处理
const scriptValue = computed({
  get() {
    // 将存储的脚本字符串中的转义字符还原为实际字符
    if (props.field.stringValue) {
      const value = props.field.stringValue
        .replace(/\\n/g, '\n')
        .replace(/\\t/g, '\t');
      
      // 初始化历史记录（仅在首次获取值时）
      if (scriptHistory.value.length === 0) {
        scriptHistory.value = [value];
        scriptHistoryIndex.value = 0;
      }
      
      return value;
    }
    return '';
  },
  set(value: string) {
    // 如果不是通过撤销/重做设置的值，则添加到历史记录
    if (!isUndoRedoAction.value) {
      // 如果当前不在历史记录的最后，则删除当前索引之后的所有记录
      if (scriptHistoryIndex.value < scriptHistory.value.length - 1) {
        scriptHistory.value = scriptHistory.value.slice(0, scriptHistoryIndex.value + 1);
      }
      
      // 添加新的历史记录（如果与当前不同）
      if (value !== scriptHistory.value[scriptHistoryIndex.value]) {
        scriptHistory.value.push(value);
        scriptHistoryIndex.value = scriptHistory.value.length - 1;
      }
    }
    
    // 重置操作标志
    isUndoRedoAction.value = false;
    
    // 将实际字符转换为转义字符后存储
    props.field.stringValue = value
      .replace(/\n/g, '\\n')
      .replace(/\t/g, '\\t');
  }
});

// Tab键处理函数，在脚本编辑器中支持Tab键输入
const handleTabKey = (event: KeyboardEvent) => {
  // 获取当前输入框元素
  const target = event.target as HTMLTextAreaElement;
  const start = target.selectionStart;
  const end = target.selectionEnd;
  
  // 在光标位置插入Tab字符
  const value = scriptValue.value;
  scriptValue.value = value.substring(0, start) + '\t' + value.substring(end);
  
  // 设置光标位置
  nextTick(() => {
    target.selectionStart = target.selectionEnd = start + 1;
  });
};

// 处理脚本编辑器的键盘事件
const handleScriptKeydown = (event: KeyboardEvent) => {
  // 处理撤销操作 (Ctrl+Z 或 Command+Z)
  if ((event.ctrlKey || event.metaKey) && event.key === 'z') {
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止浏览器默认撤销行为
    
    // 执行撤销操作
    if (scriptHistoryIndex.value > 0) {
      scriptHistoryIndex.value--;
      isUndoRedoAction.value = true;
      scriptValue.value = scriptHistory.value[scriptHistoryIndex.value];
    }
  }
  
  // 处理重做操作 (Ctrl+Y 或 Command+Shift+Z)
  if ((event.ctrlKey || event.metaKey) && 
      ((event.key === 'y') || (event.shiftKey && event.key === 'z'))) {
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止浏览器默认重做行为
    
    // 执行重做操作
    if (scriptHistoryIndex.value < scriptHistory.value.length - 1) {
      scriptHistoryIndex.value++;
      isUndoRedoAction.value = true;
      scriptValue.value = scriptHistory.value[scriptHistoryIndex.value];
    }
  }
};

// 处理脚本编辑器获得焦点
const handleScriptFocus = () => {
  // 如果历史记录为空，则初始化
  if (scriptHistory.value.length === 0) {
    const currentValue = props.field.stringValue
      .replace(/\\n/g, '\n')
      .replace(/\\t/g, '\t');
    
    scriptHistory.value = [currentValue];
    scriptHistoryIndex.value = 0;
  }
};

// 处理脚本编辑器失去焦点
const handleScriptBlur = () => {
  // 确保最后的值被添加到历史记录
  const currentValue = scriptValue.value;
  
  if (scriptHistory.value.length === 0 || 
      currentValue !== scriptHistory.value[scriptHistory.value.length - 1]) {
    scriptHistory.value.push(currentValue);
    scriptHistoryIndex.value = scriptHistory.value.length - 1;
  }
};

// 打开脚本编辑卡片对话框
const scriptDialogVisible = ref(false);
const tempScriptValue = ref('');

const openScriptEditor = () => {
  scriptDialogVisible.value = true;
  tempScriptValue.value = scriptValue.value;
};

const confirmScriptEdit = () => {
  scriptValue.value = tempScriptValue.value;
  scriptDialogVisible.value = false;
};

const handleDialogTabKey = (event: KeyboardEvent) => {
  // 获取当前输入框元素
  const target = event.target as HTMLTextAreaElement;
  const start = target.selectionStart;
  const end = target.selectionEnd;
  
  // 在光标位置插入Tab字符
  const value = tempScriptValue.value;
  tempScriptValue.value = value.substring(0, start) + '\t' + value.substring(end);
  
  // 设置光标位置
  nextTick(() => {
    target.selectionStart = target.selectionEnd = start + 1;
  });
};

// 计算行数
const getLineCount = (text: string): number => {
  return text ? text.split('\n').length : 1;
};

// 更新行号
const updateLineNumbers = () => {
  // 这个函数是为了在文本改变时触发行号重新计算
  // 实际工作由计算属性完成
};
</script>

<style scoped lang="scss">
$level-colors: (
  0: (#ffffff, #f2f6fc),
  1: (#f9fbff, #edf3ff),
  2: (#f5f9ff, #e6f0ff),
  3: (#f2f7ff, #e0ecff),
  4: (#eef5ff, #dae8ff),
  5: (#ebf3ff, #d4e4ff)
);

.json-field-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #ffffff;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  @for $i from 0 through 5 {
    &.level-#{$i} {
      background-color: nth(map-get($level-colors, $i), 1);
      border-color: darken(nth(map-get($level-colors, $i), 1), 5%);
      
      > .field-header {
        background-color: nth(map-get($level-colors, $i), 2);
        border-bottom-color: darken(nth(map-get($level-colors, $i), 2), 5%);
      }
      
      &:hover {
        border-color: darken(nth(map-get($level-colors, $i), 1), 10%);
      }
    }
  }
}

.field-header {
  // padding: 12px;
  cursor: pointer;
  background-color: #fafbfc;
  border-bottom: 1px solid #f0f0f0;
  
  &:hover {
    background-color: #f5f7fa;
  }
}

.field-row {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  
  &.simple-field {
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 4px;
  }
}

.field-name {
  min-width: 120px;
  max-width: 200px;
  
  :deep(.el-input.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    color: #606266;
    cursor: not-allowed;
  }
  
  :deep(.el-input__inner[readonly]) {
    background-color: #f8fafc;
    color: #606266;
    border-color: #d1d5db;
    cursor: default;
  }
}

.field-type {
  width: 150px;
  flex-shrink: 0;
}

.field-index {
  width: 50px;
  flex-shrink: 0;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 12px;
}

.field-value {
  flex: 1;
  min-width: 200px;
  
  .script-editor {
    :deep(.el-textarea__inner) {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
      line-height: 1.5;
      font-size: 14px;
      padding: 10px;
      background-color: #ffffff;
      border-color: #dcdfe6;
      white-space: pre;
      overflow-x: auto;
      tab-size: 4;
      
      &:focus {
        border-color: #409eff;
      }
    }
  }
}

.field-actions {
  width: 60px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

.delete-button {
  :deep(.el-icon) {
    font-weight: bold;
    font-size: 16px;
  }
}

.complex-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background-color: #eef1f6;
  }
}

.complex-field-display {
  display: flex;
  align-items: center;
  gap: 10px;
  
  .field-type-label {
    font-weight: 600;
  }
  
  .field-count {
    color: #909399;
    font-size: 12px;
    background-color: #ecf5ff;
    border-radius: 10px;
    padding: 0 8px;
  }
}

.null-field {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #909399;
  font-style: italic;
}

.content-loading {
  padding: 10px;
  margin: 5px 0;
}

.add-field-row {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.object-content,
.array-content {
  border-top: 1px solid #e4e7ed;
  padding: 15px;
  background-color: rgba(250, 252, 255, 0.6);
  
  @for $i from 0 through 5 {
    &.content-level-#{$i} {
      border-left: 3px solid nth(map-get($level-colors, $i), 2);
      margin-left: 10px;
      padding-left: 20px;
    }
  }
  
  &[style*="border-left"] {
    margin-left: 10px;
    padding-left: 20px;
    background-color: rgba(250, 252, 255, 0.8);
    border-left: 2px dashed #c0d7fd !important;
  }
}

.nested-level {
  margin-left: 20px;
  border-left: 2px dashed #e4e7ed;
}

// 每个层级使用不同的边框颜色
:deep(.json-field-item) {
  > .object-content, > .array-content {
    > .json-field-item {
      border-left-width: 2px;
    }
  }
}

.script-editor-container {
  position: relative;
}

.script-editor-actions {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.vscode-style-editor {
  display: flex;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  position: relative;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  line-height: 1.5;
  font-size: 14px;
  overflow: hidden;
  height: 100%;
}

.line-numbers {
  background-color: #f5f7fa;
  padding: 10px 8px;
  border-right: 1px solid #e4e7ed;
  text-align: right;
  user-select: none;
  min-width: 40px;
  overflow: hidden;
}

.line-number {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
}

.dialog-script-editor {
  flex: 1;
  
  :deep(.el-textarea__inner) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    line-height: 1.5;
    font-size: 14px;
    padding: 10px;
    background-color: #ffffff;
    border: none;
    border-radius: 0;
    box-shadow: none;
    white-space: pre;
    overflow-x: auto;
    tab-size: 4;
    height: 100%;
    
    &:focus {
      border-color: transparent;
      box-shadow: none;
    }
  }
}

.script-editor-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }
}

.dialog-footer {
  text-align: right;
}
</style> 