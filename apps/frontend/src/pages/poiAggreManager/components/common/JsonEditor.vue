<template>
  <div>
    <!-- JSON可视化编辑抽屉 -->
    <el-drawer
      v-model="visible"
      :title="props.title"
      size="75%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      direction="rtl"
      :with-header="true"
      :append-to-body="true"
      custom-class="json-editor-drawer"
    >
      <div v-if="jsonEditorError" class="json-editor-error">
        {{ jsonEditorError }}
      </div>
      
      <div v-else-if="isLoading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <div v-else class="json-editor-container" :class="{'visual-editor': !isTextMode}">
        <!-- 可视化JSON编辑器 -->
        <div v-if="!isTextMode" class="json-fields">
          <div 
            v-for="(field, index) in jsonFields" 
            :key="index"
            class="root-field-wrapper"
          >
            <JsonFieldRecursive
              :field="field"
              :level="0"
              :readonly-key="props.readonlyRootKeys"
              @remove="removeField(index)"
            />
          </div>
          
          <div class="add-field-row">
            <el-button type="primary" @click="addNewField">
              <el-icon><Plus /></el-icon> 添加属性
            </el-button>
          </div>
        </div>
        
        <!-- 文本JSON编辑器 -->
        <div v-else class="text-editor-container">
          <el-input
            v-model="jsonText"
            type="textarea"
            :rows="25"
            class="json-text-editor"
            :spellcheck="false"
            @input="handleTextInput"
            @keydown.tab.prevent="handleTabKey"
          />
          
          <div v-if="textEditorError" class="text-editor-error">
            <el-alert
              :title="textEditorError"
              type="error"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button @click="toggleEditorMode">
            <el-icon class="el-icon--left"><Switch /></el-icon>
            {{ isTextMode ? '切换为可视化编辑' : '切换为文本编辑' }}
          </el-button>
          <el-button type="primary" @click="handleSave" :disabled="!!textEditorError">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, defineExpose, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Plus, ArrowDown, ArrowRight, Switch } from '@element-plus/icons-vue'
import JsonFieldRecursive from './JsonFieldRecursive.vue'

// JSON字段定义
interface JsonField {
  key: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'null' | 'script';
  stringValue: string;
  numberValue: number | null;
  booleanValue: boolean;
  objectValue: Record<string, JsonField>;
  arrayValue: JsonField[];
  expanded?: boolean; // 用于折叠/展开状态控制
}

/**
 * JsonEditor组件props说明：
 * @param modelValue - JSON字符串值
 * @param title - 编辑器标题
 * @param readonlyRootKeys - 是否将最外层属性名设为只读，适用于需要保护JSON结构的场景
 * 
 * 使用示例：
 * <JsonEditor 
 *   v-model="jsonData" 
 *   title="配置编辑" 
 *   :readonly-root-keys="true" 
 *   ref="jsonEditorRef" 
 * />
 * 
 * 当readonlyRootKeys为true时，最外层的属性名（如"name", "age"等）将不可编辑，
 * 但属性值仍然可以正常编辑。这有助于保护重要的配置结构不被意外修改。
 */

const props = defineProps({
  modelValue: {
    type: String,
    default: '{}'
  },
  title: {
    type: String,
    default: 'JSON可视化编辑'
  },
  readonlyRootKeys: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:modelValue', 'save', 'cancel']);

const visible = ref(false);
const jsonEditorError = ref<string>('');
const isLoading = ref(false);
const isTextMode = ref(false);
const jsonText = ref('');
const textEditorError = ref('');

// JSON字段列表
const jsonFields = ref<JsonField[]>([]);

// 打开JSON编辑器
const open = () => {
  jsonFields.value = [];
  isLoading.value = true;
  jsonEditorError.value = '';
  textEditorError.value = '';
  // 确保每次打开时都是可视化模式
  isTextMode.value = false;
  visible.value = true;
  
  try {
    const value = props.modelValue;
    if (!value || typeof value !== 'string') {
      jsonEditorError.value = '无效的JSON格式，请先修正格式再进行可视化编辑';
      isLoading.value = false;
      return;
    }
    
    // 设置文本编辑器的内容
    jsonText.value = formatJsonString(value);
    
    // 使用nextTick和setTimeout确保UI先渲染loading状态，然后再解析JSON
    nextTick(() => {
      setTimeout(() => {
        try {
          const parsedJson = JSON.parse(value);
          convertJsonToFields(parsedJson);
          jsonEditorError.value = '';
          isLoading.value = false;
        } catch (error) {
          jsonEditorError.value = '无效的JSON格式，请先修正格式再进行可视化编辑';
          isLoading.value = false;
          ElMessage.error(jsonEditorError.value);
        }
      }, 300); // 延迟300毫秒，让loading状态能够渲染出来
    });
  } catch (error) {
    jsonEditorError.value = '无效的JSON格式，请先修正格式再进行可视化编辑';
    isLoading.value = false;
    ElMessage.error(jsonEditorError.value);
  }
};

// 格式化JSON字符串
const formatJsonString = (jsonStr: string): string => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2);
  } catch (error) {
    return jsonStr;
  }
};

// 将JSON对象转换为字段列表
const convertJsonToFields = (json: Record<string, any>) => {
  jsonFields.value = Object.entries(json).map(([key, value]) => createField(key, value));
};

// 切换编辑器模式
const toggleEditorMode = () => {
  if (isTextMode.value) {
    // 从文本模式切换到可视化模式
    try {
      // 需要先处理文本值中的换行符和制表符，以便正确解析JSON
      // 在JSON.parse前将字符串中的换行符和制表符转为转义字符
      const processedText = jsonText.value.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
        // 替换字符串中的换行符、制表符为转义字符
        return match
          .replace(/\n/g, '\\n')
          .replace(/\t/g, '\\t');
      });
      
      const parsedJson = JSON.parse(processedText);
      jsonFields.value = [];
      convertJsonToFields(parsedJson);
      textEditorError.value = '';
      isTextMode.value = false;
    } catch (error) {
      textEditorError.value = '当前JSON格式有误，无法切换至可视化编辑';
      ElMessage.error(textEditorError.value);
    }
  } else {
    // 从可视化模式切换到文本模式
    try {
      // 获取当前可视化编辑器的JSON数据
      const result: Record<string, any> = {};
      
      jsonFields.value.forEach(field => {
        if (field.key.trim() === '') return;
        result[field.key] = getFieldValue(field);
      });
      
      // 格式化并设置到文本编辑器
      // JSON.stringify会将\n和\t转为\\n和\\t，需要转回来方便编辑
      let formattedJson = JSON.stringify(result, null, 2);
      formattedJson = formattedJson.replace(/\\n/g, '\n').replace(/\\t/g, '\t').replace(/\\/g, '');
      
      jsonText.value = formattedJson;
      textEditorError.value = '';
      isTextMode.value = true;
    } catch (error) {
      ElMessage.error('切换至文本编辑模式失败');
    }
  }
};

// 处理文本编辑器输入
const handleTextInput = (value: string) => {
  try {
    // 尝试解析JSON格式
    JSON.parse(value);
    textEditorError.value = '';
  } catch (error) {
    if (error instanceof Error) {
      textEditorError.value = `JSON格式错误: ${error.message}`;
    } else {
      textEditorError.value = '无效的JSON格式';
    }
  }
};

// 处理Tab键输入
const handleTabKey = (event: KeyboardEvent) => {
  // 获取当前文本域元素
  const textArea = event.target as HTMLTextAreaElement;
  const start = textArea.selectionStart;
  const end = textArea.selectionEnd;
  
  // 在光标位置插入制表符
  const value = jsonText.value;
  const before = value.substring(0, start);
  const after = value.substring(end);
  
  // 更新文本值，在光标位置插入制表符
  jsonText.value = before + '\t' + after;
  
  // 重新设置光标位置 - 放在制表符之后
  nextTick(() => {
    textArea.selectionStart = textArea.selectionEnd = start + 1;
  });
};

// 创建字段
const createField = (key: string, value: any): JsonField => {
  if (typeof value === 'string') {
    // 检测是否为脚本类型（包含实际的换行符或制表符）
    if (value.includes('\n') || value.includes('\t')) {
      return {
        key,
        type: 'script' as const,
        stringValue: value, // 直接保存原始值
        numberValue: null,
        booleanValue: false,
        objectValue: {},
        arrayValue: [],
        expanded: false
      };
    }
    
    return {
      key,
      type: 'string' as const,
      stringValue: value,
      numberValue: null,
      booleanValue: false,
      objectValue: {},
      arrayValue: [],
      expanded: false
    };
  } else if (typeof value === 'number') {
    return {
      key,
      type: 'number' as const,
      stringValue: value.toString(),
      numberValue: value,
      booleanValue: false,
      objectValue: {},
      arrayValue: [],
      expanded: false
    };
  } else if (typeof value === 'boolean') {
    return {
      key,
      type: 'boolean' as const,
      stringValue: value ? 'true' : 'false',
      numberValue: null,
      booleanValue: value,
      objectValue: {},
      arrayValue: [],
      expanded: false
    };
  } else if (value === null) {
    return {
      key,
      type: 'null' as const,
      stringValue: '',
      numberValue: null,
      booleanValue: false,
      objectValue: {},
      arrayValue: [],
      expanded: false
    };
  } else if (Array.isArray(value)) {
    const field: JsonField = {
      key,
      type: 'array' as const,
      stringValue: '',
      numberValue: null,
      booleanValue: false,
      objectValue: {},
      arrayValue: [],
      expanded: true // 默认展开
    };
    field.arrayValue = value.map((item, index) => createField(index.toString(), item));
    return field;
  } else if (typeof value === 'object') {
    const field: JsonField = {
      key,
      type: 'object' as const,
      stringValue: '',
      numberValue: null,
      booleanValue: false,
      objectValue: {},
      arrayValue: [],
      expanded: true // 默认展开
    };
    Object.entries(value).forEach(([propKey, propValue]) => {
      field.objectValue[propKey] = createField(propKey, propValue);
    });
    return field;
  }
  
  // 默认返回字符串类型
  return {
    key,
    type: 'string' as const,
    stringValue: '',
    numberValue: null,
    booleanValue: false,
    objectValue: {},
    arrayValue: [],
    expanded: false
  };
};

// 添加新字段
const addNewField = () => {
  const field: JsonField = {
    key: '',
    type: 'string' as const,
    stringValue: '',
    numberValue: null,
    booleanValue: false,
    objectValue: {},
    arrayValue: [],
    expanded: false
  };
  
  jsonFields.value.push(field);
};

// 移除字段
const removeField = (index: number) => {
  jsonFields.value.splice(index, 1);
};

// 获取字段值
const getFieldValue = (field: JsonField): any => {
  switch (field.type) {
    case 'string':
      return field.stringValue;
    case 'script':
      // 对于脚本类型，直接返回原始值（已包含转义字符）
      return field.stringValue;
    case 'number':
      return field.numberValue;
    case 'boolean':
      return field.booleanValue;
    case 'object': {
      const result: Record<string, any> = {};
      Object.entries(field.objectValue).forEach(([key, childField]) => {
        if (childField.key && childField.key.trim() !== '') {
          result[childField.key] = getFieldValue(childField);
        }
      });
      return result;
    }
    case 'array': {
      return field.arrayValue.map(item => getFieldValue(item));
    }
    case 'null':
      return null;
    default:
      return null;
  }
};

// 将对象转换为数组，以便渲染
const objectToArray = (obj: Record<string, JsonField>): JsonField[] => {
  return Object.values(obj);
};

// 保存JSON编辑内容
const handleSave = () => {
  try {
    let jsonString = '';
    
    if (isTextMode.value) {
      // 文本模式下，需要先处理文本中的换行符和制表符
      // 类似于从文本模式切换到可视化模式的处理
      const processedText = jsonText.value.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
        return match
          .replace(/\n/g, '\\n')
          .replace(/\t/g, '\\t');
      });
      
      // 尝试解析以确保格式正确
      JSON.parse(processedText);
      jsonString = processedText;
    } else {
      // 可视化模式下，从字段构建JSON
      const result: Record<string, any> = {};
      let hasEmptyKey = false;
      
      jsonFields.value.forEach(field => {
        // 检查是否有空键
        if (field.key.trim() === '') {
          hasEmptyKey = true;
          return;
        }
        
        // 检查是否有重复键
        if (result.hasOwnProperty(field.key)) {
          throw new Error(`存在重复的键名: "${field.key}"`);
        }
        
        result[field.key] = getFieldValue(field);
      });
      
      if (hasEmptyKey) {
        // 内部警告，仍然保留
        ElMessage.warning('检测到空属性名，这些属性将被忽略');
      }
      
      // 格式化JSON，保持缩进统一
      jsonString = JSON.stringify(result, null, 2);
      
      // 验证JSON格式正确性
      JSON.parse(jsonString);
    }
    jsonString = jsonString.replace(/\\n/g, '\n').replace(/\\t/g, '\t').replace(/\\/g, '');
    // 更新模型值
    emits('update:modelValue', jsonString);
    
    // 发出保存事件
    emits('save', jsonString);
    
    // 关闭抽屉
    visible.value = false;
    
    ElMessage.success('JSON配置已更新');
  } catch (error) {
    console.error('保存JSON失败:', error);
    if (error instanceof Error) {
      ElMessage.error(`保存JSON失败: ${error.message}`);
    } else {
      ElMessage.error('保存JSON失败，请检查格式');
    }
  }
};

// 取消编辑
const handleCancel = () => {
  visible.value = false;
  emits('cancel');
};

// 对外暴露方法
defineExpose({
  open
});
</script>

<style scoped lang="scss">
:deep(.json-editor-drawer) {
  display: flex;
  flex-direction: column;
  
  .el-drawer__header {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .el-drawer__body {
    padding: 0;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .el-drawer__footer {
    padding: 0;
    flex-shrink: 0;
  }
}

.json-editor-error {
  color: var(--el-color-danger);
  padding: 20px;
  text-align: center;
  font-weight: bold;
}

.loading-container {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  
  :deep(.el-skeleton) {
    width: 100%;
  }
}

.json-editor-container {
  padding: 20px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  &.visual-editor {
    overflow-y: auto;
  }
}

.json-fields {
  margin-bottom: 20px;
  flex: 1;
}

.text-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  
  .json-text-editor {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 14px;
    line-height: 1.5;
    flex: 1;
    
    :deep(.el-textarea__inner) {
      padding: 12px;
      border-radius: 4px;
      border-color: #dcdfe6;
      resize: none;
      height: calc(100vh - 180px);
    }
  }
  
  .text-editor-error {
    margin-top: 15px;
  }
}

.json-editor-info {
  margin-bottom: 20px;
  
  :deep(.el-alert__content) {
    padding: 0 8px;
  }
  
  :deep(.el-alert__icon) {
    margin-right: 8px;
    font-size: 16px;
  }
}

.alert-title {
  font-weight: 600;
}

.root-field-wrapper {
  padding-bottom: 25px;
  border-bottom: 1px dashed #c0d7fd;
  
  &:last-of-type {
    border-bottom: none;
  }
  
  &:nth-child(odd) {
    background-color: rgba(247, 250, 255, 0.5);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  }
  
  &:nth-child(even) {
    background-color: rgba(252, 254, 255, 0.5);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.02);
  }
  
  &:hover {
    background-color: rgba(240, 247, 255, 0.7);
  }
}

.add-field-row {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 50px;
}

.drawer-footer {
  padding: 10px 20px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
  
  .el-button {
    margin-left: 8px;
  }
}
</style> 