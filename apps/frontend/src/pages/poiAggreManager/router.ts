import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/poiAggreManage/queryScene',
    name: 'QueryScene',
    component: () => import('./views/manager/QueryScene.vue'),
    meta: {
      title: '查询场景管理'
    }
  },
  {
    path: '/poiAggreManage/serviceOrchestration',
    name: 'ServiceOrchestration',
    component: () => import('./views/manager/ServiceOrchestration.vue'),
    meta: {
      title: '服务编排管理'
    }
  },
  {
    path: '/poiAggreManage/queryFieldMetadata',
    name: 'QueryFieldMetadata',
    component: () => import('./views/manager/QueryFieldMetadata.vue'),
    meta: {
      title: '查询字段管理'
    }
  },
  {
    path: '/poiAggreManage/syncFieldMetadata',
    name: 'SyncFieldMetadata',
    component: () => import('./views/manager/SyncFieldMetadata.vue'),
    meta: {
      title: '同步字段管理'
    }
  },
  {
    path: '/poiAggreManage/syncConfig',
    name: 'SyncConfig',
    component: () => import('./views/manager/SyncConfig.vue'),
    meta: {
      title: '同步配置管理'
    }
  },
  {
    path: '/poiAggreManage/tools/thriftServiceTool',
    name: 'ThriftServiceTool',
    component: () => import('./views/tools/ThriftServiceTool.vue'),
    meta: {
      title: 'Thrift服务工具'
    }
  },
  {
    path: '/poiAggreManage/tools/orchestrationVisualizer',
    name: 'OrchestrationVisualizer',
    component: () => import('./views/tools/orchestration/OrchestrationVisualizer.vue'),
    meta: {
      title: '服务编排可视化'
    }
  },
  {
    path: '/poiAggreManage/taskCenter/taskList',
    name: 'TaskList',
    component: () => import('./views/changelog/TaskList.vue'),
    meta: {
      title: '变更列表'
    }
  },
  {
    path: '/poiAggreManage/taskCenter/myTask',
    name: 'MyTask',
    component: () => import('./views/changelog/MyTask.vue'),
    meta: {
      title: '我的申请'
    }
  },
  {
    path: '/poiAggreManage/accessGuide',
    name: 'AccessGuide',
    component: () => import('./views/manager/AccessGuide.vue'),
    meta: {
      title: '接入指引'
    }
  }
]

export default routes
