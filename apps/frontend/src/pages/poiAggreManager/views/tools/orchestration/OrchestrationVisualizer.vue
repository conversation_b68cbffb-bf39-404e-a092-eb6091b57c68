<template>
  <div class="orchestration-visualizer">
    <div class="page-header">
      <h2>服务编排可视化</h2>
      <div class="actions">
        <el-button-group>
          <el-button :type="isEditing ? 'primary' : ''" @click="toggleEditMode">
            <el-icon><Edit /></el-icon>
            {{ isEditing ? '退出编辑' : '编辑模式' }}
          </el-button>
          <el-button type="success" @click="executeWorkflow">
            <el-icon><VideoPlay /></el-icon>
            执行
          </el-button>
          <!-- 保存按钮 -->
          <el-button 
            type="danger" 
            @click="openDslDiffViewer" 
            v-if="workflow" 
            :disabled="!hasDslChanged()"
          >
            <el-icon><Download /></el-icon>
            保存DSL(对比)
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="wrapper-container">
      <!-- 使用新的工作流包装组件 -->
      <workflow-graph-wrapper
        ref="workflowGraphWrapperRef"
        :workflow="workflow"
        :dslJson="dslJson"
        :isEditing="isEditing"
        :executionStatus="executionStatus"
        :showDslEditor="showDslEditor"
        :dslLoading="dslLoading"
        @node-click="handleNodeClick"
        @start-node-click="handleStartNodeClick"
        @end-node-click="handleEndNodeClick"
        @workflow-info-update="handleConfirmWorkflowInfo"
        @execution-indicator-click="handleExecutionIndicatorClick"
        @dsl-confirm="handleDslConfirm"
        @start-editing-dsl="startEditingDsl"
        @open-sample-selector="openSampleSelector"
        @update:dslJson="dslJson = $event"
        @toggle-dsl-editor="toggleDslEditor"
        @add-node="openAddNodeDialog"
      />
    </div>
    
    <!-- 任务详情面板 -->
    <task-detail-panel
      :visible="taskDetailVisible"
      :task="selectedTask"
      :is-editing="isEditing"
      :on-edit="isEditing ? handleEditNode : undefined"
      :on-execute="handleExecuteNode"
      @update:visible="taskDetailVisible = $event"
      @close="closeTaskDetail"
      @confirm="handleTaskConfirm"
      @delete-node="handleDeleteNode"
    />
    <!-- 节点执行详情侧边栏 -->
    <task-execution-panel
      :visible="taskExecutionVisible"
      :executionTask="executionTask"
      @update:visible="taskExecutionVisible = $event"
      @close="closeTaskExecution"
    />
    
    <!-- 添加开始节点侧边栏 -->
    <start-node-detail-panel
      :visible="startNodeDetailVisible"
      :input-params="workflow?.inputParams || []"
      :is-editing="isEditing"
      :show-execution-form="!isEditing"
      @update:visible="startNodeDetailVisible = $event"
      @close="closeStartNodeDetail"
      @confirm-params="handleConfirmTemporaryParams"
      @execute="handleWorkflowExecute"
    />
    
    <!-- 添加结束节点侧边栏 -->
    <end-node-detail-panel
      :visible="endNodeDetailVisible"
      :outputs="workflow?.outputs"
      :execution-result="executionResponse"
      :is-editing="isEditing"
      :can-edit="true"
      @update:visible="endNodeDetailVisible = $event"
      @close="closeEndNodeDetail"
      @edit="handleEditEndNode"
      @confirm="handleConfirmOutputs"
      @cancel="handleOutputCancel"
    />
    
    <!-- 添加示例选择对话框 -->
    <el-dialog
      v-model="showSampleSelector"
      title="选择DSL示例"
      width="600px"
    >
      <el-form>
        <el-form-item label="选择示例">
          <el-select 
            v-model="selectedSamplePath" 
            placeholder="请选择DSL示例" 
            style="width: 100%"
            filterable
            allow-create
            default-first-option
          >
            <el-option
              v-for="item in sampleList"
              :key="item.path"
              :label="`${item.name} - ${item.description}`"
              :value="item.fileName"
            >
              <div class="sample-option">
                <div class="sample-name">{{ item.name }}</div>
                <div class="sample-desc">{{ item.description }}</div>
                <div class="sample-file">{{ item.fileName }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button @click="showSampleSelector = false">取消</el-button>
          <el-button type="primary" @click="loadSelectedSample">加载</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 添加原始DSL记录和差异对比对话框 -->
    <el-dialog
      v-model="showDslDiffViewer"
      title="DSL对比"
      width="90%"
      top="5vh"
      :fullscreen="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
    >
      <template #header>
        <div class="dialog-header">
          <h3>DSL变更对比</h3>
          <div>
            <el-button type="primary" @click="confirmSaveDsl">确定</el-button>
          </div>
        </div>
      </template>
      <div style="height: 90vh; overflow: hidden;">
        <dsl-diff-viewer
          :originalDsl="originalDsl"
          :modifiedDsl="dslJson"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
// #region 导入依赖
import { ref, computed, onMounted, reactive, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Workflow, Task, Response, WorkflowContext, Transaction } from '../../../types/orchestration';
import { ApiResponse } from '../../../../../shared/types/response';
import {
  executeTask,
  executeWorkflow as apiExecuteWorkflow,
  parseDsl,
  stringify
} from '../../../request/orchestration';
import { cleanupObsoleteDslData as sharedCleanupObsoleteDslData } from '../../../utils/orchestrationUtils';
import { Document, Edit, VideoPlay, DArrowLeft, DArrowRight, TurnOff, Download } from '@element-plus/icons-vue';
import { useRoute } from 'vue-router';

// 组件引入 - 使用正确的相对路径
import TaskDetailPanel from '../../../components/orchestration/TaskDetailPanel.vue';
import TaskExecutionPanel from '../../../components/orchestration/TaskExecutionPanel.vue';
import StartNodeDetailPanel from '../../../components/orchestration/StartNodeDetailPanel.vue';
import EndNodeDetailPanel from '../../../components/orchestration/EndNodeDetailPanel.vue';
import DslDiffViewer from '../../../components/orchestration/DslDiffViewer.vue';
import WorkflowGraphWrapper from '../../../components/orchestration/WorkflowGraphWrapper.vue';
// #endregion

const props = defineProps<{
  dslKey?: string
}>()

// 定义emit
const emit = defineEmits<{
  (e: 'dsl-saved', dsl: string): void;
}>();

// 初始化
onMounted(() => {
  // 清理过期的DSL数据
  sharedCleanupObsoleteDslData();
  
  // 加载示例列表
  getSampleList();
  
  // 处理从URL参数获取的DSL字符串
  handleDslFromParams();
  
  // 如果没有从URL加载DSL，则初始化空的原始DSL
  if (!dslJson.value) {
    originalDsl.value = dslJson.value;
  }
  
  // 确保初始时保存一份原始DSL
  saveOriginalDsl();
});
// 处理从URL参数或localStorage获取的DSL字符串
// 获取路由实例
const route = useRoute();
const handleDslFromParams = async () => {
  try {
    // 获取URL参数
    let dslKey = route.query.dslKey as string;
    const dslParam = route.query.dsl as string;
    
    console.log('URL参数解析:', { dslKey, dslParam: dslParam ? '存在' : '不存在' });

    if ((!dslKey || dslKey === '') && props.dslKey) {
      dslKey = props.dslKey;
      console.log('使用props中传入的dslKey:', dslKey);
    }
    
    // 编辑器默认隐藏
    showDslEditor.value = false;
    console.log('DSL编辑器默认隐藏');
    
    // 优先从localStorage中获取DSL (通过dslKey)
    if (dslKey && dslKey.startsWith('dsl_visualizer_')) {
      console.log('检测到localStorage键:', dslKey);
      
      try {
        // 列出所有localStorage键，帮助调试
        const allStorageKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) allStorageKeys.push(key);
        }
        console.log('当前localStorage中的所有键:', allStorageKeys);
        
        // 获取localStorage中的DSL内容
        const storedDsl = localStorage.getItem(dslKey);
        
        if (storedDsl) {
          console.log(`从localStorage获取到DSL[${dslKey}]: ${storedDsl.length}字符, 前100字符:`, storedDsl.substring(0, 100) + '...');
          
          // 设置DSL并解析
          dslJson.value = storedDsl;
          await parseAndUpdateWorkflow(storedDsl);
          
          // 保存原始DSL
          saveOriginalDsl();
          
          // 提示用户
          ElMessage.success('已从本地存储加载DSL');
          
          // 立即清理，避免污染
          localStorage.removeItem(dslKey);
          console.log('已从localStorage中移除临时DSL数据:', dslKey);
          
          // 清理其他可能存在的过期数据
          sharedCleanupObsoleteDslData();
          
          return; // 成功加载后返回
        } else {
          console.warn('localStorage中未找到键对应的数据:', dslKey);
          // 尝试获取其他可能存在的DSL数据
          const visualizerKeys = allStorageKeys.filter(k => k.startsWith('dsl_visualizer_'));
          if (visualizerKeys.length > 0) {
            console.log('找到其他可能的DSL数据键:', visualizerKeys);
            for (const key of visualizerKeys) {
              const value = localStorage.getItem(key);
              console.log(`键[${key}]包含数据长度:`, value ? value.length : 0);
            }
          }
        }
      } catch (storageError) {
        console.error('从localStorage获取DSL失败:', storageError);
      }
    }
    
    // 其次尝试从URL参数(dsl)获取DSL内容
    if (dslParam) {
      // URL参数可能是经过编码的，需要解码
      const decodedDsl = decodeURIComponent(dslParam);
      console.log(`从URL参数获取到DSL: ${decodedDsl.length}字符, 前100字符:`, decodedDsl.substring(0, 100) + '...');
      
      // 设置DSL并解析
      dslJson.value = decodedDsl;
      await parseAndUpdateWorkflow(decodedDsl);
      
      // 保存原始DSL
      saveOriginalDsl();
      
      // 提示用户
      ElMessage.success('已从URL参数加载DSL');
      return; // 成功加载后返回
    }
    
    // 如果没有dslKey和dslParam，不做任何操作，用户可以手动加载示例或创建新的DSL
    console.log('未检测到DSL参数，等待用户操作...');
  } catch (error) {
    console.error('处理DSL数据失败:', error);
    ElMessage.error('处理DSL数据失败');
  }
};




// #region 状态变量定义
// 工作流对象
const workflow = ref<Workflow | null>(null);
const dslJson = ref<string>('');
const dslLoading = ref(false);

// WorkflowGraph包装组件引用
const workflowGraphWrapperRef = ref<any>(null);

// 示例选择相关
const showSampleSelector = ref(false);
const sampleList = ref<Array<{fileName: string; name: string; description: string; path: string}>>([]);
const selectedSamplePath = ref('');

// 编辑状态
const isEditing = ref(false);
const showDslEditor = ref(false);

// 任务详情面板状态
const taskDetailVisible = ref(false);
const selectedTask = ref<Task | null>(null);

// 任务编辑对话框状态
const taskEditVisible = ref(false);
const editingTask = ref<Task | null>(null);
const isNewTask = ref(false);

// 添加特殊节点侧边栏状态
const startNodeDetailVisible = ref(false);
const endNodeDetailVisible = ref(false);

// 执行状态
const executionStatus = reactive<Record<string, string>>({});
const executionResponse = ref<Response<Object> | null>(null);
const executionTask = {
  task: {} as Task,
  invokeTask: {}, 
  taskResult: {},
  taskException: {},
  taskTransaction: {} as Transaction,
  envMap: {},
  executionStatus: {}
};
// 节点执行详情侧边栏状态
const taskExecutionVisible = ref(false);

// 添加当前保存的参数字符串
const savedParamJson = ref('{}');

// 原始DSL记录
const originalDsl = ref('');

// 差异对比对话框状态
const showDslDiffViewer = ref(false);
// #endregion



// #region 示例管理功能
// 获取示例列表
const getSampleList = async () => {
  try {
    const response = await fetch('/api/businessoms/poiaggremanage/tools/orchestration/sample/list');
    if (response.ok) {
      const result = await response.json();
      if (result.code === 0 && Array.isArray(result.data)) {
        sampleList.value = result.data;
        console.log('获取到示例列表:', sampleList.value);
      } else {
        ElMessage.warning('获取示例列表失败: ' + (result.message || '未知错误'));
      }
    } else {
      ElMessage.error(`获取示例列表请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('获取示例列表出错:', error);
    ElMessage.error('获取示例列表出错，请稍后重试');
  }
};

// 打开示例选择对话框
const openSampleSelector = () => {
  // 确保已加载示例列表
  if (sampleList.value.length === 0) {
    getSampleList().then(() => {
      showSampleSelector.value = true;
    });
  } else {
    showSampleSelector.value = true;
  }
};

// 加载选中的示例
const loadSelectedSample = async () => {
  if (!selectedSamplePath.value) {
    ElMessage.warning('请先选择或输入一个示例');
    return;
  }
  
  showSampleSelector.value = false;
  
  try {
    // 确保文件名有.json后缀
    let fileName = selectedSamplePath.value;
    if (!fileName.endsWith('.json')) {
      fileName = fileName + '.json';
    }
    
    // 使用文件名获取示例DSL
    const dsl = await getSampleDsl(fileName);
    if (dsl) {
      // 设置DSL
      dslJson.value = dsl;
      
      // 先解析DSL
      await parseAndUpdateWorkflow(dsl);
      
      // 保存原始DSL
      saveOriginalDsl();
      
      ElMessage.success(`已加载示例: ${fileName}`);
    }
  } catch (error) {
    console.error('加载选中示例失败:', error);
    ElMessage.error('加载选中示例失败');
  }
};

// 获取示例DSL
const getSampleDsl = async (type: string = 'testDeliveryCategory') => {
  try {
    // 使用fetch获取示例DSL
    console.log('开始获取示例DSL:', type);
    const response = await fetch(`/api/businessoms/poiaggremanage/tools/orchestration/sample?type=${encodeURIComponent(type)}`);
    console.log('示例DSL请求状态:', response.status, response.statusText);
    
    if (response.ok) {
      const responseText = await response.text();
      console.log('示例DSL响应原始内容:', responseText.substring(0, 100) + '...');
      
      try {
        // 尝试解析响应为JSON对象
        const jsonData = JSON.parse(responseText);
        console.log('解析后的示例DSL响应:', jsonData);
        
        if (jsonData.code === 0) {
          if (typeof jsonData.data === 'string') {
            // 返回原始DSL字符串
            return jsonData.data;
          } else if (jsonData.data) {
            // 如果data不是字符串而是对象，转换为字符串返回
            return JSON.stringify(jsonData.data, null, 2);
          }
        } else {
          console.error('获取示例DSL失败:', jsonData.message);
          ElMessage.error('获取示例DSL失败: ' + (jsonData.message || '未知错误'));
          return null;
        }
      } catch (parseError) {
        // 响应不是JSON，可能是直接返回的DSL文本
        console.log('响应不是JSON格式，尝试直接使用文本内容');
        // 检查是否像JSON的内容
        if (responseText.trim().startsWith('{') && responseText.trim().endsWith('}')) {
          return responseText;
        } else {
          console.error('响应不是有效的DSL格式:', parseError);
          ElMessage.error('获取的示例DSL格式错误');
          return null;
        }
      }
    } else {
      // 处理HTTP错误
      console.error('获取示例DSL请求失败:', response.status, response.statusText);
      try {
        const errorText = await response.text();
        console.error('错误响应内容:', errorText);
      } catch (e) {
        // 忽略
      }
      ElMessage.error(`获取示例DSL请求失败: ${response.status} ${response.statusText}`);
      return null;
    }
  } catch (error) {
    console.error('获取示例DSL出错:', error);
    ElMessage.error('获取示例DSL出错，请稍后重试');
    return null;
  }
};
// #endregion



// #region 编辑模式与UI切换功能
// 处理DSL编辑切换
const toggleDslEditor = (show?: boolean) => {
  // 如果提供了show参数，则直接使用它，否则切换当前状态
  showDslEditor.value = show !== undefined ? show : !showDslEditor.value;
  
  // 设置一个短暂延迟，减少卡顿感
  setTimeout(() => {
    if (workflowGraphWrapperRef.value) {
      // 调用WorkflowGraphWrapper组件的fitView方法
      if (typeof workflowGraphWrapperRef.value.fitView === 'function') {
        workflowGraphWrapperRef.value.fitView();
        console.log('已调用适应画布方法');
      }
      
      // 如果隐藏了DSL编辑器，简化更新流程，减少重复渲染
      if (!showDslEditor.value && workflow.value) {
        // 减少嵌套的setTimeout，避免多次重绘
        if (typeof workflowGraphWrapperRef.value.fitView === 'function') {
          workflowGraphWrapperRef.value.fitView();
        }
      }
    }
  }, 80); // 减少延迟时间，提高响应性
  
  // 显示相应的提示消息，但不影响渲染过程
  queueMicrotask(() => {
    if (showDslEditor.value) {
      ElMessage.success('DSL编辑器已显示');
    } else {
      ElMessage.success('DSL编辑器已隐藏');
    }
  });
};

// 切换编辑模式
const toggleEditMode = () => {
  isEditing.value = !isEditing.value;
  
  // 当退出编辑模式时，确保流程图正确显示
  if (!isEditing.value) {
    setTimeout(() => {
      if (workflowGraphWrapperRef.value) {
        // 确保更新流程图
        if (workflow.value && typeof workflowGraphWrapperRef.value.updateWithoutRebuild === 'function') {
          workflowGraphWrapperRef.value.updateWithoutRebuild(workflow.value);
        }
        // 调整视图
        if (typeof workflowGraphWrapperRef.value.fitView === 'function') {
          workflowGraphWrapperRef.value.fitView();
        }
      }
    }, 200);
    ElMessage.info('已退出编辑模式，工作流已刷新');
  } else {
    ElMessage.info('已进入编辑模式，可以修改工作流');
  }
};

// 处理开始编辑DSL
const startEditingDsl = () => {
  showDslEditor.value = true;
  isEditing.value = true;
  ElMessage.info('已进入编辑模式，可以修改工作流');
};

// 处理结束节点编辑
const handleEditEndNode = () => {
  if (!isEditing.value) {
    isEditing.value = true;
    ElMessage.info('进入编辑模式');
  }
};
// #endregion



// #region 节点交互处理
// 处理节点点击
const handleNodeClick = (task: Task) => {
  // 只设置当前选中任务，不处理执行信息
  selectedTask.value = task;
  taskDetailVisible.value = true;
};

// 关闭任务详情面板
const closeTaskDetail = () => {
  taskDetailVisible.value = false;
  selectedTask.value = null;
};

// 处理节点编辑
const handleEditNode = (task: Task) => {
  editingTask.value = { ...task };
  isNewTask.value = false;
  taskEditVisible.value = true;
};

// 处理开始节点点击（输入参数配置）
const handleStartNodeClick = () => {
  console.log('handleStartNodeClick 被调用了'); // 调试日志
  startNodeDetailVisible.value = true;
};

// 处理结束节点点击（输出配置）
const handleEndNodeClick = () => {
  console.log('handleEndNodeClick 被调用了'); // 调试日志
  endNodeDetailVisible.value = true;
};

// 取消输出配置编辑
const handleOutputCancel = () => {
  // 关闭侧边栏
  endNodeDetailVisible.value = false;
};

// 关闭起始节点详情面板
const closeStartNodeDetail = () => {
  startNodeDetailVisible.value = false;
};

// 关闭结束节点详情面板
const closeEndNodeDetail = () => {
  endNodeDetailVisible.value = false;
};

// 关闭节点执行详情侧边栏
const closeTaskExecution = () => {
  taskExecutionVisible.value = false;
};

// 处理执行指示器点击
const handleExecutionIndicatorClick = (task: Task) => {
  // 当前任务
  executionTask.task = { ...task };
  
  // 如果有执行响应，查找相关的执行数据
  if (executionResponse.value && executionResponse.value.workflowContext) {
    const taskAlias = task.alias;
    const context = executionResponse.value.workflowContext as WorkflowContext;
    
    // 清空之前的数据
    executionTask.taskResult = {};
    executionTask.taskException = {};
    executionTask.taskTransaction = {} as Transaction;
    executionTask.envMap = {};
    executionTask.invokeTask = {};
    
    // 从taskResultMap中获取任务执行结果
    if (context.taskResultMap && taskAlias in context.taskResultMap) {
      executionTask.taskResult = { ...context.taskResultMap[taskAlias] };
    }
    
    // 获取任务异常信息
    if (context.taskExceptionMap && taskAlias in context.taskExceptionMap) {
      executionTask.taskException = context.taskExceptionMap[taskAlias];
    }

    // 获取任务异常信息
    if (context.transactionMap && taskAlias in context.transactionMap) {
      executionTask.taskTransaction = context.transactionMap[taskAlias];
    }

    // 获取执行任务信息
    if (context.invokerTaskMap && taskAlias in context.invokerTaskMap) {
      executionTask.invokeTask = context.invokerTaskMap[taskAlias];
    }
    
    // 获取与任务相关的环境变量
    if (context.envMap) {
      executionTask.envMap = context.envMap;
    }
    
    // 打开执行详情侧边栏
    taskExecutionVisible.value = true;
  } else {
    // 没有执行结果时给出提示
    ElMessage.info('该节点尚未执行或没有执行结果');
  }
};
// #endregion



// #region 工作流数据处理与保存
/**
 * 清除所有执行状态和相关数据
 * 当DSL变更时，应当调用此函数，这样可以确保在修改DSL后，用户看到的是一个干净的状态，避免旧的执行状态造成误解
 */
const clearExecutionStatus = () => {
  // 清除所有执行状态
  Object.keys(executionStatus).forEach(key => delete executionStatus[key]);
  // 清除执行响应
  executionResponse.value = null;
  // 清除执行任务详情
  executionTask.task = {} as Task;
  executionTask.invokeTask = {};
  executionTask.taskResult = {};
  executionTask.taskException = {};
  executionTask.taskTransaction = {} as Transaction;
  executionTask.envMap = {};
  executionTask.executionStatus = {};
  
  // 确保图表中的节点状态也被重置
  if (workflowGraphWrapperRef.value) {
    // 调用WorkflowGraph组件的resetNodeStatus方法重置所有节点状态
    if (typeof workflowGraphWrapperRef.value.resetNodeStatus === 'function') {
      workflowGraphWrapperRef.value.resetNodeStatus();
    }
    
    // 刷新视图
    nextTick(() => {
      if (typeof workflowGraphWrapperRef.value.fitView === 'function') {
        workflowGraphWrapperRef.value.fitView();
      }
    });
  }
  
  // 关闭相关面板
  taskExecutionVisible.value = false;
};

// 处理工作流信息确认
const handleConfirmWorkflowInfo = async (data: any) => {
  console.log('handleConfirmWorkflowInfo', data);

  // 检查是否只是设置编辑模式的请求
  if (data && 'isEditing' in data && Object.keys(data).length === 1) {
    // 如果是编辑模式切换请求，则只切换编辑模式
    isEditing.value = data.isEditing;
    return;
  }

  if (!workflow.value) {
    workflow.value = {} as Workflow;
  }
  
  try {
    // 创建工作流的浅拷贝，以便触发引用监听
    const updatedWorkflow = { ...workflow.value };
    
    // 更新工作流基本信息到内存中的 workflow 对象
    if (data.name) {
      updatedWorkflow.name = data.name;
    }
    if (data.description) {
      updatedWorkflow.description = data.description;
    }
    if (data.timeout) {
      updatedWorkflow.timeout = data.timeout;
    }
    updatedWorkflow.failFast = data.failFast;
    if (data.retry) {
      updatedWorkflow.retry = data.retry;
    }
    if (data.inputParams) {
      updatedWorkflow.inputParams = data.inputParams;
    }
    
    
    // 更新工作流引用，触发Vue的引用监听
    workflow.value = updatedWorkflow;
    
    // 使用后端stringify API重组DSL，确保格式正确
    const updatedDslString = await reassembleDsl(workflow.value);
    dslJson.value = updatedDslString;
    
    // 清除执行状态
    clearExecutionStatus();
    
    // 调用后端接口解析更新后的DSL，确保数据一致性
    await parseAndUpdateWorkflow(updatedDslString);
    
    // 提示确认成功
    ElMessage.success('工作流信息已更新');
  } catch (error) {
    console.error('更新工作流信息时出错:', error);
    ElMessage.error('更新工作流信息失败，请稍后重试');
  }
};

// 处理输出配置确认
const handleConfirmOutputs = async (outputs: any) => {
  if (!workflow.value) {
    workflow.value = {} as Workflow;
  }
  
  try {
    console.log('确认前的outputs:', outputs);

    // 创建工作流的浅拷贝，以便触发引用监听
    const updatedWorkflow = { ...workflow.value };
    
    // 更新工作流输出配置
    updatedWorkflow.outputs = outputs;
    
    // 更新工作流引用，触发Vue的引用监听
    workflow.value = updatedWorkflow;
    
    // 使用后端stringify API重组DSL，确保格式正确
    const updatedDslString = await reassembleDsl(workflow.value);
    dslJson.value = updatedDslString;
    
    // 清除执行状态
    clearExecutionStatus();
    
    // 调用后端接口解析更新后的DSL，确保数据一致性
    await parseAndUpdateWorkflow(updatedDslString);
    
    // 提示确认成功
    ElMessage.success('输出定义已更新');
    
    // 切换为非编辑模式
    isEditing.value = false;
    
    // 关闭侧边栏（如果是从侧边栏调用）
    endNodeDetailVisible.value = false;
  } catch (error) {
    console.error('更新输出配置时出错:', error);
    ElMessage.error('更新输出配置失败，请稍后重试');
  }
};

// 处理任务确认
const handleTaskConfirm = async (task: Task) => {
  if (!workflow.value) return;
  console.log('handleTaskConfirm', task);
  
  try {
    // 创建工作流的浅拷贝，以便触发引用监听
    const updatedWorkflow = { ...workflow.value };
    
    // 确保taskMap存在
    if (!updatedWorkflow.taskMap) {
      updatedWorkflow.taskMap = {};
    }
    
    // 添加或更新任务
    updatedWorkflow.taskMap[task.alias] = task;
    
    // 更新工作流引用，触发Vue的引用监听
    workflow.value = updatedWorkflow;
    
    // 使用后端stringify API重组DSL，确保格式正确
    const updatedDslString = await reassembleDsl(workflow.value);
    dslJson.value = updatedDslString;
    console.log('updatedDslString', updatedDslString);
    
    // 清除执行状态
    clearExecutionStatus();
    
    // 调用后端接口解析更新后的DSL，确保数据一致性
    await parseAndUpdateWorkflow(updatedDslString);
    
    ElMessage.success(`任务 ${task.alias} 已${isNewTask.value ? '创建' : '更新'}`);
    taskEditVisible.value = false;
  } catch (error) {
    console.error('确认任务时出错:', error);
    ElMessage.error('确认任务失败，请稍后重试');
  }
};

// 处理开始节点侧边栏
const handleConfirmTemporaryParams = (params: any, paramsJson: string) => {
  // 更新工作流图中的显示
  if (workflowGraphWrapperRef.value) {
    // 直接传递完整的参数对象
    workflowGraphWrapperRef.value.updateInputParams(params);
  }
  
  // 确认参数JSON字符串
  savedParamJson.value = paramsJson;
  console.log('已确认参数字符串:', savedParamJson.value);
  
  // 清除执行状态
  clearExecutionStatus();
  
  // 关闭侧边栏
  startNodeDetailVisible.value = false;
  
  // 显示成功提示
  ElMessage.success('参数已临时保存');
};

// 处理DSL确认
const handleDslConfirm = (dsl: string) => {
  console.log('handleDslConfirm 被调用了'); // 调试信息
  
  // 清除执行状态
  clearExecutionStatus();
  
  // 解析新的DSL
  parseAndUpdateWorkflow(dsl).then(() => {
    // 解析成功后，使用reassembleDsl重新格式化
    if (workflow.value) {
      // 重新格式化DSL，以确保格式一致性
      reassembleDsl(workflow.value).then(formattedDsl => {
        // 注意：这里不需要再次调用parseAndUpdateWorkflow，避免无限循环
        // 只是静默更新dslJson的值
        dslJson.value = formattedDsl;
        
        console.log('DSL已重新格式化为原始格式');
      }).catch(error => {
        console.error('重新格式化DSL失败:', error);
      });
    }
  });
};

// 解析DSL
const parseAndUpdateWorkflow = async (dsl: string) => {
  try {
    dslLoading.value = true;
    
    console.log('开始调用后端解析接口...');
    console.log('发送到后端的DSL数据长度:', dsl.length);
    
    const responseData = await parseDsl(dsl);
    console.log('后端解析返回的响应类型:', typeof responseData);
    console.log('后端解析返回的响应结构:', Object.keys(responseData));
    
    // 确保响应数据格式正确
    if (!responseData) {
      console.error('解析DSL响应为空');
      ElMessage.error('解析DSL响应为空');
      return;
    }
    
    // 处理非标准响应（可能是HTTP错误）
    if ((responseData as any).status && (responseData as any).status !== 200) {
      console.error('解析DSL HTTP错误:', responseData);
      ElMessage.error(`解析DSL请求失败: ${(responseData as any).status} ${(responseData as any).statusText || ''}`);
      return;
    }
    
    // 尝试解析响应数据
    const response = responseData as unknown as ApiResponse<Workflow>;
    console.log('解析DSL响应状态码:', response.code);
    console.log('解析DSL响应消息:', response.message);
    
    if (response.code === 0 && response.data) {
      console.log('DSL解析成功，数据结构:', Object.keys(response.data));
      console.log('工作流名称:', response.data.name);
      console.log('任务数量:', response.data.taskMap ? Object.keys(response.data.taskMap).length : 0);
      
      // 获取新的工作流数据
      const newWorkflow = response.data;
      
      // 检查当前是否已有工作流对象
      if (workflow.value) {
        // 关键改变：改为引用替换而不是对象合并
        // 这会触发Vue的引用监听，从而使WorkflowGraph组件收到更新
        workflow.value = { ...newWorkflow };
        console.log('完全替换工作流对象，确保变更被监听到');
      } else {
        // 首次加载时直接赋值
        console.log('首次加载工作流，直接赋值');
        workflow.value = newWorkflow;
      }
      
      // 主动更新图表显示（作为后备措施保留）
      if (workflowGraphWrapperRef.value && typeof workflowGraphWrapperRef.value.updateWithoutRebuild === 'function') {
        nextTick(() => {
          // 确保工作流数据已更新后再调用
          if (workflow.value) {
            console.log('调用WorkflowGraph的updateWithoutRebuild更新图表');
            workflowGraphWrapperRef.value.updateWithoutRebuild(workflow.value);
          }
        });
      }
      
      ElMessage.success('DSL解析成功');
    } else {
      // 详细记录错误信息
      const errorMsg = response.message || '未知错误';
      console.error('DSL解析失败详情:', {
        code: response.code,
        message: errorMsg,
        response: JSON.stringify(response).substring(0, 500) + '...'
      });
      ElMessage.error(`DSL解析失败: ${errorMsg}`);
    }
  } catch (error: any) {
    // 更详细地处理错误信息
    const errorMessage = error?.message || '未知错误';
    console.error('解析DSL过程发生错误:', error);
    console.error('错误类型:', error?.name);
    console.error('错误详情:', errorMessage);
    console.error('错误栈:', error?.stack);
    ElMessage.error(`DSL解析失败: ${errorMessage}`);
  } finally {
    dslLoading.value = false;
  }
};

// 处理DSL格式化
const reassembleDsl = async (workflowData: Workflow): Promise<string> => {
  if (!workflowData) return dslJson.value;
  
  try {
    console.log('reassembleDsl 被调用了');
    console.log('workflowData', workflowData);
    // 处理输出中的switch和switchExpression
    


    // 调用后端stringify API将Workflow对象转换为DSL JSON字符串
    const response = await stringify(workflowData) as unknown as ApiResponse<string>;
    if (response && response.code === 0 && response.data) {
      return response.data;
    } else {
      // 如果后端返回错误，记录日志并回退到旧方法
      console.error('后端stringify失败:', response?.message || '未知错误');
      ElMessage.error('处理DSL格式化异常');
    }
  } catch (error) {
    // 如果API调用失败，记录日志并回退到旧方法
    console.error('调用stringify API失败，回退到前端处理:', error);
    ElMessage.error('处理DSL格式化异常');
  }
  return dslJson.value;
};

// #endregion



// #region 工作流和任务执行功能
// 执行单个任务
const handleExecuteNode = async (task: Task) => {
  if (!workflow.value) return;
  
  try {
    ElMessageBox.prompt('请输入执行参数（JSON格式）', `执行任务: ${task.alias}`, {
      confirmButtonText: '执行',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '{ "param1": "value1" }',
      inputValue: '{}'
    }).then(async ({ value }) => {
      try {
        // 验证JSON格式
        JSON.parse(value);
        
        // 更新任务状态
        executionStatus[task.alias] = 'running';
        
        // 调用执行接口
        const responseData = await executeTask(dslJson.value, task.alias, value);
        const response = responseData as unknown as ApiResponse<any>;
        
        if (response.code === 0) {
          executionStatus[task.alias] = 'success';
          ElMessage.success(`任务 ${task.alias} 执行成功`);
        } else {
          executionStatus[task.alias] = 'failed';
          ElMessage.error(response.message || `任务 ${task.alias} 执行失败`);
        }
      } catch (error) {
        executionStatus[task.alias] = 'failed';
        ElMessage.error('JSON格式错误或执行出错');
      }
    }).catch(() => {
      // 取消执行
    });
  } catch (error) {
    console.error('执行任务出错:', error);
    ElMessage.error('执行任务出错');
  }
};

// 执行工作流（从按钮点击）
const executeWorkflow = () => {
  console.log('executeWorkflow 被调用了'); // 调试信息
  if (!workflow.value) return;
  
  // 更新工作流输入参数
  handleWorkflowExecute(savedParamJson.value);
};

// 处理工作流执行
const handleWorkflowExecute = (paramJson: string) => {
  console.log('handleWorkflowExecute 被调用了'); // 调试信息
  if (!workflow.value) return;
  
  // 清除执行状态
  clearExecutionStatus();

  try {
    // 验证JSON格式
    JSON.parse(paramJson);
    
    // 重置状态
    Object.keys(executionStatus).forEach(key => delete executionStatus[key]);
    
    // 设置执行中状态
    if (workflow.value.taskMap) {
      Object.keys(workflow.value.taskMap).forEach(alias => {
        executionStatus[alias] = 'running';
      });
    }
    
    // 调用执行接口
    apiExecuteWorkflow(dslJson.value, paramJson)
      .then(responseData => {
        const response = responseData as unknown as ApiResponse<Response<Object>>;
        
        if (response.code === 0 && response.data) {
          // 后端返回的数据现在是Response<Object>类型，需要从中获取实际结果
          const responseObj = response.data;
          // 执行响应
          executionResponse.value = responseObj;
          
          // 显式类型转换解决TypeScript类型检查问题
          const context = (responseObj.workflowContext || {}) as WorkflowContext;
          
          // 更新任务状态
          if (context.taskResultMap) {
            Object.entries(context.taskResultMap).forEach(([alias, result]: [string, any]) => {
              executionStatus[alias] = !context.taskExceptionMap?.[alias] ? 'success' : 'failed';
            });
          }
          
          // 处理任务异常信息
          if (context.taskExceptionMap) {
            Object.entries(context.taskExceptionMap).forEach(([alias, exception]: [string, any]) => {
              if (exception) {
                executionStatus[alias] = 'failed';
              }
            });
          }
          
          ElMessage.success('工作流执行成功');
        } else {
          // 设置失败状态
          Object.keys(executionStatus).forEach(key => {
            executionStatus[key] = 'failed';
          });
          
          ElMessage.error(response.message || '工作流执行失败');
        }
      })
      .catch(error => {
        console.error('执行工作流出错:', error);
        
        // 设置失败状态
        Object.keys(executionStatus).forEach(key => {
          executionStatus[key] = 'failed';
        });
        
        ElMessage.error('执行工作流出错');
      });
  } catch (error) {
    ElMessage.error('JSON格式错误');
  }
};
// #endregion



// #region 原始DSL记录和差异对比功能
// 保存原始DSL
const saveOriginalDsl = () => {
  if (dslJson.value) {
    originalDsl.value = dslJson.value;
    console.log('原始DSL已保存 - 长度:', originalDsl.value.length);
    // ElMessage.success('原始DSL已保存，现在可以进行编辑');
  } else {
    console.warn('保存原始DSL失败，当前DSL为空');
  }
};

// 检查DSL是否有变化
const hasDslChanged = () => {
  if (!originalDsl.value || !dslJson.value) return false;
  
  try {
    return originalDsl.value !== dslJson.value;
  } catch (error) {
    console.error('比较DSL变化时出错:', error);
    // 解析失败时，直接比较字符串
    return originalDsl.value !== dslJson.value;
  }
};

// 打开差异对比对话框
const openDslDiffViewer = () => {
  // 无论如何都显示对话框，方便用户查看内容
  showDslDiffViewer.value = true;
};

// 确认保存DSL，触发保存事件，并关闭对话框
const confirmSaveDsl = () => {
  try {
    // 触发保存事件，将DSL传递给父组件
    emit('dsl-saved', dslJson.value);
    
    // 显示成功消息
    ElMessage.success('DSL已更新');
    
    // 关闭对话框
    showDslDiffViewer.value = false;
  } catch (error) {
    console.error('保存DSL时出错:', error);
    ElMessage.error('保存DSL失败');
  }
};

// #endregion



// #region 新增功能
// 打开添加节点对话框
const openAddNodeDialog = () => {
  if (!workflow.value || !isEditing.value) {
    ElMessage.warning('请先进入编辑模式');
    return;
  }
  
  // 创建一个新的空任务
  const newTaskAlias = generateNewTaskAlias();
  
  // 创建新任务对象
  const newTask: Task = {
    alias: newTaskAlias,
    taskType: 'Calculate', // 默认类型
    description: '新节点',
    dependencyTaskMap: {}
  };
  
  // 设置为当前选中任务
  selectedTask.value = newTask;
  isNewTask.value = true;
  
  // 打开任务详情面板
  taskDetailVisible.value = true;
};

// 生成新任务别名
const generateNewTaskAlias = (): string => {
  if (!workflow.value || !workflow.value.taskMap) {
    return 'task1';
  }
  
  // 获取当前所有任务
  const tasks = Object.keys(workflow.value.taskMap);
  
  // 找到task{n}形式的最大n
  let maxNum = 0;
  for (const task of tasks) {
    if (task.startsWith('task')) {
      const numStr = task.replace('task', '');
      if (/^\d+$/.test(numStr)) {
        const num = parseInt(numStr);
        if (num > maxNum) {
          maxNum = num;
        }
      }
    }
  }
  
  // 返回新的任务名
  return `task${maxNum + 1}`;
};

// 处理删除节点
const handleDeleteNode = async (taskAlias: string) => {
  if (!workflow.value || !workflow.value.taskMap || !taskAlias) {
    return;
  }
  
  try {    
    // 创建工作流的浅拷贝，以便触发引用监听
    const updatedWorkflow = { ...workflow.value };
    
    // 删除任务
    if (updatedWorkflow.taskMap && taskAlias in updatedWorkflow.taskMap) {
      // 删除节点
      delete updatedWorkflow.taskMap[taskAlias];
      
      // 从其他任务的依赖中移除这个任务
      Object.values(updatedWorkflow.taskMap).forEach((task: Task) => {
        if (task.dependencyTaskMap && taskAlias in task.dependencyTaskMap) {
          delete task.dependencyTaskMap[taskAlias];
        }
      });
      
      // 更新工作流引用，触发Vue的引用监听
      workflow.value = updatedWorkflow;
      
      // 使用后端stringify API重组DSL，确保格式正确
      const updatedDslString = await reassembleDsl(workflow.value);
      dslJson.value = updatedDslString;
      
      // 清除执行状态
      clearExecutionStatus();
      
      // 调用后端接口解析更新后的DSL，确保数据一致性
      await parseAndUpdateWorkflow(updatedDslString);
      
      // 提示确认成功
      ElMessage.success(`节点"${taskAlias}"已删除`);
    }
  } catch (error) {
    // 用户取消操作
    if (error === 'cancel') {
      return;
    }
    
    console.error('删除节点时出错:', error);
    ElMessage.error('删除节点失败，请稍后重试');
  }
};
// #endregion
</script>

<style lang="scss" scoped>
.orchestration-visualizer {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 70px);
  padding: 0;
  overflow: hidden;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 20px;
    margin-bottom: 0;
    flex-shrink: 0;
    height: 40px;
    
    h2 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }
  
  .wrapper-container {
    flex: 1;
    min-height: 0; /* 关键：防止flex容器下overflow失效 */
    padding: 0 20px 20px;
    overflow: hidden;
    position: relative;
    height: calc(100% - 40px);
    
    /* 确保子元素正确显示 */
    display: flex;
    flex-direction: column;
    
    /* 减少DOM重绘消耗 */
    will-change: contents;
    
    /* 加快渲染，添加性能提示 */
    content-visibility: auto;
  }
  
  code {
    display: block;
    margin: 10px 0;
    padding: 6px 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-family: monospace;
  }

  // 示例选择相关样式
  .sample-option {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .sample-name {
      font-weight: bold;
      font-size: 14px;
    }
    
    .sample-desc {
      color: #606266;
      font-size: 12px;
    }
    
    .sample-file {
      color: #909399;
      font-size: 12px;
    }
  }
  
  // 对话框头部样式
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    
    h3 {
      margin: 0;
      color: #303133;
    }
  }
  
  :deep(.el-select-dropdown__item) {
    padding: 8px 12px;
    height: auto;
    white-space: normal;
    line-height: 1.5;
  }
}

// 添加全局样式，防止拖动时文本被选中
:global(.resizing) {
  user-select: none !important;
  cursor: col-resize !important;
  
  * {
    cursor: col-resize !important;
  }
}
</style>