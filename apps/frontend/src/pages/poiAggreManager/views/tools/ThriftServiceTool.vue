<template>
  <div class="thrift-service-tool">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>Thrift服务工具</span>
        </div>
      </template>
      
      <div class="card-content">
        <div class="tool-layout">
          <!-- 左侧查询表单 -->
          <div class="query-section">
            <el-form :model="formData" label-width="120px" class="service-form">
              <!-- Appkey输入 -->
              <el-form-item label="Appkey:">
                <div class="appkey-input-group">
                  <div class="appkey-input-container">
                    <el-input 
                      v-model="formData.appkey" 
                      placeholder="请输入Appkey" 
                      clearable
                      class="appkey-input"
                      @focus="handleAppkeyFocus"
                      @blur="handleAppkeyBlur"
                    />
                    <!-- 历史记录下拉菜单 -->
                    <div v-if="showAppkeyHistory && appkeyHistory.length > 0" class="appkey-history-dropdown">
                      <div class="history-header">
                        <span class="history-title">历史记录</span>
                        <el-button 
                          link
                          size="small" 
                          @click="clearAppkeyHistory"
                          class="clear-history-btn"
                        >
                          清空
                        </el-button>
                      </div>
                      <div class="history-list">
                        <div 
                          v-for="(historyAppkey, index) in appkeyHistory" 
                          :key="index"
                          class="history-item"
                          @click="selectHistoryAppkey(historyAppkey)"
                        >
                          <span class="history-appkey">{{ historyAppkey }}</span>
                          <el-button 
                            link
                            size="small"
                            class="delete-history-btn"
                            @click="removeHistoryItem(historyAppkey, $event)"
                          >
                            <el-icon><Close /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <el-select 
                    v-model="formData.env" 
                    placeholder="环境" 
                    class="env-select"
                  >
                    <el-option label="prod" value="prod" />
                    <el-option label="staging" value="staging" />
                    <el-option label="test" value="test" />
                    <el-option label="dev" value="dev" />
                  </el-select>
                  <el-button 
                    type="primary" 
                    @click="queryAppkeyHosts" 
                    :loading="loading.hostLoading"
                  >
                    获取机器
                  </el-button>
                </div>
              </el-form-item>
              
              <!-- 机器IP输入 -->
              <el-form-item label="主机名称:" required>
                <el-select 
                  v-model="formData.hostName"
                  placeholder="请选择或输入主机IP" 
                  filterable 
                  allow-create
                  clearable
                  class="host-select"
                  @change="handleHostChange"
                >
                  <el-option 
                    v-for="host in hostList" 
                    :key="host.ip" 
                    :label="host.ip + (host.name ? ` (${host.name})` : '')" 
                    :value="host.ip"
                  />
                </el-select>
              </el-form-item>
              
              <!-- 服务接口选择 -->
              <el-form-item label="服务接口:" v-if="serviceInterfaceList.length">
                <el-select 
                  v-model="formData.selectedInterface" 
                  placeholder="请选择服务接口" 
                  filterable 
                  clearable
                  @change="handleInterfaceChange"
                  class="full-width-select"
                >
                  <el-option 
                    v-for="item in serviceInterfaceList" 
                    :key="item.serviceName" 
                    :label="item.serviceName" 
                    :value="item.serviceName"
                  />
                </el-select>
              </el-form-item>
              
              <!-- 调用方法选择 -->
              <el-form-item label="调用方法:" v-if="methodList.length">
                <el-select 
                  v-model="formData.selectedMethod" 
                  placeholder="请选择调用方法" 
                  filterable 
                  clearable
                  @change="handleMethodChange"
                  class="full-width-select"
                >
                  <el-option 
                    v-for="item in methodList" 
                    :key="item" 
                    :label="item" 
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </el-form>
            
            <!-- 提示信息 -->
            <div class="empty-container" v-if="!loading.serviceLoading && !serviceInterfaceList.length">
              <el-empty description="请输入机器IP并点击查询按钮" />
            </div>
            <div class="empty-container" v-if="showNoMethodsMessage">
              <el-empty description="未找到相关方法" />
            </div>
          </div>
          
          <!-- 右侧方法信息区 -->
          <div class="info-section" v-if="formData.selectedMethod && hasMethodInfo">
            <div class="method-info-card">
              <div class="method-info-header">
                <h3>方法信息</h3>
                <el-button type="primary" @click="showJsonSchemaDialog" class="schema-button">
                  <el-icon class="icon-margin"><Document /></el-icon>查看JSON Schema
                </el-button>
              </div>
              
              <el-descriptions :column="1" border class="info-descriptions">
                <el-descriptions-item label="方法名称" label-class-name="desc-label" content-class-name="desc-content">
                  <div class="method-name">{{ currentMethod?.name || '无' }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="参数类型" label-class-name="desc-label" content-class-name="desc-content">
                  <div class="param-types">
                    <div v-for="(type, index) in currentMethod?.parameterTypes" :key="index" 
                         class="param-type-item">
                      <span class="param-index">{{ index + 1 }}.</span> {{ type }}
                    </div>
                    <div v-if="!currentMethod?.parameterTypes?.length" class="no-params">无参数</div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="返回类型" label-class-name="desc-label" content-class-name="desc-content">
                  <div class="return-type">{{ currentMethod?.returnType || '无' }}</div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
        
        <!-- 参数和返回值结构展示区域 -->
        <div v-if="hasMethodInfo" class="schema-container">
          <el-tabs v-model="activeTab" class="schema-tabs">
            <el-tab-pane label="参数结构" name="parameter">
              <div class="editor-header">
                <h3>参数编辑器</h3>
                <el-button type="primary" @click="showCallMethod" class="invoke-button">
                  <el-icon class="icon-margin"><VideoPlay /></el-icon> 调用方法
                </el-button>
              </div>
              <div class="schema-editor-container">
                <json-schema-editor 
                  :schema="jsonSchema || {}" 
                  ref="schemaEditorRef"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane label="返回结果结构" name="return">
              <div class="editor-header">
                <h3>返回结果结构</h3>
                <el-button type="primary" @click="showReturnTypeDialog" class="schema-button">
                  <el-icon class="icon-margin"><Document /></el-icon> 查看完整结构
                </el-button>
              </div>
              <div class="return-type-layout">
                <!-- 左侧：字段结构展示 -->
                <div class="return-type-structure">
                  <div class="structure-header">
                    <h4>字段结构</h4>
                  </div>
                  <div class="structure-content">
                    <div v-if="returnTypeInfo" class="return-type-preview">
                      <!-- 返回类型信息展示 -->
                      <template v-if="returnTypeInfo.typeName">
                        <div class="return-type-summary">
                          <div class="type-header">
                            <h4>{{ returnTypeInfo.simpleTypeName || returnTypeInfo.typeName }}</h4>
                            <p class="return-type-desc">{{ returnTypeInfo.description }}</p>
                          </div>
                          
                          <!-- 基本类型 -->
                          <div v-if="returnTypeInfo.isPrimitive" class="basic-type-info">
                            <el-tag type="success">基本类型</el-tag>
                          </div>
                          
                          <!-- 集合类型 -->
                          <div v-else-if="returnTypeInfo.isCollection" class="collection-type-info">
                            <el-tag type="warning">集合类型</el-tag>
                            <div class="element-type">
                              <strong>元素类型:</strong> {{ returnTypeInfo.elementType }}
                            </div>
                          </div>
                          
                          <!-- 枚举类型 -->
                          <div v-else-if="returnTypeInfo.isEnum" class="enum-type-info">
                            <el-tag type="info">枚举类型</el-tag>
                            <div class="enum-values">
                              <strong>可选值:</strong>
                              <el-tag v-for="enumValue in returnTypeInfo.enumValues" :key="enumValue" size="small" class="enum-tag">
                                {{ enumValue }}
                              </el-tag>
                            </div>
                          </div>
                          
                          <!-- 复杂对象类型 -->
                          <div v-else-if="returnTypeInfo.fields" class="object-type-info">
                            <el-tag type="primary">对象类型</el-tag>
                            <div class="fields-info">
                              <div class="nested-fields-tree">
                                <NestedFieldsTree 
                                  :fields="returnTypeInfo.fields" 
                                  :level="0"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <div v-else class="empty-return-type">
                      <el-empty description="未找到返回结果结构信息" />
                    </div>
                  </div>
                </div>
                
                <!-- 右侧：示例展示 -->
                <div class="return-type-example">
                  <div class="example-header">
                    <h4>返回示例</h4>
                  </div>
                  <div class="example-content">
                    <div v-if="returnTypeInfo && returnTypeInfo.exampleValue" class="example-json-container">
                      <pre class="example-json">{{ formatJson(returnTypeInfo.exampleValue) }}</pre>
                    </div>
                    <div v-else-if="returnTypeInfo && (returnTypeInfo.isPrimitive || returnTypeInfo.isCollection || returnTypeInfo.isEnum)" class="simple-example">
                      <div class="example-value">
                        <strong>示例值:</strong>
                        <pre class="example-json">{{ JSON.stringify(returnTypeInfo.exampleValue, null, 2) }}</pre>
                      </div>
                    </div>
                    <div v-else class="no-example">
                      <el-empty description="暂无示例数据" />
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <!-- 提示信息 -->
        <div class="empty-container" v-if="showNoSchemaMessage">
          <el-empty description="未找到相关参数信息" />
        </div>
      </div>
    </el-card>
    
    <!-- JSON Schema 对话框 -->
    <el-dialog
      v-model="jsonSchemaDialogVisible"
      title="完整 JSON Schema"
      width="70%"
      class="schema-dialog"
    >
      <pre class="json-schema">{{ formatJson(jsonSchema) }}</pre>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="jsonSchemaDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyJsonSchema">
            <el-icon class="icon-margin"><Document /></el-icon>复制
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 方法调用对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="方法调用"
      width="60%"
      :close-on-click-modal="false"
      class="call-dialog"
    >
      <div class="call-method-dialog">
        <div class="method-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="方法名称">
              {{ currentMethod?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="服务名称">
              {{ formData.selectedInterface }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="params-section">
          <h4>参数内容</h4>
          <div class="params-preview">
            <pre>{{ formatJson(methodParams) }}</pre>
          </div>
        </div>
        
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="callMethod" :loading="loading.callLoading">
            <el-icon class="icon-margin"><VideoPlay /></el-icon>调用
          </el-button>
        </div>
      </div>
    </el-dialog>
    
    <!-- 调用结果对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      title="调用结果"
      width="60%"
      class="result-dialog"
    >
      <div class="result-dialog-content">
        <el-alert
          :title="callSuccess ? '调用成功' : '调用失败'"
          :type="callSuccess ? 'success' : 'error'"
          :description="callMessage"
          show-icon
          :closable="false"
        />
        
        <div class="result-section">
          <h4>返回数据</h4>
          <div class="result-preview">
            <pre>{{ formatJson(callResult) }}</pre>
          </div>
        </div>
        
        <div class="dialog-footer">
          <el-button @click="resultDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyResult" v-if="callSuccess">
            <el-icon class="icon-margin"><Document /></el-icon>复制结果
          </el-button>
        </div>
      </div>
    </el-dialog>
    
    <!-- 返回类型结构对话框 -->
    <el-dialog
      v-model="returnTypeDialogVisible"
      title="返回结果结构"
      width="70%"
      class="schema-dialog"
    >
      <pre class="json-schema">{{ formatJson(returnTypeInfo) }}</pre>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="returnTypeDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyReturnTypeSchema">
            <el-icon class="icon-margin"><Document /></el-icon>复制
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, defineAsyncComponent } from 'vue';
import { ElMessage } from 'element-plus';
import { VideoPlay, Document, Close, InfoFilled, ArrowDown, ArrowRight } from '@element-plus/icons-vue';
import { 
  getServiceInterfaces, 
  getServiceMethods, 
  getMethodJsonSchema,
  getAppkeyHosts 
} from '../../request/thriftServiceTool';
import type {
  ServiceInterface,
  ServiceInterfaceResponse,
  ServiceMethodResponse,
  JsonSchemaResponse,
  ServiceInfo,
  ServiceMethod,
  RocketHost,
  FieldInfo
} from '../../types/thriftServiceTool';
import NestedFieldsTree from '../../components/NestedFieldsTree.vue';
// 使用动态导入
const JsonSchemaEditor = defineAsyncComponent(() => import('../../components/thrifttask/JsonSchemaEditor.vue'));

// 表单数据
const formData = reactive({
  appkey: '',
  env: 'test',
  hostName: '',
  selectedInterface: '',
  selectedMethod: ''
});

// 历史记录相关
const appkeyHistory = ref<string[]>([]);
const showAppkeyHistory = ref(false);
const APPKEY_HISTORY_KEY = 'thrift_appkey_history';
const MAX_HISTORY_SIZE = 10;

// 加载状态
const loading = reactive({
  serviceLoading: false,
  methodLoading: false,
  schemaLoading: false,
  callLoading: false,
  hostLoading: false
});

// 数据列表
const serviceInterfaceList = ref<ServiceInterface[]>([]);
const methodList = ref<string[]>([]);
const jsonSchema = ref<Record<string, any> | null>(null);
const schemaEditorRef = ref<any>(null);
const hostList = ref<{ ip: string; name?: string }[]>([]);
const activeTab = ref('parameter');

// 对话框
const dialogVisible = ref(false);
const resultDialogVisible = ref(false);
const jsonSchemaDialogVisible = ref(false);
const returnTypeDialogVisible = ref(false);
const methodParams = ref<any>({});
const callResult = ref<any>(null);
const callSuccess = ref(false);
const callMessage = ref('');

// 计算属性
const hasMethodInfo = computed(() => 
  jsonSchema.value?.methods && jsonSchema.value.methods.length > 0
);

const hasTypeInfo = computed(() => 
  jsonSchema.value?.types && jsonSchema.value.types.length > 0
);

const currentMethod = computed(() => 
  hasMethodInfo.value ? jsonSchema.value?.methods[0] : null
);

// 返回结果类型信息
const returnTypeInfo = computed(() => {
  if (jsonSchema.value?.returnType) {
    console.log('使用返回类型信息:', jsonSchema.value.returnType);
    console.log('字段信息:', jsonSchema.value.returnType.fields);
    console.log('字段数量:', jsonSchema.value.returnType.fields ? Object.keys(jsonSchema.value.returnType.fields).length : 0);
    return jsonSchema.value.returnType;
  }
  
  console.log('缺少返回类型信息');
  return null;
});

const showNoMethodsMessage = computed(() => 
  !loading.methodLoading && 
  serviceInterfaceList.value.length > 0 && 
  methodList.value.length === 0 && 
  formData.selectedInterface
);

const showNoSchemaMessage = computed(() => 
  !loading.schemaLoading && 
  formData.selectedMethod && 
  !jsonSchema.value
);

// 显示JSON Schema对话框
const showJsonSchemaDialog = () => {
  jsonSchemaDialogVisible.value = true;
};

// 显示返回类型对话框
const showReturnTypeDialog = () => {
  if (returnTypeInfo.value) {
    returnTypeDialogVisible.value = true;
  } else {
    ElMessage.warning('未找到返回结果结构信息');
  }
};

// 复制JSON Schema
const copyJsonSchema = () => {
  try {
    navigator.clipboard.writeText(formatJson(jsonSchema.value));
    ElMessage.success('复制成功');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败');
  }
};

// 方法 - 加载历史记录
const loadAppkeyHistory = () => {
  try {
    const historyStr = localStorage.getItem(APPKEY_HISTORY_KEY);
    if (historyStr) {
      appkeyHistory.value = JSON.parse(historyStr);
    }
  } catch (error) {
    console.error('加载appkey历史记录失败:', error);
    appkeyHistory.value = [];
  }
};

// 方法 - 保存appkey到历史记录
const saveAppkeyToHistory = (appkey: string) => {
  if (!appkey || appkey.trim() === '') return;
  
  try {
    // 移除已存在的相同appkey
    const filteredHistory = appkeyHistory.value.filter(item => item !== appkey);
    
    // 添加到开头
    const newHistory = [appkey, ...filteredHistory];
    
    // 限制历史记录数量
    if (newHistory.length > MAX_HISTORY_SIZE) {
      newHistory.splice(MAX_HISTORY_SIZE);
    }
    
    appkeyHistory.value = newHistory;
    localStorage.setItem(APPKEY_HISTORY_KEY, JSON.stringify(newHistory));
  } catch (error) {
    console.error('保存appkey历史记录失败:', error);
  }
};

// 方法 - 选择历史记录
const selectHistoryAppkey = (appkey: string) => {
  formData.appkey = appkey;
  showAppkeyHistory.value = false;
  
  // 自动触发获取机器
  queryAppkeyHosts();
};

// 方法 - 删除历史记录项
const removeHistoryItem = (appkey: string, event: Event) => {
  event.stopPropagation();
  
  try {
    appkeyHistory.value = appkeyHistory.value.filter(item => item !== appkey);
    localStorage.setItem(APPKEY_HISTORY_KEY, JSON.stringify(appkeyHistory.value));
    ElMessage.success('已删除历史记录');
  } catch (error) {
    console.error('删除历史记录失败:', error);
    ElMessage.error('删除历史记录失败');
  }
};

// 方法 - 清空历史记录
const clearAppkeyHistory = () => {
  try {
    appkeyHistory.value = [];
    localStorage.removeItem(APPKEY_HISTORY_KEY);
    showAppkeyHistory.value = false;
    ElMessage.success('已清空历史记录');
  } catch (error) {
    console.error('清空历史记录失败:', error);
    ElMessage.error('清空历史记录失败');
  }
};

// 方法 - 处理输入框失焦
const handleAppkeyBlur = () => {
  setTimeout(() => {
    showAppkeyHistory.value = false;
  }, 200);
};

// 方法 - 处理输入框聚焦
const handleAppkeyFocus = () => {
  showAppkeyHistory.value = appkeyHistory.value.length > 0;
};

// 方法 - 查询Appkey的主机列表
const queryAppkeyHosts = async () => {
  if (!formData.appkey) {
    ElMessage.warning('请输入Appkey');
    return;
  }

  try {
    loading.hostLoading = true;
    
    // 清空主机、接口、方法选择
    formData.hostName = '';
    formData.selectedInterface = '';
    formData.selectedMethod = '';
    serviceInterfaceList.value = [];
    methodList.value = [];
    jsonSchema.value = null;
    hostList.value = [];
    
    const response = await getAppkeyHosts({
      appkey: formData.appkey,
      env: formData.env
    });
    
    if (response?.code === 0) {
      // 处理后端直接返回数组的情况
      if (Array.isArray(response.data)) {
        hostList.value = response.data.map((host: { id: string | number; ipLan?: string; ip?: string; name?: string }) => ({
          id: host.id,
          ip: host.ipLan || host.ip || '', // 优先使用ipLan字段，兼容ip字段
          name: host.name || ''
        }));
      } else if (response.data?.hosts && Array.isArray(response.data.hosts)) {
        // 处理后端返回{hosts:[...]}的情况
        hostList.value = response.data.hosts;
      } else {
        hostList.value = [];
      }
      
      if (hostList.value.length === 0) {
        ElMessage.info('未找到相关机器列表');
      } else {
        ElMessage.success(`已获取到${hostList.value.length}台机器`);
        // 成功获取机器列表后，保存appkey到历史记录
        saveAppkeyToHistory(formData.appkey);
      }
    } else {
      ElMessage.error(response?.message || '获取机器列表失败');
    }
  } catch (error) {
    console.error('查询机器列表失败:', error);
    ElMessage.error('查询机器列表失败，请重试');
  } finally {
    loading.hostLoading = false;
  }
};

// 方法 - 查询服务接口信息
const queryServiceInterfaces = async () => {
  if (!formData.hostName) {
    ElMessage.warning('请输入主机IP或名称');
    return;
  }

  try {
    loading.serviceLoading = true;
    
    const response = await getServiceInterfaces({ hostName: formData.hostName });
    
    if (response?.code === 0) {
      const serviceInterfaceDTO = response.data;
      
      if (!serviceInterfaceDTO?.serviceInfo?.[0]?.serviceIfaceInfos) {
        ElMessage.warning('返回数据结构异常');
        return;
      }
      
      // 直接使用后端返回的扁平化接口列表
      serviceInterfaceList.value = serviceInterfaceDTO.serviceInfo[0].serviceIfaceInfos;
      resetSelection();
      
      if (serviceInterfaceList.value.length === 0) {
        ElMessage.info('未找到服务接口信息');
      }
    } else {
      ElMessage.error(response?.message || '获取服务接口失败');
    }
  } catch (error) {
    console.error('查询服务接口失败:', error);
    if (error instanceof Error) {
      ElMessage.error(`查询服务接口失败: ${error.message}`);
    } else {
      ElMessage.error('查询服务接口失败，请重试');
    }
  } finally {
    loading.serviceLoading = false;
  }
};

// 方法 - 重置选择
const resetSelection = () => {
  formData.selectedInterface = '';
  formData.selectedMethod = '';
  methodList.value = [];
  jsonSchema.value = null;
};

// 方法 - 接口选择变更
const handleInterfaceChange = async () => {
  if (!formData.selectedInterface) {
    methodList.value = [];
    jsonSchema.value = null;
    return;
  }

  try {
    loading.methodLoading = true;
    
    const response = await getServiceMethods({
      hostName: formData.hostName,
      serviceName: formData.selectedInterface
    });
    
    if (response?.code === 0) {
      const serviceMethodDTO = response.data;
      
      // 后端已经过滤，直接使用第一个服务的方法列表（如果存在）
      methodList.value = serviceMethodDTO.serviceMethods?.[0]?.methods || [];
      formData.selectedMethod = '';
      jsonSchema.value = null;
      
      if (methodList.value.length === 0) {
        ElMessage.info('未找到方法信息');
      }
    } else {
      ElMessage.error(response?.message || '获取服务方法失败');
    }
  } catch (error) {
    console.error('查询方法失败:', error);
    ElMessage.error('查询方法失败，请重试');
  } finally {
    loading.methodLoading = false;
  }
};

// 方法 - 方法选择变更
const handleMethodChange = async () => {
  if (!formData.selectedMethod) {
    jsonSchema.value = null;
    return;
  }

  try {
    loading.schemaLoading = true;
    
    // 不需要额外处理方法名，后端会自动提取纯方法名
    const response = await getMethodJsonSchema({
      hostName: formData.hostName,
      serviceName: formData.selectedInterface,
      methodName: formData.selectedMethod
    });
    
    console.log('获取到方法JSON Schema:', response);
    
    if (response?.code === 0) {
      jsonSchema.value = response.data;
      console.log('设置jsonSchema:', jsonSchema.value);
      
      if (!hasMethodInfo.value) {
        console.warn('未找到方法信息，接口返回:', response.data);
        ElMessage.info('未找到参数信息');
      }
    } else {
      console.error('获取JSON Schema失败:', response?.message);
      ElMessage.error(response?.message || '获取参数信息失败');
    }
  } catch (error) {
    console.error('查询参数信息失败:', error);
    ElMessage.error('查询参数信息失败，请重试');
  } finally {
    loading.schemaLoading = false;
  }
};

// 方法 - 格式化JSON
const formatJson = (json: any) => {
  return JSON.stringify(json, null, 2);
};

// 方法 - 显示方法调用对话框
const showCallMethod = () => {
  try {
    if (!loading.callLoading) {
      if (!jsonSchema.value) {
        console.warn('jsonSchema为空');
        ElMessage.warning('参数信息不完整，无法调用方法');
        return;
      }
      
      if (!schemaEditorRef.value) {
        console.warn('schemaEditorRef为空');
        ElMessage.warning('编辑器组件未初始化，请刷新页面重试');
        return;
      }
      
      console.log('准备从Schema编辑器获取参数');
      const params = schemaEditorRef.value.buildJsonFromSchema() || {};
      console.log('获取到的参数:', JSON.stringify(params, null, 2));
      
      methodParams.value = params;
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('显示方法调用对话框出错:', error);
    ElMessage.error('准备调用参数时出错，请刷新页面重试');
  }
};

// 方法 - 调用方法
const callMethod = async () => {
  loading.callLoading = true;
  
  try {
    // 模拟调用方法
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 在这里添加实际的方法调用逻辑
    // 例如: const response = await callThriftMethod(formData.hostName, formData.selectedInterface, currentMethod.value?.name, methodParams.value);
    
    // 模拟成功响应
    callResult.value = {
      success: true,
      data: {
        result: "模拟调用返回结果",
        timestamp: new Date().toISOString()
      }
    };
    
    callSuccess.value = true;
    callMessage.value = '方法调用成功';
    
    // 关闭调用对话框，显示结果对话框
    dialogVisible.value = false;
    resultDialogVisible.value = true;
  } catch (error) {
    console.error('方法调用失败:', error);
    
    callSuccess.value = false;
    callMessage.value = error instanceof Error ? error.message : '未知错误';
    callResult.value = { error: callMessage.value };
    
    dialogVisible.value = false;
    resultDialogVisible.value = true;
  } finally {
    loading.callLoading = false;
  }
};

// 方法 - 复制结果
const copyResult = () => {
  const resultStr = JSON.stringify(callResult.value, null, 2);
  navigator.clipboard.writeText(resultStr)
    .then(() => {
      ElMessage.success('结果已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
      ElMessage.error('复制失败，请手动复制');
    });
};

// 方法 - 复制返回类型结构
const copyReturnTypeSchema = () => {
  try {
    navigator.clipboard.writeText(formatJson(returnTypeInfo.value));
    ElMessage.success('返回类型结构已复制到剪贴板');
  } catch (error) {
    console.error('复制返回类型结构失败:', error);
    ElMessage.error('复制返回类型结构失败，请手动复制');
  }
};

// 方法 - 主机选择变更
const handleHostChange = () => {
  if (formData.hostName) {
    queryServiceInterfaces();
  }
};

// 组件挂载时自动查询
onMounted(() => {
  // 加载历史记录
  loadAppkeyHistory();
  
  if (formData.hostName) {
    queryServiceInterfaces();
  }
});

// 辅助函数 - 判断是否为集合类型字段
const isCollectionFieldType = (type: string) => {
  if (!type) return false;
  
  const lowerType = type.toLowerCase();
  return lowerType.includes('list') || 
         lowerType.includes('set') || 
         lowerType.includes('collection') ||
         lowerType.startsWith('java.util.list') ||
         lowerType.startsWith('java.util.set') ||
         lowerType.startsWith('java.util.collection');
};
</script>

<style lang="scss" scoped>
.thrift-service-tool {
  padding: 24px;
  min-height: calc(100vh - 100px);
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  
  .main-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    border: none;
    
    :deep(.el-card__header) {
      padding: 16px 24px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fafbfc;
    }
  }

  :deep(.el-card__body) {
    flex: 1;
    overflow: auto;
    padding: 24px;
  }
  
  .card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 500px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }
  
  .service-form {
    max-width: 700px;
    margin-bottom: 20px;
    
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #606266;
    }
    
    .appkey-input-group {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .appkey-input-container {
        position: relative;
        flex: 1;
        
        .appkey-input {
          width: 400px;
        }
      }
      
      .env-select {
        flex: 0 0 auto;
        width: 90px;
      }
      
      .appkey-history-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        background-color: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        padding: 8px 0;
        margin-top: 4px;
        max-height: 280px;
        overflow-y: auto;
        
        .history-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 16px;
          border-bottom: 1px solid #ebeef5;
          margin-bottom: 4px;
          
          .history-title {
            font-size: 12px;
            font-weight: 500;
            color: #909399;
          }
          
          .clear-history-btn {
            padding: 0;
            height: auto;
            font-size: 12px;
            color: #409eff;
            
            &:hover {
              color: #66b1ff;
            }
          }
        }
        
        .history-list {
          max-height: 200px;
          overflow-y: auto;
          
          .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.2s;
            
            &:hover {
              background-color: #f5f7fa;
            }
            
            .history-appkey {
              flex: 1;
              font-size: 14px;
              color: #606266;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            
            .delete-history-btn {
              padding: 4px;
              margin-left: 8px;
              opacity: 0;
              transition: opacity 0.2s;
              color: #c0c4cc;
              
              &:hover {
                color: #f56c6c;
              }
            }
            
            &:hover .delete-history-btn {
              opacity: 1;
            }
          }
        }
      }
    }
    
    .host-select {
      width: 500px;
    }
  }
  
  .input-with-btn {
    width: 100%;
    max-width: 500px;
  }
  
  .full-width-select {
    width: 100%;
    max-width: 500px;
  }
  
  .tool-layout {
    display: flex;
    flex-direction: row;
    gap: 24px;
    margin-bottom: 24px;
    flex-wrap: wrap;
  }
  
  .query-section {
    flex: 1;
    min-width: 340px;
  }
  
  .info-section {
    flex: 1;
    min-width: 340px;
  }
  
  .method-info-card {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    border: 1px solid #ebeef5;
    
    .method-info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        color: #303133;
      }
    }
    
    .info-descriptions {
      :deep(.el-descriptions__header) {
        display: none;
      }
      
      :deep(.desc-label) {
        background-color: #f5f7fa;
        width: 100px;
        font-weight: 500;
      }
      
      :deep(.desc-content) {
        word-break: break-all;
      }
      
      .method-name {
        font-weight: 500;
        color: #409EFF;
      }
      
      .param-types {
        .param-type-item {
          margin-bottom: 8px;
          position: relative;
          padding-left: 24px;
          color: #606266;
          
          .param-index {
            position: absolute;
            left: 0;
            font-weight: 500;
            color: #333;
          }
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        .no-params {
          color: #909399;
          font-style: italic;
        }
      }
      
      .return-type {
        color: #67c23a;
        font-weight: 500;
      }
    }
  }
  
  .schema-container {
    margin-top: 24px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    padding: 20px;
    border: 1px solid #ebeef5;
  }
  
  .schema-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }
  
  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: #303133;
    }
  }
  
  .editor-actions {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }
  
  .schema-editor-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    height: 600px;
    overflow: hidden;
  }
  
  .return-type-layout {
    display: flex;
    flex-direction: row;
    gap: 24px;
    height: 600px;
  }
  
  .return-type-structure {
    flex: 1;
    min-width: 340px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .return-type-example {
    flex: 1;
    min-width: 340px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .structure-header,
  .example-header {
    background-color: #f5f7fa;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #303133;
    }
  }
  
  .structure-content,
  .example-content {
    flex: 1;
    overflow: auto;
    padding: 16px;
  }
  
  .example-json-container,
  .simple-example {
    height: 100%;
    
    .example-json {
      background-color: #f8fafc;
      padding: 16px;
      border-radius: 8px;
      font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
      font-size: 13px;
      line-height: 1.5;
      white-space: pre-wrap;
      overflow: auto;
      border: 1px solid #ebeef5;
      color: #303133;
      height: 100%;
      margin: 0;
    }
  }
  
  .simple-example {
    .example-value {
      strong {
        display: block;
        margin-bottom: 8px;
        color: #303133;
        font-size: 14px;
      }
    }
  }
  
  .no-example {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .type-header {
    margin-bottom: 16px;
    
    h4 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    .return-type-desc {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .return-type-summary {
    .basic-type-info,
    .collection-type-info,
    .enum-type-info,
    .object-type-info {
      margin-bottom: 16px;
      
      .el-tag {
        margin-bottom: 8px;
      }
      
      .element-type {
        margin: 8px 0;
        font-size: 14px;
        color: #606266;
        
        strong {
          color: #303133;
        }
      }
      
      .enum-values {
        margin: 8px 0;
        
        strong {
          display: block;
          margin-bottom: 8px;
          color: #303133;
        }
        
        .enum-tag {
          margin: 2px 4px 2px 0;
        }
      }
      
      .fields-info {
        margin-top: 16px;
        
        .nested-fields-tree {
          border: 1px solid #ebeef5;
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }
  
  .empty-return-type {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    margin-top: 50px;
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  }
  
  .json-schema {
    background-color: #f8fafc;
    padding: 20px;
    border-radius: 8px;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    overflow-x: auto;
    height: 500px;
    overflow-y: auto;
    border: 1px solid #ebeef5;
    color: #303133;
  }
  
  .call-method-dialog,
  .result-dialog-content {
    .method-info {
      margin-bottom: 24px;
    }
    
    .params-section,
    .result-section {
      h4 {
        font-size: 16px;
        margin: 0 0 12px 0;
        color: #303133;
        font-weight: 500;
      }
    }
    
    .params-preview,
    .result-preview {
      background-color: #f8fafc;
      padding: 16px;
      border-radius: 8px;
      margin: 16px 0;
      max-height: 300px;
      overflow: auto;
      border: 1px solid #ebeef5;
      
      pre {
        margin: 0;
        font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
        color: #303133;
      }
    }
  }
  
  .dialog-footer {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  
  .schema-button,
  .invoke-button {
    font-weight: 500;
  }
  
  .icon-margin {
    margin-right: 4px;
    vertical-align: middle;
  }
  
  // 对话框样式
  :deep(.el-dialog__header) {
    padding: 20px 24px;
    margin-right: 0;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafbfc;
    
    .el-dialog__title {
      font-weight: 600;
      font-size: 18px;
      color: #303133;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 24px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background-color: #fafbfc;
  }
}
</style>

