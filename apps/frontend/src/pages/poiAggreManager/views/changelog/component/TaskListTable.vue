<template>
  <div class="task-list-table">
    <el-card class="task-list-card">
      <template #header>
        <div class="card-header">
          <span>{{ title }}</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="任务类型">
          <el-select v-model="searchForm.taskType" placeholder="请选择任务类型" clearable style="width: 200px">
            <el-option
              v-for="type in taskTypeOptions"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select v-model="searchForm.status" placeholder="请选择任务状态" clearable style="width: 200px">
            <el-option
              v-for="status in taskStatusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table v-loading="tableLoading" :data="tableData" style="width: 100%">
          <el-table-column prop="taskType" label="变更类型">
            <template #default="{ row }">
              {{ getTaskTypeName(row.taskType) }}
            </template>
          </el-table-column>
          <el-table-column prop="taskName" label="变更描述" show-overflow-tooltip />
          <el-table-column prop="opName" label="发起人姓名" />
          <el-table-column prop="opMis" label="发起人Mis" />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="ctime" label="发起时间">
            <template #default="{ row }">
              {{ formatTimestamp(row.ctime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { TaskItem, TaskQueryParams } from '../../../types'
import { TaskStatus, TaskType } from '../../../types'

const props = defineProps<{
  title: string
  fetchData: (params: TaskQueryParams) => Promise<any>
}>()

const emit = defineEmits<{
  (e: 'view', row: TaskItem): void
}>()

// 任务类型选项
const taskTypeOptions = [
  { label: '查询字段', value: TaskType.QUERY_FIELD },
  { label: '服务编排', value: TaskType.SERVICE_ORCHESTRATION },
  { label: '同步字段', value: TaskType.SYNC_FIELD },
  { label: '同步配置', value: TaskType.SYNC_CONFIG },
  { label: '接入指引', value: TaskType.ACCESS_GUIDE }
]

// 任务状态选项
const taskStatusOptions = [
  { label: '通过', value: TaskStatus.PASSED },
  { label: '进行中', value: TaskStatus.PROCESSING },
  { label: '撤销', value: TaskStatus.REVOKED },
  { label: '驳回', value: TaskStatus.REJECTED },
  { label: '异常', value: TaskStatus.EXCEPTION }
]

// 搜索表单
const searchForm = reactive<TaskQueryParams>({
  taskType: undefined,
  status: undefined,
  pageNo: 1,
  pageSize: 10
})

// 表格数据
const tableData = ref<TaskItem[]>([])
const tableLoading = ref(false)

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 获取任务类型名称
const getTaskTypeName = (type: TaskType): string => {
  const option = taskTypeOptions.find(opt => opt.value === type)
  return option ? option.label : String(type)
}

// 获取任务状态名称
const getStatusName = (status: TaskStatus): string => {
  const option = taskStatusOptions.find(opt => opt.value === status)
  return option ? option.label : String(status)
}

// 获取状态标签类型
const getStatusTagType = (status: TaskStatus): string => {
  switch (status) {
    case TaskStatus.PASSED:
      return 'success'
    case TaskStatus.PROCESSING:
      return 'warning'
    case TaskStatus.REVOKED:
      return 'info'
    case TaskStatus.REJECTED:
      return 'danger'
    case TaskStatus.EXCEPTION:
      return 'danger'
    default:
      return 'info'
  }
}

// 格式化时间戳
const formatTimestamp = (timestamp: number | string | undefined): string => {
  if (!timestamp) return '-'
  const date = new Date(Number(timestamp) * 1000)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 获取数据列表
const fetchData = async () => {
  try {
    tableLoading.value = true
    const params = {
      ...searchForm,
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize
    }
    const res = await props.fetchData(params)
    if (res.code === 0) {
      tableData.value = res.data.records
      pagination.total = res.data.total
    } else {
      ElMessage.error(res.message || '加载数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    tableLoading.value = false
  }
}

// 搜索
const handleSearch = async () => {
  pagination.currentPage = 1
  await fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.taskType = undefined
  searchForm.status = undefined
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  fetchData()
}

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  fetchData()
}

// 查看详情
const handleView = (row: TaskItem) => {
  if (row.mcmUrl) {
    window.open(row.mcmUrl, '_blank')
  } else {
    ElMessage.warning('该任务没有关联的MCM链接')
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.task-list-table {
  padding: 10px 20px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;

  .task-list-card {
    width: 100%;
    box-sizing: border-box;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
    }

    .search-form {
      margin-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: flex-start;

      :deep(.el-form-item) {
        margin-bottom: 0;
        margin-right: 0;

        .el-form-item__label {
          width: 80px;
          text-align: right;
        }

        .el-form-item__content {
          width: 200px;
        }

        &.el-form-item--small {
          .el-form-item__label {
            line-height: 32px;
          }
        }
      }

      .el-button {
        margin-left: 32px;
        & + .el-button {
          margin-left: 16px;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }

    .table-container {
      width: 100%;
      overflow-x: auto;

      :deep(.el-table) {
        --el-table-border-color: var(--el-border-color-lighter);
        width: 100%;

        .el-table__body-wrapper {
          overflow-x: auto !important;
        }

        .el-table__fixed-right {
          height: 100% !important;
          box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
        }
      }
    }
  }
}
</style> 