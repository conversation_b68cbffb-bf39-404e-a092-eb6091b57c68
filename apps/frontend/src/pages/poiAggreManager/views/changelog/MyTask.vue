<template>
  <div class="my-task-page">
    <task-list-table
      title="我的申请"
      :fetch-data="fetchMyTaskList"
      @view="handleView"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import TaskListTable from './component/TaskListTable.vue'
import { getMyTaskList } from '../../request'

const router = useRouter()

// 获取我的任务列表数据
const fetchMyTaskList = async (params: any) => {
  return await getMyTaskList(params)
}

// 查看任务详情
const handleView = (row: any) => {
  // 跳转到任务详情页
  router.push({
    path: '/task/detail',
    query: { id: row.id }
  })
}
</script>

<style lang="scss" scoped>
.my-task-page {
  width: 100%;
  height: 100%;
}
</style> 