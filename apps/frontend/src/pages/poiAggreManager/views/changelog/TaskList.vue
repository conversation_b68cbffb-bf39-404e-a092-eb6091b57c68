<template>
  <div class="task-list-page">
    <task-list-table
      title="变更列表"
      :fetch-data="fetchTaskList"
      @view="handleView"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import TaskListTable from './component/TaskListTable.vue'
import { getTaskList } from '../../request'

const router = useRouter()

// 获取任务列表数据
const fetchTaskList = async (params: any) => {
  return await getTaskList(params)
}

// 查看任务详情
const handleView = (row: any) => {
  // 跳转到任务详情页
  router.push({
    path: '/task/detail',
    query: { id: row.id }
  })
}
</script>

<style lang="scss" scoped>
.task-list-page {
  width: 100%;
  height: 100%;
}
</style> 