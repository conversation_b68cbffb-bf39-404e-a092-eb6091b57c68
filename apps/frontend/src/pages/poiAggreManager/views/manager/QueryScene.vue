<template>
  <div class="query-scene-page">
    <el-card class="query-scene-card">
      <template #header>
        <div class="card-header">
          <span>查询场景管理</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :model="searchForm" class="search-form" inline>
        <el-form-item label="场景编码">
          <el-input
            v-model="searchForm.sceneCode"
            placeholder="请输入场景编码"
            clearable
            @keyup.enter="handleSearch"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="场景名称">
          <el-input
            v-model="searchForm.sceneName"
            placeholder="请输入场景名称"
            clearable
            @keyup.enter="handleSearch"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="场景级别">
          <el-select v-model="searchForm.sceneLevel" placeholder="请选择" clearable style="width: 100px">
            <el-option
              v-for="item in SCENE_LEVEL_OPTIONS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="状态">
          <el-select v-model="searchForm.valid" placeholder="请选择" clearable style="width: 100px">
            <el-option label="有效" :value="1" />
            <el-option label="无效" :value="0" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="table-operations">
        <el-button type="primary" @click="handleAdd">新增场景</el-button>
        <el-button type="danger" :disabled="!selectedRows.length" @click="handleBatchDelete">批量删除</el-button>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40" fixed="left" />
          <el-table-column prop="id" label="ID" width="50" fixed="left" />
          <el-table-column prop="appkeys" label="接入服务Appkeys" :width="columnWidths.appkeys" fixed>
            <template #default="{ row }">
              <div class="tag-group">
                <el-tag
                  v-for="(appkey, index) in row.appkeys?.filter((item: string) => item && item.trim() !== '')"
                  :key="index"
                  class="tag-item"
                  type="info"
                  effect="plain"
                >
                  {{ appkey }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sceneName" label="场景名称" :width="columnWidths.sceneName">
            <template #default="{ row }">
              <span class="scene-name">{{ row.sceneName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="sceneDescription" label="场景描述" min-width="240" show-overflow-tooltip />
          <el-table-column prop="sceneCode" label="场景编码" :width="columnWidths.sceneCode">
            <template #default="{ row }">
              <span class="scene-code">{{ row.sceneCode }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="prdWikis" label="PRD/技术方案Wiki" :width="columnWidths.prdWikis">
            <template #default="{ row }">
              <div class="tag-group" v-if="row.prdWikis?.length">
                <el-tag
                  v-for="(wiki, index) in row.prdWikis.filter((w: string) => w && w.trim() !== '')"
                  :key="index"
                  class="tag-item"
                  type="info"
                  effect="plain"
                >
                  {{ wiki }}
                </el-tag>
              </div>
              <span v-else class="empty-text">无</span>
            </template>
          </el-table-column>
          <el-table-column prop="appType" label="服务类型" :width="columnWidths.appType">
            <template #default="{ row }">
              <el-tag type="success" effect="plain">
                {{ APP_TYPE_OPTIONS.find(item => item.value === row.appType)?.label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="administrator" label="管理员" :width="columnWidths.administrator">
            <template #default="{ row }">
              <div class="tag-group">
                <el-tag
                  v-for="(admin, index) in row.administrator?.filter((a: string) => a && a.trim() !== '')"
                  :key="index"
                  class="tag-item"
                  type="warning"
                  effect="plain"
                >
                  {{ admin }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="sceneLevel" label="场景级别" :width="columnWidths.sceneLevel">
            <template #default="{ row }">
              {{ SCENE_LEVEL_OPTIONS.find(item => item.value === row.sceneLevel)?.label }}
            </template>
          </el-table-column>
          <el-table-column prop="peakPeriod" label="高峰期" :width="columnWidths.peakPeriod" />
          <el-table-column prop="fieldCodes" label="使用字段" :width="columnWidths.fieldCodes">
            <template #default="{ row }">
              <div class="tag-group">
                <el-tag
                  v-for="(field, index) in row.fieldCodes?.filter((f: string) => f && f.trim() !== '')"
                  :key="index"
                  class="tag-item"
                  type="warning"
                  effect="plain"
                >
                  {{ field }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="dependencyType" label="依赖类型" :width="columnWidths.dependencyType">
            <template #default="{ row }">
              {{ SCENE_TYPE_OPTIONS.find(item => item.value === row.dependencyType)?.label }}
            </template>
          </el-table-column>
          <el-table-column prop="estimateQps" label="预估QPS" :width="columnWidths.estimateQps" />
          <el-table-column prop="actualQps" label="实际QPS" :width="columnWidths.actualQps" />
          <!-- <el-table-column prop="valid" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.valid === 1 ? 'success' : 'danger'">
                {{ row.valid === 1 ? '有效' : '无效' }}
              </el-tag>
            </template>
          </el-table-column> -->
          <el-table-column prop="remark" label="备注" min-width="240" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.remark || '无' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="opMis" label="操作人MIS" :width="columnWidths.opMis" />
          <el-table-column prop="utime" label="更新时间" :width="columnWidths.utime">
            <template #default="{ row }">
              {{ formatTimestamp(row.utime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                class="manage-btn"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                plain
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNo"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 新增/编辑场景对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="60%"
        :close-on-click-modal="false"
        :before-close="handleDialogClose"
        align-center
        class="scene-dialog"
      >
        <query-scene-form
          ref="sceneFormRef"
          :initial-data="currentScene"
          @submit="handleFormSubmit"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleDialogClose">取消</el-button>
            <el-button 
              type="primary" 
              @click="handleDialogSubmit"
              :loading="submitLoading"
              :disabled="submitLoading"
            >
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { SceneItem } from '../../types'
import { SCENE_TYPE_OPTIONS, SCENE_LEVEL_OPTIONS, APP_TYPE_OPTIONS } from '../../types'
import { getSceneList, addScene, updateScene, batchDeleteScene } from '../../request'
import QuerySceneForm from './component/QuerySceneForm.vue'

// 搜索表单
const searchForm = reactive({
  sceneCode: '',
  sceneName: '',
  sceneLevel: undefined as number | undefined,
  valid: undefined as number | undefined
})

// 表格数据
const tableData = ref<SceneItem[]>([])
const loading = ref(false)
const total = ref(0)
const selectedRows = ref<SceneItem[]>([])

// 分页配置
const pagination = reactive({
  pageNo: 1,
  pageSize: 10
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const currentScene = ref<Partial<SceneItem>>({})
const sceneFormRef = ref<InstanceType<typeof QuerySceneForm>>()
const submitLoading = ref(false)  // 添加提交loading状态

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 计算字符串显示宽度
const getTextWidth = (text: string): number => {
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  if (!context) return 0
  
  // 设置与实际表格单元格相同的字体
  context.font = '14px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif'
  
  return Math.ceil(context.measureText(text).width)
}

// 计算数组内容的最大宽度
const getArrayContentWidth = (arr: any[], formatter?: (item: any) => string): number => {
  if (!arr?.length) return 0
  return Math.max(...arr.map(item => {
    const text = formatter ? formatter(item) : String(item)
    return getTextWidth(text)
  }))
}

// 列标签映射
const columnLabels: Record<keyof SceneItem | string, string> = {
  id: 'ID',
  appkeys: '接入服务Appkeys',
  sceneName: '场景名称',
  sceneDescription: '场景描述',
  sceneCode: '场景编码',
  prdWikis: 'PRD/技术方案Wiki',
  appType: '接入服务类型',
  administrator: '管理员',
  sceneLevel: '场景级别',
  peakPeriod: '高峰期',
  fieldCodes: '使用字段',
  dependencyType: '依赖类型',
  estimateQps: '预估QPS',
  actualQps: '实际QPS',
  remark: '备注',
  opMis: '操作人MIS',
  utime: '更新时间'
}

// 列宽度配置
interface ColumnWidths {
  id: number
  appkeys: number
  sceneName: number
  sceneCode: number
  prdWikis: number
  appType: number
  administrator: number
  sceneLevel: number
  peakPeriod: number
  estimateQps: number
  actualQps: number
  valid: number
  opInfo: number
  time: number
  fieldCodes: number
  dependencyType: number
  opMis: number
  utime: number
}

const columnWidths = reactive<ColumnWidths>({
  id: 80,
  appkeys: 200,
  sceneName: 150,
  sceneCode: 120,
  prdWikis: 200,
  appType: 100,
  administrator: 200,
  sceneLevel: 100,
  peakPeriod: 120,
  estimateQps: 100,
  actualQps: 100,
  valid: 80,
  opInfo: 200,
  time: 180,
  fieldCodes: 200,
  dependencyType: 100,
  opMis: 120,
  utime: 180
})

// 更新列宽度
const updateColumnWidths = (data: SceneItem[]) => {
  if (!data.length) return

  // 更新各列的宽度
  columnWidths.appkeys = Math.max(
    120,
    ...data.map(item => getArrayContentWidth(item.appkeys || []))
  ) + 32

  columnWidths.sceneName = Math.max(
    100,
    ...data.map(item => getTextWidth(item.sceneName))
  ) + 32

  columnWidths.sceneCode = Math.max(
    100,
    ...data.map(item => getTextWidth(item.sceneCode || ''))
  ) + 32

  columnWidths.prdWikis = Math.max(
    120,
    ...data.map(item => getArrayContentWidth(item.prdWikis || []))
  ) + 32

  columnWidths.appType = Math.max(
    80,
    getTextWidth(APP_TYPE_OPTIONS.find(opt => opt.value === data[0].appType)?.label || '')
  ) + 32

  columnWidths.administrator = Math.max(
    120,
    ...data.map(item => getArrayContentWidth(item.administrator || []))
  ) + 32

  // ... rest of the column width calculations
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      ...pagination
    }
    const res = await getSceneList(params)
    if (res.code === 0 && res.data) {
      // 处理每条记录，确保数组字段不包含空值
      const processedData = res.data.records.map(item => {
        // 过滤所有可能包含空值的数组字段
        if (item.appkeys) {
          item.appkeys = item.appkeys.filter(appkey => appkey && appkey.trim() !== '')
        }
        if (item.administrator) {
          item.administrator = item.administrator.filter(admin => admin && admin.trim() !== '')
        }
        if (item.prdWikis) {
          item.prdWikis = item.prdWikis.filter(wiki => wiki && wiki.trim() !== '')
        }
        if (item.fieldCodes) {
          item.fieldCodes = item.fieldCodes.filter(field => field && field.trim() !== '')
        }
        return item
      })
      
      tableData.value = processedData
      total.value = res.data.total
      // 更新列宽度
      updateColumnWidths(tableData.value)
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNo = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  searchForm.sceneCode = ''
  searchForm.sceneName = ''
  searchForm.sceneLevel = undefined
  searchForm.valid = undefined
  handleSearch()
}

// 新增场景
const handleAdd = () => {
  dialogTitle.value = '新增场景'
  currentScene.value = {
    sceneName: '',
    sceneDescription: '',
    sceneLevel: undefined as unknown as number,
    dependencyType: undefined as unknown as number,
    fieldCodes: [],
    prdWikis: [],
    peakPeriod: '',
    estimateQps: 0,
    actualQps: 0,
    valid: 1,
    remark: '',
    administrator: [],
    appkeys: [],
    appType: undefined as unknown as number,
    opName: '',
    opMis: ''
  }
  dialogVisible.value = true
  
  // 确保表单组件已加载并重置
  nextTick(() => {
    setTimeout(() => {
      if (sceneFormRef.value) {
        sceneFormRef.value.resetForm()
        console.log('新增场景，表单已重置')
      }
    }, 100)
  })
}

// 编辑场景
const handleEdit = (row: SceneItem) => {
  dialogTitle.value = '编辑场景'
  currentScene.value = {
    id: row.id,
    sceneName: row.sceneName,
    sceneDescription: row.sceneDescription,
    sceneCode: row.sceneCode,
    appkeys: row.appkeys || [],
    appType: row.appType,
    sceneLevel: row.sceneLevel,
    dependencyType: row.dependencyType,
    administrator: row.administrator || [],
    fieldCodes: row.fieldCodes || [],
    prdWikis: row.prdWikis || [],
    peakPeriod: row.peakPeriod,
    estimateQps: row.estimateQps,
    actualQps: row.actualQps,
    valid: row.valid,
    remark: row.remark || '',
    opName: row.opName,
    opMis: row.opMis
  }
  dialogVisible.value = true
  
  // 打印日志，方便调试
  console.log('编辑场景，当前数据:', currentScene.value)
}

// 删除场景
const handleDelete = async (row: SceneItem) => {
  try {
    await ElMessageBox.confirm('确定要删除该场景吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置确认按钮为loading状态
          instance.confirmButtonLoading = true
          
          // 不调用done()，让对话框保持打开状态
          if (!row.id) {
            ElMessage.error('无效的ID')
            instance.confirmButtonLoading = false
            done()
            return
          }
          
          // 执行删除操作
          batchDeleteScene([row.id])
            .then(res => {
              if (res.code === 0) {
                ElMessage.success('删除成功')
                loadTableData()
              } else {
                ElMessage.error(res.message || '删除失败')
              }
            })
            .catch(error => {
              console.error('删除失败:', error)
              ElMessage.error((error as any)?.response?.data?.message || '删除失败')
            })
            .finally(() => {
              // 操作完成后，重置按钮状态并关闭对话框
              instance.confirmButtonLoading = false
              done()
            })
        } else {
          // 用户点击取消，直接关闭对话框
          done()
        }
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error((error as any)?.response?.data?.message || '删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的场景')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 个场景吗？`, '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置确认按钮为loading状态
          instance.confirmButtonLoading = true
          
          // 不调用done()，让对话框保持打开状态
          const ids = selectedRows.value.map(row => row.id).filter((id): id is number => id !== undefined)
          if (ids.length !== selectedRows.value.length) {
            ElMessage.error('部分记录ID不存在')
            instance.confirmButtonLoading = false
            done()
            return
          }
          
          // 执行批量删除操作
          batchDeleteScene(ids)
            .then(res => {
              if (res.code === 0) {
                ElMessage.success('删除成功')
                loadTableData()
              } else {
                ElMessage.error(res.message || '删除失败')
              }
            })
            .catch(error => {
              console.error('删除失败:', error)
              ElMessage.error((error as any)?.response?.data?.message || '删除失败')
            })
            .finally(() => {
              // 操作完成后，重置按钮状态并关闭对话框
              instance.confirmButtonLoading = false
              done()
            })
        } else {
          // 用户点击取消，直接关闭对话框
          done()
        }
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error((error as any)?.response?.data?.message || '删除失败')
    }
  }
}

// 表格选择
const handleSelectionChange = (rows: SceneItem[]) => {
  selectedRows.value = rows
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadTableData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  pagination.pageNo = page
  loadTableData()
}

// 关闭对话框
const handleDialogClose = () => {
  // 先关闭对话框
  dialogVisible.value = false
  
  // 延迟重置表单，确保对话框完全关闭后再执行
  setTimeout(() => {
    if (sceneFormRef.value) {
      // 确保完全清空当前场景数据
      currentScene.value = {}
      sceneFormRef.value.resetForm()
      console.log('对话框已关闭，表单已重置')
    }
  }, 200)
}

// 处理表单提交
const handleFormSubmit = async (data: Partial<SceneItem> | null) => {
  try {
    if (!data) {
      // 表单校验不通过或发生错误
      submitLoading.value = false
      return
    }
    
    console.log('提交的表单数据:', data)
    
    const isEdit = !!data.id
    
    // 构建提交数据
    const submitData: any = {
      ...data,
      opName: 'system',
      opMis: 'system'
    }

    submitLoading.value = true  // 开始加载
    const res = isEdit
      ? await updateScene(submitData)
      : await addScene(submitData)

    if (res.code === 0) {
      ElMessage.success(`${isEdit ? '编辑' : '新增'}成功`)
      dialogVisible.value = false
      loadTableData()
    } else {
      ElMessage.error(res.message || `${isEdit ? '编辑' : '新增'}失败`)
    }
  } catch (error) {
    console.error(`${data?.id ? '编辑' : '新增'}失败:`, error)
    // 判断是否为超时错误
    const errorMessage = error instanceof Error && error.message.includes('timeout') 
      ? '请求超时，请稍后重试'
      : `${data?.id ? '编辑' : '新增'}失败`
    ElMessage.error(errorMessage)
  } finally {
    submitLoading.value = false  // 结束加载
  }
}

// 提交对话框
const handleDialogSubmit = () => {
  if (!sceneFormRef.value) {
    ElMessage.warning('表单组件未加载完成')
    return
  }
  
  // 打印日志，方便调试
  console.log('提交表单，当前场景数据:', currentScene.value)
  
  // 调用表单组件的提交方法
  sceneFormRef.value.submitForm()
}

// 初始化
onMounted(() => {
  loadTableData()
})
</script>

<style lang="scss" scoped>
.query-scene-page {
  padding: 10px 20px;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.query-scene-card {
  width: 100%;
  box-sizing: border-box;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.table-container {
  margin-bottom: 16px;
  overflow-x: auto;

  :deep(.el-table) {
    .scene-code,
    .scene-name,
    .scene-description {
      white-space: normal;
      word-break: break-word;
      line-height: 1.5;
    }

    .el-table__cell {
      .cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      // 标签组的单元格样式特殊处理
      .tag-group {
        .cell {
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    .tag-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .tag-item {
        width: fit-content;
        margin: 2px 0;
        
        &.el-tag {
          white-space: normal;
          height: auto;
          padding: 0 8px;
          line-height: 22px;
        }
      }
    }

    .empty-text {
      color: #909399;
      font-size: 13px;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.manage-btn {
  margin-right: 8px;
}

.scene-dialog {
  :deep(.el-dialog) {
    margin-top: 8vh !important;
  }
}
</style> 