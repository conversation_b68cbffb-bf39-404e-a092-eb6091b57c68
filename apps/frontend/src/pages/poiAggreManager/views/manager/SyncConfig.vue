<template>
  <div class="sync-config-page">
    <el-card class="sync-config-card">
      <template #header>
        <div class="card-header">
          <span>同步配置管理</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :model="searchForm" class="search-form" inline>
        <el-form-item label="同步租户">
          <el-input
            v-model.number="searchForm.syncTenant"
            placeholder="请输入同步租户"
            type="number"
            :min="0"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="同步场景">
          <el-input
            v-model.number="searchForm.syncScene"
            placeholder="请输入同步场景"
            type="number"
            :min="0"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="同步名称">
          <el-input
            v-model="searchForm.syncName"
            placeholder="请输入同步名称"
            clearable
            @keyup.enter="handleSearch"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="同步类型">
          <el-select v-model="searchForm.syncType" placeholder="请选择" style="width: 100px">
            <el-option
              v-for="option in SYNC_TYPE_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="table-operations">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="danger" :disabled="!selectedRows.length" @click="handleBatchDelete">批量删除</el-button>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40" fixed="left" :selectable="(row: SyncConfigItem) => row.status === 0"/>
          <el-table-column prop="syncTenant" label="同步租户" :width="85" fixed="left"/>
          <el-table-column prop="syncScene" label="同步场景" :width="85" fixed="left"/>
          <el-table-column prop="syncName" label="同步名称" :width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="sync-name" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block;">{{ row.syncName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="syncDescription" label="同步描述" min-width="150" show-overflow-tooltip />
          <el-table-column prop="syncFieldCodes" label="同步字段" :width="columnWidths.syncFieldCodes">
            <template #default="{ row }">
              <div class="tag-group">
                <el-tag
                  v-for="(field, index) in row.syncFieldCodes"
                  :key="index"
                  class="tag-item"
                  type="warning"
                  effect="plain"
                >
                  {{ field }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="同步类型" :width="85">
            <template #default="{ row }">
              <el-tag :type="row.type === 1 ? 'success' : 'warning'">
                {{ SYNC_TYPE_OPTIONS.find(option => option.value === row.type)?.label || '-' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="isPoiOuter"
            label="POI纬度"
            width="80"
          >
            <template #default="{ row }">
              <el-tag :type="row.isPoiOuter ? 'success' : 'error'">
                {{ row.isPoiOuter ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            label="配置信息" 
            min-width="600"
          >
            <template #default="{ row }">
              <div class="config-container">
                <div class="config-row">
                  <div v-if="row.totalPoiConsistencyCheck && row.isPoiOuter" class="config-item">
                    <div class="config-label">全量PoiId一致性校验：</div>
                    <div class="config-preview" @click="showConfigDetail('totalPoiConsistencyCheck', row.totalPoiConsistencyCheck)">
                      {{ row.totalPoiConsistencyCheck }}
                    </div>
                  </div>
                  <div v-if="row.mafkaConsumeConfig" class="config-item">
                    <div class="config-label">Mafka配置：</div>
                    <div class="config-preview" @click="showConfigDetail('mafka', row.mafkaConsumeConfig)">
                      {{ row.mafkaConsumeConfig }}
                    </div>
                  </div>
                  <div v-if="row.queryDataConfig" class="config-item">
                    <div class="config-label">查询数据配置：</div>
                    <div class="config-preview" @click="showConfigDetail('queryData', row.queryDataConfig)">
                      {{ row.queryDataConfig }}
                    </div>
                  </div>
                  <div v-if="row.queryChangeIdsConfig" class="config-item">
                    <div class="config-label">查询最近变更ID配置：</div>
                    <div class="config-preview" @click="showConfigDetail('queryChangeIds', row.queryChangeIdsConfig)">
                      {{ row.queryChangeIdsConfig }}
                    </div>
                  </div>
                </div>
                <div v-if="row.dtsSyncConfig" class="config-item dts-config">
                  <div class="config-label">DTS配置：</div>
                  <div class="config-preview" @click="showConfigDetail('dts', row.dtsSyncConfig)">
                    {{ row.dtsSyncConfig }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="opMis" label="操作人MIS" :width="columnWidths.opMis" />
          <el-table-column prop="utime" label="更新时间" :width="columnWidths.utime">
            <template #default="{ row }">
              {{ formatTimestamp(row.utime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <template v-if="row.status === 0">
                <el-button type="primary" class="manage-btn" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="danger" plain size="small" @click="handleDelete(row)">删除</el-button>
              </template>
              <span v-else class="review-status">审核中</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNo"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 新增/编辑同步配置对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="60%"
        :close-on-click-modal="false"
        :before-close="handleDialogClose"
        align-center
        class="sync-dialog"
      >
        <SyncConfigForm
          ref="syncFormRef"
          :initial-data="currentSync"
          @submit="handleFormSubmit"
          @formChange="handleFormChange"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleDialogClose">取消</el-button>
            <el-button 
              type="primary" 
              @click="handleDialogSubmit" 
              :loading="submitLoading"
              :disabled="!formHasChanged"
            >
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 配置详情对话框 -->
      <el-dialog
        v-model="configDetailDialogVisible"
        :title="configDetailTitle"
        width="60%"
        class="config-detail-dialog"
        destroy-on-close
        align-center
        :close-on-click-modal="false"
      >
        <div class="config-detail-container">
          <pre class="config-detail">{{ currentConfigDetail }}</pre>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="configDetailDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="copyConfigDetail">
              <el-icon><CopyDocument /></el-icon>复制配置
            </el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { SyncConfigItem, McmChangeItem } from '../../types'
import { SYNC_TYPE_OPTIONS, POI_OUTER_OPTIONS, SyncTypeEnum } from '../../types'
import { getSyncConfigList, addSyncConfig, updateSyncConfig, batchDeleteSyncConfig } from '../../request'
import SyncConfigForm from './component/SyncConfigForm.vue'
import { CopyDocument } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  syncTenant: undefined as number | undefined,
  syncScene: undefined as number | undefined,
  syncName: '',
  syncType: undefined as number | undefined
})

// 表格数据
const tableData = ref<SyncConfigItem[]>([])
const loading = ref(false)
const total = ref(0)
const selectedRows = ref<SyncConfigItem[]>([])

// 分页配置
const pagination = reactive({
  pageNo: 1,
  pageSize: 10
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const currentSync = ref<Partial<SyncConfigItem>>({})
const syncFormRef = ref<InstanceType<typeof SyncConfigForm>>()
const submitLoading = ref(false)
const formHasChanged = ref(false)

// 配置详情对话框
const configDetailDialogVisible = ref(false)
const configDetailTitle = ref('')
const currentConfigDetail = ref('')

// 计算字符串显示宽度
const getTextWidth = (text: string): number => {
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  if (!context) return 0
  
  context.font = '14px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif'
  
  return Math.ceil(context.measureText(text).width)
}

// 计算数组内容的最大宽度
const getArrayContentWidth = (arr: any[], formatter?: (item: any) => string): number => {
  if (!arr?.length) return 0
  return Math.max(...arr.map(item => {
    const text = formatter ? formatter(item) : String(item)
    return getTextWidth(text)
  }))
}

// 列宽度配置
interface ColumnWidths {
  syncScene: number
  syncTenant: number
  syncName: number
  syncFieldCodes: number
  type: number
  isPoiOuter: number
  opMis: number
  utime: number
}

const columnWidths = reactive<ColumnWidths>({
  syncScene: 100,
  syncTenant: 100,
  syncName: 150,
  syncFieldCodes: 200,
  type: 100,
  isPoiOuter: 120,
  opMis: 120,
  utime: 180
})

// 更新列宽度
const updateColumnWidths = (data: SyncConfigItem[]) => {
  if (!data.length) return

  columnWidths.syncScene = Math.max(
    80,
    ...data.map(item => getTextWidth(String(item.syncScene)))
  ) + 24

  columnWidths.syncTenant = Math.max(
    80,
    ...data.map(item => getTextWidth(String(item.syncTenant)))
  ) + 24

  columnWidths.syncName = Math.max(
    100,
    ...data.map(item => getTextWidth(item.syncName || ''))
  ) + 32

  columnWidths.syncFieldCodes = Math.max(
    120,
    ...data.map(item => getArrayContentWidth(item.syncFieldCodes || []))
  ) + 32

  columnWidths.type = Math.max(
    80,
    ...SYNC_TYPE_OPTIONS.map(option => getTextWidth(option.label))
  ) + 50

  columnWidths.isPoiOuter = Math.max(
    100,
    ...POI_OUTER_OPTIONS.map(option => getTextWidth(option.label))
  ) + 50

  columnWidths.opMis = Math.max(
    100,
    ...data.map(item => getTextWidth(item.opMis || ''))
  ) + 24

  columnWidths.utime = Math.max(
    140,
    ...data.map(item => getTextWidth(formatTimestamp(item.utime ?? 0)))
  ) + 24
}

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      ...pagination
    }
    const res = await getSyncConfigList(params)
    if (res.code === 0 && res.data) {
      tableData.value = res.data.records
      total.value = res.data.total
      
      // 更新列宽度
      updateColumnWidths(tableData.value)
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNo = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  searchForm.syncTenant = undefined
  searchForm.syncScene = undefined
  searchForm.syncName = ''
  searchForm.syncType = undefined
  handleSearch()
}

// 新增同步配置
const handleAdd = () => {
  dialogTitle.value = '新增同步配置'
  if (syncFormRef.value) {
    syncFormRef.value.resetForm()
  }
  
  currentSync.value = {
    syncTenant: undefined,
    syncScene: undefined,
    syncName: '',
    syncDescription: '',
    syncFieldCodes: [],
    type: undefined,
    isPoiOuter: undefined,
    mafkaConsumeConfig: '',
    queryDataConfig: '',
    queryChangeIdsConfig: '',
    dtsSyncConfig: '',
    valid: 1
  }
  
  formHasChanged.value = false
  dialogVisible.value = true
  
  nextTick(() => {
    if (syncFormRef.value) {
      syncFormRef.value.resetForm()
    }
  })
}

// 编辑同步配置
const handleEdit = async (row: SyncConfigItem) => {
  dialogTitle.value = '编辑同步配置'
  // 深拷贝行数据，避免引用问题
  currentSync.value = JSON.parse(JSON.stringify(row))
  formHasChanged.value = false
  dialogVisible.value = true
  
  // 等待对话框显示完成
  await nextTick()
  
  // 确保表单组件已经挂载
  if (syncFormRef.value) {
    // 手动触发表单初始化
    syncFormRef.value.initFormData()
  }
}

// 删除同步配置
const handleDelete = async (row: SyncConfigItem) => {
  try {
    await ElMessageBox.confirm('确定要删除该同步配置吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置确认按钮为loading状态
          instance.confirmButtonLoading = true
          
          // 不调用done()，让对话框保持打开状态
          if (!row.id) {
            ElMessage.error('无效的ID')
            instance.confirmButtonLoading = false
            done()
            return
          }
          
          const deleteData = {
            changeDescription: '',
            changeBefore: {"SyncConfigList": [row]},
            changeAfter: null,
            requestBaseUrl: '',
            requestUri: '',
            requestData: {"ids":[row.id]}
          }
          
          // 执行删除操作
          batchDeleteSyncConfig(deleteData as Omit<McmChangeItem, 'changeAfter'>)
            .then(res => {
              if (res.code === 0) {
                // 修改成功提示方式，添加"查看任务"链接
                if (res.data && typeof res.data === 'string') {
                  // 使用h函数创建VNode
                  ElMessage({
                    message: h('div', {}, [
                      res.message || '删除成功',
                      ' ',
                      h('a', {
                        href: res.data,
                        target: '_blank',
                        class: 'view-task-link'
                      }, '查看任务')
                    ]),
                    type: 'success',
                    dangerouslyUseHTMLString: true
                  })
                } else {
                  // 没有链接时展示普通成功信息
                  ElMessage.success(res.message || '删除成功')
                }
                loadTableData()
              } else {
                ElMessage.error(res.message || '删除失败')
              }
            })
            .catch(error => {
              console.error('删除失败:', error)
              ElMessage.error((error as any)?.response?.data?.message || '删除失败')
            })
            .finally(() => {
              // 操作完成后，重置按钮状态并关闭对话框
              instance.confirmButtonLoading = false
              done()
            })
        } else {
          // 用户点击取消，直接关闭对话框
          done()
        }
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      if (error instanceof Error && error.message.includes('timeout')) {
        ElMessage.error('请求超时，请稍后重试')
      } else {
        ElMessage.error((error as any)?.response?.data?.message || '系统异常')
      }
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的同步配置')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 个同步配置吗？`, '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置确认按钮为loading状态
          instance.confirmButtonLoading = true
          
          // 不调用done()，让对话框保持打开状态
          const ids = selectedRows.value.map(row => row.id).filter((id): id is number => id !== undefined)
          if (ids.length !== selectedRows.value.length) {
            ElMessage.error('部分记录ID不存在')
            instance.confirmButtonLoading = false
            done()
            return
          }
          
          const deleteData = {
            changeDescription: '',
            changeBefore: {"SyncConfigList": selectedRows.value},
            changeAfter: null,
            requestBaseUrl: '',
            requestUri: '',
            requestData: {"ids":ids}
          }
          
          // 执行批量删除操作
          batchDeleteSyncConfig(deleteData as Omit<McmChangeItem, 'changeAfter'>)
            .then(res => {
              if (res.code === 0) {
                // 修改成功提示方式，添加"查看任务"链接
                if (res.data && typeof res.data === 'string') {
                  // 使用h函数创建VNode
                  ElMessage({
                    message: h('div', {}, [
                      res.message || '批量删除成功',
                      ' ',
                      h('a', {
                        href: res.data,
                        target: '_blank',
                        class: 'view-task-link'
                      }, '查看任务')
                    ]),
                    type: 'success',
                    dangerouslyUseHTMLString: true
                  })
                } else {
                  // 没有链接时展示普通成功信息
                  ElMessage.success(res.message || '批量删除成功')
                }
                loadTableData()
              } else {
                ElMessage.error(res.message || '批量删除失败')
              }
            })
            .catch(error => {
              console.error('批量删除失败:', error)
              ElMessage.error((error as any)?.response?.data?.message || '批量删除失败')
            })
            .finally(() => {
              // 操作完成后，重置按钮状态并关闭对话框
              instance.confirmButtonLoading = false
              done()
            })
        } else {
          // 用户点击取消，直接关闭对话框
          done()
        }
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error((error as any)?.response?.data?.message || '批量删除失败')
    }
  }
}

// 表格选择
const handleSelectionChange = (rows: SyncConfigItem[]) => {
  selectedRows.value = rows
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadTableData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  pagination.pageNo = page
  loadTableData()
}

// 处理表单修改状态变化
const handleFormChange = (hasChanged: boolean) => {
  formHasChanged.value = hasChanged
}

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false
  setTimeout(() => {
    if (syncFormRef.value) {
      currentSync.value = {}
      syncFormRef.value.resetForm()
      formHasChanged.value = false
    }
  }, 200)
}

// 处理表单提交
const handleFormSubmit = async (data: Partial<SyncConfigItem> | null) => {
  try {
    // 如果data为null，说明表单未发生变更
    if (data === null) {
      console.log('表单未发生变更，关闭对话框')
      dialogVisible.value = false
      return
    }
    
    console.log('提交的表单数据:', data)
    
    // 使用dialogTitle来判断是新增还是编辑操作
    const isEdit = dialogTitle.value === '编辑同步配置'
    
    submitLoading.value = true  // 开始加载

    let res;
    if (isEdit) {
      // 获取原始数据并进行深拷贝
      const originalItem = tableData.value.find(item => item.id === data.id)
      if (!originalItem) {
        ElMessage.error('未找到原始数据')
        return
      }
      
      // 使用Object.create和Object.assign实现深拷贝
      const originalData = Object.assign(
        Object.create(Object.getPrototypeOf(originalItem)),
        originalItem
      )

      // 重新构造originalData对象，按照指定顺序排列字段
      const orderedOriginalData: SyncConfigItem = {
        id: undefined,
        syncTenant: originalData.syncTenant,
        syncScene: originalData.syncScene,
        syncName: originalData.syncName || '',
        syncDescription: originalData.syncDescription || '',
        syncFieldCodes: originalData.syncFieldCodes || [],
        type: originalData.type,
        isPoiOuter: originalData.isPoiOuter ?? false,
        totalPoiConsistencyCheck: originalData.totalPoiConsistencyCheck || undefined,
        mafkaConsumeConfig: originalData.mafkaConsumeConfig || undefined,
        queryDataConfig: originalData.queryDataConfig || undefined,
        queryChangeIdsConfig: originalData.queryChangeIdsConfig || undefined,
        dtsSyncConfig: originalData.dtsSyncConfig || undefined,
        valid: undefined,
        status: undefined,
        opName: undefined,
        opMis: undefined,
        ctime: undefined,
        utime: undefined,
        lionConfigDescription: undefined
      }

      // data对象中也需要包含status字段，并按照指定顺序排列
      const orderedSubmitData: SyncConfigItem = {
        id: data.id,
        syncTenant: data.syncTenant,
        syncScene: data.syncScene,
        syncName: data.syncName || '',
        syncDescription: data.syncDescription || '',
        syncFieldCodes: data.syncFieldCodes || [],
        type: data.type,
        isPoiOuter: data.isPoiOuter == 1 ? true : false,
        totalPoiConsistencyCheck: data.totalPoiConsistencyCheck || undefined,
        mafkaConsumeConfig: data.mafkaConsumeConfig || undefined,
        queryDataConfig: data.queryDataConfig || undefined,
        queryChangeIdsConfig: data.queryChangeIdsConfig || undefined,
        dtsSyncConfig: data.dtsSyncConfig || undefined,
        valid: data.valid ?? 1,
        status: undefined,
        opName: undefined,
        opMis: undefined,
        ctime: undefined,
        utime: undefined,
        lionConfigDescription: undefined
      }

      const changeAfterData = Object.assign(
        Object.create(Object.getPrototypeOf(orderedSubmitData)),
        orderedSubmitData
      )
      changeAfterData.id = undefined
      changeAfterData.valid = undefined

      // 构造符合ChangeConfigDTO格式的请求数据
      const updateData = {
        changeDescription: '',
        changeBefore: orderedOriginalData,
        changeAfter: changeAfterData,
        requestBaseUrl: '',
        requestUri: '',
        requestData: orderedSubmitData
      }
      res = await updateSyncConfig(updateData as Omit<McmChangeItem, 'requestData'>)
    } else {
      const changeAfterData = Object.assign(
        Object.create(Object.getPrototypeOf(data)),
        data
      )
      changeAfterData.id = undefined
      changeAfterData.valid = undefined
      const addData = {
        changeDescription: '',
        changeBefore: null,
        changeAfter: changeAfterData,
        requestBaseUrl: '',
        requestUri: '',
        requestData: data
      }
      res = await addSyncConfig(addData as Omit<McmChangeItem, 'changeBefore'>)
    }

    if (res.code === 0) {
      // 修改成功提示方式，添加"查看任务"链接
      if (res.data && typeof res.data === 'string') {
        // 使用h函数创建VNode
        ElMessage({
          message: h('div', {}, [
            res.message || (isEdit ? '编辑成功' : '新增成功'),
            ' ',
            h('a', {
              href: res.data,
              target: '_blank',
              class: 'view-task-link'
            }, '查看任务')
          ]),
          type: 'success',
          dangerouslyUseHTMLString: true
        })
      } else {
        // 没有链接时展示普通成功信息
        ElMessage.success(res.message || (isEdit ? '编辑成功' : '新增成功'))
      }
      dialogVisible.value = false
      loadTableData()
    } else {
      ElMessage.error(res.message || (isEdit ? '编辑失败' : '新增失败'))
    }
  } catch (error) {
    console.error('表单提交失败:', error)
    if (error instanceof Error && error.message.includes('timeout')) {
      ElMessage.error('请求超时，请稍后重试')
    } else {
      ElMessage.error((error as any)?.response?.data?.message || '系统异常')
    }
  } finally {
    submitLoading.value = false  // 结束加载
  }
}

// 提交对话框
const handleDialogSubmit = () => {
  if (!syncFormRef.value) {
    ElMessage.warning('表单组件未加载完成')
    return
  }
  syncFormRef.value.submitForm()
}

// 显示配置详情
const showConfigDetail = (type: string, config: string) => {
  const titleMap = {
    mafka: 'Mafka配置',
    queryData: '查询数据配置',
    queryChangeIds: '变更ID配置',
    dts: 'DTS配置'
  }
  
  configDetailTitle.value = titleMap[type as keyof typeof titleMap] || '配置详情'
  currentConfigDetail.value = config || '无'
  configDetailDialogVisible.value = true
}

// 复制配置详情
const copyConfigDetail = () => {
  const textArea = document.createElement('textarea')
  textArea.value = currentConfigDetail.value
  document.body.appendChild(textArea)
  textArea.select()
  document.execCommand('copy')
  document.body.removeChild(textArea)
  ElMessage.success('配置已复制到剪贴板')
}

// 初始化
onMounted(() => {
  loadTableData()
})
</script>

<style lang="scss" scoped>
.sync-config-page {
  padding: 10px 20px;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.sync-config-card {
  width: 100%;
  box-sizing: border-box;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.table-container {
  margin-bottom: 16px;
  overflow-x: auto;

  :deep(.el-table) {
    .sync-name {
      white-space: normal;
      word-break: break-word;
      line-height: 1.5;
    }

    .el-table__cell {
      .cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .tag-group {
        .cell {
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    .tag-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .tag-item {
        width: fit-content;
        margin: 2px 0;
        
        &.el-tag {
          white-space: normal;
          height: auto;
          padding: 0 8px;
          line-height: 22px;
        }
      }
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.manage-btn {
  margin-right: 8px;
}

.sync-dialog {
  :deep(.el-dialog) {
    margin-top: 8vh !important;
    
    .el-dialog__body {
      max-height: 60vh;
      overflow-y: auto;
      padding: 0;
    }
    
    .el-dialog__header {
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.config-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-row {
  display: flex;
  gap: 8px;
  width: 100%;
  
  .config-item {
    flex: 1;
    min-width: 0; // 防止内容溢出
  }
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 0; // 防止内容溢出

  &.dts-config {
    width: 100%;
  }
}

.config-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.config-preview {
  white-space: normal;
  word-break: break-word;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-family: "Courier New", monospace;
  font-size: 12px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  
  &:hover {
    background-color: #e6e9ed;
    
    &::after {
      content: "点击查看完整配置";
      position: absolute;
      bottom: 2px;
      right: 4px;
      font-size: 10px;
      color: #409eff;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 0 4px;
      border-radius: 2px;
    }
  }
}

.config-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
  
  :deep(.el-dialog__header) {
    padding: 16px 20px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 12px 20px;
  }
}

.config-detail-container {
  background-color: #1e1e1e;
  overflow: auto;
  border-radius: 0;
  padding: 0;
  margin: 0;
  max-height: 60vh;
  width: 100%;
}

.config-detail {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #e6e6e6;
  background-color: #1e1e1e;
  padding: 16px;
  margin: 0;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.empty-text {
  color: #909399;
  font-size: 13px;
}

.review-status {
  color: #ff8c00;
  font-size: 14px;
  display: flex;
  justify-content: center;
  width: 100%;
}
</style> 