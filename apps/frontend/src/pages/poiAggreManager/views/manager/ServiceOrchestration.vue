<template>
  <div class="service-orchestration-page">
    <el-card class="service-orchestration-card">
      <template #header>
        <div class="card-header">
          <span>服务编排管理</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :model="searchForm" class="search-form" inline>
        <el-form-item label="DSL名称">
          <el-input
            v-model="searchForm.dslName"
            placeholder="请输入DSL名称"
            clearable
            @keyup.enter="handleSearch"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="DSL描述">
          <el-input
            v-model="searchForm.dslDescription"
            placeholder="请输入DSL描述"
            clearable
            @keyup.enter="handleSearch"
            style="width: 200px"
          />
        </el-form-item>
        <!-- <el-form-item label="状态">
          <el-select v-model="searchForm.valid" placeholder="请选择" clearable style="width: 100px">
            <el-option label="有效" :value="1" />
            <el-option label="无效" :value="0" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="table-operations">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="danger" :disabled="!selectedRows.length" @click="handleBatchDelete">批量删除</el-button>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40" fixed="left" :selectable="(row: DSLConfigItem) => row.status === 0"/>
          <el-table-column prop="id" label="ID" width="45" fixed="left" />
          <el-table-column prop="dslName" label="DSL名称" :width="columnWidths.dslName - 15" fixed="left">
            <template #default="{ row }">
              <span class="dsl-name">{{ row.dslName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="dslDescription" label="DSL描述" min-width="200" show-overflow-tooltip />
          <el-table-column
            prop="dsl"
            label="DSL内容"
            min-width="400"
          >
            <template #default="{ row }">
              <div class="dsl-content-preview" @click="showDSLCode(row.dsl)">
                {{ row.dsl }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="fieldCodes" label="使用字段" :width="columnWidths.fieldCodes">
            <template #default="{ row }">
              <div class="tag-group">
                <template v-if="row.fieldCodes?.filter((f: string) => f && f.trim() !== '').length">
                  <div class="tag-row">
                    <el-tag
                      v-for="(field, index) in row.fieldCodes?.filter((f: string) => f && f.trim() !== '').slice(0, Math.ceil(row.fieldCodes.length / 2))"
                      :key="index"
                      class="tag-item"
                      type="warning"
                      effect="plain"
                    >
                      {{ field }}
                    </el-tag>
                  </div>
                  <div class="tag-row">
                    <el-tag
                      v-for="(field, index) in row.fieldCodes?.filter((f: string) => f && f.trim() !== '').slice(Math.ceil(row.fieldCodes.length / 2))"
                      :key="index + Math.ceil(row.fieldCodes.length / 2)"
                      class="tag-item"
                      type="warning"
                      effect="plain"
                    >
                      {{ field }}
                    </el-tag>
                  </div>
                </template>
                <span v-else class="no-data">无</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="thrownException" label="是否抛出异常" min-width="110px">
            <template #default="{ row }">
              <el-tag :type="row.thrownException ? 'danger' : 'success'">
                {{ row.thrownException ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dslPreHeatParam" label="预热参数" width="150">
            <template #default="{ row }">
              <div class="dsl-content-preview" @click="showPreHeatParam(row.dslPreHeatParam)">
                {{ row.dslPreHeatParam || '无' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="option" label="其他配置" min-width="150">
            <template #default="{ row }">
              <div class="dsl-content-preview" @click="showOptionDetail(row.option)">
                {{ formatOption(row.option) }}
              </div>
            </template>
          </el-table-column>
          <!-- TODO 后续使用时删除 v-show="false" -->
          <template v-show="false">
            <el-table-column prop="supportQps" label="支持QPS" width="100">
              <template #default="{ row }">
                {{ row.supportQps || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="actualQps" label="实际QPS" width="100">
              <template #default="{ row }">
                {{ row.actualQps || 0 }}
              </template>
            </el-table-column>
          </template>
          <!-- <el-table-column prop="valid" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.valid === 1 ? 'success' : 'danger'">
                {{ row.valid === 1 ? '有效' : '无效' }}
              </el-tag>
            </template>
          </el-table-column> -->
          <el-table-column prop="opMis" label="操作人MIS" :width="columnWidths.opMis" />
          <el-table-column prop="utime" label="更新时间" :width="columnWidths.utime - 15">
            <template #default="{ row }">
              {{ formatTimestamp(row.utime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <template v-if="row.status === 0">
                <el-button
                  type="primary"
                  size="small"
                  class="manage-btn"
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  plain
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
              <span v-else class="review-status">审核中</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNo"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 新增/编辑DSL配置对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="60%"
        :close-on-click-modal="false"
        :before-close="handleDialogClose"
        align-center
        class="dsl-dialog"
      >
        <DSLConfigForm
          ref="dslFormRef"
          :initial-data="currentDSL"
          @submit="handleFormSubmit"
          @formChange="handleFormChange"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleDialogClose">取消</el-button>
            <el-button 
              type="primary" 
              @click="handleDialogSubmit" 
              :loading="submitLoading"
              :disabled="!formHasChanged"
            >
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- DSL代码查看对话框 -->
      <el-dialog
        v-model="dslCodeDialogVisible"
        title="DSL流程图"
        width="95%"
        class="dsl-flow-dialog"
        destroy-on-close
        align-center
        :close-on-click-modal="false"
      >
        <div class="dsl-flow-container">
          <div v-if="dslLoading" class="loading-container">
            <el-skeleton style="width: 100%" animated>
              <template #template>
                <div style="padding: 20px">
                  <el-skeleton-item variant="h3" style="width: 30%" />
                  <div style="display: flex; align-items: center; margin-top: 20px">
                    <el-skeleton-item variant="circle" style="margin-right: 10px; width: 100px; height: 100px" />
                    <el-skeleton-item variant="p" style="width: 40%" />
                  </div>
                  <div style="margin-top: 20px; display: flex; justify-content: space-between">
                    <el-skeleton-item variant="text" style="margin-right: 10px" />
                    <el-skeleton-item variant="text" style="width: 30%" />
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>
          <div v-else-if="currentDslCode" class="workflow-wrapper">
            <WorkflowGraphWrapper
              :dsl-json="currentDslCode"
              @update:dsl-json="currentDslCode = $event"
              @toggle-dsl-editor="showDslEditor = !showDslEditor"
              :show-dsl-editor="showDslEditor"
              :is-editing="isEditing"
              :dsl-loading="dslLoading"
            />
          </div>
          <el-empty v-else description="DSL解析失败，无法显示流程图">
            <template #description>
              <p>未能成功解析DSL，请检查DSL格式是否正确。</p>
              <p>您可以尝试复制DSL内容进行分析：</p>
            </template>
            <el-button type="primary" @click="copyDslCode">
              <el-icon><CopyDocument /></el-icon>复制DSL代码
            </el-button>
          </el-empty>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dslCodeDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="copyDslCode">
              <el-icon><CopyDocument /></el-icon>复制代码
            </el-button>
            <el-button type="success" @click="() => goToOrchestrationVisualizer(currentDslCode, { closeCurrentDialog: () => dslCodeDialogVisible = false })">
              <el-icon><View /></el-icon>查看详情
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 预热参数查看对话框 -->
      <el-dialog
        v-model="preHeatParamDialogVisible"
        title="预热参数"
        width="60%"
        class="dsl-code-dialog"
        destroy-on-close
        align-center
        :close-on-click-modal="false"
      >
        <div class="dsl-code-container">
          <pre class="dsl-code">{{ currentPreHeatParam }}</pre>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="preHeatParamDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="copyPreHeatParam">
              <el-icon><CopyDocument /></el-icon>复制参数
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 配置详情对话框 -->
      <el-dialog
        v-model="optionDetailDialogVisible"
        title="其他参数"
        width="60%"
        class="config-detail-dialog"
        destroy-on-close
        align-center
        :close-on-click-modal="false"
      >
        <div class="config-detail-container">
          <pre class="config-detail">{{ currentOptionDetail }}</pre>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="optionDetailDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="copyOptionDetail">
              <el-icon><CopyDocument /></el-icon>复制配置
            </el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { DSLConfigItem, McmChangeItem } from '../../types'
import { getDSLConfigList, addDSLConfig, updateDSLConfig, batchDeleteDSLConfig } from '../../request'
import DSLConfigForm from './component/DSLConfigForm.vue'
import { CopyDocument, View, Document } from '@element-plus/icons-vue'
import WorkflowGraphWrapper from '../../components/orchestration/WorkflowGraphWrapper.vue'
import { goToOrchestrationVisualizer } from '../../utils/orchestrationUtils'

// 搜索表单
const searchForm = reactive({
  dslName: '',
  dslDescription: '',
  valid: undefined as number | undefined
})

// 表格数据
const tableData = ref<DSLConfigItem[]>([])
const loading = ref(false)
const total = ref(0)
const selectedRows = ref<DSLConfigItem[]>([])

// 分页配置
const pagination = reactive({
  pageNo: 1,
  pageSize: 10
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const currentDSL = ref<Partial<DSLConfigItem>>({})
const dslFormRef = ref<InstanceType<typeof DSLConfigForm>>()
const submitLoading = ref(false)
const formHasChanged = ref(false)

// DSL代码查看对话框
const dslCodeDialogVisible = ref(false)
const currentDslCode = ref('')
const dslLoading = ref(false)
// 是否显示DSL编辑器
const showDslEditor = ref(false)
// 控制是否处于编辑模式
const isEditing = ref(false)

// 预热参数查看对话框
const preHeatParamDialogVisible = ref(false)
const currentPreHeatParam = ref('')

// 配置详情对话框
const optionDetailDialogVisible = ref(false)
const currentOptionDetail = ref('')

// 计算字符串显示宽度
const getTextWidth = (text: string): number => {
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  if (!context) return 0
  
  // 设置与实际表格单元格相同的字体
  context.font = '14px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif'
  
  return Math.ceil(context.measureText(text).width)
}

// 计算数组内容的最大宽度
const getArrayContentWidth = (arr: any[], formatter?: (item: any) => string): number => {
  if (!arr?.length) return 0
  return Math.max(...arr.map(item => {
    const text = formatter ? formatter(item) : String(item)
    return getTextWidth(text)
  }))
}

// 列宽度配置
interface ColumnWidths {
  dslName: number
  fieldCodes: number
  thrownException: number
  opMis: number
  utime: number
}

const columnWidths = reactive<ColumnWidths>({
  dslName: 150,
  fieldCodes: 200,
  thrownException: 120,
  opMis: 120,
  utime: 180
})

// 更新列宽度
const updateColumnWidths = (data: DSLConfigItem[]) => {
  if (!data.length) return

  // 更新各列的宽度
  columnWidths.dslName = Math.max(
    100,
    ...data.map(item => getTextWidth(item.dslName))
  ) + 32

  columnWidths.fieldCodes = Math.max(
    120,
    ...data.map(item => getArrayContentWidth(item.fieldCodes || []))
  ) + 32

  columnWidths.thrownException = Math.max(
    80,
    getTextWidth('是') + 50, // Tag的额外宽度
    getTextWidth('否') + 50
  )

  columnWidths.opMis = Math.max(
    100,
    ...data.map(item => getTextWidth(item.opMis || ''))
  ) + 24

  columnWidths.utime = Math.max(
    140,
    ...data.map(item => getTextWidth(formatTimestamp(item.utime ?? 0)))
  ) + 24
}

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      ...pagination
    }
    const res = await getDSLConfigList(params)
    if (res.code === 0 && res.data) {
      // 确保字段都有值
      const processedData = res.data.records.map(item => {
        // 处理fieldCodes字段
        if (typeof item.fieldCodes === 'string') {
          // 如果后端返回的是字符串，转换为数组
          item.fieldCodes = (item.fieldCodes as string).split(',').filter(code => code && code.trim() !== '')
        } else if (!Array.isArray(item.fieldCodes)) {
          // 如果不是数组也不是字符串，设为空数组
          item.fieldCodes = []
        }
        return item
      })
      
      tableData.value = processedData
      total.value = res.data.total
      
      // 更新列宽度
      updateColumnWidths(tableData.value)
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNo = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  searchForm.dslName = ''
  searchForm.dslDescription = ''
  searchForm.valid = undefined
  handleSearch()
}

// 新增DSL配置
const handleAdd = () => {
  dialogTitle.value = '新增服务编排'
  // 先重置表单
  if (dslFormRef.value) {
    dslFormRef.value.resetForm()
  }
  
  // 设置初始值
  currentDSL.value = {
    dslName: '',
    dslDescription: '',
    dsl: '',
    fieldCodes: [],
    thrownException: false,
    dslPreHeatParam: '',
    valid: 1,
    opName: '',
    opMis: ''
  }
  
  formHasChanged.value = false
  dialogVisible.value = true
  
  // 确保表单组件已加载并重置
  nextTick(() => {
    if (dslFormRef.value) {
      dslFormRef.value.resetForm()
      console.log('新增服务编排，表单已重置')
    } else {
      console.warn('新增服务编排，表单组件未加载')
    }
  })
}

// 编辑DSL配置
const handleEdit = (row: DSLConfigItem) => {
  dialogTitle.value = '编辑服务编排'
  // 复制对象，避免直接修改表格数据
  currentDSL.value = { ...row }
  formHasChanged.value = false
  dialogVisible.value = true
  
  // 确保表单组件已加载并重置
  nextTick(() => {
    if (dslFormRef.value) {
      dslFormRef.value.resetForm()
    } else {
      console.warn('编辑服务编排，表单组件未加载')
    }
  })
}

// 删除DSL配置
const handleDelete = async (row: DSLConfigItem) => {
  try {
    await ElMessageBox.confirm('确定要删除该服务编排吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置确认按钮为loading状态
          instance.confirmButtonLoading = true
          
          // 不调用done()，让对话框保持打开状态
          if (!row.id) {
            ElMessage.error('无效的ID')
            instance.confirmButtonLoading = false
            done()
            return
          }
          
          const deleteData = {
            changeDescription: '',
            changeBefore: {"DSLConfigList": [row]},
            changeAfter: null,
            requestBaseUrl: '',
            requestUri: '',
            requestData: {"ids":[row.id]}
          }
          
          // 执行删除操作
          batchDeleteDSLConfig(deleteData as Omit<McmChangeItem, 'changeAfter'>)
            .then(res => {
              if (res.code === 0) {
                // 修改成功提示方式，添加"查看任务"链接
                if (res.data && typeof res.data === 'string') {
                  // 使用h函数创建VNode
                  ElMessage({
                    message: h('div', {}, [
                      res.message || '删除成功',
                      ' ',
                      h('a', {
                        href: res.data,
                        target: '_blank',
                        class: 'view-task-link'
                      }, '查看任务')
                    ]),
                    type: 'success',
                    dangerouslyUseHTMLString: true
                  })
                } else {
                  // 没有链接时展示普通成功信息
                  ElMessage.success(res.message || '删除成功')
                }
                loadTableData()
              } else {
                ElMessage.error(res.message || '删除失败')
              }
            })
            .catch(error => {
              console.error('删除失败:', error)
              ElMessage.error((error as any)?.response?.data?.message || '删除失败')
            })
            .finally(() => {
              // 操作完成后，重置按钮状态并关闭对话框
              instance.confirmButtonLoading = false
              done()
            })
        } else {
          // 用户点击取消，直接关闭对话框
          done()
        }
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error((error as any)?.response?.data?.message || '删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的服务编排')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 个服务编排吗？`, '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置确认按钮为loading状态
          instance.confirmButtonLoading = true
          
          // 不调用done()，让对话框保持打开状态
          const ids = selectedRows.value.map(row => row.id).filter((id): id is number => id !== undefined)
          if (ids.length !== selectedRows.value.length) {
            ElMessage.error('部分记录ID不存在')
            instance.confirmButtonLoading = false
            done()
            return
          }
          
          const deleteData = {
            changeDescription: '',
            changeBefore: {"DSLConfigList": selectedRows.value},
            changeAfter: null,
            requestBaseUrl: '',
            requestUri: '',
            requestData: {"ids":ids}
          }
          
          // 执行批量删除操作
          batchDeleteDSLConfig(deleteData as Omit<McmChangeItem, 'changeAfter'>)
            .then(res => {
              if (res.code === 0) {
                // 修改成功提示方式，添加"查看任务"链接
                if (res.data && typeof res.data === 'string') {
                  // 使用h函数创建VNode
                  ElMessage({
                    message: h('div', {}, [
                      res.message || '批量删除成功',
                      ' ',
                      h('a', {
                        href: res.data,
                        target: '_blank',
                        class: 'view-task-link'
                      }, '查看任务')
                    ]),
                    type: 'success',
                    dangerouslyUseHTMLString: true
                  })
                } else {
                  // 没有链接时展示普通成功信息
                  ElMessage.success(res.message || '批量删除成功')
                }
                loadTableData()
              } else {
                ElMessage.error(res.message || '批量删除失败')
              }
            })
            .catch(error => {
              console.error('批量删除失败:', error)
              ElMessage.error((error as any)?.response?.data?.message || '批量删除失败')
            })
            .finally(() => {
              // 操作完成后，重置按钮状态并关闭对话框
              instance.confirmButtonLoading = false
              done()
            })
        } else {
          // 用户点击取消，直接关闭对话框
          done()
        }
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error((error as any)?.response?.data?.message || '批量删除失败')
    }
  }
}

// 表格选择
const handleSelectionChange = (rows: DSLConfigItem[]) => {
  selectedRows.value = rows
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadTableData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  pagination.pageNo = page
  loadTableData()
}

// 处理表单修改状态变化
const handleFormChange = (hasChanged: boolean) => {
  console.log('表单变化状态:', hasChanged)
  formHasChanged.value = hasChanged
}

// 关闭对话框
const handleDialogClose = () => {
  // 先关闭对话框
  dialogVisible.value = false
  
  // 延迟重置表单，确保对话框完全关闭后再执行
  setTimeout(() => {
    if (dslFormRef.value) {
      // 确保完全清空当前DSL数据
      currentDSL.value = {}
      dslFormRef.value.resetForm()
      formHasChanged.value = false  // 重置表单修改状态
      console.log('对话框已关闭，表单已重置')
    }
  }, 200)
}

// 处理表单提交
const handleFormSubmit = async (data: Partial<DSLConfigItem> | null) => {
  try {
    // 如果data为null，说明表单未发生变更
    if (data === null) {
      console.log('表单未发生变更，关闭对话框')
      dialogVisible.value = false
      return
    }
    
    console.log('提交的表单数据:', data)
    
    // 使用dialogTitle来判断是新增还是编辑操作
    const isEdit = dialogTitle.value === '编辑服务编排'
    
    // 确保fieldCodes是有效的数组
    const fieldCodes = Array.isArray(data.fieldCodes) 
      ? data.fieldCodes.filter(code => code && code.trim() !== '')
      : [];
    
    // 构建提交数据
    const submitData = {
      ...data,
      fieldCodes
    }

    submitLoading.value = true  // 开始加载

    let res;
    if (isEdit) {
      // 获取原始数据并进行深拷贝
      const originalItem = tableData.value.find(item => item.id === data.id)
      if (!originalItem) {
        ElMessage.error('未找到原始数据')
        return
      }

      // 使用Object.create和Object.assign实现深拷贝
      const originalData = Object.assign(
        Object.create(Object.getPrototypeOf(originalItem)),
        originalItem
      )

      // 重新构造originalData对象，按照指定顺序排列字段
      const orderedOriginalData: DSLConfigItem = {
        id: undefined,
        dslName: originalData.dslName || '',
        dslDescription: originalData.dslDescription || '',
        dsl: originalData.dsl || '',
        fieldCodes: originalData.fieldCodes || [],
        thrownException: originalData.thrownException ?? false,
        dslPreHeatParam: originalData.dslPreHeatParam || '',
        option: originalData.option || {},
        supportQps: originalData.supportQps || 0,
        actualQps: originalData.actualQps || 0,
        valid: undefined,
        status: undefined,
        opName: undefined,
        opMis: undefined,
        ctime: undefined,
        utime: undefined,
        lionConfigDescription: undefined
      }

      // 重新构造submitData对象，按照相同顺序排列字段
      const orderedSubmitData: DSLConfigItem = {
        id: submitData.id,
        dslName: submitData.dslName || '',
        dslDescription: submitData.dslDescription || '',
        dsl: submitData.dsl || '',
        fieldCodes: submitData.fieldCodes || [],
        thrownException: submitData.thrownException ?? false,
        dslPreHeatParam: submitData.dslPreHeatParam || '',
        option: submitData.option || {},
        supportQps: submitData.supportQps || 0,
        actualQps: submitData.actualQps || 0,
        valid: submitData.valid ?? 1,
        status: undefined,
        opName: undefined,
        opMis: undefined,
        ctime: undefined,
        utime: undefined,
        lionConfigDescription: undefined
      }

      const changeAfterData = Object.assign(
        Object.create(Object.getPrototypeOf(orderedSubmitData)),
        orderedSubmitData
      )
      changeAfterData.id = undefined
      changeAfterData.valid = undefined

      // 构造符合ChangeConfigDTO格式的请求数据
      const updateData = {
        changeDescription: '',
        changeBefore: orderedOriginalData,
        changeAfter: changeAfterData,
        requestBaseUrl: '',
        requestUri: '',
        requestData: orderedSubmitData
      }
      res = await updateDSLConfig(updateData as Omit<McmChangeItem, 'requestData'>)
    } else {
      data.option = data.option || {}
      const changeAfterData = Object.assign(
        Object.create(Object.getPrototypeOf(data)),
        data
      )
      changeAfterData.id = undefined
      changeAfterData.valid = undefined
      const addData = {
        changeDescription: '',
        changeBefore: null,
        changeAfter: changeAfterData,
        requestBaseUrl: '',
        requestUri: '',
        requestData: submitData
      }
      res = await addDSLConfig(addData as Omit<McmChangeItem, 'changeBefore'>)
    }

    if (res.code === 0) {
      // 修改成功提示方式，添加"查看任务"链接
      if (res.data && typeof res.data === 'string') {
        // 使用h函数创建VNode
        ElMessage({
          message: h('div', {}, [ 
            res.message || (isEdit ? '编辑成功' : '新增成功'),
            ' ',
            h('a', {
              href: res.data,
              target: '_blank',
              class: 'view-task-link'
            }, '查看任务')
          ]),
          type: 'success',
          dangerouslyUseHTMLString: true
        })
      } else {
        // 没有链接时展示普通成功信息
        ElMessage.success(res.message || (isEdit ? '编辑成功' : '新增成功'))
      }
      dialogVisible.value = false
      loadTableData()
    } else {
      ElMessage.error(res.message || (isEdit ? '编辑失败' : '新增失败'))
    }
  } catch (error) {
    // 判断是否为超时错误
    if (error instanceof Error && error.message.includes('timeout')) {
      ElMessage.error('请求超时，请稍后重试')
    } else {
      ElMessage.error((error as any)?.response?.data?.message || '系统异常')
    }
  } finally {
    submitLoading.value = false  // 结束加载
  }
}

// 提交对话框
const handleDialogSubmit = () => {
  if (!dslFormRef.value) {
    ElMessage.warning('表单组件未加载完成')
    return
  }
  
  // 打印日志，方便调试
  console.log('提交表单，当前DSL数据:', currentDSL.value)
  console.log('表单是否已修改:', formHasChanged.value)
  
  // 调用表单组件的提交方法
  dslFormRef.value.submitForm()
}

// 显示DSL代码
const showDSLCode = async (dsl: string) => {
  try {
    // 设置当前DSL代码（用于复制）
    currentDslCode.value = dsl;
    
    // 显示加载状态
    dslLoading.value = true;
    
    // 重置编辑器显示状态
    showDslEditor.value = false;
    isEditing.value = false;
    
    // 显示对话框
    dslCodeDialogVisible.value = true;
    
    // 关闭加载状态 - 由WorkflowGraphWrapper内部处理解析
    setTimeout(() => {
      dslLoading.value = false;
    }, 300);
  } catch (error) {
    console.error('处理DSL展示失败:', error);
    ElMessage.error('处理DSL展示失败');
    dslLoading.value = false;
  }
}

// 复制DSL代码
const copyDslCode = () => {
  const textArea = document.createElement('textarea')
  textArea.value = currentDslCode.value
  document.body.appendChild(textArea)
  textArea.select()
  document.execCommand('copy')
  document.body.removeChild(textArea)
  ElMessage.success('DSL代码已复制到剪贴板')
}

// 显示预热参数
const showPreHeatParam = (preHeatParam: string) => {
  currentPreHeatParam.value = preHeatParam || '无'
  preHeatParamDialogVisible.value = true
}

// 复制预热参数
const copyPreHeatParam = () => {
  const textArea = document.createElement('textarea')
  textArea.value = currentPreHeatParam.value
  document.body.appendChild(textArea)
  textArea.select()
  document.execCommand('copy')
  document.body.removeChild(textArea)
  ElMessage.success('预热参数已复制到剪贴板')
}

// 格式化option显示
const formatOption = (option: any) => {
  if (!option || Object.keys(option).length === 0) {
    return '无'
  }
  try {
    return JSON.stringify(option)
  } catch (e) {
    return '无'
  }
}

// 显示option详情
const showOptionDetail = (option: any) => {
  if (!option || Object.keys(option).length === 0) {
    ElMessage.info('无配置信息')
    return
  }
  try {
    currentOptionDetail.value = JSON.stringify(option, null, 2)
    optionDetailDialogVisible.value = true
  } catch (e) {
    ElMessage.error('配置格式错误')
  }
}

// 复制option详情
const copyOptionDetail = () => {
  const textArea = document.createElement('textarea')
  textArea.value = currentOptionDetail.value
  document.body.appendChild(textArea)
  textArea.select()
  document.execCommand('copy')
  document.body.removeChild(textArea)
  ElMessage.success('配置已复制到剪贴板')
}

// 初始化
onMounted(() => {
  loadTableData()
})
</script>

<style lang="scss" scoped>
.service-orchestration-page {
  padding: 10px 20px;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.service-orchestration-card {
  width: 100%;
  box-sizing: border-box;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.table-container {
  margin-bottom: 16px;
  overflow-x: auto;

  :deep(.el-table) {
    .dsl-name {
      white-space: normal;
      word-break: break-word;
      line-height: 1.5;
    }

    .el-table__cell {
      .cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      // 标签组的单元格样式特殊处理
      .tag-group {
        .cell {
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    .tag-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
      overflow-x: auto;
      padding: 2px 0;
      
      &::-webkit-scrollbar {
        height: 4px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 2px;
      }
      
      .tag-row {
        display: flex;
        flex-wrap: nowrap;
        gap: 4px;
      }
      
      .tag-item {
        flex-shrink: 0;
        margin: 0;
        font-size: 12px;
        padding: 0 4px;
        height: 22px;
        line-height: 22px;
      }
      
      .no-data {
        color: #909399;
        font-size: 13px;
      }
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.manage-btn {
  margin-right: 8px;
}

.dsl-dialog {
  :deep(.el-dialog) {
    margin-top: 8vh !important;
  }
}

.dsl-content-preview {
  white-space: normal;
  word-break: break-word;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-family: "Courier New", monospace;
  font-size: 12px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
  
  &:hover {
    background-color: #e6e9ed;
    
    &::after {
      content: "点击查看完整代码";
      position: absolute;
      bottom: 2px;
      right: 4px;
      font-size: 10px;
      color: #409eff;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 0 4px;
      border-radius: 2px;
    }
  }
}

.dsl-code-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
  
  :deep(.el-dialog__header) {
    padding: 16px 20px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 12px 20px;
  }
}

.dsl-code-container {
  background-color: #1e1e1e;
  overflow: auto;
  border-radius: 0;
  padding: 0;
  margin: 0;
  max-height: 60vh;
  width: 100%;
}

.dsl-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #e6e6e6;
  background-color: #1e1e1e;
  padding: 16px;
  margin: 0;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.empty-text {
  color: #909399;
  font-size: 13px;
}

.review-status {
  color: #ff8c00;
  font-size: 14px;
  display: flex;
  justify-content: center;
  width: 100%;
}

.dsl-flow-dialog {
  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;
    height: 85vh !important;
    max-width: 1600px;
    
    .el-dialog__body {
      flex: 1;
      overflow: hidden;
      padding: 0;
    }
  }
}

.dsl-flow-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  .loading-container {
    width: 100%;
    padding: 20px;
  }
  
  .workflow-wrapper {
    height: 100%;
    width: 100%;
    min-height: 600px;
    overflow: hidden;
    position: relative;
    
    :deep(.workflow-graph-container),
    :deep(.workflow-graph-wrapper) {
      height: 100%;
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
}

.dsl-flow-iframe {
  width: 100%;
  height: 100%;
  min-height: 70vh;
  border: none;
  display: block;
}

.config-preview {
  white-space: normal;
  word-break: break-word;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-family: "Courier New", monospace;
  font-size: 12px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
  
  &:hover {
    background-color: #e6e9ed;
    
    &::after {
      content: "点击查看完整配置";
      position: absolute;
      bottom: 2px;
      right: 4px;
      font-size: 10px;
      color: #409eff;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 0 4px;
      border-radius: 2px;
    }
  }
}

.config-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
  
  :deep(.el-dialog__header) {
    padding: 16px 20px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 12px 20px;
  }
}

.config-detail-container {
  background-color: #1e1e1e;
  overflow: auto;
  border-radius: 0;
  padding: 0;
  margin: 0;
  max-height: 60vh;
  width: 100%;
}

.config-detail {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #e6e6e6;
  background-color: #1e1e1e;
  padding: 16px;
  margin: 0;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style> 