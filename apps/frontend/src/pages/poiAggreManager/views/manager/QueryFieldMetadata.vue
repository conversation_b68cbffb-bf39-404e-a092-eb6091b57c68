<template>
  <div class="query-metadata-page">
    <el-card class="query-metadata-card">
      <template #header>
        <div class="card-header">
          <span>查询字段管理</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="字段Code">
          <el-input v-model="searchForm.fieldCode" placeholder="请输入字段Code" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="字段名称">
          <el-input v-model="searchForm.fieldName" placeholder="请输入字段名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="字段类型">
          <el-select v-model="searchForm.type" placeholder="请选择" style="width: 100px">
            <el-option v-for="type in FIELD_TYPE_OPTIONS" :key="type.value" :label="type.label" :value="type.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="本地/动态">
          <el-select v-model="searchForm.handlerType" placeholder="请选择" style="width: 100px">
            <el-option label="本地" :value="0" />
            <el-option label="动态" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon>新增
          </el-button>
          <el-button type="primary" @click="handleBatchAdd">
            <el-icon>
              <Plus />
            </el-icon>批量新增
          </el-button>
          <el-button type="danger" :disabled="!selectedRows.length" @click="handleBatchDelete">
            <el-icon>
              <Delete />
            </el-icon>批量删除
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table v-loading="tableLoading" :data="tableData" border stripe style="width: 100%"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40" fixed="left"
            :selectable="(row: FieldMetadataItem) => row.status === 0" />
          <el-table-column prop="id" label="ID" width="50" fixed />
          <el-table-column prop="fieldCode" label="字段Code" :width="columnWidths.fieldCode - 10" fixed min-width="100" />
          <el-table-column prop="fieldProperty" label="字段Property" :width="columnWidths.fieldProperty - 10"
            min-width="120" />
          <el-table-column prop="fieldName" label="字段名称" :width="columnWidths.fieldName" min-width="90" />
          <el-table-column prop="description" label="字段描述" min-width="240" show-overflow-tooltip />
          <el-table-column prop="type" label="字段类型" width="90">
            <template #default="{ row }">
              <el-tag type="warning">{{ getFieldTypeName(row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="defaultValue" label="默认值" :width="columnWidths.defaultValue">
            <template #default="{ row }">
              {{ formatDefaultValue(row.defaultValue, row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="handlerType" label="本地/动态" width="90">
            <template #default="{ row }">
              <el-tag :type="row.handlerType === 1 ? 'warning' : 'info'" size="small">
                {{ row.handlerType === 1 ? '动态' : '本地' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="syncedField" label="同步字段" width="85">
            <template #default="{ row }">
              <el-tag :type="row.syncedField ? 'success' : 'info'" size="small">
                {{ row.syncedField ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dependentFields" label="依赖字段" :width="columnWidths.dependentFields">
            <template #default="{ row }">
              <div class="dependent-fields-container">
                <template v-if="row.dependentFields && row.dependentFields.length">
                  <el-tag v-for="field in row.dependentFields" :key="field" class="dependent-field-tag" size="small"
                    type="warning">
                    {{ field }}
                  </el-tag>
                </template>
                <span v-else class="no-data">无</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="opMis" label="操作人Mis" :width="columnWidths.opMis" min-width="100" />
          <el-table-column prop="utime" label="更新时间" :width="160">
            <template #default="{ row }">
              {{ formatTimestamp(row.utime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <template v-if="row.status === 0">
                <el-button type="primary" class="manage-btn" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="danger" plain size="small" @click="handleDelete(row)">删除</el-button>
              </template>
              <span v-else class="review-status">审核中</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false"
      :align-center="true" :before-close="closeDialog">
      <query-field-metadata-form ref="formRef" :initial-data="formData" :existing-fields="existingFields"
        @submit="handleFormSubmit" @form-change="handleFormChange" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="dialogLoading" :disabled="dialogLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量新增对话框 -->
    <el-dialog v-model="batchDialogVisible" title="批量新增字段元数据" width="800px" :close-on-click-modal="false"
      :align-center="true" :before-close="closeBatchDialog">
      <batch-add-field-metadata-form ref="batchFormRef" @submit="handleBatchFormSubmit"
        @loading-state-change="handleBatchLoadingChange" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeBatchDialog">取消</el-button>
          <el-button type="primary" @click="submitBatchForm" :loading="batchDialogLoading"
            :disabled="batchDialogLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { getFieldMetadataList, addFieldMetadata, updateFieldMetadata, batchDeleteFieldMetadata, batchAddFieldMetadata } from '../../request'
import type { FieldMetadataItem, FieldType, McmChangeItem } from '../../types'
import { FIELD_TYPE_OPTIONS } from '../../types'
import QueryFieldMetadataForm from './component/QueryFieldMetadataForm.vue'
import BatchAddFieldMetadataForm from './component/BatchAddFieldMetadataForm.vue'

// 格式化时间戳
const formatTimestamp = (timestamp: number | string | undefined): string => {
  if (!timestamp) return '-'
  const date = new Date(Number(timestamp) * 1000)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const formRef = ref<InstanceType<typeof QueryFieldMetadataForm>>()
const tableLoading = ref(false)
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const selectedRows = ref<FieldMetadataItem[]>([])
const dialogLoading = ref(false)  // 添加对话框loading状态
const formHasChanged = ref(false) // 表单是否有修改
const deleteConfirmButtonInstance = ref<any>(null)  // 修复变量初始化

interface SearchFormType {
  fieldCode: string
  fieldName: string
  type: FieldType | undefined
  valid: 0 | 1 | undefined
  handlerType: 0 | 1 | undefined
}

// 搜索表单
const searchForm = reactive<SearchFormType>({
  fieldCode: '',
  fieldName: '',
  type: undefined,
  valid: undefined,
  handlerType: undefined
})

// 表格数据
const tableData = ref<FieldMetadataItem[]>([])
const allFieldsData = ref<FieldMetadataItem[]>([]) // 新增：存储所有字段数据

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = ref<{
  id: number | undefined
  fieldCode: string
  fieldProperty: string
  fieldName: string
  description: string
  type: FieldType | undefined
  defaultValue: string
  dependentFields: string[]
  syncedField: boolean
  valid: 0 | 1
  operatorName: string | undefined
  operatorMis: string | undefined
  lionConfigDescription: string | undefined
  handlerType: number
}>({
  id: undefined,
  fieldCode: '',
  fieldProperty: '',
  fieldName: '',
  description: '',
  type: undefined,
  defaultValue: '',
  dependentFields: [],
  syncedField: false,
  valid: 1,
  operatorName: '',
  operatorMis: '',
  lionConfigDescription: '',
  handlerType: 1
})

// 获取字段类型名称
const getFieldTypeName = (type: string) => {
  const fieldType = FIELD_TYPE_OPTIONS.find(t => t.value === type)
  return fieldType ? fieldType.label : type
}

// 格式化默认值
const formatDefaultValue = (value: string, type: string) => {
  // 如果值为null，直接返回null
  if (value === 'null') {
    return value
  }

  // 如果是String类型，确保值两侧有双引号
  if (type === '1') {
    // 如果值已经包含双引号，则直接返回
    if (value.startsWith('"') && value.endsWith('"')) {
      return value
    }
    // 否则添加双引号
    return `"${value}"`
  }

  return value
}

// 计算字符串显示宽度
const getTextWidth = (text: string): number => {
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  if (!context) return 0

  // 设置与实际表格单元格相同的字体
  context.font = '14px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif'

  return Math.ceil(context.measureText(text).width)
}

// 计算数组内容的最大宽度
const getArrayContentWidth = (arr: any[], formatter?: (item: any) => string): number => {
  if (!arr?.length) return 0
  return Math.max(...arr.map(item => {
    const text = formatter ? formatter(item) : String(item)
    return getTextWidth(text)
  }))
}

// 列标签映射
const columnLabels: Record<string, string> = {
  id: 'ID',
  fieldCode: '字段Code',
  fieldProperty: '字段Property',
  fieldName: '字段名称',
  description: '字段描述',
  type: '字段类型',
  defaultValue: '默认值',
  dependentFields: '依赖字段',
  syncedField: '同步字段',
  opMis: '操作人Mis',
  utime: '更新时间',
  lionConfigDescription: 'Lion配置描述',
}

// 列宽度数据
const columnWidths = ref<Record<string, number>>({})

// 计算列宽度
const calculateColumnWidth = (data: FieldMetadataItem[], prop: keyof typeof columnLabels): number => {
  if (!data.length) return 0

  // 获取表头宽度
  const headerWidth = getTextWidth(columnLabels[prop] || prop)

  // 根据不同的字段类型计算内容宽度
  let contentWidth = 0
  switch (prop) {
    case 'type':
      contentWidth = Math.max(...data.map(row =>
        getTextWidth(getFieldTypeName(row.type))
      ))
      break
    case 'dependentFields':
      contentWidth = Math.max(...data.map(row =>
        getArrayContentWidth(row.dependentFields || [])
      ))
      break
    case 'syncedField':
      contentWidth = getTextWidth('否') + 40 // 添加tag的内边距
      break
    case 'utime':
      contentWidth = Math.max(...data.map(row =>
        getTextWidth(formatTimestamp(row.utime || 0))
      ))
      break
    case 'defaultValue':
      contentWidth = Math.max(...data.map(row => {
        const value = row.defaultValue || ''
        // 如果是String类型，需要考虑双引号的长度
        if (row.type === '1') {
          // 如果值已经包含双引号，则不需要额外添加
          if (value.startsWith('"') && value.endsWith('"')) {
            return getTextWidth(value)
          }
          // 否则需要加上两个双引号的宽度
          return getTextWidth(value) + getTextWidth('""')
        }
        return getTextWidth(String(value))
      }))
      break
    default:
      contentWidth = Math.max(...data.map(row => {
        const value = row[prop as keyof FieldMetadataItem]
        return getTextWidth(String(value || ''))
      }))
  }

  // 添加内边距和边框宽度
  const padding = 32 // 左右各16px的内边距
  const minWidth = 60 // 最小宽度

  return Math.max(minWidth, Math.ceil(Math.max(headerWidth, contentWidth)) + padding)
}

// 更新列宽度
const updateColumnWidths = () => {
  if (!tableData.value.length) return

  const newWidths: Record<string, number> = {}
  Object.keys(columnLabels).forEach(prop => {
    newWidths[prop] = calculateColumnWidth(tableData.value, prop as keyof typeof columnLabels)
  })
  columnWidths.value = newWidths
}

// 获取数据列表
const fetchData = async () => {
  try {
    tableLoading.value = true
    const params = {
      ...searchForm,
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize
    }
    const res = await getFieldMetadataList(params)
    if (res.code === 0) {
      tableData.value = res.data.records
      pagination.total = res.data.total
      // 更新列宽度
      updateColumnWidths()
    } else {
      ElMessage.error(res.message || '加载数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    tableLoading.value = false
  }
}

// 搜索
const handleSearch = async () => {
  pagination.currentPage = 1
  await fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.fieldCode = ''
  searchForm.fieldName = ''
  searchForm.type = undefined
  searchForm.handlerType = undefined
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  fetchData()
}

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  fetchData()
}

// 处理表格选择变化
const handleSelectionChange = (rows: FieldMetadataItem[]) => {
  selectedRows.value = rows
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  formData.value = {
    id: undefined,
    fieldCode: '',
    fieldProperty: '',
    fieldName: '',
    description: '',
    type: undefined,
    defaultValue: '',
    dependentFields: [],
    syncedField: false,
    valid: 1,
    operatorName: '',
    operatorMis: '',
    lionConfigDescription: '',
    handlerType: 1
  }
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: FieldMetadataItem) => {
  console.log('row', row)
  dialogType.value = 'edit'
  formData.value = {
    id: row.id,
    fieldCode: row.fieldCode,
    fieldProperty: row.fieldProperty || '',
    fieldName: row.fieldName,
    description: row.description || '',
    type: row.type as FieldType,
    defaultValue: row.defaultValue || '',
    dependentFields: row.dependentFields || [],
    syncedField: row.syncedField ?? false,
    handlerType: row.handlerType ?? 1,
    valid: 1,
    operatorName: row.opName,
    operatorMis: row.opMis,
    lionConfigDescription: row.lionConfigDescription || ''
  }
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetForm()
  }
  // 重置formData
  formData.value = {
    id: undefined,
    fieldCode: '',
    fieldProperty: '',
    fieldName: '',
    description: '',
    type: undefined,
    defaultValue: '',
    dependentFields: [],
    syncedField: false,
    valid: 1,
    operatorName: '',
    operatorMis: '',
    lionConfigDescription: '',
    handlerType: 1
  }
}

// 删除
const handleDelete = async (row: FieldMetadataItem) => {
  if (!row.id) {
    ElMessage.error('记录ID不存在')
    return
  }
  try {
    await ElMessageBox.confirm('确认删除该字段元数据吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置确认按钮为loading状态
          instance.confirmButtonLoading = true
          
          // 不调用done()，让对话框保持打开状态
          // 执行删除操作
          const deleteData = {
            changeDescription: '',
            changeBefore: { "QueryFieldMetadataList": [row] },
            changeAfter: null,
            requestBaseUrl: '',
            requestUri: '',
            requestData: { "ids": [row.id] }
          }
          
          // 执行删除操作
          batchDeleteFieldMetadata(deleteData as Omit<McmChangeItem, 'changeAfter'>)
            .then(async res => {
              if (res.code === 0) {
                // 修改成功提示方式，添加"查看任务"链接
                if (res.data && typeof res.data === 'string') {
                  // 使用h函数创建VNode
                  ElMessage({
                    message: h('div', {}, [
                      res.message || '删除成功',
                      ' ',
                      h('a', {
                        href: res.data,
                        target: '_blank',
                        class: 'view-task-link'
                      }, '查看任务')
                    ]),
                    type: 'success',
                    dangerouslyUseHTMLString: true
                  })
                } else {
                  // 没有链接时展示普通成功信息
                  ElMessage.success(res.message || '删除成功')
                }
                await fetchAllFieldsData() // 更新所有字段数据
                await fetchData() // 更新分页数据
              } else {
                ElMessage.error(res.message || '删除失败')
              }
            })
            .catch(error => {
              console.error('删除字段元数据失败', error)
              ElMessage.error((error as any)?.response?.data?.message || '删除字段元数据失败')
            })
            .finally(() => {
              // 操作完成后关闭对话框
              instance.confirmButtonLoading = false
              done()
            })
        } else {
          // 取消操作，直接关闭对话框
          done()
        }
      }
    })
  } catch (error) {
    // 这里捕获的是用户取消操作的情况
    if (error !== 'cancel') {
      console.error('删除字段元数据失败', error)
      ElMessage.error((error as any)?.response?.data?.message || '删除字段元数据失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedRows.value.length) {
    return
  }
  try {
    await ElMessageBox.confirm(`确认删除选中的 ${selectedRows.value.length} 条数据吗？`, '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置确认按钮为loading状态
          instance.confirmButtonLoading = true
          deleteConfirmButtonInstance.value = instance
          
          // 不调用done()，让对话框保持打开状态
          // 执行删除操作
          const ids = selectedRows.value.map(row => row.id).filter((id): id is number => id !== undefined)
          if (ids.length !== selectedRows.value.length) {
            ElMessage.error('部分记录ID不存在')
            instance.confirmButtonLoading = false
            done() // 出错时关闭对话框
            return
          }
          
          const deleteData = {
            changeDescription: '',
            changeBefore: { "QueryFieldMetadataList": selectedRows.value },
            changeAfter: null,
            requestBaseUrl: '',
            requestUri: '',
            requestData: { "ids": ids }
          }
          
          // 执行删除操作
          batchDeleteFieldMetadata(deleteData as Omit<McmChangeItem, 'changeAfter'>)
            .then(async res => {
              if (res.code === 0) {
                // 修改成功提示方式，添加"查看任务"链接
                if (res.data) {
                  // 使用h函数创建VNode
                  ElMessage({
                    message: h('div', {}, [
                      res.message,
                      ' ',
                      h('a', {
                        href: res.data,
                        target: '_blank',
                        class: 'view-task-link'
                      }, '查看任务')
                    ]),
                    type: 'success',
                    dangerouslyUseHTMLString: true
                  })
                } else {
                  // 没有链接时展示普通成功信息
                  ElMessage.success(res.message)
                }
                await fetchAllFieldsData() // 更新所有字段数据
                await fetchData() // 更新分页数据
              } else {
                ElMessage.error(res.message)
              }
            })
            .catch(error => {
              console.error('批量删除字段元数据失败', error)
              ElMessage.error((error as any)?.response?.data?.message || '批量删除字段元数据失败')
            })
            .finally(() => {
              // 操作完成后关闭对话框
              instance.confirmButtonLoading = false
              done()
            })
        } else {
          // 取消操作，直接关闭对话框
          done()
        }
      }
    })
  } catch (error) {
    // 这里捕获的是用户取消操作的情况
    if (error !== 'cancel') {
      console.error('批量删除字段元数据失败', error)
      ElMessage.error((error as any)?.response?.data?.message || '批量删除字段元数据失败')
    }
  }
}

// 关闭对话框
const closeDialog = () => {
  // 如果正在提交，不允许关闭
  if (dialogLoading.value) return

  // 关闭对话框
  dialogVisible.value = false

  // 延迟重置表单，确保对话框完全关闭后再执行
  setTimeout(() => {
    resetForm()
  }, 200)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  dialogLoading.value = true  // 开启loading
  try {
    await formRef.value.submitForm()
  } catch (error) {
    console.error('表单提交失败:', error)
    dialogLoading.value = false  // 关闭loading
  }
}

// 处理表单提交
const handleFormSubmit = async (data: Partial<FieldMetadataItem> | null) => {
  if (!data) {
    dialogVisible.value = false
    dialogLoading.value = false  // 关闭loading
    return
  }

  // 如果是表单校验失败的情况
  if ('valid' in data && data.valid === 0) {
    dialogLoading.value = false  // 关闭loading
    return
  }

  // 根据字段类型处理默认值，如果是字符串，需要去除两端的"
  if (data.type === '1' && data.defaultValue !== 'null') {
    data.defaultValue = data.defaultValue?.substring(1, data.defaultValue.length - 1)
  }

  try {
    let res;
    if (dialogType.value === 'add') {
      const changeAfterData = Object.assign(
        Object.create(Object.getPrototypeOf(data)),
        data
      )
      changeAfterData.id = undefined
      changeAfterData.valid = undefined
      
      // 如果handlerType为0，设置相关字段为undefined
      if (changeAfterData.handlerType === 0) {
        changeAfterData.lionConfigDescription = undefined
        changeAfterData.syncedField = undefined
        changeAfterData.dependentFields = undefined
      }
      
      const addData = {
        changeDescription: '',
        changeBefore: null,
        changeAfter: changeAfterData,
        requestBaseUrl: '',
        requestUri: '',
        requestData: data
      }
      res = await addFieldMetadata(addData as Omit<McmChangeItem, 'changeBefore'>)
    } else {
      // 获取原始数据并进行深拷贝
      const originalItem = tableData.value.find(item => item.id === data.id)
      if (!originalItem) {
        ElMessage.error('未找到原始数据')
        return
      }

      // 使用Object.create和Object.assign实现深拷贝
      const originalData = Object.assign(
        Object.create(Object.getPrototypeOf(originalItem)),
        originalItem
      )

      // 重新构造originalData对象，按照指定顺序排列字段
      const orderedOriginalData: Partial<FieldMetadataItem> = {
        id: undefined,
        fieldCode: originalData.fieldCode,
        fieldProperty: originalData.fieldProperty || '',
        fieldName: originalData.fieldName,
        description: originalData.description || '',
        type: originalData.type,
        defaultValue: originalData.defaultValue || '',
        dependentFields: originalData.dependentFields || [],
        syncedField: !!originalData.syncedField,
        handlerType: originalData.handlerType,
        valid: undefined,
        status: undefined, // 保留status字段，默认为0
        opName: undefined,
        opMis: undefined
      };

      // data对象中也需要包含status字段
      data.status = undefined;
      data.lionConfigDescription = undefined;

      const changeAfterData = Object.assign(
        Object.create(Object.getPrototypeOf(data)),
        data
      )
      changeAfterData.id = undefined
      changeAfterData.valid = undefined
      
      // 如果handlerType为0，设置相关字段为undefined
      if (changeAfterData.handlerType === 0) {
        orderedOriginalData.lionConfigDescription = undefined
        orderedOriginalData.syncedField = undefined
        orderedOriginalData.dependentFields = undefined

        changeAfterData.lionConfigDescription = undefined
        changeAfterData.syncedField = undefined
        changeAfterData.dependentFields = undefined
      }

      // 构造符合ChangeConfigDTO格式的请求数据
      const updateData = {
        changeDescription: '',
        changeBefore: orderedOriginalData,
        changeAfter: changeAfterData,
        requestBaseUrl: '',
        requestUri: '',
        requestData: data
      }
      res = await updateFieldMetadata(updateData as Omit<McmChangeItem, 'requestData'>)
    }
    if (res.code === 0) {
      // 修改成功提示方式，添加"查看任务"链接
      if (res.data && typeof res.data === 'string') {
        // 使用h函数创建VNode
        ElMessage({
          message: h('div', {}, [
            res.message || (dialogType.value === 'add' ? '新增成功' : '更新成功'),
            ' ',
            h('a', {
              href: res.data,
              target: '_blank',
              class: 'view-task-link'
            }, '查看任务')
          ]),
          type: 'success',
          dangerouslyUseHTMLString: true
        })
      } else {
        // 没有链接时展示普通成功信息
        ElMessage.success(res.message || (dialogType.value === 'add' ? '新增成功' : '更新成功'))
      }
      dialogVisible.value = false
      await fetchAllFieldsData() // 更新所有字段数据
      await fetchData() // 更新分页数据
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    console.error(dialogType.value === 'add' ? '新增字段元数据失败' : '更新字段元数据失败', error)
    // 判断是否为超时错误
    const errorMessage = error instanceof Error && error.message.includes('timeout')
      ? '请求超时，请稍后重试'
      : (dialogType.value === 'add' ? '新增字段元数据失败' : '更新字段元数据失败')
    ElMessage.error(errorMessage)
  } finally {
    dialogLoading.value = false  // 确保关闭loading
  }
}

// 获取所有字段数据
const fetchAllFieldsData = async () => {
  try {
    const params = {
      pageNo: 1,
      pageSize: 9999, // 设置一个足够大的数字以获取所有数据
      valid: 1 // 只获取有效的字段
    }
    const res = await getFieldMetadataList(params)
    if (res.code === 0) {
      allFieldsData.value = res.data.records
    } else {
      console.error('获取所有字段数据失败:', res.message)
    }
  } catch (error) {
    console.error('获取所有字段数据失败:', error)
  }
}

// 修改 existingFields 计算属性
const existingFields = computed(() =>
  allFieldsData.value.map(item => item.fieldCode)
)

const dialogTitle = computed(() => dialogType.value === 'add' ? '新增字段元数据' : '编辑字段元数据')

// 监听数据变化，更新列宽
watch(() => tableData.value, () => {
  updateColumnWidths()
}, { deep: true })

// 处理表单变化
const handleFormChange = (hasChanged: boolean) => {
  console.log('表单变化状态:', hasChanged)
  formHasChanged.value = hasChanged
}

const batchDialogVisible = ref(false)
const batchDialogLoading = ref(false)
const batchFormRef = ref<InstanceType<typeof BatchAddFieldMetadataForm>>()

// 批量新增
const handleBatchAdd = () => {
  // 先重置表单，确保每次打开对话框时都是空白表单
  if (batchFormRef.value) {
    batchFormRef.value.resetForm()
  }
  batchDialogVisible.value = true
}

// 关闭批量新增对话框
const closeBatchDialog = () => {
  if (batchDialogLoading.value) return
  batchDialogVisible.value = false
  if (batchFormRef.value) {
    batchFormRef.value.resetForm()
  }
}

// 处理批量加载状态变化
const handleBatchLoadingChange = (loading: boolean) => {
  batchDialogLoading.value = loading
}

// 提交批量新增表单
const submitBatchForm = async () => {
  if (!batchFormRef.value) return

  // 使用组件的submitForm方法，它会自动设置loading状态
  const success = await batchFormRef.value.submitForm()
  if (!success) {
    batchDialogLoading.value = false
    return
  }

  try {
    batchDialogLoading.value = true
    const fields = batchFormRef.value.getFormData()

    // 处理字符串类型的默认值
    const processedFields = fields.map(field => {
      // 深拷贝字段
      const newField: Partial<FieldMetadataItem> = { ...field }

      // 如果是字符串类型，需要去除两端的双引号
      if (newField.type === '1' && newField.defaultValue && newField.defaultValue !== 'null') {
        newField.defaultValue = newField.defaultValue.startsWith('"') && newField.defaultValue.endsWith('"')
          ? newField.defaultValue.substring(1, newField.defaultValue.length - 1)
          : newField.defaultValue
      }

      return newField
    })

    // 为changeAfter创建深拷贝并处理handlerType为0的情况
    const changeAfterFields = processedFields.map(field => {
      const newField = { ...field }
      if (newField.handlerType === 0) {
        newField.lionConfigDescription = undefined
        newField.syncedField = undefined
        newField.dependentFields = undefined
      }
      return newField
    })

    const addData = {
      changeDescription: '',
      changeBefore: null,
      changeAfter: { "batchAddList": changeAfterFields },
      requestBaseUrl: '',
      requestUri: '',
      requestData: { "batchAddList": processedFields }
    }

    const res = await batchAddFieldMetadata(addData as Omit<McmChangeItem, 'changeBefore'>)
    if (res.code === 0) {
      if (res.data && typeof res.data === 'string') {
        ElMessage({
          message: h('div', {}, [
            res.message || '批量新增成功',
            ' ',
            h('a', {
              href: res.data,
              target: '_blank',
              class: 'view-task-link'
            }, '查看任务')
          ]),
          type: 'success',
          dangerouslyUseHTMLString: true
        })
      } else {
        ElMessage.success(res.message || '批量新增成功')
      }
      batchDialogVisible.value = false
      if (batchFormRef.value) {
        batchFormRef.value.resetForm()
      }
      await fetchAllFieldsData() // 更新所有字段数据
      await fetchData() // 更新分页数据
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    console.error('批量新增字段元数据失败', error)
    // 判断是否为超时错误
    const errorMessage = error instanceof Error && error.message.includes('timeout')
      ? '请求超时，请稍后重试'
      : '批量新增字段元数据失败'
    ElMessage.error(errorMessage)
  } finally {
    batchDialogLoading.value = false
  }
}

// 处理批量表单提交
const handleBatchFormSubmit = async (data: any) => {
  if (!data) {
    batchDialogVisible.value = false
    batchDialogLoading.value = false
    return
  }
  // 表单组件提交后，数据会通过事件发送过来
  // 此时表单组件已经设置了loading状态，我们只需处理API请求
}

onMounted(async () => {
  await fetchAllFieldsData() // 先获取所有字段数据
  await fetchData() // 然后获取分页数据
})
</script>

<style lang="scss" scoped>
.query-metadata-page {
  padding: 10px 20px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;

  .query-metadata-card {
    width: 100%;
    box-sizing: border-box;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
    }

    .search-form {
      margin-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .table-toolbar {
      margin-bottom: 20px;

      .toolbar-left {
        display: flex;
        gap: 10px;
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }

    .table-container {
      width: 100%;
      overflow-x: auto;

      :deep(.el-table) {
        --el-table-border-color: var(--el-border-color-lighter);
        width: fit-content;
        min-width: 100%;

        .el-table__body-wrapper {
          overflow-x: auto !important;
        }

        .el-table__fixed-right {
          height: 100% !important;
          box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
        }

        .el-table__fixed-left {
          height: 100% !important;
          box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.12);
        }
      }
    }
  }
}

.manage-btn {
  margin-right: 8px;
}

.field-code-text {
  display: inline-block;
  white-space: normal;
  word-break: keep-all;
  line-height: 1.5;
}

.field-code,
.field-name,
.field-property,
.field-desc,
.default-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dependent-fields-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  padding: 4px 0;
  overflow-x: auto;

  .no-data {
    color: #909399;
    font-size: 13px;
  }

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 2px;
  }
}

.dependent-field-tag {
  flex-shrink: 0;
  margin: 0;
  font-size: 12px;
  padding: 0 4px;

  :deep(.el-tag--info) {
    --el-tag-bg-color: var(--el-color-info-light-9);
    --el-tag-border-color: var(--el-color-info-light-5);
    --el-tag-text-color: var(--el-text-color-regular);
  }
}

.review-status {
  color: #ff8c00;
  // color: #ff0059;
  font-size: 14px;
  display: flex;
  justify-content: center;
  width: 100%;
}

.view-task-link {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    color: #66b1ff;
  }
}
</style>