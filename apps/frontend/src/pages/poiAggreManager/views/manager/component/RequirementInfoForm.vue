<template>
  <div class="requirement-info-form">
    <el-card shadow="never" class="requirement-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">需求信息</span>
        </div>
      </template>
      
      <el-form 
        ref="formRef"
        :model="requirementForm" 
        :rules="rules"
        label-position="top" 
        class="requirement-form"
      >
        <div class="link-fields">
          <el-form-item label="需求/PRD链接" class="link-item">
            <el-input 
              v-model="requirementForm.prdLink" 
              placeholder="请输入需求文档或PRD链接" 
              clearable
              @blur="handleBlur('prdLink')"
              @input="handleInput('prdLink')"
            />
          </el-form-item>
          
          <el-form-item label="技术方案Wiki链接" class="link-item">
            <el-input 
              v-model="requirementForm.wikiLink" 
              placeholder="请输入技术方案Wiki链接" 
              clearable
              @blur="handleBlur('wikiLink')"
              @input="handleInput('wikiLink')"
            />
          </el-form-item>
        </div>

        <el-form-item label="需求分析结果" prop="analysisResult">
          <div class="ai-textarea-wrapper">
            <el-input 
              v-model="requirementForm.analysisResult" 
              type="textarea" 
              :rows="11"
              :placeholder="analysisResultPlaceholder" 
              :maxlength="5000"
              show-word-limit
              @blur="handleBlur('analysisResult')"
              @input="handleInput('analysisResult')"
              @focus="handleAnalysisResultFocus"
            />
            <el-button
              class="ai-btn"
              type="primary"
              @click="handleAiConfig"
              v-if="false"
            >
              AI 生成配置
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
    <el-dialog
      v-model="aiDialogVisible"
      width="320px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
      class="ai-wait-dialog"
    >
      <div class="ai-dialog-content">
        <el-icon class="ai-dialog-icon" size="32"><Loading /></el-icon>
        <div class="ai-dialog-text">{{ aiDialogText }}</div>
        <div class="ai-dialog-tip">预计等待时间约2分钟</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch, onMounted } from 'vue';
import { RequirementInfo } from '../../../types/index';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { getAiAccessGuideConfig } from '../../../request/index';
import { Loading } from '@element-plus/icons-vue';

const props = defineProps({
  initialData: {
    type: Object as () => RequirementInfo,
    default: () => ({
      prdLink: '',
      analysisResult: '',
      wikiLink: ''
    })
  }
});

const emit = defineEmits<{
  (e: 'update', data: RequirementInfo): void;
  (e: 'valid-change', valid: boolean): void;
  (e: 'ai-config', data: any): void;
}>();

// 需求分析结果模版
const analysisResultPlaceholder =
    "【接入方式】DTS / Mafka / RPC\n" +
    "【是否外部Poi】是 / 否\n" +
    "【接入字段】\n" +
    "\t【数据源字段Code】fieldCode\n" +
    "\t【接入后字段Code】example_fieldCode\n" +
    "【字段映射方式】直接映射 / 脚本处理\n" +
    "【其他】\n" +
    "\t【脚本代码】（可选）\n" +
    "\t【数据库表】（可选）\n" +
    "\t【RPC-Thrift服务信息】URL、方法名、Appkey、端口等（可选）\n" +
    "\t【备注】（可选）";

const formRef = ref<FormInstance>();
const formValid = ref(false);
// 追踪已交互的字段
const touchedFields = ref<Set<string>>(new Set());

// 处理需求分析结果输入框获得焦点事件
const handleAnalysisResultFocus = () => {
  if (!requirementForm.value.analysisResult) {
    requirementForm.value.analysisResult = analysisResultPlaceholder;
    // 记录用户已交互的字段
    touchedFields.value.add('analysisResult');
    // 触发验证
    validateForm();
  }
};

// 检验URL格式的函数
const isValidUrl = (url: string) => {
  return /^https?:\/\//.test(url);
};

// 表单验证规则
const rules = ref<FormRules>({
  analysisResult: [
    { required: true, message: '请输入需求分析结果', trigger: 'blur' }
  ],
  wikiLink: [
    { validator: (rule, value, callback) => {
      if (value && !isValidUrl(value)) {
        callback(new Error('链接必须以http://或https://开头'));
      } else {
        callback();
      }
    }, trigger: 'blur' }
  ]
});

const requirementForm = ref<RequirementInfo>({
  prdLink: props.initialData.prdLink || '',
  analysisResult: props.initialData.analysisResult || '',
  wikiLink: props.initialData.wikiLink || ''
});

// 监听表单变化并向父组件发送更新
const updateParent = () => {
  emit('update', {
    prdLink: requirementForm.value.prdLink,
    analysisResult: requirementForm.value.analysisResult,
    wikiLink: requirementForm.value.wikiLink
  });
};

// 创建一个简单的防抖函数
let validateTimer: number | null = null;
const validateForm = () => {
  if (validateTimer) {
    clearTimeout(validateTimer);
  }
  
  validateTimer = window.setTimeout(() => {
    if (!formRef.value) return;
    
    formRef.value.validate()
      .then(() => {
        if (formValid.value !== true) {
          formValid.value = true;
          emit('valid-change', true);
        }
      })
      .catch(() => {
        if (formValid.value !== false) {
          formValid.value = false;
          emit('valid-change', false);
        }
      })
      .finally(() => {
        validateTimer = null;
      });
  }, 300);
};

// 监听表单字段的blur事件，记录已交互字段并只验证当前字段
const handleBlur = (field: string) => {
  // 记录用户已交互的字段
  touchedFields.value.add(field);
  
  if (!formRef.value) return;
  
  // 只验证当前失去焦点的字段
  formRef.value.validateField(field).then(() => {
    // 仅检查必填字段是否已交互并通过验证
    const requiredFieldsTouched = ['analysisResult']
      .every(field => touchedFields.value.has(field));
      
    if (requiredFieldsTouched) {
      validateForm();
    }
  }).catch(() => {
    // 字段验证失败时，确保表单状态为无效
    if (formValid.value !== false) {
      formValid.value = false;
      emit('valid-change', false);
    }
  });
};

// 输入内容变化时触发验证
let inputDebounceTimers: Record<string, number> = {};
const handleInput = (field: string) => {
  // 记录用户已交互的字段
  touchedFields.value.add(field);
  
  // 清除之前的定时器
  if (inputDebounceTimers[field]) {
    clearTimeout(inputDebounceTimers[field]);
  }
  
  if (!formRef.value) return;
  
  // 只验证当前输入的字段
  formRef.value.validateField(field).then(() => {
    // 仅检查必填字段是否已交互并通过验证
    const requiredFieldsTouched = ['analysisResult']
      .every(field => touchedFields.value.has(field));
      
    if (requiredFieldsTouched) {
      validateForm();
    }
  }).catch(() => {
    // 字段验证失败时，确保表单状态为无效
    if (formValid.value !== false) {
      formValid.value = false;
      emit('valid-change', false);
    }
  });
  
  inputDebounceTimers[field] = 0;
};

// 只监听表单值变化，向父组件发送更新
watch(requirementForm, () => {
  updateParent();
}, { deep: true });

// 初始挂载时设置表单为无效状态，不执行验证
onMounted(() => {
  // 如果初始analysisResult有值，则执行验证
  if (requirementForm.value.analysisResult) {
    touchedFields.value.add('analysisResult');
    validateForm();
  } else {
    // 告知父组件表单初始状态为无效，禁用"下一步"按钮
    formValid.value = false;
    emit('valid-change', false);
  }
});

// 添加设置表单数据的方法，用于从缓存恢复
const setFormData = (data: RequirementInfo) => {
  if (data) {
    // 更新表单数据
    requirementForm.value.prdLink = data.prdLink || requirementForm.value.prdLink;
    requirementForm.value.analysisResult = data.analysisResult || requirementForm.value.analysisResult;
    requirementForm.value.wikiLink = data.wikiLink || requirementForm.value.wikiLink;
    
    // 如果表单有值，设置为已交互
    if (requirementForm.value.analysisResult) {
      touchedFields.value.add('analysisResult');
    }
    if (requirementForm.value.prdLink) {
      touchedFields.value.add('prdLink');
    }
    if (requirementForm.value.wikiLink) {
      touchedFields.value.add('wikiLink');
    }
    
    // 验证表单
    validateForm();
  }
};

const aiDialogVisible = ref(false);
const aiDialogText = ref('正在分析需求…');
let aiDialogTimer: number | null = null;
let aiDialogStartTime: number | null = null;

const startAiDialog = () => {
  aiDialogVisible.value = true;
  aiDialogText.value = '正在分析需求…';
  aiDialogStartTime = Date.now();
  if (aiDialogTimer) clearInterval(aiDialogTimer);
  aiDialogTimer = window.setInterval(() => {
    const elapsed = ((Date.now() - (aiDialogStartTime || 0)) / 1000);
    if (elapsed > 100) {
      aiDialogText.value = '正在整理结果…';
    } else if (elapsed > 10) {
      aiDialogText.value = '正在生成配置…';
    } else {
      aiDialogText.value = '正在分析需求…';
    }
  }, 500);
};

const stopAiDialog = () => {
  aiDialogVisible.value = false;
  aiDialogText.value = '正在分析需求…';
  if (aiDialogTimer) {
    clearInterval(aiDialogTimer);
    aiDialogTimer = null;
  }
  aiDialogStartTime = null;
};

const handleAiConfig = async () => {
  const analysis = requirementForm.value.analysisResult?.trim();
  if (!analysis || analysis === analysisResultPlaceholder.trim()) {
    ElMessage.warning('请先填写有效的需求分析结果');
    return;
  }
  // 构造请求参数
  const params = {
    requirementInfo: { ...requirementForm.value },
    accessWay: '',
    dtsSubscriptionUrl: ''
  };
  startAiDialog();
  try {
    const res = await getAiAccessGuideConfig(params);
    stopAiDialog();
    if (res.code === 0) {
      ElMessage.success('AI生成配置成功');
      emit('ai-config', res.data);
    } else {
      ElMessage.error(res.message || 'AI生成配置失败');
    }
  } catch (e) {
    stopAiDialog();
    console.error(e);
    ElMessage.error('AI生成配置请求异常');
  }
};

// 暴露方法供父组件使用
defineExpose({
  setFormData,
  validateForm
});
</script>

<style scoped lang="scss">
.requirement-info-form {
  margin-bottom: 20px;
  
  .requirement-card {
    border-radius: 8px;
    
    :deep(.el-card__header) {
      padding: 15px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      background-color: var(--el-fill-color-light);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      
      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .requirement-form {
      padding: 5px 0;
      
      :deep(.el-form-item__label) {
        padding-bottom: 4px;
        font-weight: 500;
        color: var(--el-text-color-regular);
        line-height: 1.4;
      }
      
      :deep(.el-form-item) {
        margin-bottom: 24px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &.is-error {
          .el-input__wrapper,
          .el-textarea__inner {
            box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
          }
        }
      }
      
      :deep(.el-input__wrapper),
      :deep(.el-textarea__inner) {
        box-shadow: 0 0 0 1px var(--el-border-color) inset;
        
        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
        
        &:focus-within {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
    }
  }
}

.link-fields {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  
  .link-item {
    flex: 1;
    margin-bottom: 0;
  }
}

.ai-textarea-wrapper {
  width: 100%;
  position: relative;
  .ai-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 2;
    padding: 5px 10px;
    min-width: 28px;
    min-height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :deep(.el-textarea__inner) {
    padding-right: 40px !important;
  }
}

.ai-wait-dialog {
  .ai-dialog-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px 0 12px 0;
  }
  .ai-dialog-icon {
    margin-bottom: 12px;
    color: var(--el-color-primary);
    animation: ai-spin 1s linear infinite;
  }
  .ai-dialog-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--el-text-color-primary);
  }
  .ai-dialog-tip {
    font-size: 13px;
    color: var(--el-text-color-secondary);
  }
}
@keyframes ai-spin {
  100% { transform: rotate(360deg); }
}
</style> 