<template>
  <div class="create-task">
    <p class="section-desc"><PERSON>平台创建定时一致性校验任务</p>
    <div class="task-instruction">
      <p class="instruction-text">请前往Crane平台创建定时一致性校验任务，完成后点击下方按钮确认。</p>
      <div class="task-info-box">
        <div class="crane-links">
          <div class="link-item">
            <span class="link-label">Crane 线上：</span>
            <a href="https://crane.sankuai.com/" target="_blank" class="crane-link">https://crane.sankuai.com/</a>
          </div>
          <div class="link-item">
            <span class="link-label">Crane 线下：</span>
            <a href="http://crane.mws-test.sankuai.com/" target="_blank" class="crane-link">http://crane.mws-test.sankuai.com/</a>
          </div>
        </div>
        
        <div class="naming-guide">
          <h4 class="guide-title">Crane任务名称格式说明：</h4>
          <ul class="guide-list">
            <li><span class="step-num">1</span> <strong>external.provider</strong> &nbsp;是前缀；</li>
            <li><span class="step-num">2</span> <strong>increment</strong> &nbsp;是增量，<strong>total</strong> &nbsp;是全量；</li>
            <li><span class="step-num">3</span> <strong>check</strong> &nbsp;是后缀。</li>
            <li><span class="step-num">4</span> 同步场景（可选）。</li>
          </ul>
          <div class="examples">
            <span class="example-label">示例：</span>
            <div class="example-items">
              <code class="example-code">external.provider.increment.check</code>
              <code class="example-code">external.provider.increment.check.80001</code>
            </div>
          </div>
        </div>
        
        <!-- 定时创建指南折叠区 -->
        <el-collapse v-model="activeCollapse" class="guide-collapse">
          <el-collapse-item name="guide">
            <template #title>
              <div class="collapse-header">
                <h4 class="guide-title">Crane定时一致性校验任务创建指南</h4>
                <span class="collapse-action">{{ activeCollapse.includes('guide') ? '收起' : '展开' }}</span>
              </div>
            </template>
            <div class="subscription-guide">
              <h4>创建Crane定时一致性校验任务步骤：</h4>
              <ol class="guide-steps">
                <li>登录Crane平台；</li>
                <li>根据业务场景选择全量一致性校验或者增量一致性校验；</li>
                <li>默认编辑 external.provider.increment.check 或者 external.provider.total.check 定时任务，修改方法参数，将最新的配置追加到原有的参数中即可；</li>
                <li>如果有额外的需求，则需要新建任务：
                  <ul>
                    <li>任务名称：按照上述命名规范填写；</li>
                    <li>任务配置：参考external.provider.increment.check 和 external.provider.total.check 定时任务，根据具体的场景设置定时时间。</li>
                  </ul>
                </li>
              </ol>

              <h4>任务创建示例图：</h4>
              <p><b>通用外部增量、全量一致性校验任务</b></p>
              <img src="../../../../../../assets/images/crane-1.png" width="1000" alt="crane-task-access-guide-form-1">

              <p><b>一致性校验任务类名、方法名、方法参数配置示例</b></p>
              <img src="../../../../../../assets/images/crane-2.png" width="1000" alt="crane-task-access-guide-form-2">
              
              <div class="tips">
                <h4>注意事项：</h4>
                <ul>
                  <li>请确保已正确配置 Mafka 消费组；</li>
                  <li>创建成功后复制任务链接，填入下方输入框。</li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    
    <!-- Crane任务链接输入框 -->
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top" class="subscription-form">
      <el-form-item label="一致性校验Crane定时任务链接" prop="craneUrl">
        <el-input 
          v-model="form.craneUrl" 
          :placeholder="craneUrlPlaceholder" 
          clearable
          style="width: 600px"
          :disabled="hasCompletedFinalStep"
          @input="onCraneUrlChange"
          @focus="onCraneUrlFocus"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    
    <div class="task-action">
      <el-button 
        type="primary" 
        @click="completeTask" 
        :disabled="isButtonDisabled"
      >
        {{ hasCompletedFinalStep ? '已完成创建' : '确认创建完成' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, defineExpose, ref, computed, watch, onMounted, nextTick } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

const props = defineProps({
  hasCompletedFinalStep: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits<{
  (e: 'valid-change', valid: boolean): void;
  (e: 'complete-task'): void;
  (e: 'crane-url-update', url: string): void;
}>();

// 折叠区状态
const activeCollapse = ref<string[]>([]);

// 表单引用和状态
const formRef = ref<FormInstance>();
const form = ref({
  craneUrl: ''
});

const craneUrlPlaceholder = '【定时任务-1 链接】：\n【定时任务-n 链接】：';

// 表单验证规则
const rules: FormRules = {
  craneUrl: [
    { required: true, message: '请输入Crane定时任务链接', trigger: 'blur' }
  ]
};

// 表单是否有效
const isFormValid = ref(false);
// 是否已经初始化过（用于控制初始不触发验证）
const isInitialized = ref(false);

// 监听表单值变化
watch(() => form.value.craneUrl, (newValue) => {
  // 只有在组件已初始化后才触发验证
  if (isInitialized.value) {
    validateForm();
  }
});

// 校验表单
const validateForm = async () => {
  if (!formRef.value) return false;
  
  // 如果链接为空，直接返回无效
  if (!form.value.craneUrl.trim()) {
    isFormValid.value = false;
    emit('valid-change', false);
    return false;
  }
  
  try {
    await formRef.value.validate();
    isFormValid.value = true;
    emit('valid-change', props.hasCompletedFinalStep);
    return true;
  } catch (error) {
    isFormValid.value = false;
    emit('valid-change', false);
    return false;
  }
};

// 完成任务
const completeTask = async () => {
  if (!isInitialized.value) {
    // 设置初始化标记，强制进行首次验证
    isInitialized.value = true;
  }
  
  const valid = await validateForm();
  if (valid) {
    // 发送任务链接更新事件
    emit('crane-url-update', form.value.craneUrl);
    emit('complete-task');
    emit('valid-change', true);
  }
};

// 验证任务
const validateTaskForm = async () => {
  const valid = await validateForm();
  emit('valid-change', valid && props.hasCompletedFinalStep);
  return valid && props.hasCompletedFinalStep;
};

// 主动触发表单验证并更新valid状态
const activeValidate = async () => {
  const valid = await validateForm();
  const result = valid && props.hasCompletedFinalStep;
  emit('valid-change', result);
  return result;
};

// 处理任务链接变化
const onCraneUrlChange = () => {
  // 如果内容为空，不进行校验
  if (!form.value.craneUrl.trim()) {
    isFormValid.value = false;
    emit('valid-change', false);
    return;
  }
  
  // 第一次输入时设置初始化标记
  isInitialized.value = true;
  validateForm();
};

// 处理任务链接获取焦点
const onCraneUrlFocus = () => {
  // 当输入框为空时，自动填入placeholder的值
  if (!form.value.craneUrl.trim()) {
    form.value.craneUrl = craneUrlPlaceholder;
    // 设置初始化标记
    isInitialized.value = true;
  }
};

// 组件挂载后
onMounted(() => {
  // 初始时不触发校验
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
});

// 按钮是否禁用
const isButtonDisabled = computed(() => {
  if (props.hasCompletedFinalStep) {
    return true; // 已完成则禁用
  }
  
  // 链接为空时禁用按钮
  if (!form.value.craneUrl.trim()) {
    return true;
  }
  
  if (!isInitialized.value) {
    // 初始状态下，只有在有有效输入时才启用
    return false;
  } else {
    // 初始化后，根据表单验证结果禁用
    return !isFormValid.value;
  }
});

// 设置任务URL的方法
const setTaskUrl = (url: string) => {
  if (url && typeof url === 'string') {
    // 更新craneUrl
    if (form && form.value) {
      form.value.craneUrl = url;
      // 确保表单已初始化
      isInitialized.value = true;
      
      // 如果URL不为空，通知父组件
      emit('crane-url-update', url);
      
      // 执行表单验证
      nextTick(() => {
        validateForm();
      });
    }
  }
};

// 对外暴露方法
defineExpose({
  validateTaskForm,
  activeValidate,
  form,
  setTaskUrl
});
</script>

<style scoped lang="scss">
.create-task {
  .section-desc {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
  }
  
  .task-instruction {
    margin-bottom: 24px;
    
    .instruction-text {
      font-size: 14px;
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
    }
    
    .task-info-box {
      padding: 20px;
      background-color: var(--el-fill-color-lighter);
      border-radius: 8px;
      border-left: 4px solid var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      
      .crane-links {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px dashed var(--el-border-color-lighter);
        
        .link-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          .link-label {
            font-weight: 500;
            color: var(--el-text-color-primary);
            min-width: 80px;
          }
          
          .crane-link {
            color: var(--el-color-primary);
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
              color: var(--el-color-primary-dark-2);
            }
          }
        }
      }
      
      .naming-guide {
        margin-bottom: 16px;
        
        .guide-title {
          font-size: 14px;
          font-weight: 600;
          margin-top: 0;
          margin-bottom: 12px;
          color: var(--el-text-color-primary);
        }
        
        .guide-list {
          padding-left: 0;
          margin: 0 0 16px 0;
          list-style-type: none;
          
          li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--el-text-color-regular);
            
            .step-num {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 20px;
              height: 20px;
              margin-right: 8px;
              background-color: var(--el-color-primary);
              color: white;
              font-size: 12px;
              font-weight: bold;
              border-radius: 50%;
            }
            
            strong {
              font-weight: 600;
              color: var(--el-color-danger);
            }
          }
        }
        
        .examples {
          background-color: transparent;
          border-radius: 6px;
          padding: 8px 0;
          
          .example-label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--el-text-color-primary);
          }
          
          .example-items {
            display: flex;
            flex-direction: column;
            gap: 8px;
            
            .example-code {
              display: inline-block;
              font-family: monospace;
              color: var(--el-color-primary);
              background-color: rgba(var(--el-color-primary-rgb), 0.1);
              padding: 4px 8px;
              border-radius: 4px;
              word-break: break-all;
              font-size: 13px;
              max-width: fit-content;
            }
          }
        }
      }
      
      .guide-collapse {
        border-top: 1px dashed var(--el-border-color-lighter);
        padding-top: 16px;
        
        .collapse-header {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .guide-title {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
            color: var(--el-text-color-primary);
          }
          
          .collapse-action {
            font-size: 14px;
            color: var(--el-color-primary);
            font-weight: 400;
          }
        }
        
        :deep(.el-collapse-item__header) {
          background-color: var(--el-fill-color-light);
          border: 1px solid var(--el-border-color);
          border-radius: 4px;
          padding: 12px 16px;
          margin-bottom: 0;
          
          .el-collapse-item__arrow {
            display: none;
          }
          
          &:hover {
            background-color: var(--el-fill-color);
          }
        }
        
        :deep(.el-collapse-item__content) {
          padding: 16px;
          background-color: var(--el-fill-color-lighter);
          border: 1px solid var(--el-border-color);
          border-top: none;
          border-bottom-left-radius: 4px;
          border-bottom-right-radius: 4px;
        }
        
        .subscription-guide {
          h4 {
            font-size: 14px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 12px;
            color: var(--el-text-color-primary);
            
            &:not(:first-child) {
              margin-top: 16px;
            }
          }
          
          .guide-steps {
            padding-left: 20px;
            margin: 0 0 16px 0;
            
            li {
              margin-bottom: 10px;
              font-size: 14px;
              color: var(--el-text-color-regular);
              position: relative;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              ul {
                padding-left: 20px;
                margin: 8px 0;
                
                li {
                  margin-bottom: 4px;
                  
                  &:before {
                    content: "•";
                    position: absolute;
                    left: -12px;
                    color: var(--el-color-primary);
                  }
                }
              }
            }
          }
          
          .tips {
            background-color: rgba(var(--el-color-warning-rgb), 0.1);
            border-radius: 4px;
            padding: 12px 16px;
            border-left: 3px solid var(--el-color-warning);
            
            h4 {
              color: var(--el-color-warning-dark-2);
              margin-top: 0;
              margin-bottom: 8px;
            }
            
            ul {
              padding-left: 20px;
              margin: 0;
              
              li {
                color: var(--el-color-warning-dark-2);
                font-size: 13px;
                margin-bottom: 6px;
                
                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .subscription-form {
    margin-bottom: 16px;
    
    :deep(.el-form-item__label) {
      font-weight: 500;
    }
  }
  
  .task-action {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}
</style> 