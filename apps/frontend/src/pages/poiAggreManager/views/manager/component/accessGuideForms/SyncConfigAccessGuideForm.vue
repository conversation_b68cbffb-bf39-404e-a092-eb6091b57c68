<template>
  <div class="sync-config-info">
    <!-- 添加提示信息 -->
    <el-alert
      type="warning"
      show-icon
      :closable="false"
      class="guide-alert"
    >
      <span>
        此阶段配置的是在<b>「聚合查询 - 同步服务」</b>中将数据同步到聚合查询DB的相关配置信息。<br>
        注意：同步租户和同步场景需要参考「同步租户 & 同步场景填写须知」文档后再填写。<br>
      </span>
    </el-alert>
    
    <!-- 同步配置表单 -->
    <el-form ref="configFormRef" :model="formData" :rules="rules" label-position="right" label-width="160px" :validate-on-rule-change="false">
      <!-- 添加租户 & 场景标题 -->
      <h3 class="section-title">租户 & 场景</h3>
      <div class="wiki-link-container">
        <el-link type="error" :href="syncTenantAndSceneWikiLink" target="_blank" :underline="false">
          <el-icon class="el-icon--left"><Document /></el-icon>
          同步租户 & 同步场景填写须知
        </el-link>
      </div>
      <el-form-item label="同步租户" prop="syncTenant">
        <el-input
          v-model.number="formData.syncTenant"
          placeholder="请输入同步租户"
          style="width: 300px"
          type="number"
          :min="0"
        />
      </el-form-item>
      
      <el-form-item label="同步场景" prop="syncScene">
        <el-input
          v-model.number="formData.syncScene"
          placeholder="请输入同步场景"
          style="width: 300px"
          type="number"
          :min="0"
        />
      </el-form-item>

      <!-- 添加基本信息标题 -->
      <h3 class="section-title">基本信息</h3>
      
      <el-form-item label="同步名称" prop="syncName">
        <el-input
          v-model="formData.syncName"
          placeholder="请输入同步名称"
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item label="同步描述" prop="syncDescription">
        <el-input
          v-model="formData.syncDescription"
          type="textarea"
          :rows="3"
          placeholder="请输入同步描述"
          style="width: 500px"
        />
      </el-form-item>

      <!-- 添加配置信息标题 -->
      <h3 class="section-title">配置信息</h3>
      
      <el-form-item label="同步字段" prop="syncFieldCodes">
        <el-select
          v-model="formData.syncFieldCodes"
          multiple
          filterable
          :allow-create="!syncFieldsLoading && syncFieldOptions.length > 0 && !props.disableSyncFieldsEdit"
          default-first-option
          placeholder="请选择同步字段"
          style="width: 500px"
          class="field-select"
          :loading="syncFieldsLoading"
          :no-data-text="syncFieldsLoading ? '加载中...' : '暂无数据'"
          :disabled="props.disableSyncFieldsEdit"
        >
          <el-option
            v-for="field in syncFieldOptions"
            :key="field.value"
            :label="field.label"
            :value="field.value"
          />
        </el-select>
      </el-form-item>

      <div class="form-row">
        <el-form-item v-if="props.showSyncType" label="同步类型" prop="type">
          <el-select 
          v-model="formData.type" 
          placeholder="请选择同步类型" 
          style="width: 130px"
          disabled 
          @change="handleTypeChange">
            <el-option
              v-for="option in SYNC_TYPE_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="props.showPoiOuter" label="是否POI维度" prop="isPoiOuter">
          <el-select 
            v-model="formData.isPoiOuter" 
            placeholder="请选择是否POI维度"
            style="width: 70px"
            :disabled="formData.type === SyncTypeEnum.DTS"
          >
            <el-option :key="true" label="是" :value="true" />
            <el-option :key="false" label="否" :value="false" />
          </el-select>
        </el-form-item>
      </div>
      
      <!-- Mafka相关配置，仅在type=1时显示 -->
      <template v-if="formData.type === SyncTypeEnum.MAFKA">
        <h3 class="section-title">Mafka 配置</h3>
        <div v-show="formData.type === SyncTypeEnum.MAFKA" class="mafka-fields-container">
          <el-form ref="mafkaFormRef" :model="mafkaFormData" label-position="right" label-width="160px">
            <div class="mafka-field-group">
              <el-form-item 
                label="outer_id 字段名" 
                class="mafka-field"
                prop="outerIdsKey"
                :rules="[
                  { required: true, message: '请输入 outer_id 字段名', trigger: 'blur' }
                ]"
              >
                <el-input 
                  v-model="mafkaFormData.outerIdsKey" 
                  placeholder="请输入 outer_id 字段名"
                  style="width: 300px"
                  @input="handleOuterIdsKeyChange"
                />
              </el-form-item>
            </div>
            
            <div class="mafka-field-group">
              <el-form-item label="主题名称" class="mafka-field">
                <el-input 
                  v-model="mafkaConfigObj.topic" 
                  placeholder="请输入主题名称"
                  :disabled="true"
                  style="width: 340px"
                />
              </el-form-item>
              
              <el-form-item label="命名空间" class="mafka-field">
                <el-input 
                  v-model="mafkaConfigObj.namespace" 
                  placeholder="请输入命名空间"
                  :disabled="true"
                  style="width: 340px"
                />
              </el-form-item>
            </div>
            
            <div class="mafka-field-group">
              <el-form-item label="消费组名称" class="mafka-field">
                <el-input 
                  v-model="mafkaConfigObj.group" 
                  placeholder="请输入消费组名称"
                  :disabled="true"
                  style="width: 340px"
                />
              </el-form-item>
              
              <el-form-item label="消费组AppKey" class="mafka-field">
                <el-input 
                  v-model="mafkaConfigObj.appKey" 
                  placeholder="请输入AppKey"
                  :disabled="true"
                  style="width: 340px"
                />
              </el-form-item>
            </div>
            
            <div class="mafka-field-group">
              <el-form-item label="过滤脚本" class="mafka-field full-width">
                <el-input 
                  v-model="mafkaConfigObj.filterScript" 
                  type="textarea"
                  :rows="3"
                  placeholder="无"
                  :disabled="true"
                  style="width: 700px"
                />
              </el-form-item>
            </div>
          </el-form>
            
          <!-- 隐藏的原始textarea，用于表单验证 -->
          <el-input
            v-model="formData.mafkaConsumeConfig"
            type="hidden"
          />
        </div>
        
        <!-- 添加查询数据配置标题 -->
        <h3 class="section-title">查询数据配置</h3>
        
        <el-form-item label="查询数据配置" prop="queryDataConfig" class="json-form-item">
          <div class="textarea-container">
            <el-input
              v-model="formData.queryDataConfig"
              type="textarea"
              :rows="3"
              placeholder="请输入查询数据配置（JSON格式）"
              style="width: 700px"
              @blur="handleJsonInputBlur('queryDataConfig')"
              :disabled="true"
            />
            <div class="json-visual-edit">
              <el-button type="primary" text @click="openJsonEditor('queryDataConfig')">
                <el-icon class="el-icon--left"><EditPen /></el-icon>
                可视化编辑
              </el-button>
            </div>
          </div>
        </el-form-item>

        <h3 class="section-title">一致性校验配置</h3>

        <el-form-item label="一致性校验方式" class="consistency-check-mode">
          <el-radio-group v-model="consistencyCheckMode" @change="handleConsistencyModeChange">
            <el-radio label="incremental">最近变更Id</el-radio>
            <el-radio label="full">全量PoiId</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item 
          v-show="consistencyCheckMode === 'full'"
          label="全量PoiId校验配置" 
          prop="totalPoiConsistencyCheck"
          class="json-form-item"
        >
          <div class="full-poi-check-container">
            <div class="full-poi-check-item">
              <span class="check-label">启用校验：</span>
              <el-switch
                v-model="totalPoiCheckConfig.enable"
                @change="updateTotalPoiCheckJson"
              />
            </div>
            <div class="full-poi-check-item full-width">
              <span class="check-label">过滤脚本：</span>
              <el-input
                v-model="totalPoiCheckConfig.filterScript"
                type="textarea"
                :rows="3"
                placeholder="请输入过滤脚本（可选）"
                style="width: 520px"
                @blur="updateTotalPoiCheckJson"
              />
            </div>
          </div>
          <!-- 隐藏的JSON输入框，用于表单验证 -->
          <el-input
            v-model="formData.totalPoiConsistencyCheck"
            type="hidden"
          />
        </el-form-item>
  
        <el-form-item 
          v-show="consistencyCheckMode === 'incremental'"
          label="查询最近变更ID配置" 
          prop="queryChangeIdsConfig"
          class="json-form-item"
        >
          <div class="textarea-container">
            <el-input
              v-model="formData.queryChangeIdsConfig"
              type="textarea"
              :rows="3"
              placeholder="请输入查询最近变更ID配置（JSON格式）"
              style="width: 700px"
              @blur="handleJsonInputBlur('queryChangeIdsConfig')"
              :disabled="true"
            />
            <div class="json-visual-edit">
              <el-button type="primary" text @click="openJsonEditor('queryChangeIdsConfig')">
                <el-icon class="el-icon--left"><EditPen /></el-icon>
                可视化编辑
              </el-button>
            </div>
          </div>
        </el-form-item>
      </template>

      <!-- DTS配置，仅在type=2时显示 -->
      <template v-if="formData.type === SyncTypeEnum.DTS">
        <el-form-item label="DTS配置" prop="dtsSyncConfig" class="json-form-item">
          <div class="textarea-container">
            <el-input
              v-model="formData.dtsSyncConfig"
              type="textarea"
              :rows="6"
              placeholder="请输入DTS同步配置（JSON格式）"
              style="width: 700px"
              @blur="handleJsonInputBlur('dtsSyncConfig')"
            />
            <div class="json-visual-edit">
              <el-button type="primary" text @click="openJsonEditor('dtsSyncConfig')">
                <el-icon class="el-icon--left"><EditPen /></el-icon>
                可视化编辑
              </el-button>
            </div>
          </div>
        </el-form-item>
      </template>


      <h3 class="section-title">Lion 配置</h3>
      <el-form-item label="配置描述" prop="lionConfigDescription">
        <div class="textarea-container">
          <el-input
            v-model="formData.lionConfigDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入Lion配置描述"
            style="width: 700px"
          />
        </div>
      </el-form-item>
    </el-form>
  </div>

  <!-- 使用JsonEditor组件 -->
  <json-editor
    ref="jsonEditorRef"
    v-model="currentJsonValue"
    @save="handleJsonEditorSave"
    @cancel="handleJsonEditorCancel"
    :readonly-root-keys="props.accessWay === 'dts'" 
  />

  <!-- 添加服务编排可视化抽屉 -->
  <el-drawer
    v-model="showOrchestrationDrawer"
    title="服务编排可视化"
    size="90%"
    :destroy-on-close="true"
    :close-on-click-modal="false"
  >
    <OrchestrationVisualizer 
      v-if="visualizerDslKey" 
      :dslKey="visualizerDslKey" 
      @dsl-saved="handleDslSaved"
    />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { SYNC_TYPE_OPTIONS, SyncTypeEnum } from '../../../../types'
import type { SyncConfigItem, SyncMetadataItem } from '../../../../types'
import { ElMessage } from 'element-plus'
import { getSyncFieldMetadataList } from '../../../../request'
import { Document, EditPen } from '@element-plus/icons-vue'
import JsonEditor from '../../../../components/common/JsonEditor.vue'
import { goToOrchestrationVisualizer, getVisualDslKey } from '../../../../utils/orchestrationUtils'
import OrchestrationVisualizer from '../../../../views/tools/orchestration/OrchestrationVisualizer.vue'

const props = defineProps({
  configForm: {
    type: Object,
    required: true
  },
  accessWay: {
    type: String,
    required: true
  },
  // 新增属性：控制是否可以编辑同步字段
  disableSyncFieldsEdit: {
    type: Boolean,
    default: false
  },
  // 新增属性：控制是否显示同步类型
  showSyncType: {
    type: Boolean,
    default: true
  },
  // 新增属性：控制是否显示是否外部POI
  showPoiOuter: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits<{
  (e: 'valid-change', valid: boolean): void;
  (e: 'sync-config-update', data: SyncConfigItem): void;
}>();

const syncTenantAndSceneWikiLink = 'https://km.sankuai.com/collabpage/1428789120';

// 表单引用
const configFormRef = ref<FormInstance>();
const isInitializing = ref(true);
const isSubmitting = ref(false);
const syncFieldsLoading = ref(false);
const prevType = ref<number | undefined>(undefined);

// 同步字段选项
const syncFieldOptions = ref<Array<{ label: string, value: string }>>([]);

// 定义默认值常量
const DEFAULT_CONFIGS = {
  totalPoiConsistencyCheck: `{
  "enable": true,
  "filterScript": "data.exampleField1 == 1"
}`,
  mafkaConsumeConfig: `{
  "namespace": "example_namespace",
  "appkey": "example_appkey",
  "topic": "example_topic",
  "group": "example_group",
  "outerIdsKey": "example_poi_id",
  "scene": -1,
  "tenant": -1,
  "filterScript": "data.exampleField1 == 1 && data.exampleField2 == 6（可移除）"
}`,
  queryDataConfig: '',
  queryChangeIdsConfig: '',
  dtsSyncConfig: `{
  "tableName": "example_db.example_table",
  "poiIdKey": "poi_id",
  "validKey": "",
  "validTrueValue": 1,
  "validFalseValue": -1,
  "fieldDefinition": {
    "example_field1": {
      "type": "script",
      "describe": "example",
      "outerFields": [
        "field1"
      ],
      "script": "example_other_field=='100' ? 1 : 0"
    }
  }
}`,
  exampleDsl: `{
  "dsl": {
    "name": "exampleDsl",
    "description": "DSL样例（3个节点，简单链式依赖）",
    "timeout": 20,
    "tasks": [
      {
        "alias": "node1",
        "taskType": "Calculate",
        "description": "node1",
        "inputs": {
          "sourcePoiIds": "$params.poiIds"
        }
      },
      {
        "alias": "node2",
        "taskType": "Calculate",
        "description": "node2",
        "inputs": "count($node1.sourcePoiIds)"
      },
      {
        "alias": "node3",
        "taskType": "Calculate",
        "description": "node3",
        "inputs": {
          "size": "$node2"
        }
      }
    ],
    "outputs": {
      "type": "map",
      "transform": "seq.map('total', $node3.size, 'data', $node1)"
    }
  }
}`
};

// 表单数据
const formData = reactive<Partial<SyncConfigItem>>({
  syncScene: undefined,
  syncTenant: undefined,
  syncName: '',
  syncDescription: '',
  lionConfigDescription: '',
  syncFieldCodes: [],
  type: props.accessWay === 'dts' ? SyncTypeEnum.DTS : SyncTypeEnum.MAFKA, // 根据接入方式设置类型
  isPoiOuter: false,
  totalPoiConsistencyCheck: DEFAULT_CONFIGS.totalPoiConsistencyCheck,
  mafkaConsumeConfig: DEFAULT_CONFIGS.mafkaConsumeConfig,
  queryDataConfig: DEFAULT_CONFIGS.queryDataConfig,
  queryChangeIdsConfig: DEFAULT_CONFIGS.queryChangeIdsConfig,
  dtsSyncConfig: DEFAULT_CONFIGS.dtsSyncConfig,
  valid: 1 // 默认值为1
});

// 添加一个一致性校验方式变量
const consistencyCheckMode = ref<'full' | 'incremental'>('incremental');

// 添加Mafka配置对象
const mafkaConfigObj = reactive({
  namespace: '',
  group: '',
  topic: '',
  appKey: '',
  scene: -1,
  tenant: -1,
  outerIdsKey: '',
  filterScript: ''
});

// 添加外部ID字段错误状态
const outerIdsKeyError = ref(false);

// 添加Mafka表单数据和表单引用
const mafkaFormData = reactive({
  outerIdsKey: ''
});

// 添加全量PoiId校验配置对象
const totalPoiCheckConfig = reactive({
  enable: true,
  filterScript: ''
});

// 添加状态变量
const showOrchestrationDrawer = ref(false);
const visualizerDslKey = ref('');

// 添加处理外部ID字段变化的方法
const handleOuterIdsKeyChange = (value: string) => {
  // 同步更新到mafkaConfigObj
  mafkaConfigObj.outerIdsKey = value;
  
  // 只在外部ID字段有值时更新JSON
  if (!isInitializing.value) {
    // 构建JSON对象
    const mafkaJsonObj = {
      namespace: mafkaConfigObj.namespace,
      group: mafkaConfigObj.group,
      topic: mafkaConfigObj.topic,
      appkey: mafkaConfigObj.appKey,
      scene: mafkaConfigObj.scene,
      tenant: mafkaConfigObj.tenant,
      outerIdsKey: value || '', // 使用表单数据
      filterScript: mafkaConfigObj.filterScript
    };
    
    // 更新JSON字符串，但不触发全表单验证
    formData.mafkaConsumeConfig = JSON.stringify(mafkaJsonObj, null, 2);
    formData.mafkaConsumeConfig = formData.mafkaConsumeConfig.replace(/\\n/g, '\n').replace(/\\t/g, '\t');
    
    // 只有当用户实际修改了值时才触发验证，但只验证当前字段
    nextTick(() => {
      if (mafkaFormRef.value) {
        // 手动验证outerIdsKey字段
        mafkaFormRef.value.validateField('outerIdsKey');
      }
      
      // 更新到父组件，但不触发其他字段的验证
      emit('sync-config-update', getFormData());
    });
  }
};

// 验证JSON格式并检查是否使用了默认值
const validateJson = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  
  // 检查是否等于默认值
  const fieldName = rule.field;
  if (fieldName !== 'totalPoiConsistencyCheck' && DEFAULT_CONFIGS[fieldName as keyof typeof DEFAULT_CONFIGS] === value) {
    callback(new Error('请修改默认示例内容后再提交'));
    return;
  }
  
  // 特殊处理totalPoiConsistencyCheck字段
  if (fieldName === 'totalPoiConsistencyCheck') {
    try {
      const config = JSON.parse(value);
      // 只验证结构是否正确，不检查默认值
      if (config.enable === undefined) {
        callback(new Error('校验配置缺少enable字段'));
        return;
      }
    } catch (error) {
      callback(new Error('请输入有效的JSON格式'));
      return;
    }
    callback();
    return;
  }
  
  try {
    // 处理JSON字符串中的转义字符
    // 提取双引号之间的字符串并处理其中的转义字符
    const processedValue = value.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
      // 替换字符串中的换行符、制表符和其他需要转义的字符
      return match
        .replace(/\n/g, '\\n')
        .replace(/\t/g, '\\t');
    });
    
    JSON.parse(processedValue);
    callback();
  } catch (error) {
    callback(new Error('请输入有效的JSON格式'));
  }
};

// 规则定义
const rules: FormRules = {
  syncTenant: [
    { required: true, message: '请输入同步租户', trigger: 'blur' },
    { type: 'number', message: '同步租户必须为数字', trigger: 'blur' }
  ],
  syncScene: [
    { required: true, message: '请输入同步场景', trigger: 'blur' },
    { type: 'number', message: '同步场景必须为数字', trigger: 'blur' }
  ],
  syncName: [
    { required: true, message: '请输入同步名称', trigger: 'blur' },
    { max: 50, message: '同步名称不能超过50个字符', trigger: 'change' }
  ],
  syncDescription: [
    { required: true, message: '请输入同步描述', trigger: 'blur' },
    { max: 200, message: '同步描述不能超过200个字符', trigger: 'change' }
  ],
  syncFieldCodes: [
    { required: true, message: '请选择同步字段', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个同步字段', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择同步类型', trigger: 'change' }
  ],
  isPoiOuter: [
    { required: true, message: '请选择是否外部POI', trigger: 'change' }
  ],
  lionConfigDescription: [
    { required: true, message: '请输入Lion配置描述', trigger: 'blur' },
    { max: 200, message: 'Lion配置描述不能超过200个字符', trigger: 'change' }
  ],
  // 各类型特有字段，根据type的值决定是否required
  mafkaConsumeConfig: [
    { required: formData.type === SyncTypeEnum.MAFKA, message: '请输入Mafka配置', trigger: 'manual' },
    { validator: validateJson, trigger: 'manual' }
  ],
  queryDataConfig: [
    { required: formData.type === SyncTypeEnum.MAFKA, message: '请输入查询数据配置', trigger: 'manual' },
    { validator: validateJson, trigger: 'manual' }
  ],
  queryChangeIdsConfig: [
    { required: formData.type === SyncTypeEnum.MAFKA, message: '请输入查询最近变更ID配置', trigger: 'manual' },
    { validator: validateJson, trigger: 'manual' }
  ],
  totalPoiConsistencyCheck: [
    { required: formData.type === SyncTypeEnum.MAFKA, message: '请输入全量PoiId一致性校验配置', trigger: 'blur' },
    { validator: validateJson, trigger: 'manual' }
  ],
  dtsSyncConfig: [
    { required: formData.type === SyncTypeEnum.DTS, message: '请输入DTS配置', trigger: 'manual' },
    { validator: validateJson, trigger: 'manual' }
  ]
};

// 处理同步类型变更
const handleTypeChange = (value: number) => {
  // 初始化阶段不触发校验
  if (isInitializing.value) return;
  
  // 临时设置初始化状态，防止类型切换时触发校验
  isInitializing.value = true;
  
  // 更新上一次类型
  prevType.value = value;

  // 根据同步类型设置isPoiOuter的值
  if (value === SyncTypeEnum.DTS) {
    formData.isPoiOuter = false; // DTS类型设置为否
  }
  
  // 清除表单验证状态
  if (configFormRef.value) {
    configFormRef.value.clearValidate();
  }
  
  // 使用setTimeout恢复验证状态，确保DOM更新完成后再允许验证
  setTimeout(() => {
    isInitializing.value = false;
    // 向父组件发送数据更新
    emit('sync-config-update', getFormData());
  }, 100);
};

// 监听syncScene和syncDescription变化，自动设置lionConfigDescription
watch(
  [() => formData.syncScene, () => formData.syncDescription],
  ([newSyncScene, newSyncDescription]) => {
    if (!newSyncScene && !newSyncDescription) return;
    
    // 自动设置lionConfigDescription为"【同步配置】+syncScene-syncDescription"
    formData.lionConfigDescription = `【同步配置】${newSyncScene || ''}-${newSyncDescription || ''}`;
    
    // 更新到父组件
    emit('sync-config-update', getFormData());
  }
);

// 监听syncTenant变化，改为直接更新mafkaConfigObj
watch(
  () => formData.syncTenant,
  (newSyncTenant) => {
    // 只在有新值且类型为MAFKA时处理
    if (newSyncTenant !== undefined && formData.type === SyncTypeEnum.MAFKA) {
      mafkaConfigObj.tenant = newSyncTenant;
      // 更新JSON
      updateMafkaConsumeConfigJson();
    }
  }
);

// 监听syncScene变化，改为直接更新mafkaConfigObj
watch(
  () => formData.syncScene,
  (newSyncScene) => {
    // 只在有新值且类型为MAFKA时处理
    if (newSyncScene !== undefined && formData.type === SyncTypeEnum.MAFKA) {
      mafkaConfigObj.scene = newSyncScene;
      // 更新JSON
      updateMafkaConsumeConfigJson();
    }
  }
);

// 监听表单数据变化，触发验证和更新事件
watch(
  () => formData,
  () => {
    // 排除初始化阶段
    if (!isInitializing.value) {
      // 触发表单验证
      validateContentAndEmit();
      // 向父组件发送数据更新
      emit('sync-config-update', getFormData());
    }
  },
  { deep: true }
);

// 监听accessWay属性变化，更新同步类型和表单状态
watch(
  () => props.accessWay,
  (newAccessWay) => {
    if (isInitializing.value) return;
    
    // 临时设置初始化状态，防止类型切换时触发校验
    isInitializing.value = true;
    
    // 清除表单验证状态
    if (configFormRef.value) {
      configFormRef.value.clearValidate();
    }
    
    // 根据接入方式更新同步类型
    formData.type = newAccessWay === 'dts' ? SyncTypeEnum.DTS : SyncTypeEnum.MAFKA;
    
    // 根据类型设置isPoiOuter的值
    if (formData.type === SyncTypeEnum.DTS) {
      formData.isPoiOuter = false;
    }
    
    // 使用setTimeout恢复验证状态，确保DOM更新完成后再允许验证
    setTimeout(() => {
      isInitializing.value = false;
      // 手动触发一次type字段的校验
      nextTick(() => {
        if (configFormRef.value) {
          configFormRef.value.validateField(['type']);
        }
      });
    }, 50);
  }
);

// 获取同步字段选项
const getSyncFields = async () => {
  syncFieldsLoading.value = true;
  try {
    // 调用request/index.ts中的方法获取字段列表
    const res = await getSyncFieldMetadataList({
      pageNo: 1,
      pageSize: 1000, // 获取足够多的字段
      valid: 1,       // 只获取有效的字段
      conditions: {}  // 无筛选条件，获取所有字段
    });
    
    if (res.code === 0 && res.data?.records) {
      // 转换响应数据为下拉选项格式
      // 只使用字段code作为标签和值
      syncFieldOptions.value = res.data.records.map((item: SyncMetadataItem) => ({
        label: item.fieldCode, // 只显示字段编码
        value: item.fieldCode // 实际保存的值使用字段编码
      }));
    } else {
      ElMessage.error(res.message || '获取同步字段失败');
    }
  } catch (error) {
    console.error('获取同步字段出错:', error);
    ElMessage.error('获取同步字段失败');
  } finally {
    syncFieldsLoading.value = false;
  }
};

// 表单内容校验
const validateContentAndEmit = () => {
  const isValid = validateFormContent();
  emit('valid-change', isValid);
  return isValid;
};

// 基于表单内容的验证方法
const validateFormContent = (): boolean => {
  // 检查必填字段
  if (
    !formData.syncTenant ||
    !formData.syncScene ||
    !formData.syncName ||
    !formData.syncDescription ||
    !formData.type ||
    formData.isPoiOuter === undefined ||
    !formData.lionConfigDescription ||
    !formData.syncFieldCodes ||
    formData.syncFieldCodes.length === 0
  ) {
    return false;
  }
  
  // 根据接入方式检查额外字段
  if (props.accessWay === 'mafka' || formData.type === SyncTypeEnum.MAFKA) {
    // 检查Mafka配置和查询数据配置是否存在且不等于默认值
    if (
      !formData.mafkaConsumeConfig || 
      formData.mafkaConsumeConfig === DEFAULT_CONFIGS.mafkaConsumeConfig ||
      !formData.queryDataConfig || 
      formData.queryDataConfig === DEFAULT_CONFIGS.queryDataConfig
    ) {
      return false;
    }
    
    // 验证Mafka表单 - 这里不要在每次验证表单内容时都验证mafka表单
    // 只检查outerIdsKey字段值是否存在，而不触发表单验证
    if (!mafkaFormData.outerIdsKey) {
      return false;
    }
    
    // 根据一致性校验方式检查对应的必填字段
    if (consistencyCheckMode.value === 'full') {
      // 全量模式下，检查全量PoiId一致性校验配置是否存在
      if (!formData.totalPoiConsistencyCheck) {
        return false;
      }
      
      // 检查JSON格式是否有效
      try {
        if (formData.totalPoiConsistencyCheck) {
          const config = JSON.parse(formData.totalPoiConsistencyCheck);
          // 验证enable字段是否存在
          if (config.enable === undefined) {
            return false;
          }
        }
      } catch (error) {
        return false;
      }
    }
    
    if (consistencyCheckMode.value === 'incremental') {
      // 增量模式下，查询最近变更ID配置必填且不等于默认值
      if (
        !formData.queryChangeIdsConfig || 
        formData.queryChangeIdsConfig === DEFAULT_CONFIGS.queryChangeIdsConfig
      ) {
        return false;
      }
      
      // 检查JSON格式
      try {
        if (formData.queryChangeIdsConfig) {
          JSON.parse(formData.queryChangeIdsConfig.replace(/\n/g, '').replace(/\t/g, ''));
        }
      } catch (error) {
        return false;
      }
    }
    
    // 检查其他JSON格式
    try {
      if (formData.mafkaConsumeConfig) {
        JSON.parse(formData.mafkaConsumeConfig.replace(/\n/g, '').replace(/\t/g, ''));
      }
      if (formData.queryDataConfig) {
        JSON.parse(formData.queryDataConfig.replace(/\n/g, '').replace(/\t/g, ''));
      }
    } catch (error) {
      return false;
    }
  } else if (props.accessWay === 'dts' || formData.type === SyncTypeEnum.DTS) {
    // 检查DTS配置是否存在且不等于默认值
    if (
      !formData.dtsSyncConfig || 
      formData.dtsSyncConfig === DEFAULT_CONFIGS.dtsSyncConfig
    ) {
      return false;
    }
    
    // 检查JSON格式
    try {
      if (formData.dtsSyncConfig) {
        JSON.parse(formData.dtsSyncConfig.replace(/\n/g, '').replace(/\t/g, ''));
      }
    } catch (error) {
      return false;
    }
  }
  
  return true;
};

// 用于表单校验的方法
const validateConfigForm = async () => {
  if (!configFormRef.value) return false;
  
  try {
    await configFormRef.value.validate();
    // 表单验证通过后，进行内容校验
    return validateContentAndEmit();
  } catch (error) {
    emit('valid-change', false);
    return false;
  }
};

// 主动触发表单验证并更新valid状态
const activeValidate = async () => {
  const result = validateContentAndEmit();
  return result;
};

// 获取表单数据
const getFormData = (): SyncConfigItem => {
  // 使用逐个属性赋值代替扩展运算符
  return {
    syncScene: formData.syncScene,
    syncTenant: formData.syncTenant,
    syncName: formData.syncName,
    syncDescription: formData.syncDescription,
    lionConfigDescription: formData.lionConfigDescription,
    syncFieldCodes: formData.syncFieldCodes ? [...formData.syncFieldCodes] : [],
    type: formData.type,
    isPoiOuter: formData.isPoiOuter,
    totalPoiConsistencyCheck: formData.type === SyncTypeEnum.MAFKA && consistencyCheckMode.value === 'full' ? formData.totalPoiConsistencyCheck : '',
    mafkaConsumeConfig: formData.type === SyncTypeEnum.MAFKA ? formData.mafkaConsumeConfig : '',
    queryDataConfig: formData.type === SyncTypeEnum.MAFKA ? formData.queryDataConfig : '',
    queryChangeIdsConfig: formData.type === SyncTypeEnum.MAFKA && consistencyCheckMode.value === 'incremental' ? formData.queryChangeIdsConfig : '',
    dtsSyncConfig: formData.type === SyncTypeEnum.DTS ? formData.dtsSyncConfig : '',
    valid: formData.valid
  } as SyncConfigItem;
};

// 重置表单
const resetForm = () => {
  if (configFormRef.value) {
    configFormRef.value.resetFields();
  }
  
  Object.assign(formData, {
    syncScene: undefined,
    syncTenant: undefined,
    syncName: '',
    syncDescription: '',
    lionConfigDescription: '',
    syncFieldCodes: [],
    type: props.accessWay === 'dts' ? SyncTypeEnum.DTS : SyncTypeEnum.MAFKA,
    isPoiOuter: false,
    totalPoiConsistencyCheck: DEFAULT_CONFIGS.totalPoiConsistencyCheck,
    mafkaConsumeConfig: DEFAULT_CONFIGS.mafkaConsumeConfig,
    queryDataConfig: DEFAULT_CONFIGS.queryDataConfig,
    queryChangeIdsConfig: DEFAULT_CONFIGS.queryChangeIdsConfig,
    dtsSyncConfig: DEFAULT_CONFIGS.dtsSyncConfig,
    valid: 1
  });
  
  // 重置后通知父组件
  emit('valid-change', false);
};

// 添加输入框的事件处理函数，手动触发验证
const handleJsonInputBlur = (field: string) => {
  // 在初始化阶段不进行验证
  if (isInitializing.value) return;
  
  // 对查询数据配置和查询最近变更ID配置进行特殊处理
  if ((field === 'queryDataConfig' || field === 'queryChangeIdsConfig') && formData[field as keyof typeof formData]) {
    let fieldValue = formData[field as keyof typeof formData] as string;
    
    // 检查是否包含dsl字段
    const hasDslField = /"dsl"\s*:/i.test(fieldValue);
    
    if (!hasDslField) {
      try {
        // 尝试解析JSON字符串，确保格式正确
        const parsedJson = JSON.parse(fieldValue);
        fieldValue = fieldValue.replace(/\n/g, '\n\t');
        // 格式正确，将其包装为dsl字段
        const wrappedJson = `{\n  "dsl": ${fieldValue}\n}`;
        (formData as any)[field] = wrappedJson;
        
        ElMessage.success('已自动将输入内容设置为DSL字段的值');
      } catch (error) {
        // 如果不是有效的JSON，不自动包装，让验证器处理
        console.error('输入的JSON格式有误，无法自动包装:', error);
      }
    }
  }
  
  // 手动验证当前字段
  nextTick(() => {
    configFormRef.value?.validateField([field]);
    // 触发内容校验
    validateContentAndEmit();
  });
};

// 提交表单
const submitForm = async () => {
  const isValid = await validateConfigForm();
  if (!isValid) return false;
  
  isSubmitting.value = true;
  
  try {
    emit('sync-config-update', getFormData());
    return true;
  } catch (error) {
    console.error('配置表单提交失败:', error);
    return false;
  } finally {
    isSubmitting.value = false;
  }
};

// 修改更新验证规则方法，解决TypeScript错误
const updateRulesByConsistencyMode = (mode: 'full' | 'incremental') => {
  // 根据当前一致性校验方式更新验证规则
  if (mode === 'full') {
    // 全量模式：totalPoiConsistencyCheck必填，queryChangeIdsConfig非必填
    if (rules.totalPoiConsistencyCheck && Array.isArray(rules.totalPoiConsistencyCheck)) {
      if (rules.totalPoiConsistencyCheck[0] && typeof rules.totalPoiConsistencyCheck[0] === 'object') {
        (rules.totalPoiConsistencyCheck[0] as any).required = formData.type === SyncTypeEnum.MAFKA;
      }
    }

    if (rules.queryChangeIdsConfig && Array.isArray(rules.queryChangeIdsConfig)) {
      if (rules.queryChangeIdsConfig[0] && typeof rules.queryChangeIdsConfig[0] === 'object') {
        (rules.queryChangeIdsConfig[0] as any).required = false;
      }
    }
  } else {
    // 增量模式：queryChangeIdsConfig必填，totalPoiConsistencyCheck非必填
    if (rules.totalPoiConsistencyCheck && Array.isArray(rules.totalPoiConsistencyCheck)) {
      if (rules.totalPoiConsistencyCheck[0] && typeof rules.totalPoiConsistencyCheck[0] === 'object') {
        (rules.totalPoiConsistencyCheck[0] as any).required = false;
      }
    }

    if (rules.queryChangeIdsConfig && Array.isArray(rules.queryChangeIdsConfig)) {
      if (rules.queryChangeIdsConfig[0] && typeof rules.queryChangeIdsConfig[0] === 'object') {
        (rules.queryChangeIdsConfig[0] as any).required = formData.type === SyncTypeEnum.MAFKA;
      }
    }
  }
};

// 处理一致性校验方式变更
const handleConsistencyModeChange = (mode: 'full' | 'incremental') => {
  // 更新验证规则
  updateRulesByConsistencyMode(mode);
  
  // 根据一致性校验方式重新验证相关字段
  nextTick(() => {
    if (configFormRef.value) {
      if (mode === 'full') {
        // 全量模式下重新初始化全量PoiId配置
        if (!formData.totalPoiConsistencyCheck || formData.totalPoiConsistencyCheck === DEFAULT_CONFIGS.totalPoiConsistencyCheck) {
          // 使用totalPoiCheckConfig的值创建新的JSON配置
          updateTotalPoiCheckJson();
        }
        
        // 清除增量模式的校验结果
        configFormRef.value.clearValidate(['queryChangeIdsConfig']);
        formData.queryChangeIdsConfig = DEFAULT_CONFIGS.queryChangeIdsConfig;
        // 全量模式验证
        setTimeout(() => {
          configFormRef.value?.validateField(['totalPoiConsistencyCheck']);
        }, 0);
      } else if (mode === 'incremental') {
        // 增量模式下验证查询最近变更ID配置
        configFormRef.value.validateField(['queryChangeIdsConfig']);
        // 清除全量模式的校验结果
        configFormRef.value.clearValidate(['totalPoiConsistencyCheck']);
        formData.totalPoiConsistencyCheck = '';
      }
    }
    
    // 重新验证表单内容并发送更新
    validateContentAndEmit();
    emit('sync-config-update', getFormData());
  });
};

// 监听mafkaFormData对象的变化
watch(
  () => mafkaFormData,
  () => {
    // 只有在非初始化阶段才更新
    if (!isInitializing.value) {
      // 同步更新到mafkaConfigObj
      mafkaConfigObj.outerIdsKey = mafkaFormData.outerIdsKey;
      // 更新JSON字符串
      updateMafkaConsumeConfigJson();
    }
  },
  { deep: true }
);

// 组件挂载后初始化
onMounted(() => {
  // 根据接入方式初始化同步类型和是否外部POI
  formData.type = props.accessWay === 'dts' ? SyncTypeEnum.DTS : SyncTypeEnum.MAFKA;
  
  // 加载同步字段选项
  getSyncFields();
  
  // 如果父组件传入了syncFieldCodes，初始化时就填充
  if (props.configForm.syncFieldCodes && Array.isArray(props.configForm.syncFieldCodes)) {
    formData.syncFieldCodes = [...props.configForm.syncFieldCodes];
  }
  
  // 初始化时设置rules
  updateRulesByConsistencyMode(consistencyCheckMode.value);
  
  // 解析现有的mafkaConsumeConfig到mafkaConfigObj
  parseMafkaConsumeConfig();
  
  // 解析现有的totalPoiConsistencyCheck到totalPoiCheckConfig
  parseTotalPoiCheckConfig();
  
  // 如果是全量校验模式且没有有效的totalPoiConsistencyCheck，生成一个初始值
  if (consistencyCheckMode.value === 'full' && 
      (!formData.totalPoiConsistencyCheck || formData.totalPoiConsistencyCheck === DEFAULT_CONFIGS.totalPoiConsistencyCheck)) {
    // 延迟执行，确保在初始化完成后更新
    setTimeout(() => {
      if (!isInitializing.value) {
        updateTotalPoiCheckJson();
      }
    }, 100);
  }
  
  // 初始化完成后，延长初始化标志关闭时间，避免过早触发验证
  setTimeout(() => {
    isInitializing.value = false;
    validateContentAndEmit();
    
    // 确保不触发outer_id字段验证
    if (mafkaFormRef.value && formData.type === SyncTypeEnum.MAFKA) {
      mafkaFormRef.value.clearValidate(['outerIdsKey']);
    }
  }, 100);
});

// 设置同步字段代码
const setSyncFieldCodes = (fieldCodes: string[]) => {
  // 更新表单数据中的syncFieldCodes字段
  formData.syncFieldCodes = fieldCodes;
  
  // 触发表单验证
  nextTick(() => {
    validateContentAndEmit();
  });
};

// JSON编辑相关
const jsonEditorRef = ref<InstanceType<typeof JsonEditor> | null>(null);
const currentEditingField = ref<string>('');
const currentJsonValue = ref<string>('');

// 辅助函数：递归匹配JSON对象中的嵌套括号
const extractNestedObject = (str: string, startIdx: number): { extracted: string, endIdx: number } => {
  let depth = 1; // 从1开始，因为第一个{已经被消费
  let i = startIdx;
  
  while (i < str.length && depth > 0) {
    const char = str[i];
    if (char === '{') {
      depth++;
    } else if (char === '}') {
      depth--;
    } else if (char === '"') {
      // 跳过字符串内容，避免字符串中的花括号干扰计数
      i++;
      while (i < str.length && str[i] !== '"') {
        // 处理转义字符
        if (str[i] === '\\') {
          i += 2; // 跳过转义字符和后面的字符
        } else {
          i++;
        }
      }
    }
    
    i++;
  }
  
  if (depth === 0) {
    return {
      extracted: str.substring(startIdx - 1, i), // 包含开始和结束的花括号
      endIdx: i
    };
  }
  
  // 括号不匹配，返回空字符串
  return { extracted: '', endIdx: startIdx };
};

// 打开JSON编辑器
const openJsonEditor = (field: string) => {
  currentEditingField.value = field;
  
  // 特殊处理查询数据配置和查询最近变更ID配置
  if (field === 'queryDataConfig' || field === 'queryChangeIdsConfig') {
    try {
      let fieldValue = formData[field as keyof typeof formData];
      if (fieldValue === '') {
        fieldValue = DEFAULT_CONFIGS.exampleDsl;
      }
      if (fieldValue && typeof fieldValue === 'string') {
        try {
          // 使用正则表达式提取dsl字段的值，更健壮的方式
          const dslRegex = /"dsl"\s*:\s*("(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\})/;
          const match = fieldValue.match(dslRegex);
          
          if (match && match[1]) {
            let dslValue = match[1];
            
            // 如果dsl值是用引号包裹的字符串，需要去掉引号并进行转义处理
            if ((dslValue.startsWith('"') && dslValue.endsWith('"')) || 
                (dslValue.startsWith("'") && dslValue.endsWith("'"))) {
              // 去掉两端的引号
              dslValue = dslValue.substring(1, dslValue.length - 1);
              // 处理转义字符
              dslValue = dslValue.replace(/\\"/g, '"').replace(/\\n/g, '\n').replace(/\\t/g, '\t').replace(/\\\\/g, '\\');
            }
            
            // 使用getVisualDslKey代替goToOrchestrationVisualizer
            dslValue = dslValue.replace(/( {2}){1,}/g, (match) => ' '.repeat(match.length - 2));
            visualizerDslKey.value = getVisualDslKey(dslValue) || '';
            if (visualizerDslKey.value) {
              // 打开抽屉展示组件
              showOrchestrationDrawer.value = true;
            } else {
              ElMessage.error('生成DSL可视化Key失败，请稍后重试');
              // 回退到普通JSON编辑器
              openRegularJsonEditor(field);
            }
          } else {
            // 未找到dsl字段或提取失败，尝试JSON解析
            try {
              const jsonObj = JSON.parse(fieldValue);
              if (jsonObj && jsonObj.dsl) {
                // 使用getVisualDslKey代替goToOrchestrationVisualizer
                visualizerDslKey.value = getVisualDslKey(jsonObj.dsl) || '';
                if (visualizerDslKey.value) {
                  // 打开抽屉展示组件
                  showOrchestrationDrawer.value = true;
                } else {
                  ElMessage.error('生成DSL可视化Key失败，请稍后重试');
                  // 回退到普通JSON编辑器
                  openRegularJsonEditor(field);
                }
              } else {
                ElMessage.warning('配置中无DSL属性，无法使用可视化编辑');
                // 如果没有dsl字段，回退到普通JSON编辑器
                openRegularJsonEditor(field);
              }
            } catch (jsonError) {
              ElMessage.warning('配置格式不正确，将使用普通JSON编辑器');
              openRegularJsonEditor(field);
            }
          }
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          ElMessage.warning('配置格式不正确，将使用普通JSON编辑器');
          // 解析失败时，回退到普通JSON编辑器
          openRegularJsonEditor(field);
        }
      } else {
        // 字段为空或非字符串，提示创建新配置
        ElMessage.info('配置为空，将使用普通JSON编辑器创建配置');
        openRegularJsonEditor(field);
      }
    } catch (error) {
      console.error('处理可视化编辑失败:', error);
      // 出错时，回退到普通JSON编辑器
      openRegularJsonEditor(field);
    }
    return;
  }
  
  // 其他字段使用普通JSON编辑器
  openRegularJsonEditor(field);
};

// 普通JSON编辑器打开方法
const openRegularJsonEditor = (field: string) => {
  try {
    const fieldValue = formData[field as keyof typeof formData];
    if (fieldValue && typeof fieldValue === 'string') {
      // 处理JSON字符串中的转义字符
      const processedValue = fieldValue.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
        // 替换字符串中的换行符、制表符和其他需要转义的字符
        return match
          .replace(/\n/g, '\\n')
          .replace(/\t/g, '\\t');
      });
      
      // 尝试解析JSON格式，确保格式正确
      JSON.parse(processedValue);
      // 设置当前JSON值为处理后的值
      currentJsonValue.value = processedValue;
      // 打开编辑器 - 将处理后的数据传递给JSON编辑器
      nextTick(() => {
        jsonEditorRef.value?.open();
      });
    } else {
      // 如果字段为空，设置空对象
      currentJsonValue.value = '{}';
      nextTick(() => {
        jsonEditorRef.value?.open();
      });
    }
  } catch (error) {
    // JSON格式错误时，仍然设置值并打开编辑器
    // 由JsonEditor组件内部处理格式错误
    const fieldValue = formData[field as keyof typeof formData];
    if (fieldValue && typeof fieldValue === 'string') {
      // 即使JSON格式不正确，仍然尝试处理转义字符
      const processedValue = fieldValue.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
        return match
          .replace(/\n/g, '\\n')
          .replace(/\t/g, '\\t');
      });
      currentJsonValue.value = processedValue;
    } else {
      currentJsonValue.value = fieldValue && typeof fieldValue === 'string' ? fieldValue : '{}';
    }
    // 移除警告消息，由 JsonEditor 组件内部处理
    // ElMessage.warning('JSON格式有误，编辑器将尝试修复或显示错误信息');
    nextTick(() => {
      jsonEditorRef.value?.open();
    });
  }
};

// 处理JSON编辑器保存事件
const handleJsonEditorSave = (jsonString: string) => {
  if (currentEditingField.value && formData.hasOwnProperty(currentEditingField.value)) {
    try {
      // 对查询数据配置和查询最近变更ID配置进行特殊处理
      if (currentEditingField.value === 'queryDataConfig' || currentEditingField.value === 'queryChangeIdsConfig') {
        // 检查JSON是否含有dsl字段
        const hasDslField = /"dsl"\s*:/i.test(jsonString);
        
        if (!hasDslField) {
          // 如果没有dsl字段，将输入内容包装为dsl字段的值
          try {
            // 尝试解析JSON字符串，确保格式正确
            JSON.parse(jsonString);
            jsonString = jsonString.replace(/\n/g, '\n\t');
            // 格式正确，将其包装为dsl字段
            const wrappedJson = `{\n  "dsl": ${jsonString}\n}`;
            (formData as any)[currentEditingField.value] = wrappedJson;
            
            ElMessage.success('已自动将输入内容设置为DSL字段的值');
          } catch (parseError) {
            // 如果不是有效的JSON，直接包装为字符串
            jsonString = jsonString.replace(/\n/g, '\n\t');
            const wrappedJson = `{\n  "dsl": ${JSON.stringify(jsonString)}\n}`;
            (formData as any)[currentEditingField.value] = wrappedJson;
            
            ElMessage.warning('输入的JSON格式有误，已作为字符串包装到DSL字段');
          }
        } else {
          // 有dsl字段，直接保存
          (formData as any)[currentEditingField.value] = jsonString;
        }
      } else {
        // 其他字段直接保存
        (formData as any)[currentEditingField.value] = jsonString;
      }
      
      // 触发表单验证
      handleJsonInputBlur(currentEditingField.value);
      
      // 向父组件发送数据更新
      emit('sync-config-update', getFormData());
    } catch (error) {
      console.error('保存JSON配置失败:', error);
    }
  }
};

// 处理JSON编辑器取消事件
const handleJsonEditorCancel = () => {
  // 重置编辑状态
  currentEditingField.value = '';
  
  // 可以在这里添加其他取消时的逻辑
  // 不显示通知消息，避免重复
};

// 修改更新Mafka消费配置的方法
const updateMafkaConsumeConfig = (mafkaConfig: any) => {
  if (!mafkaConfig) return;
  
  // 直接将值赋给mafkaConfigObj对象
  if (mafkaConfig.namespace) {
    mafkaConfigObj.namespace = mafkaConfig.namespace;
  }
  if (mafkaConfig.group) {
    mafkaConfigObj.group = mafkaConfig.group;
  }
  if (mafkaConfig.topic) {
    mafkaConfigObj.topic = mafkaConfig.topic;
  }
  if (mafkaConfig.appKey) {
    mafkaConfigObj.appKey = mafkaConfig.appKey;
  }
  if (mafkaConfig.filterScript) {
    if (mafkaConfig.filterScript.length) {
      mafkaConfigObj.filterScript = '';
    } else {
      mafkaConfigObj.filterScript = mafkaConfig.filterScript;
    }
  }
  if (mafkaConfig.outerIdsKey) {
    mafkaConfigObj.outerIdsKey = mafkaConfig.outerIdsKey;
    // 同步更新到表单数据
    mafkaFormData.outerIdsKey = mafkaConfig.outerIdsKey;
  }
  
 // 更新formData.mafkaConsumeConfig
  updateMafkaConsumeConfigJson();
};

// 监听outerIdsKey变化，验证非空
watch(
  () => mafkaConfigObj.outerIdsKey,
  (newValue) => {
    if (!isInitializing.value && formData.type === SyncTypeEnum.MAFKA) {
      outerIdsKeyError.value = !newValue;
      validateContentAndEmit();
    }
  }
);

// 添加表单引用
const mafkaFormRef = ref<FormInstance>();

// 更新mafkaConsumeConfig JSON字符串
const updateMafkaConsumeConfigJson = () => {
  // 在初始化阶段不进行验证
  if (isInitializing.value) return;

  // 构建JSON对象
  const mafkaJsonObj = {
    namespace: mafkaConfigObj.namespace,
    group: mafkaConfigObj.group,
    topic: mafkaConfigObj.topic,
    appkey: mafkaConfigObj.appKey,
    scene: mafkaConfigObj.scene,
    tenant: mafkaConfigObj.tenant,
    outerIdsKey: mafkaFormData.outerIdsKey || '', // 使用表单数据
    filterScript: mafkaConfigObj.filterScript
  };
  
  // 将对象转换为JSON字符串，使用缩进格式化
  formData.mafkaConsumeConfig = JSON.stringify(mafkaJsonObj, null, 2);
  formData.mafkaConsumeConfig = formData.mafkaConsumeConfig.replace(/\\n/g, '\n').replace(/\\t/g, '\t');
  
  // 触发表单验证 - 不再验证整个表单
  nextTick(() => {
    if (configFormRef.value) {
      configFormRef.value.validateField(['mafkaConsumeConfig']);
    }
    // 更新到父组件
    emit('sync-config-update', getFormData());
  });
};

// 解析现有的mafkaConsumeConfig到mafkaConfigObj
const parseMafkaConsumeConfig = () => {
  if (!formData.mafkaConsumeConfig || formData.mafkaConsumeConfig === DEFAULT_CONFIGS.mafkaConsumeConfig) {
    return;
  }
  
  try {
    const config = JSON.parse(formData.mafkaConsumeConfig);
    mafkaConfigObj.namespace = config.namespace || '';
    mafkaConfigObj.group = config.group || '';
    mafkaConfigObj.topic = config.topic || '';
    mafkaConfigObj.appKey = config.appkey || ''; // 注意这里的key名可能是appkey而不是appKey
    mafkaConfigObj.scene = config.scene !== undefined ? config.scene : -1;
    mafkaConfigObj.tenant = config.tenant !== undefined ? config.tenant : -1;
    mafkaConfigObj.outerIdsKey = config.outerIdsKey || '';
    mafkaConfigObj.filterScript = config.filterScript || '';
    
    // 同步更新到表单数据
    mafkaFormData.outerIdsKey = mafkaConfigObj.outerIdsKey;
  } catch (e) {
    console.error('解析Mafka消费配置失败:', e);
  }
};

// 添加主动清除outer_id字段校验的方法
const clearOuterIdsKeyValidation = () => {
  if (mafkaFormRef.value) {
    nextTick(() => {
      // 使用可选链操作符避免undefined错误
      mafkaFormRef.value?.clearValidate?.(['outerIdsKey']);
    });
  }
};

// 添加更新全量PoiId校验配置JSON的方法
const updateTotalPoiCheckJson = () => {
  // 在初始化阶段不进行更新
  if (isInitializing.value) return;

  // 构建JSON对象
  const checkJsonObj = {
    enable: totalPoiCheckConfig.enable,
    filterScript: totalPoiCheckConfig.filterScript || ''
  };
  
  // 将对象转换为JSON字符串，使用缩进格式化
  formData.totalPoiConsistencyCheck = JSON.stringify(checkJsonObj, null, 2);
  
  // 触发表单验证
  nextTick(() => {
    if (configFormRef.value) {
      configFormRef.value.validateField(['totalPoiConsistencyCheck']);
    }
    // 更新到父组件
    emit('sync-config-update', getFormData());
  });
};

// 解析现有的totalPoiConsistencyCheck到totalPoiCheckConfig
const parseTotalPoiCheckConfig = () => {
  if (!formData.totalPoiConsistencyCheck || formData.totalPoiConsistencyCheck === DEFAULT_CONFIGS.totalPoiConsistencyCheck) {
    // 如果没有值或是默认值，使用默认设置但不更新formData
    totalPoiCheckConfig.enable = true;
    totalPoiCheckConfig.filterScript = '';
    return;
  }
  
  try {
    const config = JSON.parse(formData.totalPoiConsistencyCheck);
    totalPoiCheckConfig.enable = config.enable !== undefined ? config.enable : true;
    totalPoiCheckConfig.filterScript = config.filterScript || '';
  } catch (e) {
    console.error('解析全量PoiId校验配置失败:', e);
    // 解析失败时使用默认值
    totalPoiCheckConfig.enable = true;
    totalPoiCheckConfig.filterScript = '';
  }
};

// 添加一个新方法：设置整个表单数据
const setFormData = (config: SyncConfigItem) => {
  isInitializing.value = true;
  
  // 设置表单数据
  Object.assign(formData, {
    syncScene: config.syncScene,
    syncTenant: config.syncTenant,
    syncName: config.syncName,
    syncDescription: config.syncDescription,
    lionConfigDescription: config.lionConfigDescription,
    syncFieldCodes: config.syncFieldCodes ? [...config.syncFieldCodes] : [],
    type: config.type !== undefined ? config.type : (props.accessWay === 'dts' ? SyncTypeEnum.DTS : SyncTypeEnum.MAFKA),
    isPoiOuter: config.isPoiOuter !== undefined ? config.isPoiOuter : false,
    totalPoiConsistencyCheck: typeof config.totalPoiConsistencyCheck === 'object' && config.totalPoiConsistencyCheck !== null ? JSON.stringify(config.totalPoiConsistencyCheck, null, 2) : (config.totalPoiConsistencyCheck || DEFAULT_CONFIGS.totalPoiConsistencyCheck),
    mafkaConsumeConfig: typeof config.mafkaConsumeConfig === 'object' && config.mafkaConsumeConfig !== null ? JSON.stringify(config.mafkaConsumeConfig, null, 2) : (config.mafkaConsumeConfig || DEFAULT_CONFIGS.mafkaConsumeConfig),
    queryDataConfig: typeof config.queryDataConfig === 'object' && config.queryDataConfig !== null ? JSON.stringify(config.queryDataConfig, null, 2) : (config.queryDataConfig || DEFAULT_CONFIGS.queryDataConfig),
    queryChangeIdsConfig: typeof config.queryChangeIdsConfig === 'object' && config.queryChangeIdsConfig !== null ? JSON.stringify(config.queryChangeIdsConfig, null, 2) : (config.queryChangeIdsConfig || DEFAULT_CONFIGS.queryChangeIdsConfig),
    dtsSyncConfig: typeof config.dtsSyncConfig === 'object' && config.dtsSyncConfig !== null ? JSON.stringify(config.dtsSyncConfig, null, 2) : (config.dtsSyncConfig || DEFAULT_CONFIGS.dtsSyncConfig),
    valid: config.valid !== undefined ? config.valid : 1
  });
  
  // 解析Mafka配置
  if (formData.type === SyncTypeEnum.MAFKA && formData.mafkaConsumeConfig) {
    parseMafkaConsumeConfig();
  }
  
  // 解析一致性检查配置并设置模式
  if (formData.type === SyncTypeEnum.MAFKA) {
    parseTotalPoiCheckConfig();
    
    // 确定一致性检查模式
    if (formData.queryChangeIdsConfig && formData.queryChangeIdsConfig.trim() !== '') {
      consistencyCheckMode.value = 'incremental';
    } else {
      consistencyCheckMode.value = 'full';
    }
    
    // 更新验证规则
    updateRulesByConsistencyMode(consistencyCheckMode.value);
  }
  
  // 使用setTimeout恢复验证状态，确保DOM更新完成后再允许验证
  setTimeout(() => {
    isInitializing.value = false;
    // 验证表单
    nextTick(() => {
      validateContentAndEmit();
    });
  }, 200);
};

// 对外暴露方法
defineExpose({
  configFormRef,
  validateConfigForm,
  activeValidate,
  getFormData,
  resetForm,
  submitForm,
  isSubmitting,
  setSyncFieldCodes,
  updateMafkaConsumeConfig,
  clearOuterIdsKeyValidation,
  setFormData
});

// 添加处理DSL保存事件的方法
const handleDslSaved = (dsl: string) => {
  if (!currentEditingField.value) return;
  
  try {
    // 检查当前编辑的字段
    if (currentEditingField.value === 'queryDataConfig' || currentEditingField.value === 'queryChangeIdsConfig') {
      // 包装为{"dsl": ...}
      dsl = dsl.replace(/\n/g, '\n  ');
      const dslWrapper = '{\n  "dsl": '+ dsl + '\n}';
      // 更新表单数据
      (formData as any)[currentEditingField.value] = dslWrapper;
      // 关闭抽屉
      showOrchestrationDrawer.value = false;
      // 触发表单验证
      handleJsonInputBlur(currentEditingField.value);
      // 向父组件发送数据更新
      emit('sync-config-update', getFormData());
    }
  } catch (error) {
    console.error('处理DSL保存失败:', error);
    ElMessage.error('处理DSL保存失败，请手动编辑配置');
  }
};
</script>

<style scoped lang="scss">
.sync-config-info {
  .guide-alert {
    margin-bottom: 20px;
    
    :deep(.el-alert__content) {
      padding: 0 8px;
    }

    :deep(.el-alert__icon) {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 1.5;
    }
  }
  
  .wiki-link-container {
    margin-top: 10px;
    margin-bottom: 30px;
    
    :deep(.el-link) {
      font-size: 14px;
      font-weight: 500;
    }
  }
  
  .field-select {
    width: 500px;
  }
  
  // 文本域容器样式
  .textarea-container {
    position: relative;
    width: 700px;
    
    .el-textarea {
      width: 100%;
    }
    
    // JSON编辑按钮样式
    .json-visual-edit {
      position: absolute;
      top: 4px;
      right: 10px;
      z-index: 2;
      
      .el-button {
        font-size: 14px;
        padding: 4px 8px;
        background-color: rgba(255, 255, 255, 0.8);
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  :deep(.el-form) {
    max-width: 950px;
    
    .el-form-item {
      margin-bottom: 22px;
      position: relative;
      
      .el-form-item__label {
        padding-right: 20px;
        font-weight: 500;
      }
      
      .el-form-item__content {
        display: flex;
        flex-wrap: wrap;
      }
    }
    
    // 处理具有JSON可视化编辑的表单项
    .json-form-item {
      margin-bottom: 22px;
    }
    
    // 一致性校验方式单选按钮组
    .consistency-check-mode {
      .el-form-item__content {
        line-height: 32px;
      }
    }
  }
  
  // 隐藏非.textarea-container内的.json-visual-edit
  > .json-visual-edit {
    display: none;
  }
  
  .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
      min-width: 200px;
    }
  }
  
  .section-title {
    width: 100%;
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    clear: both;
  }
  
  .mafka-fields-container {
    margin-bottom: 30px;
    width: 100%;
    
    :deep(.el-form) {
      .el-form-item__label {
        text-align: right;
        padding-right: 20px;
        font-weight: 500;
      }
      
      .el-form-item__content {
        display: flex;
        flex-wrap: wrap;
      }
      
      .el-form-item {
        position: relative;
        margin-bottom: 22px;
      }
    }
  }
  
  .mafka-field-group {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 15px;
    
    .mafka-field {
      flex: 1 0 calc(50% - 30px);
      min-width: 250px;
      margin-bottom: 0;
      
      &.full-width {
        flex-basis: 100%;
        min-width: 100%;
      }
    }
  }
  
  // Media queries for responsive design
  @media screen and (max-width: 768px) {
    :deep(.el-form) {
      .el-form-item__label {
        float: none;
        display: block;
        text-align: left;
        padding: 0 0 8px 0;
        width: 100% !important;
      }
      
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
    
    .form-row {
      flex-direction: column;
      gap: 10px;
      
      .el-form-item {
        margin-bottom: 15px;
        width: 100%;
      }
    }
    
    .mafka-field-group {
      flex-direction: column;
      gap: 15px;
      
      .mafka-field {
        width: 100%;
        min-width: 100%;
        flex-basis: 100%;
      }
    }
    
    .mafka-fields-container {
      :deep(.el-form) {
        .el-form-item__label {
          float: none;
          display: block;
          text-align: left;
          padding: 0 0 8px 0;
          width: 100% !important;
        }
        
        .el-form-item__content {
          margin-left: 0 !important;
        }
      }
    }
    
    .field-select {
      width: 100%;
    }
    
    .textarea-container {
      width: 100%;
      
      .json-visual-edit {
        position: static;
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
        margin-bottom: 8px;
      }
    }
    
    .full-poi-check-container {
      width: 100%;
      
      .full-poi-check-item {
        flex-direction: column;
        align-items: flex-start;
        
        .check-label {
          width: 100%;
          text-align: left;
          margin-bottom: 5px;
        }
        
        .el-input {
          width: 100% !important;
        }
      }
    }
  }
  
  // 添加全量PoiId校验配置样式
  .full-poi-check-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 700px;
    
    .full-poi-check-item {
      display: flex;
      align-items: center;
      
      &.full-width {
        margin-top: 5px;
        align-items: flex-start;
      }
      
      .check-label {
        width: 90px;
        text-align: right;
        padding-right: 10px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

// JSON编辑器样式
.json-editor-error {
  color: var(--el-color-danger);
  padding: 20px;
  text-align: center;
  font-weight: bold;
}

.json-editor-container {
  padding: 20px;
  height: calc(100% - 53px);
  overflow-y: auto;
}

.json-editor-info {
  margin-bottom: 20px;
  
  :deep(.el-alert__content) {
    padding: 0 8px;
  }
  
  :deep(.el-alert__icon) {
    margin-right: 8px;
    font-size: 16px;
  }
}
</style> 