<template>
  <div class="dsl-config-info">
    <!-- 添加提示信息 -->
    <el-alert
      type="warning"
      show-icon
      :closable="false"
      class="guide-alert"
    >
      <span>
        此阶段配置的是在<b>「RPC-Thrift 服务」</b>中调用的相关接口信息。<b>「DSL」</b>是一个特殊的语言，用于描述调用多个服务的编排信息。<br>
        注意：建议 DSL 在<b>可视化编辑器</b>中配置。<br>
      </span>
    </el-alert>
    
    <!-- RPC配置表单 -->
    <el-form ref="configFormRef" :model="formData" :rules="rules" label-position="right" label-width="160px" :validate-on-rule-change="false">
      <!-- 添加基本信息标题 -->
      <h3 class="section-title">基本信息</h3>
      
      <el-form-item label="DSL名称" prop="dslName">
        <el-input
          v-model="formData.dslName"
          placeholder="请输入DSL名称"
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item label="DSL描述" prop="dslDescription">
        <el-input
          v-model="formData.dslDescription"
          type="textarea"
          :rows="3"
          placeholder="请输入DSL描述"
          style="width: 500px"
        />
      </el-form-item>

      <!-- 添加DSL配置信息标题 -->
      <h3 class="section-title">DSL配置信息</h3>
      
      <el-form-item label="DSL内容" prop="dsl" class="json-form-item">
        <div class="textarea-container">
          <el-input
            v-model="formData.dsl"
            type="textarea"
            :rows="4"
            placeholder="请输入DSL内容（JSON格式）"
            style="width: 700px"
            :disabled="true"
            @blur="handleJsonInputBlur('dsl')"
          />
          <div class="json-visual-edit">
            <el-button type="primary" text @click="openJsonEditor('dsl')">
              <el-icon class="el-icon--left"><EditPen /></el-icon>
              可视化编辑
            </el-button>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="使用字段" prop="fieldCodes">
        <el-select 
          v-model="formData.fieldCodes" 
          multiple 
          filterable 
          :allow-create="!fieldsLoading && fieldOptions.length > 0 && !fieldSelectDisabled"
          default-first-option
          placeholder="请选择编排字段"
          style="width: 500px"
          class="field-select"
          :loading="fieldsLoading"
          :disabled="fieldSelectDisabled"
          :no-data-text="fieldsLoading ? '加载中...' : '暂无数据'"
          @focus="fetchFieldList"
        >
          <el-option 
            v-for="field in fieldOptions" 
            :key="field.value" 
            :label="field.label" 
            :value="field.value" 
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否抛出异常" prop="thrownException">
        <el-switch
          v-model="formData.thrownException"
          active-text="是"
          inactive-text="否"
          :active-value="true"
          :inactive-value="false"
          style="width: 80px"
        />
      </el-form-item>

      <el-form-item label="预热参数配置" prop="dslPreHeatParam" class="json-form-item">
        <div class="textarea-container">
          <el-input
            v-model="formData.dslPreHeatParam"
            type="textarea"
            :rows="3"
            placeholder='请输入预热参数配置（String格式，例如：{\"key\": \"value\"}，{}表示没有预热参数）'
            style="width: 700px"
            @blur="handleJsonInputBlur('dslPreHeatParam')"
          />
        </div>
      </el-form-item>
      <el-form-item label="其他配置" prop="option" class="json-form-item">
        <div class="textarea-container">
          <el-input
            v-model="formData.optionStr"
            type="textarea"
            :rows="3"
            placeholder="请输入JSON格式的配置"
            style="width: 700px"
            @blur="handleJsonInputBlur('optionStr')"
          />
          <div class="json-visual-edit">
            <el-button type="primary" text @click="openJsonEditor('optionStr')">
              <el-icon class="el-icon--left"><EditPen /></el-icon>
              可视化编辑
            </el-button>
          </div>
        </div>
      </el-form-item>
      <!-- TODO 后续使用时删除 v-show="false" -->
      <template v-show="false">
        <el-form-item label="支持QPS" prop="supportQps">
          <el-input-number v-model="formData.supportQps" :min="0" :step="1" placeholder="请输入支持QPS" style="width: 200px" />
        </el-form-item>
        <el-form-item label="实际QPS" prop="actualQps">
          <el-input-number v-model="formData.actualQps" :min="0" :step="1" placeholder="请输入实际QPS" style="width: 200px" />
        </el-form-item>
      </template>
      <h3 class="section-title">Lion 配置</h3>
      <el-form-item label="配置描述" prop="lionConfigDescription">
        <div class="textarea-container">
          <el-input
            v-model="formData.lionConfigDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入Lion配置描述"
            style="width: 700px"
          />
        </div>
      </el-form-item>
    </el-form>
  </div>

  <!-- 使用JsonEditor组件 -->
  <json-editor
    ref="jsonEditorRef"
    v-model="currentJsonValue"
    @save="handleJsonEditorSave"
    @cancel="handleJsonEditorCancel"
  />

  <!-- 添加服务编排可视化抽屉 -->
  <el-drawer
    v-model="showOrchestrationDrawer"
    title="服务编排可视化"
    size="90%"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :before-close="handleDrawerClose"
  >
    <template #header>
      <div class="drawer-header">
        <span>服务编排可视化</span>
        <el-tag v-if="drawerLoading" type="info" effect="plain">加载中...</el-tag>
      </div>
    </template>
    <div v-loading="drawerLoading" class="visualizer-container">
      <OrchestrationVisualizer 
        v-if="visualizerDslKey" 
        :dslKey="visualizerDslKey" 
        @dsl-saved="handleDslSaved"
      />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { EditPen } from '@element-plus/icons-vue';
import JsonEditor from '../../../../components/common/JsonEditor.vue';
import { getFieldMetadataList } from '../../../../request';
import type { DSLConfigItem } from '../../../../types';
import { goToOrchestrationVisualizer, getVisualDslKey } from '../../../../utils/orchestrationUtils';
import OrchestrationVisualizer from '../../../../views/tools/orchestration/OrchestrationVisualizer.vue';

const props = defineProps({
  dslConfigForm: {
    type: Object,
    default: () => ({})
  },
  fieldSelectDisabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  (e: 'valid-change', valid: boolean): void;
  (e: 'dsl-config-update', data: DSLConfigItem): void;
}>();

// 表单引用
const configFormRef = ref<FormInstance>();
const isInitializing = ref(true);
const isSubmitting = ref(false);
const fieldsLoading = ref(false);

// 字段选项
const fieldOptions = ref<Array<{ label: string, value: string }>>([]);

// 表单数据
const formData = reactive<Partial<DSLConfigItem> & { optionStr?: string; supportQps?: number; actualQps?: number }>({
  dslName: '',
  dslDescription: '',
  dsl: '',
  fieldCodes: [],
  thrownException: false,
  dslPreHeatParam: '',
  lionConfigDescription: '',
  valid: 1,
  optionStr: '',
  supportQps: 0,
  actualQps: 0
});

// 定义默认值常量
const DEFAULT_CONFIGS = {
  dsl: '',
  dslPreHeatParam: '',
  optionStr: '{}',
  exampleDsl: `{
  "name": "exampleDsl",
  "description": "DSL样例（3个节点，简单链式依赖）",
  "timeout": 20,
  "tasks": [
    {
      "alias": "node1",
      "taskType": "Calculate",
      "description": "node1",
      "inputs": {
        "sourcePoiIds": "$params.poiIds"
      }
    },
    {
      "alias": "node2",
      "taskType": "Calculate",
      "description": "node2",
      "inputs": "count($node1.sourcePoiIds)"
    },
    {
      "alias": "node3",
      "taskType": "Calculate",
      "description": "node3",
      "inputs": {
        "size": "$node2"
      }
    }
  ],
  "outputs": {
    "type": "map",
    "transform": "seq.map('total', $node3.size, 'data', $node1)"
  }
}`
};

// 添加状态变量
const showOrchestrationDrawer = ref(false);
const visualizerDslKey = ref('');
const drawerLoading = ref(false);

// 验证JSON格式
const validateJson = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  
  // 检查是否等于默认值
  const fieldName = rule.field;
  if (DEFAULT_CONFIGS[fieldName as keyof typeof DEFAULT_CONFIGS] === value) {
    callback(new Error('请修改默认示例内容后再提交'));
    return;
  }
  
  try {
    // 处理JSON字符串中的转义字符
    const processedValue = value.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
      // 替换字符串中的换行符、制表符和其他需要转义的字符
      return match
        .replace(/\n/g, '\\n')
        .replace(/\t/g, '\\t');
    });
    
    JSON.parse(processedValue);
    callback();
  } catch (error) {
    callback(new Error('请输入有效的JSON格式'));
  }
};

// 验证规则
const rules: FormRules = {
  dslName: [
    { required: true, message: '请输入DSL名称', trigger: 'blur' },
    { max: 50, message: 'DSL名称不能超过50个字符', trigger: 'change' }
  ],
  dslDescription: [
    { required: true, message: '请输入DSL描述', trigger: 'blur' },
    { max: 200, message: 'DSL描述不能超过200个字符', trigger: 'change' }
  ],
  dsl: [
    { required: true, message: '请输入DSL内容', trigger: 'blur' },
    { validator: validateJson, trigger: 'blur' }
  ],
  fieldCodes: [
    { required: true, message: '请选择编排字段', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个编排字段', trigger: 'change' }
  ],
  thrownException: [
    { required: true, message: '请选择是否抛出异常', trigger: 'change' }
  ],
  dslPreHeatParam: [
    { required: true, message: '请输入预热参数配置', trigger: 'blur' },
    { validator: validateJson, trigger: 'blur' }
  ],
  lionConfigDescription: [
    { required: true, message: '请输入Lion配置描述', trigger: 'blur' },
    { max: 200, message: 'Lion配置描述不能超过200个字符', trigger: 'change' }
  ],
  option: [{
    validator: (rule: any, value: any, callback: any) => {
      if (!formData.optionStr) return callback()
      try {
        JSON.parse(formData.optionStr)
        callback()
      } catch (e) {
        callback(new Error('请输入合法的JSON'))
      }
    },
    trigger: 'blur'
  }]
};

// 获取字段列表
const fetchFieldList = async () => {
  if (fieldOptions.value.length > 0) return;
  
  fieldsLoading.value = true;
  try {
    const res = await getFieldMetadataList({
      pageNo: 1,
      pageSize: 1000,
      valid: 1
    });
    
    if (res.code === 0 && res.data?.records) {
      fieldOptions.value = res.data.records.map((item: any) => ({
        label: item.fieldCode,
        value: item.fieldCode
      }));
    } else {
      ElMessage.error(res.message || '获取字段列表失败');
    }
  } catch (error) {
    console.error('获取字段列表失败:', error);
    ElMessage.error('获取字段列表失败');
  } finally {
    fieldsLoading.value = false;
  }
};

// 监听dslDescription变化，自动设置lionConfigDescription
watch(
  () => formData.dslDescription,
  (newValue) => {
    if (!newValue || isInitializing.value) return;
    
    // 自动设置lionConfigDescription为"【RPC服务配置】+description"
    formData.lionConfigDescription = `【RPC服务配置】${newValue}`;
  }
);

// 监听表单数据变化，触发验证和更新事件
watch(
  () => formData,
  () => {
    // 排除初始化阶段
    if (!isInitializing.value) {
      // 触发表单验证
      const isValid = validateContentAndEmit();
      if (isValid) {
      
      } else {
        
      }
      // 向父组件发送数据更新
      emit('dsl-config-update', getFormData());
    }
  },
  { deep: true }
);

// 处理JSON输入框失焦事件
const handleJsonInputBlur = (field: string) => {
  // 在初始化阶段不进行验证
  if (isInitializing.value) return;
  
  // 手动验证当前字段
  nextTick(() => {
    configFormRef.value?.validateField([field]);
    // 触发内容校验
    validateContentAndEmit();
  });
};

// 表单内容校验
const validateContentAndEmit = () => {
  const isValid = validateFormContent();
  emit('valid-change', isValid);
  return isValid;
};

// 基于表单内容的验证方法
const validateFormContent = (): boolean => {
  // 检查必填字段
  if (
    !formData.dslName ||
    !formData.dslDescription ||
    !formData.dsl ||
    !formData.fieldCodes ||
    formData.fieldCodes.length === 0 ||
    formData.thrownException === undefined ||
    !formData.dslPreHeatParam ||
    !formData.lionConfigDescription
  ) {
    return false;
  }
  
  // 检查DSL内容是否有效
  try {
    if (formData.dsl) {
      if (formData.dsl === DEFAULT_CONFIGS.dsl) {
        return false;
      }
      JSON.parse(formData.dsl.replace(/\n/g, '').replace(/\t/g, ''));
    }
  } catch (error) {
    return false;
  }
  
  // 检查预热参数格式是否有效
  try {
    if (formData.dslPreHeatParam) {
      if (formData.dslPreHeatParam === DEFAULT_CONFIGS.dslPreHeatParam) {
        return false;
      }
      JSON.parse(formData.dslPreHeatParam.replace(/\n/g, '').replace(/\t/g, ''));
    }
  } catch (error) {
    return false;
  }
  
  // 检查其他配置（optionStr）格式是否有效
  if (formData.optionStr && formData.optionStr.trim() !== '') {
    try {
      JSON.parse(formData.optionStr);
    } catch (error) {
      return false;
    }
  }
  
  return true;
};

// 用于表单校验的方法
const validateConfigForm = async () => {
  if (!configFormRef.value) return false;
  
  try {
    await configFormRef.value.validate();
    // 表单验证通过后，进行内容校验
    return validateContentAndEmit();
  } catch (error) {
    emit('valid-change', false);
    return false;
  }
};

// 主动触发表单验证并更新状态
const activeValidate = async () => {
  const result = validateContentAndEmit();
  return result;
};

// 获取表单数据
const getFormData = (): DSLConfigItem => {
  let optionObj = undefined
  if (formData.optionStr) {
    try {
      optionObj = JSON.parse(formData.optionStr)
    } catch (e) {
      optionObj = undefined
    }
  }
  return {
    dslName: formData.dslName || '',
    dslDescription: formData.dslDescription || '',
    dsl: formData.dsl || '',
    fieldCodes: formData.fieldCodes || [],
    thrownException: formData.thrownException || false,
    dslPreHeatParam: formData.dslPreHeatParam || '',
    lionConfigDescription: formData.lionConfigDescription || '',
    valid: formData.valid || 1,
    option: optionObj ?? {},
    supportQps: formData.supportQps ?? 0,
    actualQps: formData.actualQps ?? 0
  } as DSLConfigItem
};

// 重置表单
const resetForm = () => {
  if (configFormRef.value) {
    configFormRef.value.resetFields();
  }
  
  Object.assign(formData, {
    dslName: '',
    dslDescription: '',
    dsl: DEFAULT_CONFIGS.dsl,
    fieldCodes: [],
    thrownException: false,
    dslPreHeatParam: DEFAULT_CONFIGS.dslPreHeatParam,
    lionConfigDescription: '',
    valid: 1,
    optionStr: DEFAULT_CONFIGS.optionStr,
    supportQps: 0,
    actualQps: 0
  });
  
  // 重置后通知父组件
  emit('valid-change', false);
};

// 设置表单数据
const setFormData = (data: Partial<DSLConfigItem>) => {
  isInitializing.value = true;
  
  Object.assign(formData, {
    dslName: data.dslName || '',
    dslDescription: data.dslDescription || '',
    dsl: typeof data.dsl === 'object' && data.dsl !== null ? JSON.stringify(data.dsl, null, 2) : (data.dsl || DEFAULT_CONFIGS.dsl),
    fieldCodes: data.fieldCodes || [],
    thrownException: data.thrownException || false,
    dslPreHeatParam: data.dslPreHeatParam === null ? '{}' : (data.dslPreHeatParam || DEFAULT_CONFIGS.dslPreHeatParam),
    lionConfigDescription: data.lionConfigDescription || '',
    valid: data.valid !== undefined ? data.valid : 1,
    optionStr: data.option ? JSON.stringify(data.option, null, 2) : DEFAULT_CONFIGS.optionStr,
    supportQps: data.supportQps ?? 0,
    actualQps: data.actualQps ?? 0
  });
  
  // 使用setTimeout恢复验证状态，确保DOM更新完成后再允许验证
  setTimeout(() => {
    isInitializing.value = false;
    // 验证表单
    nextTick(() => {
      validateContentAndEmit();
    });
  }, 200);
};

// JSON编辑相关
const jsonEditorRef = ref<InstanceType<typeof JsonEditor> | null>(null);
const currentEditingField = ref<string>('');
const currentJsonValue = ref<string>('');

// 打开JSON编辑器
const openJsonEditor = (field: string) => {
  currentEditingField.value = field;
  
  // 特殊处理dsl字段
  if (field === 'dsl') {
    try {
      let fieldValue = formData[field];
      if (!fieldValue || fieldValue === '') {
        fieldValue = DEFAULT_CONFIGS.exampleDsl;
      }
      
      if (typeof fieldValue === 'string') {
        try {
          // 设置loading状态为true
          drawerLoading.value = true;
          
          // 直接使用DSL内容生成可视化key
          visualizerDslKey.value = getVisualDslKey(fieldValue) || '';
          if (visualizerDslKey.value) {
            // 打开抽屉展示组件
            showOrchestrationDrawer.value = true;
            
            // 使用setTimeout模拟异步加载，并在一定时间后关闭loading
            setTimeout(() => {
              drawerLoading.value = false;
            }, 1000); // 设置一个合理的延迟，以确保组件已加载
            
            return;
          } else {
            drawerLoading.value = false;
            ElMessage.error('生成DSL可视化Key失败，请稍后重试');
            // 回退到普通JSON编辑器
            openRegularJsonEditor(field);
          }
        } catch (error) {
          drawerLoading.value = false;
          console.error('解析JSON字符串失败:', error);
          ElMessage.warning('配置格式不正确，将使用普通JSON编辑器');
          // 解析失败时，回退到普通JSON编辑器
          openRegularJsonEditor(field);
        }
      } else {
        // 字段为空或非字符串，提示创建新配置
        ElMessage.info('配置为空，将使用普通JSON编辑器创建配置');
        openRegularJsonEditor(field);
      }
    } catch (error) {
      console.error('处理可视化编辑失败:', error);
      // 出错时，回退到普通JSON编辑器
      openRegularJsonEditor(field);
    }
    return;
  }
  
  // 其他字段使用普通JSON编辑器
  openRegularJsonEditor(field);
};

// 普通JSON编辑器打开方法
const openRegularJsonEditor = (field: string) => {
  try {
    const fieldValue = formData[field as keyof typeof formData];
    if (fieldValue && typeof fieldValue === 'string') {
      // 处理JSON字符串中的转义字符
      const processedValue = fieldValue.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
        // 替换字符串中的换行符、制表符和其他需要转义的字符
        return match
          .replace(/\n/g, '\\n')
          .replace(/\t/g, '\\t');
      });
      
      // 尝试解析JSON格式，确保格式正确
      JSON.parse(processedValue);
      // 设置当前JSON值为处理后的值
      currentJsonValue.value = processedValue;
      // 打开编辑器
      nextTick(() => {
        jsonEditorRef.value?.open();
      });
    } else {
      // 如果字段为空，设置默认值
      const defaultValue = DEFAULT_CONFIGS[field as keyof typeof DEFAULT_CONFIGS] || '{}';
      currentJsonValue.value = defaultValue;
      nextTick(() => {
        jsonEditorRef.value?.open();
      });
    }
  } catch (error) {
    // JSON格式错误时，仍然设置值并打开编辑器
    const fieldValue = formData[field as keyof typeof formData];
    if (fieldValue && typeof fieldValue === 'string') {
      currentJsonValue.value = fieldValue;
    } else {
      const defaultValue = DEFAULT_CONFIGS[field as keyof typeof DEFAULT_CONFIGS] || '{}';
      currentJsonValue.value = defaultValue;
    }
    nextTick(() => {
      jsonEditorRef.value?.open();
    });
  }
};

// 处理JSON编辑器保存事件
const handleJsonEditorSave = (jsonString: string) => {
  if (currentEditingField.value && formData.hasOwnProperty(currentEditingField.value)) {
    try {
      // 保存JSON到表单数据
      (formData as any)[currentEditingField.value] = jsonString;
      
      // 触发表单验证
      handleJsonInputBlur(currentEditingField.value);
      
      // 向父组件发送数据更新
      emit('dsl-config-update', getFormData());
    } catch (error) {
      console.error('保存JSON配置失败:', error);
    }
  }
};

// 处理JSON编辑器取消事件
const handleJsonEditorCancel = () => {
  // 重置编辑状态
  currentEditingField.value = '';
  
  // 可以在这里添加其他取消时的逻辑
};

// 提交表单
const submitForm = async () => {
  const isValid = await validateConfigForm();
  if (!isValid) return false;
  
  isSubmitting.value = true;
  
  try {
    emit('dsl-config-update', getFormData());
    return true;
  } catch (error) {
    console.error('配置表单提交失败:', error);
    return false;
  } finally {
    isSubmitting.value = false;
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 初始化DSL和预热参数的默认值
  if (!formData.dsl) {
    formData.dsl = DEFAULT_CONFIGS.dsl;
  }
  
  if (!formData.dslPreHeatParam) {
    formData.dslPreHeatParam = DEFAULT_CONFIGS.dslPreHeatParam;
  }
  
  // 初始化其他配置的默认值
  if (!formData.optionStr) {
    formData.optionStr = DEFAULT_CONFIGS.optionStr;
  }
  
  // 延迟初始化完成的状态，避免过早触发验证
  setTimeout(() => {
    isInitializing.value = false;
    validateContentAndEmit();
  }, 300);
});

// 添加处理DSL保存事件的方法
const handleDslSaved = (dsl: string) => {
  if (!currentEditingField.value) return;
  
  try {
    // 只处理dsl字段
    if (currentEditingField.value === 'dsl') {
      try {
        // 设置loading状态
        drawerLoading.value = true;
        
        // 直接更新表单数据，不需要像SyncConfigAccessGuideForm那样包装dsl字段
        formData.dsl = dsl;
        
        // 延迟关闭抽屉，给用户一些视觉反馈
        setTimeout(() => {
          // 关闭抽屉
          showOrchestrationDrawer.value = false;
          // 关闭loading状态
          drawerLoading.value = false;
          
          // 触发表单验证
          handleJsonInputBlur('dsl');
          
          // 向父组件发送数据更新
          emit('dsl-config-update', getFormData());
          
          ElMessage.success('DSL配置已成功更新');
        }, 500);
      } catch (parseError) {
        drawerLoading.value = false;
        console.error('DSL格式解析失败:', parseError);
        ElMessage.error('DSL格式不正确，请检查后重试');
      }
    }
  } catch (error) {
    drawerLoading.value = false;
    console.error('处理DSL保存失败:', error);
    ElMessage.error('处理DSL保存失败，请手动编辑配置');
  }
};

// 设置字段代码
const setFieldCodes = (codes: string[], disabled: boolean = false) => {
  // 设置字段代码
  formData.fieldCodes = codes;
  
  // 如果传入的字段代码不在当前选项中，需要添加到选项列表
  if (codes && codes.length > 0) {
    // 确保有字段选项用于显示
    const currentCodes = fieldOptions.value.map(opt => opt.value);
    const newCodes = codes.filter(code => !currentCodes.includes(code));
    
    // 添加新的字段选项
    if (newCodes.length > 0) {
      fieldOptions.value = [
        ...fieldOptions.value,
        ...newCodes.map(code => ({ label: code, value: code }))
      ];
    }
  }
  
  // 触发验证
  nextTick(() => {
    validateContentAndEmit();
  });
};

// 处理抽屉关闭前的回调
const handleDrawerClose = (done: () => void) => {
  // 清除loading状态
  drawerLoading.value = false;
  // 关闭抽屉
  done();
};

// 对外暴露方法
defineExpose({
  configFormRef,
  validateConfigForm,
  activeValidate,
  getFormData,
  resetForm,
  submitForm,
  isSubmitting,
  setFormData,
  setFieldCodes
});
</script>

<style scoped lang="scss">
.dsl-config-info {
  .guide-alert {
    margin-bottom: 20px;
    
    :deep(.el-alert__content) {
      padding: 0 8px;
    }

    :deep(.el-alert__icon) {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 1.5;
    }
  }
  
  .field-select {
    width: 500px;
  }
  
  // 文本域容器样式
  .textarea-container {
    position: relative;
    width: 700px;
    
    .el-textarea {
      width: 100%;
    }
    
    // JSON编辑按钮样式
    .json-visual-edit {
      position: absolute;
      top: 4px;
      right: 10px;
      z-index: 2;
      
      .el-button {
        font-size: 14px;
        padding: 4px 8px;
        background-color: rgba(255, 255, 255, 0.8);
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  :deep(.el-form) {
    max-width: 950px;
    
    .el-form-item {
      margin-bottom: 22px;
      position: relative;
      
      .el-form-item__label {
        padding-right: 20px;
        font-weight: 500;
      }
      
      .el-form-item__content {
        display: flex;
        flex-wrap: wrap;
      }
    }
    
    // 处理具有JSON可视化编辑的表单项
    .json-form-item {
      margin-bottom: 22px;
    }
  }
  
  // 隐藏非.textarea-container内的.json-visual-edit
  > .json-visual-edit {
    display: none;
  }
  
  .section-title {
    width: 100%;
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    clear: both;
  }
  
  // Media queries for responsive design
  @media screen and (max-width: 768px) {
    :deep(.el-form) {
      .el-form-item__label {
        float: none;
        display: block;
        text-align: left;
        padding: 0 0 8px 0;
        width: 100% !important;
      }
      
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
    
    .field-select {
      width: 100%;
    }
    
    .textarea-container {
      width: 100%;
      
      .json-visual-edit {
        position: static;
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
        margin-bottom: 8px;
      }
    }
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  gap: 12px;
  
  span {
    font-size: 16px;
    font-weight: 600;
  }
}

.visualizer-container {
  height: 100%;
  width: 100%;
  
  // 确保loading层覆盖整个容器
  &.el-loading-parent--relative {
    position: relative;
    min-height: 200px;
  }
}
</style> 