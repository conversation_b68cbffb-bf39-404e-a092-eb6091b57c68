<template>
  <div class="create-task">
    <p class="section-desc">DTS平台创建数据订阅任务</p>
    <div class="task-instruction">
      <p class="instruction-text">请前往DTS平台创建数据订阅任务，完成后点击下方按钮确认。</p>
      <div class="task-info-box">
        <div class="dts-links">
          <div class="link-item">
            <span class="link-label">DTS 线上：</span>
            <a href="https://dts.mws.sankuai.com/subscription" target="_blank" class="dts-link">https://dts.mws.sankuai.com/</a>
          </div>
          <div class="link-item">
            <span class="link-label">DTS 线下：</span>
            <a href="https://dts.mws-test.sankuai.com/subscription" target="_blank" class="dts-link">https://dts.mws-test.sankuai.com/</a>
          </div>
        </div>
        
        <div class="naming-guide">
          <h4 class="guide-title">DTS任务名称格式说明：</h4>
          <ul class="guide-list">
            <li><span class="step-num">1</span> <strong>AggreSync</strong> &nbsp;是前缀；</li>
            <li><span class="step-num">2</span> <strong>sg</strong> &nbsp;是来源，表示闪购，还有外卖（ <strong>wm</strong>）、海葵（<strong>hk</strong>）；</li>
            <li><span class="step-num">3</span> 表名，如有来源开头 hk、wm，可以去掉；</li>
            <li><span class="step-num">4</span> 同步场景。</li>
          </ul>
          <div class="examples">
            <span class="example-label">示例：</span>
            <div class="example-items">
              <code class="example-code">AggreSync_sg_businesspartitionpoi_100001</code>
              <code class="example-code">AggreSync_hk_bm_customer_outer_org_poi_rel_10011</code>
            </div>
          </div>
        </div>
        
        <!-- 数据订阅创建指南折叠区 -->
        <el-collapse v-model="activeCollapse" class="guide-collapse">
          <el-collapse-item name="guide">
            <template #title>
              <div class="collapse-header">
                <h4 class="guide-title">数据订阅创建指南</h4>
                <span class="collapse-action">{{ activeCollapse.includes('guide') ? '收起' : '展开' }}</span>
              </div>
            </template>
            <div class="subscription-guide">
              <h4>创建DTS订阅步骤：</h4>
              <ol class="guide-steps">
                <li>登录DTS平台，选择"数据订阅"功能</li>
                <li>点击"创建订阅任务"按钮</li>
                <li>填写订阅基本信息：
                  <ul>
                    <li>订阅名称：按照上述命名规范填写</li>
                    <li>订阅配置：参考下方图片，源数据库集群、数据库和表选择本次接入对应的集群、数据库和表</li>
                    <li>计费单元：com.sankuai.deliverybusiness.poi.sync</li>
                  </ul>
                </li>
                <li>确认信息并提交</li>
                <li>等待订阅创建成功，获取订阅链接</li>
              </ol>

              <h4>订阅配置参考：</h4>
              <img src="../../../../../../assets/images/dts-1.png" width="1000" alt="dts-task-access-guide-form-1">

              <h4>全量数据同步：</h4>
              <ol class="guide-steps">
                <li><b>「接入完成后」</b>，确定增量数据同步没有问题，可以开始全量数据同步</li>
                <li>点击数据订阅任务，进入任务详情页，点击"全量数据同步"</li>
                <li>在如图所示的界面中，点击新增任务：
                  <ul>
                    <li>导出数据表配置：点击新增，选择对应的数据表</li>
                    <li>如<b>全量数据同步示例图</b>所示，可根据 where 条件，实现灰度同步，例如：valid=1 and id%100&lt;10 </li>
                  </ul>
                </li>
              </ol>
              <p><b>新增任务示例图：</b></p>
              <img src="../../../../../../assets/images/dts-2.png" width="1000" alt="dts-task-access-guide-form-1">

              <p><b>全量数据同步示例图：</b></p>
              <img src="../../../../../../assets/images/dts-3.png" width="1000" alt="dts-task-access-guide-form-1">
              
              <div class="tips">
                <h4>注意事项：</h4>
                <ul>
                  <li>请确保所选数据源有访问权限；</li>
                  <li>创建成功后复制订阅链接，填入下方输入框；</li>
                  <li><b>注意：全量数据同步在接入完成后进行。</b></li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    
    <!-- DTS订阅链接输入框 -->
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top" class="subscription-form">
      <el-form-item label="DTS数据订阅链接" prop="subscriptionUrl">
        <el-input 
          v-model="form.subscriptionUrl" 
          placeholder="请输入DTS数据订阅链接" 
          clearable
          style="width: 500px"
          :disabled="hasCompletedFinalStep"
          @input="onSubscriptionUrlChange"
        />
      </el-form-item>
    </el-form>
    
    <div class="task-action">
      <el-button 
        type="primary" 
        @click="completeTask" 
        :disabled="isButtonDisabled"
      >
        {{ hasCompletedFinalStep ? '已完成创建' : '确认创建完成' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, defineExpose, ref, computed, watch, onMounted, nextTick } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

const props = defineProps({
  hasCompletedFinalStep: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits<{
  (e: 'valid-change', valid: boolean): void;
  (e: 'complete-task'): void;
  (e: 'subscription-url-update', url: string): void;
}>();

// 折叠区状态
const activeCollapse = ref<string[]>([]);

// 表单引用和状态
const formRef = ref<FormInstance>();
const form = ref({
  subscriptionUrl: ''
});

// 表单验证规则
const rules: FormRules = {
  subscriptionUrl: [
    { 
      validator: (rule, value, callback) => {
        // 空值直接跳过验证
        if (!value || !value.trim()) {
          callback();
          return;
        }
        
        // 验证URL格式
        if (!/^https?:\/\/.+/i.test(value)) {
          callback(new Error('请输入有效的URL，必须以http://或https://开头'));
          return;
        }
        
        callback();
      },
      trigger: 'blur' 
    }
  ]
};

// 表单是否有效
const isFormValid = ref(false);
// 是否已经初始化过（用于控制初始不触发验证）
const isInitialized = ref(false);

// 监听表单值变化
watch(() => form.value.subscriptionUrl, (newValue) => {
  // 只有在组件已初始化后才触发验证
  if (isInitialized.value) {
    validateForm();
  }
});

// 校验表单
const validateForm = async () => {
  if (!formRef.value) return false;
  
  // 如果链接为空，直接返回无效
  if (!form.value.subscriptionUrl.trim()) {
    isFormValid.value = false;
    emit('valid-change', false);
    return false;
  }
  
  try {
    await formRef.value.validate();
    isFormValid.value = true;
    emit('valid-change', props.hasCompletedFinalStep);
    return true;
  } catch (error) {
    isFormValid.value = false;
    emit('valid-change', false);
    return false;
  }
};

// 完成任务
const completeTask = async () => {
  if (!isInitialized.value) {
    // 设置初始化标记，强制进行首次验证
    isInitialized.value = true;
  }
  
  const valid = await validateForm();
  if (valid) {
    // 发送订阅链接更新事件
    emit('subscription-url-update', form.value.subscriptionUrl);
    emit('complete-task');
    emit('valid-change', true);
  }
};

// 验证任务
const validateTaskForm = async () => {
  const valid = await validateForm();
  emit('valid-change', valid && props.hasCompletedFinalStep);
  return valid && props.hasCompletedFinalStep;
};

// 主动触发表单验证并更新valid状态
const activeValidate = async () => {
  const valid = await validateForm();
  emit('valid-change', valid && props.hasCompletedFinalStep);
  return valid && props.hasCompletedFinalStep;
};

// 处理订阅链接变化
const onSubscriptionUrlChange = () => {
  // 如果内容为空，不进行校验
  if (!form.value.subscriptionUrl.trim()) {
    isFormValid.value = false;
    emit('valid-change', false);
    return;
  }
  
  // 第一次输入时设置初始化标记
  isInitialized.value = true;
  validateForm();
};

// 组件挂载后
onMounted(() => {
  // 初始时不触发校验
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
});

// 按钮是否禁用
const isButtonDisabled = computed(() => {
  if (props.hasCompletedFinalStep) {
    return true; // 已完成则禁用
  }
  
  // 链接为空时禁用按钮
  if (!form.value.subscriptionUrl.trim()) {
    return true;
  }
  
  if (!isInitialized.value) {
    // 初始状态下，只有在有有效输入时才启用
    return false;
  } else {
    // 初始化后，根据表单验证结果禁用
    return !isFormValid.value;
  }
});

// 设置订阅URL的方法
const setSubscriptionUrl = (url: string) => {
  if (url && typeof url === 'string') {
    // 更新subscriptionUrl
    if (typeof form.value.subscriptionUrl !== 'undefined') {
      form.value.subscriptionUrl = url;
    } 
    // 或者更新formData中的URL字段
    else if (form.value && form.value.subscriptionUrl) {
      form.value.subscriptionUrl = url;
    }
    
    // 如果需要，在这里更新其他相关状态
    // 例如，如果URL不为空，可能表示任务已完成
    if (url) {
      // 通知父组件状态改变
      emit('valid-change', true);
    }
  }
};

// 对外暴露方法
defineExpose({
  validateTaskForm,
  activeValidate,
  form,
  setSubscriptionUrl
});
</script>

<style scoped lang="scss">
.create-task {
  .section-desc {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
  }
  
  .task-instruction {
    margin-bottom: 24px;
    
    .instruction-text {
      font-size: 14px;
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
    }
    
    .task-info-box {
      padding: 20px;
      background-color: var(--el-fill-color-lighter);
      border-radius: 8px;
      border-left: 4px solid var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      
      .dts-links {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px dashed var(--el-border-color-lighter);
        
        .link-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          .link-label {
            font-weight: 500;
            color: var(--el-text-color-primary);
            min-width: 80px;
          }
          
          .dts-link {
            color: var(--el-color-primary);
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
              color: var(--el-color-primary-dark-2);
            }
          }
        }
      }
      
      .naming-guide {
        margin-bottom: 16px;
        
        .guide-title {
          font-size: 14px;
          font-weight: 600;
          margin-top: 0;
          margin-bottom: 12px;
          color: var(--el-text-color-primary);
        }
        
        .guide-list {
          padding-left: 0;
          margin: 0 0 16px 0;
          list-style-type: none;
          
          li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--el-text-color-regular);
            
            .step-num {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 20px;
              height: 20px;
              margin-right: 8px;
              background-color: var(--el-color-primary);
              color: white;
              font-size: 12px;
              font-weight: bold;
              border-radius: 50%;
            }
            
            strong {
              font-weight: 600;
              color: var(--el-color-danger);
            }
          }
        }
        
        .examples {
          background-color: transparent;
          border-radius: 6px;
          padding: 8px 0;
          
          .example-label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--el-text-color-primary);
          }
          
          .example-items {
            display: flex;
            flex-direction: column;
            gap: 8px;
            
            .example-code {
              display: inline-block;
              font-family: monospace;
              color: var(--el-color-primary);
              background-color: rgba(var(--el-color-primary-rgb), 0.1);
              padding: 4px 8px;
              border-radius: 4px;
              word-break: break-all;
              font-size: 13px;
              max-width: fit-content;
            }
          }
        }
      }
      
      .guide-collapse {
        border-top: 1px dashed var(--el-border-color-lighter);
        padding-top: 16px;
        
        .collapse-header {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .guide-title {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
            color: var(--el-text-color-primary);
          }
          
          .collapse-action {
            font-size: 14px;
            color: var(--el-color-primary);
            font-weight: 400;
          }
        }
        
        :deep(.el-collapse-item__header) {
          background-color: var(--el-fill-color-light);
          border: 1px solid var(--el-border-color);
          border-radius: 4px;
          padding: 12px 16px;
          margin-bottom: 0;
          
          .el-collapse-item__arrow {
            display: none;
          }
          
          &:hover {
            background-color: var(--el-fill-color);
          }
        }
        
        :deep(.el-collapse-item__content) {
          padding: 16px;
          background-color: var(--el-fill-color-lighter);
          border: 1px solid var(--el-border-color);
          border-top: none;
          border-bottom-left-radius: 4px;
          border-bottom-right-radius: 4px;
        }
        
        .subscription-guide {
          h4 {
            font-size: 14px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 12px;
            color: var(--el-text-color-primary);
            
            &:not(:first-child) {
              margin-top: 16px;
            }
          }
          
          .guide-steps {
            padding-left: 20px;
            margin: 0 0 16px 0;
            
            li {
              margin-bottom: 10px;
              font-size: 14px;
              color: var(--el-text-color-regular);
              position: relative;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              ul {
                padding-left: 20px;
                margin: 8px 0;
                
                li {
                  margin-bottom: 4px;
                  
                  &:before {
                    content: "•";
                    position: absolute;
                    left: -12px;
                    color: var(--el-color-primary);
                  }
                }
              }
            }
          }
          
          .tips {
            background-color: rgba(var(--el-color-warning-rgb), 0.1);
            border-radius: 4px;
            padding: 12px 16px;
            border-left: 3px solid var(--el-color-warning);
            
            h4 {
              color: var(--el-color-warning-dark-2);
              margin-top: 0;
              margin-bottom: 8px;
            }
            
            ul {
              padding-left: 20px;
              margin: 0;
              
              li {
                color: var(--el-color-warning-dark-2);
                font-size: 13px;
                margin-bottom: 6px;
                
                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .subscription-form {
    margin-bottom: 16px;
    
    :deep(.el-form-item__label) {
      font-weight: 500;
    }
  }
  
  .task-action {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}
</style> 