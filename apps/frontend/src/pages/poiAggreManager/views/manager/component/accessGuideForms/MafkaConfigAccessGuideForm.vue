<template>
  <div class="mafka-config-form">
    <el-alert
      type="warning"
      show-icon
      :closable="false"
      class="guide-alert"
    >
      <span>
        此步骤配置的是<b>「Mafka消息队列」</b>的相关基本信息，用于建立与Mafka的连接。<br>
        1、Mafka  主题由「数据源提供方」申请；<br>
        2、Mafka消费组由「数据接入方」申请；<br>
        3、过滤脚本执行结果为true的消息才会消费，否则会直接丢弃。<br>
        请确保所填写的信息正确，避免连接失败或消费异常情况。
      </span>
    </el-alert>

    <h3 class="section-title">请前往 Mafka 申请消费组</h3>
    <div class="link-container">
      <div class="link-item">
        <span class="link-label">Mafka 线上</span>
        <el-link href="https://mafka.mws.sankuai.com/" target="_blank" type="primary">https://mafka.mws.sankuai.com/</el-link>
      </div>
      <div class="link-item">
        <span class="link-label">Mafka 线下</span>
        <el-link href="https://mafka.mws-test.sankuai.com/" target="_blank" type="primary">https://mafka.mws-test.sankuai.com/</el-link>
      </div>
    </div>
    
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="rules" 
      label-position="right"
      label-width="160px"
      @submit.prevent
    >
      <h3 class="section-title">Mafka 消费组</h3>
      <el-form-item label="主题名称" prop="topic" required>
        <el-input 
          v-model="formData.topic" 
          placeholder="请输入Topic"
          clearable
          style="width: 400px"
          @change="onFormChange"
        />
      </el-form-item>

      <el-form-item label="消费组名称" prop="group" required>
        <el-input 
          v-model="formData.group" 
          placeholder="请输入Group"
          clearable
          style="width: 400px"
          @change="onFormChange"
        />
      </el-form-item>

      <el-form-item label="消费组 Appkey" prop="appKey" required>
        <el-input 
          v-model="formData.appKey" 
          placeholder="请输入AppKey"
          clearable
          style="width: 400px"
          @change="onFormChange"
        />
      </el-form-item>

      <el-form-item label="Namespace" prop="namespace" required>
        <el-select
          v-model="formData.namespace"
          placeholder="请选择或输入Namespace"
          clearable
          filterable
          allow-create
          default-first-option
          style="width: 400px"
          @change="onFormChange"
        >
          <el-option
            v-for="item in namespaceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <h3 class="section-title">消息配置</h3>
      <el-form-item label="过滤脚本" prop="filterScript" class="filter-script-item">
        <div class="textarea-container">
          <el-input 
            v-model="formData.filterScript" 
            type="textarea"
            :rows="3"
            :placeholder="filterScriptPlaceholder"
            clearable
            style="width: 600px"
            @change="onFormChange"
          />
        </div>
      </el-form-item>

    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits, defineExpose, nextTick, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';

// 定义namespace选项
const namespaceOptions = [
  { label: 'com.sankuai.mafka.castle.daojialvyue', value: 'com.sankuai.mafka.castle.daojialvyue' },
  { label: 'waimai', value: 'waimai' }
];

interface MafkaConfigData {
  namespace: string;
  group: string;
  topic: string;
  appKey: string;
  filterScript: string;
}

// 定义emit事件
const emit = defineEmits<{
  (e: 'valid-change', valid: boolean): void;
  (e: 'mafka-config-update', data: MafkaConfigData): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<MafkaConfigData>({
  namespace: '',
  group: '',
  topic: '',
  appKey: 'com.sankuai.deliverybusiness.poi.sync',
  filterScript: ''
});

const filterScriptPlaceholder = '请输入过滤脚本，过滤脚本执行结果为true的消息才会消费，否则会直接丢弃。' +
                                '\n示例：data.dimension == 1 && data.topic == 6，其中data为消息体，dimension和topic为消息体的字段';

// 表单验证规则
const rules = reactive<FormRules>({
  namespace: [
    { required: true, message: '请输入Namespace', trigger: 'blur' },
    { min: 2, max: 50, message: 'Namespace长度在2到50个字符之间', trigger: 'blur' }
  ],
  group: [
    { required: true, message: '请输入Group名称', trigger: 'blur' },
    { min: 2, max: 50, message: 'Group长度在2到50个字符之间', trigger: 'blur' }
  ],
  topic: [
    { required: true, message: '请输入Topic名称', trigger: 'blur' },
    { min: 2, max: 50, message: 'Topic长度在2到50个字符之间', trigger: 'blur' }
  ],
  appKey: [
    { required: true, message: '请输入AppKey', trigger: 'blur' },
    { min: 2, max: 50, message: 'AppKey长度在2到50个字符之间', trigger: 'blur' }
  ],
  filterScript: [
    { max: 2000, message: '过滤脚本长度不能超过2000个字符', trigger: 'blur' }
  ]
});

// 验证表单
const validateForm = async (silent: boolean = false) => {
  if (!formRef.value) return false;
  
  try {
    await formRef.value.validate();
    emit('valid-change', true);
    emit('mafka-config-update', formData);
    return true;
  } catch (error) {
    if (!silent) {
      ElMessage.warning('表单验证未通过，请检查填写的信息');
    }
    emit('valid-change', false);
    return false;
  }
};

// 基于内容的验证方法（类似SyncConfigAccessGuideForm中的实现）
const validateFormContent = (): boolean => {
  // 检查必填字段
  if (!formData.namespace || !formData.group || !formData.topic || !formData.appKey) {
    return false;
  }
  
  // 检查字段长度
  if (
    formData.namespace.length < 3 || formData.namespace.length > 50 ||
    formData.group.length < 3 || formData.group.length > 50 ||
    formData.topic.length < 3 || formData.topic.length > 50 ||
    formData.appKey.length < 3 || formData.appKey.length > 50 ||
    (formData.filterScript && formData.filterScript.length > 2000)
  ) {
    return false;
  }
  
  return true;
};

// 主动触发验证（供父组件调用）
const activeValidate = async () => {
  // 先使用内容验证简单检查
  const contentValid = validateFormContent();
  if (!contentValid) {
    emit('valid-change', false);
    return false;
  }
  
  // 然后使用正式验证完整检查
  return await validateForm(true);
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  
  // 重置表单数据
  Object.assign(formData, {
    namespace: '',
    group: '',
    topic: '',
    appKey: 'com.sankuai.deliverybusiness.poi.sync',
    filterScript: ''
  });
  
  // 通知父组件表单状态变化
  emit('valid-change', false);
};

// 设置表单数据的方法
const setFormData = (data: any) => {
  if (data && typeof data === 'object') {
    // 使用Object.assign更新formData，确保不会覆盖现有属性
    Object.assign(formData, data);
    
    // 如果表单引用存在，重新验证
    if (formRef && formRef.value) {
      setTimeout(() => {
        formRef.value?.validate();
      }, 100);
    }
  }
};

// 获取表单数据
const getFormData = (): MafkaConfigData => {
  return { ...formData };
};

// 监听表单变化
const onFormChange = () => {
//   validateForm(true);
};

// 监听表单数据变化，触发验证和更新事件
watch(
  () => formData,
  () => {
    // 验证表单
    const isValid = validateFormContent();
    emit('valid-change', isValid);
    
    // 向父组件发送数据更新
    emit('mafka-config-update', { ...formData });
  },
  { deep: true }
);

// 组件挂载后初始化
onMounted(() => {
  // 初始状态下表单应该是无效的
  emit('valid-change', false);
});

// 对外暴露方法
defineExpose({
  activeValidate,
  resetForm,
  setFormData,
  getFormData
});
</script>

<style scoped lang="scss">
.mafka-config-form {
  margin-bottom: 20px;
  
  .guide-alert {
    margin-bottom: 20px;
    
    :deep(.el-alert__content) {
      padding: 0 8px;
    }

    :deep(.el-alert__icon) {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 1.5;
    }
  }
  
  .link-container {
    padding: 0 0 16px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 20px;
    
    .link-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      max-width: 950px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .link-label {
        width: 160px;
        font-size: 14px;
        color: #303133;
        font-weight: 500;
        text-align: right;
        padding-right: 20px;
      }
      
      :deep(.el-link) {
        font-size: 14px;
      }
    }
  }
  
  :deep(.el-form) {
    max-width: 950px;
    
    .el-form-item {
      margin-bottom: 22px;
      position: relative;
      
      .el-form-item__label {
        padding-right: 20px;
        font-weight: 500;
      }
      
      .el-form-item__content {
        display: flex;
        flex-wrap: wrap;
      }
    }
    
    // 过滤脚本表单项特殊处理
    .filter-script-item {
      .el-form-item__label {
        align-self: flex-start;
        padding-top: 8px;
      }
    }
  }
  
  // 文本域容器样式
  .textarea-container {
    position: relative;
    width: 600px;
    
    .el-textarea {
      width: 100%;
    }
  }
  
  .section-title {
    width: 100%;
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    clear: both;
  }
  
  .section-desc {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-bottom: 20px;
  }
  
  .form-item-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 5px;
    line-height: 1.4;
  }
  
  // Media queries for responsive design
  @media screen and (max-width: 768px) {
    :deep(.el-form) {
      .el-form-item__label {
        float: none;
        display: block;
        text-align: left;
        padding: 0 0 8px 0;
        width: 100% !important;
      }
      
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
    
    .textarea-container {
      width: 100%;
    }
    
    .link-container {
      .link-item {
        .link-label {
          width: auto;
          text-align: left;
          margin-right: 10px;
        }
      }
    }
  }
}
</style> 