<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="scene-form">
    <el-form-item label="场景名称" prop="sceneName">
      <el-input v-model="form.sceneName" placeholder="请输入场景名称" />
    </el-form-item>
    <el-form-item label="场景描述" prop="sceneDescription">
      <el-input v-model="form.sceneDescription" type="textarea" :rows="3" placeholder="请输入场景描述" />
    </el-form-item>
    <el-form-item label="场景编码">
      <el-input placeholder="无需填写，由系统自动生成" disabled />
    </el-form-item>
    <el-form-item label="接入服务配置" prop="serviceConfigs" required>
      <div class="service-configs-container">
        <div v-for="(appkey, index) in appkeys" :key="index" class="service-config-item">
          <div class="service-inputs">
            <div class="service-input-row">
              <el-form-item :label="`Appkey #${index + 1}`">
                <el-input v-model="appkeys[index]" placeholder="请输入接入服务Appkey" class="service-input" />
              </el-form-item>
              <div class="service-config-actions">
                <el-button v-if="index !== 0" type="danger" circle @click="removeServiceConfig(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
                <el-button v-if="index === appkeys.length - 1" type="primary" circle @click="addServiceConfig">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form-item>
    <el-form-item label="服务类型" prop="appType">
      <el-select v-model="form.appType" placeholder="请选择服务类型">
        <el-option v-for="item in APP_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="场景级别" prop="sceneLevel">
      <el-select v-model="form.sceneLevel" placeholder="请选择场景级别">
        <el-option v-for="item in SCENE_LEVEL_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="依赖类型" prop="dependencyType">
      <el-select v-model="form.dependencyType" placeholder="请选择依赖类型">
        <el-option v-for="item in SCENE_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="管理员" prop="administrator">
      <el-input v-model="form.administrator" placeholder="请输入管理员Mis（多个管理员用英文 , 分隔，例：mis1,mis2）" />
      <!-- <div class="admin-configs-container">
        <div v-for="(admin, index) in administrators" :key="index" class="admin-config-item">
          <div class="admin-config-header">
            <span class="admin-config-index">管理员 #{{ index + 1 }}</span>
            <el-button 
              type="danger" 
              link
              @click="removeAdministrator(index)"
              v-if="index !== 0"
            >
              删除
            </el-button>
          </div>
          <div class="admin-inputs">
            <el-form-item label="MIS账号" label-width="80px">
              <div class="input-wrapper full-width">
                <el-input
                  v-model="administrators[index]"
                  placeholder="请输入管理员MIS"
                />
              </div>
            </el-form-item>
          </div>
        </div>
        <div class="add-admin-btn">
          <el-button type="primary" class="add-config-btn" @click="addAdministrator">
            <el-icon><Plus /></el-icon>添加管理员
          </el-button>
        </div>
      </div> -->
    </el-form-item>
    <el-form-item label="使用字段Codes" prop="fieldCodes">
      <el-select 
        v-model="form.fieldCodes" 
        multiple 
        filterable 
        placeholder="请选择使用字段Code"
        @focus="fetchFieldList"
      >
        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="PRD/技术方案" prop="prdWikis">
      <el-input v-model="form.prdWikis" placeholder="请输入PRD/技术方案Wiki链接" />
    </el-form-item>
    <el-form-item label="高峰期" prop="peakPeriod">
      <el-input v-model="form.peakPeriod" placeholder="请输入高峰期" />
    </el-form-item>
    <el-form-item label="预估QPS" prop="estimateQps">
      <el-input-number v-model="form.estimateQps" :min="0" placeholder="请输入预估QPS" />
    </el-form-item>
    <el-form-item label="实际QPS" prop="actualQps" v-if="props.initialData?.id">
      <el-input-number v-model="form.actualQps" :min="0" placeholder="请输入实际QPS" />
    </el-form-item>
    <!-- <el-form-item label="状态" prop="valid">
      <el-radio-group v-model="form.valid">
        <el-radio :label="1">有效</el-radio>
        <el-radio :label="0">无效</el-radio>
      </el-radio-group>
    </el-form-item> -->
    <el-form-item label="备注" prop="remark">
      <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import type { SceneItem } from '../../../types'
import { SCENE_TYPE_OPTIONS, SCENE_LEVEL_OPTIONS, APP_TYPE_OPTIONS } from '../../../types'
import { Plus, Delete } from '@element-plus/icons-vue'
import { getFieldMetadataList } from '../../../request'
import { ElMessage } from 'element-plus'

const props = defineProps<{
  initialData?: Partial<SceneItem>
}>()

const emit = defineEmits<{
  (e: 'submit', data: Partial<SceneItem> | null): void
}>()

const formRef = ref<FormInstance>()

// 服务配置列表改为只存储 appkey
const appkeys = ref<string[]>([''])

// 管理员列表
const administrators = ref<string[]>([''])

// 添加管理员
const addAdministrator = () => {
  administrators.value.push('')
}

// 删除管理员
const removeAdministrator = (index: number) => {
  administrators.value.splice(index, 1)
}

// 修改表单数据结构
interface FormState extends Omit<Partial<SceneItem>, 'administrator' | 'prdWikis'> {
  administrator: string;
  prdWikis: string;
  appType: number | undefined;
}

// 表单数据
const form = reactive<FormState>({
  sceneName: '',
  sceneDescription: '',
  sceneLevel: undefined,
  appType: undefined,
  dependencyType: undefined,
  fieldCodes: [],
  prdWikis: '',
  peakPeriod: '',
  estimateQps: 0,
  actualQps: 0,
  valid: 1,
  remark: '',
  administrator: '',
  appkeys: []
})

// 计算最终的 appkeys
const computedAppConfigs = computed(() => {
  return {
    appkeys: form.appkeys || [],
    appType: form.appType
  }
})

// 添加一个标志，表示表单是否处于初始状态
const isFormInitializing = ref(true)

// 表单校验规则
const rules = {
  sceneName: [{ required: true, message: '请输入场景名称', trigger: 'blur' }],
  sceneDescription: [{ required: true, message: '请输入场景描述', trigger: 'blur' }],
  sceneLevel: [{ 
    required: true, 
    message: '请选择场景级别', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      // 如果表单正在初始化，则不校验
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (value === undefined || value === '') {
        callback(new Error('请选择场景级别'))
        return
      }
      callback()
    }
  }],
  dependencyType: [{ 
    required: true, 
    message: '请选择依赖类型', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      // 如果表单正在初始化，则不校验
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (value === undefined || value === '') {
        callback(new Error('请选择依赖类型'))
        return
      }
      callback()
    }
  }],
  administrator: [{ 
    required: true, 
    message: '请选择管理员', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      // 如果表单正在初始化，则不校验
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (!value || value.trim() === '') {
        callback(new Error('请选择管理员'))
        return
      }
      callback()
    }
  }],
  fieldCodes: [{ 
    required: true, 
    message: '请输入或选择使用字段', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      // 如果表单正在初始化，则不校验
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (!value || value.length === 0) {
        callback(new Error('请输入或选择使用字段'))
        return
      }
      callback()
    }
  }],
  peakPeriod: [{ 
    required: true, 
    message: '请输入高峰期', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      // 如果表单正在初始化，则不校验
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (!value || value.trim() === '') {
        callback(new Error('请输入高峰期'))
        return
      }
      callback()
    }
  }],
  estimateQps: [{ 
    required: true, 
    message: '请输入预估QPS', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      // 如果表单正在初始化，则不校验
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (value === undefined || value === null) {
        callback(new Error('请输入预估QPS'))
        return
      }
      callback()
    }
  }],
  actualQps: [{ 
    required: true, 
    message: '请输入实际QPS', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      // 如果表单正在初始化，则不校验
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (value === undefined || value === null) {
        callback(new Error('请输入实际QPS'))
        return
      }
      callback()
    }
  }],
  valid: [{ required: true, message: '请选择状态', trigger: 'blur' }],
  appType: [{ 
    required: true, 
    message: '请选择服务类型', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (value === undefined || value === '') {
        callback(new Error('请选择服务类型'))
        return
      }
      callback()
    }
  }],
  serviceConfigs: [{
    required: true,
    message: '请至少添加一个服务配置',
    validator: (rule: any, value: any, callback: any) => {
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      const configs = appkeys.value
      if (!configs || configs.length === 0) {
        callback(new Error('请至少添加一个服务配置'))
        return
      }

      // 检查是否存在至少一个有效的配置
      const validConfigs = configs.filter(appkey => appkey.trim() !== '')
      if (validConfigs.length === 0) {
        callback(new Error('请至少添加一个有效的服务配置'))
        return
      }
      callback()
    },
    trigger: 'blur'
  }]
}

// 添加服务配置
const addServiceConfig = () => {
  appkeys.value.push('')
}

// 删除服务配置
const removeServiceConfig = (index: number) => {
  appkeys.value.splice(index, 1)
}

// 初始化表单数据
const initializeServiceConfigs = () => {
  if (props.initialData?.appkeys?.length) {
    appkeys.value = [...props.initialData.appkeys]
  } else {
    appkeys.value = ['']
  }
  
  // 设置服务类型
  form.appType = props.initialData?.appType
  
  console.log('服务配置已初始化:', { appkeys: appkeys.value, appType: form.appType })
}

// 监听初始数据变化
watch(
  () => props.initialData,
  (newVal) => {
    if (newVal) {
      console.log('初始化表单数据:', newVal)
      
      // 初始化表单数据
      form.sceneName = newVal.sceneName || ''
      form.sceneDescription = newVal.sceneDescription || ''
      form.sceneLevel = newVal.sceneLevel
      form.appType = newVal.appType
      form.dependencyType = newVal.dependencyType
      form.fieldCodes = newVal.fieldCodes || []
      form.prdWikis = Array.isArray(newVal.prdWikis) 
        ? newVal.prdWikis.join(',')
        : newVal.prdWikis || ''
      form.peakPeriod = newVal.peakPeriod || ''
      form.estimateQps = newVal.estimateQps ?? 0
      form.actualQps = newVal.actualQps ?? 0
      form.valid = newVal.valid ?? 1
      form.remark = newVal.remark || ''
      form.administrator = Array.isArray(newVal.administrator)
        ? newVal.administrator.join(',')
        : newVal.administrator || ''
      
      // 初始化服务配置
      appkeys.value = newVal.appkeys || ['']
      
      console.log('表单数据初始化完成:', form)
    } else {
      // 重置表单数据
      form.sceneName = ''
      form.sceneDescription = ''
      form.sceneLevel = undefined
      form.appType = undefined
      form.dependencyType = undefined
      form.fieldCodes = []
      form.prdWikis = ''
      form.peakPeriod = ''
      form.estimateQps = 0
      form.actualQps = 0
      form.valid = 1
      form.remark = ''
      form.administrator = ''
      form.appkeys = []
      
      // 重置服务配置
      appkeys.value = ['']
      
      console.log('表单数据已重置')
    }
    
    // 重置表单校验状态
    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate()
        console.log('表单校验状态已重置')
      }
    })
  },
  { deep: true, immediate: true }
)

// 字段选项列表
const fieldOptions = ref<Array<{ label: string; value: string }>>([])

// 获取字段列表
const fetchFieldList = async () => {
  try {
    const res = await getFieldMetadataList({
      pageNo: 1,
      pageSize: 1000,
      valid: 1
    })

    if (res.code === 0 && res.data?.records) {
      fieldOptions.value = res.data.records
        .map(item => ({
          label: item.fieldCode,
          value: item.fieldCode
        }))
    }
  } catch (error) {
    console.error('获取字段列表失败:', error)
  }
}

// 组件挂载时获取字段列表
onMounted(() => {
  fetchFieldList()
})

// 提交表单
const submitForm = async () => {
  // 确保表单不在初始化状态
  isFormInitializing.value = false;
  
  // 1. 验证表单
  if (!formRef.value) return

  try {
    // 先清除之前的校验结果
    await formRef.value.clearValidate()
    
    // 进行表单校验
    const valid = await formRef.value.validate().catch(errors => {
      console.error('表单校验失败:', errors)
      return false
    })
    
    if (!valid) {
      console.error('表单校验未通过')
      return
    }
    
    // 获取有效的服务配置
    const validServiceAppkeys: string[] = appkeys.value.filter(appkey => appkey.trim() !== '')
    
    if (validServiceAppkeys.length === 0) {
      ElMessage.error('请至少添加一个有效的服务配置')
      return
    }

    // 构建提交数据
    const submitData: Partial<SceneItem> = {
      ...form,
      appkeys: validServiceAppkeys,
      appType: form.appType,
      administrator: form.administrator.split(',').map(item => item.trim()).filter(item => item.length > 0),
      prdWikis: form.prdWikis ? form.prdWikis.split(',').map(item => item.trim()).filter(item => item.length > 0) : [],
      id: props.initialData?.id,
      sceneCode: props.initialData?.sceneCode
    }

    console.log('submitData', submitData)
    emit('submit', submitData)
  } catch (error) {
    console.error('提交表单失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  // 设置表单为初始化状态
  isFormInitializing.value = true;
  
  // 重置表单数据
  Object.keys(form).forEach(key => {
    if (key !== 'valid') {
      (form as any)[key] = '';
    }
  });
  
  form.fieldCodes = [];
  form.estimateQps = 0;
  form.actualQps = 0;
  form.valid = 1;
  
  // 重置服务配置
  appkeys.value = ['']
  
  // 重置表单校验状态
  if (formRef.value) {
    nextTick(() => {
      formRef.value?.clearValidate();
      console.log('表单已完全重置');
      
      // 延迟将表单标记为非初始化状态，确保不会立即触发校验
      setTimeout(() => {
        isFormInitializing.value = false;
        console.log('表单初始化完成，可以开始校验');
      }, 500);
    });
  }
}

defineExpose({
  submitForm,
  resetForm
})
</script>

<style lang="scss" scoped>
.scene-form {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
}

.service-configs-container {
  width: 100%;
  box-sizing: border-box;
}

.service-config-item {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.service-inputs {
  .service-input-row {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    :deep(.el-form-item) {
      margin-bottom: 0;
      flex: 0 0 auto;
    }
  }
}

.service-input {
  width: 400px;
}

.service-type-select {
  width: 120px;
}

.service-config-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: 8px;
}

.admin-configs-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
  background-color: #f8f9fa;
  width: 100%;
  box-sizing: border-box;
}

.admin-config-item {
  padding: 20px;
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 12px;
  }
}

.admin-config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.admin-config-index {
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.admin-inputs {
  :deep(.el-form-item) {
    margin-bottom: 0;

    .el-form-item__content {
      flex: 1;
    }
  }
}

.add-admin-btn {
  margin-top: 16px;
  display: flex;
  justify-content: center;

  .add-config-btn {
    width: 140px;

    .el-icon {
      margin-right: 4px;
    }

    &.el-button--primary {
      color: #fff;

      &:hover,
      &:focus,
      &:active {
        color: #fff;
      }
    }
  }
}
</style>