<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="dsl-form">
    <el-form-item label="DSL名称" prop="dslName">
      <el-input v-model="form.dslName" placeholder="请输入DSL名称" />
    </el-form-item>
    <el-form-item label="DSL描述" prop="dslDescription">
      <el-input v-model="form.dslDescription" type="textarea" :rows="3" placeholder="请输入DSL描述" />
    </el-form-item>
    <el-form-item label="DSL内容" prop="dsl" class="json-form-item">
      <div class="textarea-container">
        <el-input
          v-model="form.dsl"
          type="textarea"
          :rows="5"
          placeholder="请输入DSL内容（JSON格式）"
          :disabled="true"
          @blur="handleJsonInputBlur('dsl')"
        />
        <div class="json-visual-edit">
          <el-button type="primary" text @click="openJsonEditor('dsl')">
            <el-icon class="el-icon--left"><EditPen /></el-icon>
            可视化编辑
          </el-button>
        </div>
      </div>
    </el-form-item>
    <el-form-item label="使用字段" prop="fieldCodes">
      <el-select 
        v-model="form.fieldCodes" 
        multiple 
        filterable 
        :allow-create="true"
        default-first-option
        placeholder="请选择编排字段"
        @focus="fetchFieldList"
      >
        <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="是否抛出异常" prop="thrownException">
      <el-switch
        v-model="form.thrownException"
        active-text="是"
        inactive-text="否"
        :active-value="true"
        :inactive-value="false"
      />
    </el-form-item>
    <el-form-item label="预热参数配置" prop="dslPreHeatParam">
      <el-input v-model="form.dslPreHeatParam" type="textarea" :rows="3" placeholder="请输入预热参数" />
    </el-form-item>
    <el-form-item label="其他配置(JSON)" prop="option" class="json-form-item">
      <div class="textarea-container">
        <el-input
          v-model="form.optionStr"
          type="textarea"
          :rows="3"
          placeholder="请输入JSON格式的配置"
          @blur="handleJsonInputBlur('optionStr')"
        />
        <div class="json-visual-edit">
          <el-button type="primary" text @click="openJsonEditor('optionStr')">
            <el-icon class="el-icon--left"><EditPen /></el-icon>
            可视化编辑
          </el-button>
        </div>
      </div>
    </el-form-item>
    <!-- TODO 后续使用时删除 v-show="false" -->
    <template v-show="false">
      <el-form-item label="支持QPS" prop="supportQps">
        <el-input-number v-model="form.supportQps" :min="0" :step="1" placeholder="请输入支持QPS" style="width: 150px" />
      </el-form-item>
      <el-form-item label="实际QPS" prop="actualQps">
        <el-input-number v-model="form.actualQps" :min="0" :step="1" placeholder="请输入实际QPS" style="width: 150px" />
      </el-form-item>
    </template>
    <el-form-item v-if="!props.initialData?.id" label="Lion配置描述" prop="lionConfigDescription">
      <el-input v-model="form.lionConfigDescription" type="textarea" :rows="3" placeholder="请输入Lion配置描述" />
    </el-form-item>
    <!-- <el-form-item label="状态" prop="valid">
      <el-radio-group v-model="form.valid">
        <el-radio :label="1">有效</el-radio>
        <el-radio :label="0">无效</el-radio>
      </el-radio-group>
    </el-form-item> -->
  </el-form>

  <!-- 使用JsonEditor组件 -->
  <json-editor
    ref="jsonEditorRef"
    v-model="currentJsonValue"
    @save="handleJsonEditorSave"
    @cancel="handleJsonEditorCancel"
  />

  <!-- 添加服务编排可视化抽屉 -->
  <el-drawer
    v-model="showOrchestrationDrawer"
    title="服务编排可视化"
    size="90%"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :before-close="handleDrawerClose"
  >
    <template #header>
      <div class="drawer-header">
        <span>服务编排可视化</span>
        <el-tag v-if="drawerLoading" type="info" effect="plain">加载中...</el-tag>
      </div>
    </template>
    <div v-loading="drawerLoading" class="visualizer-container">
      <OrchestrationVisualizer 
        v-if="visualizerDslKey" 
        :dslKey="visualizerDslKey" 
        @dsl-saved="handleDslSaved"
      />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import type { FormInstance } from 'element-plus'
import { EditPen } from '@element-plus/icons-vue'
import type { DSLConfigItem } from '../../../types'
import { getFieldMetadataList } from '../../../request'
import JsonEditor from '../../../components/common/JsonEditor.vue'
import { ElMessage } from 'element-plus'
import { getVisualDslKey } from '../../../utils/orchestrationUtils'
import OrchestrationVisualizer from '../../../views/tools/orchestration/OrchestrationVisualizer.vue'

const props = defineProps<{
  initialData?: Partial<DSLConfigItem>
}>()

const emit = defineEmits<{
  (e: 'submit', data: Partial<DSLConfigItem> | null): void
  (e: 'formChange', hasChanged: boolean): void
}>()

const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<{
  dslName: string
  dslDescription: string
  dsl: string
  fieldCodes: string[]
  thrownException: boolean
  dslPreHeatParam: string
  valid: number
  lionConfigDescription: string
  optionStr: string
  supportQps: number
  actualQps: number
}>({
  dslName: '',
  dslDescription: '',
  dsl: '',
  fieldCodes: [],
  thrownException: false,
  dslPreHeatParam: '',
  valid: 1,
  lionConfigDescription: '',
  optionStr: '',
  supportQps: 0,
  actualQps: 0
})

// 初始表单数据
const initialFormData = ref<typeof form | null>(null)

// 标志，表示表单是否处于初始状态
const isFormInitializing = ref(true)

// 表单校验规则
const rules = {
  dslName: [{ required: true, message: '请输入编排名称', trigger: 'blur' }],
  dslDescription: [{ required: true, message: '请输入编排描述', trigger: 'blur' }],
  dsl: [{ required: true, message: '请输入编排DSL', trigger: 'blur' }],
  fieldCodes: [{ 
    required: true, 
    message: '请选择编排字段', 
    trigger: 'blur',
    validator: (rule: any, value: any, callback: any) => {
      if (isFormInitializing.value) {
        callback()
        return
      }
      
      if (!value || value.length === 0) {
        callback(new Error('请选择编排字段'))
        return
      }
      callback()
    }
  }],
  thrownException: [{ required: true, message: '请选择是否抛出异常', trigger: 'blur' }],
  dslPreHeatParam: [{ required: true, message: '请输入预热参数配置', trigger: 'blur' }],
  valid: [{ required: true, message: '请选择状态', trigger: 'blur' }],
  lionConfigDescription: [{ required: true, message: '请输入DSL配置描述', trigger: 'blur' }],
  option: [{
    validator: (rule: any, value: any, callback: any) => {
      if (!form.optionStr) return callback()
      try {
        JSON.parse(form.optionStr)
        callback()
      } catch (e) {
        callback(new Error('请输入合法的JSON'))
      }
    },
    trigger: 'blur'
  }],
  supportQps: [{
    type: 'number',
    required: false,
    message: '请输入支持QPS',
    trigger: 'blur'
  }],
  actualQps: [{
    type: 'number',
    required: false,
    message: '请输入实际QPS',
    trigger: 'blur'
  }],
}

// 字段选项列表
const fieldOptions = ref<Array<{ label: string; value: string }>>([])

// 定义默认值常量
const DEFAULT_CONFIGS = {
  optionStr: '{}'
}

// JSON编辑相关
const jsonEditorRef = ref<InstanceType<typeof JsonEditor> | null>(null)
const currentEditingField = ref<string>('')
const currentJsonValue = ref<string>('')

// 编排可视化相关状态
const showOrchestrationDrawer = ref(false)
const visualizerDslKey = ref('')
const drawerLoading = ref(false)

// 获取字段列表
const fetchFieldList = async () => {
  try {
    const res = await getFieldMetadataList({
      pageNo: 1,
      pageSize: 1000,
      valid: 1
    })

    if (res.code === 0 && res.data?.records) {
      fieldOptions.value = res.data.records
        .map(item => ({
          label: item.fieldCode,
          value: item.fieldCode
        }))
    }
  } catch (error) {
    console.error('获取字段列表失败:', error)
  }
}

// 处理JSON输入框失焦事件
const handleJsonInputBlur = (field: string) => {
  // 手动验证当前字段
  nextTick(() => {
    formRef.value?.validateField([field])
  })
}

// 打开JSON编辑器
const openJsonEditor = (field: string) => {
  currentEditingField.value = field
  
  // 特殊处理dsl字段
  if (field === 'dsl') {
    try {
      let fieldValue = form[field];
      if (!fieldValue || fieldValue === '') {
        // 如果DSL为空，设置默认示例
        const exampleDsl = `{
  "name": "exampleDsl",
  "description": "DSL样例（3个节点，简单链式依赖）",
  "timeout": 20,
  "tasks": [
    {
      "alias": "node1",
      "taskType": "Calculate",
      "description": "node1",
      "inputs": {
        "sourcePoiIds": "$params.poiIds"
      }
    },
    {
      "alias": "node2",
      "taskType": "Calculate",
      "description": "node2",
      "inputs": "count($node1.sourcePoiIds)"
    },
    {
      "alias": "node3",
      "taskType": "Calculate",
      "description": "node3",
      "inputs": {
        "size": "$node2"
      }
    }
  ],
  "outputs": {
    "type": "map",
    "transform": "seq.map('total', $node3.size, 'data', $node1)"
  }
}`;
        fieldValue = exampleDsl;
      }
      
      if (typeof fieldValue === 'string') {
        try {
          // 设置loading状态为true
          drawerLoading.value = true;
          
          // 直接使用DSL内容生成可视化key
          visualizerDslKey.value = getVisualDslKey(fieldValue) || '';
          if (visualizerDslKey.value) {
            // 打开抽屉展示组件
            showOrchestrationDrawer.value = true;
            
            // 使用setTimeout模拟异步加载，并在一定时间后关闭loading
            setTimeout(() => {
              drawerLoading.value = false;
            }, 1000); // 设置一个合理的延迟，以确保组件已加载
            
            return;
          } else {
            drawerLoading.value = false;
            ElMessage.error('生成DSL可视化Key失败，请稍后重试');
            // 回退到普通JSON编辑器
            openRegularJsonEditor(field);
          }
        } catch (error) {
          drawerLoading.value = false;
          console.error('解析JSON字符串失败:', error);
          ElMessage.warning('配置格式不正确，将使用普通JSON编辑器');
          // 解析失败时，回退到普通JSON编辑器
          openRegularJsonEditor(field);
        }
      } else {
        // 字段为空或非字符串，提示创建新配置
        ElMessage.info('配置为空，将使用普通JSON编辑器创建配置');
        openRegularJsonEditor(field);
      }
    } catch (error) {
      console.error('处理可视化编辑失败:', error);
      // 出错时，回退到普通JSON编辑器
      openRegularJsonEditor(field);
    }
    return;
  }
  
  // 其他字段使用普通JSON编辑器
  openRegularJsonEditor(field);
}

// 普通JSON编辑器打开方法
const openRegularJsonEditor = (field: string) => {
  try {
    const fieldValue = form[field as keyof typeof form]
    if (fieldValue && typeof fieldValue === 'string') {
      // 处理JSON字符串中的转义字符
      const processedValue = fieldValue.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
        // 替换字符串中的换行符、制表符和其他需要转义的字符
        return match
          .replace(/\n/g, '\\n')
          .replace(/\t/g, '\\t')
      })
      
      // 尝试解析JSON格式，确保格式正确
      JSON.parse(processedValue)
      // 设置当前JSON值为处理后的值
      currentJsonValue.value = processedValue
      // 打开编辑器
      nextTick(() => {
        jsonEditorRef.value?.open()
      })
    } else {
      // 如果字段为空，设置默认值
      const defaultValue = DEFAULT_CONFIGS[field as keyof typeof DEFAULT_CONFIGS] || '{}'
      currentJsonValue.value = defaultValue
      nextTick(() => {
        jsonEditorRef.value?.open()
      })
    }
  } catch (error) {
    // JSON格式错误时，仍然设置值并打开编辑器
    const fieldValue = form[field as keyof typeof form]
    if (fieldValue && typeof fieldValue === 'string') {
      currentJsonValue.value = fieldValue
    } else {
      const defaultValue = DEFAULT_CONFIGS[field as keyof typeof DEFAULT_CONFIGS] || '{}'
      currentJsonValue.value = defaultValue
    }
    nextTick(() => {
      jsonEditorRef.value?.open()
    })
  }
}

// 处理JSON编辑器保存事件
const handleJsonEditorSave = (jsonString: string) => {
  if (currentEditingField.value && form.hasOwnProperty(currentEditingField.value)) {
    try {
      // 保存JSON到表单数据
      ;(form as any)[currentEditingField.value] = jsonString
      
      // 触发表单验证
      handleJsonInputBlur(currentEditingField.value)
    } catch (error) {
      console.error('保存JSON配置失败:', error)
    }
  }
}

// 处理JSON编辑器取消事件
const handleJsonEditorCancel = () => {
  // 重置编辑状态
  currentEditingField.value = ''
}

// 监听表单数据变化
watch(form, (newVal) => {
  if (isFormInitializing.value) return
  
  // 比较当前表单数据和初始数据
  const hasChanged = JSON.stringify(newVal) !== JSON.stringify(initialFormData.value)
  emit('formChange', hasChanged)
}, { deep: true })

// 监听dslDescription变化，自动设置lionConfigDescription
watch(
  () => form.dslDescription,
  (newValue) => {
    if (!newValue || props.initialData?.id) return
    
    // 自动设置lionConfigDescription为"【查询DSL配置】+description"
    form.lionConfigDescription = `【查询DSL配置】${newValue}`
  },
  { deep: true }
)

// 初始化表单数据
const initializeForm = () => {
  if (props.initialData) {
    Object.assign(form, props.initialData)
    // option字段转为字符串
    form.optionStr = props.initialData.option ? JSON.stringify(props.initialData.option, null, 2) : DEFAULT_CONFIGS.optionStr
    form.supportQps = props.initialData.supportQps ?? 0
    form.actualQps = props.initialData.actualQps ?? 0
    initialFormData.value = JSON.parse(JSON.stringify({
      ...props.initialData,
      optionStr: form.optionStr,
      supportQps: form.supportQps,
      actualQps: form.actualQps
    }))
  } else {
    Object.assign(form, {
      dslName: '',
      dslDescription: '',
      dsl: '',
      fieldCodes: [],
      thrownException: false,
      dslPreHeatParam: '',
      valid: 1,
      lionConfigDescription: '',
      optionStr: DEFAULT_CONFIGS.optionStr,
      supportQps: 0,
      actualQps: 0
    })
    initialFormData.value = null
  }
  isFormInitializing.value = false
}

// 重置表单
const resetForm = () => {
  isFormInitializing.value = true
  if (formRef.value) {
    formRef.value.resetFields()
  }
  initializeForm()
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  try {
    await formRef.value.validate()
    const hasChanged = JSON.stringify(form) !== JSON.stringify(initialFormData.value)
    if (!hasChanged) {
      emit('submit', null)
      return
    }
    let optionObj = undefined
    if (form.optionStr) {
      try {
        optionObj = JSON.parse(form.optionStr)
      } catch (e) {
        emit('submit', null)
        return
      }
    }
    emit('submit', {
      ...form,
      option: optionObj,
      supportQps: form.supportQps,
      actualQps: form.actualQps
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 监听初始数据变化
watch(() => props.initialData, () => {
  initializeForm()
}, { immediate: true })

// 组件挂载时获取字段列表
onMounted(() => {
  fetchFieldList()
})

// 添加处理DSL保存事件的方法
const handleDslSaved = (dsl: string) => {
  if (!currentEditingField.value) return;
  
  try {
    // 只处理dsl字段
    if (currentEditingField.value === 'dsl') {
      try {
        // 设置loading状态
        drawerLoading.value = true;
        
        // 直接更新表单数据，不需要像SyncConfigAccessGuideForm那样包装dsl字段
        form.dsl = dsl;
        
        // 延迟关闭抽屉，给用户一些视觉反馈
        setTimeout(() => {
          // 关闭抽屉
          showOrchestrationDrawer.value = false;
          // 关闭loading状态
          drawerLoading.value = false;
          
          // 触发表单验证
          handleJsonInputBlur('dsl');
          
          ElMessage.success('DSL配置已成功更新');
        }, 500);
      } catch (parseError) {
        drawerLoading.value = false;
        console.error('DSL格式解析失败:', parseError);
        ElMessage.error('DSL格式不正确，请检查后重试');
      }
    }
  } catch (error) {
    drawerLoading.value = false;
    console.error('处理DSL保存失败:', error);
    ElMessage.error('处理DSL保存失败，请手动编辑配置');
  }
};

// 处理抽屉关闭前的回调
const handleDrawerClose = (done: () => void) => {
  // 清除loading状态
  drawerLoading.value = false
  // 关闭抽屉
  done()
}

defineExpose({
  submitForm,
  resetForm,
  hasFormChanged: () => {
    // 如果是新增，只要有填写内容就认为有变更
    if (!props.initialData?.id) {
      return true
    }

    // 比较基本字段
    const basicFields: (keyof typeof form)[] = [
      'dslName',
      'dslDescription',
      'dsl',
      'thrownException',
      'dslPreHeatParam',
      'valid',
      'lionConfigDescription'
    ]

    for (const field of basicFields) {
      const formValue = form[field]
      const originalValue = props.initialData[field as keyof typeof props.initialData]
      
      // 对于数字类型的字段，需要进行类型转换后比较
      if (typeof formValue === 'number' || typeof originalValue === 'number') {
        if (Number(formValue) !== Number(originalValue)) {
          console.log(`字段 ${field} 发生变更:`, { formValue, originalValue })
          return true
        }
      } else if (formValue !== originalValue) {
        console.log(`字段 ${field} 发生变更:`, { formValue, originalValue })
        return true
      }
    }

    // 比较字段代码列表
    const currentFields = form.fieldCodes || []
    const originalFields = props.initialData.fieldCodes || []
    if (currentFields.length !== originalFields.length || 
        !currentFields.every(field => originalFields.includes(field))) {
      console.log('字段代码列表发生变更')
      return true
    }

    return false
  }
})
</script>

<style lang="scss" scoped>
.dsl-form {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  max-height: 60vh; // 设置固定高度，减去头部和底部padding
  overflow-y: auto; // 添加垂直滚动条
  overflow-x: hidden; // 隐藏水平滚动条

  // 美化滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }

  // JSON表单项样式
  :deep(.json-form-item) {
    .el-form-item__content {
      position: relative;
    }
  }

  // 文本域容器样式
  .textarea-container {
    position: relative;
    width: 100%;
    
    .el-textarea {
      width: 100%;
    }
    
    // JSON编辑按钮样式
    .json-visual-edit {
      position: absolute;
      top: 4px;
      right: 10px;
      z-index: 2;
      
      .el-button {
        font-size: 14px;
        padding: 4px 8px;
        background-color: rgba(255, 255, 255, 0.8);
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}
</style> 