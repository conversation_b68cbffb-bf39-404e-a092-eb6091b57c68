<template>
  <div class="rpc-access-guide">
    <!-- 步骤导航条 -->
    <el-steps 
      :active="actualStep" 
      finish-status="success" 
      class="rpc-steps"
      process-status="process"
      align-center
    >
      <el-step title="查询功能配置" description="配置是否提供查询及字段" />
      <el-step title="RPC配置信息" description="配置RPC接口信息" />
      <!-- <el-step title="RPC接口测试" description="测试RPC接口" /> -->
    </el-steps>

    <!-- 步骤内容区 -->
    <el-card shadow="never" class="guide-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">RPC-Thrift 接入指引</span>
        </div>
      </template>
      
      <!-- 步骤1：查询功能配置 -->
      <div v-show="actualStep === 0" class="step-content">
        <QueryFieldsAccessGuideForm
          v-model:provide-query-function="provideQueryFunction"
          :query-field-form="queryFieldForm"
          :lock-synced-field="true"
          :default-synced-field="false"
          :show-dependent-fields="true"
          :hide-query-option="true"
          @valid-change="handleValidChange"
          @query-fields-data="handleQueryFieldsDataUpdate"
          @update:provide-query-function="(value: boolean) => emit('provide-query-function-update', value)"
          ref="queryFieldsFormRef"
        />
      </div>
      
      <!-- 步骤2：RPC配置 -->
      <div v-show="actualStep === 1" class="step-content">
        <div class="rpc-config-form">
          <DSLConfigAccessGuideForm
            :dsl-config-form="dslConfigForm"
            :field-select-disabled="true"
            @valid-change="handleValidChange"
            @dsl-config-update="handleDslConfigUpdate"
            ref="dslConfigFormRef"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import QueryFieldsAccessGuideForm from './accessGuideForms/QueryFieldsAccessGuideForm.vue';
import DSLConfigAccessGuideForm from './accessGuideForms/DSLConfigAccessGuideForm.vue';

// 定义props，接收父组件传入的step
const props = defineProps({
  step: {
    type: Number,
    required: true
  }
});

// 定义emit，确保在使用前声明
const emit = defineEmits<{
  (e: 'valid-change', valid: boolean): void;
  (e: 'update:step', step: number): void;
  (e: 'query-metadata-update', data: any[]): void;
  (e: 'provide-query-function-update', provide: boolean): void;
  (e: 'dsl-config-update', data: any): void;
}>();

// 当前组件内部的步骤（0-1对应2个RPC步骤）
const actualStep = ref(props.step - 1);

// 是否提供查询功能
const provideQueryFunction = ref(true);

// 查询字段表单数据
const queryFieldForm = ref({});

// DSL配置表单数据
const dslConfigForm = ref({});

// 保存查询字段数据，用于传递给DSL配置
const queryFieldsData = ref<Array<any>>([]);

// 组件引用
const queryFieldsFormRef = ref();
const dslConfigFormRef = ref();

// 监听父组件传入的step变化
watch(() => props.step, (newStep) => {
  const prevStep = actualStep.value;
  actualStep.value = newStep - 1;
  
  // 如果从查询功能配置(step=0)切换到DSL配置(step=1)
  if (prevStep === 0 && actualStep.value === 1) {
    // 将查询字段的fieldCode设置到DSL配置表单
    if (dslConfigFormRef.value && queryFieldsData.value.length > 0) {
      const fieldCodes = queryFieldsData.value
        .map(item => item.fieldCode)
        .filter(code => code !== undefined && code !== null);
      
      dslConfigFormRef.value.setFieldCodes(fieldCodes, true); // 第二个参数表示禁用编辑
    }
  }
  
  // 步骤切换后，主动验证当前步骤的表单状态
  nextTick(() => {
    validateCurrentStepForm();
  });
});

// 验证当前步骤的表单
const validateCurrentStepForm = async () => {
  let isValid = false;
  
  switch (actualStep.value) {
    case 0: // 查询功能配置
      if (queryFieldsFormRef.value && queryFieldsFormRef.value.activeValidate) {
        isValid = await queryFieldsFormRef.value.activeValidate();
      }
      break;
    case 1: // RPC配置
      if (dslConfigFormRef.value && dslConfigFormRef.value.activeValidate) {
        isValid = await dslConfigFormRef.value.activeValidate();
      } else {
        isValid = false;
      }
      break;
    default:
      isValid = false;
  }
  
  // 更新验证状态
  setTimeout(() => {
    emit('valid-change', isValid);
  }, 150);
  return isValid;
};

// 统一处理子组件的valid-change事件
const handleValidChange = (valid: boolean) => {
  // 直接传递给父组件
  emit('valid-change', valid);
};

// 处理查询字段数据更新
const handleQueryFieldsDataUpdate = (data: any[]) => {
  // 保存查询字段数据，用于后续传递给DSL配置表单
  if (Array.isArray(data)) {
    queryFieldsData.value = [...data];
    
    // 将查询字段数据传递给父组件
    emit('query-metadata-update', data);
  }
};

// 处理DSL配置数据更新
const handleDslConfigUpdate = (data: any) => {
  // 将DSL配置数据传递给父组件
  emit('dsl-config-update', data);
};

// 重置查询字段表单数据
const resetQueryFieldsForm = () => {
  if (queryFieldsFormRef.value && queryFieldsFormRef.value.resetForm) {
    queryFieldsFormRef.value.resetForm();
  }
  provideQueryFunction.value = true; // 默认提供查询功能
};

// 重置DSL配置表单数据
const resetDslConfigForm = () => {
  if (dslConfigFormRef.value && dslConfigFormRef.value.resetForm) {
    dslConfigFormRef.value.resetForm();
  }
};

// 重置所有表单数据
const resetAllForms = () => {
  resetQueryFieldsForm();
  resetDslConfigForm();
};

// 设置查询字段数据
const setQueryFieldsData = (fieldData: any[], provideQuery: boolean = true) => {
  // 设置是否提供查询功能，RPC模式下始终为true
  provideQueryFunction.value = true;
  
  // 如果已经创建了queryFieldsFormRef组件实例，则设置查询字段数据和控制标识
  if (queryFieldsFormRef.value) {
    queryFieldsFormRef.value.setFieldsData(fieldData, true); // 确保传递true
  }
};

// 设置DSL配置数据
const setDslConfigData = (configData: any) => {
  // 如果已经创建了dslConfigFormRef组件实例，则设置DSL配置数据
  if (dslConfigFormRef.value && dslConfigFormRef.value.setFormData) {
    dslConfigFormRef.value.setFormData(configData);
  }
};

// 对外暴露步骤状态和方法
defineExpose({
  step: actualStep,
  resetQueryFieldsForm,
  resetDslConfigForm,
  resetAllForms,
  validateCurrentStepForm,
  setQueryFieldsData,
  setDslConfigData
});
</script>

<style scoped lang="scss">
.rpc-access-guide {
  .rpc-steps {
    margin-bottom: 20px;
    padding: 16px 0;
    
    :deep(.el-step) {
      .el-step__head {
        .el-step__line {
          background-color: var(--el-border-color-lighter);
        }
        
        .el-step__icon {
          &.is-text {
            border-color: var(--el-border-color);
            background-color: var(--el-bg-color);
            color: var(--el-text-color-secondary);
          }
        }
        
        &.is-process {
          .el-step__icon {
            &.is-text {
              border-color: var(--el-color-primary);
              background-color: var(--el-color-primary);
              color: white;
            }
          }
        }
        
        &.is-finish {
          .el-step__icon {
            &.is-text {
              border-color: var(--el-color-success);
              background-color: var(--el-color-success);
              color: white;
            }
          }
          
          .el-step__line {
            background-color: var(--el-color-success);
          }
        }
      }
      
      .el-step__main {
        .el-step__title {
          font-size: 14px;
          
          &.is-process {
            color: var(--el-color-primary);
            font-weight: 500;
          }
          
          &.is-finish {
            color: var(--el-color-success);
          }
        }
        
        .el-step__description {
          font-size: 12px;
          
          &.is-process {
            color: var(--el-color-primary-light-3);
          }
          
          &.is-finish {
            color: var(--el-color-success-light-3);
          }
        }
      }
    }
  }
  
  .guide-card {
    border-radius: 8px;
    
    :deep(.el-card__header) {
      padding: 15px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      background-color: var(--el-fill-color-light);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      
      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .step-content {
      .section-desc {
        font-size: 14px;
        color: var(--el-text-color-regular);
        margin-bottom: 20px;
      }
      
      .rpc-config-form {
        // RPC配置表单样式
        width: 100%;
      }
    }
  }
}
</style> 