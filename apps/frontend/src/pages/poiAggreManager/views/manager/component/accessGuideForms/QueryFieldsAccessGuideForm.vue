<template>
  <div class="query-function-config">
    <!-- 添加提示信息 -->
    <el-alert
      type="warning"
      show-icon
      :closable="false"
      class="guide-alert"
    >
      <span>
        此阶段配置的是<b>「聚合查询 - 查询服务」</b>中的字段基本信息，规定了哪些字段是允许<b>查询</b>的。<br>
        注意：字段Code必须唯一，且符合命名规范；字段名称应当简洁明确，便于理解字段的业务含义。<br>
      </span>
    </el-alert>
    
    <p v-if="!props.hideQueryOption" class="section-desc">是否需要提供查询功能？</p>
    
    <el-radio-group v-if="!props.hideQueryOption" v-model="localProvideQueryFunction" class="query-radio-group">
      <el-radio :label="true">是</el-radio>
      <el-radio :label="false">否</el-radio>
    </el-radio-group>
    
    <!-- 查询字段配置，仅当选择提供查询功能时显示，或者当hideQueryOption为true时始终显示 -->
    <div v-if="localProvideQueryFunction" class="query-field-config">
      <p class="sub-section-title">请配置查询字段信息</p>
      
      <!-- 查询字段表单 -->
      <el-form ref="queryFieldFormRef" :model="formData" :rules="rules" label-position="top">
        <div class="fields-container">
          <div v-for="(field, index) in formData.fields" :key="index" class="field-item">
            <div class="field-header">
              <span class="field-title">查询字段 {{ index + 1 }}</span>
              <el-button
                type="danger"
                :icon="Delete"
                circle
                size="small"
                @click="removeField(index)"
              />
            </div>
            <div class="field-content">
              <div class="field-row">
                <el-form-item
                  :prop="'fields.' + index + '.fieldCode'"
                  label="字段Code"
                  required
                  class="field-col"
                >
                  <el-input
                    v-model="field.fieldCode"
                    placeholder="请输入字段Code（命名规范：xxx_yyy_zzz）"
                  />
                </el-form-item>
                
                <el-form-item
                  :prop="'fields.' + index + '.fieldProperty'"
                  label="字段Property"
                  required
                  class="field-col"
                >
                  <el-input
                    v-model="field.fieldProperty"
                    placeholder="请输入字段属性（命名规范：xxxYyyZzz）"
                  />
                </el-form-item>
              </div>
              
              <div class="field-row">
                <el-form-item
                  :prop="'fields.' + index + '.fieldName'"
                  label="字段名称"
                  required
                  class="field-col"
                >
                  <el-input
                    v-model="field.fieldName"
                    placeholder="请输入字段名称"
                  />
                </el-form-item>
                
                <el-form-item
                  :prop="'fields.' + index + '.description'"
                  label="字段描述"
                  class="field-col"
                >
                  <el-input
                    v-model="field.description"
                    placeholder="请输入字段描述"
                  />
                </el-form-item>
              </div>
              
              <div class="field-row">
                <el-form-item
                  :prop="'fields.' + index + '.type'"
                  label="字段类型"
                  required
                  class="field-col"
                >
                  <el-select v-model="field.type" placeholder="请选择字段类型" style="width: 100%">
                    <el-option
                      v-for="type in FIELD_TYPE_OPTIONS"
                      :key="type.value"
                      :label="type.label"
                      :value="type.value"
                    />
                  </el-select>
                </el-form-item>
                
                <el-form-item
                  :prop="'fields.' + index + '.defaultValue'"
                  label="默认值"
                  required
                  class="field-col"
                >
                  <el-input
                    v-model="field.defaultValue"
                    placeholder="请输入默认值"
                  />
                </el-form-item>
              </div>
              
              <div class="field-row">
                <el-form-item
                  :prop="'fields.' + index + '.syncedField'"
                  class="field-col sync-field"
                >
                  <template #label>
                    <span class="label-no-trigger">是否是同步字段</span>
                  </template>
                  <el-switch 
                    v-model="field.syncedField" 
                    :disabled="props.lockSyncedField"
                  />
                </el-form-item>
                
                <el-form-item
                  v-if="props.showDependentFields"
                  :prop="'fields.' + index + '.dependentFields'"
                  label="依赖字段"
                  class="field-col"
                >
                  <el-select
                    v-model="field.dependentFields"
                    multiple
                    filterable
                    :allow-create="!dependentFieldsLoading && dependentFieldOptions.length > 0"
                    default-first-option
                    placeholder="请选择或输入依赖字段"
                    style="width: 100%"
                    :loading="dependentFieldsLoading"
                    :no-data-text="dependentFieldsLoading ? '加载中...' : '暂无数据'"
                  >
                    <el-option
                      v-for="option in dependentFieldOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              
              <div class="field-row">
                <el-form-item
                  :prop="'fields.' + index + '.lionConfigDescription'"
                  label="Lion配置描述"
                  required
                  class="field-col"
                >
                  <el-input
                    v-model="field.lionConfigDescription"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入Lion配置描述"
                  />
                </el-form-item>
              </div>
            </div>
          </div>
        </div>
        <div class="form-actions">
          <el-button type="primary" @click="addField">
            <el-icon><Plus /></el-icon>添加查询字段
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Delete } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { FIELD_TYPE_OPTIONS } from '../../../../types';
import type { FieldType, FieldMetadataItem } from '../../../../types';
import { getFieldMetadataList } from '../../../../request';

// 创建一个与FieldMetadataItem相似但类型更宽松的接口
interface QueryFormField {
  id?: number;
  fieldCode: string;
  fieldName: string;
  description: string;
  type: FieldType | undefined;
  fieldProperty: string;
  defaultValue: string;
  dependentFields: string[];
  syncedField: boolean;
  valid: number;
  status: number;
  lionConfigDescription: string;
  opName?: string;
  opMis?: string;
  ctime?: number;
  utime?: number;
}

const props = defineProps({
  provideQueryFunction: {
    type: Boolean,
    required: true
  },
  queryFieldForm: {
    type: Object,
    default: () => ({})
  },
  existingFields: {
    type: Array as () => string[],
    default: () => []
  },
  // 新增属性：DTS模式下控制依赖字段显示
  showDependentFields: {
    type: Boolean,
    default: true
  },
  // 新增属性：是否锁定同步字段开关
  lockSyncedField: {
    type: Boolean,
    default: false
  },
  // 新增属性：同步字段开关默认值
  defaultSyncedField: {
    type: Boolean,
    default: false
  },
  // 新增属性：是否隐藏查询功能选项
  hideQueryOption: {
    type: Boolean,
    default: false
  }
});

// 定义提交事件
const emit = defineEmits([
  'submit', 
  'loading-state-change', 
  'valid-change',
  'update:provideQueryFunction',
  'update:query-metadata',
  'query-fields-data'
]);

// 本地响应式变量，用于双向绑定
const localProvideQueryFunction = ref(props.hideQueryOption ? true : props.provideQueryFunction);

// 表单引用
const queryFieldFormRef = ref<FormInstance>();
const dependentFieldsLoading = ref(false);
const dependentFieldOptions = ref<{ label: string; value: string }[]>([]);
const isSubmitting = ref(false);

const formData = reactive<{
  fields: QueryFormField[]
}>({
  fields: [{
    fieldCode: '',
    fieldName: '',
    type: undefined,
    description: '',
    defaultValue: '',
    dependentFields: [],
    fieldProperty: '',
    lionConfigDescription: '',
    valid: 1,
    status: 0,
    syncedField: false
  }]
});

// 校验字段Code是否重复
const validateFieldCodeUnique = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  
  // 获取当前字段索引
  const fieldIndex = parseInt(rule.field.split('.')[1]);
  
  // 检查其他字段中是否有相同的Code
  const hasDuplicate = formData.fields.some((field, index) => 
    index !== fieldIndex && field.fieldCode && field.fieldCode === value
  );
  
  if (hasDuplicate) {
    callback(new Error('字段Code不能重复'));
  } else {
    callback();
  }
};

// 校验字段Property是否重复
const validateFieldPropertyUnique = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  
  // 获取当前字段索引
  const fieldIndex = parseInt(rule.field.split('.')[1]);
  
  // 检查其他字段中是否有相同的Property
  const hasDuplicate = formData.fields.some((field, index) => 
    index !== fieldIndex && field.fieldProperty && field.fieldProperty === value
  );
  
  if (hasDuplicate) {
    callback(new Error('字段Property不能重复'));
  } else {
    callback();
  }
};

// 校验字段名称是否重复
const validateFieldNameUnique = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  
  // 获取当前字段索引
  const fieldIndex = parseInt(rule.field.split('.')[1]);
  
  // 检查其他字段中是否有相同的名称
  const hasDuplicate = formData.fields.some((field, index) => 
    index !== fieldIndex && field.fieldName && field.fieldName === value
  );
  
  if (hasDuplicate) {
    callback(new Error('字段名称不能重复'));
  } else {
    callback();
  }
};

// 校验默认值格式
const validateDefaultValue = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback();
    return;
  }
  
  // 获取当前字段索引
  const fieldIndex = parseInt(rule.field.split('.')[1]);
  const field = formData.fields[fieldIndex];
  
  if (!field || !field.type) {
    callback();
    return;
  }
  
  // 根据字段类型校验默认值格式
  switch (field.type) {
    case '1': // String
      // 字符串类型，检查是否有双引号包裹
      if (!value.startsWith('"') || !value.endsWith('"')) {
        callback(new Error('字符串类型默认值需要用双引号包裹，例如: "默认值"'));
      } else {
        callback();
      }
      break;
    case '2': // Boolean
      // 布尔类型，检查是否为true或false
      if (value !== 'true' && value !== 'false') {
        callback(new Error('布尔类型默认值只能是true或false'));
      } else {
        callback();
      }
      break;
    case '3': // Long
      // 整数类型，检查是否为有效整数
      if (!/^-?\d+$/.test(value)) {
        callback(new Error('整数类型默认值必须是有效的整数'));
      } else {
        callback();
      }
      break;
    case '4': // Double
      // 浮点数类型，检查是否为有效浮点数
      if (!/^-?\d+(\.\d+)?$/.test(value)) {
        callback(new Error('浮点数类型默认值必须是有效的数字'));
      } else {
        callback();
      }
      break;
    default:
      callback();
  }
};

const rules = reactive<FormRules>({
  'fields': [
    { required: true, message: '请至少添加一个查询字段', trigger: 'change' }
  ],
  'fields.0.fieldCode': [
    { required: true, message: '请输入字段Code', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段Code必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
    { validator: validateFieldCodeUnique, trigger: 'blur' }
  ],
  'fields.0.fieldProperty': [
    { required: true, message: '请输入字段Property', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9]*$/, message: '字段Property必须以字母开头，只能包含字母和数字', trigger: 'blur' },
    { validator: validateFieldPropertyUnique, trigger: 'blur' }
  ],
  'fields.0.fieldName': [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { validator: validateFieldNameUnique, trigger: 'blur' }
  ],
  'fields.0.type': [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ],
  'fields.0.defaultValue': [
    { required: true, message: '请输入默认值', trigger: 'blur, change' },
    { validator: validateDefaultValue, trigger: 'blur, change' }
  ],
  'fields.0.lionConfigDescription': [
    { required: true, message: '请输入Lion配置描述', trigger: 'blur' }
  ]
});

// 获取依赖字段选项
const fetchDependentFields = async () => {
  dependentFieldsLoading.value = true;
  try {
    const res = await getFieldMetadataList({
      pageNo: 1,
      pageSize: 1000,
      valid: 1
    });
    if (res.code === 0) {
      dependentFieldOptions.value = res.data.records.map((item: { fieldCode: string }) => ({
        label: item.fieldCode,
        value: item.fieldCode
      }));
    } else {
      ElMessage.error(res.message || '获取依赖字段失败');
    }
  } catch (error) {
    console.error('获取依赖字段失败:', error);
    ElMessage.error('获取依赖字段失败');
  } finally {
    dependentFieldsLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  if (queryFieldFormRef.value) {
    queryFieldFormRef.value.resetFields();
    // 清除所有校验状态
    queryFieldFormRef.value.clearValidate();
  }
  formData.fields = [{
    fieldCode: '',
    fieldName: '',
    type: undefined,
    description: '',
    defaultValue: '',
    dependentFields: [],
    fieldProperty: '',
    lionConfigDescription: '',
    valid: 1,
    status: 0,
    syncedField: false
  }];
};

// 在组件挂载时获取依赖字段
onMounted(() => {
  // 如果隐藏查询选项，确保提供查询功能始终为true
  if (props.hideQueryOption) {
    localProvideQueryFunction.value = true;
    emit('update:provideQueryFunction', true);
  }
  
  fetchDependentFields();
  // 初始化时清除校验状态
  nextTick(() => {
    if (queryFieldFormRef.value) {
      queryFieldFormRef.value.clearValidate();
    }
  });
  
  // 只有在初始化且表单为空时才向上传递默认数据
  // 这样可以防止在步骤切换时清空已有数据
  if (formData.fields.length === 1 && 
      !formData.fields[0].fieldCode && 
      !formData.fields[0].fieldName) {
    // 将formData对象深拷贝后向上传递
    emit('query-fields-data', getFormData());
  }
});

// 添加字段
const addField = () => {
  const newIndex = formData.fields.length;
  formData.fields.push({
    fieldCode: '',
    fieldName: '',
    type: undefined,
    description: '',
    defaultValue: '',
    dependentFields: [],
    fieldProperty: '',
    lionConfigDescription: '',
    valid: 1,
    status: 0,
    syncedField: props.defaultSyncedField || false
  });
  
  // 为新字段添加验证规则，但不立即触发验证
  rules[`fields.${newIndex}.fieldCode`] = [
    { required: true, message: '请输入字段Code', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段Code必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
    { validator: validateFieldCodeUnique, trigger: 'blur' }
  ];
  rules[`fields.${newIndex}.fieldProperty`] = [
    { required: true, message: '请输入字段Property', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9]*$/, message: '字段Property必须以字母开头，只能包含字母和数字', trigger: 'blur' },
    { validator: validateFieldPropertyUnique, trigger: 'blur' }
  ];
  rules[`fields.${newIndex}.fieldName`] = [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { validator: validateFieldNameUnique, trigger: 'blur' }
  ];
  rules[`fields.${newIndex}.type`] = [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ];
  rules[`fields.${newIndex}.defaultValue`] = [
    { required: true, message: '请输入默认值', trigger: 'blur, change' },
    { validator: validateDefaultValue, trigger: 'blur, change' }
  ];
  rules[`fields.${newIndex}.lionConfigDescription`] = [
    { required: true, message: '请输入Lion配置描述', trigger: 'blur' }
  ];
  
  // 使用nextTick确保DOM更新后再滚动到新添加的字段
  nextTick(() => {
    // 清除所有字段的校验结果，避免添加字段时触发校验
    if (queryFieldFormRef.value) {
      queryFieldFormRef.value.clearValidate();
    }
    
    // 增加延迟时间，确保DOM完全渲染
    setTimeout(() => {
      // 使用更精确的选择器，确保找到正确的容器
      const container = document.querySelector('.query-function-config .fields-container');
      
      if (container) {
        // 先记录当前滚动高度和总高度，用于调试
        const beforeScrollTop = container.scrollTop;
        const containerHeight = container.scrollHeight;
        
        // 设置滚动到底部
        container.scrollTop = containerHeight;
        
        // 再次检查是否滚动成功，如果没有则尝试其他方法
        setTimeout(() => {
          if (container.scrollTop <= beforeScrollTop && containerHeight > 0) {
            // 备用方法: 获取最后一个字段元素并滚动到该元素
            const lastField = container.querySelector('.field-item:last-child');
            if (lastField) {
              lastField.scrollIntoView({ behavior: 'smooth', block: 'end' });
            }
          }
        }, 50);
      }
    }, 50); // 增加延迟时间到200ms
  });

  // 通知父组件字段已添加
  emit('valid-change', false);
};

// 删除字段
const removeField = (index: number) => {
  if (formData.fields.length > 1) {
    formData.fields.splice(index, 1);
    // 删除该字段的验证规则
    delete rules[`fields.${index}.fieldCode`];
    delete rules[`fields.${index}.fieldProperty`];
    delete rules[`fields.${index}.fieldName`];
    delete rules[`fields.${index}.type`];
    delete rules[`fields.${index}.defaultValue`];
    delete rules[`fields.${index}.lionConfigDescription`];
    
    // 更新后续字段的验证规则索引
    for (let i = index; i < formData.fields.length; i++) {
      rules[`fields.${i}.fieldCode`] = rules[`fields.${i + 1}.fieldCode`];
      rules[`fields.${i}.fieldProperty`] = rules[`fields.${i + 1}.fieldProperty`];
      rules[`fields.${i}.fieldName`] = rules[`fields.${i + 1}.fieldName`];
      rules[`fields.${i}.type`] = rules[`fields.${i + 1}.type`];
      rules[`fields.${i}.defaultValue`] = rules[`fields.${i + 1}.defaultValue`];
      rules[`fields.${i}.lionConfigDescription`] = rules[`fields.${i + 1}.lionConfigDescription`];
      
      delete rules[`fields.${i + 1}.fieldCode`];
      delete rules[`fields.${i + 1}.fieldProperty`];
      delete rules[`fields.${i + 1}.fieldName`];
      delete rules[`fields.${i + 1}.type`];
      delete rules[`fields.${i + 1}.defaultValue`];
      delete rules[`fields.${i + 1}.lionConfigDescription`];
    }
  } else {
    ElMessage.warning('至少保留一个查询字段');
  }
};

// 检查字段是否有效
const isFieldValid = (field: QueryFormField): boolean => {
  if (!field.fieldCode || !field.fieldProperty || !field.fieldName || !field.type || field.defaultValue === undefined || field.defaultValue === '') {
    return false;
  }
  
  // 检查默认值格式
  if (field.type === '1') { // String
    if (!field.defaultValue.startsWith('"') || !field.defaultValue.endsWith('"')) {
      return false;
    }
  } else if (field.type === '2') { // Boolean
    if (field.defaultValue !== 'true' && field.defaultValue !== 'false') {
      return false;
    }
  } else if (field.type === '3') { // Long
    if (!/^-?\d+$/.test(field.defaultValue)) {
      return false;
    }
  } else if (field.type === '4') { // Double
    if (!/^-?\d+(\.\d+)?$/.test(field.defaultValue)) {
      return false;
    }
  }
  
  return true;
};

// 基于数组内容的验证方法
const validateFieldsContent = (): boolean => {
  if (!localProvideQueryFunction.value) {
    return true; // 如果不提供查询功能，直接返回true
  }
  
  // 检查是否有字段
  if (formData.fields.length === 0) {
    return false;
  }
  
  // 检查每个字段是否都有效
  const allFieldsValid = formData.fields.every(field => isFieldValid(field));
  if (!allFieldsValid) {
    return false;
  }
  
  // 检查是否有重复的字段Code
  const fieldCodes = formData.fields.map(field => field.fieldCode).filter(Boolean);
  const uniqueCodes = new Set(fieldCodes);
  if (fieldCodes.length !== uniqueCodes.size) {
    return false;
  }
  
  // 检查是否有重复的字段Property
  const fieldProperties = formData.fields.map(field => field.fieldProperty).filter(Boolean);
  const uniqueProperties = new Set(fieldProperties);
  if (fieldProperties.length !== uniqueProperties.size) {
    return false;
  }
  
  // 检查是否有重复的字段名称
  const fieldNames = formData.fields.map(field => field.fieldName).filter(Boolean);
  const uniqueNames = new Set(fieldNames);
  if (fieldNames.length !== uniqueNames.size) {
    return false;
  }
  
  return true;
};

// 监听字段变化，更新验证状态
watch(() => formData.fields, () => {
  if (!localProvideQueryFunction.value) return;
  
  // 当字段内容变化时，根据字段数组的内容进行校验
  const isValid = validateFieldsContent();
  // 通知父组件验证状态变化
  emit('valid-change', isValid);
  
  // 当字段内容变化时，向父组件发送深拷贝的字段数据，避免直接共享引用
  emit('query-fields-data', getFormData());
  
  // 如果表单有效，则发送最新的查询字段数据
  if (isValid) {
    const fieldsData = getFormData();
    emit('update:query-metadata', fieldsData);
  }
}, { deep: true });

// 监听提供查询功能的状态变化
watch(localProvideQueryFunction, (newValue) => {
  emit('update:provideQueryFunction', newValue);
  
  // 更新验证状态
  const isValid = newValue ? validateFieldsContent() : true;
  emit('valid-change', isValid);
  
  // 如果切换为不提供查询功能，清空表单数据
  if (!newValue) {
    // 不需要重置表单，但是需要通知父组件查询字段数据为空
    emit('query-fields-data', []);
    emit('update:query-metadata', []);
  } else {
    // 如果切换为提供查询功能，发送当前表单数据
    const fieldsData = getFormData();
    emit('query-fields-data', fieldsData);
    if (isValid) {
      emit('update:query-metadata', fieldsData);
    }
  }
});

// 监听props变化，同步到本地变量
watch(() => props.provideQueryFunction, (newValue) => {
  // 如果隐藏查询选项，则始终提供查询功能
  if (props.hideQueryOption) {
    localProvideQueryFunction.value = true;
  } else {
    localProvideQueryFunction.value = newValue;
  }
});

// 添加监听 hideQueryOption 的变化
watch(() => props.hideQueryOption, (newValue) => {
  // 如果隐藏查询选项，则始终提供查询功能
  if (newValue) {
    localProvideQueryFunction.value = true;
  }
}, { immediate: true });

// 验证表单
const validateForm = async () => {
  if (!localProvideQueryFunction.value) return true;
  if (!queryFieldFormRef.value) return false;
  
  // 先检查是否有重复的字段
  const fieldCodes = formData.fields.map(field => field.fieldCode);
  const fieldProperties = formData.fields.map(field => field.fieldProperty);
  const fieldNames = formData.fields.map(field => field.fieldName);
  
  // 检查是否有重复的字段Code
  const hasDuplicateCode = fieldCodes.some((code, index) => {
    if (!code) return false; // 跳过空值
    return fieldCodes.indexOf(code) !== index;
  });
  
  if (hasDuplicateCode) {
    ElMessage.error('存在重复的字段Code，请修改后再提交');
    return false;
  }
  
  // 检查是否有重复的字段Property
  const hasDuplicateProperty = fieldProperties.some((prop, index) => {
    if (!prop) return false; // 跳过空值
    return fieldProperties.indexOf(prop) !== index;
  });
  
  if (hasDuplicateProperty) {
    ElMessage.error('存在重复的字段Property，请修改后再提交');
    return false;
  }
  
  // 检查是否有重复的字段名称
  const hasDuplicateName = fieldNames.some((name, index) => {
    if (!name) return false; // 跳过空值
    return fieldNames.indexOf(name) !== index;
  });
  
  if (hasDuplicateName) {
    ElMessage.error('存在重复的字段名称，请修改后再提交');
    return false;
  }
  
  try {
    if (!props.hideQueryOption) {
      await queryFieldFormRef.value.validate();
    }
    return true;
  } catch (error) {
    return false;
  }
};

// 提交表单
const submitForm = async () => {
  if (!localProvideQueryFunction.value) {
    // 如果不提供查询功能，直接返回成功
    emit('submit', []);
    return true;
  }
  
  const isValid = await validateForm();
  if (!isValid) return false;
  
  isSubmitting.value = true;
  emit('loading-state-change', true); // 通知父组件更新loading状态
  
  try {
    const formData = getFormData();
    emit('submit', formData);
    return true;
  } catch (error) {
    console.error('表单提交失败:', error);
    return false;
  } finally {
    isSubmitting.value = false;
    emit('loading-state-change', false);
  }
};

// 获取表单数据
const getFormData = (): QueryFormField[] => {
  if (!localProvideQueryFunction.value) {
    return [];
  }
  const fields = JSON.parse(JSON.stringify(formData.fields));
  fields.forEach((field: QueryFormField) => {
    field.defaultValue = field.defaultValue.startsWith('""') && field.defaultValue.endsWith('""') ? field.defaultValue.slice(1, -1) : field.defaultValue;
  });
  return fields;
};

// 主动触发表单验证
const activeValidate = async () => {
  if (!localProvideQueryFunction.value) {
    return true;
  }
  return await validateForm();
};

// 手动发送查询字段数据
const emitQueryFieldsData = () => {
  const fieldsData = getFormData();
  emit('query-fields-data', fieldsData);
};

// 设置字段数据，用于接收从父组件传递的同步字段
const setFieldsData = (fieldData: any[], provideQuery: boolean = true) => {
  // 检查当前表单是否已有有效数据
  const hasExistingData = formData.fields.length > 0 && 
    formData.fields.some(field => field.fieldCode && field.fieldName);
  
  // 如果已有有效数据，则不进行重复填充
  if (hasExistingData) {
    // 只更新查询功能开关状态，不覆盖已有字段数据
    localProvideQueryFunction.value = provideQuery;
    emit('update:provideQueryFunction', provideQuery);
    return;
  }
  
  // 以下是原有的填充逻辑，只在没有有效数据时执行
  // 设置查询功能开关
  localProvideQueryFunction.value = provideQuery;
  emit('update:provideQueryFunction', provideQuery);
  
  // 清空现有字段
  formData.fields = [];
  
  // 将同步字段转化为查询字段
  fieldData.forEach(syncField => {
    if (syncField.fieldCode) {
      formData.fields.push({
        fieldCode: syncField.fieldCode,
        fieldName: syncField.fieldName || syncField.fieldCode,
        type: syncField.type || 'STRING',
        description: syncField.description || '',
        defaultValue: '""',  // 字符串类型的默认值
        dependentFields: [],
        fieldProperty: convertToProperty(syncField.fieldCode),
        lionConfigDescription: `【查询字段】${syncField.fieldProperty}${syncField.description ? '-' + syncField.description : ''}`,
        valid: 1,
        status: 0,
        syncedField: props.defaultSyncedField || true // 默认设置为同步字段
      });
    }
  });
  
  // 如果没有字段数据，添加一个空字段
  if (formData.fields.length === 0) {
    addField();
  }
  
  // 发送更新后的字段数据给父组件
  emitQueryFieldsData();
  
  // 触发验证
  nextTick(() => {
    validateForm();
  });
};

// 将字段code转换为property格式（下划线命名转驼峰）
const convertToProperty = (fieldCode: string): string => {
  if (!fieldCode) return '';
  
  // 将下划线命名转为驼峰
  return fieldCode.replace(/_([a-z])/g, (match, group) => group.toUpperCase());
};

// 监听每个字段的类型变化
const handleTypeChange = (field: QueryFormField) => {
  // 根据不同的字段类型设置默认值
  switch (field.type) {
    case '1': // String
      field.defaultValue = '""';
      break;
    case '2': // Boolean
      field.defaultValue = 'false';
      break;
    case '3': // Long
      field.defaultValue = '0';
      break;
    case '4': // Double
      field.defaultValue = '0.0';
      break;
    default:
      field.defaultValue = '';
  }
};

// 监听所有字段的类型变化
watch(() => formData.fields.map(f => f.type), (newTypes, oldTypes) => {
  if (!oldTypes) return;
  formData.fields.forEach((field, index) => {
    if (field.type !== oldTypes[index]) {
      handleTypeChange(field);
    }
  });
}, { deep: true });

// 监听默认值变化，触发校验
watch(() => formData.fields.map(f => f.defaultValue), (newValues, oldValues) => {
  if (!oldValues) return;
  
  // 使用nextTick确保DOM更新后再触发校验
  nextTick(() => {
    formData.fields.forEach((field, index) => {
      if (field.defaultValue !== oldValues[index] && queryFieldFormRef.value) {
        // 触发对应字段的默认值校验
        queryFieldFormRef.value.validateField(`fields.${index}.defaultValue`);
      }
    });
  });
}, { deep: true });

// 监听fieldCode变化，自动生成fieldProperty
watch(() => formData.fields.map(f => f.fieldCode), (newValues, oldValues) => {
  if (!oldValues) return;
  
  formData.fields.forEach((field, index) => {
    if (field.fieldCode !== oldValues[index]) {
      // 将下划线命名转换为驼峰命名
      const camelCase = convertFieldCodeToProperty(field.fieldCode || '');
      
      // 只有当fieldProperty为空或者是由fieldCode自动生成的时候才更新
      if (!field.fieldProperty || field.fieldProperty === convertFieldCodeToProperty(oldValues[index] || '')) {
        field.fieldProperty = camelCase;
        
        // 触发校验
        nextTick(() => {
          if (queryFieldFormRef.value) {
            queryFieldFormRef.value.validateField(`fields.${index}.fieldCode`);
            queryFieldFormRef.value.validateField(`fields.${index}.fieldProperty`);
          }
        });
      }
    }
  });
}, { deep: true });

// 监听fieldProperty变化，自动生成fieldCode
watch(() => formData.fields.map(f => f.fieldProperty), (newValues, oldValues) => {
  if (!oldValues) return;
  
  formData.fields.forEach((field, index) => {
    if (field.fieldProperty !== oldValues[index]) {
      // 将驼峰命名转换为下划线命名
      const snakeCase = convertPropertyToFieldCode(field.fieldProperty || '');
      
      // 只有当fieldCode为空或者是由fieldProperty自动生成的时候才更新
      if (!field.fieldCode || field.fieldCode === convertPropertyToFieldCode(oldValues[index] || '')) {
        field.fieldCode = snakeCase;
        
        // 触发校验
        nextTick(() => {
          if (queryFieldFormRef.value) {
            queryFieldFormRef.value.validateField(`fields.${index}.fieldCode`);
            queryFieldFormRef.value.validateField(`fields.${index}.fieldProperty`);
          }
        });
      }
    }
  });
}, { deep: true });

// 监听fieldProperty和description变化，自动生成lionConfigDescription
watch(
  [
    () => formData.fields.map(f => f.fieldCode),
    () => formData.fields.map(f => f.description)
  ],
  (newValues, oldValues) => {
    // 如果是初始化，oldValues 会是 undefined
    const oldCodes = oldValues?.[0] || [];
    const oldDescriptions = oldValues?.[1] || [];
    
    formData.fields.forEach((field, index) => {
      // 检查字段是否发生变化或者是初始化
      const codeChanged = !oldCodes.length || field.fieldCode !== oldCodes[index];
      const descriptionChanged = !oldDescriptions.length || field.description !== oldDescriptions[index];
      
      // 如果任一字段发生变化或者是初始化，且fieldCode不为空，则生成 lionConfigDescription
      if ((codeChanged || descriptionChanged) && field.fieldCode) {
        const oldLionConfigDescription = field.lionConfigDescription;
        field.lionConfigDescription = `【查询字段】${field.fieldProperty}${field.description ? '-' + field.description : ''}`;
        
        // 如果lionConfigDescription发生变化，触发校验
        if (oldLionConfigDescription !== field.lionConfigDescription) {
          nextTick(() => {
            if (queryFieldFormRef.value) {
              queryFieldFormRef.value.validateField(`fields.${index}.lionConfigDescription`);
            }
          });
        }
      } else if (!field.fieldCode) {
        field.lionConfigDescription = '';
      }
    });
  },
  { deep: true, immediate: true }
);

// 辅助函数：将fieldCode转换为fieldProperty
const convertFieldCodeToProperty = (fieldCode: string): string => {
  if (!fieldCode) return '';
  return fieldCode
    .split('_')
    .map((word, index) => {
      if (index === 0) {
        return word.toLowerCase();
      }
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join('');
};

// 辅助函数：将fieldProperty转换为fieldCode
const convertPropertyToFieldCode = (fieldProperty: string): string => {
  if (!fieldProperty) return '';
  return fieldProperty
    .replace(/([A-Z])/g, '_$1')
    .toLowerCase()
    .replace(/^_/, '');
};

// 对外暴露方法
defineExpose({
  validateForm,
  activeValidate,
  submitForm,
  setFieldsData
});
</script>

<style scoped lang="scss">
.query-function-config {
  .guide-alert {
    margin-bottom: 20px;
    
    :deep(.el-alert__content) {
      padding: 0 8px;
    }

    :deep(.el-alert__icon) {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 1.5;
    }
  }
  
  .section-desc {
    font-size: 14px;
    margin-bottom: 16px;
    color: #606266;
  }
  
  .query-radio-group {
    margin-bottom: 20px;
  }
  
  .query-field-config {
    margin-top: 20px;
    
    .sub-section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #303133;
    }
    
    .fields-container {
      max-height: calc(70vh - 150px);
      overflow-y: auto;
      padding-right: 16px;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
      }
      
      .field-item {
        margin-bottom: 24px;
    padding: 20px;
        border: 1px solid #dcdfe6;
    border-radius: 4px;
        background-color: #f8f9fa;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .field-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #ebeef5;
          
          .field-title {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }
        }
        
        .field-content {
          .field-row {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 8px;
            
            .field-col {
              flex: 1;
              min-width: 220px;
              
              &.sync-field {
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                
                :deep(.el-form-item__content) {
                  display: flex;
                  align-items: center;
                  height: 40px;
                }
                
                :deep(.el-form-item__label) {
                  pointer-events: none;
                }
              }
            }
            
            /* 当宽度小于768px时，一列显示 */
            @media (max-width: 768px) {
              flex-direction: column;
              gap: 0;
              
              .field-col {
                width: 100%;
                min-width: 100%;
              }
            }
          }
        }
      }
    }
    
    .form-actions {
      padding-top: 16px;
      border-top: 1px solid #ebeef5;
    text-align: center;
    }
  }
}
</style> 