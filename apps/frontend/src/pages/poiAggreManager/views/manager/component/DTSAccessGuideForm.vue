<template>
  <div class="dts-access-guide">
    <!-- 步骤导航条 -->
    <el-steps 
      :active="actualStep" 
      finish-status="success" 
      class="dts-steps"
      process-status="process"
      align-center
    >
      <el-step title="填写同步字段信息" description="配置需要同步的字段内容" />
      <el-step title="填写同步配置信息" description="设置同步规则与配置" />
      <el-step title="查询功能配置" description="配置是否提供查询及字段" />
      <el-step title="创建数据订阅任务" description="DTS平台创建订阅任务" />
    </el-steps>

    <!-- 步骤内容区 -->
    <el-card shadow="never" class="guide-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">DTS 接入指引</span>
        </div>
      </template>
      
      <!-- 步骤1：填写同步字段信息 -->
      <div v-show="actualStep === 0" class="step-content">
        <SyncFieldsAccessGuideForm 
          @valid-change="handleValidChange"
          @sync-fields-data="handleSyncFieldsDataUpdate"
          ref="syncFieldsFormRef"
        />
      </div>
      
      <!-- 步骤2：填写同步配置信息 -->
      <div v-show="actualStep === 1" class="step-content">
        <SyncConfigAccessGuideForm 
          :config-form="configForm" 
          :access-way="props.accessWay"
          :disable-sync-fields-edit="actualStep === 1"
          :show-sync-type="false"
          :show-poi-outer="false"
          @valid-change="handleValidChange"
          @sync-config-update="handleSyncConfigUpdate"
          ref="syncConfigFormRef"
        />
      </div>
      
      <!-- 步骤3：查询功能配置 -->
      <div v-show="actualStep === 2" class="step-content">
        <QueryFieldsAccessGuideForm
          v-model:provide-query-function="provideQueryFunction"
          :query-field-form="queryFieldForm"
          :lock-synced-field="true"
          :default-synced-field="true"
          :show-dependent-fields="true"
          @valid-change="handleValidChange"
          @query-fields-data="handleQueryFieldsDataUpdate"
          @update:provide-query-function="(value: boolean) => emit('provide-query-function-update', value)"
          ref="queryFieldsFormRef"
        />
      </div>
      
      <!-- 步骤4：创建数据订阅任务 -->
      <div v-show="actualStep === 3 || actualStep === 4" class="step-content">
        <DtsTaskAccessGuideForm
          :has-completed-final-step="hasCompletedFinalStep"
          @valid-change="handleValidChange"
          @complete-task="completeTask"
          @subscription-url-update="handleSubscriptionUrlUpdate"
          ref="dtsTaskFormRef"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';

// 引入步骤组件
import SyncFieldsAccessGuideForm from './accessGuideForms/SyncFieldsAccessGuideForm.vue';
import SyncConfigAccessGuideForm from './accessGuideForms/SyncConfigAccessGuideForm.vue';
import QueryFieldsAccessGuideForm from './accessGuideForms/QueryFieldsAccessGuideForm.vue';
import DtsTaskAccessGuideForm from './accessGuideForms/DtsTaskAccessGuideForm.vue';

// 定义props，接收父组件传入的step
const props = defineProps({
  step: {
    type: Number,
    required: true
  },
  accessWay: {
    type: String,
    required: true
  }
});

// 定义emit，确保在使用前声明
const emit = defineEmits<{
  (e: 'valid-change', valid: boolean): void;
  (e: 'update:step', step: number): void;
  (e: 'sync-fields-data-update', data: any[]): void;
  (e: 'sync-config-update', data: any): void;
  (e: 'query-metadata-update', data: any[]): void;
  (e: 'provide-query-function-update', provide: boolean): void;
  (e: 'complete-task'): void;
  (e: 'subscription-url-update', url: string): void;
}>();

// 当前组件内部的步骤映射（0-3对应4个DTS步骤）
const actualStep = ref(props.step - 1);
// 记录是否已完成最后一步
const hasCompletedFinalStep = ref(false);

// 保持actualStep与props.step同步
watch(() => props.step, (newStep) => {
  actualStep.value = newStep - 1;
  
  // 如果已经完成了最后步骤，直接显示最终完成状态
  if (actualStep.value === 3 && hasCompletedFinalStep.value) {
    actualStep.value = 4; // 将步骤设为已全部完成的状态
    // 让父组件知道当前是完成状态，可以提交
    emit('valid-change', true);
  }
  // 步骤切换后，主动验证当前步骤的表单状态
  nextTick(() => {
    validateCurrentStepForm();
  });
});

// 验证当前步骤的表单
const validateCurrentStepForm = async () => {
  let isValid = false;
  
  switch (actualStep.value) {
    case 0: // 同步字段信息
      if (syncFieldsFormRef.value && syncFieldsFormRef.value.activeValidate) {
        isValid = await syncFieldsFormRef.value.activeValidate();
      }
      break;
    case 1: // 同步配置信息
      if (syncConfigFormRef.value && syncConfigFormRef.value.activeValidate) {
        isValid = await syncConfigFormRef.value.activeValidate();
      }
      break;
    case 2: // 查询功能配置
      if (queryFieldsFormRef.value && queryFieldsFormRef.value.activeValidate) {
        isValid = await queryFieldsFormRef.value.activeValidate();
      }
      break;
    case 3: // DTS任务
      if (dtsTaskFormRef.value && dtsTaskFormRef.value.activeValidate) {
        isValid = await dtsTaskFormRef.value.activeValidate();
      }
      break;
    case 4: // DTS任务完成
      if (dtsTaskFormRef.value && dtsTaskFormRef.value.activeValidate) {
        isValid = await dtsTaskFormRef.value.activeValidate();
        isValid = isValid && hasCompletedFinalStep.value;
      }
      break;
    default:
      isValid = false;
  }
  
  // 更新验证状态
  setTimeout(() => {
    emit('valid-change', isValid);
  }, 150);
  return isValid;
};

// 步骤标题
const stepTitles = [
  '填写同步字段信息',
  '填写同步配置信息',
  '查询功能配置',
  '创建数据订阅任务'
];

// 是否提供查询功能
const provideQueryFunction = ref(false);

// 表单数据
const configForm = ref({
  // 同步配置表单数据
  syncScene: undefined,
  syncTenant: undefined,
  syncName: '',
  syncDescription: '',
  lionConfigDescription: '',
  syncFieldCodes: [] as string[],
  type: 2, // 默认DTS类型
  isPoiOuter: 0,
  dtsSyncConfig: '',
  valid: 1
});

const queryFieldForm = ref({
  // 查询字段表单数据
});

// 组件引用
const syncFieldsFormRef = ref();
const syncConfigFormRef = ref();
const queryFieldsFormRef = ref();
const dtsTaskFormRef = ref();

// 统一处理子组件的valid-change事件
const handleValidChange = (valid: boolean) => {
  // 直接传递给父组件
  emit('valid-change', valid);
};

// 处理同步字段数据更新
const handleSyncFieldsDataUpdate = (data: any[]) => {
  // 将子组件的数据传递给父组件，但只有当有实际数据时才传递
  if (data.length > 0 && (data[0].fieldCode || data[0].fieldName)) {
    emit('sync-fields-data-update', data);
  }
};

// 处理同步配置更新
const handleSyncConfigUpdate = (data: any) => {
  // 更新本地配置数据
  Object.assign(configForm.value, data);
  // 将子组件的数据传递给父组件
  emit('sync-config-update', data);
};

// 处理查询字段数据更新
const handleQueryFieldsDataUpdate = (data: any[]) => {
  // 将查询字段数据传递给父组件
  if (Array.isArray(data)) {
    emit('query-metadata-update', data);
  }
};

// 确认任务创建完成
const completeTask = () => {
  hasCompletedFinalStep.value = true;
  // 将步骤设为已全部完成的状态
  actualStep.value = 4;
  // 让父组件知道可以提交了
  emit('valid-change', true);
  emit('complete-task');
};

// 重置字段表单数据
const resetSyncFieldsForm = () => {
  if (syncFieldsFormRef.value) {
    syncFieldsFormRef.value.resetForm();
  }
};

// 重置同步配置表单数据
const resetSyncConfigForm = () => {
  if (syncConfigFormRef.value) {
    syncConfigFormRef.value.resetForm();
  }
};

// 重置所有表单数据
const resetAllForms = () => {
  resetSyncFieldsForm();
  resetSyncConfigForm();
  // 如果有其他表单也一并重置
};

// 监听provideQueryFunction的变化
watch(provideQueryFunction, (newValue) => {
  // 发送查询功能配置状态给父组件
  emit('provide-query-function-update', newValue);
});

// 设置同步字段代码
const setSyncFieldCodes = (fieldCodes: string[]) => {
  // 更新本地configForm中的字段代码
  if (!configForm.value.syncFieldCodes) {
    configForm.value.syncFieldCodes = [];
  }
  configForm.value.syncFieldCodes = [...fieldCodes];
  
  // 如果已经创建了syncConfigFormRef组件实例，则调用它的方法设置同步字段
  if (syncConfigFormRef.value) {
    syncConfigFormRef.value.setSyncFieldCodes(fieldCodes);
  }
};

// 添加新方法：设置同步配置数据
const setSyncConfigData = (configData: any) => {
  // 更新本地configForm
  Object.assign(configForm.value, configData);
  
  // 如果已经创建了syncConfigFormRef组件实例，则调用它的方法设置完整表单数据
  if (syncConfigFormRef.value && syncConfigFormRef.value.setFormData) {
    syncConfigFormRef.value.setFormData(configData);
  }
};

// 设置查询字段数据
const setQueryFieldsData = (fieldData: any[], provideQuery: boolean = true) => {
  // 设置是否提供查询功能
  provideQueryFunction.value = provideQuery;
  
  // 如果已经创建了queryFieldsFormRef组件实例，则设置查询字段数据和控制标识
  if (queryFieldsFormRef.value) {
    queryFieldsFormRef.value.setFieldsData(fieldData, provideQuery);
  }
};

// 处理订阅链接更新
const handleSubscriptionUrlUpdate = (url: string) => {
  emit('subscription-url-update', url);
};

// 添加watch监听actualStep变化，处理步骤切换逻辑
watch(
  () => actualStep.value,
  (newValue, oldValue) => {
    // 当步骤从其他步骤切换到同步配置步骤(步骤2)时
    if (newValue === 1 && oldValue !== 1) {
      // 延迟执行，确保视图已更新
      nextTick(() => {
        // 调用SyncConfigAccessGuideForm的clearOuterIdsKeyValidation方法清除校验
        if (syncConfigFormRef.value && syncConfigFormRef.value.clearOuterIdsKeyValidation) {
          syncConfigFormRef.value.clearOuterIdsKeyValidation();
        }
      });
    }
  }
);

// 新增方法：设置同步字段数据
const setSyncFieldsData = (data: any[]) => {
  if (syncFieldsFormRef.value) {
    syncFieldsFormRef.value.setFieldsData(data);
  }
};

// 新增方法：设置订阅URL
const setSubscriptionUrl = (url: string) => {
  if (dtsTaskFormRef.value) {
    dtsTaskFormRef.value.setSubscriptionUrl(url);
  }
};

// 对外暴露步骤状态和方法
defineExpose({
  step: actualStep.value,
  resetSyncFieldsForm,
  resetSyncConfigForm,
  resetAllForms,
  validateCurrentStepForm,
  setSyncFieldCodes,
  setQueryFieldsData,
  setSyncFieldsData,
  setSubscriptionUrl,
  hasCompletedFinalStep,
  setSyncConfigData
});
</script>

<style scoped lang="scss">
.dts-access-guide {
  .dts-steps {
    margin-bottom: 20px;
    padding: 16px 0;
    
    :deep(.el-step) {
      .el-step__head {
        .el-step__line {
          background-color: var(--el-border-color-lighter);
        }
        
        .el-step__icon {
          &.is-text {
            border-color: var(--el-border-color);
            background-color: var(--el-bg-color);
            color: var(--el-text-color-secondary);
          }
        }
        
        &.is-process {
          .el-step__icon {
            &.is-text {
              border-color: var(--el-color-primary);
              background-color: var(--el-color-primary);
              color: white;
            }
          }
        }
        
        &.is-finish {
          .el-step__icon {
            &.is-text {
              border-color: var(--el-color-success);
              background-color: var(--el-color-success);
              color: white;
            }
          }
          
          .el-step__line {
            background-color: var(--el-color-success);
          }
        }
      }
      
      .el-step__main {
        .el-step__title {
          font-size: 14px;
          
          &.is-process {
            color: var(--el-color-primary);
            font-weight: 500;
          }
          
          &.is-finish {
            color: var(--el-color-success);
          }
        }
        
        .el-step__description {
          font-size: 12px;
          
          &.is-process {
            color: var(--el-color-primary-light-3);
          }
          
          &.is-finish {
            color: var(--el-color-success-light-3);
          }
        }
      }
    }
  }
  
  .guide-card {
    border-radius: 8px;
    
    :deep(.el-card__header) {
      padding: 15px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      background-color: var(--el-fill-color-light);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      
      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .step-title {
        margin-left: 12px;
        font-size: 14px;
        color: var(--el-color-primary);
        font-weight: 500;
      }
    }

    .step-content {
      
      .section-desc {
        font-size: 14px;
        color: var(--el-text-color-regular);
        margin-bottom: 20px;
      }
    }
  }
}
</style> 