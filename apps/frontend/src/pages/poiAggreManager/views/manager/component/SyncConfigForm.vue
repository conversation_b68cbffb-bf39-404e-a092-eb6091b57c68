<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="120px"
    class="sync-config-form"
    :validate-on-rule-change="false"
  >
  <el-form-item label="同步租户" prop="syncTenant">
      <el-input
        v-model.number="formData.syncTenant"
        placeholder="请输入同步租户"
        type="number"
        :min="0"
      />
    </el-form-item>
    
    <el-form-item label="同步场景" prop="syncScene">
      <el-input
        v-model.number="formData.syncScene"
        placeholder="请输入同步场景"
        type="number"
        :min="0"
      />
    </el-form-item>

    <el-form-item label="同步名称" prop="syncName">
      <el-input
        v-model="formData.syncName"
        placeholder="请输入同步名称"
      />
    </el-form-item>

    <el-form-item label="同步描述" prop="syncDescription">
      <el-input
        v-model="formData.syncDescription"
        type="textarea"
        :rows="3"
        placeholder="请输入同步描述"
      />
    </el-form-item>

    <el-form-item label="同步字段" prop="syncFieldCodes">
      <el-select
        v-model="formData.syncFieldCodes"
        multiple
        filterable
        :allow-create="!syncFieldsLoading && syncFieldOptions.length > 0"
        default-first-option
        placeholder="请选择同步字段"
        class="field-select"
        :loading="syncFieldsLoading"
        :no-data-text="syncFieldsLoading ? '加载中...' : '暂无数据'"
      >
        <el-option
          v-for="field in syncFieldOptions"
          :key="field.value"
          :label="field.label"
          :value="field.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="同步类型" prop="type">
      <el-select v-model="formData.type" placeholder="请选择同步类型" @change="handleTypeChange">
        <el-option
          v-for="option in SYNC_TYPE_OPTIONS"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="是否外部POI" prop="isPoiOuter">
      <el-select 
        v-model="formData.isPoiOuter" 
        placeholder="请选择是否外部POI"
        :disabled="formData.type === SyncTypeEnum.DTS"
      >
        <el-option
          v-for="option in POI_OUTER_OPTIONS"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
    </el-form-item>

    <!-- Mafka相关配置，仅在type=1时显示 -->
    <template v-if="formData.type === SyncTypeEnum.MAFKA">

      <el-form-item 
        label="一致性检查配置" 
        prop="totalPoiConsistencyCheck"
        class="json-form-item"
      >
        <div class="textarea-container">
          <el-input
            v-model="formData.totalPoiConsistencyCheck"
            type="textarea"
            :rows="6"
            placeholder="请输入POI一致性检查配置（JSON格式）"
            @blur="handleJsonInputBlur('totalPoiConsistencyCheck')"
            @input="() => handleJsonInput('totalPoiConsistencyCheck')"
          />
          <div class="json-visual-edit">
            <el-button type="primary" text @click="openJsonEditor('totalPoiConsistencyCheck')">
              <el-icon class="el-icon--left"><EditPen /></el-icon>
              可视化编辑
            </el-button>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="Mafka配置" prop="mafkaConsumeConfig" class="json-form-item">
        <div class="textarea-container">
          <el-input
            v-model="formData.mafkaConsumeConfig"
            type="textarea"
            :rows="6"
            placeholder="请输入Mafka消费配置（JSON格式）"
            @blur="handleJsonInputBlur('mafkaConsumeConfig')"
            @input="() => handleJsonInput('mafkaConsumeConfig')"
          />
          <div class="json-visual-edit">
            <el-button type="primary" text @click="openJsonEditor('mafkaConsumeConfig')">
              <el-icon class="el-icon--left"><EditPen /></el-icon>
              可视化编辑
            </el-button>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="查询数据配置" prop="queryDataConfig" class="json-form-item">
        <div class="textarea-container">
          <el-input
            v-model="formData.queryDataConfig"
            type="textarea"
            :rows="6"
            placeholder="请输入查询数据配置（JSON格式）"
            @blur="handleJsonInputBlur('queryDataConfig')"
            @input="() => handleJsonInput('queryDataConfig')"
          />
          <div class="json-visual-edit">
            <el-button type="primary" text @click="openJsonEditor('queryDataConfig')">
              <el-icon class="el-icon--left"><EditPen /></el-icon>
              可视化编辑
            </el-button>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="变更ID配置" prop="queryChangeIdsConfig" class="json-form-item">
        <div class="textarea-container">
          <el-input
            v-model="formData.queryChangeIdsConfig"
            type="textarea"
            :rows="6"
            placeholder="请输入变更ID配置（JSON格式）"
            @blur="handleJsonInputBlur('queryChangeIdsConfig')"
            @input="() => handleJsonInput('queryChangeIdsConfig')"
          />
          <div class="json-visual-edit">
            <el-button type="primary" text @click="openJsonEditor('queryChangeIdsConfig')">
              <el-icon class="el-icon--left"><EditPen /></el-icon>
              可视化编辑
            </el-button>
          </div>
        </div>
      </el-form-item>
    </template>

    <!-- DTS配置，仅在type=2时显示 -->
    <template v-if="formData.type === SyncTypeEnum.DTS">
      <el-form-item label="DTS配置" prop="dtsSyncConfig">
        <div class="textarea-container">
          <el-input
            v-model="formData.dtsSyncConfig"
            type="textarea"
            :rows="6"
            placeholder="请输入DTS同步配置（JSON格式）"
            @blur="handleJsonInputBlur('dtsSyncConfig')"
            @input="() => handleJsonInput('dtsSyncConfig')"
          />
          <div class="json-visual-edit">
            <el-button type="primary" text @click="openJsonEditor('dtsSyncConfig')">
              <el-icon class="el-icon--left"><EditPen /></el-icon>
              可视化编辑
            </el-button>
          </div>
        </div>
      </el-form-item>
    </template>

    <!-- 新增Lion配置描述，仅在新增时显示 -->
    <el-form-item 
      v-if="!props.initialData?.id" 
      label="Lion配置描述" 
      prop="lionConfigDescription"
    >
      <el-input
        v-model="formData.lionConfigDescription"
        type="textarea"
        :rows="3"
        placeholder="请输入Lion配置描述"
      />
    </el-form-item>
  </el-form>

  <!-- 使用JsonEditor组件 -->
  <json-editor
    ref="jsonEditorRef"
    v-model="currentJsonValue"
    @save="handleJsonEditorSave"
    @cancel="handleJsonEditorCancel"
    :readonly-root-keys="formData.type === SyncTypeEnum.DTS"
  />

  <!-- DSL可视化编辑抽屉 -->
  <el-drawer
    v-model="showOrchestrationDrawer"
    title="服务编排可视化"
    size="90%"
    :destroy-on-close="true"
    :close-on-click-modal="false"
  >
    <OrchestrationVisualizer
      v-if="visualizerDslKey"
      :dslKey="visualizerDslKey"
      @dsl-saved="handleDslSaved"
    />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { EditPen } from '@element-plus/icons-vue'
import { SYNC_TYPE_OPTIONS, POI_OUTER_OPTIONS, SyncTypeEnum } from '../../../types'
import type { SyncConfigItem, SyncMetadataItem } from '../../../types'
import { ElMessage } from 'element-plus'
import { getSyncFieldMetadataList } from '../../../request'
import JsonEditor from '../../../components/common/JsonEditor.vue'
import { getVisualDslKey } from '../../../utils/orchestrationUtils'
import OrchestrationVisualizer from '../../../views/tools/orchestration/OrchestrationVisualizer.vue'

const props = defineProps<{
  initialData?: Partial<SyncConfigItem>
}>()

/**
 * 初始化标志变量
 * 用于防止表单初始化阶段触发校验
 * 在完成初始化后延迟设置为false
 */
const isInitializing = ref(true)

const emit = defineEmits<{
  (e: 'submit', data: Partial<SyncConfigItem> | null): void
  (e: 'formChange', hasChanged: boolean): void
}>()

const formRef = ref<FormInstance>()
const formData = reactive<Partial<SyncConfigItem>>({
  syncScene: undefined,
  syncTenant: undefined,
  syncName: '',
  syncDescription: '',
  lionConfigDescription: '', // 新增字段
  syncFieldCodes: [],
  type: SyncTypeEnum.MAFKA, // 默认选择Mafka类型
  isPoiOuter: undefined, // 修改默认值为undefined
  totalPoiConsistencyCheck: '',
  mafkaConsumeConfig: '',
  queryDataConfig: '',
  queryChangeIdsConfig: '',
  dtsSyncConfig: '',
  valid: 1 // 默认值为1
})

// 记录上一次的同步类型，用于检测类型变化
const prevType = ref<number | undefined>(undefined)

// 同步字段选项
const syncFieldOptions = ref<Array<{ label: string, value: string }>>([])
const syncFieldsLoading = ref(false)

// 定义默认值常量
const DEFAULT_CONFIGS = {
  totalPoiConsistencyCheck: '',
  mafkaConsumeConfig: '{}',
  queryDataConfig: '{}',
  queryChangeIdsConfig: '',
  dtsSyncConfig: `{
  "tableName": "example_db.example_table",
  "poiIdKey": "poi_id",
  "validKey": "",
  "validTrueValue": 1,
  "validFalseValue": -1,
  "fieldDefinition": {
    "example_field1": {
      "type": "script",
      "describe": "example",
      "outerFields": [
        "field1"
      ],
      "script": "example_other_field=='100' ? 1 : 0"
    }
  }
}`,
  exampleDsl: `{
  "dsl": {
    "name": "exampleDsl",
    "description": "DSL样例（3个节点，简单链式依赖）",
    "timeout": 20,
    "tasks": [
      {
        "alias": "node1",
        "taskType": "Calculate",
        "description": "node1",
        "inputs": {
          "sourcePoiIds": "$params.poiIds"
        }
      },
      {
        "alias": "node2",
        "taskType": "Calculate",
        "description": "node2",
        "inputs": "count($node1.sourcePoiIds)"
      },
      {
        "alias": "node3",
        "taskType": "Calculate",
        "description": "node3",
        "inputs": {
          "size": "$node2"
        }
      }
    ],
    "outputs": {
      "type": "map",
      "transform": "seq.map('total', $node3.size, 'data', $node1)"
    }
  }
}`
}

// JSON编辑相关
const jsonEditorRef = ref<InstanceType<typeof JsonEditor> | null>(null)
const currentEditingField = ref<string>('')
const currentJsonValue = ref<string>('')

// 新增状态
const showOrchestrationDrawer = ref(false)
const visualizerDslKey = ref('')

// 获取同步字段选项
const getSyncFields = async () => {
  syncFieldsLoading.value = true
  try {
    // 调用request/index.ts中的方法获取字段列表
    const res = await getSyncFieldMetadataList({
      pageNo: 1,
      pageSize: 1000, // 获取足够多的字段
      conditions: {}  // 无筛选条件，获取所有字段
    })
    
    if (res.code === 0 && res.data?.records) {
      // 转换响应数据为下拉选项格式
      // 只使用字段code作为标签和值
      syncFieldOptions.value = res.data.records.map((item: SyncMetadataItem) => ({
        label: item.fieldCode ?? '',
        value: item.fieldCode ?? ''
      }))
    } else {
      ElMessage.error(res.message || '获取同步字段失败')
    }
  } catch (error) {
    console.error('获取同步字段出错:', error)
    ElMessage.error('获取同步字段失败')
  } finally {
    syncFieldsLoading.value = false
  }
}

// 打开JSON编辑器
const openJsonEditor = (field: string) => {
  currentEditingField.value = field;
  
  // 特殊处理查询数据配置和查询最近变更ID配置
  if (field === 'queryDataConfig' || field === 'queryChangeIdsConfig') {
    try {
      let fieldValue = formData[field as keyof typeof formData];
      if (fieldValue === '' || fieldValue === '{}') {
        fieldValue = DEFAULT_CONFIGS.exampleDsl;
      }
      if (fieldValue && typeof fieldValue === 'string') {
        try {
          // 使用正则表达式提取dsl字段的值，更健壮的方式
          const dslRegex = /"dsl"\s*:\s*("(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\})/;
          const match = fieldValue.match(dslRegex);
          
          if (match && match[1]) {
            let dslValue = match[1];
            
            // 如果dsl值是用引号包裹的字符串，需要去掉引号并进行转义处理
            if ((dslValue.startsWith('"') && dslValue.endsWith('"')) || 
                (dslValue.startsWith("'") && dslValue.endsWith("'"))) {
              // 去掉两端的引号
              dslValue = dslValue.substring(1, dslValue.length - 1);
              // 处理转义字符
              dslValue = dslValue.replace(/\\"/g, '"').replace(/\\n/g, '\n').replace(/\\t/g, '\t').replace(/\\\\/g, '\\');
            }
            
            // 使用getVisualDslKey代替goToOrchestrationVisualizer
            dslValue = dslValue.replace(/( {2}){1,}/g, (match) => ' '.repeat(match.length - 2));
            visualizerDslKey.value = getVisualDslKey(dslValue) || '';
            if (visualizerDslKey.value) {
              // 打开抽屉展示组件
              showOrchestrationDrawer.value = true;
            } else {
              ElMessage.error('生成DSL可视化Key失败，请稍后重试');
              // 回退到普通JSON编辑器
              openRegularJsonEditor(field);
            }
          } else {
            // 未找到dsl字段或提取失败，尝试JSON解析
            try {
              const jsonObj = JSON.parse(fieldValue);
              if (jsonObj && jsonObj.dsl) {
                // 使用getVisualDslKey代替goToOrchestrationVisualizer
                visualizerDslKey.value = getVisualDslKey(jsonObj.dsl) || '';
                if (visualizerDslKey.value) {
                  // 打开抽屉展示组件
                  showOrchestrationDrawer.value = true;
                } else {
                  ElMessage.error('生成DSL可视化Key失败，请稍后重试');
                  // 回退到普通JSON编辑器
                  openRegularJsonEditor(field);
                }
              } else {
                ElMessage.warning('配置中无DSL属性，无法使用可视化编辑');
                // 如果没有dsl字段，回退到普通JSON编辑器
                openRegularJsonEditor(field);
              }
            } catch (jsonError) {
              ElMessage.warning('配置格式不正确，将使用普通JSON编辑器');
              openRegularJsonEditor(field);
            }
          }
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          ElMessage.warning('配置格式不正确，将使用普通JSON编辑器');
          // 解析失败时，回退到普通JSON编辑器
          openRegularJsonEditor(field);
        }
      } else {
        // 字段为空或非字符串，提示创建新配置
        ElMessage.info('配置为空，将使用普通JSON编辑器创建配置');
        openRegularJsonEditor(field);
      }
    } catch (error) {
      console.error('处理可视化编辑失败:', error);
      // 出错时，回退到普通JSON编辑器
      openRegularJsonEditor(field);
    }
    return;
  }
  
  // 其他字段使用普通JSON编辑器
  openRegularJsonEditor(field);
};

// 普通JSON编辑器打开方法
const openRegularJsonEditor = (field: string) => {
  try {
    const fieldValue = formData[field as keyof typeof formData];
    if (fieldValue && typeof fieldValue === 'string') {
      // 处理JSON字符串中的转义字符
      const processedValue = fieldValue.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
        // 替换字符串中的换行符、制表符和其他需要转义的字符
        return match
          .replace(/\n/g, '\\n')
          .replace(/\t/g, '\\t');
      });
      
      // 尝试解析JSON格式，确保格式正确
      JSON.parse(processedValue);
      // 设置当前JSON值为处理后的值
      currentJsonValue.value = processedValue;
      // 打开编辑器 - 将处理后的数据传递给JSON编辑器
      nextTick(() => {
        jsonEditorRef.value?.open();
      });
    } else {
      // 如果字段为空，设置空对象
      currentJsonValue.value = '{}';
      nextTick(() => {
        jsonEditorRef.value?.open();
      });
    }
  } catch (error) {
    // JSON格式错误时，仍然设置值并打开编辑器
    // 由JsonEditor组件内部处理格式错误
    const fieldValue = formData[field as keyof typeof formData];
    if (fieldValue && typeof fieldValue === 'string') {
      // 即使JSON格式不正确，仍然尝试处理转义字符
      const processedValue = fieldValue.replace(/"((?:\\.|[^"\\])*)"/g, (match) => {
        return match
          .replace(/\n/g, '\\n')
          .replace(/\t/g, '\\t');
      });
      currentJsonValue.value = processedValue;
    } else {
      currentJsonValue.value = fieldValue && typeof fieldValue === 'string' ? fieldValue : '{}';
    }
    // 移除警告消息，由 JsonEditor 组件内部处理
    // ElMessage.warning('JSON格式有误，编辑器将尝试修复或显示错误信息');
    nextTick(() => {
      jsonEditorRef.value?.open();
    });
  }
};

// 处理JSON编辑器保存事件
const handleJsonEditorSave = (jsonString: string) => {
  if (currentEditingField.value && formData.hasOwnProperty(currentEditingField.value)) {
    try {
      // 对查询数据配置和查询最近变更ID配置进行特殊处理
      if (currentEditingField.value === 'queryDataConfig' || currentEditingField.value === 'queryChangeIdsConfig') {
        // 检查JSON是否含有dsl字段
        const hasDslField = /"dsl"\s*:/i.test(jsonString);
        
        if (!hasDslField) {
          // 如果没有dsl字段，将输入内容包装为dsl字段的值
          try {
            // 尝试解析JSON字符串，确保格式正确
            JSON.parse(jsonString);
            jsonString = jsonString.replace(/\n/g, '\n\t');
            // 格式正确，将其包装为dsl字段
            const wrappedJson = `{\n  "dsl": ${jsonString}\n}`;
            (formData as any)[currentEditingField.value] = wrappedJson;
            
            ElMessage.success('已自动将输入内容设置为DSL字段的值');
          } catch (parseError) {
            // 如果不是有效的JSON，直接包装为字符串
            jsonString = jsonString.replace(/\n/g, '\n\t');
            const wrappedJson = `{\n  "dsl": ${JSON.stringify(jsonString)}\n}`;
            (formData as any)[currentEditingField.value] = wrappedJson;
            
            ElMessage.warning('输入的JSON格式有误，已作为字符串包装到DSL字段');
          }
        } else {
          // 有dsl字段，直接保存
          (formData as any)[currentEditingField.value] = jsonString;
        }
      } else {
        // 其他字段直接保存
        (formData as any)[currentEditingField.value] = jsonString;
      }
      
      // 触发表单验证
      handleJsonInputBlur(currentEditingField.value);
      
    } catch (error) {
      console.error('保存JSON配置失败:', error);
    }
  }
};

// 处理JSON编辑器取消事件
const handleJsonEditorCancel = () => {
  // 重置编辑状态
  currentEditingField.value = '';
  
  // 可以在这里添加其他取消时的逻辑
  // 不显示通知消息，避免重复
};

// 用于表单防抖校验的工具函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null
  return function(this: any, ...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, delay) as unknown as number
  }
}

// 动态验证规则
const rules = computed<FormRules>(() => {
  // 记录上一次的类型，用于判断类型是否变化
  const currentType = formData.type
  
  // 如果类型发生变化，记录并标记为初始化阶段
  if (prevType.value !== currentType && currentType !== undefined) {
    prevType.value = currentType
    // 类型变化时，临时进入初始化状态，避免自动验证
    if (!isInitializing.value) {
      isInitializing.value = true
      setTimeout(() => {
        isInitializing.value = false
      }, 300)
    }
  }

  // 基础验证规则
  const baseRules: FormRules = {
    syncTenant: [
      { required: true, message: '请输入同步租户', trigger: 'blur' },
      { type: 'number', message: '同步租户必须为数字', trigger: 'blur' }
    ],
    syncScene: [
      { required: true, message: '请输入同步场景', trigger: 'blur' },
      { type: 'number', message: '同步场景必须为数字', trigger: 'blur' }
    ],
    syncName: [
      { required: true, message: '请输入同步名称', trigger: 'blur' },
      { max: 50, message: '同步名称不能超过50个字符', trigger: 'change' }
    ],
    syncDescription: [
      { required: true, message: '请输入同步描述', trigger: 'blur' },
      { max: 200, message: '同步描述不能超过200个字符', trigger: 'change' }
    ],
    syncFieldCodes: [
      { required: true, message: '请选择同步字段', trigger: 'change' },
      { type: 'array', min: 1, message: '至少选择一个同步字段', trigger: 'change' }
    ],
    type: [
      { required: true, message: '请选择同步类型', trigger: 'blur' }
    ],
    isPoiOuter: [
      { required: true, message: '请选择是否外部POI', trigger: 'blur' }
    ],
    lionConfigDescription: [
      { required: !props.initialData?.id, message: '请输入Lion配置描述', trigger: 'blur' },
      { max: 200, message: 'Lion配置描述不能超过200个字符', trigger: 'change' }
    ]
  }

  // 根据同步类型添加不同的验证规则
  if (formData.type === SyncTypeEnum.MAFKA) {
    // Mafka类型时，相关配置必填
    baseRules.mafkaConsumeConfig = [
      // { required: true, message: '请输入Mafka配置', trigger: 'manual' },
      // { validator: jsonValidator, trigger: 'manual' }
      { required: true, message: '请输入Mafka配置', trigger: 'manual' }
    ]
    baseRules.queryDataConfig = [
      // { required: true, message: '请输入查询数据配置', trigger: 'manual' },
      // { validator: jsonValidator, trigger: 'manual' }
      { required: true, message: '请输入查询数据配置', trigger: 'manual' }
    ]
    baseRules.queryChangeIdsConfig = [
      // { required: true, message: '请输入变更ID配置', trigger: 'manual' },
      // { validator: jsonValidator, trigger: 'manual' }
      // { required: true, message: '请输入变更ID配置', trigger: 'manual' }
    ]
    // 添加POI一致性检查配置的验证规则
    if (formData.isPoiOuter === 1) {
      baseRules.totalPoiConsistencyCheck = []
    }
  } else if (formData.type === SyncTypeEnum.DTS) {
    // DTS类型时，DTS配置必填
    baseRules.dtsSyncConfig = [
      // { required: true, message: '请输入DTS配置', trigger: 'manual' },
      // { validator: jsonValidator, trigger: 'manual' }
      { required: true, message: '请输入DTS配置', trigger: 'manual' }
    ]
  }

  return baseRules
})

// 处理同步类型变更
const handleTypeChange = (value: number) => {
  // 初始化阶段不触发校验
  if (isInitializing.value) return

  // 临时设置初始化状态，防止类型切换时触发校验
  isInitializing.value = true
  
  // 更新上一次类型
  prevType.value = value

  // 根据同步类型设置isPoiOuter的值和清理不相关配置
  if (value === SyncTypeEnum.DTS) {
    formData.isPoiOuter = 0 // DTS类型设置为否
    // 清空Mafka相关配置
    formData.totalPoiConsistencyCheck = DEFAULT_CONFIGS.totalPoiConsistencyCheck
    formData.mafkaConsumeConfig = DEFAULT_CONFIGS.mafkaConsumeConfig
    formData.queryDataConfig = DEFAULT_CONFIGS.queryDataConfig
    formData.queryChangeIdsConfig = DEFAULT_CONFIGS.queryChangeIdsConfig
  } else {
    formData.isPoiOuter = undefined // Mafka类型设置为空
    // 清空DTS相关配置
    formData.dtsSyncConfig = DEFAULT_CONFIGS.dtsSyncConfig
  }
  
  // 使用setTimeout恢复验证状态，确保DOM更新完成后再允许验证
  setTimeout(() => {
    isInitializing.value = false
  }, 300)
}

// 监听表单数据变化
watch(
  () => formData,
  () => {
    const hasChanged = JSON.stringify(formData) !== JSON.stringify(props.initialData)
    emit('formChange', hasChanged)
  },
  { deep: true }
)

// 监听syncScene和syncDescription变化，自动设置lionConfigDescription
watch(
  [() => formData.syncScene, () => formData.syncDescription],
  ([newSyncScene, newSyncDescription]) => {
    if ((!newSyncScene && !newSyncDescription) || props.initialData?.id) return
    
    // 自动设置lionConfigDescription为"【同步配置】+syncScene-syncDescription"
    formData.lionConfigDescription = `【同步配置】${newSyncScene || ''}-${newSyncDescription || ''}`
  },
  { deep: true }
)

// 初始化表单数据
const initFormData = () => {
  // 先设置初始化状态，防止触发验证
  isInitializing.value = true
  
  if (props.initialData) {
    // 使用nextTick确保DOM更新后再赋值，避免触发验证
    nextTick(() => {
      // 深拷贝初始数据，避免引用问题
      const initialData = JSON.parse(JSON.stringify(props.initialData))
      // 确保isPoiOuter是数字类型
      if (initialData.isPoiOuter !== undefined) {
        initialData.isPoiOuter = Number(initialData.isPoiOuter)
      }
      // 确保JSON字段有默认值
      Object.keys(DEFAULT_CONFIGS).forEach(key => {
        if (!initialData[key]) {
          initialData[key] = DEFAULT_CONFIGS[key as keyof typeof DEFAULT_CONFIGS]
        }
      })
      Object.assign(formData, initialData)
      // 同步prevType
      prevType.value = formData.type
      // 延迟结束初始化状态
      setTimeout(() => {
        isInitializing.value = false
      }, 300)
    })
  } else {
    // 如果没有初始数据，默认设置为Mafka类型
    formData.type = SyncTypeEnum.MAFKA
    formData.isPoiOuter = undefined // 默认设置为空
    formData.valid = 1 // 默认设置为1
    // 设置JSON字段默认值
    Object.keys(DEFAULT_CONFIGS).forEach(key => {
      ;(formData as any)[key] = DEFAULT_CONFIGS[key as keyof typeof DEFAULT_CONFIGS]
    })
    // 同步prevType
    prevType.value = formData.type
    // 延迟结束初始化状态
    setTimeout(() => {
      isInitializing.value = false
    }, 300)
  }
}

// 重置表单
const resetForm = () => {
  // 先临时设置为初始化状态，防止重置触发校验
  isInitializing.value = true
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    syncScene: undefined,
    syncTenant: undefined,
    syncName: '',
    syncDescription: '',
    lionConfigDescription: '', // 新增字段
    syncFieldCodes: [],
    type: SyncTypeEnum.MAFKA, // 重置为默认Mafka类型
    isPoiOuter: undefined, // 重置为undefined
    totalPoiConsistencyCheck: DEFAULT_CONFIGS.totalPoiConsistencyCheck,
    mafkaConsumeConfig: DEFAULT_CONFIGS.mafkaConsumeConfig,
    queryDataConfig: DEFAULT_CONFIGS.queryDataConfig,
    queryChangeIdsConfig: DEFAULT_CONFIGS.queryChangeIdsConfig,
    dtsSyncConfig: DEFAULT_CONFIGS.dtsSyncConfig,
    valid: 1 // 重置为默认值1
  })
  
  // 同步prevType
  prevType.value = formData.type
  
  // 重置完成后恢复校验状态
  setTimeout(() => {
    isInitializing.value = false
  }, 300)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await validate()
    if (valid) {
      // 检查表单是否有变化
      const hasChanged = JSON.stringify(formData) !== JSON.stringify(props.initialData)
      
      if (hasChanged) {
        // 创建提交数据的副本，根据同步类型清理不相关的配置字段
        const submitData = { ...formData }
        
        if (submitData.type === SyncTypeEnum.MAFKA) {
          // Mafka类型：清空DTS相关配置
          submitData.dtsSyncConfig = ''
        } else if (submitData.type === SyncTypeEnum.DTS) {
          // DTS类型：清空Mafka相关配置
          submitData.totalPoiConsistencyCheck = ''
          submitData.mafkaConsumeConfig = ''
          submitData.queryDataConfig = ''
          submitData.queryChangeIdsConfig = ''
        }
        
        emit('submit', submitData)
      } else {
        emit('submit', null)
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 验证表单
const validate = () => {
  if (isInitializing.value) {
    return Promise.resolve(true)
  }
  
  return new Promise((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      resolve(valid)
    })
  })
}

// 获取表单数据（根据同步类型清理不相关字段）
const getFormData = () => {
  const data = { ...formData }
  
  if (data.type === SyncTypeEnum.MAFKA) {
    // Mafka类型：清空DTS相关配置
    data.dtsSyncConfig = ''
  } else if (data.type === SyncTypeEnum.DTS) {
    // DTS类型：清空Mafka相关配置
    data.totalPoiConsistencyCheck = ''
    data.mafkaConsumeConfig = ''
    data.queryDataConfig = ''
    data.queryChangeIdsConfig = ''
  }
  
  return data
}

// 暴露方法给父组件
defineExpose({
  validate: formRef.value?.validate,
  resetForm: resetForm,
  submitForm: submitForm,
  getFormData: getFormData,
  initFormData
})

onMounted(() => {
  initFormData()
  // 加载同步字段选项
  getSyncFields()
})

// 添加输入框的事件处理函数，手动触发验证
const handleJsonInputBlur = (field: string) => {
  // 在初始化阶段不进行验证
  if (isInitializing.value) return
  
  // 手动验证当前字段
  nextTick(() => {
    formRef.value?.validateField([field])
  })
}

// 添加输入框的输入事件处理函数，使用防抖验证
const handleJsonInput = debounce((field: string) => {
  // 在初始化阶段不进行验证
  if (isInitializing.value) return
  
  // 手动验证当前字段
  nextTick(() => {
    formRef.value?.validateField([field])
  })
}, 500)

// 处理DSL可视化编辑器保存
const handleDslSaved = (dsl: string) => {
  if (!currentEditingField.value) return
  try {
    // 只处理queryDataConfig和queryChangeIdsConfig
    if (currentEditingField.value === 'queryDataConfig' || currentEditingField.value === 'queryChangeIdsConfig') {
      // 包装为{"dsl": ...}
      dsl = dsl.replace(/\n/g, '\n  ');
      const dslWrapper = '{\n  "dsl": '+ dsl + '\n}';
      (formData as any)[currentEditingField.value] = dslWrapper
      showOrchestrationDrawer.value = false
      handleJsonInputBlur(currentEditingField.value)
    }
  } catch (e) {
    console.error('处理DSL保存失败:', e);
    ElMessage.error('处理DSL保存失败，请手动编辑配置');
    showOrchestrationDrawer.value = false
  }
}
</script>

<style lang="scss" scoped>
.sync-config-form {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  max-height: 60vh; // 设置固定高度，减去头部和底部padding
  overflow-y: auto; // 添加垂直滚动条
  overflow-x: hidden; // 隐藏水平滚动条

  // 美化滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
  
  .field-select {
    width: 100%;
  }

  // JSON表单项样式
  :deep(.json-form-item) {
    .el-form-item__content {
      position: relative;
    }
  }

  // 文本域容器样式
  .textarea-container {
    position: relative;
    width: 100%;
    
    .el-textarea {
      width: 100%;
    }
    
    // JSON编辑按钮样式
    .json-visual-edit {
      position: absolute;
      top: 4px;
      right: 10px;
      z-index: 2;
      
      .el-button {
        font-size: 14px;
        padding: 4px 8px;
        background-color: rgba(255, 255, 255, 0.8);
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 22px;
    
    .el-form-item__label {
      font-weight: 500;
    }
    
    .el-input,
    .el-select {
      width: 100%;
    }
    
    .el-textarea__inner {
      font-family: monospace;
      line-height: 1.5;
      padding: 8px 12px;
      font-size: 13px;
      resize: vertical;
      min-height: 80px;
      background-color: #fff;
      
      &:focus {
        background-color: #fff;
        border-color: #409eff;
      }
    }
  }
}
</style> 