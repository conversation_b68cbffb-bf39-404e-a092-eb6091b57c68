<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="metadata-form">
    <el-form-item label="字段Code" prop="fieldCode">
      <el-input v-model="form.fieldCode" placeholder="请输入字段Code（命名规范：xxx_yyy_zzz）" :disabled="props.initialData?.id" />
    </el-form-item>
    <el-form-item label="字段Property" prop="fieldProperty">
      <el-input v-model="form.fieldProperty" placeholder="请输入字段属性（命名规范：xxxYyyZzz）" :disabled="props.initialData?.id" />
    </el-form-item>
    <el-form-item label="字段名称" prop="fieldName">
      <el-input v-model="form.fieldName" placeholder="请输入字段名称" />
    </el-form-item>
    <el-form-item label="字段描述" prop="description">
      <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入字段描述" />
    </el-form-item>
    <el-form-item label="字段类型" prop="type">
      <el-select v-model="form.type" placeholder="请选择字段类型">
        <el-option v-for="type in FIELD_TYPE_OPTIONS" :key="type.value" :label="type.label" :value="type.value" />
      </el-select>
    </el-form-item>
    <el-form-item label="默认值" prop="defaultValue">
      <el-input v-model="form.defaultValue" placeholder="请输入默认值" />
    </el-form-item>
    <el-form-item label="本地/动态" prop="handlerType">
      <el-switch
        v-model="form.handlerType"
        :active-value="1"
        :inactive-value="0"
        active-text="动态"
        inactive-text="本地"
        :disabled="!!props.initialData?.id"
      />
    </el-form-item>
    <template v-if="form.handlerType === 1">
      <el-form-item label="依赖字段" prop="dependentFields">
        <el-select
          v-model="form.dependentFields"
          multiple
          filterable
          allow-create
          default-first-option
          :reserve-keyword="false"
          placeholder="请选择或输入依赖字段"
          style="width: 100%"
          @keyup.enter="handleDependentFieldInput"
        >
          <el-option
            v-for="field in availableDependentFields"
            :key="field"
            :label="field"
            :value="field"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否同步字段" prop="syncedField">
        <el-switch v-model="form.syncedField" :disabled="props.initialData?.id" />
      </el-form-item>
      <el-form-item v-if="!props.initialData?.id" label="Lion配置描述" prop="lionConfigDescription">
        <el-input v-model="form.lionConfigDescription" type="textarea" :rows="2" placeholder="请输入Lion配置描述" />
      </el-form-item>
    </template>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { FieldMetadataItem, FieldType } from '../../../types'
import { FIELD_TYPE_OPTIONS } from '../../../types'

const props = defineProps<{
  initialData?: Partial<FieldMetadataItem>
  existingFields?: string[]
}>()

const emit = defineEmits<{
  (e: 'submit', data: Partial<FieldMetadataItem> | null): void
}>()

const formRef = ref<FormInstance>()
// 添加一个标志位来控制是否应该更新默认值
const shouldUpdateDefaultValue = ref(false)

// 表单数据
const form = reactive<{
  id: number | undefined
  fieldCode: string
  fieldProperty: string
  fieldName: string
  description: string
  type: FieldType | undefined
  defaultValue: string
  dependentFields: string[]
  syncedField: boolean
  valid: 0 | 1
  opName: string | undefined
  opMis: string | undefined
  lionConfigDescription: string | undefined
  handlerType: number
}>({
  id: undefined,
  fieldCode: '',
  fieldProperty: '',
  fieldName: '',
  description: '',
  type: undefined,
  defaultValue: '',
  dependentFields: [],
  syncedField: false,
  valid: 1,
  opName: '',
  opMis: '',
  lionConfigDescription: '',
  handlerType: 1
})

// 表单验证规则
const rules = reactive<FormRules>({
  fieldCode: [
    { required: true, message: '请输入字段Code', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段Code只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' }
  ],
  fieldName: [
    { required: true, message: '请输入字段名称', trigger: 'blur' }
  ],
  fieldProperty: [
    { required: true, message: '请输入字段属性', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择字段类型', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }
        if (!FIELD_TYPE_OPTIONS.map(option => option.value).includes(value)) {
          callback(new Error('无效的字段类型'))
          return
        }
        callback()
      },
      trigger: 'change'
    }
  ],
  defaultValue: [
    { required: true, message: '请输入默认值', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!form.type) {
          callback(new Error('请先选择字段类型'))
          return
        }

        // 如果值为null，直接通过校验
        if (value === 'null') {
          callback()
          return
        }

        // 根据字段类型校验默认值格式
        switch (form.type) {
          case '1': // String
            // 检查是否包含双引号
            if (!value.startsWith('"') || !value.endsWith('"')) {
              callback(new Error('String类型的默认值需要用双引号包裹'))
              return
            }
            // 检查双引号中间的内容是否合法
            const content = value.slice(1, -1)
            if (content.includes('"')) {
              callback(new Error('String类型的默认值中不能包含双引号'))
              return
            }
            break
          case '2': // Boolean
            if (value !== 'true' && value !== 'false') {
              callback(new Error('Boolean类型的默认值只能是true或false'))
              return
            }
            break
          case '3': // Long
            if (!/^-?\d+$/.test(value)) {
              callback(new Error('Long类型的默认值必须是整数'))
              return
            }
            break
          case '4': // Double
            if (!/^-?\d*\.?\d+$/.test(value)) {
              callback(new Error('Double类型的默认值必须是数字'))
              return
            }
            break
        }
        callback()
      },
      trigger: 'blur'
    }
  ],
  handlerType: [
    { required: true, message: '请选择处理器类型', trigger: 'change' },
    {
      type: 'number',
      validator: (rule, value) => {
        if (value !== 0 && value !== 1) {
          return new Error('处理器类型只能为0或1')
        }
        return true
      },
      trigger: 'change'
    }
  ],
  syncedField: [
    {
      required: true,
      message: '请选择是否同步字段',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.handlerType === 1 && value === undefined) {
          callback(new Error('动态处理器必须设置是否同步字段'))
          return
        }
        callback()
      }
    }
  ],
  lionConfigDescription: [
    {
      required: true,
      message: '请输入Lion配置描述',
      trigger: 'blur',
      validator: (rule: any, value: any, callback: any) => {
        if (props.initialData?.id || form.handlerType === 0) {
          callback()
          return
        }
        if (!value || !value.trim()) {
          callback(new Error('请输入Lion配置描述'))
          return
        }
        callback()
      }
    }
  ],
  valid: [
    { required: true, message: '请选择有效性', trigger: 'change' },
    {
      type: 'number',
      validator: (rule, value) => {
        if (value !== 0 && value !== 1) {
          return new Error('有效性只能为0或1')
        }
        return true
      },
      trigger: 'change'
    }
  ]
})

// 可选的依赖字段列表
const availableDependentFields = computed(() => {
  if (!props.existingFields) return []
  return props.existingFields.filter(field => field !== form.fieldCode)
})

// 监听initialData变化
watch(
  () => props.initialData,
  (newVal) => {
    if (newVal) {
      // 初始化表单数据
      const formData = {
        id: newVal.id,
        fieldCode: newVal.fieldCode || '',
        fieldProperty: newVal.fieldProperty || '',
        fieldName: newVal.fieldName || '',
        description: newVal.description || '',
        type: newVal.type,
        defaultValue: newVal.defaultValue || '',
        dependentFields: newVal.dependentFields || [],
        syncedField: newVal.syncedField ?? false,
        valid: newVal.valid ?? 1,
        opName: newVal.opName,
        opMis: newVal.opMis,
        lionConfigDescription: newVal.lionConfigDescription,
        handlerType: newVal.handlerType ?? 1
      }
      
      // 处理String类型的默认值
      if (newVal.type === '1') {
        // 如果是空字符串，直接设置为双引号
        if (formData.defaultValue === '') {
          formData.defaultValue = '""'
        } else if (formData.defaultValue && formData.defaultValue === 'null') {
          formData.defaultValue = 'null'
        }
        // 如果已经有值但不是以双引号包裹，则添加双引号
        else if (formData.defaultValue && (!formData.defaultValue.startsWith('"') || !formData.defaultValue.endsWith('"'))) {
          formData.defaultValue = '"' + formData.defaultValue + '"'
        }
      }

      // 使用nextTick确保在DOM更新后再设置表单数据
      nextTick(() => {
        // 设置表单数据前，将标志位设置为false
        shouldUpdateDefaultValue.value = false
        Object.assign(form, formData)
        // 设置完成后，将标志位设置为true，允许后续的type变化触发默认值更新
        setTimeout(() => {
          shouldUpdateDefaultValue.value = true
        }, 100)
      })
    } else {
      // 重置表单数据
      Object.assign(form, {
        id: undefined,
        fieldCode: '',
        fieldProperty: '',
        fieldName: '',
        description: '',
        type: undefined,
        defaultValue: '',
        dependentFields: [],
        syncedField: false,
        valid: 1,
        opName: '',
        opMis: '',
        lionConfigDescription: '',
        handlerType: 1
      })
    }
  },
  { deep: true, immediate: true }
)

// 监听字段类型变化，设置对应的默认值
watch(
  () => form.type,
  (newType, oldType) => {
    // 如果不应该更新默认值，直接返回
    console.log('shouldUpdateDefaultValue.value', shouldUpdateDefaultValue.value)
    if (!shouldUpdateDefaultValue.value) {
      return
    }

    // 如果是编辑模式，且类型发生了变化，才更新默认值
    if (props.initialData?.id && newType !== oldType) {
      switch (newType) {
        case '1': // String
          form.defaultValue = '""'
          break
        case '2': // Boolean
          form.defaultValue = 'false'
          break;
        case '3': // Long
          form.defaultValue = '0'
          break;
        case '4': // Double
          form.defaultValue = '0.0'
          break;
        default:
          form.defaultValue = ''
      }
      return
    }

    // 新增模式下，直接设置对应类型的默认值
    if (!props.initialData?.id) {
      switch (newType) {
        case '1': // String
          form.defaultValue = '""'
          break
        case '2': // Boolean
          form.defaultValue = 'false'
          break
        case '3': // Long
          form.defaultValue = '0'
          break
        case '4': // Double
          form.defaultValue = '0.0'
          break
        default:
          form.defaultValue = ''
      }
    }
  }
)

// 监听fieldCode变化，自动生成fieldProperty
watch(
  () => form.fieldCode,
  (newValue, oldValue) => {
    if (!newValue || props.initialData?.id) return
    
    // 只有当fieldProperty为空或者是由fieldCode自动生成的时候才更新
    if (!form.fieldProperty || (form.fieldProperty === convertFieldCodeToProperty(oldValue))) {
      form.fieldProperty = convertFieldCodeToProperty(newValue)
    }
  }, { deep: true }
)

// 监听fieldProperty变化，自动生成fieldCode
watch(
  () => form.fieldProperty,
  (newValue, oldValue) => {
    if (!newValue || props.initialData?.id) return
    
    // 只有当fieldCode为空或者是由fieldProperty自动生成的时候才更新
    if (!form.fieldCode || (form.fieldCode === convertPropertyToFieldCode(oldValue))) {
      form.fieldCode = convertPropertyToFieldCode(newValue)
    }
  }, { deep: true }
)

// 监听fieldProperty和description变化，自动设置lionConfigDescription
watch(
  [() => form.fieldProperty, () => form.description],
  ([newFieldProperty, newDescription]) => {
    if ((!newFieldProperty && !newDescription) || props.initialData?.id) return
    
    // 自动设置lionConfigDescription为"【字段元数据】+fieldProperty-description"
    form.lionConfigDescription = `【字段元数据】${newFieldProperty || ''}-${newDescription || ''}`
  },
  { deep: true }
)

// 监听处理器类型变化
watch(
  () => form.handlerType,
  (newValue) => {
    if (newValue === 0) {
      // 如果是本地处理器，强制清空相关字段
      form.dependentFields = []
      form.syncedField = false
      form.lionConfigDescription = ''
    }
  },
  { immediate: true }
)

// 辅助函数：将fieldCode转换为fieldProperty
const convertFieldCodeToProperty = (fieldCode: string): string => {
  if (!fieldCode) return ''
  return fieldCode
    .split('_')
    .map((word, index) => {
      if (index === 0) {
        return word.toLowerCase()
      }
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    })
    .join('')
}

// 辅助函数：将fieldProperty转换为fieldCode
const convertPropertyToFieldCode = (fieldProperty: string): string => {
  if (!fieldProperty) return ''
  return fieldProperty
    .replace(/([A-Z])/g, '_$1')
    .toLowerCase()
    .replace(/^_/, '')
}

// 检查表单是否有变更
const hasFormChanged = () => {
  // 如果是新增操作，只要有数据就认为有变更
  if (!props.initialData?.id) {
    return true
  }

  // 比较基本字段
  const fields = [
    'fieldCode',
    'fieldProperty',
    'fieldName',
    'description',
    'type',
    'defaultValue',
    'syncedField',
    'lionConfigDescription',
    'valid'
  ]

  for (const field of fields) {
    if (form[field as keyof typeof form] !== props.initialData[field as keyof typeof props.initialData]) {
      return true
    }
  }

  // 比较依赖字段列表
  const currentDependentFields = form.dependentFields || []
  const originalDependentFields = props.initialData.dependentFields || []

  if (currentDependentFields.length !== originalDependentFields.length) {
    return true
  }

  // 检查每个依赖字段是否都存在于原始数据中
  for (const field of currentDependentFields) {
    if (!originalDependentFields.includes(field)) {
      return true
    }
  }

  return false
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate(async (valid) => {
      if (valid) {
        // 无论是否有变更，都发送请求
        emit('submit', { ...form })
      } else {
        // 表单校验失败，发送特殊值
        emit('submit', { valid: 0 })
      }
    })
  } catch (error) {
    console.error('表单校验失败:', error)
    emit('submit', { valid: 0 })
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 处理依赖字段输入
const handleDependentFieldInput = (event: KeyboardEvent) => {
  const input = event.target as HTMLInputElement
  const value = input.value.trim()
  
  if (value && !form.dependentFields.includes(value)) {
    form.dependentFields.push(value)
    input.value = ''
  }
}

defineExpose({
  submitForm,
  resetForm,
  hasFormChanged
})
</script>

<style lang="scss" scoped>
.metadata-form {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
}
</style>