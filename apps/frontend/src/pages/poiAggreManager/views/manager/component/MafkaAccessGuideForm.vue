<template>
  <div class="mafka-access-guide">
    <!-- 步骤导航条 -->
    <el-steps 
      :active="actualStep" 
      finish-status="success" 
      class="mafka-steps"
      process-status="process"
      align-center
    >
      <el-step title="Mafka配置信息" description="配置Mafka基本信息" />
      <el-step title="填写同步字段信息" description="配置需要同步的字段内容" />
      <el-step title="填写同步配置信息" description="设置同步规则与配置" />
      <el-step title="查询功能配置" description="配置是否提供查询及字段" />
      <el-step title="一致性校验任务配置" description="配置Crane数据计算任务" />
    </el-steps>

    <!-- 步骤内容区 -->
    <el-card shadow="never" class="guide-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">Mafka 接入指引</span>
        </div>
      </template>
      
      <!-- 步骤1：Mafka配置信息 -->
      <div v-show="actualStep === 0" class="step-content">
        <MafkaConfigAccessGuideForm
          @valid-change="handleValidChange"
          @mafka-config-update="handleMafkaConfigUpdate"
          ref="mafkaConfigFormRef"
        />
      </div>
      
      <!-- 步骤2：填写同步字段信息 -->
      <div v-show="actualStep === 1" class="step-content">
        <SyncFieldsAccessGuideForm 
          @valid-change="handleValidChange"
          @sync-fields-data="handleSyncFieldsDataUpdate"
          ref="syncFieldsFormRef"
        />
      </div>
      
      <!-- 步骤3：填写同步配置信息 -->
      <div v-show="actualStep === 2" class="step-content">
        <SyncConfigAccessGuideForm 
          :config-form="configForm" 
          :access-way="'mafka'"
          :disable-sync-fields-edit="actualStep === 2"
          :show-sync-type="false"
          :show-poi-outer="true"
          @valid-change="handleValidChange"
          @sync-config-update="handleSyncConfigUpdate"
          ref="syncConfigFormRef"
        />
      </div>
      
      <!-- 步骤4：查询功能配置 -->
      <div v-show="actualStep === 3" class="step-content">
        <QueryFieldsAccessGuideForm
          v-model:provide-query-function="provideQueryFunction"
          :query-field-form="queryFieldForm"
          :lock-synced-field="true"
          :default-synced-field="false"
          :show-dependent-fields="true"
          @valid-change="handleValidChange"
          @query-fields-data="handleQueryFieldsDataUpdate"
          @update:provide-query-function="(value: boolean) => emit('provide-query-function-update', value)"
          ref="queryFieldsFormRef"
        />
      </div>

      <!-- 步骤5：Crane配置 -->
      <div v-show="actualStep >= 4" class="step-content">
        <CraneConfigAccessGuideForm
          :has-completed-final-step="hasCompletedFinalStep"
          @valid-change="handleValidChange"
          @complete-task="handleCraneTaskComplete"
          @crane-url-update="handleCraneUrlUpdate"
          ref="craneConfigFormRef"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, reactive } from 'vue';
import type { FormInstance } from 'element-plus';

// 引入步骤组件
import SyncFieldsAccessGuideForm from './accessGuideForms/SyncFieldsAccessGuideForm.vue';
import SyncConfigAccessGuideForm from './accessGuideForms/SyncConfigAccessGuideForm.vue';
import QueryFieldsAccessGuideForm from './accessGuideForms/QueryFieldsAccessGuideForm.vue';
import MafkaConfigAccessGuideForm from './accessGuideForms/MafkaConfigAccessGuideForm.vue';
import CraneConfigAccessGuideForm from './accessGuideForms/CraneConfigAccessGuideForm.vue';

// 定义props，用于接收父组件传入的数据
const props = defineProps({
  step: {
    type: Number,
    default: 1
  }
});

// 定义emit，确保在使用前声明所有需要发出的事件
const emit = defineEmits<{
  (e: 'valid-change', valid: boolean): void;
  (e: 'update:step', step: number): void;
  (e: 'sync-metadata-update', data: any[] | any): void;
  (e: 'query-metadata-update', data: any[] | any): void;
  (e: 'sync-config-update', data: any): void;
  (e: 'dsl-config-update', data: any): void;
  (e: 'provide-query-function-update', provide: boolean): void;
  (e: 'mafka-config-update', data: any): void;
  (e: 'complete-task'): void;
  (e: 'crane-url-update', url: string): void;
}>();

// 当前组件内部的步骤（0-4对应5个Mafka步骤）
const actualStep = ref(0);

// 是否已完成最终任务
const hasCompletedFinalStep = ref(false);

// 表单数据
// 添加mafkaConfigData保存Mafka配置数据
const mafkaConfigData = ref({
  namespace: '',
  group: '',
  topic: ''
});

// 同步配置表单数据
const configForm = ref({
  syncScene: undefined,
  syncTenant: undefined,
  syncName: '',
  syncDescription: '',
  lionConfigDescription: '',
  syncFieldCodes: [] as string[],
  type: 1, // 默认Mafka类型
  isPoiOuter: false,
  mafkaConsumeConfig: '',
  valid: 1
});

// 查询字段表单数据
const queryFieldForm = ref({});

// 是否提供查询功能
const provideQueryFunction = ref(false);

// Crane URL
const craneUrl = ref('');

// 组件引用
const mafkaConfigFormRef = ref();
const syncFieldsFormRef = ref();
const syncConfigFormRef = ref();
const queryFieldsFormRef = ref();
const craneConfigFormRef = ref();

// 监听父组件传入的step变化
watch(() => props.step, (newStep) => {
  if (newStep > 0) {
    actualStep.value = newStep - 1;
  } else {
    actualStep.value = 0;
  }
  
  if (actualStep.value === 4 && hasCompletedFinalStep.value) {
    actualStep.value = 5;
  }
  // 步骤切换后，主动验证当前步骤的表单状态
  nextTick(() => {
    validateCurrentStepForm();
  });
});

// 监听provideQueryFunction的变化
watch(provideQueryFunction, (newValue) => {
  // 发送查询功能配置状态给父组件
  emit('provide-query-function-update', newValue);
});

// 添加watch监听actualStep变化，处理步骤切换逻辑
watch(
  () => actualStep.value,
  (newValue, oldValue) => {
    // 当步骤从其他步骤切换到同步配置步骤(步骤3)时
    if (newValue === 2 && oldValue !== 2) {
      // 延迟执行，确保视图已更新
      nextTick(() => {
        // 调用SyncConfigAccessGuideForm的clearOuterIdsKeyValidation方法清除校验
        if (syncConfigFormRef.value && syncConfigFormRef.value.clearOuterIdsKeyValidation) {
          syncConfigFormRef.value.clearOuterIdsKeyValidation();
        }
      });
    }
  }
);

// 验证当前步骤的表单
const validateCurrentStepForm = async () => {
  let isValid = false;
  console.log('actualStep.value', actualStep.value);
  switch (actualStep.value) {
    case 0: // Mafka配置信息
      // 使用MafkaConfigAccessGuideForm组件的验证方法
      if (mafkaConfigFormRef.value && mafkaConfigFormRef.value.activeValidate) {
        isValid = await mafkaConfigFormRef.value.activeValidate();
      }
      break;
    case 1: // 同步字段信息
      if (syncFieldsFormRef.value && syncFieldsFormRef.value.activeValidate) {
        isValid = await syncFieldsFormRef.value.activeValidate();
      }
      break;
    case 2: // 同步配置信息
      if (syncConfigFormRef.value && syncConfigFormRef.value.activeValidate) {
        isValid = await syncConfigFormRef.value.activeValidate();
      }
      break;
    case 3: // 查询功能配置
      if (queryFieldsFormRef.value && queryFieldsFormRef.value.activeValidate) {
        isValid = await queryFieldsFormRef.value.activeValidate();
      }
      break;
    case 4: // Crane配置
      if (craneConfigFormRef.value && craneConfigFormRef.value.activeValidate) {
        isValid = await craneConfigFormRef.value.activeValidate();
        console.log('isValid', isValid);
      }
      break;
    case 5: // Crane配置
      if (craneConfigFormRef.value && craneConfigFormRef.value.activeValidate) {
        isValid = await craneConfigFormRef.value.activeValidate();
        console.log('isValid22', isValid);
      }
      break;
    default:
      isValid = false;
  }
  
  // 更新验证状态
  setTimeout(() => {
    emit('valid-change', isValid);
  }, 150);
  return isValid;
};

// 统一处理子组件的valid-change事件
const handleValidChange = (valid: boolean) => {
  // 直接传递给父组件
  emit('valid-change', valid);
};

// 处理同步字段数据更新
const handleSyncFieldsDataUpdate = (data: any[]) => {
  // 将子组件的数据传递给父组件，但只有当有实际数据时才传递
  if (data.length > 0 && (data[0].fieldCode || data[0].fieldName)) {
    emit('sync-metadata-update', data);
  }
};

// 处理同步配置更新
const handleSyncConfigUpdate = (data: any) => {
  // 更新本地配置数据
  Object.assign(configForm.value, data);
  // 将子组件的数据传递给父组件
  emit('sync-config-update', data);
};

// 处理查询字段数据更新
const handleQueryFieldsDataUpdate = (data: any[]) => {
  // 将查询字段数据传递给父组件
  if (Array.isArray(data)) {
    emit('query-metadata-update', data);
  }
};

// 添加更新Mafka消费配置的方法
const updateMafkaConsumerConfig = (mafkaConfig: any) => {
  // 如果当前步骤不是同步配置步骤（步骤2）或同步配置表单引用不存在，则不处理
  if (actualStep.value !== 1 || !syncConfigFormRef.value) return;
  
  // 直接将Mafka配置传递给SyncConfigAccessGuideForm组件
  if (syncConfigFormRef.value) {
    syncConfigFormRef.value.updateMafkaConsumeConfig(mafkaConfig);
  }
};

// 设置同步字段代码
const setSyncFieldCodes = (fieldCodes: string[]) => {
  // 更新本地configForm中的字段代码
  if (!configForm.value.syncFieldCodes) {
    configForm.value.syncFieldCodes = [];
  }
  configForm.value.syncFieldCodes = [...fieldCodes];
  
  // 如果已经创建了syncConfigFormRef组件实例，则调用它的方法设置同步字段
  if (syncConfigFormRef.value) {
    syncConfigFormRef.value.setSyncFieldCodes(fieldCodes);
  }
};

// 添加新方法：设置同步配置数据
const setSyncConfigData = (configData: any) => {
  // 更新本地configForm
  Object.assign(configForm.value, configData);
  
  // 如果已经创建了syncConfigFormRef组件实例，则调用它的方法设置完整表单数据
  if (syncConfigFormRef.value && syncConfigFormRef.value.setFormData) {
    syncConfigFormRef.value.setFormData(configData);
  }
};

// 设置查询字段数据
const setQueryFieldsData = (fieldData: any[], provideQuery: boolean = true) => {
  // 设置是否提供查询功能
  provideQueryFunction.value = provideQuery;
  
  // 如果已经创建了queryFieldsFormRef组件实例，则设置查询字段数据和控制标识
  if (queryFieldsFormRef.value) {
    queryFieldsFormRef.value.setFieldsData(fieldData, provideQuery);
  }
};

// 处理Crane任务完成
const handleCraneTaskComplete = () => {
  // 设置任务已完成
  hasCompletedFinalStep.value = true;
  actualStep.value = 5;
  // 传递任务完成事件给父组件
  emit('complete-task');
};

// 处理Crane URL更新
const handleCraneUrlUpdate = (url: string) => {
  craneUrl.value = url;
  emit('crane-url-update', url);
};

// 重置Mafka配置表单
const resetMafkaConfigForm = () => {
  if (mafkaConfigFormRef.value) {
    mafkaConfigFormRef.value.resetForm();
  }
};

// 重置字段表单数据
const resetSyncFieldsForm = () => {
  if (syncFieldsFormRef.value) {
    syncFieldsFormRef.value.resetForm();
  }
};

// 重置同步配置表单数据
const resetSyncConfigForm = () => {
  if (syncConfigFormRef.value) {
    syncConfigFormRef.value.resetForm();
  }
};

// 重置查询字段表单数据
const resetQueryFieldsForm = () => {
  if (queryFieldsFormRef.value && queryFieldsFormRef.value.resetForm) {
    queryFieldsFormRef.value.resetForm();
  }
  provideQueryFunction.value = false;
};

// 重置Crane配置表单
const resetCraneConfigForm = () => {
  // 重置最终任务完成状态
  hasCompletedFinalStep.value = false;
  // 重置Crane URL
  craneUrl.value = '';
};

// 重置所有表单数据
const resetAllForms = () => {
  resetMafkaConfigForm();
  resetSyncFieldsForm();
  resetSyncConfigForm();
  resetQueryFieldsForm();
  resetCraneConfigForm();
};

// 处理Mafka配置数据更新
const handleMafkaConfigUpdate = (data: any) => {
  // 保存配置数据
  mafkaConfigData.value = { ...data };
  
  // 将 mafka-config-update 事件传递给父组件
  emit('mafka-config-update', data);
};

// 新增方法：设置Mafka配置数据
const setMafkaConfigData = (data: any) => {
  if (mafkaConfigFormRef.value) {
    mafkaConfigFormRef.value.setFormData(data);
  }
};

// 新增方法：设置同步字段数据
const setSyncFieldsData = (data: any[]) => {
  if (syncFieldsFormRef.value) {
    syncFieldsFormRef.value.setFieldsData(data);
  }
};

// 新增方法：设置Crane URL
const setCraneUrl = (url: string) => {
  if (craneConfigFormRef.value) {
    craneConfigFormRef.value.setTaskUrl(url);
  }
};

// 对外暴露步骤状态和方法
defineExpose({
  step: actualStep,
  resetMafkaConfigForm,
  resetSyncFieldsForm,
  resetSyncConfigForm,
  resetQueryFieldsForm,
  resetCraneConfigForm,
  resetAllForms,
  validateCurrentStepForm,
  setSyncFieldCodes,
  setQueryFieldsData,
  updateMafkaConsumerConfig,
  hasCompletedFinalStep,
  setMafkaConfigData,
  setSyncFieldsData,
  setCraneUrl,
  setSyncConfigData
});
</script>

<style scoped lang="scss">
.mafka-access-guide {
  .mafka-steps {
    margin-bottom: 20px;
    padding: 16px 0;
    
    :deep(.el-step) {
      .el-step__head {
        .el-step__line {
          background-color: var(--el-border-color-lighter);
        }
        
        .el-step__icon {
          &.is-text {
            border-color: var(--el-border-color);
            background-color: var(--el-bg-color);
            color: var(--el-text-color-secondary);
          }
        }
        
        &.is-process {
          .el-step__icon {
            &.is-text {
              border-color: var(--el-color-primary);
              background-color: var(--el-color-primary);
              color: white;
            }
          }
        }
        
        &.is-finish {
          .el-step__icon {
            &.is-text {
              border-color: var(--el-color-success);
              background-color: var(--el-color-success);
              color: white;
            }
          }
          
          .el-step__line {
            background-color: var(--el-color-success);
          }
        }
      }
      
      .el-step__main {
        .el-step__title {
          font-size: 14px;
          
          &.is-process {
            color: var(--el-color-primary);
            font-weight: 500;
          }
          
          &.is-finish {
            color: var(--el-color-success);
          }
        }
        
        .el-step__description {
          font-size: 12px;
          
          &.is-process {
            color: var(--el-color-primary-light-3);
          }
          
          &.is-finish {
            color: var(--el-color-success-light-3);
          }
        }
      }
    }
  }
  
  .guide-card {
    border-radius: 8px;
    
    :deep(.el-card__header) {
      padding: 15px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      background-color: var(--el-fill-color-light);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      
      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .step-title {
        margin-left: 12px;
        font-size: 14px;
        color: var(--el-color-primary);
        font-weight: 500;
      }
    }

    .step-content {
      .section-desc {
        font-size: 14px;
        color: var(--el-text-color-regular);
        margin-bottom: 20px;
      }
      
      .guide-alert {
        margin-bottom: 20px;
        
        :deep(.el-alert__content) {
          padding: 0 8px;
        }

        :deep(.el-alert__icon) {
          margin-right: 8px;
          font-size: 16px;
        }

        span {
          color: var(--el-text-color-regular);
          font-size: 14px;
          line-height: 1.5;
        }
      }
      
      .mafka-config-form {
        // margin-top: 20px;
      }
    }
  }
}
</style> 