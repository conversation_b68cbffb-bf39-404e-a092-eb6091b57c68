<template>
  <div class="field-sync-info">
    <!-- 添加提示信息 -->
    <el-alert
      type="warning"
      show-icon
      :closable="false"
      class="guide-alert"
    >
      <span>
        此阶段配置的是<b>「聚合查询 - 同步服务」</b>中的字段基本信息，规定了哪些字段是允许<b>同步</b>的。<br>
        注意：字段Code必须唯一，且符合命名规范；字段名称应当简洁明确，便于理解字段的业务含义。<br>
      </span>
    </el-alert>
    
    <!-- 字段信息表单 -->
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <div class="fields-container">
        <div v-for="(field, index) in formData.fields" :key="index" class="field-item">
          <div class="field-header">
            <span class="field-title">字段 {{ index + 1 }}</span>
            <el-button
              type="danger"
              :icon="Delete"
              circle
              size="small"
              @click="removeField(index)"
            />
          </div>
          <div class="field-content">
            <div class="field-row">
              <el-form-item
                :prop="'fields.' + index + '.fieldCode'"
                label="字段Code"
                required
                class="field-col"
              >
                <el-input
                  v-model="field.fieldCode"
                  placeholder="请输入字段Code（命名规范：xxx_yyy_zzz）"
                />
              </el-form-item>
              
              <el-form-item
                :prop="'fields.' + index + '.fieldProperty'"
                label="字段Property"
                required
                class="field-col"
              >
                <el-input
                  v-model="field.fieldProperty"
                  placeholder="请输入字段属性（命名规范：xxxYyyZzz）"
                />
              </el-form-item>
            </div>
            
            <div class="field-row">
              <el-form-item
                :prop="'fields.' + index + '.fieldName'"
                label="字段名称"
                required
                class="field-col"
              >
                <el-input
                  v-model="field.fieldName"
                  placeholder="请输入字段名称"
                />
              </el-form-item>
              
              <el-form-item
                :prop="'fields.' + index + '.description'"
                label="字段描述"
                class="field-col"
              >
                <el-input
                  v-model="field.description"
                  placeholder="请输入字段描述"
                />
              </el-form-item>
            </div>
            
            <div class="field-row">
              <el-form-item
                :prop="'fields.' + index + '.type'"
                label="字段类型"
                required
                class="field-col"
              >
                <el-select v-model="field.type" placeholder="请选择字段类型" style="width: 100%">
                  <el-option
                    v-for="type in FIELD_TYPE_OPTIONS"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item
                :prop="'fields.' + index + '.defaultValue'"
                label="默认值"
                required
                class="field-col"
              >
                <el-input
                  v-model="field.defaultValue"
                  placeholder="请输入默认值"
                />
              </el-form-item>
            </div>
            
            <div class="field-row">
              <el-form-item
                :prop="'fields.' + index + '.lionConfigDescription'"
                label="Lion配置描述"
                required
                class="field-col"
              >
                <el-input
                  v-model="field.lionConfigDescription"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入Lion配置描述"
                />
              </el-form-item>
            </div>
          </div>
        </div>
      </div>
      <div class="form-actions">
        <el-button type="primary" @click="addField">
          <el-icon><Plus /></el-icon>添加字段
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { FIELD_TYPE_OPTIONS } from '../../../../types'
import type { FieldType, SyncMetadataItem } from '../../../../types'
import { getFieldMetadataList } from '../../../../request'

const props = defineProps<{
  existingFields?: string[]
}>()

// 定义提交事件
const emit = defineEmits([
  'submit', 
  'loading-state-change', 
  'valid-change',
  'update:sync-metadata', // 添加新的事件
  'sync-fields-data' // 添加新的同步字段数据事件
])

const formRef = ref<FormInstance>()
const isSubmitting = ref(false) // 添加提交状态变量

const formData = reactive<{
  fields: SyncMetadataItem[]
}>({
  fields: [{
    fieldCode: '',
    fieldName: '',
    type: undefined,
    description: '',
    defaultValue: '',
    dependentFields: [],
    sync2QueryField: false,
    fieldProperty: '',
    lionConfigDescription: '',
    valid: 1,
    status: 0
  }]
})

// 校验字段Code是否重复
const validateFieldCodeUnique = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback()
    return
  }
  
  // 获取当前字段索引
  const fieldIndex = parseInt(rule.field.split('.')[1])
  
  // 检查其他字段中是否有相同的Code
  const hasDuplicate = formData.fields.some((field, index) => 
    index !== fieldIndex && field.fieldCode && field.fieldCode === value
  )
  
  if (hasDuplicate) {
    callback(new Error('字段Code不能重复'))
  } else {
    callback()
  }
}

// 校验字段Property是否重复
const validateFieldPropertyUnique = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback()
    return
  }
  
  // 获取当前字段索引
  const fieldIndex = parseInt(rule.field.split('.')[1])
  
  // 检查其他字段中是否有相同的Property
  const hasDuplicate = formData.fields.some((field, index) => 
    index !== fieldIndex && field.fieldProperty && field.fieldProperty === value
  )
  
  if (hasDuplicate) {
    callback(new Error('字段Property不能重复'))
  } else {
    callback()
  }
}

// 校验字段名称是否重复
const validateFieldNameUnique = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback()
    return
  }
  
  // 获取当前字段索引
  const fieldIndex = parseInt(rule.field.split('.')[1])
  
  // 检查其他字段中是否有相同的名称
  const hasDuplicate = formData.fields.some((field, index) => 
    index !== fieldIndex && field.fieldName && field.fieldName === value
  )
  
  if (hasDuplicate) {
    callback(new Error('字段名称不能重复'))
  } else {
    callback()
  }
}

// 校验默认值格式
const validateDefaultValue = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback()
    return
  }
  
  // 获取当前字段索引
  const fieldIndex = parseInt(rule.field.split('.')[1])
  const field = formData.fields[fieldIndex]
  
  if (!field || !field.type) {
    callback()
    return
  }
  
  // 根据字段类型校验默认值格式
  switch (field.type) {
    case '1': // String
      // 字符串类型，检查是否有双引号包裹
      if (!value.startsWith('"') || !value.endsWith('"')) {
        callback(new Error('字符串类型默认值需要用双引号包裹，例如: "默认值"'))
      } else {
        callback()
      }
      break
    case '2': // Boolean
      // 布尔类型，检查是否为true或false
      if (value !== 'true' && value !== 'false') {
        callback(new Error('布尔类型默认值只能是true或false'))
      } else {
        callback()
      }
      break
    case '3': // Long
      // 整数类型，检查是否为有效整数
      if (!/^-?\d+$/.test(value)) {
        callback(new Error('整数类型默认值必须是有效的整数'))
      } else {
        callback()
      }
      break
    case '4': // Double
      // 浮点数类型，检查是否为有效浮点数
      if (!/^-?\d+(\.\d+)?$/.test(value)) {
        callback(new Error('浮点数类型默认值必须是有效的数字'))
      } else {
        callback()
      }
      break
    default:
      callback()
  }
}

const rules = reactive<FormRules>({
  'fields': [
    { required: true, message: '请至少添加一个字段', trigger: 'change' }
  ],
  'fields.0.fieldCode': [
    { required: true, message: '请输入字段Code', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段Code必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
    { validator: validateFieldCodeUnique, trigger: 'blur' }
  ],
  'fields.0.fieldProperty': [
    { required: true, message: '请输入字段Property', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9]*$/, message: '字段Property必须以字母开头，只能包含字母和数字', trigger: 'blur' },
    { validator: validateFieldPropertyUnique, trigger: 'blur' }
  ],
  'fields.0.fieldName': [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { validator: validateFieldNameUnique, trigger: 'blur' }
  ],
  'fields.0.type': [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ],
  'fields.0.defaultValue': [
    { required: true, message: '请输入默认值', trigger: 'blur, change' },
    { validator: validateDefaultValue, trigger: 'blur, change' }
  ],
  'fields.0.lionConfigDescription': [
    { required: true, message: '请输入Lion配置描述', trigger: 'blur' }
  ]
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
    // 清除所有校验状态
    formRef.value.clearValidate()
  }
  formData.fields = [{
    fieldCode: '',
    fieldName: '',
    type: undefined,
    description: '',
    defaultValue: '',
    dependentFields: [],
    sync2QueryField: false,
    fieldProperty: '',
    lionConfigDescription: '',
    valid: 1,
    status: 0
  }]
}

// 在组件挂载时获取依赖字段
onMounted(() => {
  // 初始化时清除表单校验状态
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
  
  // 只有在初始化且表单为空时才向上传递默认数据
  // 这样可以防止在步骤切换时清空已有数据
  if (formData.fields.length === 1 && 
      !formData.fields[0].fieldCode && 
      !formData.fields[0].fieldName) {
    // 将formData对象深拷贝后向上传递
    emit('sync-fields-data', getFormData());
  }
});

// 添加字段
const addField = () => {
  const newIndex = formData.fields.length
  formData.fields.push({
    fieldCode: '',
    fieldName: '',
    type: undefined,
    description: '',
    defaultValue: '',
    dependentFields: [],
    sync2QueryField: false,
    fieldProperty: '',
    lionConfigDescription: '',
    valid: 1,
    status: 0
  })
  
  // 为新字段添加验证规则，但不立即触发验证
  rules[`fields.${newIndex}.fieldCode`] = [
    { required: true, message: '请输入字段Code', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段Code必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
    { validator: validateFieldCodeUnique, trigger: 'blur' }
  ]
  rules[`fields.${newIndex}.fieldProperty`] = [
    { required: true, message: '请输入字段Property', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9]*$/, message: '字段Property必须以字母开头，只能包含字母和数字', trigger: 'blur' },
    { validator: validateFieldPropertyUnique, trigger: 'blur' }
  ]
  rules[`fields.${newIndex}.fieldName`] = [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { validator: validateFieldNameUnique, trigger: 'blur' }
  ]
  rules[`fields.${newIndex}.type`] = [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ]
  rules[`fields.${newIndex}.defaultValue`] = [
    { required: true, message: '请输入默认值', trigger: 'blur, change' },
    { validator: validateDefaultValue, trigger: 'blur, change' }
  ]
  rules[`fields.${newIndex}.lionConfigDescription`] = [
    { required: true, message: '请输入Lion配置描述', trigger: 'blur' }
  ]
  
  // 使用nextTick确保DOM更新后再滚动到新添加的字段
  nextTick(() => {
    // 滚动到新添加的字段
    const container = document.querySelector('.fields-container')
    if (container) {
      container.scrollTop = container.scrollHeight
    }
    
    // 清除所有字段的校验结果，避免添加字段时触发校验
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })

  // 通知父组件字段已添加
  emit('valid-change', false)
}

// 删除字段
const removeField = (index: number) => {
  if (formData.fields.length > 1) {
    formData.fields.splice(index, 1)
    // 删除该字段的验证规则
    delete rules[`fields.${index}.fieldCode`]
    delete rules[`fields.${index}.fieldProperty`]
    delete rules[`fields.${index}.fieldName`]
    delete rules[`fields.${index}.type`]
    delete rules[`fields.${index}.defaultValue`]
    delete rules[`fields.${index}.lionConfigDescription`]
    
    // 更新后续字段的验证规则索引
    for (let i = index; i < formData.fields.length; i++) {
      rules[`fields.${i}.fieldCode`] = rules[`fields.${i + 1}.fieldCode`]
      rules[`fields.${i}.fieldProperty`] = rules[`fields.${i + 1}.fieldProperty`]
      rules[`fields.${i}.fieldName`] = rules[`fields.${i + 1}.fieldName`]
      rules[`fields.${i}.type`] = rules[`fields.${i + 1}.type`]
      rules[`fields.${i}.defaultValue`] = rules[`fields.${i + 1}.defaultValue`]
      rules[`fields.${i}.lionConfigDescription`] = rules[`fields.${i + 1}.lionConfigDescription`]
      
      delete rules[`fields.${i + 1}.fieldCode`]
      delete rules[`fields.${i + 1}.fieldProperty`]
      delete rules[`fields.${i + 1}.fieldName`]
      delete rules[`fields.${i + 1}.type`]
      delete rules[`fields.${i + 1}.defaultValue`]
      delete rules[`fields.${i + 1}.lionConfigDescription`]
    }
  } else {
    ElMessage.warning('至少保留一个字段')
  }
}

// 监听每个字段的类型变化
const handleTypeChange = (field: SyncMetadataItem) => {
  // 根据不同的字段类型设置默认值
  switch (field.type) {
    case '1': // String
      field.defaultValue = '""'
      break
    case '2': // Boolean
      field.defaultValue = 'false'
      break
    case '3': // Long
      field.defaultValue = '0'
      break
    case '4': // Double
      field.defaultValue = '0.0'
      break
    default:
      field.defaultValue = ''
  }
}

// 监听所有字段的类型变化
watch(() => formData.fields.map(f => f.type), (newTypes, oldTypes) => {
  if (!oldTypes) return
  formData.fields.forEach((field, index) => {
    if (field.type !== oldTypes[index]) {
      handleTypeChange(field)
    }
  })
}, { deep: true })

// 监听默认值变化，触发校验
watch(() => formData.fields.map(f => f.defaultValue), (newValues, oldValues) => {
  if (!oldValues) return
  
  // 使用nextTick确保DOM更新后再触发校验
  nextTick(() => {
    formData.fields.forEach((field, index) => {
      if (field.defaultValue !== oldValues[index] && formRef.value) {
        // 触发对应字段的默认值校验
        formRef.value.validateField(`fields.${index}.defaultValue`)
      }
    })
  })
}, { deep: true })

// 监听fieldCode变化，自动生成fieldProperty
watch(() => formData.fields.map(f => f.fieldCode), (newValues, oldValues) => {
  if (!oldValues) return
  
  formData.fields.forEach((field, index) => {
    if (field.fieldCode !== oldValues[index]) {
      // 将下划线命名转换为驼峰命名
      const camelCase = convertFieldCodeToProperty(field.fieldCode || '')
      
      // 只有当fieldProperty为空或者是由fieldCode自动生成的时候才更新
      if (!field.fieldProperty || field.fieldProperty === convertFieldCodeToProperty(oldValues[index] || '')) {
        field.fieldProperty = camelCase
        
        // 触发校验
        nextTick(() => {
          if (formRef.value) {
            formRef.value.validateField(`fields.${index}.fieldCode`)
            formRef.value.validateField(`fields.${index}.fieldProperty`)
          }
        })
      }
    }
  })
}, { deep: true })

// 监听fieldProperty变化，自动生成fieldCode
watch(() => formData.fields.map(f => f.fieldProperty), (newValues, oldValues) => {
  if (!oldValues) return
  
  formData.fields.forEach((field, index) => {
    if (field.fieldProperty !== oldValues[index]) {
      // 将驼峰命名转换为下划线命名
      const snakeCase = convertPropertyToFieldCode(field.fieldProperty || '')
      
      // 只有当fieldCode为空或者是由fieldProperty自动生成的时候才更新
      if (!field.fieldCode || field.fieldCode === convertPropertyToFieldCode(oldValues[index] || '')) {
        field.fieldCode = snakeCase
        
        // 触发校验
        nextTick(() => {
          if (formRef.value) {
            formRef.value.validateField(`fields.${index}.fieldCode`)
            formRef.value.validateField(`fields.${index}.fieldProperty`)
          }
        })
      }
    }
  })
}, { deep: true })

// 监听fieldProperty和description变化，自动生成lionConfigDescription
watch(
  [
    () => formData.fields.map(f => f.fieldCode),
    () => formData.fields.map(f => f.description)
  ],
  (newValues, oldValues) => {
    // 如果是初始化，oldValues 会是 undefined
    const oldCodes = oldValues?.[0] || []
    const oldDescriptions = oldValues?.[1] || []
    
    formData.fields.forEach((field, index) => {
      // 检查字段是否发生变化或者是初始化
      const codeChanged = !oldCodes.length || field.fieldCode !== oldCodes[index]
      const descriptionChanged = !oldDescriptions.length || field.description !== oldDescriptions[index]
      
      // 如果任一字段发生变化或者是初始化，且两个字段都不为空，则生成 lionConfigDescription
      if ((codeChanged || descriptionChanged) && (field.fieldCode || field.description)) {
        const oldLionConfigDescription = field.lionConfigDescription
        field.lionConfigDescription = `【字段元数据】${field.fieldCode}-${field.description}`
        
        // 如果lionConfigDescription发生变化，触发校验
        if (oldLionConfigDescription !== field.lionConfigDescription) {
          nextTick(() => {
            if (formRef.value) {
              formRef.value.validateField(`fields.${index}.lionConfigDescription`)
            }
          })
        }
      } else if (!field.fieldCode && !field.description) {
        field.lionConfigDescription = ''
      }
    })
  },
  { deep: true, immediate: true }
)

// 辅助函数：将fieldCode转换为fieldProperty
const convertFieldCodeToProperty = (fieldCode: string): string => {
  if (!fieldCode) return ''
  return fieldCode
    .split('_')
    .map((word, index) => {
      if (index === 0) {
        return word.toLowerCase()
      }
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    })
    .join('')
}

// 辅助函数：将fieldProperty转换为fieldCode
const convertPropertyToFieldCode = (fieldProperty: string): string => {
  if (!fieldProperty) return ''
  return fieldProperty
    .replace(/([A-Z])/g, '_$1')
    .toLowerCase()
    .replace(/^_/, '')
}

// 获取表单数据
const getFormData = () => {
  return formData.fields.map(field => {
    // 先创建字段的深拷贝
    const data = JSON.parse(JSON.stringify({
      ...field,
      valid: 1,
      // 确保依赖字段和同步到查询字段始终为固定值
      dependentFields: [],
      sync2QueryField: false
    }));
    
    // 处理String类型的默认值
    if (field.type === '1' && data.defaultValue) {
      data.defaultValue = data.defaultValue.startsWith('"') && data.defaultValue.endsWith('"')
        ? data.defaultValue.substring(1, data.defaultValue.length - 1)
        : data.defaultValue
    }
    
    return data
  });
}

// 检查字段是否有效
const isFieldValid = (field: SyncMetadataItem): boolean => {
  if (!field.fieldCode || !field.fieldProperty || !field.fieldName || !field.type || field.defaultValue === undefined || field.defaultValue === '') {
    return false
  }
  
  // 检查默认值格式
  if (field.type === '1') { // String
    if (!field.defaultValue.startsWith('"') || !field.defaultValue.endsWith('"')) {
      return false
    }
  } else if (field.type === '2') { // Boolean
    if (field.defaultValue !== 'true' && field.defaultValue !== 'false') {
      return false
    }
  } else if (field.type === '3') { // Long
    if (!/^-?\d+$/.test(field.defaultValue)) {
      return false
    }
  } else if (field.type === '4') { // Double
    if (!/^-?\d+(\.\d+)?$/.test(field.defaultValue)) {
      return false
    }
  }
  
  // 如果同步到查询字段但没有设置依赖字段，仍然认为有效
  return true
}

// 基于数组内容的验证方法
const validateFieldsContent = (): boolean => {
  // 检查是否有字段
  if (formData.fields.length === 0) {
    return false
  }
  
  // 检查每个字段是否都有效
  const allFieldsValid = formData.fields.every(field => isFieldValid(field))
  if (!allFieldsValid) {
    return false
  }
  
  // 检查是否有重复的字段Code
  const fieldCodes = formData.fields.map(field => field.fieldCode).filter(Boolean)
  const uniqueCodes = new Set(fieldCodes)
  if (fieldCodes.length !== uniqueCodes.size) {
    return false
  }
  
  // 检查是否有重复的字段Property
  const fieldProperties = formData.fields.map(field => field.fieldProperty).filter(Boolean)
  const uniqueProperties = new Set(fieldProperties)
  if (fieldProperties.length !== uniqueProperties.size) {
    return false
  }
  
  // 检查是否有重复的字段名称
  const fieldNames = formData.fields.map(field => field.fieldName).filter(Boolean)
  const uniqueNames = new Set(fieldNames)
  if (fieldNames.length !== uniqueNames.size) {
    return false
  }
  
  return true
}

// 监听字段变化，更新验证状态
watch(() => formData.fields, () => {
  // 当字段内容变化时，根据字段数组的内容进行校验
  const isValid = validateFieldsContent();
  // 通知父组件验证状态变化
  emit('valid-change', isValid);
  
  // 当字段内容变化时，向父组件发送深拷贝的字段数据，避免直接共享引用
  emit('sync-fields-data', getFormData());
  
  // 如果表单有效，则发送最新的同步字段数据
  if (isValid) {
    const fieldsData = getFormData();
    emit('update:sync-metadata', fieldsData);
  }
}, { deep: true });

// 验证表单
const validateForm = async () => {
  if (!formRef.value) return false
  
  // 先检查是否有重复的字段
  const fieldCodes = formData.fields.map(field => field.fieldCode)
  const fieldProperties = formData.fields.map(field => field.fieldProperty)
  const fieldNames = formData.fields.map(field => field.fieldName)
  
  // 检查是否有重复的字段Code
  const hasDuplicateCode = fieldCodes.some((code, index) => {
    if (!code) return false // 跳过空值
    return fieldCodes.indexOf(code) !== index
  })
  
  if (hasDuplicateCode) {
    ElMessage.error('存在重复的字段Code，请修改后再提交')
    return false
  }
  
  // 检查是否有重复的字段Property
  const hasDuplicateProperty = fieldProperties.some((prop, index) => {
    if (!prop) return false // 跳过空值
    return fieldProperties.indexOf(prop) !== index
  })
  
  if (hasDuplicateProperty) {
    ElMessage.error('存在重复的字段Property，请修改后再提交')
    return false
  }
  
  // 检查是否有重复的字段名称
  const hasDuplicateName = fieldNames.some((name, index) => {
    if (!name) return false // 跳过空值
    return fieldNames.indexOf(name) !== index
  })
  
  if (hasDuplicateName) {
    ElMessage.error('存在重复的字段名称，请修改后再提交')
    return false
  }
  
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 提交表单
const submitForm = async () => {
  const isValid = await validateForm()
  if (!isValid) return false
  
  isSubmitting.value = true
  emit('loading-state-change', true) // 通知父组件更新loading状态
  
  try {
    const formData = getFormData()
    emit('submit', formData)
    return true
  } catch (error) {
    console.error('表单提交失败:', error)
    return false
  } finally {
    isSubmitting.value = false
  }
}

// 主动触发表单验证并更新valid状态
const activeValidate = async () => {
  const isValid = validateFieldsContent();
  emit('valid-change', isValid);
  return isValid;
}

// 设置字段数据的方法
const setFieldsData = (data: any[]) => {
  if (Array.isArray(data) && data.length > 0) {
    // 直接更新formData.fields
    formData.fields = JSON.parse(JSON.stringify(data));
    
    // 如果表单引用存在，重新验证
    if (formRef && formRef.value) {
      setTimeout(() => {
        formRef.value?.validate();
      }, 50);
    }
  }
};

// 对外暴露方法和数据
defineExpose({
  formData,
  getFormData,
  resetForm,
  validateForm,
  submitForm,
  isSubmitting,
  activeValidate,
  setFieldsData
})
</script>

<style lang="scss" scoped>
.field-sync-info {
  .fields-container {
    max-height: calc(70vh - 150px);
    overflow-y: auto;
    padding-right: 16px;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background-color: #f5f7fa;
    }
    
    .field-item {
      margin-bottom: 24px;
      padding: 20px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background-color: #f8f9fa;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #ebeef5;
        
        .field-title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }
      
      .field-content {
        .field-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin-bottom: 8px;
          
          .field-col {
            flex: 1;
            min-width: 220px;
            
            &.sync-query-field {
              display: flex;
              flex-direction: column;
              justify-content: flex-end;
              
              :deep(.el-form-item__content) {
                display: flex;
                align-items: center;
                height: 40px;
              }
              
              :deep(.el-form-item__label) {
                pointer-events: none;
              }
            }
          }
          
          /* 当宽度小于768px时，一列显示 */
          @media (max-width: 768px) {
            flex-direction: column;
            gap: 0;
            
            .field-col {
              width: 100%;
              min-width: 100%;
            }
          }
        }
      }
    }
  }
  
  .form-actions {
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    text-align: center;
  }
  
  .guide-alert {
    margin-bottom: 20px;
    
    :deep(.el-alert__content) {
      padding: 0 8px;
    }

    :deep(.el-alert__icon) {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 1.5;
    }
  }
}
</style> 