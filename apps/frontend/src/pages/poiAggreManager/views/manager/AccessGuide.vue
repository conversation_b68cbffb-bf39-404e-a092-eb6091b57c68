<template>
  <div class="access-guide-container">
    <div v-show="currentStep === 0">
      <div class="section-title">请选择接入方式</div>
      <el-alert
        type="warning"
        show-icon
        :closable="false"
        class="guide-alert"
      >
        <span>
          选择合适的数据接入方式，根据指引完成配置。不同接入方式适用于不同的业务场景，请根据实际需求选择。<br>
          1、如果数据源提供方提供了查询接口，建议使用 RPC 接入方式。<br>
          2、如果接入数据是 POI 维度，建议使用 DTS 接入方式。<br>
          3、如果数据源提供方没有提供查询接口，并且接入数据是非 POI 维度，建议使用 Mafka 接入方式。<br>
        </span>
      </el-alert>
      <div class="card-container">
        <el-card
          v-for="(item, index) in accessWays"
          :key="index"
          :class="['access-card', { active: currentTab === item.key, disabled: item.disabled }]"
          shadow="hover"
          @click="!item.disabled && switchTab(item.key)"
        >
          <el-tooltip
            v-if="item.disabled"
            content="暂未开放"
            placement="top"
            :enterable="false"
            effect="dark"
          >
            <div class="card-content">
              <el-icon :size="36" class="card-icon disabled-icon">
                <component :is="item.icon" />
              </el-icon>
              <div class="card-title">{{ item.title }}</div>
              <div class="card-desc">{{ item.description }}</div>
            </div>
          </el-tooltip>
          <div v-else class="card-content">
            <el-icon :size="36" class="card-icon">
              <component :is="item.icon" />
            </el-icon>
            <div class="card-title">{{ item.title }}</div>
            <div class="card-desc">{{ item.description }}</div>
          </div>
        </el-card>
      </div>
    </div>

    <div class="content-container">
      <!-- 需求信息表单组件 - 仅在步骤0显示 -->
      <div v-show="currentStep === 0">
        <RequirementInfoForm 
          :initial-data="requirementData"
          @update="handleRequirementUpdate"
          @valid-change="handleFormValidChange"
          @ai-config="handleAiConfigResult"
          ref="requirementFormRef"
        />
      </div>

      <!-- 接入指引组件 - 仅在步骤1及以后显示 -->
      <div v-show="currentStep > 0">
        <!-- 使用 v-show 而不是 v-if 避免重新渲染导致表单重置 -->
        <!-- DTS接入指引 -->
        <DTSAccessGuideForm 
          v-show="currentTab === 'dts'" 
          :step="currentStep"
          :access-way="currentTab"
          @valid-change="handleStepValidChange"
          @update:step="handleDtsStepUpdate"
          @sync-fields-data-update="handleSyncFieldsDataUpdate"
          @sync-config-update="handleSyncConfigUpdate"
          @query-metadata-update="handleQueryMetadataUpdate"
          @provide-query-function-update="handleProvideQueryServiceUpdate"
          @subscription-url-update="handleSubscriptionUrlUpdate"
          @complete-task="handleTaskComplete"
          ref="dtsFormRef"
        />
        
        <!-- Mafka接入指引 -->
        <MafkaAccessGuideForm 
          v-if="currentTab === 'mafka'" 
          :step="currentStep"
          :access-way="currentTab"
          @valid-change="handleStepValidChange"
          @sync-metadata-update="handleSyncMetadataUpdate"
          @query-metadata-update="handleQueryMetadataUpdate"
          @sync-config-update="handleSyncConfigUpdate"
          @dsl-config-update="handleDslConfigUpdate"
          @provide-query-function-update="handleProvideQueryServiceUpdate"
          @mafka-config-update="handleMafkaConfigUpdate"
          @complete-task="handleTaskComplete"
          @crane-url-update="handleCraneUrlUpdate"
          ref="mafkaFormRef"
        />
        
        <!-- RPC接入指引 -->
        <RPCAccessGuideForm 
          v-if="currentTab === 'rpc'" 
          :step="currentStep"
          @valid-change="handleStepValidChange"
          @query-metadata-update="handleQueryMetadataUpdate"
          @provide-query-function-update="handleProvideQueryServiceUpdate"
          @dsl-config-update="handleDslConfigUpdate"
          ref="rpcFormRef"
        />
        
        <!-- Cache接入指引 -->
        <div v-if="currentTab === 'cache'" class="guide-content">
          <el-card shadow="never" class="guide-card">
            <template #header>
              <div class="card-header">
                <span class="header-title">Cache-Squirrel 接入指引</span>
              </div>
            </template>
            
            <div class="card-content">
              <p>这里是 Cache-Squirrel 接入的详细说明...</p>
              <!-- 这里可以根据实际需求添加 Cache-Squirrel 接入的详细内容 -->
            </div>
          </el-card>
        </div>
      </div>

      <!-- 底部导航按钮 -->
      <div class="navigation-buttons">
        <el-button 
          v-if="showPrevButton"
          type="default" 
          @click="prevStep"
        >
          <el-icon class="el-icon--left"><ArrowLeft /></el-icon>
          上一步
        </el-button>
        <div class="spacer"></div>
        <el-button 
          v-if="showNextButton"
          type="primary" 
          @click="nextStep"
          :disabled="!canProceed"
        >
          {{ currentStep === 0 ? '开始接入' : '下一步' }}
          <el-icon class="el-icon--right"><ArrowRight /></el-icon>
        </el-button>
        <el-button 
          v-if="currentStep >= maxStep && currentStep !== 0"
          type="primary" 
          @click="submitAccessGuide"
          :disabled="!canSubmit || isSubmitting"
          :loading="isSubmitting"
        >
          提交
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { 
  Message as MessageIcon, 
  Connection as ConnectionIcon, 
  Monitor as MonitorIcon, 
  DataLine as DataLineIcon,
  ArrowRight,
  ArrowLeft
} from '@element-plus/icons-vue';
import RequirementInfoForm from './component/RequirementInfoForm.vue';
import MafkaAccessGuideForm from './component/MafkaAccessGuideForm.vue';
import DTSAccessGuideForm from './component/DTSAccessGuideForm.vue';
import RPCAccessGuideForm from './component/RPCAccessGuideForm.vue';
import { 
  RequirementInfo,
  SyncMetadataItem,
  FieldMetadataItem,
  SyncConfigItem,
  DSLConfigItem,
  AccessGuideData,
  McmChangeItem,
  FIELD_TYPE_OPTIONS
} from '../../types/index';
import { ElMessage, ElMessageBox } from 'element-plus';
import { submitAccessGuideRequest } from '../../request';
import { h } from 'vue';

// 提交状态
const isSubmitting = ref(false);

// 定义接入方式数据
const accessWays = [
  {
    key: 'dts',
    title: 'DTS',
    icon: DataLineIcon,
    description: '数据同步服务接入指引',
    disabled: false,
    maxStep: 4 // DTS接入方式有4个步骤
  },
  {
    key: 'mafka',
    title: 'Mafka',
    icon: MessageIcon,
    description: 'Mafka消息队列接入指引',
    disabled: false,
    maxStep: 5 // Mafka接入方式有5个步骤
  },
  {
    key: 'rpc',
    title: 'RPC-Thrift',
    icon: ConnectionIcon,
    description: 'Thrift RPC接入指引',
    disabled: false,
    maxStep: 2 // RPC接入方式有3个步骤
  },
  {
    key: 'cache',
    title: 'Cache-Squirrel',
    icon: MonitorIcon,
    description: 'Squirrel缓存接入指引',
    disabled: true,
    maxStep: 2 // Cache接入方式有2个步骤
  }
];

// 当前选中的接入方式
const currentTab = ref<string>('');

// 当前步骤 - 0: 需求信息，1及以后: 接入指引的各步骤
const currentStep = ref(0);

// 当前接入方式的最大步骤
const maxStep = ref(0);

// 添加对需求信息表单组件的引用
const requirementFormRef = ref();
// 添加对DTS表单组件的引用
const dtsFormRef = ref();
// 添加对Mafka表单组件的引用
const mafkaFormRef = ref();
// 添加对RPC表单组件的引用
const rpcFormRef = ref();

// 表单验证状态
const formValid = ref(false);
// 各接入方式的步骤验证状态
const stepValid = ref(false);

// 是否可以提交
const canSubmit = ref(false);

// 检查是否选择了接入方式
const hasSelectedAccessWay = computed(() => {
  return currentTab.value !== '';
});

// 获取当前选择的接入方式
const currentAccessWay = computed(() => {
  return accessWays.find(way => way.key === currentTab.value);
});

// 是否显示"上一步"按钮
const showPrevButton = computed(() => {
  return currentStep.value > 0;
});

// 是否显示"下一步"按钮
const showNextButton = computed(() => {
  // 步骤0（需求信息）始终显示下一步按钮
  if (currentStep.value === 0) {
    return true;
  }
  
  // 有选择接入方式，且当前步骤小于最大步骤时显示
  return currentStep.value < maxStep.value;
});

// 下一步按钮是否可点击
const canProceed = computed(() => {
  // 需求信息步骤（步骤0）
  if (currentStep.value === 0) {
    return formValid.value && hasSelectedAccessWay.value;
  }
  
  // 接入指引步骤
  if (currentStep.value > 0) {
    return stepValid.value;
  }
  
  return false;
});

// 需求信息数据
const requirementData = ref<RequirementInfo>({
  prdLink: '',
  analysisResult: '',
  wikiLink: ''
});

// 同步字段元数据
const syncMetadataData = ref<SyncMetadataItem[]>([]);

// 查询字段元数据
const queryMetadataData = ref<FieldMetadataItem[]>([]);

// 同步配置
const syncConfigData = ref<SyncConfigItem | undefined>(undefined);

// DSL配置
const dslConfigData = ref<DSLConfigItem | undefined>(undefined);

// 记录是否提供查询功能
const provideQueryService = ref(false);

// 全部接入数据
const accessGuideData = ref<AccessGuideData>({
  requirementInfo: requirementData.value,
  accessWay: currentTab.value,
  syncFieldList: syncMetadataData.value,
  queryFieldList: queryMetadataData.value,
  provideQueryService: provideQueryService.value,
  syncConfig: syncConfigData.value,
  dslConfig: dslConfigData.value,
  dtsSubscriptionUrl: '',
  mafkaConfig: undefined,
  craneUrlInfo: ''
});

// 缓存Key常量
const STORAGE_KEY = 'poi_aggre_access_guide_cache';

// 添加一个恢复本地缓存数据的方法
const restoreFromLocalStorage = async () => {
  try {
    const cachedData = localStorage.getItem(STORAGE_KEY);
    if (!cachedData) return false;
    
    const parsedData = JSON.parse(cachedData) as {
      accessGuideData: AccessGuideData;
      currentTab: string;
      currentStep: number;
      maxStep: number;
    };
    
    // 更新基本状态
    currentTab.value = parsedData.currentTab;
    maxStep.value = parsedData.maxStep;
    accessGuideData.value = parsedData.accessGuideData;
    
    // 恢复各种数据引用
    requirementData.value = parsedData.accessGuideData.requirementInfo;
    syncMetadataData.value = parsedData.accessGuideData.syncFieldList || [];
    queryMetadataData.value = parsedData.accessGuideData.queryFieldList || [];
    syncConfigData.value = parsedData.accessGuideData.syncConfig;
    dslConfigData.value = parsedData.accessGuideData.dslConfig;
    provideQueryService.value = parsedData.accessGuideData.provideQueryService || false;
    
    // 等待组件渲染完成
    await nextTick();
    
    // 首先恢复需求信息表单
    if (requirementFormRef.value && parsedData.accessGuideData.requirementInfo) {
      requirementFormRef.value.setFormData(parsedData.accessGuideData.requirementInfo);
    }
    
    // 确认要恢复的步骤
    if (parsedData.currentStep > 0) {
      // 设置步骤前先确保表单数据已正确填充
      await restoreFormData(parsedData);
      // 根据父组件中存储的数据填充后，再设置当前步骤
      currentStep.value = parsedData.currentStep;
    }
    
    return true;
  } catch (error) {
    console.error('恢复缓存数据失败:', error);
    // 如果恢复失败，显示警告消息但不中断流程
    ElMessage.warning('恢复之前的进度失败，将重新开始');
    return false;
  }
};

// 根据不同的接入方式，恢复各个表单的状态
const restoreFormData = async (parsedData: any) => {
  const { currentTab, accessGuideData } = parsedData;
  
  // 首先恢复需求信息表单
  // 这个表单的数据已经通过 requirementData.value 设置

  // 根据不同接入方式恢复对应表单数据
  if (currentTab === 'dts' && dtsFormRef.value) {
    // 首先恢复 DTSAccessGuideForm 中的所有表单数据
    
    // 恢复同步字段表单数据
    if (accessGuideData.syncFieldList && accessGuideData.syncFieldList.length > 0) {
      await nextTick();
      await sleep(200); // 等待300毫秒确保表单已渲染
      dtsFormRef.value.setSyncFieldsData(accessGuideData.syncFieldList);
    }
    
    // 恢复同步配置表单数据
    if (accessGuideData.syncConfig) {
      await sleep(200); // 等待300毫秒确保表单已渲染
      // 使用新方法设置完整的同步配置数据
      dtsFormRef.value.setSyncConfigData(accessGuideData.syncConfig);
    }
    
    // 恢复查询字段表单数据
    if (accessGuideData.queryFieldList && accessGuideData.queryFieldList.length > 0) {
      await sleep(200); // 等待300毫秒确保表单已渲染
      dtsFormRef.value.setQueryFieldsData(
        accessGuideData.queryFieldList, 
        accessGuideData.provideQueryService || false
      );
    }
    
    // 如果有订阅URL，恢复到DTS任务表单
    if (accessGuideData.dtsSubscriptionUrl) {
      await sleep(200); // 等待300毫秒确保表单已渲染
      dtsFormRef.value.setSubscriptionUrl(accessGuideData.dtsSubscriptionUrl);
    }
    
    // 手动触发一次DTS表单验证
    await sleep(300); // 等待500毫秒确保所有表单已加载
    dtsFormRef.value.validateCurrentStepForm();
  } 
  else if (currentTab === 'mafka' && mafkaFormRef.value) {
    // 恢复 Mafka 配置表单数据
    if (accessGuideData.mafkaConfig) {
      await nextTick();
      await sleep(200); // 等待300毫秒确保表单已渲染
      // Mafka配置表单数据恢复
      mafkaFormRef.value.setMafkaConfigData(accessGuideData.mafkaConfig);
    }
    
    // 恢复同步字段表单数据
    if (accessGuideData.syncFieldList && accessGuideData.syncFieldList.length > 0) {
      await sleep(200); // 等待300毫秒确保表单已渲染
      mafkaFormRef.value.setSyncFieldsData(accessGuideData.syncFieldList);
    }
    
    // 恢复同步配置表单数据
    if (accessGuideData.syncConfig) {
      await sleep(200); // 等待300毫秒确保表单已渲染
      // 使用新方法设置完整的同步配置数据
      mafkaFormRef.value.setSyncConfigData(accessGuideData.syncConfig);
    }
    
    // 恢复查询字段表单数据
    if (accessGuideData.queryFieldList && accessGuideData.queryFieldList.length > 0) {
      await sleep(200); // 等待300毫秒确保表单已渲染
      mafkaFormRef.value.setQueryFieldsData(
        accessGuideData.queryFieldList,
        accessGuideData.provideQueryService || false
      );
    }
    
    // 如果有Crane URL，恢复到Crane配置表单
    if (accessGuideData.craneUrlInfo) {
      await sleep(200); // 等待300毫秒确保表单已渲染
      mafkaFormRef.value.setCraneUrl(accessGuideData.craneUrlInfo);
    }
    
    // 手动触发一次Mafka表单验证
    await sleep(300); // 等待500毫秒确保所有表单已加载
    mafkaFormRef.value.validateCurrentStepForm();
  }
  else if (currentTab === 'rpc' && rpcFormRef.value) {
    // 恢复RPC接入的表单数据
    
    // 恢复查询字段表单数据
    if (accessGuideData.queryFieldList && accessGuideData.queryFieldList.length > 0) {
      await nextTick();
      await sleep(200); // 等待200毫秒确保表单已渲染
      rpcFormRef.value.setQueryFieldsData(
        accessGuideData.queryFieldList,
        accessGuideData.provideQueryService || true // RPC接入默认提供查询功能
      );
    }
    
    // 恢复DSL配置表单数据
    if (accessGuideData.dslConfig) {
      await sleep(200); // 等待200毫秒确保表单已渲染
      rpcFormRef.value.setDslConfigData(accessGuideData.dslConfig);
    }
    
    // 手动触发一次RPC表单验证
    await sleep(300); // 等待300毫秒确保所有表单已加载
    rpcFormRef.value.validateCurrentStepForm();
  }
  
  // 在所有表单恢复完成后，设置stepValid为true
  await sleep(300); // 等待800毫秒确保所有表单已完成验证
  stepValid.value = true;
  
  // 其他接入方式的恢复逻辑可以在这里添加...
};

// 添加sleep辅助函数，用于异步等待
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 添加存储到本地缓存的方法
const saveToLocalStorage = () => {
  try {
    // 更新accessGuideData确保包含所有最新数据
    accessGuideData.value = {
      requirementInfo: requirementData.value,
      accessWay: currentTab.value,
      syncFieldList: syncMetadataData.value,
      queryFieldList: queryMetadataData.value,
      provideQueryService: provideQueryService.value,
      syncConfig: syncConfigData.value,
      dslConfig: dslConfigData.value,
      dtsSubscriptionUrl: accessGuideData.value.dtsSubscriptionUrl,
      mafkaConfig: accessGuideData.value.mafkaConfig,
      craneUrlInfo: accessGuideData.value.craneUrlInfo
    };
    
    // 需要保存的完整状态
    const stateToSave = {
      accessGuideData: accessGuideData.value,
      currentTab: currentTab.value,
      currentStep: currentStep.value,
      maxStep: maxStep.value
    };
    
    // 存储到localStorage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));
    console.log('保存进度到本地缓存:', stateToSave);
  } catch (error) {
    console.error('保存缓存数据失败:', error);
  }
};

// 清除本地缓存的方法
const clearLocalStorage = () => {
  localStorage.removeItem(STORAGE_KEY);
};

// 添加组件挂载时的处理逻辑
onMounted(async () => {
  // 检查是否有本地缓存数据
  const cachedData = localStorage.getItem(STORAGE_KEY);
  if (cachedData) {
    // 存在缓存数据，显示确认对话框
    ElMessageBox.confirm(
      '检测到您有未完成的数据接入，是否从上次保存的进度恢复？',
      '恢复进度',
      {
        confirmButtonText: '恢复',
        cancelButtonText: '重新开始',
        type: 'info',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            // 用户选择恢复，显示loading
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '恢复中...';
            
            try {
              // 尝试恢复数据
              const result = await restoreFromLocalStorage();
              if (result) {
                ElMessage.success('已从上次保存的进度恢复');
              }
            } catch (error) {
              console.error('恢复缓存数据失败:', error);
              ElMessage.warning('恢复之前的进度失败，将重新开始');
            } finally {
              instance.confirmButtonLoading = false;
              done();
            }
          } else {
            // 用户选择不恢复，清除本地缓存
            clearLocalStorage();
            ElMessage.info('已清除之前的进度，重新开始');
            done();
          }
        }
      }
    ).catch(() => {
      // 对话框被关闭（点击X或按Esc），这里不进行任何操作
    });
  }
});

// 处理需求信息表单更新
const handleRequirementUpdate = (data: RequirementInfo) => {
  requirementData.value = { ...data };
};

// 处理表单验证状态变化
const handleFormValidChange = (valid: boolean) => {
  formValid.value = valid;
};

// 处理步骤验证状态变化
const handleStepValidChange = (valid: boolean) => {
  stepValid.value = valid;
  
  // 如果当前是最后一步，并且步骤验证通过，则可以提交
  if (currentStep.value >= maxStep.value && valid) {
    console.log('可以提交');
    canSubmit.value = true;
  } else {
    canSubmit.value = false;
  }
};

// 处理同步字段元数据更新
const handleSyncMetadataUpdate = (data: SyncMetadataItem | SyncMetadataItem[]) => {
  if (Array.isArray(data)) {
    // 如果是数组，直接更新
    syncMetadataData.value = data;
  } else {
    // 如果是单个对象，添加到数组中（如果不存在）
    const exists = syncMetadataData.value.some(item => 
      item.fieldCode === data.fieldCode
    );
    
    if (!exists) {
      syncMetadataData.value.push(data);
    } else {
      // 更新已存在的元素
      syncMetadataData.value = syncMetadataData.value.map(item => 
        item.fieldCode === data.fieldCode ? data : item
      );
    }
  }
};

// 处理查询字段元数据更新
const handleQueryMetadataUpdate = (data: FieldMetadataItem | FieldMetadataItem[]) => {
  if (Array.isArray(data)) {
    // 如果是数组，直接更新
    queryMetadataData.value = data;
  } else {
    // 如果是单个对象，添加到数组中（如果不存在）
    const exists = queryMetadataData.value.some(item => 
      item.fieldCode === data.fieldCode
    );
    
    if (!exists) {
      queryMetadataData.value.push(data);
    } else {
      // 更新已存在的元素
      queryMetadataData.value = queryMetadataData.value.map(item => 
        item.fieldCode === data.fieldCode ? data : item
      );
    }
  }
};

// 处理同步配置更新
const handleSyncConfigUpdate = (data: SyncConfigItem) => {
  // 确保更新前进行深拷贝，避免引用问题
  syncConfigData.value = JSON.parse(JSON.stringify(data));
  
  // 更新全局接入数据
  accessGuideData.value = {
    ...accessGuideData.value,
    syncConfig: syncConfigData.value
  };
};

// 处理DSL配置更新
const handleDslConfigUpdate = (data: DSLConfigItem) => {
  dslConfigData.value = data;
};

// 处理DTS表单验证状态变化 - 为了兼容性保留
const handleDtsValidChange = (valid: boolean) => {
  handleStepValidChange(valid);
};

// 处理DTS步骤更新
const handleDtsStepUpdate = (step: number) => {
  currentStep.value = step;
};

// 处理Mafka步骤更新
const handleMafkaStepUpdate = (step: number) => {
  currentStep.value = step;
};

// 处理任务完成事件
const handleTaskComplete = () => {
  // 设置可以提交的状态
  canSubmit.value = true;
};

// 处理同步字段数据实时更新
const handleSyncFieldsDataUpdate = (data: SyncMetadataItem[]) => {
  
  // 只有当有实际数据(非空字段)时才更新
  if (data.length > 0 && (data[0].fieldCode || data[0].fieldName)) {
    // 使用深拷贝更新syncMetadataData
    syncMetadataData.value = JSON.parse(JSON.stringify(data));
  }
};

// 切换标签的方法
const switchTab = (tab: string) => {
  // 如果切换了接入方式，则清空表单数据
  if (currentTab.value !== '' && currentTab.value !== tab) {
    // 清空AccessGuideData
    resetAccessGuideData();
    
    // 当前状态已变化，需要清除本地缓存
    clearLocalStorage();
  }
  
  // 更新当前选中的接入方式
  currentTab.value = tab;
  
  // 根据选择的接入方式设置最大步骤
  const selectedWay = accessWays.find(way => way.key === tab);
  if (selectedWay) {
    maxStep.value = selectedWay.maxStep;
  } else {
    maxStep.value = 0;
  }
  
  // 重置步骤状态
  currentStep.value = 0;
};

// 重置访问指引数据
const resetAccessGuideData = () => {
  // 重置所有数据为初始状态
  syncMetadataData.value = [];
  queryMetadataData.value = [];
  provideQueryService.value = false;
  syncConfigData.value = undefined;
  dslConfigData.value = undefined;
  
  // 如果是DTS模式，重置其表单
  if (dtsFormRef.value) {
    // 使用resetAllForms方法重置所有表单
    dtsFormRef.value.resetAllForms();
  }
  
  // 如果是Mafka模式，重置其表单
  if (mafkaFormRef.value) {
    // 使用resetAllForms方法重置所有表单
    mafkaFormRef.value.resetAllForms();
  }
  
  // 如果是RPC模式，重置其表单
  if (rpcFormRef.value) {
    // 使用resetAllForms方法重置所有表单
    rpcFormRef.value.resetAllForms();
  }
  
  // 更新AccessGuideData
  accessGuideData.value = {
    requirementInfo: requirementData.value,
    accessWay: currentTab.value,
    syncFieldList: syncMetadataData.value,
    queryFieldList: queryMetadataData.value,
    provideQueryService: provideQueryService.value,
    syncConfig: syncConfigData.value,
    dslConfig: dslConfigData.value,
    dtsSubscriptionUrl: '',
    mafkaConfig: undefined,
    craneUrlInfo: ''
  };
};

// 下一步
const nextStep = () => {
  // 检查是否已达到最大步骤
  if (currentStep.value >= maxStep.value) {
    return;
  }
  
  // 更新AccessGuideData
  accessGuideData.value = {
    requirementInfo: requirementData.value,
    accessWay: currentTab.value,
    syncFieldList: syncMetadataData.value,
    queryFieldList: queryMetadataData.value,
    provideQueryService: provideQueryService.value,
    syncConfig: syncConfigData.value,
    dslConfig: dslConfigData.value,
    dtsSubscriptionUrl: accessGuideData.value.dtsSubscriptionUrl,
    mafkaConfig: accessGuideData.value.mafkaConfig,
    craneUrlInfo: accessGuideData.value.craneUrlInfo
  };
  
  // 输出AccessGuideData
  console.log('AccessGuideData:', accessGuideData.value);
  
  // 保存当前进度到本地缓存
  saveToLocalStorage();

  // 检查如果是DTS模式且是第3步，判断是否已完成任务
  if (currentTab.value === 'dts' && currentStep.value === 3 && dtsFormRef.value?.hasCompletedFinalStep) {
    // 如果已完成最终任务，保持可提交状态
    canSubmit.value = true;
  }
  
  // 接入指引内的步骤切换
  currentStep.value++;
  
  // 从同步字段中提取所有fieldCode，过滤掉undefined值并明确类型为string[]
  const fieldCodes: string[] = syncMetadataData.value
    .map(item => item.fieldCode)
    .filter((code): code is string => code !== undefined && code !== null);
  
  // 如果是DTS模式，并且当前步骤从1切换到2（进入同步配置步骤）
  if (currentTab.value === 'dts' && currentStep.value === 2) {
    // 将同步字段传递给DTSAccessGuideForm组件处理
    if (dtsFormRef.value) {
      dtsFormRef.value.setSyncFieldCodes(fieldCodes);
    }
  }
  
  // 如果是DTS模式，并且当前步骤从2切换到3（进入查询功能配置步骤）
  if (currentTab.value === 'dts' && currentStep.value === 3) {
    // 默认开启查询功能
    provideQueryService.value = true;
    
    // 将同步字段元数据传递给DTSAccessGuideForm组件处理
    if (dtsFormRef.value) {
      // 检查查询字段是否已经有数据，只有在没有数据时才自动填充
      const hasExistingQueryFields = queryMetadataData.value && queryMetadataData.value.length > 0;
      if (!hasExistingQueryFields) {
        dtsFormRef.value.setQueryFieldsData(syncMetadataData.value, true);
      }
    }
  }
  
  // 如果是Mafka模式，并且当前步骤从1切换到2（从步骤1 Mafka配置转到步骤2 同步字段）
  // 这里不需要特殊处理
  
  // 如果是Mafka模式，并且当前步骤从2切换到3（从同步字段转到同步配置）
  if (currentTab.value === 'mafka' && currentStep.value === 3) {
    // 将同步字段传递给MafkaAccessGuideForm组件处理
    if (mafkaFormRef.value) {
      mafkaFormRef.value.setSyncFieldCodes(fieldCodes);
      
      // 如果有Mafka配置信息，传递给MafkaAccessGuideForm用于更新同步配置中的Mafka配置项
      if (accessGuideData.value.mafkaConfig) {
        // 传递Mafka配置中的Namespace、Group、Topic到同步配置
        mafkaFormRef.value.updateMafkaConsumerConfig(accessGuideData.value.mafkaConfig);
      }
    }
  }
  
  // 如果是Mafka模式，并且当前步骤从3切换到4（从同步配置转到查询功能配置）
  if (currentTab.value === 'mafka' && currentStep.value === 4) {
    // 默认开启查询功能
    provideQueryService.value = true;
    
    // 将同步字段元数据传递给MafkaAccessGuideForm组件处理
    if (mafkaFormRef.value) {
      // 检查查询字段是否已经有数据，只有在没有数据时才自动填充
      const hasExistingQueryFields = queryMetadataData.value && queryMetadataData.value.length > 0;
      if (!hasExistingQueryFields) {
        mafkaFormRef.value.setQueryFieldsData(syncMetadataData.value, true);
      }
    }
  }
  if (currentTab.value === 'mafka' && currentStep.value === 5 && mafkaFormRef.value?.hasCompletedFinalStep) {
    // 如果已完成最终任务，保持可提交状态
    canSubmit.value = true;
  } else if (currentTab.value === 'rpc' && currentStep.value === 3) {
    // RPC模式在到达最终步骤时，自动设置可提交状态
    // 会在表单验证通过后由handleStepValidChange设置可提交状态
    if (rpcFormRef.value && rpcFormRef.value.validateCurrentStepForm) {
      setTimeout(() => {
        // 延迟执行验证，确保组件已更新
        rpcFormRef.value.validateCurrentStepForm();
      }, 200);
    }
  } else {
    canSubmit.value = false;
  }

  // 步骤切换后主动验证当前表单状态
  validateCurrentForm();
};

// 上一步
const prevStep = () => {
  // 检查是否已经是最小步骤
  if (currentStep.value < 0) {
    return;
  }
  
  // 如果是从步骤1回到步骤0（需求信息），需要保存当前表单数据而不清空
  // 这里不需要做额外处理，因为数据已经通过事件同步到了父组件
  
  // 接入指引内的步骤切换
  currentStep.value--;
  if (currentStep.value === maxStep.value) {
    currentStep.value--;
  }
  
  // 保存当前进度到本地缓存
  saveToLocalStorage();
};

// 主动验证当前表单
const validateCurrentForm = () => {
  // 步骤0是需求信息表单，其他步骤由各接入方式组件处理
  if (currentStep.value === 0) {
    // 暂不处理，因为需求信息表单已有自动验证逻辑
    return;
  }
  
  // 对于步骤1及以后，调用当前接入方式组件的验证方法
  if (currentTab.value === 'dts' && dtsFormRef.value) {
    // 调用DTS表单的验证方法
    dtsFormRef.value.validateCurrentStepForm();
  }
  // 如果是Mafka模式，调用Mafka表单的验证方法
  else if (currentTab.value === 'mafka' && mafkaFormRef.value) {
    mafkaFormRef.value.validateCurrentStepForm();
  }
  // 如果是RPC模式，调用RPC表单的验证方法
  else if (currentTab.value === 'rpc' && rpcFormRef.value) {
    rpcFormRef.value.validateCurrentStepForm();
  }
  // 后续可以添加其他接入方式的处理逻辑
};

// 提交接入指引
const submitAccessGuide = async () => {
  // 更新AccessGuideData
  accessGuideData.value = {
    requirementInfo: requirementData.value,
    accessWay: currentTab.value,
    syncFieldList: syncMetadataData.value,
    queryFieldList: queryMetadataData.value,
    provideQueryService: provideQueryService.value,
    syncConfig: syncConfigData.value,
    dslConfig: dslConfigData.value,
    dtsSubscriptionUrl: accessGuideData.value.dtsSubscriptionUrl,
    mafkaConfig: accessGuideData.value.mafkaConfig,
    craneUrlInfo: accessGuideData.value.craneUrlInfo
  };
  
  console.log('提交接入指引', accessGuideData.value);
  
  // 设置提交按钮loading状态
  isSubmitting.value = true;
  
  try {
    const currentAccessWayDetails = accessWays.find(way => way.key === currentTab.value);
    const payload = {
      changeDescription: `提交 ${currentAccessWayDetails?.title || accessGuideData.value.accessWay} 接入指引`,
      changeBefore: null,
      changeAfter: accessGuideData.value,
      requestBaseUrl: '', 
      requestUri: '',
      requestData: accessGuideData.value 
    };

    // 调用新的请求方法
    const res = await submitAccessGuideRequest(payload as Omit<McmChangeItem, 'changeBefore'>);

    if (res.code === 0) {
      // 提交成功
      if (res.data && typeof res.data === 'string') {
        ElMessage({
          message: h('div', {}, [
            res.message || '提交成功，数据接入指引已发起审批',
            '，页面即将刷新，',
            h('a', {
              href: res.data,
              target: '_blank',
              class: 'view-task-link', 
              style: { color: '#409eff', textDecoration: 'underline', cursor: 'pointer' }
            }, '查看任务')
          ]),
          type: 'success',
          dangerouslyUseHTMLString: true
        });
      } else {
        ElMessage.success(res.message || '提交成功，数据接入指引已发起审批');
      }
      
      clearLocalStorage();
      setTimeout(() => {
        window.location.reload();
      }, 5000);
    } else {
      ElMessage.error(res.message || '提交失败，请稍后重试');
    }
  } catch (error) {
    console.error('提交失败:', error);
    const errorMessage = error instanceof Error && error.message.includes('timeout')
      ? '请求超时，请稍后重试'
      : ((error as any)?.response?.data?.message || '提交失败，请联系管理员');
    ElMessage.error(errorMessage);
  } finally {
    isSubmitting.value = false;
  }
};

// 更新是否提供查询功能
const handleProvideQueryServiceUpdate = (provide: boolean) => {
  provideQueryService.value = provide;
};

// 更新订阅URL
const handleSubscriptionUrlUpdate = (url: string) => {
  accessGuideData.value.dtsSubscriptionUrl = url;
};

// 处理Mafka配置更新
const handleMafkaConfigUpdate = (data: any) => {
  // 更新全局接入数据中的Mafka配置
  accessGuideData.value = {
    ...accessGuideData.value,
    mafkaConfig: data
  };
};

// 处理Crane URL更新
const handleCraneUrlUpdate = (url: string) => {
  accessGuideData.value.craneUrlInfo = url;
};

// 处理AI配置结果
const handleAiConfigResult = async (aiConfigData: any) => {
  // 1. 自动切换到对应的接入方式
  const accessWay = (aiConfigData.accessWay || '').toLowerCase();
  currentTab.value = accessWay;
  // 2. 自动设置maxStep
  const selectedWay = accessWays.find(way => way.key === accessWay);
  if (selectedWay) {
    maxStep.value = selectedWay.maxStep;
  } else {
    maxStep.value = 0;
  }
  // 3. 自动切换到第一个步骤
  currentStep.value = 1;
  await nextTick();
  
  // 4. 修正字段类型和默认值
  if (aiConfigData.syncFieldList) {
    aiConfigData.syncFieldList = fixFieldTypeAndDefault(aiConfigData.syncFieldList);
  }
  if (aiConfigData.queryFieldList) {
    aiConfigData.queryFieldList = fixFieldTypeAndDefault(aiConfigData.queryFieldList);
  }
  
  // 6. 分步骤恢复各子表单数据（参考restoreFromLocalStorage）
  if (accessWay === 'dts' && dtsFormRef.value) {
    if (aiConfigData.syncFieldList && aiConfigData.syncFieldList.length > 0) {
      await sleep(200);
      dtsFormRef.value.setSyncFieldsData(aiConfigData.syncFieldList);
    }
    if (aiConfigData.syncConfig) {
      await sleep(200);
      dtsFormRef.value.setSyncConfigData(aiConfigData.syncConfig);
    }
    if (aiConfigData.queryFieldList && aiConfigData.queryFieldList.length > 0) {
      await sleep(200);
      dtsFormRef.value.setQueryFieldsData(
        aiConfigData.queryFieldList,
        aiConfigData.provideQueryService
      );
    }
    if (aiConfigData.dtsSubscriptionUrl) {
      await sleep(200);
      dtsFormRef.value.setSubscriptionUrl(aiConfigData.dtsSubscriptionUrl);
    }
    await sleep(300);
    dtsFormRef.value.validateCurrentStepForm();
  } else if (accessWay === 'mafka' && mafkaFormRef.value) {
    if (aiConfigData.mafkaConfig) {
      await nextTick();
      await sleep(200);
      mafkaFormRef.value.setMafkaConfigData(aiConfigData.mafkaConfig);
    }
    if (aiConfigData.syncFieldList && aiConfigData.syncFieldList.length > 0) {
      await sleep(200);
      mafkaFormRef.value.setSyncFieldsData(aiConfigData.syncFieldList);
    }
    if (aiConfigData.syncConfig) {
      await sleep(200);
      mafkaFormRef.value.setSyncConfigData(aiConfigData.syncConfig);
    }
    if (aiConfigData.queryFieldList && aiConfigData.queryFieldList.length > 0) {
      await sleep(200);
      mafkaFormRef.value.setQueryFieldsData(
        aiConfigData.queryFieldList,
        aiConfigData.provideQueryService
      );
    }
    if (aiConfigData.craneUrlInfo) {
      await sleep(200);
      mafkaFormRef.value.setCraneUrl(aiConfigData.craneUrlInfo);
    }
    await sleep(300);
    mafkaFormRef.value.validateCurrentStepForm();
  } else if (accessWay === 'rpc' && rpcFormRef.value) {
    if (aiConfigData.queryFieldList && aiConfigData.queryFieldList.length > 0) {
      await nextTick();
      await sleep(200);
      rpcFormRef.value.setQueryFieldsData(
        aiConfigData.queryFieldList,
        true
      );
    }
    if (aiConfigData.dslConfig) {
      await sleep(200);
      rpcFormRef.value.setDslConfigData(aiConfigData.dslConfig);
    }
    await sleep(300);
    rpcFormRef.value.validateCurrentStepForm();
  }
  ElMessage.success('AI生成配置已自动填充，请检查并完善');
};

function fixFieldTypeAndDefault(fields: any[]) {
  if (!Array.isArray(fields)) return [];
  return fields.map(field => {
    let typeStr = field.type !== undefined && field.type !== null ? String(field.type) : '';
    const typeOption = FIELD_TYPE_OPTIONS.find(opt => opt.value === typeStr);
    return {
      ...field,
      type: typeOption ? typeOption.value : '',
      defaultValue: field.defaultValue ?? ''
    };
  });
}
</script>

<style scoped lang="scss">
.access-guide-container {
  padding: 0 20px 0 20px;

  .section-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .guide-alert {
    margin-bottom: 20px;
    
    :deep(.el-alert__content) {
      padding: 0 8px;
    }

    :deep(.el-alert__icon) {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .card-container {
    display: flex;
    gap: 20px;
    // margin-bottom: 20px;
    
    .access-card {
      flex: 1;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 8px;
      border: 2px solid transparent;
      
      &.active {
        border: 2px solid var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
        
        .card-icon {
          color: var(--el-color-primary);
        }
        
        .card-title {
          color: var(--el-color-primary);
        }
      }
      
      &.disabled {
        cursor: not-allowed;
        // background-color: #f5f5f5;
        // opacity: 0.8;
        border: 2px solid transparent;
        box-shadow: none;
        
        .card-icon, .card-title, .card-desc {
          color: #b0b0b0;
        }
      }
      
      .card-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 10px;
        
        .card-icon {
          color: var(--el-color-primary);
          margin-bottom: 8px;
          
          &.disabled-icon {
            color: #b0b0b0;
          }
        }
        
        .card-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 6px;
        }
        
        .card-desc {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          text-align: center;
        }
      }
    }
  }
  
  .access-way-tip {
    margin-bottom: 20px;
  }

  .content-container {
    // background-color: var(--el-bg-color-page);
    border-radius: 8px;
    // padding: 20px;
    // min-height: 400px;
    position: relative;
    padding-bottom: 50px;
    
    .navigation-buttons {
      position: absolute;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      
      .spacer {
        flex-grow: 1;
      }
    }
    
    .guide-content {
      .guide-card {
        border-radius: 8px;
        
        :deep(.el-card__header) {
          padding: 15px 20px;
          border-bottom: 1px solid var(--el-border-color-light);
          background-color: var(--el-fill-color-light);
        }
        
        .card-header {
          display: flex;
          align-items: center;
          
          .header-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
        }
        
        .card-content {
          padding: 16px 0;
          
          p {
            line-height: 1.6;
            margin-top: 0;
          }
        }
      }
    }
  }
}
</style> 