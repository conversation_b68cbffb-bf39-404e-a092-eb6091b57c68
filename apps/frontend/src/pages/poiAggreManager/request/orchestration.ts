import httpRequest from '../../../shared/utils/httpRequest'

/**
 * 解析DSL
 * @param dsl 编排DSL JSON字符串
 */
export const parseDsl = (dsl: string) => {
  return httpRequest.rawRequestPostAsJson('/api/businessoms/poiaggremanage/tools/orchestration/parse', { dsl })
}

/**
 * 将Workflow对象转换为DSL JSON字符串
 * @param workflow Workflow对象
 */
export const stringify = (workflow: any) => {
  return httpRequest.rawRequestPostAsJson('/api/businessoms/poiaggremanage/tools/orchestration/stringify', { workflow })
}

/**
 * 执行工作流
 * @param dsl 编排DSL JSON字符串
 * @param paramJson 参数JSON字符串
 * @param options 执行选项
 */
export const executeWorkflow = (dsl: string, paramJson: string, options?: any) => {
  return httpRequest.rawRequestPostAsJson('/api/businessoms/poiaggremanage/tools/orchestration/execute', {
    dsl,
    paramJson,
    options
  })
}

/**
 * 执行单个任务节点
 * @param dsl 编排DSL JSON字符串
 * @param taskAlias 任务别名
 * @param paramJson 参数JSON字符串
 * @param mockTaskResults 模拟的任务结果（用于调试）
 */
export const executeTask = (dsl: string, taskAlias: string, paramJson: string, mockTaskResults?: Record<string, any>) => {
  return httpRequest.rawRequestPostAsJson('/api/businessoms/poiaggremanage/tools/orchestration/execute-task', {
    dsl,
    taskAlias,
    paramJson,
    mockTaskResults
  })
}

/**
 * 保存工作流
 * @param name 工作流名称
 * @param dsl 编排DSL JSON字符串 
 */
export const saveWorkflow = (name: string, dsl: string) => {
  return httpRequest.rawRequestPostAsJson('/api/businessoms/poiaggremanage/tools/orchestration/save', {
    name,
    dsl
  })
}

/**
 * 获取工作流列表
 */
export const getWorkflowList = () => {
  return httpRequest.rawRequestGet('/api/businessoms/poiaggremanage/tools/orchestration/list', {})
}

/**
 * 获取工作流详情
 * @param id 工作流ID
 */
export const getWorkflowDetail = (id: string) => {
  return httpRequest.rawRequestGet('/api/businessoms/poiaggremanage/tools/orchestration/detail', { id })
}

/**
 * 获取历史执行记录
 * @param workflowId 工作流ID
 */
export const getExecutionHistory = (workflowId: string) => {
  return httpRequest.rawRequestGet('/api/businessoms/poiaggremanage/tools/orchestration/execution-history', { 
    workflowId 
  })
}

/**
 * 获取执行结果详情
 * @param executionId 执行ID
 */
export const getExecutionDetail = (executionId: string) => {
  return httpRequest.rawRequestGet('/api/businessoms/poiaggremanage/tools/orchestration/execution-detail', {
    executionId
  })
} 