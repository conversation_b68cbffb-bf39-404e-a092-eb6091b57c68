import type {
  ApiResponse,
  DSLConfigItem,
  DSLConfigQueryParams,
  FieldMetadataItem,
  FieldMetadataQueryParams,
  PageResult,
  QueryParams,
  SceneItem,
  SyncConfigItem,
  SyncConfigQueryParams,
  SyncMetadataItem,
  McmChangeItem,
  TaskQueryParams,
  TaskItem,
  AccessGuideData
} from '../types';
import httpRequest from '../../../shared/utils/httpRequest';

// 查询场景相关API
const SCENE_BASE_URL = '/api/businessoms/poiAggreManage/manager/query/scene';

export const getSceneList = (params: QueryParams) => httpRequest.rawRequestPostAsJson(
  `${SCENE_BASE_URL}/page`,
  params
) as unknown as Promise<ApiResponse<PageResult<SceneItem>>>;

export const addScene = (data: Omit<SceneItem, 'id' | 'opName' | 'opMis' | 'ctime' | 'utime'>) => httpRequest.rawRequestPostAsJson(
  `${SCENE_BASE_URL}/add`,
  data
) as unknown as Promise<ApiResponse<boolean>>;

export const updateScene = (data: Partial<SceneItem> & { id: number }) => httpRequest.rawRequestPostAsJson(
  `${SCENE_BASE_URL}/update`,
  data
) as unknown as Promise<ApiResponse<boolean>>;

export const batchDeleteScene = (ids: number[]) => httpRequest.rawRequestPostAsJson(
  `${SCENE_BASE_URL}/deleteByIds`,
  ids
) as unknown as Promise<ApiResponse<boolean>>;

// 查询字段元数据相关API
const FIELD_METADATA_BASE_URL = '/api/businessoms/poiAggreManage/manager/query/field/metadata';

export const getFieldMetadataList = (params: FieldMetadataQueryParams) => httpRequest.rawRequestPostAsJson(
  `${FIELD_METADATA_BASE_URL}/page`,
  params
) as unknown as Promise<ApiResponse<PageResult<FieldMetadataItem>>>;

export const addFieldMetadata = (data: Omit<McmChangeItem, 'changeBefore'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '新增查询字段',
    requestBaseUrl: FIELD_METADATA_BASE_URL,
    requestUri: '/add'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${FIELD_METADATA_BASE_URL}/add`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const batchAddFieldMetadata = (data: Omit<McmChangeItem, 'changeBefore'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '批量新增查询字段',
    requestBaseUrl: FIELD_METADATA_BASE_URL,
    requestUri: '/batchAdd'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${FIELD_METADATA_BASE_URL}/batchAdd`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const updateFieldMetadata = (data: Omit<McmChangeItem, 'requestData'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '查询字段变更',
    requestBaseUrl: FIELD_METADATA_BASE_URL,
    requestUri: '/update'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${FIELD_METADATA_BASE_URL}/update`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const batchDeleteFieldMetadata = (data: Omit<McmChangeItem, 'changeAfter'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '批量删除查询字段',
    requestBaseUrl: FIELD_METADATA_BASE_URL,
    requestUri: '/deleteByIds'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${FIELD_METADATA_BASE_URL}/deleteByIds`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

// 服务编排相关API
const SERVICE_ORCHESTRATION_BASE_URL = '/api/businessoms/poiAggreManage/manager/query/dsl';

export const getDSLConfigList = (params: DSLConfigQueryParams) => httpRequest.rawRequestPostAsJson(
  `${SERVICE_ORCHESTRATION_BASE_URL}/page`,
  params
) as unknown as Promise<ApiResponse<PageResult<DSLConfigItem>>>;

export const addDSLConfig = (data: Omit<McmChangeItem, 'changeBefore'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '新增服务编排',
    requestBaseUrl: SERVICE_ORCHESTRATION_BASE_URL,
    requestUri: '/add'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SERVICE_ORCHESTRATION_BASE_URL}/add`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const updateDSLConfig = (data: Omit<McmChangeItem, 'requestData'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '服务编排变更',
    requestBaseUrl: SERVICE_ORCHESTRATION_BASE_URL,
    requestUri: '/update'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SERVICE_ORCHESTRATION_BASE_URL}/update`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const batchDeleteDSLConfig = (data: Omit<McmChangeItem, 'changeAfter'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '批量删除服务编排',
    requestBaseUrl: SERVICE_ORCHESTRATION_BASE_URL,
    requestUri: '/deleteByIds'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SERVICE_ORCHESTRATION_BASE_URL}/deleteByIds`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

// 同步字段元数据相关API
const SYNC_FIELD_METADATA_BASE_URL = '/api/businessoms/poiAggreManage/manager/sync/field/metadata';

export const getSyncFieldMetadataList = (params: QueryParams) => httpRequest.rawRequestPostAsJson(
  `${SYNC_FIELD_METADATA_BASE_URL}/page`,
  params
) as unknown as Promise<ApiResponse<PageResult<SyncMetadataItem>>>;

export const addSyncFieldMetadata = (data: Omit<McmChangeItem, 'changeBefore'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '新增同步字段元数据',
    requestBaseUrl: SYNC_FIELD_METADATA_BASE_URL,
    requestUri: '/add'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SYNC_FIELD_METADATA_BASE_URL}/add`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const batchAddSyncFieldMetadata = (data: Omit<McmChangeItem, 'changeBefore'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '批量新增同步字段元数据',
    requestBaseUrl: SYNC_FIELD_METADATA_BASE_URL,
    requestUri: '/batchAdd'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SYNC_FIELD_METADATA_BASE_URL}/batchAdd`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const updateSyncFieldMetadata = (data: Omit<McmChangeItem, 'requestData'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '同步字段元数据变更',
    requestBaseUrl: SYNC_FIELD_METADATA_BASE_URL,
    requestUri: '/update'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SYNC_FIELD_METADATA_BASE_URL}/update`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const batchDeleteSyncFieldMetadata = (data: Omit<McmChangeItem, 'changeAfter'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '批量删除同步字段元数据',
    requestBaseUrl: SYNC_FIELD_METADATA_BASE_URL,
    requestUri: '/deleteByIds'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SYNC_FIELD_METADATA_BASE_URL}/deleteByIds`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

// 同步配置相关API
const SYNC_CONFIG_BASE_URL = '/api/businessoms/poiAggreManage/manager/sync/config';

export const getSyncConfigList = (params: SyncConfigQueryParams) => httpRequest.rawRequestPostAsJson(
  `${SYNC_CONFIG_BASE_URL}/page`,
  params
) as unknown as Promise<ApiResponse<PageResult<SyncConfigItem>>>;

export const addSyncConfig = (data: Omit<McmChangeItem, 'changeBefore'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '新增同步配置',
    requestBaseUrl: SYNC_CONFIG_BASE_URL,
    requestUri: '/add'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SYNC_CONFIG_BASE_URL}/add`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const updateSyncConfig = (data: Omit<McmChangeItem, 'requestData'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '同步配置变更',
    requestBaseUrl: SYNC_CONFIG_BASE_URL,
    requestUri: '/update'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SYNC_CONFIG_BASE_URL}/update`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

export const batchDeleteSyncConfig = (data: Omit<McmChangeItem, 'changeAfter'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '批量删除同步配置',
    requestBaseUrl: SYNC_CONFIG_BASE_URL,
    requestUri: '/deleteByIds'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${SYNC_CONFIG_BASE_URL}/deleteByIds`,
    requestData
  ) as unknown as Promise<ApiResponse<boolean>>;
};

// 任务相关API
const TASK_BASE_URL = '/api/businessoms/poiAggreManage/taskCenter'

// 获取任务列表
export const getTaskList = (params: TaskQueryParams) => {
  return httpRequest.rawRequestGet(
    `${TASK_BASE_URL}/page`,
    {
      pageNo: params.pageNo,
      pageSize: params.pageSize,
      taskType: params.taskType,
      status: params.status,
      personalTask: 0  // 全部任务
    }
  ) as unknown as Promise<ApiResponse<PageResult<TaskItem>>>
}

// 获取我的任务列表
export const getMyTaskList = (params: TaskQueryParams) => {
  return httpRequest.rawRequestGet(
    `${TASK_BASE_URL}/page`,
    {
      pageNo: params.pageNo,
      pageSize: params.pageSize,
      taskType: params.taskType,
      status: params.status,
      personalTask: 1  // 我的任务
    }
  ) as unknown as Promise<ApiResponse<PageResult<TaskItem>>>
}

// 接入指引相关API
const ACCESS_GUIDE_BASE_URL = '/api/businessoms/poiAggreManage/manager/accessGuide';

export const submitAccessGuideRequest = (data: Omit<McmChangeItem, 'changeBefore'>) => {
  // 在发送请求前设置requestBaseUrl和requestUri
  const requestData = {
    ...data,
    changeDescription: '接入流程提交',
    requestBaseUrl: ACCESS_GUIDE_BASE_URL,
    requestUri: '/add'
  };
  
  return httpRequest.rawRequestPostAsJson(
    `${ACCESS_GUIDE_BASE_URL}/add`,
    requestData
  ) as unknown as Promise<ApiResponse<string | boolean>>;
};

// 新增：AI生成配置
export const getAiAccessGuideConfig = (data: AccessGuideData) => {
  return httpRequest.rawRequestPostAsJson(
    `${ACCESS_GUIDE_BASE_URL}/aiConfig`,
    data
  ) as unknown as Promise<ApiResponse<Record<string, any>>>;
};