import httpRequest from '../../../shared/utils/httpRequest';
import type { ApiResponse } from '../types/index';
import type { ServiceInterfaceResponse, ServiceMethodResponse, JsonSchemaResponse, RocketHostResponse } from '../types/thriftServiceTool';

// API路径常量
const API_PATHS = {
  GET_HOSTS: '/api/businessoms/poiAggreManage/thriftServiceTool/hosts',
  GET_INTERFACES: '/api/businessoms/poiAggreManage/thriftServiceTool/interfaces',
  GET_METHODS: '/api/businessoms/poiAggreManage/thriftServiceTool/methods',
  GET_SCHEMA: '/api/businessoms/poiAggreManage/thriftServiceTool/schema'
} as const;

// 请求参数类型
export interface GetHostsParams {
  appkey: string;
  env: string;
}

export interface GetInterfacesParams {
  hostName: string;
}

export interface GetMethodsParams {
  hostName: string;
  serviceName: string;
}

export interface GetSchemaParams {
  hostName: string;
  serviceName: string;
  methodName: string;
}

// API请求方法
export const getAppkeyHosts = (params: GetHostsParams) => {
  return httpRequest.rawRequestGet(
    API_PATHS.GET_HOSTS,
    params
  ) as unknown as Promise<ApiResponse<RocketHostResponse>>;
};

export const getServiceInterfaces = (params: GetInterfacesParams) => {
  return httpRequest.rawRequestGet(
    API_PATHS.GET_INTERFACES,
    params
  ) as unknown as Promise<ApiResponse<ServiceInterfaceResponse>>;
};

export const getServiceMethods = (params: GetMethodsParams) => {
  return httpRequest.rawRequestGet(
    API_PATHS.GET_METHODS,
    params
  ) as unknown as Promise<ApiResponse<ServiceMethodResponse>>;
};

export const getMethodJsonSchema = (params: GetSchemaParams) => {
  return httpRequest.rawRequestGet(
    API_PATHS.GET_SCHEMA,
    params
  ) as unknown as Promise<ApiResponse<JsonSchemaResponse>>;
}; 