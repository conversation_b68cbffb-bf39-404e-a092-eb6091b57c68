export type WikiType = '1' | '2' | '5' | '6' | '7' | '8' | '9' | '10'

// 通用类型定义
export interface ApiResponse<T> {
    code: number
    message: string
    data: T
}

export interface PageResult<T> {
    total: number
    records: T[]
}

export interface WikiPageQuery {
    pageNo: number
    pageSize: number
    wikiType: number
    wikiParam?: string
}

export const Wiki_TYPE_OPTIONS = [
    { label: 'AI产品', value: 1 },
    { label: 'AI分享', value: 2 },
    { label: 'AI编码分享', value: 5 },
    { label: 'AI编码基建', value: 6 },
    { label: 'Cursor规则', value: 7 },
    { label: 'Prompt分享', value: 8 },
    { label: 'Mcp市场', value: 9 },
    { label: '小工具市场', value: 10 }
] as const

export interface AIWiki {
    id: number
    title: string
    description: string
    wikiUrl: string
    type: number
    tags: string[]
    department: string
    pbName: string
    pbMis: string
    opName: string
    opMis: string
    ctime?: number
    utime?: number
    star?: number
    top?: number
    options?: string | Record<string, any>
}

export interface WikiItem {
    id: string | number
    title: string
    description: string
    author: string
    authorAvatar?: string
    tags?: string[]
    updateTime?: number | string
    star?: number
    top?: number
    options?: any
}

export interface UserInfo {
    id?: number  
    login?: string
    name?: string
    code?: string
    email?: string
    tenantId?: string
    roles?: string[]
    isVerified?: boolean
    verifyType?: string
    verifyExpireTime?: number
    passport?: string
}