import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/aiWorkshop/aiProduct',
    name: 'AiProduct',
    component: () => import('./views/AIProduct.vue'),
    meta: {
      title: 'AI产品'
    }
  },
  {
    path: '/aiWorkshop/aiShare',
    name: 'AiShare',
    component: () => import('./views/AIShare.vue'),
    meta: {
      title: 'AI分享'
    }
  },
  {
    path: '/aiWorkshop/aiCodingShare',
    name: 'AiCodingShare',
    component: () => import('./views/AICodingShare.vue'),
    meta: {
      title: 'AICoding分享'
    }
  },
  {
    path: '/aiWorkshop/aiCodingInfrastructure',
    name: 'AiCodingInfrastructure',
    component: () => import('./views/AICodingInfrastructure.vue'),
    meta: {
      title: 'AICoding基建'
    }
  },
  {
    path: '/aiWorkshop/aiCursorRules',
    name: 'CursorRules',
    component: () => import('./views/CursorRules.vue'),
    meta: {
      title: 'Cursor Rules分享'
    }
  },
  {
    path: '/aiWorkshop/promptShare',
    name: 'PromptShare',
    component: () => import('./views/PromptShare.vue'),
    meta: {
      title: 'Prompt分享'
    }
  },
  {
    path: '/aiWorkshop/mcpMarket',
    name: 'McpMarket',
    component: () => import('./views/McpMarket.vue'),
    meta: {
      title: 'Mcp市场'
    }
  },
  {
    path: '/aiWorkshop/toolsMarket',
    name: 'ToolsMarket',
    component: () => import('./views/ToolsMarket.vue'),
    meta: {
      title: '小工具市场'
    }
  }
]

export default routes
