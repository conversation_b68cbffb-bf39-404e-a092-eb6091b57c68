import type { AIWiki, ApiResponse, PageResult, UserInfo, WikiPageQuery } from '../types';
import httpRequest from '../../../shared/utils/httpRequest';

// AI Wiki相关API
const AI_WIKI_BASE_URL = '/api/businessoms/aiWorkshop/aiWiki';

export const getAIWikiList = (params: WikiPageQuery) => httpRequest.rawRequestPostAsJson(
  `${AI_WIKI_BASE_URL}/page`,
  params
) as unknown as Promise<ApiResponse<PageResult<AIWiki>>>;

export const addAIWiki = (data: Partial<AIWiki> & { title: string, description: string, wikiUrl: string, type: number}) => httpRequest.rawRequestPostAsJson(
  `${AI_WIKI_BASE_URL}/add`,
  data
) as unknown as Promise<ApiResponse<boolean>>;

export const updateAIWiki = (data: Partial<AIWiki> & { id: number }) => httpRequest.rawRequestPostAsJson(
  `${AI_WIKI_BASE_URL}/update`,
  data
) as unknown as Promise<ApiResponse<boolean>>;

export const deleteAIWiki = (id: number) => httpRequest.rawRequestGet(
  `${AI_WIKI_BASE_URL}/delete/${id}`,
  {}
) as unknown as Promise<ApiResponse<boolean>>;

// 获取用户信息
const USER_BASE_URL = '/api/businessoms/system/user/current';

export const getUserInfo = () => httpRequest.rawRequestGet(
  USER_BASE_URL, 
  { redirect: 'manual' }
) as unknown as Promise<ApiResponse<UserInfo>>;