<template>
    <div class="ai-coding-share">
      <div class="page-header">
        <h1 class="title">AI编程分享</h1>
        <p class="description">履约平台技术部AI编程经验与最佳实践分享，帮助您更快掌握AI辅助编程的技巧和方法。</p>
      </div>
      
      <div class="search-container">
        <el-input
          v-model="searchQuery"
          placeholder="搜索"
          :prefix-icon="Search"
          clearable
          size="large"
          class="search-input"
          @input="handleSearch"
        />
      </div>
  
      <div v-loading="loading" class="content-container">
        <wiki-list 
          v-if="filteredWikiList.length > 0"
          :wiki-list="filteredWikiList" 
          :page-type="'coding'"
          @view="handleViewWiki"
          @edit="handleEdit"
          @delete="handleDelete"
          @update="handleUpdate"
        />
        
        <el-empty 
          v-else 
          class="empty-block"
          description="暂无数据"
        >
          <template #image>
            <el-icon class="empty-icon"><DocumentDelete /></el-icon>
          </template>
        </el-empty>
      </div>
  
      <div 
        class="floating-add-button"
        :style="{ top: buttonPosition + 'px' }"
        @mousedown="startDrag"
        @click="handleAdd"
      >
        <el-icon class="add-icon" :size="24"><Plus /></el-icon>
      </div>
  
      <wiki-form
        v-model="dialogVisible"
        :wiki-data="currentWiki"
        :type="TYPE"
        :loading="dialogLoading"
        @submit="handleSubmit"
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { Search, Plus, DocumentDelete } from '@element-plus/icons-vue';
  import WikiList from './components/WikiList.vue';
  import WikiForm from './components/WikiForm.vue';
  import type { AIWiki, WikiPageQuery, WikiItem } from '../types';
  import { getAIWikiList, addAIWiki, updateAIWiki, deleteAIWiki } from '../request';
  import { ElMessage, ElMessageBox } from 'element-plus';
  
  const TYPE = 5; // AI编程分享类型
  const dialogVisible = ref(false);
  const currentWiki = ref<AIWiki | undefined>(undefined);
  const loading = ref(false);
  const dialogLoading = ref(false); // 对话框提交loading状态
  
  const searchQuery = ref('');
  const wikiList = ref<AIWiki[]>([]);
  const total = ref(0);
  const pageNo = ref(1);
  const pageSize = ref(300);
  
  // 获取Wiki列表数据
  const fetchWikiList = async () => {
    loading.value = true;
    try {
      const params: WikiPageQuery = {
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        wikiType: 5,
        wikiParam: searchQuery.value || undefined
      };
      
      const res = await getAIWikiList(params);
      if (res.code === 0 && res.data) {
        wikiList.value = res.data.records || [];
        total.value = res.data.total || 0;
      } else {
        ElMessage.error(res.message || '获取列表失败');
      }
    } catch (error) {
      console.error('获取Wiki列表失败:', error);
      ElMessage.error('获取Wiki列表失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };
  
  // 组件挂载时获取数据
  onMounted(() => {
    fetchWikiList();
  });
  
  // 将 AIWiki 转换为 WikiItem
  const adaptToWikiItem = (wiki: AIWiki): WikiItem => {
    return {
      id: wiki.id || 0,
      title: wiki.title,
      description: wiki.description,
      author: (wiki.department && wiki.pbName) ? `${wiki.department} - ${wiki.pbName}` : (wiki.pbName || wiki.department || '未知'),
      tags: wiki.tags || [],
      updateTime: wiki.utime,
      star: wiki.star || 0,
      top: wiki.top || 0
    };
  };
  
  const filteredWikiList = computed(() => {
    return wikiList.value
      .filter(item => item.type === TYPE)
      .map(adaptToWikiItem);
  });
  
  const handleSearch = () => {
    pageNo.value = 1; // 重置页码
    fetchWikiList();
  };
  
  const handleViewWiki = (wiki: WikiItem) => {
    // 找到对应的原始数据
    const originalWiki = wikiList.value.find(item => item.id === wiki.id);
    if (originalWiki && originalWiki.wikiUrl) {
      window.open(originalWiki.wikiUrl, '_blank');
    }
  };
  
  const handleEdit = (wiki: WikiItem) => {
    // 找到对应的原始数据
    const originalWiki = wikiList.value.find(item => item.id === wiki.id);
    if (originalWiki) {
      currentWiki.value = { ...originalWiki };
      dialogVisible.value = true;
    }
  };
  
  const buttonPosition = ref(window.innerHeight - 150); // 初始位置设置在距离底部 150px
  let isDragging = false;
  let hasDragged = false; // 标记是否发生过拖动
  let startY = 0;
  let startTop = 0;
  
  const startDrag = (e: MouseEvent) => {
    if (e.target instanceof Element && e.target.closest('.floating-add-button')) {
      e.preventDefault(); // 阻止默认行为
      isDragging = true;
      hasDragged = false; // 重置拖动标记
      startY = e.clientY;
      startTop = buttonPosition.value;
      
      // 添加全局样式防止文本选择
      document.body.style.userSelect = 'none';
      document.body.style.webkitUserSelect = 'none';
      
      const handleMouseMove = (e: MouseEvent) => {
        if (!isDragging) return;
        
        const deltaY = e.clientY - startY;
        // 只有当移动超过5px时才认为是拖动
        if (Math.abs(deltaY) > 5) {
          hasDragged = true;
        }
        
        const newTop = startTop + deltaY;
        
        // 限制拖动范围在视窗内，底部留出更多空间
        const minTop = 100;
        const maxTop = window.innerHeight - 150; // 距离底部至少 150px
        buttonPosition.value = Math.min(Math.max(newTop, minTop), maxTop);
      };
      
      const handleMouseUp = () => {
        isDragging = false;
        // 移除全局样式
        document.body.style.userSelect = '';
        document.body.style.webkitUserSelect = '';
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        
        // 300ms后重置拖动状态，防止瞬间点击
        setTimeout(() => {
          hasDragged = false;
        }, 300);
      };
      
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  };
  
  const handleAdd = (e: MouseEvent) => {
    // 如果是拖动过程中或拖动刚结束，不触发添加事件
    if (isDragging || hasDragged) {
      return;
    }
    currentWiki.value = undefined;
    dialogVisible.value = true;
  };
  
  const handleSubmit = async (formData: AIWiki) => {
    dialogLoading.value = true; // 开启loading状态
    
    try {
      let res;
      
      // 创建超时控制器
      const controller = new AbortController();
      const timeout = setTimeout(() => {
        controller.abort();
      }, 300000); // 30秒超时
      
      if (formData.id) {
        // 更新
        res = await updateAIWiki(formData);
      } else {
        // 新增
        res = await addAIWiki({
          ...formData,
          type: TYPE
        });
      }
      
      clearTimeout(timeout); // 清除超时定时器
      
      if (res.code === 0) {
        ElMessage.success(formData.id ? '更新成功' : '添加成功');
        dialogVisible.value = false;
        fetchWikiList(); // 刷新列表
      } else {
        ElMessage.error(res.message || (formData.id ? '更新失败' : '添加失败'));
      }
    } catch (error) {
      console.error('提交数据失败:', error);
      // 判断是否为超时错误
      const errorMessage = error instanceof Error && error.message.includes('timeout')
        ? '请求超时，请稍后重试'
        : '操作失败，请稍后重试';
      ElMessage.error(errorMessage);
    } finally {
      dialogLoading.value = false; // 关闭loading状态
    }
  };
  
  const handleDelete = async (wiki: WikiItem) => {
    try {
      await ElMessageBox.confirm('确定要删除该知识条目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      
      const res = await deleteAIWiki(Number(wiki.id));
      if (res.code === 0) {
        ElMessage.success('删除成功');
        fetchWikiList(); // 刷新列表
      } else {
        ElMessage.error(res.message || '删除失败');
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error);
        ElMessage.error('删除失败，请稍后重试');
      }
    }
  };
  
  const handleUpdate = async (wiki: WikiItem) => {
    try {
      // 找到对应的原始数据
      const originalWiki = wikiList.value.find(item => item.id === wiki.id);
      if (!originalWiki) return;
  
      // 判断操作类型
      let message = '';
      if (originalWiki.star !== wiki.star) {
        message = wiki.star === 1 ? '设为星标成功' : '取消星标成功';
      } else if (originalWiki.top !== wiki.top) {
        message = wiki.top === 1 ? '设为置顶成功' : '取消置顶成功';
      }
  
      // 更新数据
      const updatedWiki = {
        ...originalWiki,
        star: wiki.star,
        top: wiki.top
      };
  
      const res = await updateAIWiki(updatedWiki);
      if (res.code === 0) {
        ElMessage.success(message);
        fetchWikiList(); // 刷新列表
      } else {
        ElMessage.error(res.message || '操作失败');
      }
    } catch (error) {
      console.error('更新失败:', error);
      ElMessage.error('操作失败，请稍后重试');
    }
  };
  
  // 导出所有方法
  defineExpose({
    handleUpdate
  });
  </script>
  
  <style lang="scss" scoped>
  .ai-coding-share {
    padding: 20px;
    position: relative;
    overflow: hidden;
  
    /* 添加内容容器样式 */
    .content-container {
      min-height: 300px;
      position: relative;
      margin-bottom: 30px;
    }
    
    /* 空状态样式 */
    .empty-block {
      padding: 60px 0;
      
      .empty-icon {
        font-size: 60px;
        color: var(--el-color-info-light-5);
        margin-bottom: 20px;
      }
    }

    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: 
        radial-gradient(2px 2px at 40px 60px, #409EFF 50%, transparent 50%),
        radial-gradient(2px 2px at 20px 50px, #67C23A 50%, transparent 50%),
        radial-gradient(2px 2px at 30px 100px, #409EFF 50%, transparent 50%);
      background-size: 200px 200px;
      opacity: 0.1;
      pointer-events: none;
    }

    .page-header {
      position: relative;
      margin-bottom: 32px;
      padding: 48px 0;
      background: linear-gradient(135deg, #409EFF, #79bbff, #3ac270, #95d475);
      background-size: 300% 300%;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      text-align: center;
      animation: gradientBG 15s ease infinite alternate;

      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        right: -50%;
        bottom: -50%;
        background: 
          radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: 1;
        animation: rotateBG 30s linear infinite;
      }

      .title {
        font-size: 46px;
        font-weight: 600;
        color: #fff;
        margin: 0 0 20px;
        letter-spacing: -0.5px;
        position: relative;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        z-index: 3;
      }

      .description {
        font-size: 16px;
        line-height: 1.6;
        color: #fff;
        margin: 0 auto;
        max-width: 800px;
        opacity: 0.95;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
        text-align: center;
        backdrop-filter: blur(4px);
        background: rgba(255, 255, 255, 0.15);
        border-radius: 8px;
        padding: 16px 24px;
        margin-bottom: 20px;
        z-index: 3;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 50%;
          transform: translateX(-50%);
          width: 100px;
          height: 3px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 4px;
          opacity: 0.8;
          animation: lineStretch 3s ease-in-out infinite alternate;
        }
      }
    }
  
    .search-container {
      max-width: 1200px;
      margin: 0 auto 24px;
      padding: 0 20px;
      position: relative;
      z-index: 1;
  
      .search-input {
        :deep(.el-input__wrapper) {
          height: 48px;
          border-radius: 24px;
          background-color: #fff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          transition: box-shadow 0.2s ease;
          padding: 0 20px;
  
          &:hover, &:focus-within {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          }
  
          .el-input__prefix {
            margin-right: 8px;
          }
        }
  
        :deep(.el-input__inner) {
          font-size: 16px;
          
          &::placeholder {
            color: #909399;
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .floating-add-button {
    position: fixed;
    right: 40px;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #67C23A, #95d475);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.2s ease, transform 0.2s ease;
    z-index: 1000;
  
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(103, 194, 58, 0.2);
    }
  
    &:active {
      transform: translateY(0);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  
    .add-icon {
      color: #fff;
      font-size: 24px;
      pointer-events: none;
    }
  }
  
  @keyframes gradientBG {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  @keyframes rotateBG {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes lineStretch {
    0% {
      width: 5%;
      opacity: 0.5;
    }
    50% {
      width: 80%;
      opacity: 1;
    }
    100% {
      width: 5%;
      opacity: 0.5;
    }
  }
  </style>