<template>
  <el-dialog
    :title="isEdit ? '编辑' : '新增'"
    v-model="dialogVisible"
    :width="dialogWidth"
    :close-on-click-modal="false"
    @close="handleClose"
    class="wiki-form-dialog"
    :style="{ maxHeight: '75vh', overflow: 'hidden' }"
  >
    <div class="dialog-content">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="wiki-form"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="formData.title"
          placeholder="请输入标题"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入描述"
          maxlength="250"
          resize="none"
        />
      </el-form-item>

      <!-- AI产品和基础设施类型显示图片上传字段 -->
      <el-form-item v-if="formData.type === 1 || formData.type === 6" label="预览图片" prop="picture">
        <div class="image-upload-container">
          <div class="upload-area" @click="triggerFileInput" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
            <div v-if="!formData.picture && !isUploading" class="upload-placeholder">
              <el-icon class="upload-icon" :size="48"><Plus /></el-icon>
              <div class="upload-text">
                <p>点击上传图片</p>
                <p class="upload-tip">支持拖拽上传或 Ctrl+V 粘贴上传</p>
                <p class="upload-tip">建议尺寸：400x300，支持 JPG、PNG、GIF 格式</p>
                <p class="upload-tip">图片将转换为base64格式传输</p>
              </div>
            </div>
            <div v-else-if="isUploading" class="upload-loading">
              <el-icon class="loading-icon is-loading" :size="48"><Loading /></el-icon>
              <p class="loading-text">图片上传中...</p>
            </div>
            <div v-else class="image-preview">
              <img :src="formData.picture" alt="预览图片" class="preview-image" />
              <div class="image-overlay">
                <el-button type="primary" :icon="Edit" circle size="small" @click.stop="triggerFileInput" />
                <el-button type="danger" :icon="Delete" circle size="small" @click.stop="removeImage" />
              </div>
            </div>
          </div>
          <input
            ref="fileInputRef"
            type="file"
            accept="image/jpeg,image/jpg,image/png,image/gif"
            style="display: none"
            @change="handleFileSelect"
          />
        </div>
      </el-form-item>

      <!-- Prompt分享类型显示Prompt内容字段 -->
      <el-form-item v-if="formData.type === 8" label="Prompt内容" prop="promptContent">
        <div class="prompt-editor">
          <el-tabs v-model="activeTab" class="prompt-tabs">
            <el-tab-pane label="编辑" name="edit">
              <el-input
                v-model="formData.promptContent"
                type="textarea"
                :rows="15"
                placeholder="请输入Prompt内容，支持Markdown格式"
                resize="none"
                class="prompt-textarea"
              />
            </el-tab-pane>
            <el-tab-pane label="预览" name="preview">
              <div class="prompt-preview" v-html="promptPreview"></div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form-item>

      <!-- 非Prompt分享类型显示Wiki链接字段 -->
      <el-form-item v-if="formData.type !== 8" label="Url" prop="wikiUrl">
        <el-input
          v-model="formData.wikiUrl"
          placeholder="请输入Wiki链接"
          maxlength="200"
        />
      </el-form-item>

      <el-form-item label="分类" prop="type">
        <el-select
          v-model="formData.type"
          placeholder="请选择分类"
          class="w-full"
        >
          <el-option
            v-for="option in Wiki_TYPE_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
    </el-form-item>

    <template v-if="formData.type !== 1">
      <el-form-item label="作者" prop="pbName">
          <el-input
            v-model="formData.pbName"
            placeholder="请输入作者"
            maxlength="40"
          />
      </el-form-item>

      <el-form-item label="部门" prop="department">
          <el-input
            v-model="formData.department"
            placeholder="请输入部门"
            maxlength="40"
          />
      </el-form-item>
    </template>

    <el-form-item label="标签" prop="tags">
        <el-input
          v-model="formData.tags"
          placeholder="请输入标签，多个标签使用、分隔"
          maxlength="40"
        />
    </el-form-item>
    </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitLoading">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading" :disabled="submitLoading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { Plus, Edit, Delete, Loading } from '@element-plus/icons-vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import { Wiki_TYPE_OPTIONS } from '../../types';
import type { AIWiki } from '../../types';
import { getUserInfo } from '../../request';

interface Props {
  modelValue: boolean;
  wikiData?: AIWiki;
  type: number;
  loading?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'submit']);

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

const isEdit = computed(() => !!props.wikiData);
const loading = ref(false);
const formRef = ref<FormInstance>();

// 动态设置对话框宽度
const dialogWidth = computed(() => {
  return formData.type === 8 ? '1000px' : '600px';
});

// Prompt编辑相关状态
const activeTab = ref('edit');

// 配置marked选项
marked.setOptions({
  gfm: true,
  breaks: true,
});

interface FormData extends Omit<Partial<AIWiki>, 'tags'> {
  tags: string;
  promptContent: string; // 新增promptContent字段
  picture: string; // 新增picture字段用于存储base64图片
}

const formData = reactive<FormData>({
  title: '',
  description: '',
  pbName: '',
  department: '',
  wikiUrl: '',
  type: props.type,
  tags: '',
  promptContent: '',
  picture: ''
});

// Prompt预览内容
const promptPreview = computed(() => {
  if (!formData.promptContent) return '<p class="empty-text">暂无内容</p>';
  
  try {
    const rawHtml = marked(formData.promptContent);
    // 处理marked可能返回Promise的情况
    if (rawHtml instanceof Promise) {
      // 如果是Promise，返回加载提示
      return '<p class="loading-text">正在渲染内容...</p>';
    } else {
      return DOMPurify.sanitize(rawHtml);
    }
  } catch (error) {
    console.error('Markdown渲染失败:', error);
    return '<p class="error-text">内容渲染失败</p>';
  }
});

const rules: FormRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' },
    { min: 10, max: 200, message: '长度在 10 到 200 个字符', trigger: 'blur' }
  ],
  pbName: [
    { required: true, message: '请输入作者', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请输入部门', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  wikiUrl: [
    { required: formData.type !== 8, message: '请输入Url', trigger: 'blur' }
  ],
  promptContent: [
    { 
      validator: (rule, value, callback) => {
        // 只有当type是8时才需要promptContent
        if (formData.type === 8) {
          if (!value) {
            callback(new Error('请输入Prompt内容'));
            return;
          }
          if (value.length < 10) {
            callback(new Error('内容长度至少10个字符'));
            return;
          }
        }
        callback();
      }, 
      trigger: 'blur' 
    }
  ],
  type: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
//   tags: [
//     { required: true, message: '请输入标签', trigger: 'blur' }
//   ]
};

// 用户信息
const userInfo = ref<{name?: string, department?: string}>({});

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const res = await getUserInfo();
    if (res && res.data) {
      console.log(res.data);
      userInfo.value.name = res.data.name || '';
      formData.pbName = userInfo.value.name || '';
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    ElMessage.error('获取用户信息失败');
  }
};

// 图片上传相关
const fileInputRef = ref<HTMLInputElement>();
const isUploading = ref(false); // 添加上传状态标记

// 触发文件选择
const triggerFileInput = () => {
  fileInputRef.value?.click();
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    handleImageFile(file);
  }
  // 清空input值，允许重复选择同一文件
  target.value = '';
};

// 处理拖拽上传
const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    const file = files[0];
    if (file.type.startsWith('image/')) {
      handleImageFile(file);
    } else {
      ElMessage.warning('请上传图片文件');
    }
  }
};

// 处理图片文件转base64
const handleImageFile = (file: File) => {
  // 防止重复上传
  if (isUploading.value) {
    return;
  }
  
  // 验证文件大小（限制为5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB');
    return;
  }
  
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请上传图片文件');
    return;
  }
  
  isUploading.value = true;
  
  const reader = new FileReader();
  reader.onload = (e) => {
    const result = e.target?.result as string;
    formData.picture = result;
    isUploading.value = false;
    ElMessage.success('图片上传成功');
  };
  reader.onerror = () => {
    isUploading.value = false;
    ElMessage.error('图片读取失败');
  };
  reader.readAsDataURL(file);
};

// 移除图片
const removeImage = () => {
  formData.picture = '';
  ElMessage.success('图片已移除');
};

// 处理剪贴板粘贴
const handlePaste = (event: ClipboardEvent) => {
  // 只在对话框打开且类型正确时处理粘贴事件
  if (!dialogVisible.value || (formData.type !== 1 && formData.type !== 6)) {
    return;
  }
  
  const items = event.clipboardData?.items;
  if (items) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.startsWith('image/')) {
        const file = item.getAsFile();
        if (file) {
          handleImageFile(file);
        }
        break;
      }
    }
  }
};

// 组件挂载时获取用户信息
onMounted(() => {
    fetchUserInfo();
    
    // 添加剪贴板事件监听器
    document.addEventListener('paste', handlePaste);
    
    // 动态设置对话框样式，确保滚动效果
    const setDialogStyle = () => {
      const dialog = document.querySelector('.wiki-form-dialog .el-dialog');
      if (dialog) {
        (dialog as HTMLElement).style.maxHeight = '75vh';
        (dialog as HTMLElement).style.overflow = 'hidden';
        (dialog as HTMLElement).style.display = 'flex';
        (dialog as HTMLElement).style.flexDirection = 'column';
        
        const dialogBody = dialog.querySelector('.el-dialog__body');
        if (dialogBody) {
          (dialogBody as HTMLElement).style.flex = '1';
          (dialogBody as HTMLElement).style.overflow = 'hidden';
          (dialogBody as HTMLElement).style.padding = '0';
        }
        
        const dialogFooter = dialog.querySelector('.el-dialog__footer');
        if (dialogFooter) {
          (dialogFooter as HTMLElement).style.display = 'flex';
          (dialogFooter as HTMLElement).style.justifyContent = 'flex-end';
          (dialogFooter as HTMLElement).style.gap = '12px';
          (dialogFooter as HTMLElement).style.flexShrink = '0';
          (dialogFooter as HTMLElement).style.padding = '20px';
          (dialogFooter as HTMLElement).style.minHeight = '70px';
        }
      }
    };
    
    // 延迟执行以确保DOM已渲染
    setTimeout(setDialogStyle, 100);
});

// 组件卸载时移除事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('paste', handlePaste);
});

watch(
  () => props.wikiData,
  (newVal) => {
    if (newVal) {
      // 编辑模式：使用已有数据
      let promptContent = '';
      let picture = '';
      
      // 如果是Prompt分享类型(8)，从options中提取prompt内容
      if (newVal.type === 8 && newVal.options) {
        try {
          let optionsData;
          if (typeof newVal.options === 'string') {
            optionsData = JSON.parse(newVal.options);
          } else {
            optionsData = newVal.options;
          }
          promptContent = optionsData?.prompt || '';
        } catch (error) {
          console.error('解析options失败:', error);
        }
      }
      
      // 如果是AI产品或基础设施类型(1或6)，从options中提取图片
      if ((newVal.type === 1 || newVal.type === 6) && newVal.options) {
        try {
          let optionsData;
          if (typeof newVal.options === 'string') {
            optionsData = JSON.parse(newVal.options);
          } else {
            optionsData = newVal.options;
          }
          picture = optionsData?.picture || '';
        } catch (error) {
          console.error('解析图片options失败:', error);
        }
      }
      
      Object.assign(formData, {
        id: newVal.id,
        title: newVal.title || '',
        description: newVal.description || '',
        pbName: newVal.pbName || '',
        department: newVal.department || '',
        wikiUrl: newVal.wikiUrl || '',
        type: newVal.type,
        // 如果tags是数组，转换为字符串
        tags: Array.isArray(newVal.tags) ? newVal.tags.join('、') : (newVal.tags || ''),
        promptContent: promptContent,
        picture: picture
      });
    } else {
      // 新增模式：重置表单并设置类型
      Object.assign(formData, {
        id: undefined,
        title: '',
        description: '',
        pbName: '',
        department: '',
        wikiUrl: '',
        type: props.type,
        tags: '',
        promptContent: '',
        picture: ''
      });
      
      // 获取当前用户信息，填充作者和部门字段（如果不是AI产品类型）
      if (props.type !== 1 && userInfo.value) {
        console.log(userInfo.value.name);
        formData.pbName = userInfo.value.name || '';
      }
    }
  },
  { immediate: true }
);

// 监听type变化，设置规则
watch(
  () => formData.type,
  (newType) => {
    // 当类型为AI产品(1)时，不需要作者和部门信息
    if (newType === 1) {
      // 清空作者和部门信息
      formData.pbName = '';
      formData.department = '';
    } else if (!props.wikiData && formData.pbName === '' && formData.department === '' && userInfo.value) {
      // 如果是新增模式且不是AI产品类型，使用用户信息
      formData.pbName = userInfo.value.name || '';
      formData.department = userInfo.value.department || '';
    }
  }
);

// 监听对话框显示状态，确保每次打开时都应用滚动样式
watch(
  () => dialogVisible.value,
  (newVal) => {
    if (newVal) {
      // 延迟执行以确保DOM已渲染
      setTimeout(() => {
        const dialog = document.querySelector('.wiki-form-dialog .el-dialog');
        if (dialog) {
          const dialogEl = dialog as HTMLElement;
          dialogEl.style.maxHeight = '75vh';
          dialogEl.style.overflow = 'hidden';
          dialogEl.style.display = 'flex';
          dialogEl.style.flexDirection = 'column';
          dialogEl.style.marginTop = '12.5vh';
          dialogEl.style.marginBottom = '12.5vh';
          
          const dialogBody = dialog.querySelector('.el-dialog__body');
          if (dialogBody) {
            const bodyEl = dialogBody as HTMLElement;
            bodyEl.style.flex = '1';
            bodyEl.style.overflow = 'hidden';
            bodyEl.style.padding = '0';
            bodyEl.style.display = 'flex';
            bodyEl.style.flexDirection = 'column';
          }
          
          const dialogContent = dialog.querySelector('.dialog-content');
          if (dialogContent) {
            const contentEl = dialogContent as HTMLElement;
            contentEl.style.flex = '1';
            contentEl.style.overflowY = 'auto';
            contentEl.style.padding = '20px';
            contentEl.style.maxHeight = 'calc(75vh - 180px)';
          }
          
          const dialogFooter = dialog.querySelector('.el-dialog__footer');
          if (dialogFooter) {
            const footerEl = dialogFooter as HTMLElement;
            footerEl.style.display = 'flex';
            footerEl.style.justifyContent = 'flex-end';
            footerEl.style.gap = '12px';
            footerEl.style.flexShrink = '0';
            footerEl.style.padding = '20px';
            footerEl.style.minHeight = '70px';
          }
        }
      }, 50);
    }
  }
);

// 使用外部传入的loading状态或内部的loading状态
const submitLoading = computed(() => props.loading !== undefined ? props.loading : loading.value);

const handleClose = () => {
  dialogVisible.value = false;
  setTimeout(() => {
    formRef.value?.resetFields();
  }, 100);
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    // 仅当使用内部loading状态时才设置为true
    if (props.loading === undefined) {
      loading.value = true;
    }
    
    // 如果是AI产品类型，移除作者和部门字段
    const submitData = { ...formData };
    if (submitData.type === 1) {
      delete submitData.pbName;
      delete submitData.department;
    }
    
    // 处理Prompt分享类型的特殊逻辑
    if (submitData.type === 8) {
      // 将promptContent封装到options中作为Map对象
      submitData.options = {
        prompt: submitData.promptContent
      };
      // 为Prompt分享类型设置一个默认的wikiUrl（如果后端需要）
      if (!submitData.wikiUrl) {
        submitData.wikiUrl = '#';
      }
      // 移除promptContent字段，因为它不是后端期望的字段
      delete (submitData as any).promptContent;
    } 
    // 处理AI产品和基础设施类型的图片逻辑
    else if (submitData.type === 1 || submitData.type === 6) {
      // 将图片base64封装到options中作为Map对象
      if (submitData.picture) {
        submitData.options = {
          picture: submitData.picture
        };
      }
      // 移除picture字段，因为它不是后端期望的字段
      delete (submitData as any).picture;
    } else {
      // 其他类型，确保相关字段不被发送
      delete (submitData as any).promptContent;
      delete (submitData as any).picture;
    }
    
    // 将tags字符串转换为数组
    const finalData = {
      ...submitData,
      tags: submitData.tags.split('、').filter(tag => tag.trim())
    };
    emit('submit', finalData);
  } catch (error) {
    console.error('表单验证失败:', error);
    // 仅当使用内部loading状态时才设置为false
    if (props.loading === undefined) {
      loading.value = false;
    }
  }
};
</script>

<style lang="scss" scoped>
// 对话框滚动样式
:deep(.wiki-form-dialog) {
  .el-dialog {
    max-height: 75vh !important;
    margin-top: 12.5vh !important;
    margin-bottom: 12.5vh !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }
  
  .el-dialog__header {
    flex-shrink: 0 !important;
    padding: 20px 20px 10px !important;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .el-dialog__body {
    flex: 1 !important;
    overflow: hidden !important;
    padding: 0 !important;
    display: flex !important;
    flex-direction: column !important;
  }
  
  .el-dialog__footer {
    flex-shrink: 0 !important;
    padding: 20px !important;
    border-top: 1px solid var(--el-border-color-lighter);
    margin-top: 0 !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 12px !important;
    min-height: 70px !important;
  }
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  max-height: calc(75vh - 180px); // 为底部按钮留出更多空间
  
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-dark);
    border-radius: 4px;
    
    &:hover {
      background: var(--el-border-color-darker);
    }
  }
}

.wiki-form {
  padding: 0;
}

.prompt-editor {
  width: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  
  .prompt-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 0;
      border-bottom: 1px solid var(--el-border-color-light);
    }
    
    :deep(.el-tabs__nav-wrap) {
      padding: 0 16px;
    }
    
    :deep(.el-tab-pane) {
      padding: 0;
    }
  }
  
  .prompt-textarea {
    :deep(.el-textarea__wrapper) {
      border: none;
      border-radius: 0;
      box-shadow: none;
      padding: 16px;
      
      &:hover,
      &.is-focus {
        box-shadow: none;
      }
    }
    
    :deep(.el-textarea__inner) {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
      border: none;
      border-radius: 0;
      box-shadow: none;
      background-color: #fafafa;
    }
  }
  
  .prompt-preview {
    padding: 16px;
    min-height: 400px;
    max-height: 400px;
    overflow-y: auto;
    background-color: #fff;
    
    .empty-text, .loading-text {
      color: var(--el-text-color-placeholder);
      font-style: italic;
      text-align: center;
      margin-top: 100px;
    }
    
    .error-text {
      color: var(--el-color-danger);
      text-align: center;
      margin-top: 100px;
    }
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-lighter);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-dark);
      border-radius: 3px;
      
      &:hover {
        background: var(--el-border-color-darker);
      }
    }
    
    // Markdown样式
    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      margin: 1.5em 0 0.8em 0;
      font-weight: 600;
      line-height: 1.4;
    }
    
    :deep(h1) {
      font-size: 1.8em;
      border-bottom: 2px solid var(--el-border-color);
      padding-bottom: 0.3em;
    }
    
    :deep(h2) {
      font-size: 1.4em;
      border-bottom: 1px solid var(--el-border-color-light);
      padding-bottom: 0.3em;
    }
    
    :deep(h3) {
      font-size: 1.2em;
    }
    
    :deep(p) {
      margin: 0.8em 0;
      line-height: 1.6;
    }
    
    :deep(ul), :deep(ol) {
      margin: 0.8em 0;
      padding-left: 2em;
    }
    
    :deep(li) {
      margin: 0.3em 0;
    }
    
    :deep(blockquote) {
      margin: 1em 0;
      padding: 0.5em 1em;
      border-left: 4px solid var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      color: var(--el-text-color-regular);
    }
    
    :deep(code) {
      background-color: var(--el-fill-color-light);
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 0.9em;
      color: var(--el-color-danger);
    }
    
    :deep(pre) {
      background-color: var(--el-fill-color-light);
      border: 1px solid var(--el-border-color);
      border-radius: 6px;
      padding: 1em;
      overflow-x: auto;
      margin: 1em 0;
      
      code {
        background: none;
        padding: 0;
        color: var(--el-text-color-primary);
      }
    }
    
    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 1em 0;
      border: 1px solid var(--el-border-color);
    }
    
    :deep(th), :deep(td) {
      border: 1px solid var(--el-border-color);
      padding: 0.6em 1em;
      text-align: left;
    }
    
    :deep(th) {
      background-color: var(--el-fill-color-light);
      font-weight: 600;
    }
    
    :deep(a) {
      color: var(--el-color-primary);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  
  &:hover {
    box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
  }
  
  &.is-focus {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-textarea__inner) {
  resize: none !important;
  overflow-y: auto;
}

// 图片上传样式
.image-upload-container {
  .upload-area {
    width: 100%;
    min-height: 200px;
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
    
    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: var(--el-text-color-secondary);
      
      .upload-icon {
        margin-bottom: 16px;
        color: var(--el-color-primary);
      }
      
      .upload-text {
        text-align: center;
        
        p {
          margin: 4px 0;
          line-height: 1.5;
        }
        
        .upload-tip {
          font-size: 12px;
          color: var(--el-text-color-placeholder);
        }
      }
    }
    
    .upload-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: var(--el-text-color-secondary);
      
      .loading-icon {
        margin-bottom: 16px;
        color: var(--el-color-primary);
        animation: rotating 2s linear infinite;
      }
      
      .loading-text {
        margin: 0;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
    
    .image-preview {
      position: relative;
      width: 100%;
      height: 200px;
      border-radius: 6px;
      overflow: hidden;
      
      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
      }
      
      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
        
        &:hover {
          opacity: 1;
        }
        
        .el-button {
          background: rgba(255, 255, 255, 0.9);
          border: none;
          
          &:hover {
            background: rgba(255, 255, 255, 1);
          }
        }
      }
      
      &:hover .image-overlay {
        opacity: 1;
      }
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

<style lang="scss">
// 全局样式，确保对话框滚动效果生效
.wiki-form-dialog {
  .el-dialog {
    max-height: 75vh !important;
    margin-top: 12.5vh !important;
    margin-bottom: 12.5vh !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }
  
  .el-dialog__header {
    flex-shrink: 0 !important;
    padding: 20px 20px 10px !important;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .el-dialog__body {
    flex: 1 !important;
    overflow: hidden !important;
    padding: 0 !important;
    display: flex !important;
    flex-direction: column !important;
  }
  
  .el-dialog__footer {
    flex-shrink: 0 !important;
    padding: 20px !important;
    border-top: 1px solid var(--el-border-color-lighter);
    margin-top: 0 !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 12px !important;
    min-height: 70px !important;
  }
  
  .dialog-content {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 20px !important;
    max-height: calc(75vh - 180px) !important;
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f5f5f5;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 4px;
      
      &:hover {
        background: #909399;
      }
    }
  }
}
</style> 