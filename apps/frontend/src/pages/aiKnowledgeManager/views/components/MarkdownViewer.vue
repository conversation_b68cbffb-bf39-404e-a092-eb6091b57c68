<template>
  <el-dialog v-model="visible" :title="title" width="80%" top="5vh" class="markdown-dialog" destroy-on-close
    @update:model-value="handleVisibleChange">
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header">
        <h4 :id="titleId" :class="titleClass">{{ title }}</h4>
        <el-button 
          type="primary" 
          :icon="CopyDocument" 
          @click="copyContent"
          size="small"
        >
          复制内容
        </el-button>
      </div>
    </template>
    <div class="markdown-container">
      <div class="markdown-content" v-html="renderedContent"></div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import { CopyDocument } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

interface Props {
  modelValue: boolean;
  title: string;
  content: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue']);

const visible = ref(props.modelValue);
const renderedContent = ref('');

// 配置marked选项
marked.setOptions({
  gfm: true, // 启用GitHub风格的Markdown
  breaks: true, // 启用换行转换
});

// 渲染Markdown内容
const renderMarkdown = async () => {
  if (!props.content) {
    renderedContent.value = '';
    return;
  }

  try {
    // 使用marked解析markdown
    const rawHtml = await marked(props.content);
    // 使用DOMPurify清理HTML，防止XSS攻击
    renderedContent.value = DOMPurify.sanitize(rawHtml);
  } catch (error) {
    console.error('Markdown渲染失败:', error);
    renderedContent.value = '<p>内容渲染失败，请检查Markdown格式</p>';
  }
};

// 监听props变化并重新渲染
watch(() => [props.content, props.modelValue], () => {
  if (props.modelValue && props.content) {
    renderMarkdown();
  }
}, { immediate: true });

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
});

// 处理对话框显示状态变化
const handleVisibleChange = (value: boolean) => {
  emit('update:modelValue', value);
};

// 复制内容到剪贴板
const copyContent = async () => {
  if (!props.content) {
    ElMessage.warning('暂无内容可复制');
    return;
  }

  try {
    await navigator.clipboard.writeText(props.content);
    ElMessage.success('内容已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    // 降级方案：使用document.execCommand
    try {
      const textArea = document.createElement('textarea');
      textArea.value = props.content;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success('内容已复制到剪贴板');
    } catch (fallbackError) {
      ElMessage.error('复制失败，请手动复制');
    }
  }
};
</script>

<style lang="scss" scoped>
.markdown-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
    max-height: 80vh;
    overflow: hidden;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.markdown-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px;
  max-height: 80vh;
}

.markdown-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  background-color: #fff;

  // Markdown样式
  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    margin: 1.5em 0 0.8em 0;
    font-weight: 600;
    line-height: 1.4;
  }

  :deep(h1) {
    font-size: 2em;
    border-bottom: 2px solid var(--el-border-color);
    padding-bottom: 0.3em;
  }

  :deep(h2) {
    font-size: 1.5em;
    border-bottom: 1px solid var(--el-border-color-light);
    padding-bottom: 0.3em;
  }

  :deep(h3) {
    font-size: 1.25em;
  }

  :deep(h4) {
    font-size: 1.1em;
  }

  :deep(h5),
  :deep(h6) {
    font-size: 1em;
  }

  :deep(p) {
    margin: 0.8em 0;
    line-height: 1.6;
  }

  :deep(ul),
  :deep(ol) {
    margin: 0.8em 0;
    padding-left: 2em;
  }

  :deep(li) {
    margin: 0.3em 0;
  }

  :deep(blockquote) {
    margin: 1em 0;
    padding: 0.5em 1em;
    border-left: 4px solid var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    color: var(--el-text-color-regular);
  }

  :deep(code) {
    background-color: var(--el-fill-color-light);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: var(--el-color-danger);
  }

  :deep(pre) {
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    padding: 1em;
    overflow-x: auto;
    margin: 1em 0;

    code {
      background: none;
      padding: 0;
      color: var(--el-text-color-primary);
    }
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    border: 1px solid var(--el-border-color);
  }

  :deep(th),
  :deep(td) {
    border: 1px solid var(--el-border-color);
    padding: 0.6em 1em;
    text-align: left;
  }

  :deep(th) {
    background-color: var(--el-fill-color-light);
    font-weight: 600;
  }

  :deep(a) {
    color: var(--el-color-primary);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 0.5em 0;
  }

  :deep(hr) {
    border: none;
    border-top: 1px solid var(--el-border-color);
    margin: 2em 0;
  }

  // 强调样式
  :deep(strong) {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  :deep(em) {
    font-style: italic;
    color: var(--el-text-color-regular);
  }

  // 删除线
  :deep(del) {
    text-decoration: line-through;
    color: var(--el-text-color-placeholder);
  }
}
</style>