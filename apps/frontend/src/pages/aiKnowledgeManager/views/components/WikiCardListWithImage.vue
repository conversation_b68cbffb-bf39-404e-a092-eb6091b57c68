<template>
  <div class="wiki-card-list-with-image">
    <el-row :gutter="20">
      <el-col
        v-for="item in wikiList"
        :key="item.id"
        :xs="24" :sm="12" :md="8" :lg="6"
        class="wiki-card-col"
      >
        <el-card class="wiki-card" shadow="hover" :class="{ 'is-topped': item.top === 1 }">
          <div v-if="item.top === 1" class="top-bar"></div>
          <div class="card-image-wrapper" @click="handlePreview(item)">
            <img
              v-if="getPicture(item)"
              :src="getPicture(item)"
              class="card-image"
              :alt="item.title"
            />
            <div v-else class="card-image-placeholder">
              <el-icon><PictureFilled /></el-icon>
            </div>
          </div>
          <div class="card-content">
            <div class="card-title-row">
              <el-tooltip :content="item.title" placement="top" effect="dark">
                <span class="card-title">
                  <el-icon v-if="item.star === 1" class="star-icon" style="color:var(--el-color-warning)"><StarFilled /></el-icon>
                  {{ item.title }}
                </span>
              </el-tooltip>
              <el-dropdown trigger="click" @command="(cmd: string) => handleCommand(cmd, item)">
                <el-icon class="more-icon"><MoreFilled /></el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="star">
                      <el-icon><Star /></el-icon>{{ item.star === 1 ? '取消星标' : '星标' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="top">
                      <el-icon><Top /></el-icon>{{ item.top === 1 ? '取消置顶' : '置顶' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="edit">
                      <el-icon><EditPen /></el-icon>编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="danger-item">
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <el-tooltip :content="item.description" placement="top" effect="dark">
              <div class="card-desc">{{ item.description }}</div>
            </el-tooltip>
            <div class="card-tags">
              <el-tag
                v-for="tag in item.tags"
                :key="tag"
                size="small"
                class="card-tag"
              >{{ tag }}</el-tag>
            </div>
            <div class="card-footer-row">
              <div class="footer-left">
                <span class="card-author">{{ item.author }}</span>
                <span class="card-time">{{ formatTime(item.updateTime) }}</span>
              </div>
              <el-button size="small" type="primary" @click.stop="$emit('view', item)">查看</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog v-model="previewVisible" width="1000px" :show-close="true" title="图片预览">
      <img v-if="previewImage" :src="previewImage" style="width:100%" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { PictureFilled, MoreFilled, Star, Top, EditPen, Delete, StarFilled } from '@element-plus/icons-vue';
import type { WikiItem } from '../../types';

const props = defineProps<{
  wikiList: WikiItem[];
  pageType?: string;
}>();

const emit = defineEmits(['view', 'edit', 'delete', 'update']);

const previewVisible = ref(false);
const previewImage = ref('');

function getPicture(item: WikiItem): string | undefined {
  // 兼容AIWiki.options.picture
  if (item.options && typeof item.options === 'object' && item.options.picture) {
    const pic = item.options.picture;
    if (typeof pic === 'string') {
      // 已有data:image前缀
      if (pic.startsWith('data:image')) return pic;
      // 纯base64补全前缀
      return 'data:image/jpeg;base64,' + pic;
    }
  }
  return undefined;
}

function handlePreview(item: WikiItem) {
  const pic = getPicture(item);
  if (pic) {
    previewImage.value = pic;
    previewVisible.value = true;
  }
}

function formatTime(time: string | number | undefined): string {
  if (!time) return '';
  let d: Date;
  if (typeof time === 'string' && /^\d+$/.test(time)) {
    // 字符串数字，可能是秒或毫秒
    d = new Date(time.length === 13 ? Number(time) : Number(time) * 1000);
  } else if (typeof time === 'number') {
    d = new Date(time > 10000000000 ? time : time * 1000);
  } else {
    d = new Date(time);
  }
  if (isNaN(d.getTime())) return '';
  const pad = (n: number) => n < 10 ? '0' + n : n;
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
}

function handleCommand(command: string, item: WikiItem) {
  switch (command) {
    case 'edit':
      emit('edit', item);
      break;
    case 'delete':
      emit('delete', item);
      break;
    case 'star':
      emit('update', { ...item, star: item.star === 1 ? 0 : 1 });
      break;
    case 'top':
      emit('update', { ...item, top: item.top === 1 ? 0 : 1 });
      break;
  }
}
</script>

<style lang="scss" scoped>
.wiki-card-list-with-image {
  .wiki-card-col {
    margin-bottom: 20px;
  }
  .wiki-card {
    position: relative;
    display: flex;
    flex-direction: column;
    // height: 100%;
    .top-bar {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--el-color-primary);
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      z-index: 2;
    }
    &.is-topped {
      box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.08);
    }
    .card-image-wrapper {
      width: 100%;
      height: 160px;
      background: var(--el-fill-color-light);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 12px;
      cursor: pointer;
      .card-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s;
      }
      .card-image-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--el-color-info-light-5);
        font-size: 48px;
        background: var(--el-fill-color-lighter);
      }
      &:hover .card-image {
        transform: scale(1.05);
      }
    }
    .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      .card-title-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        justify-content: space-between;
        .card-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: calc(100% - 30px);
          display: flex;
          align-items: center;
          gap: 4px;
          .star-icon {
            color: var(--el-color-warning);
            font-size: 18px;
            margin-right: 2px;
          }
          .top-icon {
            color: var(--el-color-danger);
            font-size: 18px;
            margin-right: 2px;
          }
        }
        .more-icon {
          font-size: 20px;
          color: #909399;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: color 0.3s;
          flex-shrink: 0;
          &:hover {
            color: #606266;
          }
        }
      }
      .card-desc {
        color: var(--el-text-color-regular);
        font-size: 14px;
        margin-bottom: 8px;
        min-height: 36px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .card-tags {
        min-height: 28px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        .card-tag {
          margin-right: 6px;
        }
      }
      .card-footer-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: auto;
        .footer-left {
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
}
:deep(.danger-item) {
  color: var(--el-color-danger);
}
</style> 