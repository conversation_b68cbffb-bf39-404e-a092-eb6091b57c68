<template>
  <div class="wiki-list">
    <el-row :gutter="20">
      <el-col
        :xs="24"
        :sm="24"
        :md="24"
        :lg="12"
        v-for="(item, index) in wikiList"
        :key="index"
      >
        <el-card class="wiki-card" shadow="hover" :class="{ 'is-topped': item.top === 1 }">
          <div class="wiki-content">
            <div class="wiki-header">
              <el-tooltip
                :content="item.title"
                placement="top"
                :show-after="200"
                :enterable="false"
                :disabled="!isTitleOverflow(index)"
                popper-class="wiki-title-tooltip"
              >
                <h3 class="wiki-title" :ref="(el) => { if (el) titleRefs[index] = el as HTMLElement }">
                  <el-icon v-if="item.star === 1" class="star-icon"><StarFilled /></el-icon>
                  {{ item.title }}
                </h3>
              </el-tooltip>
              <div class="wiki-update-time">
                <el-icon><Timer /></el-icon>
                <span>{{ formatDate(item.updateTime) }}</span>
              </div>
              <el-dropdown trigger="click" @command="(command: CommandType) => handleCommand(command, item)">
                <el-icon class="more-icon">
                  <MoreFilled />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="star">
                      <el-icon><Star /></el-icon>{{ item.star === 1 ? '取消星标' : '星标' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="top">
                      <el-icon><Top /></el-icon>{{ item.top === 1 ? '取消置顶' : '置顶' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="edit">
                      <el-icon><EditPen /></el-icon>编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="danger-item">
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <el-tooltip
              :content="item.description"
              placement="top"
              :show-after="200"
              :enterable="false"
              :disabled="!isTextOverflow(index)"
              popper-class="wiki-description-tooltip"
            >
              <p class="wiki-description" :ref="(el) => { if (el) descriptionRefs[index] = el as HTMLElement }">{{ item.description }}</p>
            </el-tooltip>
            <div class="wiki-footer">
              <template v-if="pageType === 'product'">
                <div class="wiki-tags" :ref="(el) => { if (el) tagsRefs[index] = el as HTMLElement }">
                  <el-tag
                    v-for="(tag, tagIndex) in getDisplayTags(item.tags, index)"
                    :key="tagIndex"
                    size="small"
                    class="tag-item"
                  >
                    {{ tag }}
                  </el-tag>
                  <el-tooltip
                    v-if="hasMoreTags(item.tags, index)"
                    :content="getHiddenTags(item.tags, index)"
                    placement="top"
                    :show-after="200"
                    :enterable="false"
                    popper-class="wiki-tags-tooltip"
                  >
                    <el-tag
                      size="small"
                      class="tag-item more-tag"
                    >
                      +{{ getMoreTagsCount(item.tags, index) }}
                    </el-tag>
                  </el-tooltip>
                </div>
              </template>
              <template v-else>
                <div class="wiki-author">
                  <el-avatar :size="24" :src="item.authorAvatar">{{ item.author?.charAt(0) }}</el-avatar>
                  <span>{{ item.author }}</span>
                </div>
                <div class="wiki-tags" :ref="(el) => { if (el) tagsRefs[index] = el as HTMLElement }">
                  <el-tag
                    v-for="(tag, tagIndex) in getDisplayTags(item.tags, index)"
                    :key="tagIndex"
                    size="small"
                    class="tag-item"
                  >
                    {{ tag }}
                  </el-tag>
                  <el-tooltip
                    v-if="hasMoreTags(item.tags, index)"
                    :content="getHiddenTags(item.tags, index)"
                    placement="top"
                    :show-after="200"
                    :enterable="false"
                    popper-class="wiki-tags-tooltip"
                  >
                    <el-tag
                      size="small"
                      class="tag-item more-tag"
                    >
                      +{{ getMoreTagsCount(item.tags, index) }}
                    </el-tag>
                  </el-tooltip>
                </div>
              </template>
              <span class="view-link" @click="handleView(item)">查看</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import {nextTick, onMounted, ref} from 'vue';
import {Delete, EditPen, MoreFilled, Timer, Star, Top, StarFilled} from '@element-plus/icons-vue';
import type { WikiItem } from '../../types';

type CommandType = 'edit' | 'delete' | 'star' | 'top';
type PageType = 'product' | 'coding' | 'share' | 'prompt';

interface Props {
  wikiList: WikiItem[];
  pageType: PageType;
}

const props = defineProps<Props>();
const emit = defineEmits(['view', 'edit', 'delete', 'update']);

const titleRefs = ref<HTMLElement[]>([]);
const descriptionRefs = ref<HTMLElement[]>([]);
const tagsRefs = ref<HTMLElement[]>([]);
const overflowMap = ref<Map<number, boolean>>(new Map());
const titleOverflowMap = ref<Map<number, boolean>>(new Map());

const isTextOverflow = (index: number) => {
  return overflowMap.value.get(index) || false;
};

const isTitleOverflow = (index: number) => {
  return titleOverflowMap.value.get(index) || false;
};

// 获取要显示的tags
const getDisplayTags = (tags: string[] = [], index: number): string[] => {
  if (!tags?.length) return [];
  return tags.length > 3 ? tags.slice(0, 2) : tags;
};

// 判断是否有更多tags
const hasMoreTags = (tags: string[] = [], index: number): boolean => {
  return tags?.length > 3;
};

// 获取隐藏的tags
const getHiddenTags = (tags: string[] = [], index: number): string => {
  if (!tags?.length || tags.length <= 3) return '';
  return tags.slice(2).join('、');
};

// 获取更多tags的数量
const getMoreTagsCount = (tags: string[] = [], index: number): number => {
  if (!tags?.length || tags.length <= 3) return 0;
  return tags.length - 2;
};

onMounted(async () => {
  await nextTick();
  descriptionRefs.value.forEach((el, index) => {
    if (el) {
      const isOverflow = el.scrollHeight > el.clientHeight;
      overflowMap.value.set(index, isOverflow);
    }
  });
  
  titleRefs.value.forEach((el, index) => {
    if (el) {
      const isOverflow = el.scrollWidth > el.clientWidth;
      titleOverflowMap.value.set(index, isOverflow);
    }
  });
});

const handleView = (item: WikiItem) => {
  emit('view', item);
};

const handleCommand = (command: CommandType, item: WikiItem) => {
  switch (command) {
    case 'edit':
      emit('edit', item);
      break;
    case 'delete':
      emit('delete', item);
      break;
    case 'star':
      emit('update', { ...item, star: item.star === 1 ? 0 : 1 });
      break;
    case 'top':
      emit('update', { ...item, top: item.top === 1 ? 0 : 1 });
      break;
  }
};

// 格式化日期
const formatDate = (dateStr?: string | number): string => {
  if (!dateStr) return '暂无更新时间';
  // 将时间戳转为数字
  const timestamp = typeof dateStr === 'string' ? parseInt(dateStr, 10) : dateStr;
  // 判断是否为有效数字
  if (isNaN(timestamp)) return '暂无更新时间';
  const date = new Date(typeof timestamp === 'number' && timestamp > 10000000000 ? timestamp : timestamp * 1000);
  if (isNaN(date.getTime())) return '暂无更新时间';
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
};
</script>

<style lang="scss" scoped>
.wiki-list {
  padding: 20px 0 0 0;

  .wiki-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;

    &.is-topped {
      border-top: 4px solid var(--el-color-primary);
    }

    .wiki-content {
      .wiki-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .wiki-title {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          flex: 1;
          padding-right: 8px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: flex;
          align-items: center;
          gap: 4px;

          .star-icon {
            color: var(--el-color-warning);
            font-size: 20px;
            flex-shrink: 0;
            margin-right: 4px;
          }
        }
        .wiki-update-time {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #909399;
          margin-right: 20px;
          white-space: nowrap;
          .el-icon {
            font-size: 14px;
          }
        }

        .more-icon {
          font-size: 20px;
          color: #909399;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: color 0.3s;
          flex-shrink: 0;

          &:hover {
            color: #606266;
          }
        }
      }

      .wiki-description {
        margin: 0 0 16px;
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
        height: 3.2em;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }

      .wiki-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid var(--el-border-color-lighter);

        .wiki-author {
          display: flex;
          align-items: center;
          gap: 8px;
          //max-width: 150px; // 添加最大宽度限制
          :deep(.el-avatar) {
            background-color: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
            font-weight: 500;
            flex-shrink: 0; // 防止头像缩小
          }
          span {
            font-size: 14px;
            color: var(--el-text-color-regular);
            font-weight: 500;
            white-space: nowrap; // 文本不换行
            overflow: hidden; // 溢出隐藏
            text-overflow: ellipsis; // 显示省略号
          }
        }

        .wiki-tags {
          display: flex;
          gap: 8px;
          flex-wrap: nowrap;
          overflow: hidden;
          position: relative;

          .tag-item {
            background-color: var(--el-color-primary-light-9);
            border-color: var(--el-color-primary-light-7);
            color: var(--el-color-primary);
            font-size: 12px;
            padding: 0 8px;
            height: 24px;
            line-height: 24px;
            flex-shrink: 0;

            &.more-tag {
              background-color: var(--el-color-info-light-9);
              border-color: var(--el-color-info-light-7);
              color: var(--el-color-info);
              cursor: pointer;

              &:hover {
                background-color: var(--el-color-info-light-8);
              }
            }
          }
        }

        .view-link {
          font-size: 14px;
          color: var(--el-color-primary);
          cursor: pointer;
          transition: all 0.3s;
          padding: 4px 12px;
          border-radius: 4px;
          background-color: var(--el-color-primary-light-9);

          &:hover {
            background-color: var(--el-color-primary-light-8);
            color: var(--el-color-primary);
          }

          &:active {
            background-color: var(--el-color-primary-light-7);
          }
        }
      }
    }
  }
}

:deep(.danger-item) {
  color: var(--el-color-danger);
}

:deep(.el-dropdown-menu .el-dropdown-menu__item .el-icon) {
  margin-right: 8px;
}
</style>

<style>
/* 自定义 Tooltip 样式 */
.wiki-description-tooltip {
  max-width: 40% !important;
  /* min-width: 350px !important; */
  width: auto !important;
  padding: 12px 16px !important;
  line-height: 1.6 !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
  font-size: 14px !important;
  background-color: #fff !important;
  border: 1px solid #e4e7ed !important;
  color: #606266 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

.wiki-description-tooltip .el-popper__arrow::before {
  background-color: #fff !important;
  border-color: #e4e7ed !important;
}

.wiki-title-tooltip {
  max-width: 40% !important;
  width: auto !important;
  padding: 8px 12px !important;
  line-height: 1.4 !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
  font-size: 14px !important;
  background-color: #fff !important;
  border: 1px solid #e4e7ed !important;
  color: #606266 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

.wiki-title-tooltip .el-popper__arrow::before {
  background-color: #fff !important;
  border-color: #e4e7ed !important;
}

.wiki-tags-tooltip {
  max-width: 40% !important;
  width: auto !important;
  padding: 8px 12px !important;
  line-height: 1.4 !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
  font-size: 14px !important;
  background-color: #fff !important;
  border: 1px solid #e4e7ed !important;
  color: #606266 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

.wiki-tags-tooltip .el-popper__arrow::before {
  background-color: #fff !important;
  border-color: #e4e7ed !important;
}
</style>
