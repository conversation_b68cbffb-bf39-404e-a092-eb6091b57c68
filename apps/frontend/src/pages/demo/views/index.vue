<template>
  <div class="demo-page">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <span>聚合查询场景列表</span>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="场景名称">
          <el-input v-model="searchForm.sceneName" placeholder="请输入场景名称" clearable />
        </el-form-item>
        <el-form-item label="场景级别">
          <el-select v-model="searchForm.sceneLevel" placeholder="请选择场景级别" clearable>
            <el-option v-for="i in 10" :key="i-1" :label="`级别${i-1}`" :value="i-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.valid" placeholder="请选择状态" clearable>
            <el-option label="有效" :value="1" />
            <el-option label="无效" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>新增
          </el-button>
          <el-button type="danger" :disabled="!selectedRows.length" @click="handleBatchDelete">
            <el-icon><Delete /></el-icon>批量删除
          </el-button>
        </div>
      </div>
      
      <!-- 数据表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="sceneName" label="场景名称" />
        <el-table-column prop="sceneLevel" label="场景级别">
          <template #default="{ row }">
            <el-tag :type="getSceneLevelType(row.sceneLevel)">
              级别{{ row.sceneLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expectedOPS" label="预估OPS" />
        <el-table-column prop="actualOPS" label="实际OPS" />
        <el-table-column prop="valid" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.valid ? 'success' : 'danger'">
              {{ row.valid ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" width="180">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">详情</el-button>
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @update:current-page="pagination.currentPage = $event"
          @update:page-size="pagination.pageSize = $event"
        />
      </div>
    </el-card>
    
    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增场景' : '编辑场景'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="场景名称" prop="sceneName">
          <el-input v-model="form.sceneName" placeholder="请输入场景名称" />
        </el-form-item>
        <el-form-item label="场景级别" prop="sceneLevel">
          <el-select v-model="form.sceneLevel" placeholder="请选择场景级别">
            <el-option v-for="i in 10" :key="i-1" :label="`级别${i-1}`" :value="i-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="预估OPS" prop="expectedOPS">
          <el-input-number v-model="form.expectedOPS" :min="0" :step="10" />
        </el-form-item>
        <el-form-item label="状态" prop="valid">
          <el-radio-group v-model="form.valid">
            <el-radio :label="1">有效</el-radio>
            <el-radio :label="0">无效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete } from '@element-plus/icons-vue';
import { getSceneList } from '../request';
import { SceneQueryItem, SceneQueryParams } from '../types';
import type { FormInstance, FormRules } from 'element-plus';

const router = useRouter();
const formRef = ref<FormInstance>();
const tableLoading = ref(false);
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const selectedRows = ref<SceneQueryItem[]>([]);

// 搜索表单
const searchForm = reactive({
  sceneName: '',
  sceneLevel: '',
  valid: ''
});

// 表格数据
const tableData = ref<SceneQueryItem[]>([]);

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 表单数据
const form = reactive({
  id: '',
  sceneName: '',
  sceneLevel: '',
  expectedOPS: 0,
  valid: 1
});

// 表单验证规则
const rules = reactive<FormRules>({
  sceneName: [
    { required: true, message: '请输入场景名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  sceneLevel: [
    { required: true, message: '请选择场景级别', trigger: 'change' }
  ],
  expectedOPS: [
    { required: true, message: '请输入预估OPS', trigger: 'blur' }
  ]
});

// 获取场景列表
const fetchData = async () => {
  tableLoading.value = true;
  try {
    // 创建一个新的参数对象，只包含有效的参数
    const params: any = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize
    };
    
    // 只有当值不为空字符串时才添加到参数中
    if (searchForm.sceneName) {
      params.sceneName = searchForm.sceneName;
    }
    
    // 确保sceneLevel和valid是数字类型，而不是空字符串
    if (searchForm.sceneLevel !== '') {
      params.sceneLevel = Number(searchForm.sceneLevel);
    }
    
    if (searchForm.valid !== '') {
      params.valid = Number(searchForm.valid);
    }
    
    const res = await getSceneList(params);
    if (res.code === 0) {
      tableData.value = res.data.list;
      pagination.total = res.data.total;
    } else {
      ElMessage.error(res.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取场景列表失败', error);
    ElMessage.error('获取场景列表失败');
  } finally {
    tableLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  pagination.currentPage = 1;
  fetchData();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  fetchData();
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  fetchData();
};

// 处理选择行变化
const handleSelectionChange = (rows: SceneQueryItem[]) => {
  selectedRows.value = rows;
};

// 新增场景
const handleAdd = () => {
  dialogType.value = 'add';
  Object.keys(form).forEach(key => {
    if (key === 'valid') {
      form[key] = 1;
    } else if (key === 'expectedOPS') {
      form[key] = 0;
    } else {
      form[key] = '';
    }
  });
  dialogVisible.value = true;
};

// 编辑场景
const handleEdit = (row: SceneQueryItem) => {
  dialogType.value = 'edit';
  Object.keys(form).forEach(key => {
    if (key in row) {
      // @ts-ignore - 类型安全地复制属性
      form[key] = row[key];
    }
  });
  dialogVisible.value = true;
};

// 删除场景
const handleDelete = (row: SceneQueryItem) => {
  ElMessageBox.confirm(
    `确定要删除场景 "${row.sceneName}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('删除成功');
    fetchData();
  }).catch(() => {
    // 取消删除
  });
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一条记录');
    return;
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('批量删除成功');
    fetchData();
  }).catch(() => {
    // 取消删除
  });
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      ElMessage.success(dialogType.value === 'add' ? '新增成功' : '编辑成功');
      dialogVisible.value = false;
      fetchData();
    }
  });
};

// 获取场景级别对应的标签类型
const getSceneLevelType = (level: number) => {
  const types = ['', 'success', 'warning', 'danger', 'info', 'primary'];
  return types[level % types.length] || '';
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.demo-page {
  padding: 20px;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  span {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.table-toolbar {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 