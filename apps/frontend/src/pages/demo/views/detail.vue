<template>
  <div class="detail-page">
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>聚合查询场景详情</span>
          <div>
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </template>
      
      <div v-loading="loading">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ detail.id }}</el-descriptions-item>
          <el-descriptions-item label="场景名称">{{ detail.sceneName }}</el-descriptions-item>
          <el-descriptions-item label="场景级别">
            <el-tag :type="getSceneLevelType(detail.sceneLevel)">
              {{ `级别${detail.sceneLevel}` }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="detail.valid === 1 ? 'success' : 'danger'">
              {{ detail.valid === 1 ? '有效' : '无效' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="预估QPS">{{ detail.estimateQps }}</el-descriptions-item>
          <el-descriptions-item label="实际QPS">{{ detail.actualQps }}</el-descriptions-item>
          <el-descriptions-item label="场景描述" :span="2">{{ detail.sceneDescription }}</el-descriptions-item>
          <el-descriptions-item label="使用服务" :span="2">{{ detail.appkeys }}</el-descriptions-item>
          <el-descriptions-item label="使用字段" :span="2">{{ detail.fieldCodes }}</el-descriptions-item>
          <el-descriptions-item label="场景管理员" :span="2">{{ detail.administrator }}</el-descriptions-item>
          <el-descriptions-item label="操作人">{{ detail.opName }}</el-descriptions-item>
          <el-descriptions-item label="操作人MIS">{{ detail.opMis }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(detail.ctime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(detail.utime) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getSceneDetail } from '../request';
import { SceneQueryItem } from '../types';

const router = useRouter();
const route = useRoute();

// 获取路由参数
const id = route.query.id as string;

// 详情数据
const detail = reactive<SceneQueryItem>({
  id: 0,
  sceneName: '',
  sceneDescription: '',
  sceneLevel: 0,
  appkeys: '',
  fieldCodes: '',
  administrator: '',
  estimateQps: 0,
  actualQps: 0,
  opName: '',
  opMis: '',
  valid: 1,
  ctime: 0,
  utime: 0
});

// 加载状态
const loading = ref(false);

// 获取场景级别对应的标签类型
const getSceneLevelType = (level: number) => {
  const types = ['danger', 'warning', 'success', 'info', ''];
  return types[level % types.length];
};

// 格式化时间戳
const formatTime = (timestamp: number) => {
  if (!timestamp) return '';
  const date = new Date(timestamp * 1000);
  return date.toLocaleString();
};

// 获取详情数据
const fetchDetail = async () => {
  if (!id) {
    ElMessage.error('缺少必要的ID参数');
    router.push('/demo');
    return;
  }
  
  loading.value = true;
  try {
    const res = await getSceneDetail({ id: Number(id) });
    if (res.code === 0 && res.data) {
      Object.assign(detail, res.data);
    } else {
      ElMessage.error(res.message || '获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败', error);
    ElMessage.error('获取详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 页面加载时获取详情数据
onMounted(() => {
  fetchDetail();
});
</script>

<style lang="scss" scoped>
.detail-page {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 