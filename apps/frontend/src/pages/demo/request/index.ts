/**
 * Demo模块的API请求封装
 */
import httpRequest from '../../../shared/utils/httpRequest';
import {
  API_PATHS,
  DemoListParams,
  DemoDetailParams,
  DemoCreateParams,
  DemoUpdateParams,
  DemoDeleteParams,
  SceneQueryParams,
  SceneQueryResult,
  SceneQueryItem,
  ApiResponse
} from '../types';

/**
 * 获取Demo列表
 * @param params 查询参数
 * @returns Promise
 */
export const getDemoList = (params: DemoListParams) => {
  return httpRequest.rawRequestPostAsJson(API_PATHS.getDemoList, params);
};

/**
 * 获取Demo详情
 * @param params 查询参数
 * @returns Promise
 */
export const getDemoDetail = (params: DemoDetailParams) => {
  return httpRequest.rawRequestPostAsJson(API_PATHS.getDemoDetail, params);
};

/**
 * 创建Demo
 * @param params 创建参数
 * @returns Promise
 */
export const createDemo = (params: DemoCreateParams) => {
  return httpRequest.rawRequestPostAsJson(API_PATHS.createDemo, params);
};

/**
 * 更新Demo
 * @param params 更新参数
 * @returns Promise
 */
export const updateDemo = (params: DemoUpdateParams) => {
  return httpRequest.rawRequestPostAsJson(API_PATHS.updateDemo, params);
};

/**
 * 删除Demo
 * @param params 删除参数
 * @returns Promise
 */
export const deleteDemo = (params: DemoDeleteParams) => {
  return httpRequest.rawRequestPostAsJson(API_PATHS.deleteDemo, params);
};

/**
 * 获取场景分页列表
 * @param params 查询参数
 * @returns Promise
 */
export const getSceneList = async (params: SceneQueryParams): Promise<ApiResponse<SceneQueryResult>> => {
  const response = await httpRequest.rawRequestPostAsJson(API_PATHS.SCENE_QUERY_PAGE, params);
  return response as unknown as ApiResponse<SceneQueryResult>;
};

/**
 * 获取场景详情
 * @param params 查询参数
 * @returns Promise
 */
export const getSceneDetail = async (params: { id: number }): Promise<ApiResponse<SceneQueryItem>> => {
  const response = await httpRequest.rawRequestPostAsJson(API_PATHS.SCENE_QUERY_DETAIL, params);
  return response as unknown as ApiResponse<SceneQueryItem>;
}; 