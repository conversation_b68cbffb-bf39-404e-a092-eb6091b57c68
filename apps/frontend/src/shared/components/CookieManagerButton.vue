<template>
  <div class="cookie-btn">
    <el-tooltip content="SSO Cookie管理" placement="bottom">
      <div class="action-item" @click="showDialog = true">
        <el-icon :size="18"><Brush /></el-icon>
      </div>
    </el-tooltip>

    <el-dialog title="SSO Cookie管理" v-model="showDialog" width="400px" destroy-on-close>
      <div class="cookie-dialog">
        <p class="cookie-name">Cookie名称: {{ cookieName }}</p>
        <el-input
          v-model="cookieValue"
          type="textarea"
          :rows="3"
          :placeholder="isDev ? '输入或获取Cookie值' : '点击获取按钮查看Cookie值'"
        />
        <div class="action-buttons">
          <el-button type="primary" @click="getCookie" :loading="isLoading">获取</el-button>
          <el-button v-if="cookieValue" type="success" @click="copyToClipboard">复制</el-button>
          <el-button v-if="isDev" type="warning" @click="setCookie" :disabled="!cookieValue">设置</el-button>
        </div>
        <div v-if="message" :class="['message', msgType]">{{ message }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { isDev } from '../utils/env-helper';
import { Brush } from '@element-plus/icons-vue';

export default {
  name: 'CookieManagerButton',
  components: {
    Brush
  },
  setup() {
    const showDialog = ref(false);
    const cookieValue = ref('');
    const isLoading = ref(false);
    const message = ref('');
    const msgType = ref('info');
    const cookieName = '20c01d5264_ssoid'; // 直接使用固定值
    
    const getCookie = () => {
      isLoading.value = true;
      try {
        const value = document.cookie.split(';')
          .find(c => c.trim().startsWith(`${cookieName}=`))
          ?.split('=')[1] || '';
          
        if (value) {
          cookieValue.value = value;
          showMessage('获取成功', 'success');
        } else {
          showMessage('未找到Cookie', 'error');
        }
      } catch (error) {
        showMessage('获取失败', 'error');
      } finally {
        isLoading.value = false;
      }
    };
    
    const setCookie = () => {
      if (!cookieValue.value) return;
      
      try {
        const date = new Date();
        date.setTime(date.getTime() + 30 * 24 * 60 * 60 * 1000);
        document.cookie = `${cookieName}=${cookieValue.value}; expires=${date.toUTCString()}; path=/; domain=.banma.dev.sankuai.com`;
        showMessage('设置成功！页面将刷新...', 'success');
        setTimeout(() => window.location.reload(), 2000);
      } catch (error) {
        showMessage('设置失败', 'error');
      }
    };
    
    const copyToClipboard = () => {
      navigator.clipboard.writeText(cookieValue.value)
        .then(() => showMessage('已复制到剪贴板', 'success'))
        .catch(() => showMessage('复制失败', 'error'));
    };
    
    const showMessage = (msg, type = 'info') => {
      message.value = msg;
      msgType.value = type;
      setTimeout(() => { if (message.value === msg) message.value = ''; }, 2000);
    };
    
    onMounted(getCookie);
    
    return {
      showDialog,
      cookieValue,
      isLoading,
      message,
      msgType,
      isDev,
      cookieName,
      getCookie,
      setCookie,
      copyToClipboard
    };
  }
}
</script>

<style lang="scss" scoped>
.cookie-btn {
  display: inline-block;
}

.action-item {
  padding: 8px;
  margin: 0 4px;
  cursor: pointer;
  border-radius: 4px;
  color: #606266;
  
  &:hover {
    background-color: #f5f7fa;
  }
}

.cookie-dialog {
  .cookie-name {
    margin: 0 0 10px;
    font-size: 14px;
    color: #606266;
  }
  
  .action-buttons {
    margin-top: 12px;
    display: flex;
    gap: 10px;
  }
  
  .message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    font-weight: bold;
    
    &.success { background-color: #f0f9eb; color: #67c23a; }
    &.error { background-color: #fef0f0; color: #f56c6c; }
    &.info { background-color: #f4f4f5; color: #909399; }
  }
}
</style> 