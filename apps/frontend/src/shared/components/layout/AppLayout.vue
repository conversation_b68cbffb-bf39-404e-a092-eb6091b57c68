<template>
  <div class="app-layout">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-left">
        <div class="logo">
          <!-- <img src="" alt="Logo" class="logo-image" /> -->
          <h1 class="logo-title">{{ '履约平台技术部AI门户' }}</h1>
        </div>
        <div class="menu-toggle" @click="toggleSidebar">
          <el-icon :size="20"><Fold v-if="sidebarCollapsed" /><Expand v-else /></el-icon>
        </div>
      </div>
      <div class="header-right">
        <div class="header-actions">
          <!-- Cookie管理按钮 (仅在开发/测试环境显示) -->
          <cookie-manager-button v-if="showCookieManager" />
          
          <el-tooltip content="全屏" placement="bottom">
            <div class="action-item" @click="toggleFullScreen">
              <el-icon :size="18"><FullScreen /></el-icon>
            </div>
          </el-tooltip>
          <!--          <el-tooltip content="消息通知" placement="bottom">-->
<!--            <div class="action-item">-->
<!--              <el-badge value="" :max="99" class="notification-badge">-->
<!--                <el-icon :size="18"><Bell /></el-icon>-->
<!--              </el-badge>-->
<!--            </div>-->
<!--          </el-tooltip>-->
          
          <!-- <el-tooltip content="系统设置" placement="bottom">
            <div class="action-item">
              <el-icon :size="18"><Setting /></el-icon>
            </div>
          </el-tooltip> -->
          <el-tooltip content="联系我们" placement="bottom">
            <div class="action-item" @click="openContactUsUrl()">
              <el-icon :size="18"><QuestionFilled /></el-icon>
            </div>
          </el-tooltip>
        </div>
        
        <el-divider direction="vertical" class="header-divider" />
        
        <el-dropdown trigger="click">
          <div class="user-info">
            <template v-if="userLoading">
              <el-skeleton style="width: 32px; height: 32px; border-radius: 50%;" animated />
              <el-skeleton style="width: 60px; margin: 0 8px;" animated />
            </template>
            <template v-else>
              <el-avatar :size="32" :src="`https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png`" />
              <span class="username">{{ userInfo.name || '未登录' }}</span>
            </template>
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <template v-if="userLoading">
                <el-dropdown-item disabled>
                  <el-skeleton :rows="3" animated style="width: 200px;" />
                </el-dropdown-item>
              </template>
              <template v-else-if="userError">
                <el-dropdown-item disabled>
                  <el-empty description="获取用户信息失败" :image-size="60">
                    <el-button type="primary" size="small" @click="fetchUserInfo">重新获取</el-button>
                  </el-empty>
                </el-dropdown-item>
              </template>
              <template v-else>
                <el-dropdown-item disabled>
                  <div class="user-detail">
                    <div class="user-name">{{ userInfo.name }}</div>
                    <div class="user-email">{{ userInfo.email }}</div>
                    <div class="user-login">登录名: {{ userInfo.login }}</div>
                    <div class="user-roles" v-if="userInfo.roles && userInfo.roles.length > 0">
                      <el-tag v-for="role in userInfo.roles" :key="role" size="small" class="role-tag">{{ role }}</el-tag>
                    </div>
                  </div>
                </el-dropdown-item>
                <el-divider style="margin: 5px 0" />
                <el-dropdown-item @click="handleLogout">
                  <el-icon><SwitchButton /></el-icon>
                  <span>退出登录</span>
                </el-dropdown-item>
              </template>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <!-- 主体内容区 -->
    <div class="app-container">
      <!-- 侧边菜单 -->
      <aside class="app-sidebar" :class="{ 'collapsed': sidebarCollapsed }">
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            :collapse="sidebarCollapsed"
            :collapse-transition="false"
            :unique-opened="true"
            class="el-menu-vertical"
            @select="handleMenuSelect"
          >
            <!-- 动态生成菜单 -->
            <div v-for="menu in menuList" :key="menu.nodeId">
              <!-- 有子菜单的情况 -->
              <el-sub-menu v-if="menu.childrenMenuCount > 0" :index="String(menu.nodeId)">
                <template #title>
                  <div class="parent-menu-title" @click="menu.menuURL ? handleParentMenuClick(menu.menuURL, $event) : null">
                    <el-icon><component :is="getMenuIcon(menu.nodeCode, menu)" /></el-icon>
                    <span>{{ menu.nodeName }}</span>
                  </div>
                </template>
                
                <!-- 递归渲染子菜单 -->
                <template v-for="subMenu in menu.childrenMenuList">
                  <!-- 二级菜单有子菜单的情况 -->
                  <el-sub-menu v-if="subMenu.childrenMenuCount > 0" :index="String(subMenu.nodeId)" :key="`submenu-${subMenu.nodeId}`">
                    <template #title>
                      <div class="parent-menu-title" @click="subMenu.menuURL ? handleParentMenuClick(subMenu.menuURL, $event) : null">
                        <el-icon><component :is="getMenuIcon(subMenu.nodeCode, subMenu)" /></el-icon>
                        <span>{{ subMenu.nodeName }}</span>
                      </div>
                    </template>
                    
                    <!-- 三级菜单 -->
                    <el-menu-item 
                      v-for="thirdMenu in subMenu.childrenMenuList" 
                      :key="thirdMenu.nodeId"
                      :index="convertMenuUrlToPath(thirdMenu.menuURL)"
                    >
                      <el-icon><component :is="getMenuIcon(thirdMenu.nodeCode, thirdMenu)" /></el-icon>
                      <template #title>{{ thirdMenu.nodeName }}</template>
                    </el-menu-item>
                  </el-sub-menu>
                  
                  <!-- 二级菜单没有子菜单的情况 -->
                  <el-menu-item 
                    v-else
                    :key="`menuitem-${subMenu.nodeId}`"
                    :index="convertMenuUrlToPath(subMenu.menuURL)"
                  >
                    <el-icon><component :is="getMenuIcon(subMenu.nodeCode, subMenu)" /></el-icon>
                    <template #title>{{ subMenu.nodeName }}</template>
                  </el-menu-item>
                </template>
              </el-sub-menu>
              
              <!-- 没有子菜单的情况 -->
              <el-menu-item v-else :index="convertMenuUrlToPath(menu.menuURL)">
                <el-icon><component :is="getMenuIcon(menu.nodeCode, menu)" /></el-icon>
                <template #title>{{ menu.nodeName }}</template>
              </el-menu-item>
            </div>
            
            <!-- 加载中状态 -->
            <div v-if="loading" class="menu-loading">
              <el-skeleton :rows="5" animated />
            </div>
            
            <!-- 加载失败状态 -->
            <div v-if="error" class="menu-error">
              <el-empty description="加载菜单失败">
                <el-button type="primary" @click="fetchMenuData">重新加载</el-button>
              </el-empty>
            </div>
          </el-menu>
        </el-scrollbar>
      </aside>
      
      <!-- 内容区域 -->
      <main class="app-main" :class="{ 'expanded': sidebarCollapsed }">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
  HomeFilled,
  Document,
  Setting,
  User,
  PieChart,
  Bell,
  FullScreen,
  Fold,
  Expand,
  ArrowDown,
  Key,
  SwitchButton,
  Menu as IconMenu,
  List,
  Grid,
  Operation,
  More,
  Plus,
  Link
} from '@element-plus/icons-vue';
import { getMenuTree, convertMenuUrlToPath, MenuItem } from '../../services/menuService';
import { getCurrentUser, UserInfo } from '../../services/userService';
import CookieManagerButton from '../../components/CookieManagerButton.vue';
import { isDev, isTest } from '../../utils/env-helper';

const router = useRouter();
const route = useRoute();

// 侧边栏折叠状态
const sidebarCollapsed = ref(false);

// 菜单数据
const menuList = ref<MenuItem[]>([]);
const systemName = ref('');
const loading = ref(false);
const error = ref(false);

// 用户信息
const userInfo = ref<UserInfo>({
  id: 0,
  login: '',
  name: '',
  code: '',
  email: '',
  tenantId: '',
  roles: [],
  isVerified: false,
  verifyType: '',
  verifyExpireTime: 0,
  passport: ''
});
const userLoading = ref(false);
const userError = ref(false);
const contactUsUrl = ref('');

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path;
});

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

// 菜单选择处理
const handleMenuSelect = (index: string) => {
  // 如果是外部链接，在新窗口打开
  if (index.startsWith('http')) {
    window.open(index, '_blank');
  } else {
    router.push(index);
  }
};

// 获取菜单图标
const getMenuIcon = (nodeCode: string, menu?: MenuItem) => {
  // 如果菜单有attributeList.icon属性，则使用该属性作为图标
  if (menu?.attributeList?.icon) {
    return menu.attributeList.icon;
  }
  
  // 默认图标
  return 'Grid';
};

// 获取菜单数据
const fetchMenuData = async () => {
  loading.value = true;
  error.value = false;
  
  try {
    const menuData = await getMenuTree();
    menuList.value = menuData.menuFolderList || [];
    systemName.value = menuData.businessSysName || '';

    const homeindexMenu = menuList.value.find(item => item.nodeCode === 'homeindex');
    contactUsUrl.value = homeindexMenu?.attributeList?.contactUsUrl;

    loading.value = false;
  } catch (err) {
    console.error('获取菜单失败:', err);
    error.value = true;
    loading.value = false;
    ElMessage.error('获取菜单失败，请重试');
  }
};

// 获取用户信息
const fetchUserInfo = async () => {
  userLoading.value = true;
  userError.value = false;
  
  try {
    const userData = await getCurrentUser();
    userInfo.value = userData;
    userLoading.value = false;
  } catch (err) {
    console.error('获取用户信息失败:', err);
    userError.value = true;
    userLoading.value = false;
    ElMessage.error('获取用户信息失败，请重试');
  }
};

// 打开联系我们URL
const openContactUsUrl = () => {
  window.open(contactUsUrl.value, '_blank');
};

// 切换全屏
const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
};

// 退出登录
const handleLogout = () => {
  // 清空用户信息
  userInfo.value = {
    id: 0,
    login: '',
    name: '',
    code: '',
    email: '',
    tenantId: '',
    roles: [],
    isVerified: false,
    verifyType: '',
    verifyExpireTime: 0,
    passport: ''
  };
  
  ElMessage.success('退出登录成功');
  // 可以在这里添加退出逻辑
  // router.push('/login');
};

// 监听路由变化，更新激活菜单
watch(() => route.path, (newPath) => {
  // 路由变化时的处理逻辑
  console.log('路由变化:', newPath);
}, { immediate: true });

// 组件挂载时的处理
onMounted(() => {
  console.log('布局组件已挂载');
  // 获取菜单数据
  fetchMenuData();
  // 获取用户信息
  fetchUserInfo();
});

// 是否显示Cookie管理按钮
const showCookieManager = computed(() => isDev() || isTest());

// 处理菜单链接点击
const handleMenuLinkClick = (menuURL: string) => {
  if (!menuURL) return;
  
  // 转换菜单URL为路径
  const path = convertMenuUrlToPath(menuURL);
  
  // 如果是外部链接，在新窗口打开
  if (path.startsWith('http')) {
    window.open(path, '_blank');
  } else {
    router.push(path);
  }
};

// 处理父菜单点击
const handleParentMenuClick = (menuURL: string, event: Event) => {
  if (!menuURL) return;
  
  // 转换菜单URL为路径
  const path = convertMenuUrlToPath(menuURL);
  
  // 如果是外部链接，在新窗口打开
  if (path.startsWith('http')) {
    window.open(path, '_blank');
  } else {
    // 延迟执行路由跳转，让展开子菜单的动作先执行
    setTimeout(() => {
      router.push(path);
    }, 0);
  }
};
</script>

<style lang="scss" scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  
  .app-header {
    height: 60px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    
    .header-left {
      display: flex;
      align-items: center;
      
      .logo {
        display: flex;
        align-items: center;
        
        .logo-image {
          height: 32px;
          margin-right: 8px;
        }
        
        .logo-title {
          font-size: 18px;
          font-weight: 500;
          margin: 0;
          color: #303133;
          background-image: linear-gradient(90deg, #1890ff, #36cfc9);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      
      .menu-toggle {
        margin-left: 20px;
        padding: 8px;
        cursor: pointer;
        border-radius: 4px;
        color: #606266;
        
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      
      .header-actions {
        display: flex;
        align-items: center;
        
        .action-item {
          padding: 8px;
          margin: 0 4px;
          cursor: pointer;
          border-radius: 4px;
          color: #606266;
          
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
      
      .header-divider {
        height: 20px;
        margin: 0 12px;
      }
      
      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        .username {
          margin: 0 8px;
          font-size: 14px;
          color: #303133;
        }
      }
    }
  }
  
  .app-container {
    display: flex;
    flex: 1;
    margin-top: 60px;
    
    .app-sidebar {
      width: 220px;
      height: calc(100vh - 60px);
      background-color: #f8f9fc;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      position: fixed;
      left: 0;
      top: 60px;
      bottom: 0;
      z-index: 999;
      transition: width 0.3s;
      overflow: hidden;
      
      &.collapsed {
        width: 64px;
        
        :deep(.el-menu--collapse) {
          width: 64px;
        }
        
        :deep(.el-sub-menu) {
          .el-sub-menu__title {
            span, .el-sub-menu__icon-arrow {
              display: none;
            }
          }
        }
      }
      
      .el-menu-vertical {
        border-right: none;
        background-color: #f8f9fc;
        
        &:not(.el-menu--collapse) {
          width: 220px;
        }
      }
      
      :deep(.el-menu) {
        background-color: #f8f9fc;
      }
      
      :deep(.el-menu-item) {
        &:hover, &:focus {
          background-color: #eef1f8;
        }
        
        &.is-active {
          background-color: #e6f7ff;
          color: #1890ff;
          border-right: 3px solid #1890ff;
        }
      }
      
      :deep(.el-sub-menu__title) {
        &:hover, &:focus {
          background-color: #eef1f8;
        }
      }
    }
    
    .app-main {
      flex: 1;
      padding: 20px;
      margin-left: 220px;
      transition: margin-left 0.3s;
      min-height: calc(100vh - 100px);
      background-color: #f5f7fa;
      width: calc(100% - 260px);
      
      &.expanded {
        margin-left: 64px;
      }
    }
  }
}

// 页面切换动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  
  .el-icon {
    margin-right: 8px;
  }
}

.user-detail {
  padding: 5px 0;
  min-width: 200px;
  
  .user-name {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 5px;
  }
  
  .user-email {
    font-size: 12px;
    color: #606266;
    margin-bottom: 5px;
  }
  
  .user-login {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }
  
  .user-roles {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    
    .role-tag {
      margin-right: 5px;
    }
  }
}

.menu-loading, .menu-error {
  padding: 20px;
}

// 修改父菜单的样式
.parent-menu-title {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  cursor: pointer;
}

// 移除链接图标相关样式
:deep(.el-sub-menu) {
  .el-sub-menu__title {
    // 移除之前的链接图标悬停样式
  }
}
</style> 