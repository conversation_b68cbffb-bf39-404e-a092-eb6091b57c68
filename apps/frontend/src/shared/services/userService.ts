import httpRequest from '../utils/httpRequest';

export interface UserInfo {
  id: number;
  login: string;
  name: string;
  code: string;
  email: string;
  tenantId: string;
  roles: string[];
  isVerified: boolean;
  verifyType: string;
  verifyExpireTime: number;
  passport: string;
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

/**
 * 获取当前用户信息
 * @returns Promise<UserInfo>
 */
export const getCurrentUser = async (): Promise<UserInfo> => {
  try {
    // 调用获取当前用户信息的接口
    const response = await httpRequest.rawRequestGet('/api/businessoms/system/user/current', {}) as unknown as ApiResponse<UserInfo>;
    if (response.code === 0) {
      return response.data;
    } else {
      throw new Error(response.message || '获取用户信息失败');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
}; 