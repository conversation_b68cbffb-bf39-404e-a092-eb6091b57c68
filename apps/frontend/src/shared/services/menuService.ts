import httpRequest from '../utils/httpRequest';

export interface MenuAttribute {
  [key: string]: any;
}

export interface MenuItem {
  nodeId: number;
  resId: number;
  nodeName: string;
  nodeCode: string;
  nodeType: string;
  parentNodeId: number;
  parentNodeCode: string;
  menuLevel: string;
  menuOrder: number;
  childrenMenuList: MenuItem[];
  childrenMenuCount: number;
  menuURL: string;
  newOpen: boolean;
  createTime: number;
  attributeList: MenuAttribute;
}

export interface MenuTreeResponse {
  businessSysCode: string;
  businessSysName: string;
  menuFolderList: MenuItem[];
  folderCount: number;
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

/**
 * 获取菜单树数据
 * @returns Promise<MenuTreeResponse>
 */
export const getMenuTree = async (): Promise<MenuTreeResponse> => {
  try {
    // 使用正确的接口路径
    const response = await httpRequest.rawRequestGet('/api/businessoms/system/user/menu/tree', {}) as unknown as ApiResponse<MenuTreeResponse>;
    if (response.code === 0) {
      return response.data;
    } else {
      throw new Error(response.message || '获取菜单失败');
    }
  } catch (error) {
    console.error('获取菜单失败:', error);
    throw error;
  }
};

/**
 * 将菜单URL转换为路由路径
 * @param url 菜单URL
 * @returns 路由路径
 */
export const convertMenuUrlToPath = (url: string): string => {
  if (!url) return '';
  
  // 如果是完整URL，提取路径部分（不含hash）
  if (url.startsWith('http')) {
    try {
      const urlObj = new URL(url);
      // 返回路径部分，去掉hash
      return urlObj.pathname;
    } catch (e) {
      console.error('解析URL失败:', url, e);
      return url;
    }
  }
  
  return url;
}; 