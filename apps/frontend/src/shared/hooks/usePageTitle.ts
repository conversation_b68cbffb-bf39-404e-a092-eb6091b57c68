import { watch } from 'vue';
import { useRoute, RouteMeta } from 'vue-router';
import { SYSTEM_NAME } from '../constants';

/**
 * 设置页面标题的钩子函数
 * @param defaultTitle 默认标题
 */
export function usePageTitle(defaultTitle: string = SYSTEM_NAME) {
  const route = useRoute();
  
  // 更新页面标题
  const updateTitle = (title: string) => {
    // 标准页面标题格式
    document.title = title ? `${title} - ${SYSTEM_NAME}` : SYSTEM_NAME;
  };
  
  // 监听路由变化，更新页面标题
  watch(
    () => route.meta,
    (newMeta: RouteMeta) => {
      const title = newMeta.title as string;
      updateTitle(title || defaultTitle);
    },
    { immediate: true, deep: true }
  );
  
  return { updateTitle };
} 