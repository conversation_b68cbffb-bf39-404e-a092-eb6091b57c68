/**
 * 环境配置辅助工具
 * 用于在前端代码中获取和使用环境配置
 */

// 获取当前环境
export const getAppEnv = (): string => {
  return process.env.APP_ENV || 'dev';
};

// 检查是否为开发环境
export const isDev = (): boolean => {
  return getAppEnv() === 'dev';
};

// 检查是否为测试环境
export const isTest = (): boolean => {
  return getAppEnv() === 'newtest';
};

// 检查是否为预发布环境
export const isStaging = (): boolean => {
  return getAppEnv() === 'staging';
};

// 检查是否为生产环境
export const isProduction = (): boolean => {
  return getAppEnv() === 'production';
};

// 检查是否启用调试模式
export const isDebugMode = (): boolean => {
  return process.env.DEBUG_MODE === 'true';
};

// 获取API基础URL
export const getApiBaseUrl = (serviceName: string): string => {
  // 直接返回完整的API路径
  return `/api/${serviceName}/`;
};

// 日志工具，只在调试模式下输出
export const logger = {
  debug: (message: string, ...args: any[]): void => {
    if (isDebugMode()) {
      console.debug(`[${getAppEnv().toUpperCase()}] ${message}`, ...args);
    }
  },
  info: (message: string, ...args: any[]): void => {
    if (isDebugMode()) {
      console.info(`[${getAppEnv().toUpperCase()}] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: any[]): void => {
    console.warn(`[${getAppEnv().toUpperCase()}] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]): void => {
    console.error(`[${getAppEnv().toUpperCase()}] ${message}`, ...args);
  }
};

export default {
  getAppEnv,
  isDev,
  isTest,
  isStaging,
  isProduction,
  isDebugMode,
  getApiBaseUrl,
  logger
}; 