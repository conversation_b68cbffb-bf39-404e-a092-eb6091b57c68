import { ElMessage } from 'element-plus';

/**
 * 复制文本到剪贴板
 * 采用多种策略确保复制的可靠性：
 * 1. 优先使用 Clipboard API
 * 2. 如失败则使用 document.execCommand 作为备选方案
 * 
 * @param text 要复制的文本内容
 * @param successMessage 复制成功时的提示信息，默认为"已复制到剪贴板"
 * @param errorMessage 复制失败时的提示信息，默认为"复制失败，请手动选择并复制"
 * @returns Promise<boolean> 是否复制成功
 */
export const copyToClipboard = async (
  text: string, 
  successMessage: string = '已复制到剪贴板',
  errorMessage: string = '复制失败，请手动选择并复制'
): Promise<boolean> => {
  if (!text) {
    ElMessage.warning('没有内容可复制');
    return false;
  }

  try {
    // 尝试使用现代Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      try {
        await navigator.clipboard.writeText(text);
        ElMessage.success(successMessage);
        return true;
      } catch (err) {
        console.error('使用Clipboard API复制失败:', err);
        // 如果失败，尝试备选方法
        return fallbackCopy(text, successMessage, errorMessage);
      }
    } else {
      // 如果不支持Clipboard API，使用备选方法
      return fallbackCopy(text, successMessage, errorMessage);
    }
  } catch (e) {
    console.error('复制操作发生错误:', e);
    ElMessage.error(errorMessage);
    return false;
  }
};

/**
 * 备选的复制方法，使用document.execCommand
 * 
 * @param text 要复制的文本
 * @param successMessage 成功消息
 * @param errorMessage 失败消息
 * @returns boolean 是否复制成功
 */
const fallbackCopy = (
  text: string,
  successMessage: string,
  errorMessage: string
): boolean => {
  try {
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 将文本区域设为不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    
    // 选择文本并复制
    textArea.focus();
    textArea.select();
    
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    if (success) {
      ElMessage.success(successMessage);
      return true;
    } else {
      ElMessage.warning(errorMessage);
      return false;
    }
  } catch (e) {
    console.error('备选复制方法失败:', e);
    ElMessage.error(errorMessage);
    return false;
  }
};

/**
 * 选择编辑器中的文本
 * 用于复制失败时提供辅助功能
 * 
 * @param editor Monaco编辑器实例
 */
export const selectEditorContent = (editor: any): void => {
  if (!editor) return;
  
  try {
    editor.focus();
    const model = editor.getModel();
    if (model) {
      editor.setSelection(model.getFullModelRange());
    }
  } catch (e) {
    console.error('选择编辑器内容失败:', e);
  }
}; 