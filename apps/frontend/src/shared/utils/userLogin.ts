import httpRequest from './httpRequest';
/**
 * 用户登录、退出
 *  */ 


/** 生成 sso 登录页完整链接 参考代码，未使用 */
// const getSsoLoginUrl = () => {
//   // 线下 SSO 登录页的 baseUrl, 线上 SSO 环境替换为 https://ssosv.sankuai.com/sson/login
//   const ssoLoginBaseUrl = 'https://ssosv.it.test.sankuai.com/sson/login';
    
//   // ⚠️请替换为项目自身使用的 clientId
//   const clientId = 'a958ae5303';
//   const timestamp = Date.now();
  
//   const originalUrl = window.location.href;
//   // ⚠️请将 "/api/carrier/sso/callback64" 替换为你的项目自身使用的 callback url 路径
//   const callbackUrl = `https://${window.location.host}/api/carrier/sso/callback64?original-url=${encodeURIComponent(originalUrl)}`;
//   const ssoLoginUrl = `${ssoLoginBaseUrl}?client_id=${clientId}&t=${timestamp}&redirect_uri=${encodeURIComponent(callbackUrl)}`;
  
//   return ssoLoginUrl;
// };
  
// 退出登录
async function logoutAsync() {
  try {
    /** 请将 "64" 替换为实际项目和环境自己的数字编号, 或在 Oceanus 使用 rewrite 指令统一自定义 logout 路径 */
    // redirect: manual 使 fetch 请求不跟
    localStorage.removeItem('userInfo');
    const response = await httpRequest.rawRequestGet('/api/businessoms/sso/logout', { redirect: 'manual' });
    console.log('/sso/logout respond with 302, response is: ', response);
    window.location.href = '/';
  } catch (e) {
    console.error('Failed to logout', e);
  }
}

//获取登录信息
async function getUerInfo() {
  return await httpRequest.rawRequestGet('/api/businessoms/sso/whoami', { redirect: 'manual' });
}

export { logoutAsync, getUerInfo };