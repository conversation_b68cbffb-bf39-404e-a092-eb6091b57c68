import { createApp } from 'vue';
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import elementPlus from './element-plus';

// 导入全局样式
import '../../assets/styles/global.scss';

// 导入Tailwind CSS
import 'tailwindcss/tailwind.css';

/**
 * 创建模块入口
 * @param component 模块根组件
 * @param routes 模块路由配置
 * @param defaultPath 默认重定向路径
 */
export function createModuleEntry(
  component: any,
  routes: RouteRecordRaw[],
  defaultPath: string
) {
  // 创建独立的路由实例，使用hash模式
  const router = createRouter({
    history: createWebHashHistory(),
    routes: [
      {
        path: '/',
        redirect: defaultPath
      },
      ...routes
    ]
  });

  // 创建应用实例
  const app = createApp(component);

  // 使用路由
  app.use(router);

  // 使用Element Plus组件库
  app.use(elementPlus);

  // 挂载应用
  app.mount('#app');
}

// 保留原函数名以兼容现有代码
export const createPageEntry = createModuleEntry; 