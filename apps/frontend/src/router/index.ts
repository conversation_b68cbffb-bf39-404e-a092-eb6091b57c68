import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import AppLayout from '../shared/components/layout/AppLayout.vue';

// 导入各模块路由
import homeRoutes from '../pages/home/<USER>';
import demoRoutes from '../pages/demo/router';
import corpusRoutes from '../pages/corpus/router';
import learnAIRoutes from '../pages/learnAI/router';
import poiAggreManagerRoutes from '../pages/poiAggreManager/router';
import aiKnowledgeManagerRoutes from '../pages/aiKnowledgeManager/router';

// 合并所有路由
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    component: AppLayout,
    redirect: '/home',
    children: [
      ...homeRoutes,
      ...demoRoutes,
      ...corpusRoutes,
      ...learnAIRoutes,
      ...poiAggreManagerRoutes,
      ...aiKnowledgeManagerRoutes
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../shared/components/NotFound.vue')
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

export default router; 