import axios from 'axios';

const httpRequest = axios.create({
  baseURL: '/api/llm/corpus',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
httpRequest.interceptors.request.use(
  (config) => {
    // 在这里可以添加token等认证信息
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
httpRequest.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 封装 GET 请求
const rawRequestGet = async (url: string, params?: any, config?: any) => {
  try {
    const response = await httpRequest.get(url, { params, ...config });
    return response;
  } catch (error) {
    console.error('GET request failed:', error);
    throw error;
  }
};

// 封装 POST 请求（JSON格式）
const rawRequestPostAsJson = async (url: string, data?: any, config?: any) => {
  try {
    const response = await httpRequest.post(url, data, config);
    return response;
  } catch (error) {
    console.error('POST request failed:', error);
    throw error;
  }
};

// 封装 POST 请求（Form格式）
const rawRequestPostAsForm = async (url: string, data?: any, config?: any) => {
  try {
    const formData = new FormData();
    if (data) {
      Object.keys(data).forEach(key => {
        if (Array.isArray(data[key])) {
          data[key].forEach((item: any) => {
            formData.append(key, item);
          });
        } else {
          formData.append(key, data[key]);
        }
      });
    }
    const response = await httpRequest.post(url, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      ...config
    });
    return response;
  } catch (error) {
    console.error('POST form request failed:', error);
    throw error;
  }
};

export default {
  rawRequestGet,
  rawRequestPostAsJson,
  rawRequestPostAsForm
}; 