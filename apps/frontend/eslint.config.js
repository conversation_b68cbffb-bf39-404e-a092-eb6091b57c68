import globals from "globals";
import typescriptParser from "@typescript-eslint/parser";
import js from "@eslint/js";
import stylistic from "@stylistic/eslint-plugin";
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';

// migration from eslintrc.json to eslint.config.js:
// https://eslint.org/docs/latest/use/configure/migration-guide#processors

// rules reference:
// https://eslint.org/docs/v8.x/rules/

// stylistics reference:
// https://eslint.style/rules/default/semi-style

// react rules reference:
// https://www.npmjs.com/package/eslint-plugin-react

const rulesForJsOrTsSnippet = {
  "no-use-before-define": "error",
  "no-unused-vars": "off",
  "no-constant-condition": "off",
  "arrow-body-style": ["error", "as-needed"],
  "block-scoped-var": "error",
  "eqeqeq": "error",
  "prefer-const": "warn",
  "prefer-destructuring": "warn",
  "prefer-template": "warn",
  "sort-imports": ["error", {
    "ignoreDeclarationSort": true,
  }],
  "require-await": "warn",
  "@stylistic/semi": ["error", "always"],
  "@stylistic/indent": ["error", 2],
  "@stylistic/brace-style": ["error", "1tbs"],
  "@stylistic/max-len": ["error", { 
    code: 120,
    ignoreUrls: true,
    ignoreRegExpLiterals: true
  }],
};

export default [
  js.configs.recommended,
  {
    plugins: {
      '@stylistic': stylistic,
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    files: ["**/*.{js,jsx,mjs,cjs,ts,tsx}"],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        }
      },
      globals: {
        ...globals.browser,
        ...globals.node,
      }
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      ...rulesForJsOrTsSnippet
    }
  },
  {
    ignores: ['**/dist', '**/build', '**/node_modules']
  }
];