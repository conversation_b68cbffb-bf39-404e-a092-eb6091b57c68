# 履约平台技术部AI门户前端项目

## 项目简介

本项目是基于Vue3、TypeScript、Element Plus和Vite构建的现代化履约平台技术部AI门户前端项目。作为履约平台技术部AI相关的门户平台，包含AI产品、AI助手、AI学习资料或分享、AI coding、AI资讯动态等内容。采用多页面应用（MPA）架构，所有页面共用统一的布局框架，同时每个模块也可以作为独立页面访问，便于维护和扩展。

> **环境安装说明**：如需从零开始搭建开发环境，请参考 [环境搭建指南./README-INIT.md](https://dev.sankuai.com/code/repo-detail/bm/banma_business_oms/file/detail?path=apps%2Ffrontend%2FREADME-INIT.md)

## 项目结构

```
apps/frontend/
├── src/                      # 源代码目录
│   ├── assets/               # 静态资源
│   │   ├── images/           # 图片资源
│   │   └── styles/           # 样式文件
│   │       ├── global.scss   # 全局样式
│   │       ├── theme.scss    # 主题样式
│   │       ├── layout.scss   # 布局样式
│   │       ├── components.scss # 组件样式
│   │       └── dashboard.scss # 仪表盘样式
│   ├── pages/                # 功能页面（页面隔离）
│   │   ├── home/             # 首页模块
│   │   │   ├── views/        # 模块视图
│   │   │   ├── router.ts     # 模块路由
│   │   │   └── entry.ts      # 模块入口
│   │   ├── demo/             # 示例模块
│   │   │   ├── views/        # 模块视图
│   │   │   ├── request/      # 模块API请求
│   │   │   ├── router.ts     # 模块路由
│   │   │   ├── types.ts      # 模块类型定义
│   │   │   └── entry.ts      # 模块入口
│   ├── shared/               # 共享资源
│   │   ├── components/       # 共享组件
│   │   │   ├── layout/       # 布局组件
│   │   │   │   └── AppLayout.vue # 应用布局组件
│   │   │   └── NotFound.vue  # 404页面组件
│   │   ├── constants/        # 常量定义
│   │   │   └── index.ts      # 常量索引
│   │   ├── hooks/            # 自定义钩子
│   │   │   └── usePageTitle.ts # 页面标题钩子
│   │   └── utils/            # 工具函数
│   │       ├── httpRequest.ts # HTTP请求工具
│   │       ├── element-plus.ts # Element Plus配置
│   │       ├── userLogin.ts  # 用户登录相关
│   │       ├── ui.ts         # UI工具函数
│   │       └── page-entry.ts # 页面入口工具
│   ├── router/               # 主路由配置
│   │   └── index.ts          # 路由索引
│   ├── types/                # 类型定义
│   │   └── vue-shims.d.ts    # Vue类型声明
│   ├── App.vue               # 主应用组件
│   └── main.ts               # 入口文件
├── pages/                    # 多页面应用HTML入口
│   ├── home/                 # 首页独立HTML
│   │   └── index.html        # 首页HTML入口
│   └── demo/                 # 示例页面独立HTML
│       └── index.html        # 示例页面HTML入口
├── mock/                     # 模拟数据
│   ├── userlist.ts           # 用户列表模拟数据
│   └── createuser.ts         # 创建用户模拟数据
├── index.html                # 主应用HTML入口
├── package.json              # 包管理配置
├── pnpm-lock.yaml            # 包管理锁文件
├── env.config.ts             # 环境变量配置
├── proxy.config.ts           # 代理配置
├── vite.config.ts            # Vite 配置
├── tsconfig.json             # TypeScript 配置
└── tsconfig.paths.json       # 路径别名配置
```

## 技术栈

- **核心框架**：Vue 3.5.13
- **开发语言**：TypeScript 4.8.4
- **UI组件库**：Element Plus 2.9.5
- **构建工具**：Vite 5.1.4
- **包管理器**：pnpm 9.15.1
- **路由管理**：Vue Router 4.5.0
- **样式处理**：SCSS 1.85.1
- **CSS框架**：Tailwind CSS 3.4.17
- **HTTP客户端**：Axios 1.7.9

## 快速开始

### 配置host
127.0.0.1    oms.banma.dev.sankuai.com

### 启动
开发服务器启动后，可以通过以下URL访问不同的页面：
- **主应用**：http://oms.banma.dev.sankuai.com:3000/
- **首页独立页面**：http://oms.banma.dev.sankuai.com:3000/pages/home/
- **示例页面独立页面**：http://oms.banma.dev.sankuai.com:3000/pages/demo/

## 开发指南

### 项目设计理念

1. **多页面应用架构**：
   - 项目采用多页面应用（MPA）架构，每个模块既可以作为主应用的一部分，也可以作为独立页面访问
   - 独立页面有自己的HTML入口和JavaScript入口，完全独立于主应用

2. **模块化设计**：
   - 每个功能模块（如 home、demo）都是独立的，包含自己的组件、路由和视图
   - 模块之间相互隔离，便于将来迁移
   - 每个模块必须遵循统一的目录结构，包括views、router.ts和entry.ts等必要文件
   - 模块内组件命名应使用模块名作为前缀（如UserList.vue），避免命名冲突
   - 模块应当有统一的URL前缀，如`/user`，并在子路由中保持层级清晰
   - 模块内部状态应当封装在当前模块内，避免直接依赖其他模块
   - 模块API请求应集中管理，放在模块下的`request`目录中
   - 新增模块时必须参考现有模块结构，保持项目整体一致性

3. **共享资源**：
   - 所有模块共用一套页面框架（菜单、导航等）
   - 共享组件、工具函数、钩子和常量放在 shared 目录下
   - 跨模块共享的组件、工具和类型定义都应放在`shared`目录下，而不是某个模块内部

4. **路由管理**：
   - 每个模块有自己的路由配置
   - 主路由配置整合各个模块的路由
   - 独立页面有自己的路由实例

### 如何开发新页面

> **重要提示**：在开发新页面时，必须严格遵循项目的前端代码结构规范。确保新增文件放在正确的目录位置，保持一致的命名风格，并按照项目的模块化设计原则组织代码。新开发的页面或组件应当与现有代码保持一致的风格、结构和实现方式，以确保项目的整体一致性和可维护性。

#### 新增模块（独立功能模块）

在创建新功能模块时，请严格遵循以下目录结构和命名规范：

```
src/pages/user/               # 模块根目录，使用小写字母
├── views/                    # 模块视图目录
│   ├── index.vue             # 模块主视图
│   ├── UserList.vue          # 用户列表视图（使用模块名前缀）
│   └── UserDetail.vue        # 用户详情视图（使用模块名前缀）
├── request/                  # API请求目录
│   └── index.ts              # 模块API请求方法集合
├── components/               # 模块专用组件（可选）
│   └── UserCard.vue          # 用户卡片组件（使用模块名前缀）
├── router.ts                 # 模块路由配置
├── types.ts                  # 模块类型定义（可选）
└── entry.ts                  # 模块独立入口
```

1. 在 `src/pages` 下创建新的页面目录，例如 `user`

2. **创建视图组件**：

   在 `src/pages/user/views` 下创建视图组件，例如 `index.vue`：

   ```vue
   <template>
     <div class="user-page">
       <!-- 页面内容 -->
     </div>
   </template>

   <script setup lang="ts">
   // 组件逻辑
   </script>

   <style lang="scss" scoped>
   .user-page {
     // 样式
   }
   </style>
   ```

3. **创建路由配置**：

   在 `src/pages/user` 下创建路由配置，例如 `router.ts`：

   ```typescript
   import { RouteRecordRaw } from 'vue-router';

   const userRoutes: Array<RouteRecordRaw> = [
     {
       path: '/user',
       name: 'User',
       component: () => import('./views/index.vue'),
       meta: {
         title: '用户管理',
         icon: 'user'
       }
     },
     {
       path: '/user/list',
       name: 'UserList',
       component: () => import('./views/UserList.vue'),
       meta: {
         title: '用户列表'
       }
     },
     {
       path: '/user/detail/:id',
       name: 'UserDetail',
       component: () => import('./views/UserDetail.vue'),
       meta: {
         title: '用户详情'
       }
     }
   ];

   export default userRoutes;
   ```

4. **注册模块路由**：

   在 `src/router/index.ts` 中导入并注册模块路由：

   ```typescript
   // 导入新模块路由
   import userRoutes from '../pages/user/router';

   // 在routes定义中添加
   const routes: Array<RouteRecordRaw> = [
     {
       path: '/',
       component: AppLayout,
       redirect: '/home',
       children: [
         ...homeRoutes,
         ...demoRoutes,
         ...userRoutes, // 添加新模块路由
         ...otherRoutes
       ]
     },
     // ...
   ];
   ```

5. **添加菜单项**：

   线下：https://uac.it.test.sankuai.com/app/11344/permission-template

   线上：https://uac.vip.sankuai.com/app/9230/permission-template

6. **创建独立页面（可选）**：

   如果需要将模块作为独立页面访问，需要创建以下文件：

   a. 创建HTML入口文件：

   ```bash
   mkdir -p pages/user
   ```

   在 `pages/user/index.html` 中添加：

   ```html
   <!DOCTYPE html>
   <html lang="zh-CN">
   <head>
     <meta charset="UTF-8" />
     <meta name="viewport" content="width=device-width, initial-scale=1.0" />
     <title>用户管理 - 履约平台技术部AI门户</title>
     <webstaticVersion-script></webstaticVersion-script>
   </head>
   <body>
     <div id="app"></div>
     <script type="module" src="/src/pages/user/entry.ts"></script>
   </body>
   </html>
   ```

   b. 创建入口文件：

   在 `src/pages/user` 下创建 `entry.ts`：

   ```typescript
   import { createPageApp } from '../../shared/utils/page-entry';
   import router from './router';

   createPageApp(router);
   ```

#### 在现有模块新增页面

如果要在已有的模块中添加新页面，流程相对简单：

1. **确定模块位置**：
   
   确定要新增页面的所属模块，例如在 `demo` 模块中添加新页面

2. **创建视图组件**：
   
   在模块的 `views` 目录下创建新页面组件，并遵循命名规范（使用模块名前缀）：
   
   ```
   src/pages/demo/views/
   ├── index.vue             # 已有页面
   ├── DemoList.vue          # 已有页面
   └── DemoDetail.vue        # 新增页面
   ```

3. **更新路由配置**：
   
   在该模块的 `router.ts` 中添加新页面的路由配置，保持URL前缀一致：
   
   ```typescript
   const demoRoutes: Array<RouteRecordRaw> = [
     {
       path: '/demo',
       name: 'Demo',
       component: () => import('./views/index.vue'),
       meta: { title: '示例页面' }
     },
     // 现有路由...
     {
       path: '/demo/detail/:id',
       name: 'DemoDetail',
       component: () => import('./views/DemoDetail.vue'),
       meta: { title: '示例详情' }
     }
   ];
   ```

4. **添加页面导航**：
   
   在模块内的现有页面中添加跳转链接：
   
   ```vue
   <template>
     <router-link :to="{ name: 'DemoDetail', params: { id: item.id } }">
       查看详情
     </router-link>
     <!-- 或者使用编程式导航 -->
     <el-button @click="$router.push({ name: 'DemoDetail', params: { id: item.id } })">
       查看详情
     </el-button>
   </template>
   ```

5. **更新菜单（可选）**：
   
   如果需要在菜单中展示新页面，请更新菜单配置

### 多页面应用架构特性与使用注意事项

在开发新模块或页面时，需要考虑多页面应用架构的特性：

- **模块隔离**：同一模块内的页面共享模块级别的状态和工具，不同模块间应避免直接依赖
- **URL规范**：保持统一的URL前缀，如`/user`、`/demo`，确保路由层级清晰
- **组件命名规范**：使用模块名作为前缀（如`UserList.vue`）避免命名冲突
- **独立页面访问**：独立页面不会加载主应用布局，在开发时需要同时考虑在主应用中的集成和独立访问两种场景
- **访问URL**：在开发环境下，访问独立页面的URL格式为：`http://oms.banma.dev.sankuai.com:3000/pages/{module}/`
- **资源共享**：模块可以共享`shared`目录的组件和工具，但应避免直接依赖其他模块的内部资源
- **API请求**：模块的API请求应当集中管理，放在模块下的`request`目录中，并使用统一的URL前缀`/api/businessoms/{module}/`

### 使用HTTP请求

项目提供了封装好的HTTP请求工具，位于 `src/shared/utils/httpRequest.ts`：

```typescript
// 导入HTTP请求工具
import httpRequest from '@shared/utils/httpRequest';

// 发起GET请求
const getData = async () => {
  try {
    const res = await httpRequest.rawRequestGet('/api/businessoms/data', { param1: 'value1' });
    if (res.code === 0) {
      // 处理成功响应
      console.log(res.data);
    } else {
      // 处理错误响应
      console.error(res.message);
    }
  } catch (error) {
    console.error(error);
  }
};

// 发起POST请求（JSON格式）
const postData = async (data) => {
  try {
    const res = await httpRequest.rawRequestPostAsJson('/api/businessoms/data', data);
    // 处理响应...
  } catch (error) {
    console.error(error);
  }
};

// 发起POST请求（表单格式）
const postFormData = async (data) => {
  try {
    const res = await httpRequest.rawRequestPostAsForm('/api/businessoms/data', data);
    // 处理响应...
  } catch (error) {
    console.error(error);
  }
};
```

### 代理配置

项目使用 `proxy.config.ts` 文件来管理开发环境下的API代理设置，并结合 `env.config.ts` 实现多环境配置：

```typescript
// 导入代理配置
import proxyConfig from './proxy.config';
```

#### 多环境代理配置

项目支持多环境（dev、newtest、staging、production）的代理配置，通过 `env.config.ts` 文件集中管理：


#### 添加新的代理配置

如需添加新的服务代理配置，只需在 `env.config.ts` 文件中的 `apiBaseUrls` 对象中添加新的服务配置：

```typescript
// 在env.config.ts中添加新服务
dev: {
  apiBaseUrls: {
    '/api/businessoms/': 'http://oms.banma.dev.sankuai.com:8080',
    '/api/otherservice/': 'http://oms.banma.dev.sankuai.com:8081',
    '/api/newservice/': 'http://oms.banma.dev.sankuai.com:8082' // 添加新服务
  },
  // 其他配置...
}
```

系统会自动为每个路径创建对应的代理配置，路径键名将直接用作代理路径。

#### 在前端代码中使用环境配置

项目提供了 `env-helper.ts` 工具，方便在前端代码中使用环境配置：

```typescript
import { getApiBaseUrl, isDev, logger } from '@shared/utils/env-helper';

// 获取API基础URL
const apiUrl = getApiBaseUrl('businessoms'); // 返回 '/api/businessoms/'

// 环境检查
if (isDev()) {
  // 开发环境特定代码
}

// 日志输出（只在调试模式下输出）
logger.debug('调试信息', { data: 'some data' });
```

### 多环境构建与部署

项目支持多环境构建与部署，通过环境变量 `AWP_DEPLOY_ENV` 指定当前环境：

### 项目部署与发布

项目使用Talos平台进行部署和发布管理，支持多环境发布流程：

- **测试环境**：使用`qa`分支代码进行部署
- **备机环境**：使用`release`分支代码进行部署
- **线上环境**：使用`master`分支代码进行部署

访问[Talos发布平台]进行项目发布操作(https://talos-better.sankuai.com/release?appId=33277&env=test&flowId=20734633&menuKey=release&templateId=525410)

#### 发布流程

1. **测试环境发布**：
   - 将开发完成的代码合并至`qa`分支
   - 在Talos平台选择测试环境，使用`qa`分支进行构建和部署
   - 测试通过后，进入下一环境发布流程

2. **备机环境发（当前非必须）布**：
   - 将测试通过的代码合并至`release`分支
   - 在Talos平台选择备机环境，使用`release`分支进行构建和部署
   - 验证备机环境功能正常后，可进行线上发布

3. **线上环境发布**：
   - 将备机验证通过的代码合并至`master`分支
   - 在Talos平台选择线上环境，使用`master`分支进行构建和部署

#### 注意事项

- 确保在合并代码到更高环境前，已完成充分的测试验证
- 发布过程中如遇问题，可以在Talos平台查看构建和部署日志

### 样式开发指南

项目使用SCSS作为CSS预处理器，并集成了Tailwind CSS。全局样式文件位于 `src/assets/styles/` 目录下：

- `global.scss`: 全局样式和变量
- `theme.scss`: 主题相关样式
- `components.scss`: 组件通用样式
- `layout.scss`: 布局相关样式
- `dashboard.scss`: 仪表盘相关样式

在编写SCSS时，需要注意以下几点：

1. **@use 规则**：使用 `@use` 替代已弃用的 `@import`，并确保它位于文件顶部：

   ```scss
   @use './theme.scss';
   
   // 其他样式规则...
   ```

2. **CSS变量**：优先使用CSS变量实现主题和样式复用：

   ```scss
   .my-component {
     color: var(--text-color-primary);
     background-color: var(--background-color-base);
     padding: var(--spacing-medium);
   }
   ```

3. **组件样式隔离**：使用 `scoped` 属性隔离组件样式：

   ```vue
   <style lang="scss" scoped>
   .component {
     // 样式将被隔离，不会影响其他组件
   }
   </style>
   ```

## 注意事项

1. **API路径前缀**：
   - 所有本项目后端API请求路径都应使用 `/api/businessoms/` 前缀，其他后端请求`/api/xxx/`
   - 例如：`/api/businessoms/user/list`、`/api/aiassistant/demo/create`

2. **多页面应用特性**：
   - 主应用和独立页面共享同一套代码，但有不同的入口
   - 独立页面不包含主应用的布局和菜单
   - 独立页面可以有自己的路由配置和样式

3. **代码规范**：
   - 使用TypeScript编写代码，确保类型安全
   - 遵循Vue3的组合式API风格
   - 组件名使用PascalCase命名
   - 变量和函数使用camelCase命名
   - 常量使用UPPER_SNAKE_CASE命名

4. **样式处理**：
   - 使用SCSS编写样式
   - 组件样式使用`scoped`属性隔离
   - 全局样式放在`src/assets/styles`目录下
   - 使用Element Plus提供的变量和主题

5. **性能优化**：
   - 路由组件使用异步加载
   - 大型组件拆分为小组件
   - 使用`computed`和`watch`优化计算和副作用
   - 避免不必要的组件重渲染

6. **Element Plus使用**：
   - Element Plus组件库已全局引入，无需在组件中单独引入
   - 图标组件也已全局注册，可直接使用

## 常见问题

### 路径引用错误

如果遇到路径引用错误，请检查：
1. 组件路径是否正确（如 `../pages/demo/views/index.vue`）
2. 是否缺少必要的组件文件
3. 路径大小写是否正确

### Sass相关警告

如果遇到Sass相关警告，如 `@import` 规则已弃用，请使用 `@use` 规则替代，并确保它位于文件顶部，在其他任何规则之前。例如：

```scss
/* 正确用法 */
@use './theme.scss';

/* 其他样式规则 */
.my-class {
  color: red;
}
```



### API请求问题

如果API请求失败，请检查：
1. API路径前缀是否正确（应为 `/api/businessoms/`）
2. 后端服务是否正常运行
3. 请求参数格式是否正确
4. 网络连接是否正常
5. 代理配置是否正确（检查 `proxy.config.ts`）

## 帮助与支持

如有任何问题，请联系项目负责人或参考项目文档。详细的环境搭建步骤请参考 [环境搭建指南](./README-INIT.md)。

