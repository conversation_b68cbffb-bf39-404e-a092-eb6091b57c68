#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 基础路径
const basePath = path.join(__dirname, '..');
const srcPagesPath = path.join(basePath, 'src', 'pages');
const htmlPagesPath = path.join(basePath, 'pages');

// 模板内容
const viewTemplate = `<template>
  <div class="{{pageName}}-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>{{pageTitle}}</span>
        </div>
      </template>
      <div class="card-content">
        <!-- 页面内容 -->
        <p>这是 {{pageTitle}} 页面的内容</p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 页面数据
const loading = ref(false);

// 页面加载时执行
onMounted(() => {
  console.log('{{pageTitle}} 页面已加载');
});
</script>

<style lang="scss" scoped>
.{{pageName}}-page {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .card-content {
    min-height: 300px;
  }
}
</style>`;

const entryTemplate = `import { createPageEntry } from '../../../shared/utils/page-entry';
import {{moduleName}}Routes from '../router';
import {{componentName}}View from './view.vue';

// 创建页面入口
createPageEntry({{componentName}}View, {{moduleName}}Routes, '/{{routePath}}');`;

const routerTemplate = `import { RouteRecordRaw } from 'vue-router';

const {{moduleName}}Routes: Array<RouteRecordRaw> = [
  {
    path: '/{{modulePath}}',
    name: '{{moduleName}}',
    component: () => import('./index/view.vue'),
    meta: {
      title: '{{moduleTitle}}',
      icon: '{{moduleIcon}}'
    }
  }{{additionalRoutes}}
];

export default {{moduleName}}Routes;`;

const htmlTemplate = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{{pageTitle}} - 履约平台技术部AI门户</title>
  <link rel="icon" href="/src/assets/images/icon.png" type="image/png">
  <webstaticVersion-script></webstaticVersion-script>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/src/pages/{{moduleName}}/{{pageName}}/entry.ts"></script>
</body>
</html>`;

// 首字母大写
const capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1);

// 创建目录
const createDir = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`创建目录: ${dirPath}`);
  }
};

// 创建文件
const createFile = (filePath, content) => {
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, content);
    console.log(`创建文件: ${filePath}`);
  } else {
    console.log(`文件已存在: ${filePath}`);
  }
};

// 更新路由文件
const updateRouterFile = (moduleName, moduleTitle, modulePath, moduleIcon, pages) => {
  const routerPath = path.join(srcPagesPath, moduleName, 'router.ts');
  
  let additionalRoutes = '';
  
  // 为除了 index 之外的页面添加路由
  pages.forEach(page => {
    if (page !== 'index') {
      const pagePath = `${modulePath}/${page}`;
      const pageName = `${capitalize(moduleName)}${capitalize(page)}`;
      const pageTitle = `${moduleTitle}${page === 'detail' ? '详情' : page}`;
      
      additionalRoutes += `,
  {
    path: '/${pagePath}',
    name: '${pageName}',
    component: () => import('./${page}/view.vue'),
    meta: {
      title: '${pageTitle}',
      icon: '${moduleIcon}'
    }
  }`;
    }
  });
  
  const routerContent = routerTemplate
    .replace(/{{moduleName}}/g, capitalize(moduleName))
    .replace(/{{moduleTitle}}/g, moduleTitle)
    .replace(/{{modulePath}}/g, modulePath)
    .replace(/{{moduleIcon}}/g, moduleIcon)
    .replace(/{{additionalRoutes}}/g, additionalRoutes);
  
  createFile(routerPath, routerContent);
};

// 创建页面
const createPage = (moduleName, moduleTitle, modulePath, moduleIcon, pageName, pageTitle) => {
  // 创建目录
  const pageSrcDir = path.join(srcPagesPath, moduleName, pageName);
  createDir(pageSrcDir);
  
  // 创建 HTML 页面目录
  const htmlDir = path.join(htmlPagesPath, moduleName);
  createDir(htmlDir);
  
  // 创建视图文件
  const viewPath = path.join(pageSrcDir, 'view.vue');
  const viewContent = viewTemplate
    .replace(/{{pageName}}/g, pageName)
    .replace(/{{pageTitle}}/g, pageTitle);
  createFile(viewPath, viewContent);
  
  // 创建入口文件
  const entryPath = path.join(pageSrcDir, 'entry.ts');
  const routePath = pageName === 'index' ? modulePath : `${modulePath}/${pageName}`;
  const componentName = capitalize(pageName);
  const entryContent = entryTemplate
    .replace(/{{moduleName}}/g, capitalize(moduleName))
    .replace(/{{componentName}}/g, componentName)
    .replace(/{{routePath}}/g, routePath);
  createFile(entryPath, entryContent);
  
  // 创建 HTML 文件
  const htmlFileName = pageName === 'index' ? 'index.html' : `${pageName}.html`;
  const htmlPath = path.join(htmlDir, htmlFileName);
  const htmlContent = htmlTemplate
    .replace(/{{pageTitle}}/g, pageTitle)
    .replace(/{{moduleName}}/g, moduleName)
    .replace(/{{pageName}}/g, pageName);
  createFile(htmlPath, htmlContent);
};

// 主函数
const main = async () => {
  console.log('=== 页面生成工具 ===');
  
  // 获取用户输入
  const moduleName = await new Promise(resolve => {
    rl.question('请输入模块名称 (例如: user): ', answer => resolve(answer.trim()));
  });
  
  const moduleTitle = await new Promise(resolve => {
    rl.question('请输入模块标题 (例如: 用户管理): ', answer => resolve(answer.trim()));
  });
  
  const modulePath = await new Promise(resolve => {
    rl.question(`请输入模块路径 (默认: ${moduleName}): `, answer => {
      resolve(answer.trim() || moduleName);
    });
  });
  
  const moduleIcon = await new Promise(resolve => {
    rl.question('请输入模块图标 (默认: document): ', answer => {
      resolve(answer.trim() || 'document');
    });
  });
  
  const pagesInput = await new Promise(resolve => {
    rl.question('请输入页面列表，用逗号分隔 (默认: index): ', answer => {
      resolve(answer.trim() || 'index');
    });
  });
  
  const pages = pagesInput.split(',').map(p => p.trim());
  
  // 创建模块目录
  const moduleSrcDir = path.join(srcPagesPath, moduleName);
  createDir(moduleSrcDir);
  
  // 更新路由文件
  updateRouterFile(moduleName, moduleTitle, modulePath, moduleIcon, pages);
  
  // 创建每个页面
  for (const pageName of pages) {
    const pageTitle = pageName === 'index' 
      ? moduleTitle 
      : pageName === 'detail' 
        ? `${moduleTitle}详情` 
        : `${moduleTitle}${pageName}`;
    
    createPage(moduleName, moduleTitle, modulePath, moduleIcon, pageName, pageTitle);
  }
  
  console.log('\n=== 页面生成完成 ===');
  console.log(`\n请在主路由文件 (src/router/index.ts) 中导入并注册新模块的路由:`);
  console.log(`\nimport ${moduleName}Routes from '../pages/${moduleName}/router';`);
  console.log(`\n并在 routes 定义中添加:`);
  console.log(`\n...${moduleName}Routes,`);
  
  rl.close();
};

main().catch(err => {
  console.error('发生错误:', err);
  rl.close();
}); 