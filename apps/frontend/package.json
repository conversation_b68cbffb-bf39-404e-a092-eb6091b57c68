{"name": "OMS", "version": "0.0.1", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "description": "履约平台技术部AI门户 - 履约平台技术部AI相关的门户平台型网站", "keywords": [], "homepage": "", "repository": {"type": "git", "url": ""}, "type": "module", "private": true, "scripts": {"postinstall": "echo '依赖安装完成'", "dev": "AWP_DEPLOY_ENV=dev vite --host oms.banma.dev.sankuai.com --open", "build": "outDirName='' vite build", "dist": "outDirName='test' entry=pages vite build", "typecheck": "tsc --noEmit --strict", "lint": "eslint --cache .", "format": "eslint --cache --fix .", "format:json": "prettier --cache --write *.json", "clear:deps": "rm -rf node_modules", "create-page": "node scripts/create-page.js"}, "lint-staged": {"src/**/*.ts": ["eslint --cache --fix"], "packages/**/*.d.ts": ["eslint --cache --fix"], "packages/*/src/**/*.{tsx,jsx,js}": ["eslint --cache --fix"], "packages/*/src/**/*.json": ["prettier --cache --write"]}, "devDependencies": {"@eslint/js": "^8.0.0", "@rollup/plugin-typescript": "^12.1.2", "@stylistic/eslint-plugin": "1.8.1", "@types/dompurify": "^3.2.0", "@types/marked": "^6.0.0", "@types/node": "^22.10.2", "@typescript-eslint/parser": "^5.1.0", "autoprefixer": "^10.4.20", "dompurify": "^3.2.4", "element-plus": "^2.9.5", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "lint-staged": "^15.4.3", "marked": "^15.0.7", "mockjs": "^1.1.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "rollup": "^4.30.0", "sass": "^1.85.1", "tailwindcss": "^3.4.17", "typescript": "^4.8.4", "vite": "6.3.3", "vite-plugin-mock": "^3.0.2"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-vue": "5.2.1", "axios": "^1.7.9", "dayjs": "^1.11.13", "echarts": "^5.6.0", "elkjs": "^0.10.0", "monaco-editor": "^0.52.2", "terser": "^5.39.0", "vue": "3.5.13", "vue-router": "4.5.0"}}