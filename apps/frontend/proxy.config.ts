/**
 * 代理配置文件
 * 用于多环境下的API代理设置
 */
import { getCurrentEnvConfig, getCurrentEnv } from './env.config';

// 创建代理配置
const createProxyConfig = () => {
  const envConfig = getCurrentEnvConfig();
  const { apiBaseUrls } = envConfig;
  const currentEnv = getCurrentEnv();
  const isDev = currentEnv === 'dev';
  
  console.log(`Creating proxy config for environment: ${process.env.AWP_DEPLOY_ENV || 'dev'}`);
  
  // 创建基础代理配置
  const baseProxyConfig = (serviceName: string, path: string) => {
    const config: Record<string, any> = {
      target: apiBaseUrls[serviceName],
      changeOrigin: true,
      secure: false,
      // 只有开发环境移除前缀，其他环境保留前缀
      rewrite: isDev 
        ? (reqPath: string) => reqPath.replace(new RegExp(`^${path}`), '/') 
        : (reqPath: string) => reqPath
    };
    
    // 开发环境下添加请求头，但不再手动设置Cookie
    if (isDev) {
      config.configure = (proxy: any, options: any) => {
        proxy.on('proxyReq', (proxyReq: any) => {
          // 添加其他可能需要的头信息
          proxyReq.setHeader('X-Requested-With', 'XMLHttpRequest');
          // 不再手动设置Cookie，使用浏览器中已设置的Cookie
        });
      };
    }
    
    return config;
  };
  
  // 生成所有服务的代理配置
  const proxyConfig: Record<string, any> = {};
  
  // 为每个服务创建代理配置
  Object.keys(apiBaseUrls).forEach(serviceName => {
    // 直接使用serviceName作为代理路径
    proxyConfig[`${serviceName}`] = baseProxyConfig(serviceName, serviceName);
  });
  
  return proxyConfig;
};

// 导出当前环境的代理配置
export const proxyConfig = createProxyConfig();

export default proxyConfig; 