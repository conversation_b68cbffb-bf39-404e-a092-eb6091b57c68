import { defineConfig } from 'vite';
import { join, dirname } from "node:path";
import { fileURLToPath } from 'node:url';
import vue from "@vitejs/plugin-vue";
import { viteMockServe } from 'vite-plugin-mock';
import legacy from '@vitejs/plugin-legacy';
import fs from 'node:fs';
// 导入代理配置和环境配置
import proxyConfig from './proxy.config';
import { getCurrentEnvConfig } from './env.config';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取当前环境配置
const envConfig = getCurrentEnvConfig();

// HTML模板处理插件
const preHtmlPlugin = () => ({
  name: 'pre-html',
  transformIndexHtml: (html: string) => {
    const getBuildVersionScript = () => `<script>window.____BuildVersion='${process.env.__BuildVersion || ''}'; window.__ENV='${process.env.AWP_DEPLOY_ENV || ''}';</script>`;
    return html.replace('<webstaticVersion-script></webstaticVersion-script>', getBuildVersionScript());
  }
});

// 获取所有模块页面
const getModulePages = () => {
  const pagesDir = join(__dirname, 'pages');
  const pages: Record<string, string> = {};
  
  // 主页面
  pages.main = join(__dirname, 'index.html');
  
  // 遍历 pages 目录下的所有模块目录
  const modules = fs.readdirSync(pagesDir);
  
  modules.forEach(module => {
    const modulePath = join(pagesDir, module);
    
    if (fs.statSync(modulePath).isDirectory()) {
      // 每个模块只有一个入口HTML文件
      const htmlPath = join(modulePath, 'index.html');
      if (fs.existsSync(htmlPath)) {
        pages[module] = htmlPath;
      }
    }
  });
  
  return pages;
};

export default defineConfig({
  base: process.env['PUBLIC_PATH'] ? process.env['PUBLIC_PATH'] : '',
  // @ts-ignore
  decodedBase: process.env['PUBLIC_PATH'] ? process.env['PUBLIC_PATH'] : '',
  plugins: [
    vue(),
    preHtmlPlugin(),
    viteMockServe({
      mockPath: 'mock',
      enable: envConfig.features.enableMock,
    }),
    legacy({
      targets: ['ie >= 11', 'chrome 52'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      renderLegacyChunks: true,
      modernPolyfills: true,
    }),
  ],
  resolve: {
    alias: {
      '@': join(__dirname, 'src'),
      '@assets': join(__dirname, 'src/assets'),
      '@types': join(__dirname, 'src/types'),
      '@pages': join(__dirname, 'src/pages'),
      '@shared': join(__dirname, 'src/shared'),
    }
  },
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      scss: {
        charset: false,
        sassOptions: {
          outputStyle: 'expanded'
        }
      }
    }
  },
  server: {
    port: 3000,
    host: 'oms.banma.dev.sankuai.com',
    open: true,
    proxy: proxyConfig
  },
  build: {
    outDir: 'build',
    minify: 'esbuild', // 使用 esbuild 进行压缩
    rollupOptions: {
      //input: getModulePages() //多页编译入口
      input: './index.html' //单页编译
    }
  },
  define: {
    // 将环境变量注入到客户端代码
    'process.env.APP_ENV': JSON.stringify(process.env.AWP_DEPLOY_ENV || 'dev'),
    'process.env.DEBUG_MODE': JSON.stringify(envConfig.features.debugMode)
  }
});