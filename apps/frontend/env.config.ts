/**
 * 环境配置文件
 * 用于集中管理不同环境的配置
 */

// 环境类型定义
export type EnvType = 'dev' | 'newtest' | 'staging' | 'production';

// 环境配置接口
export interface EnvConfig {
  // API基础URL
  apiBaseUrls: {
    [key: string]: string;
  };
  // 其他环境特定配置
  features: {
    enableMock: boolean;
    debugMode: boolean;
  };
}

// 环境配置映射
export const envConfigs: Record<EnvType, EnvConfig> = {
  // 开发环境配置
  dev: {
    apiBaseUrls: {
      '/api/businessoms/': 'http://oms.banma.test.sankuai.com/api/businessoms/',
      '/api/llm/corpus/': 'http://oms.banma.test.sankuai.com/api/llm/corpus',
      '/api/otherservice/': 'http://localhost:8081'
    },
    features: {
      enableMock: false,
      debugMode: true
    }
  },
  
  // 测试环境配置
  newtest: {
    apiBaseUrls: {
      '/api/businessoms/': 'http://oms.banma.test.sankuai.com',
      '/api/llm/corpus/': 'http://oms.banma.test.sankuai.com/api/llm/corpus',
      '/api/otherservice/': 'http://test-other-api.example.com'
    },
    features: {
      enableMock: false,
      debugMode: true
    }
  },
  
  // 预发布环境配置
  staging: {
    apiBaseUrls: {
      '/api/businessoms/': 'http://oms.banma.st.sankuai.com',
      '/api/llm/corpus/': 'https://dos-banma.sankuai.com/api/llm/corpus',
      '/api/otherservice/': 'http://stage-other-api.example.com'
    },
    features: {
      enableMock: false,
      debugMode: false
    }
  },
  
  // 生产环境配置
  production: {
    apiBaseUrls: {
      '/api/businessoms/': 'http://oms-banma.sankuai.com',
      '/api/llm/corpus/': 'http://oms-banma.sankuai.com/api/llm/corpus',
      '/api/otherservice/': 'http://other-api.example.com'
    },
    features: {
      enableMock: false,
      debugMode: false
    }
  }
};

// 获取当前环境
export const getCurrentEnv = (): EnvType => {
  const env = process.env.AWP_DEPLOY_ENV || 'dev';
  return env as EnvType;
};

// 获取当前环境配置
export const getCurrentEnvConfig = (): EnvConfig => {
  const env = getCurrentEnv();
  return envConfigs[env];
};

export default {
  getCurrentEnv,
  getCurrentEnvConfig,
  envConfigs
}; 