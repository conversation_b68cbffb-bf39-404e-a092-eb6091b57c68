# 前端项目环境搭建指南

本文档将指导你从零开始搭建前端开发环境，包括所需工具的安装和项目的初始化。

## 1. 环境要求

- **Node.js**: 版本 >= 18.18.0
- **pnpm**: 版本 >= 9.15.1
- **操作系统**: Windows/macOS/Linux

## 2. 安装必要工具

### 2.1 安装 Node.js

Node.js 是 JavaScript 运行环境，我们需要它来运行前端项目。

#### macOS 安装方法:

推荐使用 [nvm](https://github.com/nvm-sh/nvm) 来管理 Node.js 版本：

```bash
# 安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash

# 安装 Node.js 18.18.0
nvm install 18.18.0

# 设置为默认版本
nvm use 18.18.0
```

#### Windows 安装方法:

1. 访问 [Node.js 官网](https://nodejs.org/)
2. 下载并安装 18.18.0 LTS 版本
3. 安装完成后，打开命令提示符验证安装：

```bash
node -v  # 应显示 v18.18.0
npm -v   # 应显示 npm 版本
```

### 2.2 安装 pnpm

pnpm 是本项目使用的包管理工具，它比 npm 更快、更节省磁盘空间。

```bash
# 使用 npm 安装 pnpm
npm install -g pnpm@9.15.1

# 验证安装
pnpm -v  # 应显示 9.15.1
```

### 2.3 安装 Git

Git 是版本控制工具，用于代码管理。

#### macOS 安装方法:

```bash
# 使用 Homebrew 安装
brew install git

# 或者下载安装包：https://git-scm.com/download/mac
```

#### Windows 安装方法:

1. 访问 [Git 官网](https://git-scm.com/download/win)
2. 下载并安装 Git for Windows
3. 安装完成后，打开 Git Bash 验证安装：

```bash
git --version  # 应显示 Git 版本
```

## 3. 获取项目代码

```bash
# 克隆项目仓库
git clone <项目仓库地址>

# 进入项目目录
cd oms
```

## 4. 安装项目依赖

本项目使用 pnpm 工作区管理多个包，需要按照以下步骤安装依赖：

```bash
# 安装根目录依赖
pnpm install

# 安装前端项目依赖
pnpm frontend:install
```

## 5. 开发环境配置

### 5.1 配置本地hosts

为了使开发环境能够正确访问，需要在本地hosts文件中添加以下配置：

```
127.0.0.1    oms.banma.dev.sankuai.com
```

#### 使用SwitchHosts工具（推荐）

1. 下载并安装 [SwitchHosts](https://github.com/oldj/SwitchHosts/releases)
2. 打开SwitchHosts，创建新的hosts配置
3. 添加上述映射并启用

#### 手动修改hosts文件（略）

### 5.2 SSO Cookie配置

开发环境需要设置SSO Cookie才能正常访问API。本项目提供了Cookie管理工具，可以在导航栏中找到：

1. 启动项目后，在导航栏右上角找到Cookie管理按钮（刷子图标）
2. 点击按钮打开Cookie管理对话框
3. 如果已有Cookie值，可以点击"获取"按钮查看
4. 如需设置新的Cookie值，输入值后点击"设置"按钮
5. 设置成功后页面将自动刷新，应用新的Cookie值

Cookie名称为 `20c01d5264_ssoid`，在开发环境中，此Cookie将用于API请求的身份验证。

### 5.3 使用测试环境后端服务

如果你不需要修改OMS后端服务，开发时可以使用测试环境的后端服务（而不是本地后端），可以修改环境配置：

1. 打开 `apps/frontend/env.config.ts` 文件
2. 找到 `dev` 环境的 `apiBaseUrls` 配置
3. 将 `/api/businessoms/` 的值修改为：
   ```typescript
   '/api/businessoms/': 'http://oms.banma.test.sankuai.com/api/businessoms/'
   ```
4. 保存文件并重启开发服务器

这样前端请求将直接发送到测试环境的后端服务，适用于只进行前端开发或当本地后端无法正常工作时。

## 6. 启动开发服务器

### 6.1 仅启动前端服务

```bash
# 启动前端开发服务器
pnpm frontend:dev
```

服务启动后，浏览器会自动打开 `http://oms.banma.dev.sankuai.com:3000`。

### 6.2 同时启动前端和后端服务

```bash
# 启动前端和后端服务
pnpm dev
```

## 7. 项目结构说明

```
oms/
├── apps/                  # 应用目录
│   ├── frontend/          # 前端应用
│   │   ├── mock/          # 模拟数据
│   │   ├── pages/         # 页面入口
│   │   ├── public/        # 静态资源
│   │   ├── src/           # 源代码
│   │   │   ├── assets/    # 资源文件
│   │   │   ├── pages/     # 页面组件
│   │   │   └── shared/    # 共享代码
│   │   ├── index.html     # 主入口 HTML
│   │   └── vite.config.ts # Vite 配置
│   └── backend/           # 后端应用
├── packages/              # 共享包
└── package.json           # 项目配置
```

## 8. 技术栈

- **构建工具**: Vite 5.1.4
- **前端框架**: Vue 3.5.13
- **路由**: Vue Router 4.5.0
- **UI 组件库**: Element Plus 2.9.5
- **HTTP 客户端**: Axios 1.7.9
- **CSS 预处理器**: Sass 1.85.1
- **CSS 框架**: Tailwind CSS 3.4.17
- **TypeScript**: 4.8.4

## 9. 常见问题解决

### 9.1 Node.js 版本问题

如果遇到 Node.js 版本不兼容问题，请确保使用 Node.js 18.18.0 或更高版本。可以使用 nvm 切换版本：

```bash
nvm use 18.18.0
```

### 9.2 端口占用问题

如果 3000 端口被占用，Vite 会自动尝试其他端口（如 3001）。如果需要指定端口，可以修改 `vite.config.ts` 中的 `server.port` 配置。

### 9.3 Sass 相关警告

如果遇到 Sass 相关警告，如 `@import` 规则已弃用，请使用 `@use` 规则替代，并确保它位于文件顶部，在其他任何规则之前。

### 9.4 依赖安装失败

如果依赖安装失败，可以尝试以下方法：

```bash
# 清除 pnpm 缓存
pnpm store prune

# 重新安装依赖
pnpm install --force
```

## 10. 开发规范

### 10.1 代码风格

- 使用 TypeScript 编写代码
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码

### 10.2 提交规范

提交代码前，请确保：

1. 代码通过 ESLint 检查：`pnpm lint`
2. TypeScript 类型检查通过：`pnpm typecheck`

## 11. 构建生产版本

```bash
# 构建前端生产版本
pnpm frontend:build

# 构建整个项目
pnpm build
```

构建后的文件将输出到 `apps/frontend/dist` 目录。

## 12. 其他命令

```bash
# 类型检查
pnpm typecheck

# 代码格式化
pnpm format

# 创建新页面
pnpm create-page
```

## 13. 帮助与支持

如有任何问题，请联系项目负责人或参考项目文档。 