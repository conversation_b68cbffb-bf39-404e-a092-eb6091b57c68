package com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager;

import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryFieldMetadata;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聚合查询字段元数据Mapper
 * <AUTHOR>
 */
@Mapper
public interface QueryFieldMetadataMapper {
    /**
     * 插入字段元数据
     *
     * @param record 字段元数据
     * @return 影响行数
     */
    int insert(QueryFieldMetadata record);

    /**
     * 根据ID更新字段元数据
     *
     * @param record 字段元数据
     * @return 影响行数
     */
    int updateById(QueryFieldMetadata record);

    /**
     * 分页条件查询字段元数据
     *
     * @param fieldCode 字段Code
     * @param fieldName 字段名称
     * @param type 字段类型
     * @param handlerType 处理器类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 字段元数据列表
     */
    List<QueryFieldMetadata> selectPage(@Param("fieldCode") String fieldCode,
                                      @Param("fieldName") String fieldName,
                                      @Param("type") String type,
                                      @Param("handlerType") Integer handlerType,
                                      @Param("offset") Integer offset,
                                      @Param("limit") Integer limit);

    /**
     * 条件查询总数
     *
     * @param fieldCode 字段Code
     * @param fieldName 字段名称
     * @param type 字段类型
     * @param handlerType 处理器类型
     * @return 总数
     */
    int selectPageCount(@Param("fieldCode") String fieldCode,
                       @Param("fieldName") String fieldName,
                       @Param("type") String type,
                        @Param("handlerType") Integer handlerType);

    /**
     * 批量删除字段元数据
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据字段Code查询字段元数据
     *
     * @param fieldCode 字段Code
     * @return 字段元数据
     */
    QueryFieldMetadata selectByFieldCode(@Param("fieldCode") String fieldCode);

    /**
     * 根据ID查询字段元数据
     *
     * @param id 主键ID
     * @return 字段元数据
     */
    QueryFieldMetadata selectById(@Param("id") Long id);

    /**
     * 根据ID列表批量查询字段元数据
     *
     * @param ids ID列表
     * @return 字段元数据列表
     */
    List<QueryFieldMetadata> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据字段属性查询字段元数据
     *
     * @param fieldProperty 字段属性
     * @return 字段元数据
     */
    QueryFieldMetadata selectByFieldProperty(@Param("fieldProperty") String fieldProperty);

    /**
     * 根据字段Code或字段属性查询字段元数据
     * @param fieldCode
     * @param fieldProperty
     * @return
     */
    QueryFieldMetadata selectByFieldCodeOrFieldProperty(String fieldCode, String fieldProperty);

    /**
     * 批量更新状态
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 批量插入字段元数据
     *
     * @param records 字段元数据列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<QueryFieldMetadata> records);
}
