package com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools;

import java.util.List;
import java.util.Map;

/**
 * JSON Schema DTO
 * <AUTHOR>
 */
public class JsonSchemaDTO {
    private String canonicalName;
    private String codeSource;
    private List<MethodInfo> methods;
    private List<TypeInfo> types;
    private String uniqueId;
    
    /**
     * 返回类型信息（用于前端展示）
     */
    private ReturnTypeDTO returnType;

    /**
     * 方法信息
     */
    public static class MethodInfo {
        private String name;
        private List<String> parameterTypes;
        private String returnType;
        private List<Object> parameters;
        private String returnTypeRef;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<String> getParameterTypes() {
            return parameterTypes;
        }

        public void setParameterTypes(List<String> parameterTypes) {
            this.parameterTypes = parameterTypes;
        }

        public String getReturnType() {
            return returnType;
        }

        public void setReturnType(String returnType) {
            this.returnType = returnType;
        }

        public List<Object> getParameters() {
            return parameters;
        }

        public void setParameters(List<Object> parameters) {
            this.parameters = parameters;
        }
        
        public String getReturnTypeRef() {
            return returnTypeRef;
        }

        public void setReturnTypeRef(String returnTypeRef) {
            this.returnTypeRef = returnTypeRef;
        }
    }

    /**
     * 类型信息
     */
    public static class TypeInfo {
        private Object id;
        private String type;
        private List<Object> items;
        private List<Object> enums;
        private Object $ref;
        private Map<String, Object> properties;
        private String typeBuilderName;
        private String category;

        public Object getId() {
            return id;
        }

        public void setId(Object id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public List<Object> getItems() {
            return items;
        }

        public void setItems(List<Object> items) {
            this.items = items;
        }

        public List<Object> getEnums() {
            return enums;
        }

        public void setEnums(List<Object> enums) {
            this.enums = enums;
        }

        public Object get$ref() {
            return $ref;
        }

        public void set$ref(Object $ref) {
            this.$ref = $ref;
        }

        public Map<String, Object> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }

        public String getTypeBuilderName() {
            return typeBuilderName;
        }

        public void setTypeBuilderName(String typeBuilderName) {
            this.typeBuilderName = typeBuilderName;
        }
        
        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }
    }

    public String getCanonicalName() {
        return canonicalName;
    }

    public void setCanonicalName(String canonicalName) {
        this.canonicalName = canonicalName;
    }

    public String getCodeSource() {
        return codeSource;
    }

    public void setCodeSource(String codeSource) {
        this.codeSource = codeSource;
    }

    public List<MethodInfo> getMethods() {
        return methods;
    }

    public void setMethods(List<MethodInfo> methods) {
        this.methods = methods;
    }

    public List<TypeInfo> getTypes() {
        return types;
    }

    public void setTypes(List<TypeInfo> types) {
        this.types = types;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }
    
    public ReturnTypeDTO getReturnType() {
        return returnType;
    }
    
    public void setReturnType(ReturnTypeDTO returnType) {
        this.returnType = returnType;
    }
} 