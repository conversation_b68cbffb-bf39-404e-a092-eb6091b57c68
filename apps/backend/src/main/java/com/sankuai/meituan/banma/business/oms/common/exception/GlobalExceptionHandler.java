package com.sankuai.meituan.banma.business.oms.common.exception;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
public class GlobalExceptionHandler {

    /**
     * 处理PoiAggreQueryManagerException异常
     */
    @ExceptionHandler(PoiAggreQueryManagerException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<String> handleQueryFieldMetadataException(PoiAggreQueryManagerException e) {
        log.error("Poi聚合查询管理异常: ", e);
        return CommonResult.failed(e.getCode(), e.getMessage());
    }

    /**
     * 处理请求参数无效异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<String> handleValidationExceptions(MethodArgumentNotValidException e) {
        // 处理校验失败的逻辑
        log.error("请求参数无效: ", e);
        return CommonResult.failed(400, e.getMessage());
    }

    /**
     * 处理Lion操作异常
     */
    @ExceptionHandler(LionPartialOperateException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<String> handleLionOperateException(LionPartialOperateException e) {
        log.error("Lion操作异常: ", e);
        return CommonResult.failed(e.getCode(), e.getMessage());
    }

    /**
     * 处理其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public CommonResult<String> handleException(Exception e) {
        log.error("#系统异常#: ", e);
        return CommonResult.failed("系统异常: " + e.getMessage());
    }
} 