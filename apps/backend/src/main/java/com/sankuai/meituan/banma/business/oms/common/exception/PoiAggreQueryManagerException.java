package com.sankuai.meituan.banma.business.oms.common.exception;

/**
 * Poi聚合查询管理异常
 * <AUTHOR>
 */
public class PoiAggreQueryManagerException extends RuntimeException {
    private int code;

    public PoiAggreQueryManagerException(String message) {
        super(message);
        this.code = 500;
    }

    public PoiAggreQueryManagerException(int code, String message) {
        super(message);
        this.code = code;
    }

    public PoiAggreQueryManagerException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
    }

    public PoiAggreQueryManagerException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
