package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.retry.DefaultRetryProperties;
import com.dianping.rhino.retry.Retry;
import com.dianping.rhino.retry.RetryCallback;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.banma.business.oms.adaptor.LionAdaptor;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.constant.LionConstant;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskStatus;
import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskType;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.LionPartialOperateException;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.config.mcm.ChangeEventPreCheckHandler;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.SyncConfigDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.page.SyncConfigPageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncConfig;
import com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.SyncConfigMapper;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.changelog.McmOperateTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Validator;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 同步配置Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncConfigService implements IMcmHandleService {

    @Resource
    private SyncConfigService self;

    @Resource
    private SyncConfigMapper syncConfigMapper;

    @Resource
    private McmOperateTaskService mcmOperateTaskService;

    @Resource
    private LionAdaptor lionAdaptor;

    @Resource
    private Validator validator;

    private static final Retry BATCH_DELETE_RETRY = Rhino.newRetry(
            LionConstant.SYNC_CONFIG_BATCH_DELETE_RETRY_KEY,
            new DefaultRetryProperties.Setter()
                    .withActive(true)
                    .withDelay(500L)
                    .withMaxAttempts(5)
    );

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class)
    public CommonResult<Boolean> add(SyncConfigDTO syncConfigDto) {
        log.info("add SyncConfig: {}", syncConfigDto);

        SyncConfig syncConfig = new SyncConfig();
        BeanUtil.copyProperties(syncConfigDto, syncConfig);

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        // 设置默认值和操作人信息
        syncConfig.setOpName(user.getName());
        syncConfig.setOpMis(user.getLogin());
        // 配置预处理
        preHandleBeforeDbInsertOrUpdate(syncConfig);

        try {
            int rows = syncConfigMapper.insert(syncConfig);
            if (rows <= 0) {
                return CommonResult.failed("配置插入DB失败");
            }

            // 同步到Lion配置中心
            preHandleBeforeLionSet(syncConfig);

            String lionKey = LionConstant.BM_POI_AGGRE_QUERY_SYNC_CONFIG_LION_KEY_PREFIX + syncConfig.getSyncScene();
            String lionConfigString = lionAdaptor.getLionConfigString(syncConfig);
            boolean lionResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_SYNC_APPKEY, lionKey, lionConfigString, syncConfigDto.getLionConfigDescription());
            if (!lionResult) {
                throw new PoiAggreQueryManagerException("同步Lion配置失败");
            }
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("add SyncConfig failed, syncScene: {}", syncConfig.getSyncScene(), e);
            throw new PoiAggreQueryManagerException("添加同步配置失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class)
    public CommonResult<Boolean> update(SyncConfigDTO syncConfigDto) {
        log.info("update SyncConfig: {}", syncConfigDto);

        SyncConfig syncConfig = new SyncConfig();
        BeanUtil.copyProperties(syncConfigDto, syncConfig);

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        try {
            // 配置预处理
            preHandleBeforeDbInsertOrUpdate(syncConfig);

            // 设置操作人信息
            syncConfig.setOpName(user.getName());
            syncConfig.setOpMis(user.getLogin());

            int rows = syncConfigMapper.update(syncConfig);
            if (rows <= 0) {
                return CommonResult.failed("配置更新DB失败");
            }
            // 同步到Lion配置中心
            preHandleBeforeLionSet(syncConfig);
            String lionKey = LionConstant.BM_POI_AGGRE_QUERY_SYNC_CONFIG_LION_KEY_PREFIX + syncConfig.getSyncScene();
            String lionConfigString = lionAdaptor.getLionConfigString(syncConfig);
            boolean lionResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_SYNC_APPKEY, lionKey, lionConfigString);
            if (!lionResult) {
                throw new PoiAggreQueryManagerException("同步Lion配置失败");
            }
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("update SyncConfig failed, id: {}", syncConfig.getId(), e);
            throw new PoiAggreQueryManagerException("更新同步配置失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class, noRollbackFor = LionPartialOperateException.class)
    public CommonResult<Boolean> batchDeleteByIds(List<Long> ids) {
        log.info("batch delete SyncConfig by ids: {}", ids);
        // 先查询所有记录，获取syncScene列表
        List<SyncConfig> syncConfigList = syncConfigMapper.selectByIds(ids);
        if (syncConfigList.isEmpty()) {
            return CommonResult.failed(400, "记录不存在");
        }
        try {
            int rows = syncConfigMapper.deleteByIds(ids);
            if (rows <= 0) {
                throw new PoiAggreQueryManagerException("批量删除失败");
            }
            // 同步删除Lion配置
            Set<Integer> failedSyncSceneSet = syncConfigList.stream().map(SyncConfig::getSyncScene).collect(Collectors.toSet());
            BATCH_DELETE_RETRY.execute((RetryCallback<Object, Exception>) () -> {
                for (SyncConfig syncConfig : syncConfigList) {
                    if (!failedSyncSceneSet.contains(syncConfig.getSyncScene())) {
                        continue;
                    }
                    String lionKey = LionConstant.BM_POI_AGGRE_QUERY_SYNC_CONFIG_LION_KEY_PREFIX + syncConfig.getSyncScene();
                    boolean lionResult = lionAdaptor.deleteConfig(LionConstant.BM_POI_AGGRE_SYNC_APPKEY, lionKey);
                    if (lionResult) {
                        failedSyncSceneSet.remove(syncConfig.getSyncScene());
                    }
                }
                if (failedSyncSceneSet.size() == syncConfigList.size()) {
                    log.error("批量删除同步配置全部失败");
                    throw new PoiAggreQueryManagerException(500, "批量删除同步配置全部失败, failedSyncSceneList: " + failedSyncSceneSet);
                } else if (!failedSyncSceneSet.isEmpty()) {
                    log.error("批量同步配置失败, 以下场景删除Lion配置失败: {}", failedSyncSceneSet);
                    throw new LionPartialOperateException(500, "批量同步配置失败, 以下场景删除Lion配置失败: " + failedSyncSceneSet);
                }
                return null;
            });
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("batch delete SyncConfig failed", e);
            throw new PoiAggreQueryManagerException("批量删除同步配置失败", e);
        }
    }

    public CommonResult<PageResultDTO<SyncConfig>> page(SyncConfigPageQueryDTO pageQuery) {
        log.info("get SyncConfig page by condition: {}", pageQuery);
        // 计算偏移量
        int offset = (pageQuery.getPageNo() - 1) * pageQuery.getPageSize();
        try {
            // 查询数据
            List<SyncConfig> syncConfigList = syncConfigMapper.selectPage(
                    pageQuery.getSyncTenant(),
                    pageQuery.getSyncScene(),
                    pageQuery.getSyncName(),
                    pageQuery.getSyncType(),
                    pageQuery.getValid(),
                    offset,
                    pageQuery.getPageSize()
            );

            // 查询总数
            int total = syncConfigMapper.selectPageCount(
                    pageQuery.getSyncTenant(),
                    pageQuery.getSyncScene(),
                    pageQuery.getSyncName(),
                    pageQuery.getSyncType(),
                    pageQuery.getValid()
            );

            return CommonResult.success(new PageResultDTO<>(total, syncConfigList));
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("get SyncConfig page failed", e);
            throw new PoiAggreQueryManagerException("分页查询同步配置失败", e);
        }
    }

    @Override
    public boolean preCheck(String requestUri, McmChangeConfigDTO mcmChangeConfigDTO) {
        switch (requestUri) {
            case McmRequest.ADD:
                SyncConfigDTO syncConfigDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), SyncConfigDTO.class, false);
                addPreCheck(syncConfigDTO);
                break;
            case McmRequest.UPDATE:
                SyncConfigDTO syncConfigRequestData = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), SyncConfigDTO.class, false);
                updatePreCheck(syncConfigRequestData);
                break;
            case McmRequest.BATCH_DELETE:
                Object ids = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                if (!(ids instanceof List) || ((List<?>) ids).isEmpty()) {
                    throw new PoiAggreQueryManagerException("ids参数类型错误");
                }
                List<Long> idList = ((List<?>) ids).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());

                batchDeletePreCheck(idList);
                break;
            default:
                return false;
        }
        return true;
    }

    @Override
    @Transactional(value = "TM0", rollbackFor = Exception.class)
    public boolean addOperateTaskAndUpdateRecordStatus2Auditing(String mcmUrl, String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        // 添加任务
        McmOperateTask mcmOperateTask = getAuditingMcmOperateTask(mcmUrl, eventUuid, mcmChangeConfigDTO, McmTaskType.SYNC_CONFIG.getCode());
        boolean addTaskResult = mcmOperateTaskService.addTask(mcmOperateTask);
        if (!addTaskResult) {
            return false;
        }

        String requestUri = mcmChangeConfigDTO.getRequestUri();
        List<Long> ids = ChangeEventPreCheckHandler.getChangeIds(requestUri, mcmChangeConfigDTO);
        if (ids != null) {
            int count = syncConfigMapper.batchUpdateStatus(ids, McmRequest.MCM_STATUS_CODE_AUDITING);
            if (count != ids.size()) {
                throw new PoiAggreQueryManagerException("审核发起成功，更新状态失败");
            }
        }
        return true;
    }

    @Override
    @Transactional(value = "TM0", rollbackFor = Exception.class, noRollbackFor = PoiAggreQueryManagerException.class)
    public void mcmAuditAccepted(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            int count = syncConfigMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
            if (count != changeIdList.size()) {
                throw new PoiAggreQueryManagerException("状态修改失败");
            }
        }
        SyncConfigDTO syncConfigDTO;
        CommonResult<Boolean> res;
        try {
            switch (mcmChangeConfigDTO.getRequestUri()) {
                case McmRequest.ADD:
                    syncConfigDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), SyncConfigDTO.class, false);
                    res = self.add(syncConfigDTO);

                    break;
                case McmRequest.UPDATE:
                    syncConfigDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), SyncConfigDTO.class, false);
                    res = self.update(syncConfigDTO);

                    break;
                case McmRequest.BATCH_DELETE:
                    Object ids = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                    if (!(ids instanceof List) || ((List<?>) ids).isEmpty()) {
                        throw new PoiAggreQueryManagerException("ids参数类型错误");
                    }
                    List<Long> idList = ((List<?>) ids).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());
                    res = self.batchDeleteByIds(idList);

                    break;
                default:
                    throw new PoiAggreQueryManagerException("未知请求类型");
            }
            if (res.getCode() != 0) {
                throw new PoiAggreQueryManagerException("回调处理失败");
            }
        } catch (Exception e) {
            McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.EXCEPTION.getCode());
            mcmOperateTaskService.updateTask(mcmOperateTask);
            throw e;
        }
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.ACCEPTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);
    }


    @Override
    public void mcmAuditRejected(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.REJECTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);

        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            syncConfigMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
        }
    }

    @Override
    public void mcmAuditCancel(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.CANCELED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);

        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            syncConfigMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
        }
    }

    public void addPreCheck(SyncConfigDTO syncConfigDTO) {
        BindingResult bindingResult;
        bindingResult = new BeanPropertyBindingResult(syncConfigDTO, "syncConfigDTO");
        validator.validate(syncConfigDTO, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new PoiAggreQueryManagerException(400, "参数校验失败");
        }

        // 校验syncScene和syncName是否重复
        SyncConfig existingConfig = syncConfigMapper.selectBySyncSceneOrName(syncConfigDTO.getSyncScene(), syncConfigDTO.getSyncName());
        if (existingConfig != null) {
            log.error("添加同步配置失败, syncScene: {}, syncName: {}", syncConfigDTO.getSyncScene(), syncConfigDTO.getSyncName());
            throw new PoiAggreQueryManagerException(400, "同步场景和名称组合已存在");
        }
    }

    public void updatePreCheck(SyncConfigDTO syncConfigRequestData) {
        BindingResult bindingResult;
        if (syncConfigRequestData.getId() == null) {
            throw new PoiAggreQueryManagerException(400, "id不能为空");
        }
        bindingResult = new BeanPropertyBindingResult(syncConfigRequestData, "syncConfigRequestData");
        validator.validate(syncConfigRequestData,bindingResult);
        if (bindingResult.hasErrors()) {
            throw new PoiAggreQueryManagerException(400, "参数校验失败");
        }
        // 校验syncScene否重复
        SyncConfig otherRecord = syncConfigMapper.selectBySyncSceneOrName(syncConfigRequestData.getSyncScene(), syncConfigRequestData.getSyncName());
        SyncConfig syncConfig = syncConfigMapper.selectById(syncConfigRequestData.getId());
        if (syncConfig == null) {
            throw new PoiAggreQueryManagerException(400, "请求参数异常，id不存在");
        } else if (syncConfig.getStatus() == 1) {
            throw new PoiAggreQueryManagerException(400, "数据正在审核中，禁止操作！");
        } else if (otherRecord != null && !Objects.equals(syncConfig.getId(), otherRecord.getId())) {
            log.error("更新同步配置失败, syncScene: {}, syncName: {}", syncConfig.getSyncScene(), syncConfig.getSyncName());
            throw new PoiAggreQueryManagerException(400, "同步场景和名称组合已存在");
        }
    }

    public void batchDeletePreCheck(List<Long> idList) {
        // 先查询所有记录，获取syncScene列表
        List<SyncConfig> syncConfigList = syncConfigMapper.selectByIds(idList);
        if (syncConfigList.size() != idList.size()) {
            throw new PoiAggreQueryManagerException(400, "不能删除不存在的数据！");
        }
        syncConfigList.forEach(config -> {
            if (config.getStatus() == 1) {
                throw new PoiAggreQueryManagerException(400, "有数据正在审核中，请重新选择！");
            }
        });
    }

    private void preHandleBeforeDbInsertOrUpdate(SyncConfig syncConfig) {
        if (syncConfig.getType() == 1) {
            // Mafka
            syncConfig.setDtsSyncConfig("");
            if (!syncConfig.getIsPoiOuter()) {
                syncConfig.setTotalPoiConsistencyCheck("");
            }
        } else if (syncConfig.getType() == 2) {
            // DTS
            syncConfig.setIsPoiOuter(false);
            syncConfig.setTotalPoiConsistencyCheck("");
            syncConfig.setMafkaConsumeConfig("");
            syncConfig.setQueryDataConfig("");
            syncConfig.setQueryChangeIdsConfig("");
        }
    }

    private void preHandleBeforeLionSet(SyncConfig syncConfig) {
        if (syncConfig.getType() == 1) {
            // Mafka
            syncConfig.setDtsSyncConfig(null);
            if (!syncConfig.getIsPoiOuter()) {
                syncConfig.setTotalPoiConsistencyCheck(null);
            }
            if (StrUtil.isBlank(syncConfig.getTotalPoiConsistencyCheck())) {
                syncConfig.setTotalPoiConsistencyCheck(null);
            }
            if (StrUtil.isBlank(syncConfig.getQueryChangeIdsConfig())) {
                syncConfig.setQueryChangeIdsConfig(null);
            }
        } else if (syncConfig.getType() == 2) {
            // DTS
            syncConfig.setIsPoiOuter(null);
            syncConfig.setTotalPoiConsistencyCheck(null);
            syncConfig.setMafkaConsumeConfig(null);
            syncConfig.setQueryDataConfig(null);
            syncConfig.setQueryChangeIdsConfig(null);
        }
        syncConfig.setId(null);
        syncConfig.setValid(null);
    }
}