package com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.meituan.banma.business.oms.common.handler.FieldCodesSerializeHandler;
import com.sankuai.meituan.banma.business.oms.common.handler.JsonStringSerializeHandler;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * DSL配置DTO
 * <AUTHOR>
 */
@Data
public class DSLConfigDTO {

    /**
     * 主键ID
     */
    @JSONField(serialize = false)
    private Long id;

    /**
     * 编排名称
     */
    @JSONField(ordinal = 1)
    @NotBlank(message = "编排名称不能为空")
    private String dslName;

    /**
     * 编排描述
     */
    @JSONField(ordinal = 2)
    @NotBlank(message = "编排描述不能为空")
    private String dslDescription;

    /**
     * 编排DSL
     */
    @JSONField(ordinal = 3, serializeUsing = JsonStringSerializeHandler.class)
    @NotBlank(message = "编排DSL不能为空")
    private String dsl;

    /**
     * 编排字段
     */
    @JSONField(ordinal = 4, serializeUsing = FieldCodesSerializeHandler.class)
    @NotEmpty(message = "编排字段不能为空")
    private List<String> fieldCodes;

    /**
     * 是否抛出异常
     */
    @JSONField(ordinal = 5)
    @NotNull(message = "是否抛出异常不能为空")
    private Boolean thrownException;

    /**
     * 预热参数
     */
    @JSONField(ordinal = 6)
    private String dslPreHeatParam;

    /**
     * 其他参数（后续新增）
     */
    @JSONField(ordinal = 7)
    private Map<String, Object> option;

    /**
     * 支持QPS
     */
    @JSONField(ordinal = 8)
    private Integer supportQps;

    /**
     * 实际QPS
     */
    @JSONField(ordinal = 9)
    private Integer actualQps;

    /**
     * 有效性 0:无效;1:有效
     */
    @JSONField(serialize = false)
    @NotNull(message = "有效性不能为空")
    private Integer valid;

    /**
     * Lion配置描述
     */
    @JSONField(ordinal = 7)
    private String lionConfigDescription;
} 