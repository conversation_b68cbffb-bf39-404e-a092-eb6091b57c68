package com.sankuai.meituan.banma.business.oms.controller;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.service.DemoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Demo功能控制器
 */
@RestController
@RequestMapping("/demo")
public class DemoController {

    @Autowired
    private DemoService demoService;

    /**
     * 分页查询场景列表
     * @param params 查询参数
     * @return 分页结果
     */
    @PostMapping("/scene/page")
    public Object queryScenePage(@RequestBody Map<String, Object> params) {
        String sceneName = params.get("sceneName") != null ? params.get("sceneName").toString() : null;
        Integer sceneLevel = params.get("sceneLevel") != null ? Integer.parseInt(params.get("sceneLevel").toString()) : null;
        Integer valid = params.get("valid") != null ? Integer.parseInt(params.get("valid").toString()) : null;
        int pageNum = params.get("pageNum") != null ? Integer.parseInt(params.get("pageNum").toString()) : 1;
        int pageSize = params.get("pageSize") != null ? Integer.parseInt(params.get("pageSize").toString()) : 10;
        
        return CommonResult.success(demoService.queryScenePage(sceneName, sceneLevel, valid, pageNum, pageSize));
    }

    /**
     * 查询场景详情
     * @param params 查询参数
     * @return 场景详情
     */
    @PostMapping("/scene/detail")
    public Object querySceneDetail(@RequestBody Map<String, Object> params) {
        if (params.get("id") == null) {
            return CommonResult.failed("ID不能为空");
        }
        
        Long id = Long.parseLong(params.get("id").toString());
        return CommonResult.success(demoService.querySceneDetail(id));
    }
} 