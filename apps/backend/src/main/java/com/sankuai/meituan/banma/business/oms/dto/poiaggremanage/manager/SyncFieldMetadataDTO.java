package com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 聚合查询字段元数据
 * <AUTHOR>
 */
@Data
public class SyncFieldMetadataDTO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 字段code
     */
    @NotBlank(message = "字段code不能为空")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "字段Code只能包含字母、数字和下划线，且必须以字母开头")
    private String fieldCode;

    /**
     * 字段属性
     */
    @NotBlank(message = "字段属性不能为空")
    private String fieldProperty;

    /**
     * 字段名称
     */
    @NotBlank(message = "字段名称不能为空")
    private String fieldName;

    /**
     * 字段描述
     */

    private String description;

    /**
     * 字段类型，1-String，2-Boolean，3-Long，4-Double
     */
    @NotBlank(message = "字段类型不能为空")
    private String type;

    /**
     * 默认值
     */
    @NotBlank(message = "默认值不能为空")
    private String defaultValue;

    /**
     * 有效性 0:无效;1:有效
     */
    private Integer valid;

    /**
     * 操作人姓名
     */
    private String opName;

    /**
     * 操作人mis
     */
    private String opMis;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 更新时间
     */
    private Integer utime;

    /**
     * 同步到查询字段
     */
    @NotBlank(message = "是否同步到查询字段不能为空")
    private Boolean sync2QueryField;

    /**
     * 依赖字段
     */
    private List<String> dependentFields;

    /**
     * lion配置描述
     */
    private String lionConfigDescription;
} 