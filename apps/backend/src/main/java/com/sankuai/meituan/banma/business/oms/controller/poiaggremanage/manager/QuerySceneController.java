package com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.manager;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.page.QueryScenePageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryScene;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager.QuerySceneService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 查询场景控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/poiAggreManage/manager/query/scene")
@Validated
public class QuerySceneController {

    @Resource
    private QuerySceneService querySceneService;

    /**
     * 新增查询场景
     *
     * @param scene 场景信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public CommonResult<Boolean> addScene(@RequestBody @Valid QueryScene scene) {
        log.info("新增场景请求: {}", scene);
        return querySceneService.addScene(scene);
    }

    /**
     * 更新查询场景
     *
     * @param scene 场景信息
     * @return 操作结果
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateScene(@RequestBody @Valid QueryScene scene) {
        log.info("更新场景请求: {}", scene);
        return querySceneService.updateScene(scene);
    }

    /**
     * 批量删除查询场景
     *
     * @param ids 场景ID列表
     * @return 操作结果
     */
    @PostMapping("/deleteByIds")
    public CommonResult<Boolean> deleteSceneByIds(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除场景请求, ids: {}", ids);
        return querySceneService.deleteSceneByIds(ids);
    }

    /**
     * 分页查询场景列表
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    @PostMapping("/page")
    public CommonResult<PageResultDTO<QueryScene>> queryScenePage(@RequestBody @Valid QueryScenePageQueryDTO queryDTO) {
        log.info("查询场景列表请求: {}", queryDTO);
        return querySceneService.queryScenePage(queryDTO);
    }
} 