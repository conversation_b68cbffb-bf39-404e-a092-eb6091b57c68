package com.sankuai.meituan.banma.business.oms.config.mcm;

import com.sankuai.mcm.client.sdk.config.McmFilterFactoryBean;
import com.sankuai.mcm.client.sdk.config.annotation.EnableMcm;
import com.sankuai.mcm.client.sdk.config.annotation.McmConfigurerAdaptor;
import com.sankuai.mcm.client.sdk.config.annotation.registry.AuditCallbackHandlerRegistry;
import com.sankuai.mcm.client.sdk.config.annotation.registry.CustomConfigProviderRegistry;
import com.sankuai.mcm.client.sdk.config.annotation.registry.EventContextPropertyProviderRegistry;
import com.sankuai.mcm.client.sdk.config.annotation.registry.PreCheckHandlerRegistry;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager.*;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.DispatcherType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@EnableMcm
public class McmConfig extends McmConfigurerAdaptor {

    @Resource
    private QueryFieldMetadataService queryFieldMetadataService;

    @Resource
    private DSLConfigService dslConfigService;

    @Resource
    private SyncFieldMetadataService syncFieldMetadataService;

    @Resource
    private SyncConfigService syncConfigService;

    @Resource
    private AccessGuideService accessGuideService;

    public static final String POI_APPGRE_MANAGE_CHANGE_EVENT_NAME = "BmPoiAggreConfigChange";

    public static final Map<String, IMcmHandleService> POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP.put(McmRequest.POI_AGGRE_MANAGE_QUERY_FIELD_BASE_URL, queryFieldMetadataService);
        POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP.put(McmRequest.POI_AGGRE_MANAGE_QUERY_DSL_BASE_URL, dslConfigService);
        POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP.put(McmRequest.POI_AGGRE_MANAGE_SYNC_FIELD_BASE_URL, syncFieldMetadataService);
        POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP.put(McmRequest.POI_AGGRE_MANAGE_SYNC_CONFIG_BASE_URL, syncConfigService);
        POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP.put(McmRequest.POI_AGGRE_MANAGE_ACCESS_GUIDE_BASE_URL, accessGuideService);
    }

    @Bean
    public McmFilterFactoryBean mcmFilterBean() {
        return new McmFilterFactoryBean();
    }

    @Bean
    public FilterRegistrationBean mcmFilter() {
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        filter.setTargetBeanName("mcmFilterBean");
        filter.setTargetFilterLifecycle(true);

        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(filter);
        registration.addUrlPatterns(
                // 管控的接口
                "/poiAggreManage/*");
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("mcmFilterBean");
        registration.setOrder(Integer.MAX_VALUE);

        return registration;
    }

    @Resource
    private ChangeEventConfigProvider changeEventConfigProvider;

    @Resource
    private ChangeEventContextPropertyProvider changeEventContextPropertyProvider;

    @Resource
    private ChangeEventAuditCallbackHandler changeEventAuditCallbackHandler;

    @Resource
    private ChangeEventPreCheckHandler changeEventPreCheckHandler;

    /**
     * 添加预检处理器
     */
    @Override
    public void addPreCheckHandler(PreCheckHandlerRegistry registry) {
        registry.addHandler(changeEventPreCheckHandler);
    }

    /**
     * 添加自定义配置提供器（变更描述）
     */
    @Override
    public void addCustomConfigProvider(CustomConfigProviderRegistry registry) {
        registry.addCustomConfigProvider(changeEventConfigProvider);
    }

    /**
     * 添加事件上下文属性提供器（请求参数、变更前后信息、操作人）
     */
    @Override
    public void addEventContextPropertyProvider(EventContextPropertyProviderRegistry registry) {
        registry.addPropertyProvider(changeEventContextPropertyProvider);
    }

    /**
     * 添加审核回调处理器，可以配置多个
     */
    @Override
    public void addAuditCallbackHandler(AuditCallbackHandlerRegistry registry) {
        registry.addHandlers(changeEventAuditCallbackHandler);
    }

}
