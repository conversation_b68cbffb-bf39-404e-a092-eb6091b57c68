package com.sankuai.meituan.banma.business.oms.controller;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/system/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户的菜单树
     * @return 用户菜单树
     */
    @GetMapping("/menu/tree")
    public Object getMenuTree() {
        try {
            return CommonResult.success(userService.getMenuTree());
        } catch (Exception e) {
            return CommonResult.failed("获取用户菜单树失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     * @return 用户信息
     */
    @GetMapping("/current")
    public Object getCurrentUser() {
        try {
            return CommonResult.success(userService.getCurrentUserInfo());
        } catch (Exception e) {
            return CommonResult.failed("获取当前用户信息失败：" + e.getMessage());
        }
    }
} 