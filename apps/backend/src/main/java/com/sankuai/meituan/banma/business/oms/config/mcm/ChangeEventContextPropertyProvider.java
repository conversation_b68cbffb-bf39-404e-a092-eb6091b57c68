package com.sankuai.meituan.banma.business.oms.config.mcm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.eventcontext.EventContextPropertyProviderRequest;
import com.sankuai.meituan.banma.business.oms.adaptor.LionAdaptor;
import com.sankuai.meituan.banma.business.oms.common.constant.PoiAggreJsonKey;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.common.util.McmRequestDataUtil;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.DSLConfig;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncConfig;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 默认事件上下文属性提供器
 *
 * <AUTHOR>
 */
@Slf4j
@McmComponent
public class ChangeEventContextPropertyProvider extends EventContextPropertyProviderAdaptor {

    @Resource
    private LionAdaptor lionAdaptor;

    /**
     * 请求参数
     */
    @Override
    public Map<String, Object> getRequestParameters(EventContextPropertyProviderRequest request) {
        return BeanUtil.beanToMap(request.getArgs()[0]);
    }

    /**
     * diff的变更前信息
     */
    @Override
    public Object getBefore(EventContextPropertyProviderRequest request) {
        McmChangeConfigDTO mcmChangeConfigDTO = (McmChangeConfigDTO) request.getArgs()[0];
        return jsonConfigInfoHandle(mcmChangeConfigDTO.getChangeBefore());
    }

    /**
     * diff的变更后信息
     */
    @Override
    public Object getAfter(EventContextPropertyProviderRequest request) {
        McmChangeConfigDTO mcmChangeConfigDTO = (McmChangeConfigDTO) request.getArgs()[0];
        return jsonConfigInfoHandle(mcmChangeConfigDTO.getChangeAfter());
    }

    @Override
    public String getEventUuid(EventContextPropertyProviderRequest request) {
        return UUID.randomUUID().toString();
    }

    private Object jsonConfigInfoHandle(Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        Object config;
        if (map.containsKey(PoiAggreJsonKey.MAFKA_CONSUME_CONFIG) || map.containsKey(PoiAggreJsonKey.DTS_SYNC_CONFIG)) {
            config = McmRequestDataUtil.getConfig(map, SyncConfig.class);
        } else if (map.containsKey(PoiAggreJsonKey.DSL)) {
            config = McmRequestDataUtil.getConfig(map, DSLConfig.class);
        } else if (map.containsKey(PoiAggreJsonKey.ACCESS_WAY)) {
            config = McmRequestDataUtil.getAccessGuideDataFromMcmRequestMap(map);
        } else if (map.size() == 1) {
            // 批量删除和批量新增
            Object value = map.values().iterator().next();
            if (!(value instanceof List)) {
                throw new PoiAggreQueryManagerException("批量操作请求参数异常");
            }
            List<?> list = (List<?>) value;
            List<Map<String, Object>> mapList = list.stream()
                    .map(obj -> {
                        if (obj instanceof Map) {
                            return (Map<String, Object>) obj;
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            List<Object> result = new ArrayList<>();
            for (Map<String, Object> linkedMap : mapList) {
                if (linkedMap.containsKey(PoiAggreJsonKey.MAFKA_CONSUME_CONFIG) || linkedMap.containsKey(PoiAggreJsonKey.DTS_SYNC_CONFIG)) {
                    config = McmRequestDataUtil.getConfig(linkedMap, SyncConfig.class);
                } else if (linkedMap.containsKey(PoiAggreJsonKey.DSL)) {
                    config = McmRequestDataUtil.getConfig(linkedMap, DSLConfig.class);
                } else {
                    config = linkedMap;
                }
                result.add(config);
            }
            return lionAdaptor.getLionConfigString(result);
        } else {
            return map;
        }
        return lionAdaptor.getLionConfigString(config);
    }
}
