package com.sankuai.meituan.banma.business.oms.adaptor;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools.RocketHost;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools.RocketHostData;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools.RocketHostResponse;
import com.sankuai.oceanus.http.client.apache.OceanusHttpProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class RocketHostAdaptor {

    // 请求机器资源信息的 KMS Url
    private static final String host = "https://rocket.sankuai.com/instance";

    private static final String path = "/api/v1/hosts_by_appkey";

    // 强烈建议使用方通过 `fields` 字段只返回必要的字段列表
    private static final String fields = "ipLan,name,idc,cell,swimlane,env,status";

    // 修改URL模板，将offset和limit作为可替换的参数
    private static final String urlTemplate = host + path + "?appkey={appkey}&offset={offset}&limit={limit}&fields=" + fields;

    private static final String rocketAuthAppkey = "com.sankuai.rocket.host.instance";

    private static final int DEFAULT_LIMIT = 100;

    private static final HttpClient httpClient = createHttpClient();

    public static List<RocketHost> getAppkeyHosts(String appkey, String env) {
        // 创建一个结果对象，用于存储所有分页数据
        RocketHostData finalResult = new RocketHostData();
        List<RocketHost> allHosts = new ArrayList<>();
        finalResult.setData(allHosts);

        int offset = 0;
        int limit = DEFAULT_LIMIT;
        int total = 0;
        boolean hasMoreData = true;

        // 循环获取所有数据
        while (hasMoreData) {
            // 构建当前分页的URL
            String currentUrl = urlTemplate
                    .replace("{appkey}", appkey)
                    .replace("{offset}", String.valueOf(offset))
                    .replace("{limit}", String.valueOf(limit));

            // 获取当前分页的数据
            RocketHostData pageData = fetchPageData(currentUrl);

            // 如果获取失败，则退出循环
            if (pageData == null) {
                log.error("Failed to fetch data at offset: {}", offset);
                break;
            }

            // 更新总数
            total = pageData.getTotal();

            // 合并数据
            if (pageData.getData() != null && !pageData.getData().isEmpty()) {
                if (StringUtils.isNotBlank(env)) {
                    allHosts.addAll(pageData.getData().stream().filter(host -> env.equals(host.getEnv())).collect(Collectors.toSet()));
                } else {
                    allHosts.addAll(pageData.getData());
                }
                log.info("Fetched {} hosts at offset {}, total collected: {}/{}", pageData.getData().size(), offset, allHosts.size(), total);
            }

            // 更新offset，准备获取下一页
            offset += limit;

            // 判断是否还有更多数据
            // 当已获取的数据数量大于等于total，或者当前页返回的数据数量小于limit时，表示已获取完所有数据
            hasMoreData = allHosts.size() < total && pageData.getData() != null && pageData.getData().size() == limit;
        }

        return allHosts;
    }

    /**
     * 获取单页数据
     * @param url 请求URL
     * @return 单页数据
     */
    private static RocketHostData fetchPageData(String url) {
        HttpGet httpGet = new HttpGet(url);
        InputStream inputStream = null;

        try {
            // 发送请求并获取响应
            HttpResponse response = httpClient.execute(httpGet);

            // 解析响应内容
            HttpEntity entity = response.getEntity();
            inputStream = entity.getContent();
            String responseStr = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            log.info("Rocket API response for URL {}: {}", url, responseStr);

            // 解析JSON数据
            RocketHostResponse result = JSONObject.parseObject(responseStr, RocketHostResponse.class);
            return result.getData();
        } catch (ClientProtocolException e) {
            log.error("HTTP协议异常", e);
        } catch (IOException e) {
            log.error("IO异常", e);
        } finally {
            // 确保资源正确关闭
            IOUtils.closeQuietly(inputStream);
        }

        return null;
    }

    public static HttpClient createHttpClient(){
        // 1. 创建OceanusHttpProcessor 注意设置服务端appkey
        OceanusHttpProcessor oceanusHttpProcessor = null;
        try{
            oceanusHttpProcessor = new OceanusHttpProcessor(rocketAuthAppkey); //com.sankuai.XXX是服务端appkey，也就是你想调用的服务的appkey
        } catch (Exception e){
            throw new RuntimeException(e);
        }

        // 2. 将OceanHttpProcessor设置到创建的httpclient中
        HttpClientBuilder httpClientBuilder = HttpClients.custom();
        httpClientBuilder.addInterceptorFirst((HttpRequestInterceptor) oceanusHttpProcessor);
        HttpClient httpClient = httpClientBuilder.build();

        return httpClient;
    }
}
