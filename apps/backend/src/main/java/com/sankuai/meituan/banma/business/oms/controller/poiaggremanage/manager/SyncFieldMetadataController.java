package com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.manager;

import com.sankuai.mcm.client.sdk.config.annotation.McmHandler;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.config.mcm.McmConfig;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.page.FieldCodePageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncFieldMetadata;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager.SyncFieldMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 聚合查询字段元数据Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/poiAggreManage/manager/sync/field/metadata")
@Validated
public class SyncFieldMetadataController {

    @Resource
    private SyncFieldMetadataService syncFieldMetadataService;

    /**
     * 添加字段元数据
     * @param mcmChangeConfigDTO 变更配置DTO
     * @return 操作结果
     */
    @McmHandler(eventName = McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME)
    @PostMapping(McmRequest.ADD)
    public String add(@RequestBody McmChangeConfigDTO mcmChangeConfigDTO){
        return mcmChangeConfigDTO.toString();
    }

    /**
     * 批量添加字段元数据
     * @param mcmChangeConfigDTO 变更配置DTO
     * @return 添加失败的列表
     */
    @McmHandler(eventName = McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME)
    @PostMapping(McmRequest.BATCH_ADD)
    public String batchAdd(@RequestBody McmChangeConfigDTO mcmChangeConfigDTO) {
        return mcmChangeConfigDTO.toString();
    }

    /**
     * 更新字段元数据
     * @param mcmChangeConfigDTO 变更配置DTO
     * @return 操作结果
     */
    @McmHandler(eventName = McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME)
    @PostMapping(McmRequest.UPDATE)
    public String update(@RequestBody McmChangeConfigDTO mcmChangeConfigDTO) {
        return mcmChangeConfigDTO.toString();
    }

    /**
     * 批量删除字段元数据
     * @param mcmChangeConfigDTO 变更配置DTO
     * @return 操作结果
     */
    @McmHandler(eventName = McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME)
    @PostMapping(McmRequest.BATCH_DELETE)
    public String batchDelete(@RequestBody McmChangeConfigDTO mcmChangeConfigDTO) {
        return mcmChangeConfigDTO.toString();
    }

    /**
     * 分页查询字段元数据
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    @PostMapping("/page")
    public CommonResult<PageResultDTO<SyncFieldMetadata>> getPage(@RequestBody @Valid FieldCodePageQueryDTO pageQuery) {
        log.info("分页查询字段元数据请求: {}", pageQuery);
        return syncFieldMetadataService.getPage(pageQuery);
    }
}
