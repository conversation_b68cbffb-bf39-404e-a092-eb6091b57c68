package com.sankuai.meituan.banma.business.oms.config.mcm;

import com.google.common.collect.Lists;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.handler.PreCheckHandler;
import com.sankuai.mcm.client.sdk.context.handler.PreCheckHandlerItem;
import com.sankuai.mcm.client.sdk.context.handler.PreCheckHandlerResult;
import com.sankuai.mcm.client.sdk.context.handler.PreCheckRequest;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自定义预检（审核发起前）
 *
 * <AUTHOR>
 */
@McmComponent(events = {McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME})
public class ChangeEventPreCheckHandler implements PreCheckHandler {

    public static final String PRE_CHECK_NOT_PASS_MESSAGE = "预检未通过";

    @Resource
    private QueryFieldMetadataService queryFieldMetadataService;

    @Resource
    private DSLConfigService dslConfigService;

    @Resource
    private SyncFieldMetadataService syncFieldMetadataService;

    @Resource
    private SyncConfigService syncConfigService;

    /**
     * 自定义预检
     */
    @Override
    public PreCheckHandlerResult preCheck(PreCheckRequest request) {
        // 预检指标项
        PreCheckHandlerItem preCheckHandlerItem = new PreCheckHandlerItem();
        preCheckHandlerItem.setDescription("自定义预检拦截");

        PreCheckHandlerResult result = new PreCheckHandlerResult();

        // 校验请求参数是否合法
        // 获取请求参数
        McmChangeConfigDTO mcmChangeConfigDTO = (McmChangeConfigDTO) request.getArgs()[0];
        String requestBaseUrl = mcmChangeConfigDTO.getRequestBaseUrl();
        String requestUri = mcmChangeConfigDTO.getRequestUri();
        Map<String, IMcmHandleService> routeMap = McmConfig.POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP;
        if (!routeMap.containsKey(requestBaseUrl)) {
            throw new PoiAggreQueryManagerException("请求baseUrl不存在");
        }
        IMcmHandleService mcmHandleService = routeMap.get(requestBaseUrl);
        boolean preCheckResult = mcmHandleService.preCheck(requestUri, mcmChangeConfigDTO);
        if (!preCheckResult) {
            // 未通过预检
            throw new PoiAggreQueryManagerException(PRE_CHECK_NOT_PASS_MESSAGE);
        }
        // 是否跳过
        result.setShouldPass(true);
        result.setItems(Lists.newArrayList(preCheckHandlerItem));

        return result;
    }

    public static List<Long> getChangeIds(String requestUri, McmChangeConfigDTO mcmChangeConfigDTO) {
        List<Long> ids = null;
        switch (requestUri) {
            case McmRequest.ADD:
                break;
            case McmRequest.BATCH_ADD:
                break;
            case McmRequest.UPDATE:
                ids = new ArrayList<>();
                Long id = Long.parseLong(mcmChangeConfigDTO.getRequestData().get("id").toString());
                ids.add(id);
                break;
            case McmRequest.BATCH_DELETE:
                Object requestData = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                ids = ((List<?>) requestData).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());
                break;
            default:
                return null;
        }
        return ids;
    }
}