package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.page.QueryScenePageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryScene;
import com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.QuerySceneMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 查询场景服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class QuerySceneService {

    @Resource
    private QuerySceneMapper querySceneMapper;

    @Transactional(value = "TM0", rollbackFor = Exception.class)
    public CommonResult<Boolean> addScene(QueryScene scene) {
        log.info("新增场景: {}", scene);

        // 根据sceneName校验是否重复
        QueryScene existingScene = querySceneMapper.selectBySceneName(scene.getSceneName());
        if (existingScene != null) {
            log.error("新增场景失败，场景名称已存在: {}", scene.getSceneName());
            return CommonResult.failed("场景名称已存在");
        }

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        // 设置opName、opMis
        scene.setOpName(user.getName());
        scene.setOpMis(user.getLogin());

        // 新增场景时，如果actualQps为null，设置为0
        if (scene.getActualQps() == null) {
            scene.setActualQps(0);
            log.info("新增场景时actualQps为null，设置默认值为0");
        }

        try {
            int nextSceneCode = querySceneMapper.getNextSceneCode();
            scene.setSceneCode(nextSceneCode);

            boolean success = querySceneMapper.insert(scene) > 0;
            if (success) {
                log.info("新增场景成功，sceneCode: {}", scene.getSceneCode());
                return CommonResult.success(true);
            } else {
                return CommonResult.failed("新增场景失败");
            }
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增场景失败, scene: {}", scene, e);
            throw new PoiAggreQueryManagerException("新增场景失败");
        }
    }

    public CommonResult<Boolean> updateScene(QueryScene scene) {
        log.info("更新场景: {}", scene);

        // 根据sceneName校验是否重复
        QueryScene existingScene = querySceneMapper.selectBySceneName(scene.getSceneName());
        if (existingScene != null && !Objects.equals(existingScene.getId(), scene.getId())) {
            log.error("更新场景失败，场景名称已存在: {}", scene.getSceneName());
            return CommonResult.failed("场景名称已存在");
        }

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        // 设置opName、opMis
        scene.setOpName(user.getName());
        scene.setOpMis(user.getLogin());
        try {
            boolean success = querySceneMapper.update(scene) > 0;
            if (success) {
                log.info("更新场景成功，id: {}", scene.getId());
                return CommonResult.success(true);
            } else {
                return CommonResult.failed("更新场景失败");
            }
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新场景失败, scene: {}", scene, e);
            return CommonResult.failed("更新场景失败");
        }
    }

    public CommonResult<Boolean> deleteSceneByIds(List<Long> ids) {
        log.info("批量删除场景，ids: {}", ids);
        try {
            boolean success = querySceneMapper.deleteByIds(ids) > 0;
            if (success) {
                log.info("批量删除场景成功，ids: {}", ids);
                return CommonResult.success(true);
            } else {
                return CommonResult.failed("删除场景失败");
            }
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("批量删除场景失败, ids: {}", ids, e);
            return CommonResult.failed("批量删除场景失败");
        }
    }

    public CommonResult<PageResultDTO<QueryScene>> queryScenePage(QueryScenePageQueryDTO pageQuery) {
        log.info("Get query scene page by condition: {}", pageQuery);
        // 计算偏移量
        int offset = (pageQuery.getPageNo() - 1) * pageQuery.getPageSize();
        try {
            List<QueryScene> querySceneList = querySceneMapper.queryScenePage(
                    pageQuery.getSceneCode(),
                    pageQuery.getSceneName(),
                    pageQuery.getSceneLevel(),
                    pageQuery.getValid(),
                    offset,
                    pageQuery.getPageSize()
            );
            int total = querySceneMapper.countScene(
                    pageQuery.getSceneCode(),
                    pageQuery.getSceneName(),
                    pageQuery.getSceneLevel(),
                    pageQuery.getValid()
            );

            PageResultDTO<QueryScene> pageResult = new PageResultDTO<>(total, querySceneList);
            return CommonResult.success(pageResult);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询场景列表失败, params: {}", pageQuery, e);
            return CommonResult.failed("查询场景列表失败");
        }
    }
} 