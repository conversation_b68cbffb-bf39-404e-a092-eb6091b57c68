package com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools;

import java.util.List;
import java.util.Map;

/**
 * 返回类型结构DTO
 * 用于前端展示返回结果结构
 * <AUTHOR>
 */
public class ReturnTypeDTO {
    
    /**
     * 返回类型的完整名称
     */
    private String typeName;
    
    /**
     * 返回类型的简化名称（去掉包名）
     */
    private String simpleTypeName;
    
    /**
     * 返回类型的描述
     */
    private String description;
    
    /**
     * 是否为基本类型
     */
    private Boolean isPrimitive;
    
    /**
     * 是否为集合类型
     */
    private Boolean isCollection;
    
    /**
     * 是否为枚举类型
     */
    private Boolean isEnum;
    
    /**
     * 枚举值（如果是枚举类型）
     */
    private List<String> enumValues;
    
    /**
     * 对象字段（如果是复杂对象）
     */
    private Map<String, FieldInfo> fields;
    
    /**
     * 集合元素类型（如果是集合类型）
     */
    private String elementType;
    
    /**
     * 示例值
     */
    private Object exampleValue;
    
    /**
     * 字段信息
     */
    public static class FieldInfo {
        /**
         * 字段类型
         */
        private String type;
        
        /**
         * 字段简化类型名
         */
        private String simpleType;
        
        /**
         * 字段描述
         */
        private String description;
        
        /**
         * 是否为必需字段
         */
        private Boolean required;
        
        /**
         * 是否为基本类型
         */
        private Boolean isPrimitive;
        
        /**
         * 是否为集合类型
         */
        private Boolean isCollection;
        
        /**
         * 集合元素类型（如果是集合）
         */
        private String elementType;
        
        /**
         * 嵌套字段信息（用于对象类型和集合元素类型）
         */
        private Map<String, FieldInfo> nestedFields;
        
        /**
         * 示例值
         */
        private Object exampleValue;
        
        // Getters and Setters
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public String getSimpleType() {
            return simpleType;
        }
        
        public void setSimpleType(String simpleType) {
            this.simpleType = simpleType;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public Boolean getRequired() {
            return required;
        }
        
        public void setRequired(Boolean required) {
            this.required = required;
        }
        
        public Boolean getIsPrimitive() {
            return isPrimitive;
        }
        
        public void setIsPrimitive(Boolean isPrimitive) {
            this.isPrimitive = isPrimitive;
        }
        
        public Boolean getIsCollection() {
            return isCollection;
        }
        
        public void setIsCollection(Boolean isCollection) {
            this.isCollection = isCollection;
        }
        
        public String getElementType() {
            return elementType;
        }
        
        public void setElementType(String elementType) {
            this.elementType = elementType;
        }
        
        public Map<String, FieldInfo> getNestedFields() {
            return nestedFields;
        }
        
        public void setNestedFields(Map<String, FieldInfo> nestedFields) {
            this.nestedFields = nestedFields;
        }
        
        public Object getExampleValue() {
            return exampleValue;
        }
        
        public void setExampleValue(Object exampleValue) {
            this.exampleValue = exampleValue;
        }
    }
    
    // Getters and Setters
    public String getTypeName() {
        return typeName;
    }
    
    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
    
    public String getSimpleTypeName() {
        return simpleTypeName;
    }
    
    public void setSimpleTypeName(String simpleTypeName) {
        this.simpleTypeName = simpleTypeName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsPrimitive() {
        return isPrimitive;
    }
    
    public void setIsPrimitive(Boolean isPrimitive) {
        this.isPrimitive = isPrimitive;
    }
    
    public Boolean getIsCollection() {
        return isCollection;
    }
    
    public void setIsCollection(Boolean isCollection) {
        this.isCollection = isCollection;
    }
    
    public Boolean getIsEnum() {
        return isEnum;
    }
    
    public void setIsEnum(Boolean isEnum) {
        this.isEnum = isEnum;
    }
    
    public List<String> getEnumValues() {
        return enumValues;
    }
    
    public void setEnumValues(List<String> enumValues) {
        this.enumValues = enumValues;
    }
    
    public Map<String, FieldInfo> getFields() {
        return fields;
    }
    
    public void setFields(Map<String, FieldInfo> fields) {
        this.fields = fields;
    }
    
    public String getElementType() {
        return elementType;
    }
    
    public void setElementType(String elementType) {
        this.elementType = elementType;
    }
    
    public Object getExampleValue() {
        return exampleValue;
    }
    
    public void setExampleValue(Object exampleValue) {
        this.exampleValue = exampleValue;
    }
} 