package com.sankuai.meituan.banma.business.oms.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.meituan.banma.business.oms.common.constant.AccessWayEnum;
import com.sankuai.meituan.banma.business.oms.common.constant.PoiAggreJsonKey;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.DSLConfigDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.QueryFieldMetadataDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.SyncConfigDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.SyncFieldMetadataDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.AccessGuideData;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.DSLConfig;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.RequirementInfo;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncConfig;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * mcm请求数据工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class McmRequestDataUtil {

    public static AccessGuideData getAccessGuideDataFromMcmRequestMap(Map<String, Object> map) {
        AccessGuideData accessGuideData = new AccessGuideData();
        String accessWay = map.get(PoiAggreJsonKey.ACCESS_WAY).toString();
        if (StrUtil.isBlank(accessWay)) {
            throw new PoiAggreQueryManagerException("接入方式为空");
        }
        RequirementInfo requirementInfo = BeanUtil.mapToBean((Map<String, Object>) map.get(PoiAggreJsonKey.REQUIREMENT_INFO), RequirementInfo.class, false);
        Boolean provideQueryService = (Boolean) map.get(PoiAggreJsonKey.PROVIDE_QUERY_SERVICE);
        String dtsSubscriptionUrl = map.get(PoiAggreJsonKey.DTS_SUBSCRIPTION_URL).toString();
        String craneUrlInfo = map.get(PoiAggreJsonKey.CRANE_URL_INFO).toString();

        accessGuideData.setAccessWay(accessWay);
        accessGuideData.setRequirementInfo(requirementInfo);
        accessGuideData.setProvideQueryService(provideQueryService);

        if (StrUtil.isNotBlank(dtsSubscriptionUrl)) {
            accessGuideData.setDtsSubscriptionUrl(dtsSubscriptionUrl);
        }
        if (StrUtil.isNotBlank(craneUrlInfo)) {
            accessGuideData.setCraneUrlInfo(craneUrlInfo);
        }

        if (accessWay.equals(AccessWayEnum.DTS.getCode()) || accessWay.equals(AccessWayEnum.MAFKA.getCode())) {
            SyncConfigDTO syncConfigDTO = getConfig((Map<String, Object>) map.get(PoiAggreJsonKey.SYNC_CONFIG), SyncConfigDTO.class);
            accessGuideData.setSyncConfig(syncConfigDTO);

            // 同步字段信息
            List<?> list = (List<?>) map.get(PoiAggreJsonKey.SYNC_FIELD_LIST);
            List<SyncFieldMetadataDTO> syncFieldList = list.stream().map(obj -> {
                if (obj instanceof Map) {
                    return BeanUtil.mapToBean((Map<String, Object>) obj, SyncFieldMetadataDTO.class, false);
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            accessGuideData.setSyncFieldList(syncFieldList);
        } else if (accessWay.equals(AccessWayEnum.RPC.getCode())) {
            DSLConfigDTO dslConfigDTO = getConfig((Map<String, Object>) map.get(PoiAggreJsonKey.DSL_CONFIG), DSLConfigDTO.class);
            accessGuideData.setDslConfig(dslConfigDTO);
        } else {
            throw new PoiAggreQueryManagerException("接入方式异常");
        }

        // 查询字段信息
        if (provideQueryService) {
            List<?> list = (List<?>) map.get(PoiAggreJsonKey.QUERY_FIELD_LIST);
            List<QueryFieldMetadataDTO> queryFieldList = list.stream().map(obj -> {
                if (obj instanceof Map) {
                    return BeanUtil.mapToBean((Map<String, Object>) obj, QueryFieldMetadataDTO.class, false);
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            accessGuideData.setQueryFieldList(queryFieldList);
        }

        return accessGuideData;
    }

    public static <T> T getConfig(Map<String, Object> map, Class<T> clazz) {
        Object config;
        if (clazz.equals(SyncConfig.class)) {
            SyncConfig syncConfig = (SyncConfig) BeanUtil.mapToBean(map, clazz, false);
            config = handleSyncConfigWithAccessWay(syncConfig);
        } else if (clazz.equals(SyncConfigDTO.class)) {
            SyncConfigDTO syncConfig = (SyncConfigDTO) BeanUtil.mapToBean(map, clazz, false);
            config = handleSyncConfigWithAccessWay(syncConfig);
        } else if (clazz.equals(DSLConfig.class) || clazz.equals(DSLConfigDTO.class)) {
            config = BeanUtil.mapToBean(map, clazz, false);
        } else {
            log.error("#请求参数异常，无法获取配置#, ChangeEventContextPropertyProvider.java@getConfig");
            throw new PoiAggreQueryManagerException("请求参数异常，无法获取配置");
        }

        return (T) config;
    }

    @NotNull
    private static Object handleSyncConfigWithAccessWay(SyncConfig syncConfig) {
        SyncConfigDTO syncConfigDTO = new SyncConfigDTO();
        BeanUtil.copyProperties(syncConfig, syncConfigDTO);
        return handleSyncConfigWithAccessWay(syncConfigDTO);
    }

    @NotNull
    private static Object handleSyncConfigWithAccessWay(SyncConfigDTO syncConfig) {
        Object config;
        if (StrUtil.isBlank(syncConfig.getDtsSyncConfig())) {
            syncConfig.setDtsSyncConfig(null);
            if (StrUtil.isBlank(syncConfig.getTotalPoiConsistencyCheck())) {
                syncConfig.setTotalPoiConsistencyCheck(null);
            }
            if (StrUtil.isBlank(syncConfig.getQueryChangeIdsConfig())) {
                syncConfig.setQueryChangeIdsConfig(null);
            }
        } else {
            syncConfig.setMafkaConsumeConfig(null);
            syncConfig.setTotalPoiConsistencyCheck(null);
            syncConfig.setQueryDataConfig(null);
            syncConfig.setQueryChangeIdsConfig(null);
        }
        config = syncConfig;
        return config;
    }
}
