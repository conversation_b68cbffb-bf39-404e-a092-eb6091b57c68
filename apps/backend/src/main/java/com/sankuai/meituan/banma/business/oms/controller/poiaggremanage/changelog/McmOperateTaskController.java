package com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.changelog;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.changelog.McmOperateTaskService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * mcm操作任务
 * <AUTHOR>
 */
@RestController
@RequestMapping("/poiAggreManage/taskCenter")
public class McmOperateTaskController {

    @Resource
    private McmOperateTaskService mcmOperateTaskService;

    @GetMapping("/page")
    public CommonResult<PageResultDTO<McmOperateTask>> page(@RequestParam(value = "pageNo") Integer pageNo,
                                                            @RequestParam("pageSize") Integer pageSize,
                                                            @RequestParam(value = "taskType", required = false) Integer taskType,
                                                            @RequestParam(value = "status", required = false) Integer status,
                                                            @RequestParam(value = "personalTask", required = false) Integer personalTask) {
        return mcmOperateTaskService.page(pageNo, pageSize, taskType, status, personalTask);
    }
}
