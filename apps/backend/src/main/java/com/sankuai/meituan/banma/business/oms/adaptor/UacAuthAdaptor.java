package com.sankuai.meituan.banma.business.oms.adaptor;

import com.sankuai.meituan.uac.sdk.entity.menu.UserMenu;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * UAC认证适配器
 */
@Component
public class UacAuthAdaptor {

    @Autowired
    private UacAuthRemoteService uacAuthRemoteService;

    /**
     * 获取用户菜单
     * @param userId 用户ID
     * @return 用户菜单
     */
    public UserMenu getUserMenu(String userId) {
        UserMenu userMenu = uacAuthRemoteService.getUserMenus(userId);
        return userMenu;
    }

} 