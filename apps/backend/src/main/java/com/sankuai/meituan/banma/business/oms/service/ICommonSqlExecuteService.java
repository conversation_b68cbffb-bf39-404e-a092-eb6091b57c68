package com.sankuai.meituan.banma.business.oms.service;


import com.sankuai.meituan.banma.business.oms.common.vo.TableSchema;

import java.util.List;
import java.util.Map;



public interface ICommonSqlExecuteService {
    /**
     * 获取所有表结构
     */
    List<TableSchema> getAllTableSchema();

    /**
     * select sql
     */
    List<Map<String, Object>> query(String sql, String jdbcRef);

    /**
     * explain sql
     */
    String explain(String sql, String jdbcRef);
}
