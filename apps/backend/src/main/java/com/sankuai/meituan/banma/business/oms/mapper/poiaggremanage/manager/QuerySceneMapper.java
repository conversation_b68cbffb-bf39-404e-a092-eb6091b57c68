package com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager;

import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryScene;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 查询场景Mapper接口
 */
@Mapper
public interface QuerySceneMapper {
    /**
     * 条件分页查询
     *
     * @param sceneCode 场景编码
     * @param sceneName 场景名称
     * @param sceneLevel 场景级别
     * @param valid 是否有效
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 查询场景列表
     */
    List<QueryScene> queryScenePage(@Param("sceneCode") String sceneCode,
                                   @Param("sceneName") String sceneName,
                                   @Param("sceneLevel") Integer sceneLevel,
                                   @Param("valid") Integer valid,
                                   @Param("offset") Integer offset,
                                   @Param("limit") Integer limit);

    /**
     * 统计总数
     *
     * @param sceneCode 场景编码
     * @param sceneName 场景名称
     * @param sceneLevel 场景级别
     * @param valid 是否有效
     * @return 总数
     */
    int countScene(@Param("sceneCode") String sceneCode,
                  @Param("sceneName") String sceneName,
                  @Param("sceneLevel") Integer sceneLevel,
                  @Param("valid") Integer valid);

    /**
     * 获取下一个场景编码
     *
     * @return
     */
    int getNextSceneCode();

    /**
     * 新增场景
     *
     * @param scene 场景信息
     * @return 影响行数
     */
    int insert(QueryScene scene);

    /**
     * 更新场景
     *
     * @param scene 场景信息
     * @return 影响行数
     */
    int update(QueryScene scene);

    /**
     * 批量删除场景
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据场景名称查询场景（用于查重）
     *
     * @param sceneName 场景名称
     * @return 查询场景
     */
    QueryScene selectBySceneName(@Param("sceneName") String sceneName);
} 