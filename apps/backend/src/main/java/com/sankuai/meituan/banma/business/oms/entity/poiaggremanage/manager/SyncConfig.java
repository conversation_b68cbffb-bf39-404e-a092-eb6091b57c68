package com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.meituan.banma.business.oms.common.handler.JsonStringSerializeHandler;
import lombok.Data;

import java.util.List;

/**
 * 同步配置
 * <AUTHOR>
 */
@Data
public class SyncConfig {

    @JSONField(serialize = false)
    private Long id;

    /**
     * 同步租户
     */
    @JSONField(ordinal = 1)
    private Integer syncTenant;

    /**
     * 同步场景
     */
    @JSONField(ordinal = 2)
    private Integer syncScene;

    /**
     * 同步名称
     */
    @JSONField(ordinal = 3)
    private String syncName;

    /**
     * 同步描述
     */
    @JSONField(ordinal = 4)
    private String syncDescription;

    /**
     * 同步字段code列表
     */
    @JSONField(ordinal = 5)
    private List<String> syncFieldCodes;

    /**
     * 同步类型，1-mafka，2-dts
     */
    @JSONField(ordinal = 6)
    private Integer type;

    /**
     * 是否外部POI
     */
    @JSONField(ordinal = 7)
    private Boolean isPoiOuter;

    /**
     * 一致性校验
     */
    @JSONField(ordinal = 8, serializeUsing = JsonStringSerializeHandler.class)
    private String totalPoiConsistencyCheck;

    /**
     * mafka消费配置
     */
    @JSONField(ordinal = 9, serializeUsing = JsonStringSerializeHandler.class)
    private String mafkaConsumeConfig;

    /**
     * 查询数据配置
     */
    @JSONField(ordinal = 10, serializeUsing = JsonStringSerializeHandler.class)
    private String queryDataConfig;

    /**
     * 查询变更ID配置
     */
    @JSONField(ordinal = 11, serializeUsing = JsonStringSerializeHandler.class)
    private String queryChangeIdsConfig;

    /**
     * dts同步配置
     */
    @JSONField(ordinal = 12, serializeUsing = JsonStringSerializeHandler.class)
    private String dtsSyncConfig;

    /**
     * 是否有效
     */
    @JSONField(serialize = false)
    private Integer valid;

    /**
     * 状态，0-正常，1-审核中
     */
    @JSONField(serialize = false)
    private Integer status;

    /**
     * 操作人姓名
     */
    @JSONField(serialize = false)
    private String opName;

    /**
     * 操作人mis
     */
    @JSONField(serialize = false)
    private String opMis;

    /**
     * 创建时间
     */
    @JSONField(serialize = false)
    private Integer ctime;

    /**
     * 更新时间
     */
    @JSONField(serialize = false)
    private Integer utime;

}
