package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.meituan.banma.llm.corpus.api.client.BmOmsWorkflowThriftService;
import com.meituan.banma.llm.corpus.api.exception.LlmCorpusException;
import com.meituan.banma.llm.corpus.api.response.CommonResponse;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.constant.*;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.common.util.McmRequestDataUtil;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.DSLConfigDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.QueryFieldMetadataDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.SyncConfigDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.SyncFieldMetadataDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.AccessGuideData;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.RequirementInfo;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.changelog.McmOperateTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 接入指南Service
 * <AUTHOR>
 */
@Slf4j
@Service
public class AccessGuideService implements IMcmHandleService{

    private static final String WORKFLOW_RESPONSE_STATUS_KEY = "code";

    private static final String WORKFLOW_RESPONSE_DATA_KEY = "data";

    private static final String WORKFLOW_RESPONSE_ERROR_MESSAGE_KEY = "msg";

    @Resource
    private QueryFieldMetadataService queryFieldMetadataService;

    @Resource
    private SyncFieldMetadataService syncFieldMetadataService;

    @Resource
    private SyncConfigService syncConfigService;

    @Resource
    private DSLConfigService dslConfigService;

    @Resource
    private McmOperateTaskService mcmOperateTaskService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.deliverypaotui.llm.corpus", timeout = 200000)
    private BmOmsWorkflowThriftService bmOmsWorkflowThriftService;

    public CommonResult<Map<String, Object>> getAiAccessGuideConfig(AccessGuideData accessGuideData) {
        RequirementInfo requirementInfo = accessGuideData.getRequirementInfo();
        if (requirementInfo == null || requirementInfo.getAnalysisResult() == null) {
            return CommonResult.failed(400, "需求信息不能为空");
        }
        String analysisResult = requirementInfo.getAnalysisResult();

        try {
            CommonResponse<String> aiGeneratedConfig = bmOmsWorkflowThriftService.getAccessGuideIntelligenceConfiguration(analysisResult);
            // 解析成map返回
            LinkedHashMap<String, Object> retMap = JSON.parseObject(aiGeneratedConfig.getData(), new TypeReference<LinkedHashMap<String, Object>>() {}, Feature.OrderedField);

            if (!retMap.containsKey(WORKFLOW_RESPONSE_STATUS_KEY)) {
                log.error("##AccessGuideService##getAiAccessGuideConfig，接口返回结果异常");
                return CommonResult.failed(500, "接口调用出现未知错误");
            }
            int responseCode = Integer.parseInt(retMap.get(WORKFLOW_RESPONSE_STATUS_KEY).toString());
            if (responseCode != 0) {
                log.error("##AccessGuideService##getAiAccessGuideConfig，工作流执行异常, {}", retMap.get(WORKFLOW_RESPONSE_ERROR_MESSAGE_KEY).toString());
                throw new PoiAggreQueryManagerException(retMap.get(WORKFLOW_RESPONSE_ERROR_MESSAGE_KEY).toString());
            }
            Map<String, Object> data = (Map<String, Object>) retMap.get(WORKFLOW_RESPONSE_DATA_KEY);
            return CommonResult.success(data);
        } catch (LlmCorpusException e) {
            log.error("##AccessGuideService##getAiAccessGuideConfig，RPC接口调用异常", e);
            throw new PoiAggreQueryManagerException(e.getMessage());
        } catch (Exception e) {
            log.error("##AccessGuideService##getAiAccessGuideConfig，方法执行异常", e);
            throw e;
        }
    }

    @Override
    public boolean preCheck(String requestUri, McmChangeConfigDTO mcmChangeConfigDTO) {
        if (!McmRequest.ADD.equals(requestUri)) {
            throw new PoiAggreQueryManagerException("不支持的操作类型");
        }
        // 校验同步字段、同步配置【DTS/Mafka/RPC/Squirrel(未实现)】、查询字段是否合法
        Map<String, Object> requestData = mcmChangeConfigDTO.getRequestData();
        AccessGuideData accessGuideData = McmRequestDataUtil.getAccessGuideDataFromMcmRequestMap(requestData);

        if (StrUtil.isBlank(accessGuideData.getAccessWay())) {
            throw new PoiAggreQueryManagerException("接入方式不能为空");
        }
        if (accessGuideData.getAccessWay().equals(AccessWayEnum.DTS.getCode())) {
            if (StrUtil.isBlank(accessGuideData.getDtsSubscriptionUrl())) {
                throw new PoiAggreQueryManagerException("接入方式为DTS时，DTS订阅链接不能为空");
            }
            if (accessGuideData.getSyncConfig() == null) {
                throw new PoiAggreQueryManagerException("接入方式为DTS时，同步配置不能为空");
            }
        } else if (accessGuideData.getAccessWay().equals(AccessWayEnum.MAFKA.getCode())) {
            if (StrUtil.isBlank(accessGuideData.getCraneUrlInfo())) {
                throw new PoiAggreQueryManagerException("接入方式为MAFKA时，Crane一致性校验定时任务链接不能为空");
            }
            if (accessGuideData.getSyncConfig() == null) {
                throw new PoiAggreQueryManagerException("接入方式为MAFKA时，同步配置不能为空");
            }
        } else if (accessGuideData.getAccessWay().equals(AccessWayEnum.RPC.getCode())) {
            if (accessGuideData.getDslConfig() == null) {
                throw new PoiAggreQueryManagerException("接入方式为RPC时，DSL配置不能为空");
            }
        }

        if (accessGuideData.getAccessWay().equals(AccessWayEnum.RPC.getCode())) {
            // 校验DSL配置
            DSLConfigDTO dslConfigDTO = accessGuideData.getDslConfig();
            dslConfigService.addPreCheck(dslConfigDTO);
        } else {
            // 校验同步字段
            List<SyncFieldMetadataDTO> syncFieldDTOList = accessGuideData.getSyncFieldList();
            syncFieldMetadataService.batchAddPreCheck(syncFieldDTOList);

            // 校验同步配置
            SyncConfigDTO syncConfigDTO = accessGuideData.getSyncConfig();
            syncConfigService.addPreCheck(syncConfigDTO);
        }

        if (accessGuideData.getProvideQueryService()) {
            // 校验查询字段
            List<QueryFieldMetadataDTO> queryFieldDTOList = accessGuideData.getQueryFieldList();
            queryFieldMetadataService.batchAddPreCheck(queryFieldDTOList);
        }

        return true;
    }

    @Override
    public boolean addOperateTaskAndUpdateRecordStatus2Auditing(String mcmUrl, String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        // 只需要添加任务
        McmOperateTask mcmOperateTask = getAuditingMcmOperateTask(mcmUrl, eventUuid, mcmChangeConfigDTO, McmTaskType.ACCESS_GUIDE.getCode());
        return mcmOperateTaskService.addTask(mcmOperateTask);
    }

    @Override
    public void mcmAuditAccepted(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        CompletableFuture<Void> future;
        // Index: McmTaskType.code；Value: 0-无任务，1-完成，2-异常
        final Integer[] taskStatus = {null, 0, 0, 0, 0};
        try {
            // 1. 插入DB
            Map<String, Object> requestData = mcmChangeConfigDTO.getRequestData();
            AccessGuideData accessGuideData = McmRequestDataUtil.getAccessGuideDataFromMcmRequestMap(requestData);
            String accessWay = accessGuideData.getAccessWay();

            if (accessWay.equals(AccessWayEnum.DTS.getCode()) || accessWay.equals(AccessWayEnum.MAFKA.getCode())) {
                List<SyncFieldMetadataDTO> syncFieldDTOList = accessGuideData.getSyncFieldList();
                CompletableFuture<Void> syncFieldFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        syncFieldMetadataService.batchAdd(syncFieldDTOList);
                        taskStatus[McmTaskType.SYNC_FIELD.getCode()] = 1;
                    } catch (Exception e) {
                        taskStatus[McmTaskType.SYNC_FIELD.getCode()] = 2;
                        log.error("#接入指引# 添加同步字段异常", e);
                    }
                    return null;
                });

                SyncConfigDTO syncConfigDTO = accessGuideData.getSyncConfig();
                CompletableFuture<Void> syncConfigFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        syncConfigService.add(syncConfigDTO);
                        taskStatus[McmTaskType.SYNC_CONFIG.getCode()] = 1;
                    } catch (Exception e) {
                        taskStatus[McmTaskType.SYNC_CONFIG.getCode()] = 2;
                        log.error("#接入指引# 添加同步配置异常", e);
                    }
                    return null;
                });
                future = CompletableFuture.allOf(syncFieldFuture, syncConfigFuture);
            } else if (accessWay.equals(AccessWayEnum.RPC.getCode())) {
                DSLConfigDTO dslConfigDTO = accessGuideData.getDslConfig();
                CompletableFuture<Void> dslConfigFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        dslConfigService.add(dslConfigDTO);
                        taskStatus[McmTaskType.DSL.getCode()] = 1;
                    } catch (Exception e) {
                        taskStatus[McmTaskType.DSL.getCode()] = 2;
                        log.error("#接入指引# 添加DSL配置异常", e);
                    }
                    return null;
                });
                future = CompletableFuture.allOf(dslConfigFuture);
            } else {
                throw new PoiAggreQueryManagerException("不支持的接入方式");
            }

            if (accessGuideData.getProvideQueryService()) {
                List<QueryFieldMetadataDTO> queryFieldDTOList = accessGuideData.getQueryFieldList();
                CompletableFuture<Void> queryFieldFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        queryFieldMetadataService.batchAdd(queryFieldDTOList);
                        taskStatus[McmTaskType.QUERY_FIELD.getCode()] = 1;
                    } catch (Exception e) {
                        taskStatus[McmTaskType.QUERY_FIELD.getCode()] = 2;
                        log.error("#接入指引# 添加查询字段异常", e);
                    }
                    return null;
                });
                future = CompletableFuture.allOf(future, queryFieldFuture);
            }
            future.join();

            StringBuilder errorMsg = new StringBuilder();
            for (int i = 1; i < taskStatus.length; i++) {
                if (taskStatus[i] == 0) {
                    continue;
                }
                if (taskStatus[i] == 2) {
                    errorMsg.append("【").append(McmTaskType.getDescByCode(i)).append("】");
                }
            }
            if (StrUtil.isNotBlank(errorMsg)) {
                errorMsg.insert(0, "以下任务执行失败，请检查后手动重试:");
                log.error("#接入指引# {}", errorMsg);
                throw new PoiAggreQueryManagerException(errorMsg.toString());
            }

        } catch (Exception e) {
            McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.EXCEPTION.getCode());
            mcmOperateTaskService.updateTask(mcmOperateTask);
            throw e;
        }
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.ACCEPTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);
    }

    @Override
    public void mcmAuditRejected(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.REJECTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);
    }

    @Override
    public void mcmAuditCancel(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.CANCELED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);
    }

    @Override
    public McmOperateTask getAuditingMcmOperateTask(String mcmUrl, String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO, Integer taskType) {
        McmOperateTask mcmOperateTask = new McmOperateTask();
        mcmOperateTask.setTaskType(taskType);
        String accessWay = mcmChangeConfigDTO.getRequestData().get(PoiAggreJsonKey.ACCESS_WAY).toString().toUpperCase();
        String taskName = accessWay + "-" + mcmChangeConfigDTO.getChangeDescription();
        mcmOperateTask.setTaskName(taskName);
        mcmOperateTask.setMcmUrl(mcmUrl);
        mcmOperateTask.setMcmEventUuid(eventUuid);
        mcmOperateTask.setStatus(McmTaskStatus.AUDITING.getCode());
        return mcmOperateTask;
    }
}
