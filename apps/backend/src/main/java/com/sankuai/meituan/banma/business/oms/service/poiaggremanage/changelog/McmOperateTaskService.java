package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.changelog;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;
import com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.changelog.McmOperateTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 操作任务
 * <AUTHOR>
 */
@Slf4j
@Service
public class McmOperateTaskService {

    @Resource
    private McmOperateTaskMapper mcmOperateTaskMapper;

    /**
     * 新增
     * @param mcmOperateTask 任务信息
     * @return 是否成功
     */
    @Transactional(value = "TM0", rollbackFor = Exception.class)
    public boolean addTask(McmOperateTask mcmOperateTask) {
        User user = UserUtils.getUser();
        if (user == null) {
            throw new PoiAggreQueryManagerException("获取用户信息失败");
        }
        mcmOperateTask.setOpName(user.getName());
        mcmOperateTask.setOpMis(user.getLogin());

        int result = mcmOperateTaskMapper.insert(mcmOperateTask);
        if (result != 1) {
            log.error("#新增任务失败#, taskName: {}", mcmOperateTask.getTaskName());
            throw new PoiAggreQueryManagerException("新增任务失败");
        }
        return true;
    }

    /**
     * 修改
     * @param mcmOperateTask 任务信息
     * @return 是否成功
     */
    @Transactional(value = "TM0", rollbackFor = Exception.class)
    public boolean updateTask(McmOperateTask mcmOperateTask) {
        int result = mcmOperateTaskMapper.updateByEventUuid(mcmOperateTask);
        if (result != 1) {
            log.error("#变更记录状态修改失败#, taskId: {}", mcmOperateTask.getId());
            throw new PoiAggreQueryManagerException("变更记录状态修改失败");
        }
        return true;
    }

    /**
     * 分页查询
     * @param pageNo 页号
     * @param pageSize 页面大小
     * @param taskType 任务类型
     * @param status 任务状态
     * @param personalTask 是否个人任务
     * @return 任务列表
     */
    public CommonResult<PageResultDTO<McmOperateTask>> page(Integer pageNo, Integer pageSize, Integer taskType, Integer status, Integer personalTask) {
        User user = UserUtils.getUser();
        if (user == null) {
            throw new PoiAggreQueryManagerException("获取用户信息失败");
        }

        String opMis = null;
        if (personalTask != null && personalTask == 1) {
            opMis = user.getLogin();
        }

        // 计算偏移量
        int offset = (pageNo - 1) * pageSize;

        List<McmOperateTask> taskList = mcmOperateTaskMapper.page(offset, pageSize, taskType, status, opMis);
        int total = mcmOperateTaskMapper.count(taskType, status, opMis);

        PageResultDTO<McmOperateTask> result = new PageResultDTO<>(total, taskList);
        return CommonResult.success(result);
    }
}
