package com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 聚合查询字段元数据
 * <AUTHOR>
 */
@Data
public class QueryFieldMetadata {

    public static final Integer HANDLER_TYPE_LOCAL = 0;
    public static final Integer HANDLER_TYPE_DYNAMIC = 1;

    /**
     * 主键ID
     */
    @JSONField(serialize = false)
    private Long id;

    /**
     * 字段code
     */
    @JSONField(ordinal = 1)
    private String fieldCode;

    /**
     * 字段属性
     */
    @JSONField(ordinal = 2)
    private String fieldProperty;

    /**
     * 字段名称
     */
    @JSONField(ordinal = 3)
    private String fieldName;

    /**
     * 字段描述
     */
    @JSONField(ordinal = 4)
    private String description;

    /**
     * 字段类型，1-String，2-Boolean，3-Long，4-Double
     */
    @JSONField(ordinal = 5)
    private String type;

    /**
     * 默认值
     */
    @JSONField(ordinal = 6)
    private String defaultValue;

    /**
     * 有效性 0:无效;1:有效
     */
    @JSONField(serialize = false)
    private Integer valid;

    /**
     * 依赖字段
     */
    @JSONField(ordinal = 7)
    private List<String> dependentFields;

    /**
     * 是否是同步过来字段
     */
    @JSONField(serialize = false)
    private Boolean syncedField;

    /**
     * 处理器类型，0-本地，1-动态
     */
    @JSONField(serialize = false)
    private Integer handlerType;

    /**
     * 状态，0-正常，1-审核中
     */
    @JSONField(serialize = false)
    private Integer status;

    /**
     * 操作人姓名
     */
    @JSONField(serialize = false)
    private String opName;

    /**
     * 操作人mis
     */
    @JSONField(serialize = false)
    private String opMis;

    /**
     * 创建时间
     */
    @JSONField(serialize = false)
    private Integer ctime;

    /**
     * 更新时间
     */
    @JSONField(serialize = false)
    private Integer utime;
} 