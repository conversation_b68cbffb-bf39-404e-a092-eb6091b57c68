package com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 聚合查询场景实体类
 * <AUTHOR>
 */
@Data
public class QueryScene {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 场景使用服务
     */
    @NotEmpty(message = "场景使用服务不能为空")
    private List<String> appkeys;

    /**
     * 场景名称
     */
    @NotBlank(message = "场景名称不能为空")
    private String sceneName;

    /**
     * 场景描述
     */
    @NotBlank(message = "场景描述不能为空")
    private String sceneDescription;

    /**
     * 场景编码
     */
    private Integer sceneCode;

    /**
     * PRD或者技术方案Wiki
     */
    private List<String> prdWikis;

    /**
     * 服务类型，1-set，2-中心，3-set&中心
     */
    @NotEmpty(message = "服务类型不能为空")
    private Integer appType;

    /**
     * 场景管理员mis号列表
     */
    @NotEmpty(message = "场景管理员不能为空")
    private List<String> administrator;

    /**
     * 场景级别，0-9，数字越小重要级别越高
     */
    @NotNull(message = "场景级别不能为空")
    @Min(value = 0, message = "场景级别最小为0")
    @Max(value = 9, message = "场景级别最大为9")
    private Integer sceneLevel;

    /**
     * 高峰期
     */
    @NotBlank(message = "高峰期不能为空")
    private String peakPeriod;

    /**
     * 场景使用字段
     */
    @NotEmpty(message = "场景使用字段不能为空")
    private List<String> fieldCodes;

    /**
     * 依赖类型 1强依赖;2弱依赖
     */
    @NotNull(message = "依赖类型不能为空")
    @Min(value = 1, message = "依赖类型必须为1或2")
    @Max(value = 2, message = "依赖类型必须为1或2")
    private Integer dependencyType;

    /**
     * 预估qps
     */
    @NotNull(message = "预估QPS不能为空")
    @Min(value = 0, message = "预估QPS不能小于0")
    private Integer estimateQps;

    /**
     * 实际qps
     */
    @Min(value = 0, message = "实际QPS不能小于0")
    private Integer actualQps;

    /**
     * 有效性 0:无效;1:有效
     */
    @NotNull(message = "有效性不能为空")
    @Min(value = 0, message = "有效性必须为0或1")
    @Max(value = 1, message = "有效性必须为0或1")
    private Integer valid;

    /**
     * 状态，0-正常，1-审核中
     */
    @JSONField(serialize = false)
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人姓名
     */
    private String opName;

    /**
     * 操作人mis
     */
    private String opMis;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 更新时间
     */
    private Integer utime;
} 