package com.sankuai.meituan.banma.business.oms.config.mcm;

import cn.hutool.core.bean.BeanUtil;
import com.sankuai.mcm.client.sdk.context.handler.PreAuditHandlerResult;
import com.sankuai.mcm.client.sdk.context.handler.PreCheckHandlerResult;
import com.sankuai.mcm.client.sdk.dto.common.EventContext;
import com.sankuai.mcm.client.sdk.exception.McmPreAuditException;
import com.sankuai.mcm.client.sdk.exception.McmPreCheckException;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ChangeEventSubmitCallbackHandler {

    @Resource
    private QueryFieldMetadataService queryFieldMetadataService;

    @Resource
    private DSLConfigService dslConfigService;

    @Resource
    private SyncFieldMetadataService syncFieldMetadataService;

    @Resource
    private SyncConfigService syncConfigService;

    /**
     * 捕获预检异常，返回预检结果
     */
    @ExceptionHandler(McmPreCheckException.class)
    public CommonResult<PreCheckHandlerResult> handle(McmPreCheckException exception) {
        return CommonResult.success(exception.getResult());
    }

    /**
     * 捕获审核异常，通知用户审核已发起
     */
    @ExceptionHandler(McmPreAuditException.class)
    public CommonResult<String> handle(McmPreAuditException exception) {
        PreAuditHandlerResult result = exception.getResult();
        EventContext eventContext = result.getEventContext();
        Map<String, Object> requestParameters = eventContext.getRequestParameters();
        String eventUuid = eventContext.getEventUuid();

        McmChangeConfigDTO mcmChangeConfigDTO = BeanUtil.mapToBean(requestParameters, McmChangeConfigDTO.class, false);
        log.info("审核已发起，变更信息：{}", mcmChangeConfigDTO);

        // exception.getResult().getUrl() 异常信息里有mcm详情链接，按需拼接提示
        String mcmUrl = result.getUrl();

        // 修改数据状态
        String requestBaseUrl = mcmChangeConfigDTO.getRequestBaseUrl();
        Map<String, IMcmHandleService> routeMap = McmConfig.POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP;
        if (!routeMap.containsKey(requestBaseUrl)) {
            throw new PoiAggreQueryManagerException("请求baseUrl不存在");
        }
        IMcmHandleService mcmHandleService = routeMap.get(requestBaseUrl);
        boolean isSuccess = mcmHandleService.addOperateTaskAndUpdateRecordStatus2Auditing(mcmUrl, eventUuid, mcmChangeConfigDTO);
        if (!isSuccess) {
            return CommonResult.failed("审核已发起，但修改数据状态失败");
        }

        // 这里可以返回审核链接，给用户一些提示信息
        return CommonResult.success(mcmUrl, "审核已发起");
    }

}
