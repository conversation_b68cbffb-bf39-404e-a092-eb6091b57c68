package com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager;

import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.DSLConfigDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.QueryFieldMetadataDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.SyncConfigDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.SyncFieldMetadataDTO;
import lombok.Data;

import java.util.List;

/**
 * 接入指引数据
 * <AUTHOR>
 */
@Data
public class AccessGuideData {

    /**
     * 接入方式，dts、mafka、rpc
     */
    private String accessWay;

    /**
     * 需求信息
     */
    private RequirementInfo requirementInfo;

    /**
     * 同步字段列表
     */
    private List<SyncFieldMetadataDTO> syncFieldList;

    /**
     * 同步配置
     */
    private SyncConfigDTO syncConfig;

    /**
     * 是否提供查询服务
     */
    private Boolean provideQueryService;

    /**
     * 查询字段列表
     */
    private List<QueryFieldMetadataDTO> queryFieldList;

    /**
     * dsl配置
     */
    private DSLConfigDTO dslConfig;

    /**
     * dts订阅链接
     */
    private String dtsSubscriptionUrl;

    /**
     * crane链接
     */
    private String craneUrlInfo;

}
