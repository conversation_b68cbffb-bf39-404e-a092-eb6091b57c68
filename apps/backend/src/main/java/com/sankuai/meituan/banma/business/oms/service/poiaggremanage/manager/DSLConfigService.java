package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.retry.DefaultRetryProperties;
import com.dianping.rhino.retry.Retry;
import com.dianping.rhino.retry.RetryCallback;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.banma.business.oms.adaptor.LionAdaptor;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.constant.LionConstant;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskStatus;
import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskType;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.LionPartialOperateException;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.config.mcm.ChangeEventPreCheckHandler;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.DSLConfigDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.page.DSLConfigPageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.DSLConfig;
import com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.DSLConfigMapper;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.changelog.McmOperateTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Validator;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * DSL配置Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DSLConfigService implements IMcmHandleService {

    @Resource
    private DSLConfigService self;

    @Resource
    private DSLConfigMapper dslConfigMapper;

    @Resource
    private McmOperateTaskService mcmOperateTaskService;

    @Resource
    private LionAdaptor lionAdaptor;

    @Resource
    private Validator validator;

    private static final Retry BATCH_DELETE_RETRY = Rhino.newRetry(
            LionConstant.DSL_BATCH_DELETE_RETRY_KEY,
            new DefaultRetryProperties.Setter()
                    .withActive(true)
                    .withDelay(500L)
                    .withMaxAttempts(5)
    );

    /**
     * 添加DSL配置
     *
     * @param dslConfigDTO DSL配置DTO
     * @return 操作结果
     */
    @Transactional(value = "TM0", rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public CommonResult<Boolean> add(DSLConfigDTO dslConfigDTO) {
        log.info("添加DSL配置: {}", dslConfigDTO);

        DSLConfig dslConfig = new DSLConfig();
        BeanUtil.copyProperties(dslConfigDTO, dslConfig);

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }
        // 设置操作人信息
        dslConfig.setOpName(user.getName());
        dslConfig.setOpMis(user.getLogin());

        try {
            int rows = dslConfigMapper.insert(dslConfig);
            if (rows > 0) {
                preHandleBeforeLionSet(dslConfig);

                // 同步到Lion配置中心
                String dslName = dslConfig.getDslName();
                dslName = dslName.substring(0, 1).toUpperCase() + dslName.substring(1);
                String lionKey = LionConstant.BM_POI_AGGRE_QUERY_DSL_CONFIG_LION_KEY_PREFIX + dslName;

                String lionConfigString = lionAdaptor.getLionConfigString(dslConfig);
                boolean lionResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, lionKey, lionConfigString, dslConfigDTO.getLionConfigDescription());
                if (!lionResult) {
                    throw new PoiAggreQueryManagerException("同步Lion配置失败");
                }
                return CommonResult.success(true);
            }
            log.error("添加DSL配置失败，插入返回0行，dslName: {}", dslConfig.getDslName());
            return CommonResult.failed("添加失败");
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("添加DSL配置失败，dslName: {}", dslConfig.getDslName(), e);
            throw new PoiAggreQueryManagerException("添加DSL配置失败", e);
        }
    }

    /**
     * 更新DSL配置
     *
     * @param dslConfigDTO DSL配置DTO
     * @return 操作结果
     */
    @Transactional(value = "TM0", rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public CommonResult<Boolean> update(DSLConfigDTO dslConfigDTO) {
        log.info("更新DSL配置: {}", dslConfigDTO);

        DSLConfig dslConfig = new DSLConfig();
        BeanUtil.copyProperties(dslConfigDTO, dslConfig);

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        // 设置操作人信息
        dslConfig.setOpName(user.getName());
        dslConfig.setOpMis(user.getLogin());

        try {
            int rows = dslConfigMapper.updateById(dslConfig);
            if (rows <= 0) {
                return CommonResult.failed("更新失败");
            }
            preHandleBeforeLionSet(dslConfig);

            // 同步到Lion配置中心
            String dslName = dslConfig.getDslName();
            dslName = dslName.substring(0, 1).toUpperCase() + dslName.substring(1);
            String lionKey = LionConstant.BM_POI_AGGRE_QUERY_DSL_CONFIG_LION_KEY_PREFIX + dslName;

            String lionConfigString = lionAdaptor.getLionConfigString(dslConfig);
            boolean lionResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, lionKey, lionConfigString);
            if (!lionResult) {
                throw new PoiAggreQueryManagerException("同步Lion配置失败");
            }
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新DSL配置失败", e);
            throw new PoiAggreQueryManagerException("更新DSL配置失败", e);
        }
    }

    /**
     * 批量删除DSL配置
     *
     * @param ids ID列表
     * @return 操作结果
     */
    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class, noRollbackFor = LionPartialOperateException.class)
    public CommonResult<Boolean> batchDeleteByIds(List<Long> ids) {
        log.info("批量删除DSL配置，ids: {}", ids);
        // 先查询所有记录，获取dslName列表
        List<DSLConfig> dslConfigs = dslConfigMapper.selectByIds(ids);
        if (dslConfigs.isEmpty()) {
            return CommonResult.failed(400, "记录不存在");
        }
        try {
            int rows = dslConfigMapper.batchDeleteByIds(ids);
            if (rows <= 0) {
                return CommonResult.failed("批量删除失败");
            }
            // 同步删除Lion配置
            Set<String> failedDslNameSet = dslConfigs.stream().map(DSLConfig::getDslName).collect(Collectors.toSet());
            BATCH_DELETE_RETRY.execute((RetryCallback<Object, Exception>) () -> {
                for (DSLConfig dslConfig : dslConfigs) {
                    if (!failedDslNameSet.contains(dslConfig.getDslName())) {
                        continue;
                    }
                    String dslName = dslConfig.getDslName();
                    dslName = dslName.substring(0, 1).toUpperCase() + dslName.substring(1);
                    String lionKey = LionConstant.BM_POI_AGGRE_QUERY_DSL_CONFIG_LION_KEY_PREFIX + dslName;
                    boolean lionResult = lionAdaptor.deleteConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, lionKey);
                    if (lionResult) {
                        failedDslNameSet.remove(dslConfig.getDslName());
                    }
                }
                if (failedDslNameSet.size() == dslConfigs.size()) {
                    log.error("批量删除DSL数据全部失败");
                    throw new PoiAggreQueryManagerException(500, "批量删除DSL数据全部失败, failedDslNameList: " + failedDslNameSet);
                } else if (!failedDslNameSet.isEmpty()) {
                    log.error("批量删除DSL数据失败, 以下DSL删除Lion配置失败: {}", failedDslNameSet);
                    throw new LionPartialOperateException(500, "批量删除DSL数据失败, 以下DSL删除Lion配置失败: " + failedDslNameSet);
                }
                return null;
            });
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("批量删除DSL配置失败", e);
            throw new PoiAggreQueryManagerException("批量删除DSL配置失败", e);
        }
    }

    /**
     * 分页查询DSL配置
     *
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    public CommonResult<PageResultDTO<DSLConfig>> getPage(DSLConfigPageQueryDTO pageQuery) {
        log.info("分页查询DSL配置: {}", pageQuery);
        // 计算偏移量
        int offset = (pageQuery.getPageNo() - 1) * pageQuery.getPageSize();
        try {
            // 查询数据
            List<DSLConfig> dslConfigList = dslConfigMapper.selectPage(
                    pageQuery.getDslName(),
                    pageQuery.getDslDescription(),
                    pageQuery.getValid(),
                    offset,
                    pageQuery.getPageSize()
            );

            // 查询总数
            int total = dslConfigMapper.countByCondition(
                    pageQuery.getDslName(),
                    pageQuery.getDslDescription(),
                    pageQuery.getValid()
            );

            PageResultDTO<DSLConfig> result = new PageResultDTO<>(total, dslConfigList);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("分页查询DSL配置失败", e);
            throw new PoiAggreQueryManagerException("分页查询DSL配置失败", e);
        }
    }

    @Override
    public boolean preCheck(String requestUri, McmChangeConfigDTO mcmChangeConfigDTO) {
        BindingResult bindingResult;
        switch (requestUri) {
            case McmRequest.ADD:
                DSLConfigDTO dslConfigDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), DSLConfigDTO.class, false);
                addPreCheck(dslConfigDTO);
                break;
            case McmRequest.UPDATE:
                DSLConfigDTO dslConfigRequestData = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), DSLConfigDTO.class, false);
                updatePreCheck(dslConfigRequestData);
                break;
            case McmRequest.BATCH_DELETE:
                Object ids = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                if (!(ids instanceof List) || ((List<?>) ids).isEmpty()) {
                    throw new PoiAggreQueryManagerException("ids参数类型错误");
                }
                List<Long> idList = ((List<?>) ids).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());

                batchDeletePreCheck(idList);
                break;
            default:
                return false;
        }
        return true;
    }

    @Override
    @Transactional(value = "TM0", rollbackFor = Exception.class)
    public boolean addOperateTaskAndUpdateRecordStatus2Auditing(String mcmUrl, String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        // 添加任务
        McmOperateTask mcmOperateTask = getAuditingMcmOperateTask(mcmUrl, eventUuid, mcmChangeConfigDTO, McmTaskType.DSL.getCode());
        boolean addTaskResult = mcmOperateTaskService.addTask(mcmOperateTask);
        if (!addTaskResult) {
            return false;
        }

        String requestUri = mcmChangeConfigDTO.getRequestUri();
        List<Long> ids = ChangeEventPreCheckHandler.getChangeIds(requestUri, mcmChangeConfigDTO);
        if (ids != null) {
            int count = dslConfigMapper.batchUpdateStatus(ids, McmRequest.MCM_STATUS_CODE_AUDITING);
            if (count != ids.size()) {
                throw new PoiAggreQueryManagerException("审核发起成功，更新状态失败");
            }
        }
        return true;
    }

    @Override
    @Transactional(value = "TM0", rollbackFor = Exception.class, noRollbackFor = {PoiAggreQueryManagerException.class, LionPartialOperateException.class})
    public void mcmAuditAccepted(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            int count = dslConfigMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
            if (count != changeIdList.size()) {
                throw new PoiAggreQueryManagerException("状态修改失败");
            }
        }
        DSLConfigDTO dslConfigDTO;
        CommonResult<Boolean> res;
        try {
            switch (mcmChangeConfigDTO.getRequestUri()) {
                case McmRequest.ADD:
                    dslConfigDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), DSLConfigDTO.class, false);
                    res = self.add(dslConfigDTO);

                    break;
                case McmRequest.UPDATE:
                    dslConfigDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), DSLConfigDTO.class, false);
                    res = self.update(dslConfigDTO);

                    break;
                case McmRequest.BATCH_DELETE:
                    Object ids = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                    if (!(ids instanceof List) || ((List<?>) ids).isEmpty()) {
                        throw new PoiAggreQueryManagerException("ids参数类型错误");
                    }
                    List<Long> idList = ((List<?>) ids).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());
                    res = self.batchDeleteByIds(idList);

                    break;
                default:
                    throw new PoiAggreQueryManagerException("未知请求类型");
            }
            if (res.getCode() != 0) {
                throw new PoiAggreQueryManagerException("回调处理失败");
            }
        } catch (Exception e) {
            McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.EXCEPTION.getCode());
            mcmOperateTaskService.updateTask(mcmOperateTask);
            throw e;
        }
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.ACCEPTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);
    }

    @Override
    public void mcmAuditRejected(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.REJECTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);

        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            dslConfigMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
        }
    }

    @Override
    public void mcmAuditCancel(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.CANCELED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);

        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            dslConfigMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
        }
    }

    public void addPreCheck(DSLConfigDTO dslConfigDTO) {
        BindingResult bindingResult;
        bindingResult = new BeanPropertyBindingResult(dslConfigDTO, "dslConfigDTO");
        validator.validate(dslConfigDTO, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new PoiAggreQueryManagerException(400, "请求参数异常");
        }
        // 校验dslName是否重复
        DSLConfig existingRecord = dslConfigMapper.selectByDslName(dslConfigDTO.getDslName());
        if (existingRecord != null) {
            log.error("添加DSL配置失败，编排名称已存在，dslName: {}", dslConfigDTO.getDslName());
            throw new PoiAggreQueryManagerException(400, "编排名称已存在");
        }
    }

    public void updatePreCheck(DSLConfigDTO dslConfigRequestData) {
        BindingResult bindingResult;
        bindingResult = new BeanPropertyBindingResult(dslConfigRequestData, "dslConfigRequestData");
        validator.validate(dslConfigRequestData, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new PoiAggreQueryManagerException(400, "请求参数异常");
        }
        if (dslConfigRequestData.getId() == null) {
            throw new PoiAggreQueryManagerException(400, "id不能为空");
        }
        // 校验dslName是否重复（排除自身）
        DSLConfig otherRecord = dslConfigMapper.selectByDslName(dslConfigRequestData.getDslName());
        DSLConfig dslConfig = dslConfigMapper.selectById(dslConfigRequestData.getId());
        if (dslConfig == null) {
            throw new PoiAggreQueryManagerException(400, "请求参数异常，id不存在");
        } else if (dslConfig.getStatus() == 1) {
            throw new PoiAggreQueryManagerException(400, "有数据正在审核中，禁止操作！");
        } else if (otherRecord != null && !Objects.equals(dslConfigRequestData.getId(), otherRecord.getId())) {
            log.error("更新DSL配置失败，编排名称已存在，dslName: {}", dslConfigRequestData.getDslName());
            throw new PoiAggreQueryManagerException(400, "编排名称已存在");
        }
    }

    public void batchDeletePreCheck(List<Long> idList) {
        // 先查询所有记录，获取dslName列表
        List<DSLConfig> dslConfigs = dslConfigMapper.selectByIds(idList);
        if (dslConfigs.size() != idList.size()) {
            throw new PoiAggreQueryManagerException(400, "不能删除不存在的数据！");
        }
        dslConfigs.forEach(config -> {
            if (config.getStatus() == 1) {
                throw new PoiAggreQueryManagerException(400, "有数据正在审核中，请重新选择！");
            }
        });
    }

    private static void preHandleBeforeLionSet(DSLConfig dslConfig) {
        // 处理thrownException字段
        if (!dslConfig.getThrownException()) {
            dslConfig.setThrownException(null);
        }
        dslConfig.setId(null);
        dslConfig.setValid(null);
    }
}