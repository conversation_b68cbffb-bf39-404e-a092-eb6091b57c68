package com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.tools;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.tools.OrchestrationService;
import com.sankuai.meituan.banma.service.orchestration.model.Response;
import com.sankuai.meituan.banma.service.orchestration.model.Workflow;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 服务编排控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/poiaggremanage/tools/orchestration")
@Slf4j
public class OrchestrationController {

    @Autowired
    private OrchestrationService orchestrationService;

    /**
     * 解析DSL
     * @param request 请求体
     * @return 解析后的Workflow对象
     */
    @PostMapping("/parse")
    public CommonResult<Workflow> parseDsl(@RequestBody ParseDslRequest request) {
        try {
            Workflow workflow = orchestrationService.parseDsl(request.getDsl());
            return CommonResult.success(workflow);
        } catch (Exception e) {
            log.error("解析DSL失败", e);
            return CommonResult.failed("解析DSL失败：" + e.getMessage());
        }
    }

    /**
     * 将Workflow对象转换为JSON字符串
     * @param request 请求体
     * @return JSON字符串
     */
    @PostMapping("/stringify")
    public CommonResult<String> stringify(@RequestBody StringifyRequest request) {
        try {
            String json = orchestrationService.stringify(request.getWorkflow());
            return CommonResult.success(json);
        } catch (Exception e) {
            log.error("Workflow转JSON失败", e);
            return CommonResult.failed("Workflow转JSON失败：" + e.getMessage());
        }
    }

    @Data
    public static class StringifyRequest {
        private Workflow workflow;
    }
    

    /**
     * 执行工作流
     * @param request 请求体
     * @return 执行结果
     */
    @PostMapping("/execute")
    public CommonResult<Response<Object>> executeWorkflow(@RequestBody ExecuteWorkflowRequest request) {
        try {
            Response<Object> result = orchestrationService.executeWorkflow(request.getDsl(), request.getParamJson());
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("执行工作流失败", e);
            return CommonResult.failed("执行工作流失败：" + e.getMessage());
        }
    }

    /**
     * 执行单个任务
     * @param request 请求体
     * @return 执行结果
     */
    @PostMapping("/execute-task")
    public CommonResult<Object> executeTask(@RequestBody ExecuteTaskRequest request) {
        try {
            Object result = orchestrationService.executeTask(
                    request.getDsl(),
                    request.getTaskAlias(),
                    request.getParamJson(),
                    request.getMockTaskResults()
            );
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("执行任务失败", e);
            return CommonResult.failed("执行任务失败：" + e.getMessage());
        }
    }

    /**
     * 保存工作流
     * @param request 请求体
     * @return 保存结果
     */
    @PostMapping("/save")
    public CommonResult<String> saveWorkflow(@RequestBody SaveWorkflowRequest request) {
        try {
            String id = orchestrationService.saveWorkflow(request.getName(), request.getDsl());
            return CommonResult.success(id);
        } catch (Exception e) {
            log.error("保存工作流失败", e);
            return CommonResult.failed("保存工作流失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流列表
     * @return 工作流列表
     */
    @GetMapping("/list")
    public CommonResult<List<WorkflowInfo>> getWorkflowList() {
        try {
            List<WorkflowInfo> list = orchestrationService.getWorkflowList();
            return CommonResult.success(list);
        } catch (Exception e) {
            log.error("获取工作流列表失败", e);
            return CommonResult.failed("获取工作流列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流详情
     * @param id 工作流ID
     * @return 工作流详情
     */
    @GetMapping("/detail")
    public CommonResult<WorkflowDetail> getWorkflowDetail(@RequestParam String id) {
        try {
            WorkflowDetail detail = orchestrationService.getWorkflowDetail(id);
            return CommonResult.success(detail);
        } catch (Exception e) {
            log.error("获取工作流详情失败", e);
            return CommonResult.failed("获取工作流详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取执行历史记录
     * @param workflowId 工作流ID
     * @return 执行历史记录
     */
    @GetMapping("/execution-history")
    public CommonResult<List<ExecutionRecord>> getExecutionHistory(@RequestParam String workflowId) {
        try {
            List<ExecutionRecord> history = orchestrationService.getExecutionHistory(workflowId);
            return CommonResult.success(history);
        } catch (Exception e) {
            log.error("获取执行历史记录失败", e);
            return CommonResult.failed("获取执行历史记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取执行详情
     * @param executionId 执行ID
     * @return 执行详情
     */
    @GetMapping("/execution-detail")
    public CommonResult<ExecutionDetail> getExecutionDetail(@RequestParam String executionId) {
        try {
            ExecutionDetail detail = orchestrationService.getExecutionDetail(executionId);
            return CommonResult.success(detail);
        } catch (Exception e) {
            log.error("获取执行详情失败", e);
            return CommonResult.failed("获取执行详情失败：" + e.getMessage());
        }
    }

    /* 请求/响应类定义 */
    @Data
    public static class ParseDslRequest {
        private String dsl;
    }
    
    @Data
    public static class ExecuteWorkflowRequest {
        private String dsl;
        private String paramJson;
    }
    
    @Data
    public static class ExecuteTaskRequest {
        private String dsl;
        private String taskAlias;
        private String paramJson;
        private Map<String, Object> mockTaskResults;
    }
    
    @Data
    public static class SaveWorkflowRequest {
        private String name;
        private String dsl;
    }
    
    @Data
    public static class WorkflowInfo {
        private String id;
        private String name;
        private String description;
        private String createTime;
        private String updateTime;
    }
    
    @Data
    public static class WorkflowDetail {
        private String id;
        private String name;
        private String description;
        private String dsl;
        private String createTime;
        private String updateTime;
    }
    
    @Data
    public static class ExecutionRecord {
        private String id;
        private String workflowId;
        private String executeTime;
        private String paramJson;
        private String status;
        private long duration;
    }
    
    @Data
    public static class ExecutionDetail {
        private String id;
        private String workflowId;
        private String executeTime;
        private String paramJson;
        private Map<String, Object> result;
        private Map<String, Object> taskResults;
        private String status;
        private long startTime;
        private long endTime;
        private long duration;
    }
} 