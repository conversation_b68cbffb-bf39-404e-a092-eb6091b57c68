package com.sankuai.meituan.banma.business.oms.config.mcm;

import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.customconfig.CustomConfigProviderAdaptor;
import com.sankuai.mcm.client.sdk.context.customconfig.CustomConfigProviderRequest;
import com.sankuai.mcm.client.sdk.dto.common.ChangeConfig;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;

/**
 * 自定义配置 provider
 * <AUTHOR>
 */
@McmComponent
public class ChangeEventConfigProvider extends CustomConfigProviderAdaptor {

    /**
     * 自定义变更描述
     */
    @Override
    public ChangeConfig getChangeConfig(CustomConfigProviderRequest request) {
        ChangeConfig changeConfig = new ChangeConfig();
        McmChangeConfigDTO mcmChangeConfigDTO = (McmChangeConfigDTO) request.getArgs()[0];
        changeConfig.setContent(mcmChangeConfigDTO.getChangeDescription());
        return changeConfig;
    }

}
