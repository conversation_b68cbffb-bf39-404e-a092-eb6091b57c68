package com.sankuai.meituan.banma.business.oms.common.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * 自定义序列化器，用于将JSON字符串直接输出
 * <AUTHOR>
 */
public class JsonStringSerializeHandler implements ObjectSerializer {
    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
        if (object == null) {
            serializer.write(null);
            return;
        }

        String jsonStr = (String) object;

        // 验证是否是有效的JSON
        try {
            JSON.parse(jsonStr);
            // 直接写入原始JSON字符串，不进行解析和重排序
            serializer.out.write(jsonStr);
        } catch (Exception e) {
            // 如果解析失败，按普通字符串处理
            serializer.write(jsonStr);
        }

    }
}
