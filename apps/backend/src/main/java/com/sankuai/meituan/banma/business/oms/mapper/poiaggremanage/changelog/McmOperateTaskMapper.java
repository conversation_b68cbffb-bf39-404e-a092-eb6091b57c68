package com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.changelog;

import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * mcm操作任务
 * <AUTHOR>
 */
@Mapper
public interface McmOperateTaskMapper {

    int insert(McmOperateTask mcmOperateTask);

    int updateByEventUuid(McmOperateTask mcmOperateTask);

    List<McmOperateTask> page(@Param("offset") Integer offset,
                              @Param("pageSize") Integer pageSize,
                              @Param("taskType") Integer taskType,
                              @Param("status") Integer status,
                              @Param("opMis") String opMis);

    int count(@Param("taskType") Integer taskType,
              @Param("status") Integer status,
              @Param("opMis") String opMis);
}
