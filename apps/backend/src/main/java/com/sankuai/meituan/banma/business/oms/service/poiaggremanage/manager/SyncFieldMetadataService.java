package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.retry.DefaultRetryProperties;
import com.dianping.rhino.retry.Retry;
import com.dianping.rhino.retry.RetryCallback;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.banma.business.oms.adaptor.LionAdaptor;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.constant.LionConstant;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskStatus;
import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskType;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.LionPartialOperateException;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.config.mcm.ChangeEventPreCheckHandler;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.SyncFieldMetadataDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.page.FieldCodePageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryFieldMetadata;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncFieldMetadata;
import com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.QueryFieldMetadataMapper;
import com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.SyncFieldMetadataMapper;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.changelog.McmOperateTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Validator;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 聚合查询字段元数据Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncFieldMetadataService implements IMcmHandleService {

    @Resource
    private SyncFieldMetadataService self;

    @Resource
    private SyncFieldMetadataMapper syncFieldMetadataMapper;

    @Resource
    private QueryFieldMetadataMapper queryFieldMetadataMapper;

    @Resource
    private McmOperateTaskService mcmOperateTaskService;

    @Resource
    private LionAdaptor lionAdaptor;

    @Resource
    private Validator validator;

    private static final Retry ADD_SYNC_AND_QUERY_FIELD_RETRY = Rhino.newRetry(
            LionConstant.SYNC_QUERY_RETRY_KEY,
            new DefaultRetryProperties.Setter()
                    .withActive(true)
                    .withDelay(500L)
                    .withMaxAttempts(5)
    );

    private static final Retry BATCH_DELETE_RETRY = Rhino.newRetry(
            LionConstant.SYNC_FIELD_BATCH_DELETE_RETRY_KEY,
            new DefaultRetryProperties.Setter()
                    .withActive(true)
                    .withDelay(500L)
                    .withMaxAttempts(5)
    );
    @Autowired
    private QueryFieldMetadataService queryFieldMetadataService;

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class)
    public CommonResult<Boolean> add(SyncFieldMetadataDTO syncFieldMetadataDTO) {
        SyncFieldMetadata syncFieldMetadata = new SyncFieldMetadata();
        BeanUtil.copyProperties(syncFieldMetadataDTO, syncFieldMetadata);

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        // 设置opName、opMis
        syncFieldMetadata.setOpName(user.getName());
        syncFieldMetadata.setOpMis(user.getLogin());

        try {
            int rows = syncFieldMetadataMapper.insert(syncFieldMetadata);
            if (rows <= 0) {
                return CommonResult.failed("添加失败");
            }

            // 同步到Lion配置中心
            preHandleBeforeLionSet(syncFieldMetadata);
            String lionKey = LionConstant.BM_POI_AGGRE_SYNC_FIELD_METADATA_LION_KEY_PREFIX + syncFieldMetadata.getFieldCode();
            String lionConfigString = JSON.toJSONString(syncFieldMetadata, SerializerFeature.PrettyFormat);
            boolean lionResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_SYNC_APPKEY, lionKey, lionConfigString, syncFieldMetadataDTO.getLionConfigDescription());
            if (!lionResult) {
                throw new PoiAggreQueryManagerException("同步Lion配置失败");
            }
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("add SyncFieldMetadata failed, fieldProperty: {}", syncFieldMetadata.getFieldProperty(), e);
            throw new PoiAggreQueryManagerException("添加字段元数据失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class, noRollbackFor = LionPartialOperateException.class)
    public CommonResult<Boolean> batchAdd(List<SyncFieldMetadataDTO> syncFieldMetadataDTOList) {
        User user = UserUtils.getUser();
        List<SyncFieldMetadata> syncFieldMetadataList = new ArrayList<>();
        // fieldProperty: queryFieldMetadata
        Map<String, QueryFieldMetadata> queryFieldMetadataMap = new HashMap<>();
        for (SyncFieldMetadataDTO syncFieldMetadataDTO : syncFieldMetadataDTOList) {
            SyncFieldMetadata syncFieldMetadata = new SyncFieldMetadata();
            BeanUtil.copyProperties(syncFieldMetadataDTO, syncFieldMetadata);
            syncFieldMetadata.setOpName(user.getName());
            syncFieldMetadata.setOpMis(user.getLogin());
            syncFieldMetadataList.add(syncFieldMetadata);

            if (syncFieldMetadataDTO.getSync2QueryField()) {
                QueryFieldMetadata queryFieldMetadata = new QueryFieldMetadata();
                BeanUtil.copyProperties(syncFieldMetadataDTO, queryFieldMetadata);
                queryFieldMetadata.setSyncedField(true);
                queryFieldMetadata.setOpName(user.getName());
                queryFieldMetadata.setOpMis(user.getLogin());
                queryFieldMetadataMap.put(syncFieldMetadata.getFieldProperty(), queryFieldMetadata);
            }
        }
        List<String> fieldCacheHandlerFields;
        if (queryFieldMetadataMap.isEmpty()) {
            fieldCacheHandlerFields = new ArrayList<>();
        } else {
            fieldCacheHandlerFields = Lion.getList(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, String.class);
        }
        try {
            int batchAddSyncFieldRows = syncFieldMetadataMapper.batchInsert(syncFieldMetadataList);
            int batchAddQueryFieldRows = 0;
            if (!queryFieldMetadataMap.isEmpty()) {
                batchAddQueryFieldRows = queryFieldMetadataMapper.batchInsert(new ArrayList<>(queryFieldMetadataMap.values()));
            }
            if (batchAddSyncFieldRows != syncFieldMetadataList.size() || batchAddQueryFieldRows != queryFieldMetadataMap.size()) {
                throw new PoiAggreQueryManagerException("数据库批量插入失败");
            }
            batchAddToLionWithRetry(syncFieldMetadataDTOList, syncFieldMetadataList, queryFieldMetadataMap, fieldCacheHandlerFields);
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException | LionPartialOperateException e) {
            throw e;
        } catch (Exception e) {
            log.error("#批量添加失败#, exception: {}", e.getMessage());
            throw new PoiAggreQueryManagerException("添加字段元数据失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class)
    public CommonResult<Boolean> update(SyncFieldMetadataDTO syncFieldMetadataDTO) {
        log.info("Update sync field metadata: {}", syncFieldMetadataDTO);

        SyncFieldMetadata syncFieldMetadata = new SyncFieldMetadata();
        BeanUtil.copyProperties(syncFieldMetadataDTO, syncFieldMetadata);

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        // 设置opName、opMis
        syncFieldMetadata.setOpName(user.getName());
        syncFieldMetadata.setOpMis(user.getLogin());

        try {
            int rows = syncFieldMetadataMapper.updateById(syncFieldMetadata);
            if (rows <= 0) {
                return CommonResult.failed("更新失败");
            }
            // 同步到Lion配置中心
            preHandleBeforeLionSet(syncFieldMetadata);
            String lionKey = LionConstant.BM_POI_AGGRE_SYNC_FIELD_METADATA_LION_KEY_PREFIX + syncFieldMetadata.getFieldCode();
            String lionConfigString = JSON.toJSONString(syncFieldMetadata, SerializerFeature.PrettyFormat);
            boolean lionResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_SYNC_APPKEY, lionKey, lionConfigString);
            if (!lionResult) {
                throw new PoiAggreQueryManagerException("同步Lion配置失败");
            }
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("Update sync field metadata failed", e);
            throw new PoiAggreQueryManagerException("更新字段元数据失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class, noRollbackFor = LionPartialOperateException.class)
    public CommonResult<Boolean> batchDeleteByIds(List<Long> ids) {
        log.info("Batch delete sync field metadata by ids: {}", ids);
        // 先查询所有记录，获取fieldProperty列表
        List<SyncFieldMetadata> syncFieldMetadataList = syncFieldMetadataMapper.selectByIds(ids);
        if (syncFieldMetadataList.isEmpty()) {
            return CommonResult.failed(400, "记录不存在");
        }
        try {
            int rows = syncFieldMetadataMapper.batchDeleteByIds(ids);
            if (rows <= 0) {
                return CommonResult.failed("批量删除失败");
            }
            // 同步删除Lion配置
            Set<String> failedFieldPropertySet = syncFieldMetadataList.stream().map(SyncFieldMetadata::getFieldProperty).collect(Collectors.toSet());
            BATCH_DELETE_RETRY.execute((RetryCallback<Object, Exception>) () -> {
                for (SyncFieldMetadata syncFieldMetadata : syncFieldMetadataList) {
                    if (!failedFieldPropertySet.contains(syncFieldMetadata.getFieldProperty())) {
                        continue;
                    }
                    String lionKey = LionConstant.BM_POI_AGGRE_SYNC_FIELD_METADATA_LION_KEY_PREFIX + syncFieldMetadata.getFieldCode();
                    boolean lionResult = lionAdaptor.deleteConfig(LionConstant.BM_POI_AGGRE_SYNC_APPKEY, lionKey);
                    if (lionResult) {
                        failedFieldPropertySet.remove(syncFieldMetadata.getFieldProperty());
                    }
                }
                if (failedFieldPropertySet.size() == syncFieldMetadataList.size()) {
                    log.error("批量删除同步字段全部失败");
                    throw new PoiAggreQueryManagerException(500, "批量删除同步字段全部失败, failedFieldPropertyList: " + failedFieldPropertySet);
                } else if (!failedFieldPropertySet.isEmpty()) {
                    log.error("批量删除同步字段失败, 以下同步字段删除Lion配置失败: {}", failedFieldPropertySet);
                    throw new LionPartialOperateException(500, "批量删除同步字段失败, 以下同步字段删除Lion配置失败: " + failedFieldPropertySet);
                }
                return null;
            });
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("Batch delete sync field metadata failed", e);
            throw new PoiAggreQueryManagerException("批量删除字段元数据失败", e);
        }
    }

    public CommonResult<PageResultDTO<SyncFieldMetadata>> getPage(FieldCodePageQueryDTO pageQuery) {
        log.info("Get sync field metadata page by condition: {}", pageQuery);
        // 计算偏移量
        int offset = (pageQuery.getPageNo() - 1) * pageQuery.getPageSize();
        try {
            // 查询数据
            List<SyncFieldMetadata> syncFieldMetadataList = syncFieldMetadataMapper.selectPage(
                    pageQuery.getFieldCode(),
                    pageQuery.getFieldName(),
                    pageQuery.getType(),
                    pageQuery.getValid(),
                    offset,
                    pageQuery.getPageSize()
            );

            // 查询总数
            int total = syncFieldMetadataMapper.selectPageCount(
                    pageQuery.getFieldCode(),
                    pageQuery.getFieldName(),
                    pageQuery.getType(),
                    pageQuery.getValid()
            );

            return CommonResult.success(new PageResultDTO<>(total, syncFieldMetadataList));
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("Get sync field metadata page failed", e);
            throw new PoiAggreQueryManagerException("分页查询字段元数据失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class, noRollbackFor = LionPartialOperateException.class)
    public CommonResult<Boolean> addAndSync2QueryField(SyncFieldMetadataDTO syncFieldMetadataDTO) {
        SyncFieldMetadata syncFieldMetadata = new SyncFieldMetadata();
        QueryFieldMetadata queryFieldMetadata = new QueryFieldMetadata();
        BeanUtil.copyProperties(syncFieldMetadataDTO, syncFieldMetadata);
        BeanUtil.copyProperties(syncFieldMetadataDTO, queryFieldMetadata);

        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        // 设置opName、opMis
        syncFieldMetadata.setOpName(user.getName());
        queryFieldMetadata.setOpName(user.getName());
        syncFieldMetadata.setOpMis(user.getLogin());
        queryFieldMetadata.setOpMis(user.getLogin());

        queryFieldMetadata.setSyncedField(true);

        // 同步字段[0], 查询字段[1], 同步字段列表[2]同步到lion的结果
        final Boolean[] result = {false, false, false};
        try {
            int syncRows = syncFieldMetadataMapper.insert(syncFieldMetadata);
            if (syncRows <= 0) {
                return CommonResult.failed("同步字段添加失败");
            }
            int queryRows = queryFieldMetadataMapper.insert(queryFieldMetadata);
            if (queryRows <= 0) {
                throw new PoiAggreQueryManagerException("查询字段添加失败");
            }

            // 获取插入后的记录ID
            Long syncId = syncFieldMetadata.getId();
            Long queryId = queryFieldMetadata.getId();
            if (syncId == null || queryId == null) {
                log.error("#添加失败，无法获取插入后的Id#, fieldProperty: {}", syncFieldMetadata.getFieldProperty());
                throw new PoiAggreQueryManagerException("获取记录ID失败");
            }

            // 同步到Lion
            preHandleBeforeLionSet(syncFieldMetadata);
            queryFieldMetadataService.preHandleBeforeLionSet(queryFieldMetadata);
            syncField2QueryField4LionWithRetry(syncFieldMetadataDTO, syncFieldMetadata, queryFieldMetadata, result);

            log.info("#添加字段成功#, fieldProperty: {}", syncFieldMetadata.getFieldProperty());
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException | LionPartialOperateException e) {
            throw e;
        } catch (Exception e) {
            log.error("#添加字段元数据失败#, fieldProperty: {}", syncFieldMetadata.getFieldProperty(), e);
            throw new PoiAggreQueryManagerException("添加字段元数据失败", e);
        }
    }

    /**
     * 同步字段元数据到查询字段元数据(同步字段列表)并同步到Lion配置中心
     */
    private void syncField2QueryField4LionWithRetry(SyncFieldMetadataDTO syncFieldMetadataDTO, SyncFieldMetadata syncFieldMetadata, QueryFieldMetadata queryFieldMetadata, Boolean[] result) throws Exception {
        // 同步到Lion配置中心
        String lionSyncKey = LionConstant.BM_POI_AGGRE_SYNC_FIELD_METADATA_LION_KEY_PREFIX + syncFieldMetadata.getFieldCode();
        String lionQueryKey = LionConstant.BM_POI_AGGRE_QUERY_FIELD_METADATA_LION_KEY_PREFIX + queryFieldMetadata.getFieldProperty();
        ADD_SYNC_AND_QUERY_FIELD_RETRY.execute((RetryCallback<Object, Exception>) () -> {
            if (!result[0]) {
                // 任务一：配置sync.filed
                String lionConfigString = JSON.toJSONString(syncFieldMetadata, SerializerFeature.PrettyFormat);
                result[0] = lionAdaptor.setConfig(
                        LionConstant.BM_POI_AGGRE_SYNC_APPKEY,
                        lionSyncKey, lionConfigString,
                        syncFieldMetadataDTO.getLionConfigDescription()
                );
            }
            if (result[0] && !result[1]) {
                // 任务二：配置query.filed
                String lionConfigString = JSON.toJSONString(queryFieldMetadata, SerializerFeature.PrettyFormat);
                result[1] = lionAdaptor.setConfig(
                        LionConstant.BM_POI_AGGRE_QUERY_APPKEY, lionQueryKey,
                        lionConfigString,
                        syncFieldMetadataDTO.getLionConfigDescription()
                );
            }
            if (result[1] && !result[2]) {
                // 任务三：配置field.cache
                List<String> fieldCacheHandlerFields = Lion.getList(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, String.class);
                if (!fieldCacheHandlerFields.contains(queryFieldMetadata.getFieldCode())) {
                    fieldCacheHandlerFields.add(queryFieldMetadata.getFieldCode());
                    result[2] = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, JSON.toJSONString(fieldCacheHandlerFields));
                } else {
                    result[2] = true;
                }
            }
            if (!result[0]) {
                log.error("#Lion配置失败# 同步字段结果: false, 查询字段结果: false, 同步列表结果: false");
                throw new PoiAggreQueryManagerException("#Lion配置失败# 同步字段结果: false, 查询字段结果: false, 同步列表结果: false");
            }
            if (!result[1] || !result[2]) {
                log.error("#Lion配置失败# 同步字段结果: true, 查询字段结果: {}, 同步列表结果: {}", result[1], result[2]);
                throw new LionPartialOperateException("#Lion配置失败# 同步字段结果: true, 查询字段结果: " + result[1] + ", 同步列表结果: " + result[2]);
            }
            return null;
        });
    }

    private void batchAddToLionWithRetry(List<SyncFieldMetadataDTO> syncFieldDTOList, List<SyncFieldMetadata> syncFieldList, Map<String, QueryFieldMetadata> queryFieldMap, List<String> fieldCacheHandlerFields) throws Exception {
        // 获取需要调用lion的字段的fieldProperty
        Set<String> failedFieldPropertySet = syncFieldList.stream().map(SyncFieldMetadata::getFieldProperty).collect(Collectors.toSet());
        ADD_SYNC_AND_QUERY_FIELD_RETRY.execute((RetryCallback<Object, Exception>) () -> {
            for (int idx = 0; idx < syncFieldList.size(); idx++) {
                SyncFieldMetadata syncFieldMetadata = syncFieldList.get(idx);
                if (!failedFieldPropertySet.contains(syncFieldMetadata.getFieldProperty())) {
                    continue;
                }
                // lion配置的描述
                String lionConfigDescription = syncFieldDTOList.get(idx).getLionConfigDescription();
                // 配置一：同步字段
                preHandleBeforeLionSet(syncFieldMetadata);
                String syncFieldLionKey = LionConstant.BM_POI_AGGRE_SYNC_FIELD_METADATA_LION_KEY_PREFIX + syncFieldMetadata.getFieldCode();
                Boolean addSyncFieldResult = lionAdaptor.setConfig(
                        LionConstant.BM_POI_AGGRE_SYNC_APPKEY, syncFieldLionKey,
                        JSON.toJSONString(syncFieldMetadata, SerializerFeature.PrettyFormat),
                        lionConfigDescription
                );

                // 配置二 + 配置三：查询字段
                Boolean addQueryFieldResult = !queryFieldMap.containsKey(syncFieldMetadata.getFieldProperty());
                if (!addQueryFieldResult) {
                    QueryFieldMetadata queryFieldMetadata = queryFieldMap.get(syncFieldMetadata.getFieldProperty());
                    queryFieldMetadataService.preHandleBeforeLionSet(queryFieldMetadata);
                    String queryFieldLionKey = LionConstant.BM_POI_AGGRE_QUERY_FIELD_METADATA_LION_KEY_PREFIX + syncFieldMetadata.getFieldProperty();
                    Boolean queryFieldResult = lionAdaptor.setConfig(
                            LionConstant.BM_POI_AGGRE_QUERY_APPKEY,
                            queryFieldLionKey,
                            JSON.toJSONString(queryFieldMetadata, SerializerFeature.PrettyFormat),
                            lionConfigDescription
                    );
                    Boolean addFieldCacheHandlerResult = fieldCacheHandlerFields.contains(syncFieldMetadata.getFieldCode());
                    if (!addFieldCacheHandlerResult) {
                        fieldCacheHandlerFields.add(syncFieldMetadata.getFieldCode());
                        addFieldCacheHandlerResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, JSON.toJSONString(fieldCacheHandlerFields));
                        if (!addFieldCacheHandlerResult) {
                            fieldCacheHandlerFields.remove(syncFieldMetadata.getFieldCode());
                        }
                    }
                    addQueryFieldResult = queryFieldResult && addFieldCacheHandlerResult;
                }
                if (addSyncFieldResult && addQueryFieldResult) {
                    failedFieldPropertySet.remove(syncFieldMetadata.getFieldProperty());
                }
            }
            if (failedFieldPropertySet.size() == syncFieldList.size()) {
                log.error("#批量添加字段元数据数据全部失败#");
                throw new PoiAggreQueryManagerException(500, "批量添加字段元数据全部失败, failedFieldPropertyList: " + failedFieldPropertySet);
            } else if (!failedFieldPropertySet.isEmpty()) {
                log.error("批量添加字段元数据失败, 以下字段添加Lion配置失败: {}", failedFieldPropertySet);
                throw new LionPartialOperateException(500, "批量添加字段元数据失败, 以下字段添加Lion配置失败: " + failedFieldPropertySet);
            }
            return null;
        });
    }

    @Override
    public boolean preCheck(String requestUri, McmChangeConfigDTO mcmChangeConfigDTO) {
        switch (requestUri) {
            case McmRequest.ADD:
                SyncFieldMetadataDTO syncFieldMetadataDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), SyncFieldMetadataDTO.class, false);
                addPreCheck(syncFieldMetadataDTO);
                break;
            case McmRequest.BATCH_ADD:
                Object requestData = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_ADD_PARAM_KEY);
                if (!(requestData instanceof List) || ((List<?>) requestData).isEmpty()) {
                    throw new PoiAggreQueryManagerException("请求参数类型错误");
                }
                List<SyncFieldMetadataDTO> syncFieldMetadataDTOList = ((List<?>) requestData)
                        .stream()
                        .map(obj -> BeanUtil.mapToBean((Map<String, Object>) obj, SyncFieldMetadataDTO.class, false))
                        .collect(Collectors.toList());

                batchAddPreCheck(syncFieldMetadataDTOList);

                break;
            case McmRequest.UPDATE:
                SyncFieldMetadataDTO fieldMetadataRequestData = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), SyncFieldMetadataDTO.class, false);
                updatePreCheck(fieldMetadataRequestData);
                break;
            case McmRequest.BATCH_DELETE:
                Object ids = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                if (!(ids instanceof List) || ((List<?>) ids).isEmpty()) {
                    throw new PoiAggreQueryManagerException("ids参数类型错误");
                }
                List<Long> idList = ((List<?>) ids).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());
                batchDeletePreCheck(idList);
                break;
            default:
                return false;
        }
        return true;
    }

    @Override
    @Transactional(value = "TM0", rollbackFor = Exception.class)
    public boolean addOperateTaskAndUpdateRecordStatus2Auditing(String mcmUrl, String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        // 添加任务
        McmOperateTask mcmOperateTask = getAuditingMcmOperateTask(mcmUrl, eventUuid, mcmChangeConfigDTO, McmTaskType.SYNC_FIELD.getCode());
        boolean addTaskResult = mcmOperateTaskService.addTask(mcmOperateTask);
        if (!addTaskResult) {
            return false;
        }

        String requestUri = mcmChangeConfigDTO.getRequestUri();
        List<Long> ids = ChangeEventPreCheckHandler.getChangeIds(requestUri, mcmChangeConfigDTO);
        if (ids != null) {
            int count = syncFieldMetadataMapper.batchUpdateStatus(ids, McmRequest.MCM_STATUS_CODE_AUDITING);
            if (count != ids.size()) {
                throw new PoiAggreQueryManagerException("审核发起成功，更新状态失败");
            }
        }
        return true;
    }

    @Override
    @Transactional(value = "TM0", rollbackFor = Exception.class, noRollbackFor = PoiAggreQueryManagerException.class)
    public void mcmAuditAccepted(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            int count = syncFieldMetadataMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
            if (count != changeIdList.size()) {
                throw new PoiAggreQueryManagerException("状态修改失败");
            }
        }

        SyncFieldMetadataDTO syncFieldMetadataDTO;
        CommonResult<Boolean> res;
        try {
            switch (mcmChangeConfigDTO.getRequestUri()) {
                case McmRequest.ADD:
                    syncFieldMetadataDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), SyncFieldMetadataDTO.class, false);
                    if (syncFieldMetadataDTO.getSync2QueryField()) {
                        res = self.addAndSync2QueryField(syncFieldMetadataDTO);
                    } else {
                        res = self.add(syncFieldMetadataDTO);
                    }
                    break;
                case McmRequest.BATCH_ADD:
                    Object requestData = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_ADD_PARAM_KEY);
                    List<SyncFieldMetadataDTO> syncFieldMetadataDTOList = ((List<?>) requestData)
                            .stream()
                            .map(obj -> BeanUtil.mapToBean((Map<String, Object>) obj, SyncFieldMetadataDTO.class, false))
                            .collect(Collectors.toList());
                    res = self.batchAdd(syncFieldMetadataDTOList);
                    break;
                case McmRequest.UPDATE:
                    syncFieldMetadataDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), SyncFieldMetadataDTO.class, false);
                    res = self.update(syncFieldMetadataDTO);

                    break;
                case McmRequest.BATCH_DELETE:
                    Object ids = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                    if (!(ids instanceof List) || ((List<?>) ids).isEmpty()) {
                        throw new PoiAggreQueryManagerException("ids参数类型错误");
                    }
                    List<Long> idList = ((List<?>) ids).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());
                    res = self.batchDeleteByIds(idList);

                    break;
                default:
                    throw new PoiAggreQueryManagerException("未知请求类型");
            }
            if (res.getCode() != 0) {
                throw new PoiAggreQueryManagerException("回调处理失败");
            }
        } catch (Exception e) {
            McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.EXCEPTION.getCode());
            mcmOperateTaskService.updateTask(mcmOperateTask);
            throw e;
        }
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.ACCEPTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);
    }

    @Override
    public void mcmAuditRejected(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.REJECTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);

        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            syncFieldMetadataMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
        }
    }

    @Override
    public void mcmAuditCancel(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.CANCELED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);

        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            syncFieldMetadataMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
        }
    }

    public void batchAddPreCheck(List<SyncFieldMetadataDTO> syncFieldMetadataDTOList) {
        BindingResult bindingResult;
        for (SyncFieldMetadataDTO dto : syncFieldMetadataDTOList) {
            bindingResult = new BeanPropertyBindingResult(dto, "syncFieldMetadataDTO");
            validator.validate(dto, bindingResult);
            if (bindingResult.hasErrors()) {
                throw new PoiAggreQueryManagerException(400, "参数校验失败");
            }
        }

        SyncFieldMetadata syncRecord;
        QueryFieldMetadata queryRecord = null;
        Set<String> notExistRecords = new HashSet<>();
        for (SyncFieldMetadataDTO dto : syncFieldMetadataDTOList) {
            // 校验fieldCode、fieldProperty是否重复
            syncRecord = syncFieldMetadataMapper.selectByFieldCodeOrFieldProperty(dto.getFieldCode(), dto.getFieldProperty());
            if (dto.getSync2QueryField()) {
                queryRecord = queryFieldMetadataMapper.selectByFieldCodeOrFieldProperty(dto.getFieldCode(), dto.getFieldProperty());
            }
            if (syncRecord == null && queryRecord == null) {
                // 组内校验重复
                notExistRecords.add(dto.getFieldCode());
            }
        }
        if (notExistRecords.size() != syncFieldMetadataDTOList.size()) {
            Set<String> existRecords = syncFieldMetadataDTOList.stream()
                    .map(SyncFieldMetadataDTO::getFieldCode)
                    .filter(fieldCode -> !notExistRecords.contains(fieldCode))
                    .collect(Collectors.toSet());
            throw new PoiAggreQueryManagerException(400, "字段Code或字段property已存在##" + JSON.toJSONString(existRecords));
        }
    }

    public void addPreCheck(SyncFieldMetadataDTO syncFieldMetadataDTO) {
        BindingResult bindingResult;
        bindingResult = new BeanPropertyBindingResult(syncFieldMetadataDTO, "syncFieldMetadataDTO");
        validator.validate(syncFieldMetadataDTO, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new PoiAggreQueryManagerException(400, "参数校验失败");
        }
        // 校验fieldCode、fieldProperty是否重复
        SyncFieldMetadata existingRecord = syncFieldMetadataMapper.selectByFieldCodeOrFieldProperty(syncFieldMetadataDTO.getFieldCode(), syncFieldMetadataDTO.getFieldProperty());
        if (existingRecord != null) {
            throw new PoiAggreQueryManagerException(400, "字段Code已存在");
        }
        if (syncFieldMetadataDTO.getSync2QueryField()) {
            QueryFieldMetadata existingQueryField = queryFieldMetadataMapper.selectByFieldCodeOrFieldProperty(syncFieldMetadataDTO.getFieldCode(), syncFieldMetadataDTO.getFieldProperty());
            if (existingQueryField != null) {
                throw new PoiAggreQueryManagerException(400, "字段Code已存在");
            }
        }
    }

    public void batchDeletePreCheck(List<Long> idList) {
        // 先查询所有记录，获取fieldProperty列表
        List<SyncFieldMetadata> syncFieldMetadataList = syncFieldMetadataMapper.selectByIds(idList);
        if (syncFieldMetadataList.size() != idList.size()) {
            throw new PoiAggreQueryManagerException(400, "不能删除不存在的数据！");
        }
        syncFieldMetadataList.forEach(field -> {
            if (field.getStatus() == 1) {
                throw new PoiAggreQueryManagerException(400, "有数据正在审核中，请重新选择！");
            }
        });
    }

    public void updatePreCheck(SyncFieldMetadataDTO fieldMetadataRequestData) {
        BindingResult bindingResult;
        if (fieldMetadataRequestData.getId() == null) {
            throw new PoiAggreQueryManagerException(400, "id不能为空");
        }
        bindingResult = new BeanPropertyBindingResult(fieldMetadataRequestData, "syncFieldMetadataDTO");
        validator.validate(fieldMetadataRequestData, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new PoiAggreQueryManagerException(400, "参数校验失败");
        }
        // 校验fieldProperty是否已存在
        SyncFieldMetadata otherRecord = syncFieldMetadataMapper.selectByFieldCodeOrFieldProperty(fieldMetadataRequestData.getFieldCode(), fieldMetadataRequestData.getFieldProperty());
        SyncFieldMetadata syncFieldMetadata = syncFieldMetadataMapper.selectById(fieldMetadataRequestData.getId());

        if (syncFieldMetadata == null) {
            throw new PoiAggreQueryManagerException(400, "请求参数异常，id不存在");
        } else if (syncFieldMetadata.getStatus() == 1) {
            throw new PoiAggreQueryManagerException(400, "数据正在审核中，禁止操作！");
        } else if (otherRecord != null && !Objects.equals(syncFieldMetadata.getId(), otherRecord.getId())) {
            throw new PoiAggreQueryManagerException(400, "字段Property已存在");
        }
    }

    private static void preHandleBeforeLionSet(SyncFieldMetadata syncFieldMetadata) {
        syncFieldMetadata.setId(null);
        syncFieldMetadata.setValid(null);
    }

}