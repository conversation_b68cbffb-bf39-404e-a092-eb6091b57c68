package com.sankuai.meituan.banma.business.oms.common.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * Map<String, Object>类型转换器
 * 用于处理数据库VARCHAR类型和Java Map<String, Object>类型之间的转换
 * 使用JSON格式进行序列化和反序列化
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(Map.class)
@Slf4j
public class MapTypeHandler extends BaseTypeHandler<Map<String, Object>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, Object> parameter, JdbcType jdbcType)
            throws SQLException {
        // 将Map转换为JSON字符串
        String value = JSON.toJSONString(parameter);
        ps.setString(i, value);
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return convertToMap(rs.getString(columnName));
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return convertToMap(rs.getString(columnIndex));
    }

    @Override
    public Map<String, Object> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return convertToMap(cs.getString(columnIndex));
    }

    private Map<String, Object> convertToMap(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            // 使用 TypeReference 来正确处理复杂类型的转换
            Map<String, Object> retMap = JSON.parseObject(value, new TypeReference<Map<String, Object>>() {}, Feature.OrderedField);
            return retMap;
        } catch (Exception e) {
            // 如果解析失败，返回空Map
            log.error("convertToMap error, value:{}", value, e);
            return new HashMap<>();
        }
    }
} 