package com.sankuai.meituan.banma.business.oms.service;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Demo服务接口
 */
@Service
public class DemoService {

    /**
     * 模拟数据库中的场景数据
     */
    private static final List<Map<String, Object>> MOCK_SCENE_DATA = new ArrayList<>();

    static {
        // 初始化模拟数据
        for (int i = 1; i <= 50; i++) {
            Map<String, Object> scene = new HashMap<>();
            scene.put("id", i);
            scene.put("sceneName", "场景" + i);
            scene.put("sceneDescription", "这是场景" + i + "的描述信息，用于展示场景的详细信息。");
            scene.put("sceneLevel", i % 10);
            scene.put("appkeys", "app1,app2,app" + i);
            scene.put("fieldCodes", "field1,field2,field" + i);
            scene.put("administrator", "admin1,admin2,admin" + i);
            scene.put("estimateQps", 100 + i * 10);
            scene.put("actualQps", 80 + i * 8);
            scene.put("opName", "操作员" + i);
            scene.put("opMis", "mis" + i);
            scene.put("valid", i % 3 == 0 ? 0 : 1);
            scene.put("ctime", System.currentTimeMillis() / 1000 - i * 86400);
            scene.put("utime", System.currentTimeMillis() / 1000 - i * 3600);
            MOCK_SCENE_DATA.add(scene);
        }
    }

    public Map<String, Object> queryScenePage(String sceneName, Integer sceneLevel, Integer valid, int pageNum, int pageSize) {
        // 过滤数据
        List<Map<String, Object>> filteredList = MOCK_SCENE_DATA.stream()
                .filter(scene -> {
                    boolean match = true;

                    // 按场景名称过滤
                    if (sceneName != null && !sceneName.isEmpty()) {
                        match = match && scene.get("sceneName").toString().contains(sceneName);
                    }

                    // 按场景级别过滤
                    if (sceneLevel != null) {
                        match = match && sceneLevel.equals(scene.get("sceneLevel"));
                    }

                    // 按有效性过滤
                    if (valid != null) {
                        match = match && valid.equals(scene.get("valid"));
                    }

                    return match;
                })
                .collect(Collectors.toList());

        // 计算总数
        int total = filteredList.size();

        // 分页
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        // 截取当前页数据
        List<Map<String, Object>> pageData = start < end
                ? filteredList.subList(start, end)
                : new ArrayList<>();

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("list", pageData);

        return result;
    }

    public Map<String, Object> querySceneDetail(Long id) {
        // 查找指定ID的场景
        return MOCK_SCENE_DATA.stream()
                .filter(scene -> id.equals(scene.get("id")))
                .findFirst()
                .orElse(new HashMap<>());
    }
} 