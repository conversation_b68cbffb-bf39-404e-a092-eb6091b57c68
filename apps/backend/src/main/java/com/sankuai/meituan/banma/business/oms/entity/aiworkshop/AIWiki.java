package com.sankuai.meituan.banma.business.oms.entity.aiworkshop;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class AIWiki {

    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;

    /**
     * 描述
     */
    @NotBlank(message = "描述不能为空")
    private String description;

    /**
     * wiki地址
     */
    @NotBlank(message = "wiki地址不能为空")
    private String wikiUrl;

    /**
     * 其他配置
     */
    private Map<String, Object> options;

    /**
     * 类型：1-AI产品，2-AI分享，3-入门指南，4-进阶资料
     */
    @NotBlank(message = "类型不能为空")
    private Integer type;

    /**
     * 星标，0-否，1-是
     */
    private Integer star;

    /**
     * 置顶，0-否，1-是
     */
    private Integer top;

    /**
     * 发布人姓名
     */
    private String pbName;

    /**
     * 部门
     */
    private String department;

    /**
     * wiki标签，使用、分割
     */
    private List<String> tags;

    /**
     * 有效性
     */
    private Integer valid;

    /**
     * 发布人mis
     */
    private String pbMis;

    /**
     * 操作人姓名
     */
    private String opName;

    /**
     * 操作人mis
     */
    private String opMis;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 更新时间
     */
    private Integer utime;
}
