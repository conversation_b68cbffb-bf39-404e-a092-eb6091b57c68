package com.sankuai.meituan.banma.business.oms.common.handler;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.List;

/**
 * 编排字段序列化器
 * 用于将List<String>序列化为逗号分隔的字符串
 * <AUTHOR>
 */
public class FieldCodesSerializeHandler implements ObjectSerializer {
    
    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
        if (object == null) {
            serializer.writeNull();
            return;
        }
        
        if (!(object instanceof List)) {
            serializer.write(object);
            return;
        }
        
        List<?> list = (List<?>) object;
        if (list.isEmpty()) {
            serializer.write("");
            return;
        }
        
        // 将List中的元素转换为字符串并用逗号连接
        String result = list.stream()
                .map(item -> item != null ? item.toString() : "")
                .filter(s -> !s.isEmpty())
                .reduce((a, b) -> a + "," + b)
                .orElse("");
                
        serializer.write(result);
    }
} 