package com.sankuai.meituan.banma.business.oms.service;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.banma.business.oms.adaptor.UacAuthAdaptor;
import com.sankuai.meituan.banma.business.oms.common.dto.UserInfoDTO;
import com.sankuai.meituan.uac.sdk.entity.menu.UserMenu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;

/**
 * 用户服务类
 */
@Service
public class UserService {

    @Autowired
    private UacAuthAdaptor uacAuthAdaptor;

    /**
     * 获取当前用户的菜单树
     * @return 用户菜单树
     */
    public UserMenu getMenuTree() {
        // 获取当前用户
        User user = UserUtils.getUser();
        if (user == null) {
            return null;
        }
        // 调用适配器获取用户菜单
        return uacAuthAdaptor.getUserMenu(String.valueOf(user.getId()));
    }
    
    /**
     * 获取当前用户信息
     * @return 用户信息DTO
     */
    public UserInfoDTO getCurrentUserInfo() {
        // 获取当前用户
        User user = UserUtils.getUser();
        if (user == null) {
            return null;
        }
        
        // 构建用户信息DTO
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setId(user.getId());
        userInfoDTO.setLogin(user.getLogin());
        userInfoDTO.setName(user.getName());
        userInfoDTO.setCode(user.getCode());
        userInfoDTO.setEmail(user.getEmail());
        userInfoDTO.setTenantId(user.getTenantId());
        userInfoDTO.setRoles(user.getRoles() != null ? user.getRoles() : new ArrayList<>());
        userInfoDTO.setIsVerified(user.isVerified);
        userInfoDTO.setVerifyType(user.verifyType);
        userInfoDTO.setVerifyExpireTime(user.verifyExpireTime);
        userInfoDTO.setPassport(user.getPassport());
        
        return userInfoDTO;
    }
} 