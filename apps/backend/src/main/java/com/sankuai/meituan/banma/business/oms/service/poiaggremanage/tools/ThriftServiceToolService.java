package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.tools;

import com.sankuai.meituan.banma.business.oms.adaptor.RocketHostAdaptor;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.JsonSchemaDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.ReturnTypeDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.ServiceInterfaceDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.ServiceMethodDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools.RocketHost;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Thrift服务工具Service
 * <AUTHOR>
 */
@Service
public class ThriftServiceToolService {
    
    private static final Logger logger = LoggerFactory.getLogger(ThriftServiceToolService.class);
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 服务接口信息URL
    private static final String SERVICE_INTERFACE_URI_FORMAT = "http://%s:5080/service.info";
    
    // 服务方法列表URL
    private static final String SERVICE_METHOD_URI_FORMAT = "http://%s:5080/method.info";
    
    // 方法JSON Schema URL
    private static final String METHOD_JSON_SCHEMA_URI_FORMAT = 
            "http://%s:5080/method.jsonSchema?serviceName=%s&methodName=%s";


    /**
     * 获取服务接口信息
     * @param appkey 应用key
     * @param env 环境
     * @return 服务接口信息
     */
    public List<RocketHost> getAppkeyHosts(String appkey, String env) {
        logger.info("获取服务接口信息, appkey: {}, env: {}", appkey, env);
        if (StringUtils.isBlank(appkey)) {
            return null;
        }
        return RocketHostAdaptor.getAppkeyHosts(appkey, env);
    }

    
    /**
     * 获取服务接口信息
     * @param hostName 主机名称/IP
     * @return 服务接口信息
     */
    public ServiceInterfaceDTO getServiceInterfaces(String hostName) {
        String url = String.format(SERVICE_INTERFACE_URI_FORMAT, hostName);
        logger.info("调用Thrift服务接口API: {}", url);
        
        try {
            ResponseEntity<ServiceInterfaceDTO> response = restTemplate.getForEntity(url, ServiceInterfaceDTO.class);
            
            if (response.getStatusCode() != HttpStatus.OK) {
                throw new PoiAggreQueryManagerException(
                        String.format("获取服务接口信息失败，状态码：%s", response.getStatusCode()));
            }
            
            ServiceInterfaceDTO serviceInterfaceDTO = response.getBody();
            
            // 对serviceInfo进行过滤，只保留serviceIfaceInfos不为null且status为ALIVE的服务
            if (serviceInterfaceDTO != null && serviceInterfaceDTO.getServiceInfo() != null) {
                List<ServiceInterfaceDTO.ServiceInfo> filteredServiceInfo = serviceInterfaceDTO.getServiceInfo().stream()
                        .filter(info -> info.getServiceIfaceInfos() != null && "ALIVE".equals(info.getStatus()))
                        .collect(Collectors.toList());
                
                // 更新过滤后的服务信息
                serviceInterfaceDTO.setServiceInfo(filteredServiceInfo);
                logger.info("过滤后的服务数量: {}", filteredServiceInfo.size());
                
                // 按照接口全路径名称去重
                deduplicateServiceInterfaces(serviceInterfaceDTO);
                
                // 扁平化处理，将嵌套接口列表提取为单层列表
                flattenServiceInterfaces(serviceInterfaceDTO);
            }
            
            return serviceInterfaceDTO;
        } catch (Exception e) {
            logger.error("获取服务接口信息异常", e);
            throw new PoiAggreQueryManagerException("获取服务接口信息异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 对服务接口列表进行去重处理
     * 按照接口全路径名称（serviceName）进行去重
     * @param serviceInterfaceDTO 服务接口DTO
     */
    private void deduplicateServiceInterfaces(ServiceInterfaceDTO serviceInterfaceDTO) {
        if (serviceInterfaceDTO == null || serviceInterfaceDTO.getServiceInfo() == null) {
            return;
        }
        
        // 记录处理前的接口总数
        int beforeCount = serviceInterfaceDTO.getServiceInfo().stream()
                .mapToInt(info -> info.getServiceIfaceInfos() != null ? info.getServiceIfaceInfos().size() : 0)
                .sum();
        
        // 用于存储去重后的接口信息
        java.util.Map<String, ServiceInterfaceDTO.ServiceIfaceInfo> uniqueInterfaces = new java.util.HashMap<>();
        
        // 遍历所有服务信息，收集唯一接口
        for (ServiceInterfaceDTO.ServiceInfo serviceInfo : serviceInterfaceDTO.getServiceInfo()) {
            if (serviceInfo.getServiceIfaceInfos() != null) {
                // 保存该服务的端口信息，用于组装更新后的ServiceInfo
                String port = serviceInfo.getPort();
                String status = serviceInfo.getStatus();
                
                // 按serviceName去重，并保留遇到的第一个接口实例
                for (ServiceInterfaceDTO.ServiceIfaceInfo iface : serviceInfo.getServiceIfaceInfos()) {
                    uniqueInterfaces.putIfAbsent(iface.getServiceName(), iface);
                }
            }
        }
        
        // 创建一个新的ServiceInfo，包含所有去重后的接口
        ServiceInterfaceDTO.ServiceInfo combinedInfo = new ServiceInterfaceDTO.ServiceInfo();
        combinedInfo.setPort("combined"); // 使用一个特殊标记，表示这是合并后的结果
        combinedInfo.setStatus("ALIVE");
        combinedInfo.setServiceIfaceInfos(new java.util.ArrayList<>(uniqueInterfaces.values()));
        
        // 更新DTO，只保留一个包含所有去重接口的ServiceInfo
        serviceInterfaceDTO.setServiceInfo(java.util.Collections.singletonList(combinedInfo));
        
        // 记录处理信息
        int afterCount = uniqueInterfaces.size();
        logger.info("接口去重: 原始数量 = {}, 去重后数量 = {}", beforeCount, afterCount);
    }
    
    /**
     * 对服务接口数据进行扁平化处理
     * 将嵌套的服务接口列表提取成单层列表结构，方便前端直接使用
     * @param serviceInterfaceDTO 服务接口DTO
     */
    private void flattenServiceInterfaces(ServiceInterfaceDTO serviceInterfaceDTO) {
        if (serviceInterfaceDTO == null || serviceInterfaceDTO.getServiceInfo() == null || serviceInterfaceDTO.getServiceInfo().isEmpty()) {
            return;
        }
        
        // 收集所有接口
        List<ServiceInterfaceDTO.ServiceIfaceInfo> allInterfaces = new java.util.ArrayList<>();
        
        for (ServiceInterfaceDTO.ServiceInfo info : serviceInterfaceDTO.getServiceInfo()) {
            if (info.getServiceIfaceInfos() != null) {
                allInterfaces.addAll(info.getServiceIfaceInfos());
            }
        }
        
        // 创建一个新的ServiceInfo对象，包含所有扁平化后的接口列表
        ServiceInterfaceDTO.ServiceInfo flattenedInfo = new ServiceInterfaceDTO.ServiceInfo();
        flattenedInfo.setPort("flattened");
        flattenedInfo.setStatus("ALIVE");
        flattenedInfo.setServiceIfaceInfos(allInterfaces);
        
        // 更新DTO，只保留这一个包含所有接口的ServiceInfo
        serviceInterfaceDTO.setServiceInfo(java.util.Collections.singletonList(flattenedInfo));
        logger.info("扁平化处理后的接口总数: {}", allInterfaces.size());
    }
    
    /**
     * 获取服务方法信息
     * @param hostName 主机名称/IP
     * @param serviceName 服务名称
     * @return 服务方法信息
     */
    public ServiceMethodDTO getServiceMethods(String hostName, String serviceName) {
        String url = String.format(SERVICE_METHOD_URI_FORMAT, hostName);
        logger.info("调用Thrift服务方法API: {}, serviceName: {}", url, serviceName);
        
        try {
            ResponseEntity<ServiceMethodDTO> response = restTemplate.getForEntity(url, ServiceMethodDTO.class);
            
            if (response.getStatusCode() != HttpStatus.OK) {
                throw new PoiAggreQueryManagerException(
                        String.format("获取服务方法信息失败，状态码：%s", response.getStatusCode()));
            }
            
            ServiceMethodDTO serviceMethodDTO = response.getBody();
            
            // 只保留指定服务名称的方法信息
            if (serviceMethodDTO != null && serviceMethodDTO.getServiceMethods() != null && serviceName != null) {
                // 找到指定服务名称的方法信息
                List<ServiceMethodDTO.ServiceMethod> filteredMethods = serviceMethodDTO.getServiceMethods().stream()
                        .filter(method -> serviceName.equals(method.getServiceName()))
                        .collect(Collectors.toList());
                
                // 更新DTO，只保留指定服务的方法信息
                serviceMethodDTO.setServiceMethods(filteredMethods);
                logger.info("过滤后的服务方法数量: {}", filteredMethods.size());
            }
            
            return serviceMethodDTO;
        } catch (Exception e) {
            logger.error("获取服务方法信息异常", e);
            throw new PoiAggreQueryManagerException("获取服务方法信息异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取方法参数JSON Schema
     * @param hostName 主机名称/IP
     * @param serviceName 服务名称
     * @param methodName 方法名称（可能包含参数部分）
     * @return 方法参数JSON Schema
     */
    public JsonSchemaDTO getMethodJsonSchema(String hostName, String serviceName, String methodName) {
        // 处理方法名，提取纯方法名（去掉参数部分）
        String purifiedMethodName = methodName;
        if (methodName != null && methodName.contains("(")) {
            purifiedMethodName = methodName.substring(0, methodName.indexOf("("));
            logger.info("从方法名中提取纯方法名: {} -> {}", methodName, purifiedMethodName);
        }
        
        String url = String.format(METHOD_JSON_SCHEMA_URI_FORMAT, hostName, serviceName, purifiedMethodName);
        logger.info("调用Thrift服务Schema API: {}", url);
        
        try {
            ResponseEntity<JsonSchemaDTO> response = restTemplate.getForEntity(url, JsonSchemaDTO.class);
            ResponseEntity<String> response2 = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() != HttpStatus.OK) {
                throw new PoiAggreQueryManagerException(
                        String.format("获取方法参数Schema失败，状态码：%s", response.getStatusCode()));
            }
            
            JsonSchemaDTO jsonSchemaDTO = response.getBody();
            
            // 增强JsonSchemaDTO，添加returnTypeRef和category字段
            if (jsonSchemaDTO != null && jsonSchemaDTO.getMethods() != null && !jsonSchemaDTO.getMethods().isEmpty()) {
                for (JsonSchemaDTO.MethodInfo methodInfo : jsonSchemaDTO.getMethods()) {
                    // 设置returnTypeRef，指向返回类型
                    methodInfo.setReturnTypeRef(methodInfo.getReturnType());
                    logger.info("设置方法 {} 的返回类型引用: {}", methodInfo.getName(), methodInfo.getReturnTypeRef());
                    
                    // 如果有类型信息，为每个类型添加category标识
                    if (jsonSchemaDTO.getTypes() != null) {
                        // 先标记参数类型
                        if (methodInfo.getParameterTypes() != null) {
                            for (String paramType : methodInfo.getParameterTypes()) {
                                for (JsonSchemaDTO.TypeInfo typeInfo : jsonSchemaDTO.getTypes()) {
                                    if (typeInfo.getType() != null && typeInfo.getType().equals(paramType)) {
                                        typeInfo.setCategory("parameter");
                                        logger.info("标记参数类型: {}", paramType);
                                    }
                                }
                            }
                        }
                        
                        // 再标记返回类型
                        if (methodInfo.getReturnType() != null) {
                            String returnType = methodInfo.getReturnType();
                            
                            // 直接匹配
                            boolean foundReturnType = false;
                            for (JsonSchemaDTO.TypeInfo typeInfo : jsonSchemaDTO.getTypes()) {
                                if (typeInfo.getType() != null && typeInfo.getType().equals(returnType)) {
                                    typeInfo.setCategory("return");
                                    foundReturnType = true;
                                    logger.info("标记返回类型: {}", returnType);
                                }
                            }
                            
                            // 如果没有直接匹配，尝试匹配简单类名
                            if (!foundReturnType && returnType.contains(".")) {
                                String simpleReturnType = returnType.substring(returnType.lastIndexOf(".") + 1);
                                for (JsonSchemaDTO.TypeInfo typeInfo : jsonSchemaDTO.getTypes()) {
                                    if (typeInfo.getType() != null) {
                                        String typeSimpleName = typeInfo.getType().contains(".") 
                                            ? typeInfo.getType().substring(typeInfo.getType().lastIndexOf(".") + 1) 
                                            : typeInfo.getType();
                                        
                                        if (typeSimpleName.equals(simpleReturnType)) {
                                            typeInfo.setCategory("return");
                                            foundReturnType = true;
                                            logger.info("通过简单类名标记返回类型: {} -> {}", returnType, typeInfo.getType());
                                        }
                                    }
                                }
                            }
                            
                            // 如果仍未找到，尝试查找包含返回类型名称的类型
                            if (!foundReturnType) {
                                for (JsonSchemaDTO.TypeInfo typeInfo : jsonSchemaDTO.getTypes()) {
                                    if (typeInfo.getType() != null && 
                                        (typeInfo.getType().endsWith(returnType) || 
                                         (returnType.contains(".") && typeInfo.getType().endsWith(returnType.substring(returnType.lastIndexOf(".") + 1))))) {
                                        typeInfo.setCategory("return");
                                        logger.info("通过名称包含关系标记返回类型: {} -> {}", returnType, typeInfo.getType());
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 生成返回类型信息
                ReturnTypeDTO returnTypeDTO = buildReturnType(jsonSchemaDTO);
                jsonSchemaDTO.setReturnType(returnTypeDTO);
            }
            
            return jsonSchemaDTO;
        } catch (Exception e) {
            logger.error("获取方法参数Schema异常", e);
            throw new PoiAggreQueryManagerException("获取方法参数Schema异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建返回类型信息
     * @param jsonSchemaDTO 原始的JSON Schema信息
     * @return 返回类型信息
     */
    private ReturnTypeDTO buildReturnType(JsonSchemaDTO jsonSchemaDTO) {
        if (jsonSchemaDTO == null || jsonSchemaDTO.getMethods() == null || jsonSchemaDTO.getMethods().isEmpty()) {
            logger.warn("构建返回类型失败: jsonSchemaDTO为空或无方法信息");
            return null;
        }
        
        // 设置当前处理的JsonSchemaDTO，供递归查找类型时使用
        this.currentJsonSchemaDTO = jsonSchemaDTO;
        
        JsonSchemaDTO.MethodInfo methodInfo = jsonSchemaDTO.getMethods().get(0);
        String returnType = methodInfo.getReturnType();
        
        if (returnType == null) {
            logger.warn("构建返回类型失败: 返回类型为空");
            return null;
        }
        
        logger.info("开始构建返回类型, returnType: {}", returnType);
        
        ReturnTypeDTO returnTypeDTO = new ReturnTypeDTO();
        returnTypeDTO.setTypeName(returnType);
        
        // 设置简化类型名（去掉包名）
        String simpleTypeName = returnType.contains(".") 
            ? returnType.substring(returnType.lastIndexOf(".") + 1) 
            : returnType;
        returnTypeDTO.setSimpleTypeName(simpleTypeName);
        
        // 检查是否为基本类型
        boolean isPrimitive = isPrimitiveType(returnType);
        returnTypeDTO.setIsPrimitive(isPrimitive);
        
        // 检查是否为集合类型
        boolean isCollection = isCollectionType(returnType);
        returnTypeDTO.setIsCollection(isCollection);
        
        logger.info("类型分析结果: isPrimitive={}, isCollection={}", isPrimitive, isCollection);
        
        if (isPrimitive) {
            // 基本类型处理
            returnTypeDTO.setDescription("基本数据类型：" + simpleTypeName);
            returnTypeDTO.setExampleValue(getPrimitiveExampleValue(returnType));
            logger.info("处理基本类型完成");
        } else if (isCollection) {
            // 集合类型处理
            String elementType = extractElementType(returnType);
            returnTypeDTO.setElementType(elementType);
            returnTypeDTO.setDescription("集合类型，元素类型：" + elementType);
            returnTypeDTO.setExampleValue("[]");
            logger.info("处理集合类型完成, elementType: {}", elementType);
        } else {
            // 复杂对象类型处理
            logger.info("开始处理复杂对象类型");
            JsonSchemaDTO.TypeInfo returnTypeInfo = findReturnTypeInfo(jsonSchemaDTO, returnType);
            
            if (returnTypeInfo != null) {
                logger.info("找到返回类型信息: {}", returnTypeInfo.getType());
                logger.info("类型属性数量: {}", returnTypeInfo.getProperties() != null ? returnTypeInfo.getProperties().size() : 0);
                
                returnTypeDTO.setDescription("复杂对象类型：" + simpleTypeName);
                
                // 检查是否为枚举类型
                if (returnTypeInfo.getEnums() != null && !returnTypeInfo.getEnums().isEmpty()) {
                    logger.info("处理枚举类型, 枚举值数量: {}", returnTypeInfo.getEnums().size());
                    returnTypeDTO.setIsEnum(true);
                    List<String> enumValues = returnTypeInfo.getEnums().stream()
                        .map(Object::toString)
                        .collect(Collectors.toList());
                    returnTypeDTO.setEnumValues(enumValues);
                    returnTypeDTO.setExampleValue(enumValues.get(0));
                } else {
                    // 处理对象字段
                    logger.info("开始构建字段信息映射");
                    returnTypeDTO.setIsEnum(false);
                    Map<String, ReturnTypeDTO.FieldInfo> fields = buildFieldInfoMap(returnTypeInfo);
                    logger.info("字段信息映射构建完成, 字段数量: {}", fields.size());
                    
                    if (!fields.isEmpty()) {
                        logger.info("字段列表: {}", String.join(", ", fields.keySet()));
                    }
                    
                    returnTypeDTO.setFields(fields);
                    returnTypeDTO.setExampleValue(buildExampleObject(fields));
                }
            } else {
                logger.warn("未找到返回类型信息: {}", returnType);
                returnTypeDTO.setDescription("未知类型：" + simpleTypeName);
                returnTypeDTO.setExampleValue(null);
            }
        }
        
        // 清理引用
        this.currentJsonSchemaDTO = null;
        
        logger.info("返回类型构建完成");
        return returnTypeDTO;
    }
    
    /**
     * 查找返回类型信息
     */
    private JsonSchemaDTO.TypeInfo findReturnTypeInfo(JsonSchemaDTO jsonSchemaDTO, String returnType) {
        if (jsonSchemaDTO.getTypes() == null) {
            return null;
        }
        
        // 直接匹配
        for (JsonSchemaDTO.TypeInfo typeInfo : jsonSchemaDTO.getTypes()) {
            if (typeInfo.getType() != null && typeInfo.getType().equals(returnType)) {
                return typeInfo;
            }
        }
        
        // 简化类名匹配
        if (returnType.contains(".")) {
            String simpleReturnType = returnType.substring(returnType.lastIndexOf(".") + 1);
            for (JsonSchemaDTO.TypeInfo typeInfo : jsonSchemaDTO.getTypes()) {
                if (typeInfo.getType() != null) {
                    String typeSimpleName = typeInfo.getType().contains(".") 
                        ? typeInfo.getType().substring(typeInfo.getType().lastIndexOf(".") + 1) 
                        : typeInfo.getType();
                    
                    if (typeSimpleName.equals(simpleReturnType)) {
                        return typeInfo;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 构建字段信息映射
     */
    private Map<String, ReturnTypeDTO.FieldInfo> buildFieldInfoMap(JsonSchemaDTO.TypeInfo typeInfo) {
        return buildFieldInfoMap(typeInfo, new java.util.HashSet<>(), 0);
    }
    
    /**
     * 构建字段信息映射（带递归保护）
     * @param typeInfo 类型信息
     * @param visitedTypes 已访问的类型（防止循环引用）
     * @param depth 当前递归深度（防止过深递归）
     */
    private Map<String, ReturnTypeDTO.FieldInfo> buildFieldInfoMap(JsonSchemaDTO.TypeInfo typeInfo, 
                                                                              java.util.Set<String> visitedTypes, 
                                                                              int depth) {
        Map<String, ReturnTypeDTO.FieldInfo> fields = new LinkedHashMap<>();
        
        logger.info("构建字段信息映射, 类型: {}, 深度: {}, 已访问类型数: {}", 
                typeInfo.getType(), depth, visitedTypes.size());
        
        if (typeInfo.getProperties() == null || depth > 3) { // 限制递归深度为3层
            if (typeInfo.getProperties() == null) {
                logger.warn("类型 {} 没有properties字段", typeInfo.getType());
            }
            if (depth > 3) {
                logger.warn("递归深度超过限制: {}", depth);
            }
            return fields;
        }
        
        logger.info("类型 {} 有 {} 个属性", typeInfo.getType(), typeInfo.getProperties().size());
        
        for (Map.Entry<String, Object> entry : typeInfo.getProperties().entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();
            
            logger.info("处理字段: {}", fieldName);
            
            // 跳过技术性字段
            if (fieldName.equals("__isset_bit_vector") || fieldName.equals("optionals")) {
                logger.info("跳过技术性字段: {}", fieldName);
                continue;
            }
            
            ReturnTypeDTO.FieldInfo fieldInfo = new ReturnTypeDTO.FieldInfo();
            
            if (fieldValue instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> fieldMap = (Map<String, Object>) fieldValue;
                String fieldType = (String) fieldMap.get("type");
                
                if (fieldType != null) {
                    logger.info("字段 {} 类型: {}", fieldName, fieldType);
                    
                    fieldInfo.setType(fieldType);
                    fieldInfo.setSimpleType(fieldType.contains(".") 
                        ? fieldType.substring(fieldType.lastIndexOf(".") + 1) 
                        : fieldType);
                    fieldInfo.setIsPrimitive(isPrimitiveType(fieldType));
                    fieldInfo.setIsCollection(isCollectionType(fieldType));
                    fieldInfo.setRequired(true); // 默认为必需
                    
                    // 防止循环引用
                    if (visitedTypes.contains(fieldType)) {
                        logger.warn("检测到循环引用: {}", fieldType);
                        fieldInfo.setDescription("循环引用类型：" + fieldInfo.getSimpleType());
                        fieldInfo.setExampleValue("...");
                        fields.put(fieldName, fieldInfo);
                        continue;
                    }
                    
                    // 增强字段描述和示例值处理
                    if (isPrimitiveType(fieldType)) {
                        logger.info("字段 {} 是基本类型", fieldName);
                        fieldInfo.setDescription("基本类型");
                        fieldInfo.setExampleValue(getFieldExampleValue(fieldType));
                    } else if (isCollectionType(fieldType)) {
                        logger.info("字段 {} 是集合类型", fieldName);
                        // 处理集合类型
                        fieldInfo.setIsCollection(true);
                        String elementType = extractElementType(fieldType);
                        fieldInfo.setElementType(elementType);
                        
                        String elementSimpleType = elementType.contains(".") 
                            ? elementType.substring(elementType.lastIndexOf(".") + 1)
                            : elementType;
                        
                        if (isPrimitiveType(elementType)) {
                            fieldInfo.setDescription("基本类型集合");
                            fieldInfo.setExampleValue(java.util.Collections.singletonList(getFieldExampleValue(elementType)));
                        } else {
                            // 复杂对象集合，查找元素类型的详细信息
                            JsonSchemaDTO.TypeInfo elementTypeInfo = findTypeByName(elementType);
                            if (elementTypeInfo != null) {
                                logger.info("找到集合元素类型信息: {}", elementType);
                                // 创建新的访问集合，包含当前类型
                                java.util.Set<String> newVisitedTypes = new java.util.HashSet<>(visitedTypes);
                                newVisitedTypes.add(fieldType);
                                
                                Map<String, ReturnTypeDTO.FieldInfo> elementFields = 
                                    buildFieldInfoMap(elementTypeInfo, newVisitedTypes, depth + 1);
                                
                                fieldInfo.setNestedFields(elementFields);
                                fieldInfo.setDescription("对象集合 (" + elementFields.size() + " 个字段)");
                                
                                // 构建集合示例，包含一个示例元素
                                if (!elementFields.isEmpty()) {
                                    Object elementExample = buildExampleObject(elementFields);
                                    fieldInfo.setExampleValue(java.util.Collections.singletonList(elementExample));
                                } else {
                                    fieldInfo.setExampleValue(java.util.Collections.emptyList());
                                }
                            } else {
                                logger.warn("未找到集合元素类型信息: {}", elementType);
                                fieldInfo.setDescription("集合类型");
                                fieldInfo.setExampleValue(java.util.Collections.emptyList());
                            }
                        }
                    } else {
                        logger.info("字段 {} 是复杂对象类型", fieldName);
                        // 复杂对象类型，查找其详细信息
                        JsonSchemaDTO.TypeInfo nestedTypeInfo = findTypeByName(fieldType);
                        if (nestedTypeInfo != null) {
                            logger.info("找到嵌套对象类型信息: {}", fieldType);
                            // 创建新的访问集合，包含当前类型
                            java.util.Set<String> newVisitedTypes = new java.util.HashSet<>(visitedTypes);
                            newVisitedTypes.add(fieldType);
                            
                            Map<String, ReturnTypeDTO.FieldInfo> nestedFields = 
                                buildFieldInfoMap(nestedTypeInfo, newVisitedTypes, depth + 1);
                            
                            fieldInfo.setNestedFields(nestedFields);
                            fieldInfo.setDescription("对象类型 (" + nestedFields.size() + " 个字段)");
                            
                            if (!nestedFields.isEmpty()) {
                                fieldInfo.setExampleValue(buildExampleObject(nestedFields));
                            } else {
                                fieldInfo.setExampleValue(null);
                            }
                        } else {
                            logger.warn("未找到嵌套对象类型信息: {}", fieldType);
                            fieldInfo.setDescription("对象类型");
                            fieldInfo.setExampleValue(null);
                        }
                    }
                } else {
                    logger.warn("字段 {} 的类型信息为空", fieldName);
                }
            } else {
                logger.warn("字段 {} 的值不是Map类型: {}", fieldName, fieldValue.getClass().getSimpleName());
            }
            
            fields.put(fieldName, fieldInfo);
            logger.info("字段 {} 处理完成", fieldName);
        }
        
        logger.info("字段信息映射构建完成, 最终字段数量: {}", fields.size());
        return fields;
    }
    
    /**
     * 当前处理的JsonSchemaDTO（用于在递归查找类型时使用）
     */
    private JsonSchemaDTO currentJsonSchemaDTO;
    
    /**
     * 根据类型名称查找类型信息
     */
    private JsonSchemaDTO.TypeInfo findTypeByName(String typeName) {
        if (currentJsonSchemaDTO == null || currentJsonSchemaDTO.getTypes() == null || typeName == null) {
            return null;
        }
        
        // 直接匹配
        for (JsonSchemaDTO.TypeInfo typeInfo : currentJsonSchemaDTO.getTypes()) {
            if (typeInfo.getType() != null && typeInfo.getType().equals(typeName)) {
                return typeInfo;
            }
        }
        
        // 简化类名匹配
        if (typeName.contains(".")) {
            String simpleTypeName = typeName.substring(typeName.lastIndexOf(".") + 1);
            for (JsonSchemaDTO.TypeInfo typeInfo : currentJsonSchemaDTO.getTypes()) {
                if (typeInfo.getType() != null) {
                    String typeSimpleName = typeInfo.getType().contains(".") 
                        ? typeInfo.getType().substring(typeInfo.getType().lastIndexOf(".") + 1) 
                        : typeInfo.getType();
                    
                    if (typeSimpleName.equals(simpleTypeName)) {
                        return typeInfo;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 构建示例对象
     */
    private Object buildExampleObject(Map<String, ReturnTypeDTO.FieldInfo> fields) {
        Map<String, Object> example = new LinkedHashMap<>();
        
        for (Map.Entry<String, ReturnTypeDTO.FieldInfo> entry : fields.entrySet()) {
            String fieldName = entry.getKey();
            ReturnTypeDTO.FieldInfo fieldInfo = entry.getValue();
            
            if (fieldInfo.getExampleValue() != null) {
                example.put(fieldName, fieldInfo.getExampleValue());
            }
        }
        
        return example;
    }
    
    /**
     * 判断是否为基本类型
     */
    private boolean isPrimitiveType(String type) {
        if (type == null) return false;
        
        return type.equals("string") || type.equals("int") || type.equals("long") ||
               type.equals("double") || type.equals("float") || type.equals("boolean") ||
               type.equals("char") || type.equals("byte") || type.equals("short") ||
               type.equals("java.lang.String") || type.equals("java.lang.Integer") ||
               type.equals("java.lang.Long") || type.equals("java.lang.Double") ||
               type.equals("java.lang.Float") || type.equals("java.lang.Boolean");
    }
    
    /**
     * 判断是否为集合类型
     */
    private boolean isCollectionType(String type) {
        if (type == null) return false;
        
        return type.startsWith("java.util.List") || type.startsWith("java.util.Set") ||
               type.startsWith("java.util.Collection") || type.contains("List<") ||
               type.contains("Set<") || type.contains("Collection<");
    }
    
    /**
     * 提取集合元素类型
     */
    private String extractElementType(String collectionType) {
        if (collectionType == null) return "Object";
        
        int startIndex = collectionType.indexOf('<');
        int endIndex = collectionType.lastIndexOf('>');
        
        if (startIndex > 0 && endIndex > startIndex) {
            return collectionType.substring(startIndex + 1, endIndex);
        }
        
        return "Object";
    }
    
    /**
     * 获取基本类型的示例值
     */
    private Object getPrimitiveExampleValue(String type) {
        if (type == null) return null;
        
        switch (type.toLowerCase()) {
            case "string":
            case "java.lang.string":
                return "示例字符串";
            case "int":
            case "integer":
            case "java.lang.integer":
                return 0;
            case "long":
            case "java.lang.long":
                return 0L;
            case "double":
            case "java.lang.double":
                return 0.0;
            case "float":
            case "java.lang.float":
                return 0.0f;
            case "boolean":
            case "java.lang.boolean":
                return false;
            default:
                return null;
        }
    }
    
    /**
     * 获取字段的示例值
     */
    private Object getFieldExampleValue(String fieldType) {
        if (isPrimitiveType(fieldType)) {
            return getPrimitiveExampleValue(fieldType);
        } else if (isCollectionType(fieldType)) {
            return java.util.Collections.emptyList();
        } else {
            return null; // 复杂对象类型
        }
    }
} 