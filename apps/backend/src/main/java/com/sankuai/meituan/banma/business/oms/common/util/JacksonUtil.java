package com.sankuai.meituan.banma.business.oms.common.util;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonParser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;




public class JacksonUtil {
    private static final Logger log = LoggerFactory.getLogger(JacksonUtil.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final Logger logger = LoggerFactory.getLogger(JacksonUtil.class);

    public JacksonUtil() {
    }

    public static String serialize(Object result) throws JsonProcessingException {
        return mapper.writeValueAsString(result);
    }

    public static String serializeWithoutException(Object result) {
        try {
            return mapper.writeValueAsString(result);
        } catch (JsonProcessingException var2) {
            JsonProcessingException e = var2;
            logger.error("JacksonUtil.serializeWitoutException", e);
            return null;
        }
    }

    public static <T> T deserializeWithoutException(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        } else {
            try {
                return mapper.readValue(jsonString, clazz);
            } catch (Exception var3) {
                Exception e = var3;
                logger.error("JacksonUtil.deserializeWithoutException. jsonString:{}", jsonString, e);
                return null;
            }
        }
    }

    public static <T> T deserialize(String jsonString, Class<T> clazz) throws IOException {
        return StringUtils.isBlank(jsonString) ? null : mapper.readValue(jsonString, clazz);
    }

    public static <T> T deserialize(String jsonString, JavaType type) throws IOException {
        return StringUtils.isBlank(jsonString) ? null : mapper.readValue(jsonString, type);
    }

    public static <T> T deserializeList(String jsonString, Class<?> elementClass) throws IOException {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        } else {
            JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, new Class[]{elementClass});
            return mapper.readValue(jsonString, javaType);
        }
    }

    public static <T> T deserializeMap(String jsonString, Class<?> keyClass, Class<?> valueClass) throws IOException {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        } else {
            JavaType javaType = mapper.getTypeFactory().constructParametricType(Map.class, new Class[]{keyClass, valueClass});
            return mapper.readValue(jsonString, javaType);
        }
    }

    public static <T> T deserializeMapWithListValue(String jsonString, Class<?> keyClass, Class<?> elementClass) throws IOException {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        } else {
            JavaType inner = mapper.getTypeFactory().constructCollectionType(ArrayList.class, elementClass);
            JavaType key = mapper.constructType(keyClass);
            JavaType javaType = mapper.getTypeFactory().constructParametricType(Map.class, new JavaType[]{key, inner});
            return mapper.readValue(jsonString, javaType);
        }
    }

    public static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        return mapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }

    static {
        mapper.setSerializationInclusion(Include.ALWAYS);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        mapper.configure(Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
    }
}
