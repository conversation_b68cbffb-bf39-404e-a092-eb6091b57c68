package com.sankuai.meituan.banma.business.oms.common.constant;

public interface PoiAggreJsonKey {

    String MAFKA_CONSUME_CONFIG = "mafkaConsumeConfig";

    String DTS_SYNC_CONFIG = "dtsSyncConfig";

    String DSL = "dsl";

    String ACCESS_WAY = "accessWay";
    String REQUIREMENT_INFO = "requirementInfo";
    String SYNC_FIELD_LIST = "syncFieldList";
    String SYNC_CONFIG = "syncConfig";
    String PROVIDE_QUERY_SERVICE = "provideQueryService";
    String QUERY_FIELD_LIST = "queryFieldList";
    String DSL_CONFIG = "dslConfig";
    String DTS_SUBSCRIPTION_URL = "dtsSubscriptionUrl";
    String CRANE_URL_INFO = "craneUrlInfo";
}
