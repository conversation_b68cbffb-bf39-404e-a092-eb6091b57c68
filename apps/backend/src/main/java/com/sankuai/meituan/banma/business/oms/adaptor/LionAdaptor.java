package com.sankuai.meituan.banma.business.oms.adaptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.lion.client.api.Response;
import com.dianping.lion.client.api.service.ConfigAPIForm;
import com.dianping.lion.client.api.service.ConfigRepositoryService;
import com.dianping.zebra.util.StringUtils;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class LionAdaptor {

    @Resource
    private ConfigRepositoryService configRepositoryService;

    @Data
    public static class SetConfigResponse {
        private String key;
        private String name;
        private Boolean success;
        private String message;
    }

    public Boolean setConfig(String appkey, String key, String value) {
        return setConfig(appkey, key, value, "");
    }

    public Boolean setConfig(String appkey, String key, String value, String description) {
        // 结果值: prod、staging、ppe、test、dev
        String envStr = MdpContextUtils.getHostEnvStr();
        if ("dev".equals(envStr)) {
            envStr = "test";
        }

        ConfigAPIForm form = new ConfigAPIForm();
        form.setKey(key);
        form.setValue(value);
        if (description == null) {
            form.setDesc("");
        } else {
            form.setDesc(description);
        }

        // @param env           环境，可选值: dev、test、staging、prod，不支持跨线上线下访问(线下--test/dev，线上--prod/staging)
        Response response = configRepositoryService.set(envStr, appkey, form);

        String rawValue = response.getValue();
        if (StringUtils.isBlank(rawValue)) {
            log.error("同步Lion配置失败，response: {}", response);
            return false;
        }
        SetConfigResponse setConfigResponse = com.sankuai.it.iam.common.base.gson.bridge.JSON.parseObject(rawValue, SetConfigResponse.class);
        return setConfigResponse.getSuccess();
    }

    public Boolean deleteConfig(String appkey, String key) {
        // 结果值: prod、staging、ppe、test、dev
        String envStr = MdpContextUtils.getHostEnvStr();
        if ("dev".equals(envStr)) {
            envStr = "test";
        }

        ConfigAPIForm form = new ConfigAPIForm();
        form.setKey(key);

        // @param env 环境，可选值: dev、test、staging、prod，不支持跨线上线下访问(线下--test/dev，线上--prod/staging)
        Response response = configRepositoryService.delete(envStr, appkey, key, form);

        String resultString = response.getValue();
        if (StringUtils.isBlank(resultString)) {
            return false;
        }
        return Boolean.parseBoolean(resultString);
    }

    /**
     * 获取JSON字符串，处理由序列化导致的转义问题
     * @param config JSON对象
     * @return JSON字符串
     */
    public String getLionConfigString(Object config) {
        String dslConfigJsonString = JSON.toJSONString(config);
//        dslConfigJsonString = dslConfigJsonString
//                .replace("\\\"", "\"")
//                .replace("\\n", "\n")
//                .replace("\\t", "\t")
//                .replace("\\\\", "\\");

        // 格式化
        Object jsonObject;
        if (config instanceof List) {
            jsonObject = JSON.parse(dslConfigJsonString);
        } else {
            jsonObject = JSON.parseObject(dslConfigJsonString, new TypeReference<LinkedHashMap<String, Object>>() {}, Feature.OrderedField);
        }
        dslConfigJsonString =  JSON.toJSONString(jsonObject, SerializerFeature.PrettyFormat);
        dslConfigJsonString = dslConfigJsonString
                .replace("\\\"", "\"")
                .replace("\\n", "\n")
                .replace("\\t", "\t")
                .replace("\\\\", "\\");

        return dslConfigJsonString;
    }
}
