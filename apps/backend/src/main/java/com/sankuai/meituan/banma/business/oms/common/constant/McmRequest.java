package com.sankuai.meituan.banma.business.oms.common.constant;

/**
 * 请求uri（方法上）
 * <AUTHOR>
 */
public interface McmRequest {

    String POI_AGGRE_MANAGE_QUERY_FIELD_BASE_URL = "/api/businessoms/poiAggreManage/manager/query/field/metadata";

    String POI_AGGRE_MANAGE_QUERY_DSL_BASE_URL = "/api/businessoms/poiAggreManage/manager/query/dsl";

    String POI_AGGRE_MANAGE_SYNC_FIELD_BASE_URL = "/api/businessoms/poiAggreManage/manager/sync/field/metadata";

    String POI_AGGRE_MANAGE_SYNC_CONFIG_BASE_URL = "/api/businessoms/poiAggreManage/manager/sync/config";

    String POI_AGGRE_MANAGE_ACCESS_GUIDE_BASE_URL = "/api/businessoms/poiAggreManage/manager/accessGuide";

    String ADD = "/add";
    String BATCH_ADD = "/batchAdd";
    String UPDATE = "/update";
    String BATCH_DELETE = "/deleteByIds";

    String BATCH_DELETE_PARAM_KEY = "ids";
    String BATCH_ADD_PARAM_KEY = "batchAddList";

    Integer MCM_STATUS_CODE_AUDITING = 1;

    Integer MCM_STATUS_CODE_AUDIT_COMPLETED = 0;
}
