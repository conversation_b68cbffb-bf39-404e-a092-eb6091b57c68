package com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog;

import lombok.Data;

/**
 * 操作任务
 * <AUTHOR>
 */
@Data
public class McmOperateTask {

    private Long id;

    /**
     * 任务类型，1-查询字段、2-服务编排、3-同步字段、4-同步配置
     */
    private Integer taskType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * mcm链接
     */
    private String mcmUrl;

    /**
     * mcm事件uuid
     */
    private String mcmEventUuid;

    /**
     * 任务状态，0-通过、1-进行中、2-撤销、3-驳回
     */
    private Integer status;

    /**
     * 有效性
     */
    private Integer valid;

    /**
     * 操作人姓名
     */
    private String opName;

    /**
     * 操作人mis
     */
    private String opMis;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 更新时间
     */
    private Integer utime;

}
