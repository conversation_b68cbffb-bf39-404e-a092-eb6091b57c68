package com.sankuai.meituan.banma.business.oms.common.constant;

import lombok.Getter;

/**
 * Mcm任务类型
 * <AUTHOR>
 */
@Getter
public enum McmTaskType {

    QUERY_FIELD(1, "查询字段"),
    DSL(2, "服务编排-DSL"),
    SYNC_FIELD(3, "同步字段"),
    SYNC_CONFIG(4, "同步配置"),
    ACCESS_GUIDE(5, "接入指引");

    private final Integer code;

    private final String desc;

    McmTaskType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (McmTaskType value : McmTaskType.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
