package com.sankuai.meituan.banma.business.oms.config;

import com.dianping.lion.client.api.LionAPI;
import com.dianping.lion.client.api.service.ConfigRepositoryService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class LionServiceConfig {

    private final String lionUsername;
    private final String lionPassword;

    public LionServiceConfig(
            @Value("${lion.username}") String lionUsername,
            @Value("${lion.password}") String lionPassword) {
        this.lionUsername = lionUsername;
        this.lionPassword = lionPassword;
    }

    @Bean
    public ConfigRepositoryService configRepositoryService() {
        LionAPI lionAPI = LionAPI.createBuilder().setName(lionUsername).setPassword(lionPassword).build();
        return lionAPI.create(ConfigRepositoryService.class);
    }

}
