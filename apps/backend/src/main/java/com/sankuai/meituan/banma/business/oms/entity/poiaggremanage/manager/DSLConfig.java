package com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.meituan.banma.business.oms.common.handler.FieldCodesSerializeHandler;
import com.sankuai.meituan.banma.business.oms.common.handler.JsonStringSerializeHandler;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * DSL配置实体类
 * <AUTHOR>
 */
@Data
public class DSLConfig {

    @JSONField(serialize = false)
    private Long id;

    /**
     * 编排名称
     */
    @JSONField(ordinal = 1)
    private String dslName;

    /**
     * 编排描述
     */
    @JSONField(ordinal = 2)
    private String dslDescription;

    /**
     * 编排DSL
     */
    @JSONField(ordinal = 3, serializeUsing = JsonStringSerializeHandler.class)
    private String dsl;

    /**
     * 编排字段
     */
    @JSONField(ordinal = 4, serializeUsing = FieldCodesSerializeHandler.class)
    private List<String> fieldCodes;

    /**
     * 是否抛出异常
     */
    @JSONField(ordinal = 5)
    private Boolean thrownException;

    /**
     * 预热参数
     */
    @JSONField(ordinal = 6)
    private String dslPreHeatParam;

    /**
     * 其他参数（后续新增）
     */
    @JSONField(ordinal = 7)
    private Map<String, Object> option;

    /**
     * 支持QPS
     */
    @JSONField(serialize = false)
    private Integer supportQps;

    /**
     * 实际QPS
     */
    @JSONField(serialize = false)
    private Integer actualQps;

    /**
     * 有效性 0:无效;1:有效
     */
    @JSONField(serialize = false)
    private Integer valid;

    /**
     * 状态，0-正常，1-审核中
     */
    @JSONField(serialize = false)
    private Integer status;

    /**
     * 操作人姓名
     */
    @JSONField(serialize = false)
    private String opName;

    /**
     * 操作人mis
     */
    @JSONField(serialize = false)
    private String opMis;

    /**
     * 创建时间
     */
    @JSONField(serialize = false)
    private Integer ctime;

    /**
     * 更新时间
     */
    @JSONField(serialize = false)
    private Integer utime;
}
