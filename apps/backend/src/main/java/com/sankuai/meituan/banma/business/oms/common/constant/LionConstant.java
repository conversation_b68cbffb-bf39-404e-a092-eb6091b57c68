package com.sankuai.meituan.banma.business.oms.common.constant;

/**
 * <AUTHOR>
 */
public interface LionConstant {

    String BM_POI_AGGRE_QUERY_APPKEY = "com.sankuai.deliverybusiness.poiaggre.query";

    String BM_POI_AGGRE_SYNC_APPKEY = "com.sankuai.deliverybusiness.poi.sync";

    // 同步过来的字段列表key
    String BM_POI_AGGRE_SYNC_FIELD_LIST_KEY = "poi.aggre.field.cache.handler.fields";

    // Field MataData配置前缀
    String BM_POI_AGGRE_QUERY_FIELD_METADATA_LION_KEY_PREFIX = "bm.poi.aggre.query.field.metadata.";

    // 同步字段元数据配置前缀
    String BM_POI_AGGRE_SYNC_FIELD_METADATA_LION_KEY_PREFIX = "bm.poi.aggre.sync.field.metadata.";

    // 聚合查询DSL配置前缀
    String BM_POI_AGGRE_QUERY_DSL_CONFIG_LION_KEY_PREFIX = "bm.poi.aggre.query.dsl.config.";

    String BM_POI_AGGRE_QUERY_SYNC_CONFIG_LION_KEY_PREFIX = "bm.poi.aggre.query.sync.config.";

    // 配置返回成功结果key
    String SET_CONFIG_RESULT_KEY = "success";

    // 重试key
    String QUERY_RETRY_KEY = "query-field-retryer";

    String SYNC_QUERY_RETRY_KEY = "sync-and-query-field-retryer";

    String DSL_BATCH_DELETE_RETRY_KEY = "dsl-batch-delete-retryer";
    
    String QUERY_FIELD_BATCH_DELETE_RETRY_KEY = "query-field-batch-delete-retryer";
    
    String SYNC_CONFIG_BATCH_DELETE_RETRY_KEY = "sync-config-batch-delete-retryer";

    String SYNC_FIELD_BATCH_DELETE_RETRY_KEY = "sync-field-batch-delete-retryer";
}
