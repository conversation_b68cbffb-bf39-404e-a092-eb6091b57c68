package com.sankuai.meituan.banma.business.oms.thrift;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;

@ThriftService
public interface CommonSqlExecuteIface {
    /**
     * description "Execute a select SQL query and return results in a readable format. Results will be truncated after 4000 characters."
     * @param sql SQL query statement
     * @return String
     */
    @ThriftMethod
    String queryDB(String sql, String jdbcRef);

    /**
     * description "Explain a SQL query and return results in a readable format."
     * @param sql SQL query statement
     * @return String
     */
    @ThriftMethod
    String explainSql(String sql, String jdbcRef);

    @ThriftMethod
    String listAllTableSchema();

    /**
     * 将秒级时间戳转换为易读的日期时间格式(yyyy-MM-dd HH:mm:ss)，方便查询结果中的时间戳展示。
     * @param timestamp 秒级时间戳
     * @return 易读的日期时间格式
     */
    @ThriftMethod
    String formatTimestamp(Long timestamp);

}
