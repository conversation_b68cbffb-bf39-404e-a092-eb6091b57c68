package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.tools;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.util.DefaultIndenter;
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.sankuai.meituan.banma.service.orchestration.model.Outputs;
import com.sankuai.meituan.banma.service.orchestration.model.Task;
import com.sankuai.meituan.banma.service.orchestration.model.Workflow;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Map;

public class DSLUtil {

    private static ObjectMapper prettyMapper = new ObjectMapper();

    static {
        // 配置专门用于格式化输出的 mapper
        prettyMapper.enable(SerializationFeature.INDENT_OUTPUT);
        prettyMapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
        prettyMapper.configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, false);
        prettyMapper.configure(JsonGenerator.Feature.ESCAPE_NON_ASCII, false);
        prettyMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        prettyMapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);

        // 自定义 PrettyPrinter，去掉所有不必要的空格
        DefaultPrettyPrinter prettyPrinter = new DefaultPrettyPrinter() {
            // 缩进器
            private final DefaultIndenter indenter = new DefaultIndenter("  ", "\n");

            @Override
            public DefaultPrettyPrinter createInstance() {
                return new DefaultPrettyPrinter(this);
            }

            // 重写所有可能添加空格的方法
            @Override
            public void writeObjectFieldValueSeparator(JsonGenerator g) throws IOException {
                g.writeRaw(':'); // 只写冒号，不加空格
            }

            @Override
            public void writeObjectEntrySeparator(JsonGenerator g) throws IOException {
                g.writeRaw(',');
                g.writeRaw(indenter.getEol());
            }

            @Override
            public void writeStartObject(JsonGenerator g) throws IOException {
                g.writeRaw('{');
                _objectIndenter.writeIndentation(g, _nesting + 1);
            }

            @Override
            public void writeEndObject(JsonGenerator g, int nrOfEntries) throws IOException {
                if (nrOfEntries > 0) {
                    _objectIndenter.writeIndentation(g, _nesting);
                }
                g.writeRaw('}');
            }

            @Override
            public void writeArrayValueSeparator(JsonGenerator g) throws IOException {
                g.writeRaw(',');
                _arrayIndenter.writeIndentation(g, _nesting);
            }

            @Override
            public void writeStartArray(JsonGenerator g) throws IOException {
                g.writeRaw('[');
                _arrayIndenter.writeIndentation(g, _nesting + 1);
            }

            @Override
            public void writeEndArray(JsonGenerator g, int nrOfValues) throws IOException {
                if (nrOfValues > 0) {
                    _arrayIndenter.writeIndentation(g, _nesting);
                }
                g.writeRaw(']');
            }
        };

        // 设置缩进
        DefaultIndenter indenter = new DefaultIndenter("  ", "\n");
        prettyPrinter.indentObjectsWith(indenter);
        prettyPrinter.indentArraysWith(indenter);

        // 设置自定义的 PrettyPrinter
        prettyMapper.setDefaultPrettyPrinter(prettyPrinter);

        // 禁用JsonGenerator中的QUOTE_FIELD_NAMES特性可能会影响序列化
        prettyMapper.configure(JsonGenerator.Feature.QUOTE_FIELD_NAMES, true);

        // 注册自定义序列化器
        prettyMapper.registerModule(new SimpleModule()
                .addSerializer(Task.class, getTaskSerializer())
                .addSerializer(Outputs.class, getOutputsSerializer())
                .addSerializer(Workflow.class, getWorkflowSerializer()));
    }

    /**
     * 将对象转换为格式化的 JSON 字符串，保留复杂字符串中的换行符
     * 特别适用于 Workflow 对象的序列化
     *
     * @param obj 要序列化的对象
     * @return 格式化的 JSON 字符串
     */
    public static String toFormattedJson(Object obj) {
        try {
            String json = prettyMapper.writeValueAsString(obj);
            // 对于某些对象可能仍然会有空格，这里通过正则表达式移除字段名和冒号之间的空格
            return json.replaceAll("\"([^\"]+)\"\\s*:", "\"$1\":");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static boolean isJson(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            JsonNode jsonNode = prettyMapper.readTree(str);
            // 只有是对象或数组才返回 true
            return jsonNode.isObject() || jsonNode.isArray();
        } catch (Exception e) {
            return false;
        }
    }



    /**
     * 写入行复杂字符串（单行或多行）
     *
     * @param gen   JsonGenerator
     * @throws IOException 如果写入失败
     */
    private static void writeRawComplexLineString(JsonGenerator gen, String fieldName, String fieldValue) throws IOException {
        if (StringUtils.isBlank(fieldName) || StringUtils.isBlank(fieldValue)) {
            return;
        }
        if (fieldValue.contains("\n") || fieldValue.contains("\\n")) {
            // 处理多行字符串
            writeRawMultiLineString(gen, fieldName, fieldValue);
        } else {
            // 处理单行字符串
            writeRawSingleLineString(gen, fieldName, fieldValue);
        }
    }
    private static void writeRawSingleLineString(JsonGenerator gen, String fieldName, String fieldValue) throws IOException {
        // 处理开头和结尾的\"
        if (fieldValue.startsWith("\"") && fieldValue.endsWith("\"")) {
            gen.writeFieldName(fieldName);
            gen.writeRawValue(fieldValue);
        } else {
            gen.writeStringField(fieldName, fieldValue);
        }
    }
    private static void writeRawMultiLineString(JsonGenerator gen, String fieldName, String fieldValue) throws IOException {
        // 写入字段名
        gen.writeFieldName(fieldName);
        
        // 使用另一种方式来输出包含实际换行符的JSON字符串
        // 通过先构造完整的JSON字符串内容，然后使用writeRawValue输出
        StringBuilder processedJson = new StringBuilder();
        
        // 处理转义的换行符，将其转换为真正的换行符
        if (fieldValue.contains("\\n")) {
            fieldValue = fieldValue.replace("\\n", "\n");
        }

        // 处理开头和结尾的引号
        if (fieldValue.startsWith("\"")) {
            fieldValue = "\n" + fieldValue.substring(2);
        }
        if (fieldValue.endsWith("\"")) {
            fieldValue = fieldValue.substring(0, fieldValue.length() - 1) + "\n";
        }
        
        // 开始构建JSON字符串
        processedJson.append('"');
        
        // 处理每一行
        String[] lines = fieldValue.split("\n");
        for (int i = 0; i < lines.length; i++) {
            // 转义当前行中的双引号和反斜杠
            String line = lines[i].replace("\\", "\\\\").replace("\"", "\\\"");
            processedJson.append(line);
            
            // 如果不是最后一行，添加实际的换行符
            if (i < lines.length - 1) {
                processedJson.append('\n');
            }
        }
        
        // 结束JSON字符串
        processedJson.append('"');
        
        // 使用writeRawValue输出完整的JSON字符串内容
        // 这会保留实际的换行符，同时确保JSON生成器状态正确
        gen.writeRawValue(processedJson.toString());
    }

    /**
     * 获取专门用于格式化输出的 Task 序列化器
     * 保留复杂字符串中的换行符
     */
    private static JsonSerializer getTaskSerializer() {
        return new JsonSerializer<Task>() {
            @Override
            public void serialize(Task value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                gen.writeStartObject();
                Field[] fields = Task.class.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    // 跳过Jacoco插桩字段
                    if ("$jacocoData".equals(fieldName)) continue;
                    try {
                        Object fieldValue = field.get(value);
                        if (fieldValue != null) {
                            if ("inputs".equals(fieldName) || "inputsExtra".equals(fieldName)) {
                                if (fieldValue == null) {
                                    continue;
                                }

                                if (isJson((String) fieldValue)) {
                                    gen.writeFieldName(fieldName);
                                    // 如果是 JSON 字符串，解析为 Map 并逐个处理字段
                                    Map<String, Object> inputsMap = prettyMapper.readValue((String) fieldValue, Map.class);

                                    gen.writeStartObject();

                                    // 处理每个字段
                                    for (Map.Entry<String, Object> entry : inputsMap.entrySet()) {
                                        String key = entry.getKey();
                                        Object val = entry.getValue();

                                        // 特殊处理字符串类型的值，尤其是多行字符串
                                        if (val instanceof String) {
                                            writeRawComplexLineString(gen, key, (String) val);
                                        } else {
                                            gen.writeObjectField(key, val);
                                        }
                                    }

                                    gen.writeEndObject();
                                } else if (fieldValue instanceof String) {
                                    // 处理字符串类型的 inputs
                                    writeRawComplexLineString(gen, fieldName, (String) fieldValue);
                                } else {
                                    // 非字符串类型正常处理
                                    gen.writeObjectField(fieldName, fieldValue);
                                }
                            }
                            else if ("timeout".equals(fieldName)) {
                                if (value.getTimeout() > 0 && value.getTimeout() != 1000) {
                                    gen.writeNumberField("timeout", value.getTimeout());
                                }
                            }
                            else if ("ignoreException".equals(fieldName)) {
                                if (value.isIgnoreException()) {
                                    gen.writeBooleanField("ignoreException", value.isIgnoreException());
                                }
                            }
                            else if ("switchExpression".equals(fieldName)) {
                                if (StringUtils.isNotBlank(value.getSwitchExpression())) {
                                    gen.writeStringField("switch", value.getSwitchExpression());
                                }
                            }
                            else if ("dependencyTaskMap".equals(fieldName) || "level".equals(fieldName)) {
                                // 不输出这些字段
                            }
                            else {
                                gen.writeObjectField(fieldName, fieldValue);
                            }
                        }
                    } catch (Exception e) {
                        // 处理异常
                        e.printStackTrace();
                    }
                }
                gen.writeEndObject();
            }
        };
    }


    /**
     * 获取专门用于格式化输出的 Task 序列化器
     * 保留复杂字符串中的换行符
     */
    private static JsonSerializer getOutputsSerializer() {
        return new JsonSerializer<Outputs>() {
            @Override
            public void serialize(Outputs value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                gen.writeStartObject();
                Field[] fields = Outputs.class.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    // 跳过Jacoco插桩字段
                    if ("$jacocoData".equals(fieldName)) continue;
                    try {
                        Object fieldValue = field.get(value);
                        if (fieldValue != null) {
                            if ("transform".equals(fieldName)) {
                                writeRawComplexLineString(gen, fieldName, (String) fieldValue);
                            }
                            else if ("type".equals(fieldName)) {
                                if (StringUtils.isNotBlank((String) fieldValue)) {
                                    gen.writeObjectField(fieldName, fieldValue);
                                }
                            }
                            else if ("variables".equals(fieldName)) {
                                if (value.getVariables() != null && value.getVariables().size() > 0) {
                                    gen.writeObjectField(fieldName, fieldValue);
                                }
                            }
                            else if ("switchExpression".equals(fieldName)) {
                                if (StringUtils.isNotBlank(value.getSwitchExpression())) {
                                    gen.writeStringField("switch", value.getSwitchExpression());
                                }
                            }
                            else {
                                gen.writeObjectField(fieldName, fieldValue);
                            }
                        }
                    } catch (Exception e) {
                        // 处理异常
                        e.printStackTrace();
                    }
                }
                gen.writeEndObject();
            }
        };
    }


    /**
     * 获取 Workflow 序列化器，用于处理 outputs 字段中的多行字符串
     */
    private static JsonSerializer getWorkflowSerializer() {
        return new JsonSerializer<Workflow>() {
            @Override
            public void serialize(Workflow value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                gen.writeStartObject();

                // 序列化基本字段
                gen.writeStringField("name", value.getName());
                gen.writeStringField("description", value.getDescription());
                gen.writeNumberField("timeout", value.getTimeout());
                if (!value.isFailFast()) {
                    gen.writeBooleanField("failFast", value.isFailFast());
                }


                // 序列化 retry 字段
                if (value.getRetry() != null) {
                    gen.writeFieldName("retry");
                    gen.writeObject(value.getRetry());
                }

                // 序列化 taskMap 字段
                if (value.getTaskMap() != null) {
                    gen.writeFieldName("tasks");
                    gen.writeStartArray();
                    for (Task task : value.getTaskMap().values()) {
                        gen.writeObject(task);
                    }
                    gen.writeEndArray();
                }

                // 序列化 outputs 字段
                if (value.getOutputs() != null) {
                    gen.writeFieldName("outputs");
                    gen.writeObject(value.getOutputs());
                }

                gen.writeEndObject();
            }
        };
    }
}
