package com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager;

import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.DSLConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DSL配置Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface DSLConfigMapper {

    /**
     * 插入DSL配置
     *
     * @param record DSL配置记录
     * @return 影响行数
     */
    int insert(DSLConfig record);

    /**
     * 根据ID更新DSL配置
     *
     * @param record DSL配置记录
     * @return 影响行数
     */
    int updateById(DSLConfig record);

    /**
     * 批量删除DSL配置
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据编排名称查询DSL配置
     *
     * @param dslName 编排名称
     * @return DSL配置
     */
    DSLConfig selectByDslName(@Param("dslName") String dslName);

    /**
     * 根据ID列表查询DSL配置
     *
     * @param ids ID列表
     * @return DSL配置列表
     */
    List<DSLConfig> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 分页查询DSL配置
     *
     * @param dslName        编排名称
     * @param dslDescription 编排描述
     * @param valid          有效性
     * @param offset         偏移量
     * @param limit          限制
     * @return DSL配置列表
     */
    List<DSLConfig> selectPage(
            @Param("dslName") String dslName,
            @Param(("dslDescription")) String dslDescription,
            @Param("valid") Integer valid,
            @Param("offset") int offset,
            @Param("limit") int limit
    );

    /**
     * 条件统计DSL配置数量
     *
     * @param dslName        编排名称
     * @param dslDescription 编排描述
     * @param valid          有效性
     * @return 数量
     */
    int countByCondition(
            @Param("dslName") String dslName,
            @Param(("dslDescription")) String dslDescription,
            @Param("valid") Integer valid
    );

    /**
     * 批量更新状态
     * @param ids
     * @param status
     * @return
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 根据ID查询DSL配置
     * @param id 配置ID
     * @return DSL配置
     */
    DSLConfig selectById(Long id);
}