package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.tools;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.tools.OrchestrationController.ExecutionDetail;
import com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.tools.OrchestrationController.ExecutionRecord;
import com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.tools.OrchestrationController.WorkflowDetail;
import com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.tools.OrchestrationController.WorkflowInfo;
import com.sankuai.meituan.banma.service.orchestration.facade.Engine;
import com.sankuai.meituan.banma.service.orchestration.facade.Manager;
import com.sankuai.meituan.banma.service.orchestration.model.Option;
import com.sankuai.meituan.banma.service.orchestration.model.Response;
import com.sankuai.meituan.banma.service.orchestration.model.Task;
import com.sankuai.meituan.banma.service.orchestration.model.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 服务编排服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrchestrationService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 模拟存储，实际应该使用数据库
    private final Map<String, WorkflowDetail> workflowStore = new ConcurrentHashMap<>();
    private final Map<String, List<ExecutionRecord>> executionHistoryStore = new ConcurrentHashMap<>();
    private final Map<String, ExecutionDetail> executionDetailStore = new ConcurrentHashMap<>();
    private final AtomicLong workflowIdGenerator = new AtomicLong(1);
    private final AtomicLong executionIdGenerator = new AtomicLong(1);

    public Workflow parseDsl(String dsl) {
        Response<Workflow> response = Manager.getWorkFlow(dsl, "");
        return response.getData();
    }

    public String stringify(Workflow workflow) {
        return DSLUtil.toFormattedJson(workflow);
    }

    public Response<Object> executeWorkflow(String dsl, String paramJson) {
        // 首先解析DSL
        Workflow workflow = null;
        try {
            workflow = parseDsl(dsl);
        } catch (Exception e) {
            log.error("解析工作流失败", e);
            throw new RuntimeException("解析工作流失败: " + e.getMessage(), e);
        }

        try {
            Option option = new Option();
            option.setWithContext(true);
            Response<Object> response = Engine.invoke(dsl, paramJson, option);

            return response;
        } catch (Exception e) {
            log.error("执行工作流失败", e);
            throw new RuntimeException("执行工作流失败: " + e.getMessage(), e);
        }
    }

    public Object executeTask(String dsl, String taskAlias, String paramJson, Map<String, Object> mockTaskResults) {
        try {
            // 解析DSL
            Workflow workflow = parseDsl(dsl);

            // 查找指定的任务
            Task task = workflow.getTaskMap().get(taskAlias);
            if (task == null) {
                throw new RuntimeException("找不到任务: " + taskAlias);
            }

            // 模拟执行任务
            return simulateTaskExecution(task, paramJson);
        } catch (Exception e) {
            log.error("执行任务失败", e);
            throw new RuntimeException("执行任务失败: " + e.getMessage(), e);
        }
    }

    public String saveWorkflow(String name, String dsl) {
        try {
            // 先解析验证DSL
            parseDsl(dsl);
            
            // 生成ID
            String id = "WF" + workflowIdGenerator.getAndIncrement();
            
            // 创建工作流详情
            WorkflowDetail detail = new WorkflowDetail();
            detail.setId(id);
            detail.setName(name);
            
            // 提取描述信息
            JsonNode dslNode = objectMapper.readTree(dsl);
            detail.setDescription(dslNode.path("description").asText(""));
            
            // 设置DSL和时间信息
            detail.setDsl(dsl);
            String now = formatCurrentTime();
            detail.setCreateTime(now);
            detail.setUpdateTime(now);
            
            // 保存到存储
            workflowStore.put(id, detail);
            
            // 创建执行历史存储
            executionHistoryStore.put(id, new ArrayList<>());
            
            return id;
        } catch (Exception e) {
            log.error("保存工作流失败", e);
            throw new RuntimeException("保存工作流失败: " + e.getMessage(), e);
        }
    }

    public List<WorkflowInfo> getWorkflowList() {
        List<WorkflowInfo> result = new ArrayList<>();
        
        // 从存储中查询所有工作流
        for (WorkflowDetail detail : workflowStore.values()) {
            WorkflowInfo info = new WorkflowInfo();
            info.setId(detail.getId());
            info.setName(detail.getName());
            info.setDescription(detail.getDescription());
            info.setCreateTime(detail.getCreateTime());
            info.setUpdateTime(detail.getUpdateTime());
            result.add(info);
        }
        
        // 按创建时间排序
        result.sort(Comparator.comparing(WorkflowInfo::getCreateTime).reversed());
        
        return result;
    }

    public WorkflowDetail getWorkflowDetail(String id) {
        WorkflowDetail detail = workflowStore.get(id);
        if (detail == null) {
            throw new RuntimeException("找不到工作流: " + id);
        }
        return detail;
    }

    public List<ExecutionRecord> getExecutionHistory(String workflowId) {
        List<ExecutionRecord> history = executionHistoryStore.get(workflowId);
        if (history == null) {
            throw new RuntimeException("找不到工作流执行历史: " + workflowId);
        }
        return history;
    }

    public ExecutionDetail getExecutionDetail(String executionId) {
        ExecutionDetail detail = executionDetailStore.get(executionId);
        if (detail == null) {
            throw new RuntimeException("找不到执行详情: " + executionId);
        }
        return detail;
    }

    /**
     * 模拟任务执行
     */
    private Map<String, Object> simulateTaskExecution(Task task, String paramJson) {
        try {
            // 实际开发中应该调用相应的任务执行器
            // 这里简单模拟执行结果
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("message", "success");
            
            // 不同任务类型返回不同的数据
            if ("ThriftGeneric".equals(task.getTaskType())) {
                Map<String, Object> data = new HashMap<>();
                data.put("taskAlias", task.getAlias());
                data.put("result", "模拟Thrift调用结果 - " + task.getMethod());
                result.put("data", data);
            } else if ("Calculate".equals(task.getTaskType())) {
                Map<String, Object> data = new HashMap<>();
                data.put("taskAlias", task.getAlias());
                data.put("result", "模拟计算结果");
                result.put("data", data);
            } else if ("Squirrel".equals(task.getTaskType())) {
                Map<String, Object> data = new HashMap<>();
                data.put("taskAlias", task.getAlias());
                data.put("result", "模拟Squirrel调用结果");
                result.put("data", data);
            } else {
                Map<String, Object> data = new HashMap<>();
                data.put("taskAlias", task.getAlias());
                data.put("result", "模拟默认调用结果");
                result.put("data", data);
            }

            return result;
        } catch (Exception e) {
            log.error("执行任务失败: {}", task.getAlias(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", e.getMessage());
            return result;
        }
    }

    /**
     * 生成输出数据
     */
    private Map<String, Object> generateOutputData(Workflow workflow, Map<String, Object> taskResultMap, String paramJson) {
        try {
            // 实际开发中应该根据outputs定义处理输出
            // 这里简单模拟返回结果
            Map<String, Object> outputData = new HashMap<>();
            
            for (Map.Entry<String, Task> entry : workflow.getTaskMap().entrySet()) {
                String taskAlias = entry.getKey();
                Task task = entry.getValue();
                
                // 如果任务有结果，添加到输出中
                if (taskResultMap.containsKey(taskAlias)) {
                    Map<String, Object> taskResult = (Map<String, Object>) taskResultMap.get(taskAlias);
                    
                    // 只添加成功的任务结果
                    if (0 == (int) taskResult.get("code")) {
                        Object data = taskResult.get("data");
                        outputData.put(taskAlias, data);
                    }
                }
            }
            
            return outputData;
        } catch (Exception e) {
            log.error("生成输出数据失败", e);
            return Collections.singletonMap("error", e.getMessage());
        }
    }

    /**
     * 记录执行历史
     */
    private String recordExecution(String workflowName, String dsl, String paramJson, Map<String, Object> result, long startTime, long endTime) {
        try {
            // 从DSL解析工作流ID
            JsonNode dslNode = objectMapper.readTree(dsl);
            String workflowId = null;
            
            // 尝试从存储中找到匹配的工作流
            for (WorkflowDetail detail : workflowStore.values()) {
                if (workflowName.equals(detail.getName())) {
                    workflowId = detail.getId();
                    break;
                }
            }
            
            // 如果找不到，创建新的工作流
            if (workflowId == null) {
                workflowId = saveWorkflow(workflowName, dsl);
            }
            
            // 生成执行ID
            String executionId = "EX" + executionIdGenerator.getAndIncrement();
            
            // 创建执行记录
            ExecutionRecord record = new ExecutionRecord();
            record.setId(executionId);
            record.setWorkflowId(workflowId);
            record.setExecuteTime(formatCurrentTime());
            record.setParamJson(paramJson);
            
            // 判断执行状态
            boolean hasException = result.containsKey("exception");
            record.setStatus(hasException ? "FAILED" : "SUCCESS");
            record.setDuration(endTime - startTime);
            
            // 保存执行记录
            List<ExecutionRecord> history = executionHistoryStore.get(workflowId);
            if (history == null) {
                history = new ArrayList<>();
                executionHistoryStore.put(workflowId, history);
            }
            history.add(record);
            
            // 创建执行详情
            ExecutionDetail detail = new ExecutionDetail();
            detail.setId(executionId);
            detail.setWorkflowId(workflowId);
            detail.setExecuteTime(record.getExecuteTime());
            detail.setParamJson(paramJson);
            detail.setResult((Map<String, Object>) result.getOrDefault("data", new HashMap<>()));
            detail.setTaskResults((Map<String, Object>) result.getOrDefault("taskResultMap", new HashMap<>()));
            detail.setStatus(record.getStatus());
            detail.setStartTime(startTime);
            detail.setEndTime(endTime);
            detail.setDuration(endTime - startTime);
            
            // 保存执行详情
            executionDetailStore.put(executionId, detail);
            
            return executionId;
        } catch (Exception e) {
            log.error("记录执行历史失败", e);
            return null;
        }
    }

    /**
     * 格式化当前时间
     */
    private String formatCurrentTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }
}