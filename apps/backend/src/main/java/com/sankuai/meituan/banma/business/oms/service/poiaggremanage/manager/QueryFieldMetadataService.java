package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.retry.DefaultRetryProperties;
import com.dianping.rhino.retry.Retry;
import com.dianping.rhino.retry.RetryCallback;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.banma.business.oms.adaptor.LionAdaptor;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.constant.LionConstant;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskStatus;
import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskType;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.LionPartialOperateException;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.config.mcm.ChangeEventPreCheckHandler;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.QueryFieldMetadataDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.page.FieldCodePageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryFieldMetadata;
import com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.QueryFieldMetadataMapper;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.changelog.McmOperateTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Validator;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 聚合查询字段元数据Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class QueryFieldMetadataService implements IMcmHandleService {

    @Resource
    private QueryFieldMetadataService self;

    @Resource
    private QueryFieldMetadataMapper queryFieldMetadataMapper;

    @Resource
    private McmOperateTaskService mcmOperateTaskService;

    @Resource
    private LionAdaptor lionAdaptor;

    @Resource
    private Validator validator;

    private static final Retry ADD_QUERY_FIELD_RETRY = Rhino.newRetry(
            LionConstant.QUERY_RETRY_KEY,
            new DefaultRetryProperties.Setter()
                    .withActive(true)
                    .withDelay(500L)
                    .withMaxAttempts(5)
    );

    private static final Retry BATCH_DELETE_RETRY = Rhino.newRetry(
            LionConstant.QUERY_FIELD_BATCH_DELETE_RETRY_KEY,
            new DefaultRetryProperties.Setter()
                    .withActive(true)
                    .withDelay(500L)
                    .withMaxAttempts(5)
    );

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class, noRollbackFor = LionPartialOperateException.class)
    public CommonResult<Boolean> add(QueryFieldMetadataDTO queryFieldMetadataDTO) {
        log.info("add QueryFieldMetadata: {}", queryFieldMetadataDTO);
        QueryFieldMetadata queryFieldMetadata = new QueryFieldMetadata();
        BeanUtil.copyProperties(queryFieldMetadataDTO, queryFieldMetadata);
        // 查询用户信息
        User user = UserUtils.getUser();
        if (user == null) {
            return CommonResult.failed("获取用户信息失败");
        }

        // 设置opName、opMis
        queryFieldMetadata.setOpName(user.getName());
        queryFieldMetadata.setOpMis(user.getLogin());

        final Boolean[] result = {false, !queryFieldMetadata.getSyncedField()};
        try {
            int rows = queryFieldMetadataMapper.insert(queryFieldMetadata);
            if (rows <= 0) {
                return CommonResult.failed("字段插入DB失败");
            }
            if (queryFieldMetadata.getHandlerType() != null && queryFieldMetadata.getHandlerType() == 0) {
                // 本地的不需要写入Lion
                return CommonResult.success(true);
            }
            preHandleBeforeLionSet(queryFieldMetadata);
            sync2LionWithRetry(queryFieldMetadataDTO, queryFieldMetadata, result);

            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException | LionPartialOperateException e) {
            throw e;
        } catch (Exception e) {
            log.error("add QueryFieldMetadata failed, fieldProperty: {}", queryFieldMetadata.getFieldProperty(), e);
            throw new PoiAggreQueryManagerException("添加字段元数据失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class)
    public CommonResult<Boolean> update(QueryFieldMetadataDTO queryFieldMetadataDTO) {
        log.info("Update query field metadata: {}", queryFieldMetadataDTO);
        QueryFieldMetadata queryFieldMetadata = new QueryFieldMetadata();
        BeanUtil.copyProperties(queryFieldMetadataDTO, queryFieldMetadata);
        try {
            // 查询用户信息
            User user = UserUtils.getUser();
            if (user == null) {
                return CommonResult.failed("获取用户信息失败");
            }

            // 设置opName、opMis
            queryFieldMetadata.setOpName(user.getName());
            queryFieldMetadata.setOpMis(user.getLogin());

            // 查询DB
            QueryFieldMetadata fieldMetadata = queryFieldMetadataMapper.selectById(queryFieldMetadata.getId());
            if (fieldMetadata == null) {
                return CommonResult.failed("记录不存在");
            }

            int rows = queryFieldMetadataMapper.updateById(queryFieldMetadata);
            if (rows <= 0) {
                return CommonResult.failed("更新DB失败");
            }

            // 不需要修改Lion
            if (fieldMetadata.getHandlerType() == 0) {
                return CommonResult.success(true);
            }

            // 同步到Lion配置中心
            preHandleBeforeLionSet(queryFieldMetadata);
            String lionKey = LionConstant.BM_POI_AGGRE_QUERY_FIELD_METADATA_LION_KEY_PREFIX + queryFieldMetadata.getFieldProperty();
            String lionConfigString = JSON.toJSONString(queryFieldMetadata, SerializerFeature.PrettyFormat);
            boolean lionResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, lionKey, lionConfigString);
            if (!lionResult) {
                throw new PoiAggreQueryManagerException("同步Lion配置失败");
            }
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("Update query field metadata failed", e);
            throw new PoiAggreQueryManagerException("更新字段元数据失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class, noRollbackFor = LionPartialOperateException.class)
    public CommonResult<Boolean> batchDeleteByIds(List<Long> ids) {
        // 先查询所有记录，获取fieldProperty列表
        List<QueryFieldMetadata> queryFieldMetadataList = queryFieldMetadataMapper.selectByIds(ids);
        if (queryFieldMetadataList.size() != ids.size()) {
            return CommonResult.failed(400, "记录不存在");
        }

        try {
            int rows = queryFieldMetadataMapper.batchDeleteByIds(ids);
            if (rows <= 0) {
                return CommonResult.failed("批量删除失败");
            }
            // 同步删除Lion配置
            List<String> fieldCacheHandlerFields = Lion.getList(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, String.class);
            AtomicReference<Boolean> fieldCacheDeleteFlag = new AtomicReference<>(false);
            Set<String> failedFieldPropertySet = queryFieldMetadataList.stream().map(QueryFieldMetadata::getFieldProperty).collect(Collectors.toSet());
            BATCH_DELETE_RETRY.execute((RetryCallback<Object, Exception>) () -> {
                for (QueryFieldMetadata queryFieldMetadata : queryFieldMetadataList) {
                    if (!failedFieldPropertySet.contains(queryFieldMetadata.getFieldProperty())) {
                        continue;
                    }
                    if (queryFieldMetadata.getHandlerType() == 0) {
                        // 本地不需要删除Lion配置
                        failedFieldPropertySet.remove(queryFieldMetadata.getFieldProperty());
                        continue;
                    }
                    if (queryFieldMetadata.getSyncedField()) {
                        fieldCacheHandlerFields.remove(queryFieldMetadata.getFieldCode());
                        fieldCacheDeleteFlag.set(true);
                    }
                    String lionKey = LionConstant.BM_POI_AGGRE_QUERY_FIELD_METADATA_LION_KEY_PREFIX + queryFieldMetadata.getFieldProperty();
                    Boolean queryFieldResult = lionAdaptor.deleteConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, lionKey);
                    if (queryFieldResult) {
                        failedFieldPropertySet.remove(queryFieldMetadata.getFieldProperty());
                    }
                }
                Boolean fieldCacheResult = true;
                if (fieldCacheDeleteFlag.get()) {
                    fieldCacheResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, JSON.toJSONString(fieldCacheHandlerFields));
                    fieldCacheDeleteFlag.set(!fieldCacheResult);
                }
                if (failedFieldPropertySet.size() == queryFieldMetadataList.size()) {
                    log.error("批量删除字段元数据数据全部失败");
                    throw new PoiAggreQueryManagerException(500, "批量删除字段元数据全部失败, failedFieldPropertyList: " + failedFieldPropertySet + ", fieldCacheResult: " + fieldCacheResult);
                } else if (!failedFieldPropertySet.isEmpty()) {
                    log.error("批量删除字段元数据失败, 以下字段删除Lion配置失败: {}", failedFieldPropertySet);
                    throw new LionPartialOperateException(500, "批量删除字段元数据失败, 以下字段删除Lion配置失败: " + failedFieldPropertySet + ", fieldCacheResult: " + fieldCacheResult);
                } else if (!fieldCacheResult) {
                    log.error("批量删除字段元数据失败, 修改同步字段列表失败");
                    throw new LionPartialOperateException(500, "批量删除字段元数据失败, 修改同步字段列表失败");
                }
                return null;
            });
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("Batch delete query field metadata failed", e);
            throw new PoiAggreQueryManagerException("批量删除字段元数据失败", e);
        }
    }

    public CommonResult<PageResultDTO<QueryFieldMetadata>> getPage(FieldCodePageQueryDTO pageQuery) {
        log.info("Get query field metadata page by condition: {}", pageQuery);
        // 计算偏移量
        int offset = (pageQuery.getPageNo() - 1) * pageQuery.getPageSize();
        try {
            // 查询数据
            List<QueryFieldMetadata> queryFieldMetadataList = queryFieldMetadataMapper.selectPage(
                    pageQuery.getFieldCode(),
                    pageQuery.getFieldName(),
                    pageQuery.getType(),
                    pageQuery.getHandlerType(),
                    offset,
                    pageQuery.getPageSize()
            );

            // 查询总数
            int total = queryFieldMetadataMapper.selectPageCount(
                    pageQuery.getFieldCode(),
                    pageQuery.getFieldName(),
                    pageQuery.getType(),
                    pageQuery.getHandlerType()
            );

            return CommonResult.success(new PageResultDTO<>(total, queryFieldMetadataList));
        } catch (PoiAggreQueryManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("Get query field metadata page failed", e);
            throw new PoiAggreQueryManagerException("分页查询字段元数据失败", e);
        }
    }

    @Transactional(value = "TM0", propagation = Propagation.NESTED, rollbackFor = Exception.class, noRollbackFor = LionPartialOperateException.class)
    public CommonResult<Boolean> batchAdd(List<QueryFieldMetadataDTO> queryFieldMetadataDTOList) {
        User user = UserUtils.getUser();
        List<QueryFieldMetadata> queryFieldMetadataList = new ArrayList<>();
        boolean syncFieldListFlag = false;
        for (QueryFieldMetadataDTO queryFieldMetadataDTO : queryFieldMetadataDTOList) {
            QueryFieldMetadata queryFieldMetadata = new QueryFieldMetadata();
            BeanUtil.copyProperties(queryFieldMetadataDTO, queryFieldMetadata);
            queryFieldMetadata.setOpName(user.getName());
            queryFieldMetadata.setOpMis(user.getLogin());
            queryFieldMetadataList.add(queryFieldMetadata);
            if (queryFieldMetadataDTO.getSyncedField()) {
                syncFieldListFlag = true;
            }
        }
        List<String> fieldCacheHandlerFields;
        if (syncFieldListFlag) {
            fieldCacheHandlerFields = Lion.getList(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, String.class);
        } else {
            fieldCacheHandlerFields = new ArrayList<>();
        }
        try {
            int rows = queryFieldMetadataMapper.batchInsert(queryFieldMetadataList);
            if (rows != queryFieldMetadataList.size()) {
                log.error("#批量插入数据库失败#");
                throw new PoiAggreQueryManagerException("数据库批量插入失败");
            }
            batchAddToLionWithRetry(queryFieldMetadataDTOList, queryFieldMetadataList, fieldCacheHandlerFields);
            log.info("#批量新增成功#");
            return CommonResult.success(true);
        } catch (PoiAggreQueryManagerException | LionPartialOperateException e) {
            throw e;
        } catch (Exception e) {
            log.error("#批量添加失败#, exception: {}", e.getMessage());
            throw new PoiAggreQueryManagerException("添加字段元数据失败", e);
        }
    }

    /**
     * 同步到Lion配置中心
     */
    private void sync2LionWithRetry(QueryFieldMetadataDTO queryFieldMetadataDTO, QueryFieldMetadata queryFieldMetadata, Boolean[] result) throws Exception {
        ADD_QUERY_FIELD_RETRY.execute((RetryCallback<Object, Exception>) () -> {
            // 同步到Lion配置中心
            if (!result[0]) {
                String lionKey = LionConstant.BM_POI_AGGRE_QUERY_FIELD_METADATA_LION_KEY_PREFIX + queryFieldMetadata.getFieldProperty();
                String lionConfigString = JSON.toJSONString(queryFieldMetadata, SerializerFeature.PrettyFormat);
                result[0] = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, lionKey, lionConfigString, queryFieldMetadataDTO.getLionConfigDescription());
            }

            // 查询lion，判断是否需要添加到同步字段列表中
            if (result[0] && !result[1]) {
                List<String> fieldCacheHandlerFields = Lion.getList(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, String.class);
                if (!fieldCacheHandlerFields.contains(queryFieldMetadata.getFieldCode())) {
                    fieldCacheHandlerFields.add(queryFieldMetadata.getFieldCode());
                    result[1] = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, JSON.toJSONString(fieldCacheHandlerFields));
                } else {
                    result[1] = true;
                }
            }
            if (!result[0]) {
                log.error("#Lion配置失败# 查询字段结果: false, 同步字段列表结果: false");
                throw new PoiAggreQueryManagerException(500, "#Lion配置失败# 查询字段结果: false, 同步字段列表结果: false");
            } else if (!result[1]) {
                log.error("#Lion配置失败# 查询字段结果: true, 同步字段列表结果: false");
                throw new LionPartialOperateException(500, "#Lion配置失败# 查询字段结果: true, 同步字段列表结果: false");
            }
            return null;
        });
    }

    private void batchAddToLionWithRetry(List<QueryFieldMetadataDTO> queryFieldDTOList, List<QueryFieldMetadata> queryFieldList, List<String> fieldCacheHandlerFields) throws Exception {
        // 获取需要调用lion的字段的fieldProperty
        Set<String> failedFieldPropertySet = queryFieldList.stream().map(QueryFieldMetadata::getFieldProperty).collect(Collectors.toSet());

        ADD_QUERY_FIELD_RETRY.execute((RetryCallback<Object, Exception>) () -> {
            for (int idx = 0; idx < queryFieldList.size(); idx++) {
                QueryFieldMetadata queryFieldMetadata = queryFieldList.get(idx);
                if (!failedFieldPropertySet.contains(queryFieldMetadata.getFieldProperty())) {
                    continue;
                }
                Integer handlerType = queryFieldMetadata.getHandlerType();
                if (handlerType != null && handlerType == 0) {
                    // 本地的不需要写入Lion
                    failedFieldPropertySet.remove(queryFieldMetadata.getFieldProperty());
                    continue;
                }
                preHandleBeforeLionSet(queryFieldMetadata);
                String lionKey = LionConstant.BM_POI_AGGRE_QUERY_FIELD_METADATA_LION_KEY_PREFIX + queryFieldMetadata.getFieldProperty();
                // 添加查询字段结果
                Boolean addFieldResult = lionAdaptor.setConfig(
                        LionConstant.BM_POI_AGGRE_QUERY_APPKEY,
                        lionKey,
                        JSON.toJSONString(queryFieldMetadata, SerializerFeature.PrettyFormat),
                        queryFieldDTOList.get(idx).getLionConfigDescription()
                );
                // 初始化同步字段列表的结果，根据该字段是否是同步字段初始化
                Boolean addFieldCacheHandlerResult = !queryFieldMetadata.getSyncedField();
                if (fieldCacheHandlerFields.contains(queryFieldMetadata.getFieldCode())) {
                    addFieldCacheHandlerResult = true;
                }
                if (!addFieldCacheHandlerResult) {
                    // 如果需要修改同步字段列表，且同步字段列表中没有这个字段（存在代表已经添加成功）
                    fieldCacheHandlerFields.add(queryFieldMetadata.getFieldCode());
                    addFieldCacheHandlerResult = lionAdaptor.setConfig(LionConstant.BM_POI_AGGRE_QUERY_APPKEY, LionConstant.BM_POI_AGGRE_SYNC_FIELD_LIST_KEY, JSON.toJSONString(fieldCacheHandlerFields));
                    if (!addFieldCacheHandlerResult) {
                        fieldCacheHandlerFields.remove(queryFieldMetadata.getFieldCode());
                    }
                }
                if (addFieldResult && addFieldCacheHandlerResult) {
                    failedFieldPropertySet.remove(queryFieldMetadata.getFieldProperty());
                }
            }
            if (failedFieldPropertySet.size() == queryFieldList.size()) {
                log.error("批量添加字段元数据数据全部失败");
                throw new PoiAggreQueryManagerException(500, "批量添加字段元数据全部失败, failedFieldPropertyList: " + failedFieldPropertySet);
            } else if (!failedFieldPropertySet.isEmpty()) {
                log.error("批量添加字段元数据失败, 以下字段添加Lion配置失败: {}", failedFieldPropertySet);
                throw new LionPartialOperateException(500, "批量添加字段元数据失败, 以下字段添加Lion配置失败: " + failedFieldPropertySet);
            }
            return null;
        });
    }

    @Override
    public boolean preCheck(String requestUri, McmChangeConfigDTO mcmChangeConfigDTO) {
        switch (requestUri) {
            case McmRequest.ADD:
                QueryFieldMetadataDTO queryFieldMetadataDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), QueryFieldMetadataDTO.class, false);
                addPreCheck(queryFieldMetadataDTO);
                break;
            case McmRequest.BATCH_ADD:
                Object requestData = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_ADD_PARAM_KEY);
                if (!(requestData instanceof List) || ((List<?>) requestData).isEmpty()) {
                    throw new PoiAggreQueryManagerException("请求参数类型错误");
                }
                List<QueryFieldMetadataDTO> queryFieldMetadataDTOList = ((List<?>) requestData)
                        .stream()
                        .map(obj -> BeanUtil.mapToBean((Map<String, Object>) obj, QueryFieldMetadataDTO.class, false))
                        .collect(Collectors.toList());

                batchAddPreCheck(queryFieldMetadataDTOList);

                break;
            case McmRequest.UPDATE:
                QueryFieldMetadataDTO fieldMetadataRequestData = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), QueryFieldMetadataDTO.class, false);
                updatePreCheck(fieldMetadataRequestData);
                break;
            case McmRequest.BATCH_DELETE:
                Object ids = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                if (!(ids instanceof List) || ((List<?>) ids).isEmpty()) {
                    throw new PoiAggreQueryManagerException("ids参数类型错误");
                }
                List<Long> idList = ((List<?>) ids).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());
                batchDeletePreCheck(idList);
                break;
            default:
                return false;
        }
        return true;
    }

    @Override
    @Transactional(value = "TM0", rollbackFor = Exception.class)
    public boolean addOperateTaskAndUpdateRecordStatus2Auditing(String mcmUrl, String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        // 添加任务
        McmOperateTask mcmOperateTask = getAuditingMcmOperateTask(mcmUrl, eventUuid, mcmChangeConfigDTO, McmTaskType.QUERY_FIELD.getCode());
        boolean addTaskResult = mcmOperateTaskService.addTask(mcmOperateTask);
        if (!addTaskResult) {
            return false;
        }

        String requestUri = mcmChangeConfigDTO.getRequestUri();
        List<Long> ids = ChangeEventPreCheckHandler.getChangeIds(requestUri, mcmChangeConfigDTO);
        if (ids != null) {
            int count = queryFieldMetadataMapper.batchUpdateStatus(ids, McmRequest.MCM_STATUS_CODE_AUDITING);
            if (count != ids.size()) {
                throw new PoiAggreQueryManagerException("审核发起成功，更新状态失败");
            }
        }

        return true;
    }

    @Override
    @Transactional(value = "TM0", rollbackFor = Exception.class, noRollbackFor = {PoiAggreQueryManagerException.class, LionPartialOperateException.class})
    public void mcmAuditAccepted(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            int count = queryFieldMetadataMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
            if (count != changeIdList.size()) {
                throw new PoiAggreQueryManagerException("状态修改失败");
            }
        }
        QueryFieldMetadataDTO queryFieldMetadataDTO;
        CommonResult<Boolean> res;
        try {
            switch (mcmChangeConfigDTO.getRequestUri()) {
                case McmRequest.ADD:
                    queryFieldMetadataDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), QueryFieldMetadataDTO.class, false);
                    res = self.add(queryFieldMetadataDTO);
                    break;
                case McmRequest.BATCH_ADD:
                    Object requestData = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_ADD_PARAM_KEY);
                    List<QueryFieldMetadataDTO> queryFieldMetadataDTOList = ((List<?>) requestData)
                            .stream()
                            .map(obj -> BeanUtil.mapToBean((Map<String, Object>) obj, QueryFieldMetadataDTO.class, false))
                            .collect(Collectors.toList());
                    res = self.batchAdd(queryFieldMetadataDTOList);
                    break;
                case McmRequest.UPDATE:
                    queryFieldMetadataDTO = BeanUtil.mapToBean(mcmChangeConfigDTO.getRequestData(), QueryFieldMetadataDTO.class, false);
                    res = self.update(queryFieldMetadataDTO);
                    break;
                case McmRequest.BATCH_DELETE:
                    Object ids = mcmChangeConfigDTO.getRequestData().get(McmRequest.BATCH_DELETE_PARAM_KEY);
                    if (!(ids instanceof List) || ((List<?>) ids).isEmpty()) {
                        throw new PoiAggreQueryManagerException("ids参数类型错误");
                    }
                    List<Long> idList = ((List<?>) ids).stream().map(obj -> Long.parseLong(obj.toString())).collect(Collectors.toList());
                    res = self.batchDeleteByIds(idList);

                    break;
                default:
                    throw new PoiAggreQueryManagerException("未知请求类型");
            }
            if (res.getCode() != 0) {
                throw new PoiAggreQueryManagerException("回调处理失败");
            }
        } catch (Exception e) {
            McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.EXCEPTION.getCode());
            mcmOperateTaskService.updateTask(mcmOperateTask);
            throw e;
        }
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.ACCEPTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);
    }

    @Override
    public void mcmAuditRejected(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.REJECTED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);

        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            queryFieldMetadataMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
        }
    }

    @Override
    public void mcmAuditCancel(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO) {
        McmOperateTask mcmOperateTask = getCompletedMcmOperateTask(eventUuid, McmTaskStatus.CANCELED.getCode());
        mcmOperateTaskService.updateTask(mcmOperateTask);

        List<Long> changeIdList = ChangeEventPreCheckHandler.getChangeIds(mcmChangeConfigDTO.getRequestUri(), mcmChangeConfigDTO);
        if (CollUtil.isNotEmpty(changeIdList)) {
            queryFieldMetadataMapper.batchUpdateStatus(changeIdList, McmRequest.MCM_STATUS_CODE_AUDIT_COMPLETED);
        }
    }

    public void addPreCheck(QueryFieldMetadataDTO queryFieldMetadataDTO) {
        BindingResult bindingResult;
        bindingResult = new BeanPropertyBindingResult(queryFieldMetadataDTO, "queryFieldMetadataDTO");
        validator.validate(queryFieldMetadataDTO, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new PoiAggreQueryManagerException(400, "参数校验失败");
        }
        // 校验fieldCode、fieldProperty是否重复
        QueryFieldMetadata existingRecord = queryFieldMetadataMapper.selectByFieldCodeOrFieldProperty(queryFieldMetadataDTO.getFieldCode(), queryFieldMetadataDTO.getFieldProperty());
        if (existingRecord != null) {
            throw new PoiAggreQueryManagerException(400, "字段Code已存在");
        }
    }

    public void batchAddPreCheck(List<QueryFieldMetadataDTO> queryFieldMetadataDTOList) {
        BindingResult bindingResult;
        for (QueryFieldMetadataDTO dto : queryFieldMetadataDTOList) {
            bindingResult = new BeanPropertyBindingResult(dto, "queryFieldMetadataDTO");
            validator.validate(dto, bindingResult);
            if (bindingResult.hasErrors()) {
                throw new PoiAggreQueryManagerException(400, "参数校验失败");
            }
        }

        QueryFieldMetadata record;
        Set<String> notExistRecords = new HashSet<>();
        for (QueryFieldMetadataDTO dto : queryFieldMetadataDTOList) {
            // 校验fieldCode、fieldProperty是否重复
            record = queryFieldMetadataMapper.selectByFieldCodeOrFieldProperty(dto.getFieldCode(), dto.getFieldProperty());
            if (record == null) {
                // 组内校验重复
                notExistRecords.add(dto.getFieldCode());
            }
        }
        if (notExistRecords.size() != queryFieldMetadataDTOList.size()) {
            Set<String> existRecords = queryFieldMetadataDTOList.stream()
                    .map(QueryFieldMetadataDTO::getFieldCode)
                    .filter(fieldCode -> !notExistRecords.contains(fieldCode))
                    .collect(Collectors.toSet());
            throw new PoiAggreQueryManagerException(400, "字段Code或字段property已存在##" + JSON.toJSONString(existRecords));
        }
    }

    public void updatePreCheck(QueryFieldMetadataDTO fieldMetadataRequestData) {
        BindingResult bindingResult;
        if (fieldMetadataRequestData.getId() == null) {
            throw new PoiAggreQueryManagerException(400, "id不能为空");
        }
        bindingResult = new BeanPropertyBindingResult(fieldMetadataRequestData, "queryFieldMetadataDTO");
        validator.validate(fieldMetadataRequestData, bindingResult);
        if (bindingResult.hasErrors()) {
            throw new PoiAggreQueryManagerException(400, "参数校验失败");
        }

        // 校验fieldProperty是否已存在
        QueryFieldMetadata otherRecord = queryFieldMetadataMapper.selectByFieldCodeOrFieldProperty(fieldMetadataRequestData.getFieldCode(), fieldMetadataRequestData.getFieldProperty());
        QueryFieldMetadata queryFieldMetadata = queryFieldMetadataMapper.selectById(fieldMetadataRequestData.getId());
        if (queryFieldMetadata == null) {
            throw new PoiAggreQueryManagerException(400, "请求参数异常，id不存在");
        } else if (queryFieldMetadata.getStatus() == 1) {
            throw new PoiAggreQueryManagerException(400, "有数据正在审核中，禁止操作！");
        } else if (otherRecord != null && !Objects.equals(fieldMetadataRequestData.getId(), otherRecord.getId())) {
            throw new PoiAggreQueryManagerException(400, "字段Property已存在");
        }
    }

    public void batchDeletePreCheck(List<Long> idList) {
        // 先查询所有记录，获取fieldProperty列表
        List<QueryFieldMetadata> queryFieldMetadataList = queryFieldMetadataMapper.selectByIds(idList);
        if (queryFieldMetadataList.size() != idList.size()) {
            throw new PoiAggreQueryManagerException(400, "不能删除不存在的数据！");
        }
        queryFieldMetadataList.forEach(field -> {
            if (field.getStatus() == 1) {
                throw new PoiAggreQueryManagerException(400, "有数据正在审核中，请重新选择！");
            }
        });
    }

    public void preHandleBeforeLionSet(QueryFieldMetadata queryFieldMetadata) {
        if (queryFieldMetadata.getDependentFields() != null && queryFieldMetadata.getDependentFields().isEmpty()) {
            queryFieldMetadata.setDependentFields(null);
        }
        queryFieldMetadata.setId(null);
        queryFieldMetadata.setValid(null);
    }
}
