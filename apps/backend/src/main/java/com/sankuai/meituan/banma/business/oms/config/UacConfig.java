package com.sankuai.meituan.banma.business.oms.config;

import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * UAC配置类
 */
@Configuration
public class UacConfig {

    @Value("${mdp.uac.host}")
    private String uacHost;
    @Value("${mdp.uac.appkey}")
    private String uacClientId;
    @Value("${mdp.uac.secret}")
    private String uacSecret;
    
    @Bean
    public UacAuthRemoteService uacAuthRemoteService() {
        UacAuthRemoteService uacAuthRemoteService = new UacAuthRemoteService();
        uacAuthRemoteService.setUacHost(uacHost);
        uacAuthRemoteService.setUacClientId(uacClientId);
        uacAuthRemoteService.setUacSecret(uacSecret);
        return uacAuthRemoteService;
    }
} 