package com.sankuai.meituan.banma.business.oms.service.aiworkshop;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.dto.aiworkshop.AIWikiPageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.aiworkshop.AIWiki;
import com.sankuai.meituan.banma.business.oms.mapper.aiworkshop.AIWikiMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * AI Wiki服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AIWikiService {

    @Resource
    private AIWikiMapper aiWikiMapper;

    public CommonResult<Boolean> add(AIWiki wiki) {
        if (wiki == null) {
            return CommonResult.failed("参数不能为空");
        }
        if (wiki.getType() == 1) {
            wiki.setPbName("");
            wiki.setDepartment("");
        }

        // 根据title和type查询是否有重复
        AIWiki aiWiki = aiWikiMapper.getByTitleAndType(wiki.getTitle(), wiki.getType());
        if (aiWiki != null) {
            return CommonResult.failed("已存在重复的Wiki Title");
        }

        User user = UserUtils.getUser();

        // 设置操作人信息
        wiki.setOpName(user.getName());
        wiki.setOpMis(user.getLogin());
        wiki.setPbMis(user.getLogin());

        preHandle(wiki);

        boolean result = aiWikiMapper.insert(wiki) > 0;
        return result ? CommonResult.success(true) : CommonResult.failed("添加失败");
    }

    public CommonResult<Boolean> update(AIWiki wiki) {
        if (wiki == null || wiki.getId() == null) {
            return CommonResult.failed("参数不能为空");
        }

        // 根据title和type查询是否有重复
        AIWiki aiWiki = aiWikiMapper.getByTitleAndType(wiki.getTitle(), wiki.getType());
        if (aiWiki != null && !Objects.equals(aiWiki.getId(), wiki.getId())) {
            return CommonResult.failed("已存在重复的Wiki Title");
        }

        User user = UserUtils.getUser();

        // 设置操作人信息
        wiki.setOpName(user.getName());
        wiki.setOpMis(user.getLogin());
        wiki.setPbMis(user.getLogin());

        boolean result = aiWikiMapper.update(wiki) > 0;
        return result ? CommonResult.success(true) : CommonResult.failed("更新失败");
    }

    public CommonResult<Boolean> delete(Long id) {
        if (id == null) {
            return CommonResult.failed("参数不能为空");
        }
        boolean result = aiWikiMapper.deleteById(id) > 0;
        return result ? CommonResult.success(true) : CommonResult.failed("删除失败");
    }

    public CommonResult<PageResultDTO<AIWiki>> page(AIWikiPageQueryDTO pageQueryDTO) {
        // 计算偏移量
        int offset = (pageQueryDTO.getPageNo() - 1) * pageQueryDTO.getPageSize();

        Integer total;
        List<AIWiki> records;
        try {
            records = aiWikiMapper.page(pageQueryDTO.getWikiType(), pageQueryDTO.getWikiParam(), offset, pageQueryDTO.getPageSize());
            total = aiWikiMapper.count(pageQueryDTO.getWikiType(), pageQueryDTO.getWikiParam());
        } catch (Exception e) {
            log.error("查询失败, message: {}", e.toString());
            return CommonResult.failed(500, "查询失败");
        }

        PageResultDTO<AIWiki> pageResult = new PageResultDTO<>(total, records);

        return CommonResult.success(pageResult);
    }

    private void preHandle(AIWiki wiki) {
        if (wiki.getType() == 1) {
            wiki.setDepartment("");
            wiki.setPbName("");
            wiki.setPbMis("");
        }
    }
} 