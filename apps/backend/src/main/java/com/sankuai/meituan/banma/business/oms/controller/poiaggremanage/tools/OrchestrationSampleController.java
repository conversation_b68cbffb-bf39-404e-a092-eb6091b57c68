package com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.tools;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;

/**
 * 服务编排示例DSL控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/poiaggremanage/tools/orchestration")
@Slf4j
public class OrchestrationSampleController {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取示例DSL
     * @param type 示例类型，默认为multiTask
     * @return 示例DSL内容
     */
    @GetMapping("/sample")
    public CommonResult<String> getSampleDsl(@RequestParam(required = false, defaultValue = "testDeliveryCategory") String type) {
        try {
            String fileName = "testMultiTask.json";
            if ("testSingleTask".equals(type)) {
                fileName = "testSingleTask.json";
            } else if ("testDeliveryCategory".equals(type)) {
                fileName = "testDeliveryCategory.json";
            } else if (type.endsWith(".json")) {
                // 支持直接通过文件名获取示例
                fileName = type;
            } else {
                // 如果不是标准类型且不是.json结尾，自动追加.json后缀
                fileName = type + ".json";
            }
            
            log.info("尝试获取示例DSL: {}", fileName);
            
            // 尝试从orchestration-doc目录获取
            String jsonContent = readResourceFile("orchestration-doc/" + fileName);
            
            // 如果获取失败，尝试从testdslshow目录获取
            if (jsonContent == null) {
                log.info("从orchestration-doc目录获取示例DSL失败，尝试从testdslshow目录获取: {}", fileName);
                jsonContent = readResourceFile("orchestration-doc/testdslshow/" + fileName);
            }
            
            // 如果还是获取失败，返回错误
            if (jsonContent == null) {
                return CommonResult.failed("获取示例DSL失败：找不到文件 " + fileName);
            }
            
            // 返回内容
            return CommonResult.success(jsonContent);
        } catch (Exception e) {
            log.error("获取示例DSL失败", e);
            return CommonResult.failed("获取示例DSL失败：" + e.getMessage());
        }
    }
    
    /**
     * 读取资源文件内容
     * @param resourcePath 资源路径
     * @return 文件内容，如果文件不存在则返回null
     */
    private String readResourceFile(String resourcePath) {
        try {
            ClassPathResource resource = new ClassPathResource(resourcePath);
            if (!resource.exists()) {
                return null;
            }
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
                return reader.lines().collect(Collectors.joining("\n"));
            }
        } catch (IOException e) {
            log.warn("读取资源文件失败: {}", resourcePath, e);
            return null;
        }
    }
    
    /**
     * 获取所有DSL示例文件列表
     * @return DSL示例文件信息列表，包含文件名、编排名称和描述
     */
    @GetMapping("/sample/list")
    public CommonResult<List<Map<String, String>>> getSampleList() {
        List<Map<String, String>> result = new ArrayList<>();
        
        try {
            // 使用PathMatchingResourcePatternResolver搜索classpath下的资源
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            
            // 获取主目录下的示例
            Resource[] mainResources = resolver.getResources("classpath:orchestration-doc/*.json");
            addResourcesToResult(mainResources, result);
            
            // 获取testdslshow目录下的示例
            Resource[] testResources = resolver.getResources("classpath:orchestration-doc/testdslshow/*.json");
            addResourcesToResult(testResources, result);
            
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取DSL示例列表失败", e);
            return CommonResult.failed("获取DSL示例列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 将资源添加到结果列表中
     * @param resources 资源数组
     * @param result 结果列表
     */
    private void addResourcesToResult(Resource[] resources, List<Map<String, String>> result) {
        for (Resource resource : resources) {
            try {
                // 获取文件名
                String path = resource.getURL().getPath();
                String fileName = path.substring(path.lastIndexOf('/') + 1);
                
                // 读取JSON内容
                String jsonContent;
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
                    jsonContent = reader.lines().collect(Collectors.joining("\n"));
                }
                
                // 解析JSON获取name和description
                Map<String, Object> jsonMap = objectMapper.readValue(jsonContent, Map.class);
                String name = jsonMap.containsKey("name") ? jsonMap.get("name").toString() : "未命名";
                String description = jsonMap.containsKey("description") ? jsonMap.get("description").toString() : "无描述";
                
                // 提取相对路径
                String relativePath = extractRelativePath(path, "orchestration-doc");
                
                // 构建结果
                Map<String, String> fileInfo = new HashMap<>();
                fileInfo.put("fileName", fileName);
                fileInfo.put("name", name);
                fileInfo.put("description", description);
                fileInfo.put("path", relativePath);
                
                result.add(fileInfo);
            } catch (Exception e) {
                // 单个文件处理失败不影响整体结果
                log.warn("处理示例文件失败: {}", resource.getFilename(), e);
            }
        }
    }
    
    /**
     * 从绝对路径中提取相对于某个目录的路径
     * @param fullPath 完整路径
     * @param baseDir 基础目录名
     * @return 相对路径
     */
    private String extractRelativePath(String fullPath, String baseDir) {
        int index = fullPath.indexOf(baseDir);
        if (index != -1) {
            return fullPath.substring(index);
        }
        return fullPath;
    }
} 