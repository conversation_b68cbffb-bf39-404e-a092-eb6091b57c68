package com.sankuai.meituan.banma.business.oms.common.exception;

/**
 * Lion操作异常
 * <AUTHOR>
 */
public class LionPartialOperateException extends RuntimeException {
    private int code;

    public LionPartialOperateException(String message) {
        super(message);
        this.code = 500;
    }

    public LionPartialOperateException(int code, String message) {
        super(message);
        this.code = code;
    }

    public LionPartialOperateException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
    }

    public LionPartialOperateException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
