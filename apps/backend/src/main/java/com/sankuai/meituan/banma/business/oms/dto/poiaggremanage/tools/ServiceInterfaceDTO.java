package com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools;

import java.util.List;

/**
 * 服务接口DTO
 * <AUTHOR>
 */
public class ServiceInterfaceDTO {
    private String appkey;
    private List<ServiceInfo> serviceInfo;
    private String env;
    private String swimlane;
    private String startTime;
    private String version;

    /**
     * 服务信息
     */
    public static class ServiceInfo {
        private String port;
        private List<ServiceIfaceInfo> serviceIfaceInfos;
        private String status;

        public String getPort() {
            return port;
        }

        public void setPort(String port) {
            this.port = port;
        }

        public List<ServiceIfaceInfo> getServiceIfaceInfos() {
            return serviceIfaceInfos;
        }

        public void setServiceIfaceInfos(List<ServiceIfaceInfo> serviceIfaceInfos) {
            this.serviceIfaceInfos = serviceIfaceInfos;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }

    /**
     * 服务接口信息
     */
    public static class ServiceIfaceInfo {
        private String serviceName;
        private String ifaceName;
        private String implName;

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public String getIfaceName() {
            return ifaceName;
        }

        public void setIfaceName(String ifaceName) {
            this.ifaceName = ifaceName;
        }

        public String getImplName() {
            return implName;
        }

        public void setImplName(String implName) {
            this.implName = implName;
        }
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public List<ServiceInfo> getServiceInfo() {
        return serviceInfo;
    }

    public void setServiceInfo(List<ServiceInfo> serviceInfo) {
        this.serviceInfo = serviceInfo;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getSwimlane() {
        return swimlane;
    }

    public void setSwimlane(String swimlane) {
        this.swimlane = swimlane;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
} 