package com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.manager;

import com.sankuai.mcm.client.sdk.config.annotation.McmHandler;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.config.mcm.McmConfig;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.AccessGuideData;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager.AccessGuideService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/poiAggreManage/manager/accessGuide")
public class AccessGuideController {

    @Resource
    private AccessGuideService accessGuideService;

    @McmHandler(eventName = McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME)
    @PostMapping(McmRequest.ADD)
    public String submitAccessGuide(@RequestBody McmChangeConfigDTO mcmChangeConfigDTO) {
        return mcmChangeConfigDTO.toString();
    }

    @PostMapping("/aiConfig")
    public CommonResult<Map<String, Object>> getAiAccessGuideConfig(@RequestBody AccessGuideData accessGuideData) {
        return accessGuideService.getAiAccessGuideConfig(accessGuideData);
    }
}
