package com.sankuai.meituan.banma.business.oms.controller.aiworkshop;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.dto.aiworkshop.AIWikiPageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.aiworkshop.AIWiki;
import com.sankuai.meituan.banma.business.oms.service.aiworkshop.AIWikiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * AI Wiki控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/aiWorkshop/aiWiki")
public class AIWikiController {

    @Resource
    private AIWikiService aiWikiService;

    /**
     * 新增Wiki
     * @param wiki Wiki信息
     * @return 是否成功
     */
    @PostMapping("/add")
    public CommonResult<Boolean> add(@RequestBody @Valid AIWiki wiki) {
        log.info("新增Wiki请求:{}", wiki);
        return aiWikiService.add(wiki);
    }

    /**
     * 更新Wiki
     * @param wiki Wiki信息
     * @return 是否成功
     */
    @PostMapping("/update")
    public CommonResult<Boolean> update(@RequestBody @Valid AIWiki wiki) {
        log.info("更新Wiki请求:{}", wiki);
        return aiWikiService.update(wiki);
    }

    /**
     * 删除Wiki
     * @param id Wiki ID
     * @return 是否成功
     */
    @GetMapping("/delete/{id}")
    public CommonResult<Boolean> delete(@PathVariable Long id) {
        log.info("删除Wiki请求, id:{}", id);
        return aiWikiService.delete(id);
    }

    /**
     * 分页查询Wiki列表
     * @param aiWikiPageQueryDTO 分页查询参数
     * @return Wiki列表
     */
    @PostMapping("/page")
    public CommonResult<PageResultDTO<AIWiki>> page(@RequestBody @Valid AIWikiPageQueryDTO aiWikiPageQueryDTO) {
        log.info("分页查询Wiki请求:{}", aiWikiPageQueryDTO);
        return aiWikiService.page(aiWikiPageQueryDTO);
    }
} 