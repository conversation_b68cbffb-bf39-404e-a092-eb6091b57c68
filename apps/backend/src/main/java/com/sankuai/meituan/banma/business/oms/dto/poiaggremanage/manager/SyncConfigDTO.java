package com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager;

import com.alibaba.fastjson.annotation.JSONField;
import com.sankuai.meituan.banma.business.oms.common.handler.JsonStringSerializeHandler;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 同步配置DTO
 * <AUTHOR>
 */
@Data
public class SyncConfigDTO {

    @JSONField(serialize = false)
    private Long id;

    /**
     * 同步租户
     */
    @NotBlank(message = "同步租户不能为空")
    @JSONField(ordinal = 1)
    private Integer syncTenant;

    /**
     * 同步场景
     */
    @NotBlank(message = "同步场景不能为空")
    @JSONField(ordinal = 2)
    private Integer syncScene;

    /**
     * 同步名称
     */
    @NotBlank(message = "同步名称不能为空")
    @JSONField(ordinal = 3)
    private String syncName;

    /**
     * 同步描述
     */
    @JSONField(ordinal = 4)
    private String syncDescription;

    /**
     * 同步字段code列表
     */
    @NotBlank(message = "同步字段code列表不能为空")
    @JSONField(ordinal = 5)
    private List<String> syncFieldCodes;

    /**
     * 同步类型，1-mafka，2-dts
     */
    @NotBlank(message = "同步类型不能为空")
    @JSONField(ordinal = 6)
    private Integer type;

    /**
     * 是否外部POI
     */
    @NotBlank(message = "是否外部POI不能为空")
    @JSONField(ordinal = 7)
    private Boolean isPoiOuter;

    /**
     * dts同步配置
     */
    @JSONField(ordinal = 8, serializeUsing = JsonStringSerializeHandler.class)
    private String dtsSyncConfig;

    /**
     * 一致性校验
     */
    @JSONField(ordinal = 9, serializeUsing = JsonStringSerializeHandler.class)
    private String totalPoiConsistencyCheck;

    /**
     * mafka消费配置
     */
    @JSONField(ordinal = 10, serializeUsing = JsonStringSerializeHandler.class)
    private String mafkaConsumeConfig;

    /**
     * 查询数据配置
     */
    @JSONField(ordinal = 11, serializeUsing = JsonStringSerializeHandler.class)
    private String queryDataConfig;

    /**
     * 查询变更ID配置
     */
    @JSONField(ordinal = 12, serializeUsing = JsonStringSerializeHandler.class)
    private String queryChangeIdsConfig;

    /**
     * lion配置描述
     */
    @JSONField(ordinal = 13)
    private String lionConfigDescription;

    /**
     * 有效性
     */
    @JSONField(serialize = false)
    private Integer valid;

}
