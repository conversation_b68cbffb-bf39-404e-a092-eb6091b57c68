package com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager;

import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聚合查询同步配置Mapper接口
 */
@Mapper
public interface SyncConfigMapper {
    /**
     * 插入同步配置
     *
     * @param record 同步配置
     * @return 影响行数
     */
    int insert(SyncConfig record);

    /**
     * 更新同步配置
     *
     * @param record 同步配置
     * @return 影响行数
     */
    int update(SyncConfig record);

    /**
     * 批量删除同步配置
     *
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 分页条件查询同步配置
     *
     * @param syncTenant 同步租户
     * @param syncScene 同步场景
     * @param syncName  同步名称
     * @param type      同步类型
     * @param valid     有效性
     * @param offset    偏移量
     * @param limit     限制数量
     * @return 同步配置列表
     */
    List<SyncConfig> selectPage(@Param("syncTenant") Integer syncTenant,
                                @Param("syncScene") Integer syncScene,
                                @Param("syncName") String syncName,
                                @Param("type") Integer type,
                                @Param("valid") Integer valid,
                                @Param("offset") Integer offset,
                                @Param("limit") Integer limit);

    /**
     * 条件查询总数
     *
     * @param syncTenant 同步租户
     * @param syncScene 同步场景
     * @param syncName  同步名称
     * @param type      同步类型
     * @param valid     有效性
     * @return 总数
     */
    int selectPageCount(@Param("syncTenant") Integer syncTenant,
                        @Param("syncScene") Integer syncScene,
                        @Param("syncName") String syncName,
                        @Param("type") Integer type,
                        @Param("valid") Integer valid);

    /**
     * 根据同步场景查询同步配置
     *
     * @param syncScene 同步场景
     * @return 同步配置
     */
    SyncConfig selectBySyncScene(@Param("syncScene") Integer syncScene);

    /**
     * 根据ID列表批量查询同步配置
     *
     * @param ids ID列表
     * @return 同步配置列表
     */
    List<SyncConfig> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据同步场景或名称查询同步配置
     *
     * @param syncScene
     * @param syncName
     * @return
     */
    SyncConfig selectBySyncSceneOrName(Integer syncScene, String syncName);

    /**
     * 批量更新状态
     * @param ids
     * @param status
     * @return
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,@Param("status") Integer status);

    /**
     * 根据ID查询同步配置
     * @param id 同步配置的ID
     * @return 同步配置信息
     */
    SyncConfig selectById(@Param("id") Long id);
}