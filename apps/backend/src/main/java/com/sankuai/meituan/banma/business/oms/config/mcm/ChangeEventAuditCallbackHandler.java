package com.sankuai.meituan.banma.business.oms.config.mcm;

import cn.hutool.core.bean.BeanUtil;
import com.sankuai.mcm.client.sdk.annotation.McmComponent;
import com.sankuai.mcm.client.sdk.context.handler.callback.AuditCallbackHandlerAdaptor;
import com.sankuai.mcm.client.sdk.context.handler.callback.AuditResultRequest;
import com.sankuai.mcm.client.sdk.dto.common.EventContext;
import com.sankuai.meituan.banma.business.oms.common.constant.McmAuditCallbackStatus;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.exception.PoiAggreQueryManagerException;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager.*;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInvocation;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 自定义审核回调处理器
 * 处理审核回调
 *
 * {@link AuditResultRequest} 中的args[0]就是接口需要的参数 {@link McmChangeConfigDTO}
 *
 * <AUTHOR>
 */
@Slf4j
@McmComponent(events = {McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME})
public class ChangeEventAuditCallbackHandler extends AuditCallbackHandlerAdaptor {

    @Resource
    private QueryFieldMetadataService queryFieldMetadataService;

    @Resource
    private DSLConfigService dslConfigService;

    @Resource
    private SyncFieldMetadataService syncFieldMetadataService;

    @Resource
    private SyncConfigService syncConfigService;

    @Override
    public Object auditAccepted(MethodInvocation methodInvocation, AuditResultRequest auditResultRequest) {
        log.info("审核通过回调逻辑: {}", auditResultRequest.toString());
        callbackHandle(auditResultRequest, McmAuditCallbackStatus.ACCEPTED.getCode());
        return "审核通过回调逻辑";
    }

    @Override
    public Object auditRejected(MethodInvocation methodInvocation, AuditResultRequest auditResultRequest) {
        log.info("审核驳回回调逻辑");
        callbackHandle(auditResultRequest, McmAuditCallbackStatus.REJECTED.getCode());
        return "审核驳回回调逻辑";
    }

    @Override
    public Object auditCancel(MethodInvocation methodInvocation, AuditResultRequest auditResultRequest) {
        log.info("审核撤销回调逻辑");
        callbackHandle(auditResultRequest, McmAuditCallbackStatus.CANCEL.getCode());
        return "审核撤销回调逻辑";
    }

    @Override
    public Object auditIgnored(MethodInvocation methodInvocation, AuditResultRequest auditResultRequest) {
        log.info("审核忽略回调逻辑");
        return "审核忽略回调逻辑，mcm没有生成审核链路，无需审核";
    }

    @Override
    public Object auditAuditing(MethodInvocation methodInvocation, AuditResultRequest auditResultRequest) {
        log.info("审核中回调逻辑");
        return "审核中回调逻辑，mcm审核发起后，审核中间通过时回调";
    }

    private void callbackHandle(AuditResultRequest auditResultRequest, Integer auditCallbackAcceptCode) {
        EventContext eventContext = auditResultRequest.getEventContext();
        Map<String, Object> requestParameters = eventContext.getRequestParameters();
        McmChangeConfigDTO mcmChangeConfigDTO = BeanUtil.mapToBean(requestParameters, McmChangeConfigDTO.class, false);
        String requestBaseUrl = mcmChangeConfigDTO.getRequestBaseUrl();

        String eventUuid = eventContext.getEventUuid();

        Map<String, IMcmHandleService> routeMap = McmConfig.POI_AGGRE_MANAGE_CHANGE_REQUEST_BASE_URL_MAP;
        if (!routeMap.containsKey(requestBaseUrl)) {
            throw new PoiAggreQueryManagerException("请求baseUrl不存在");
        }
        IMcmHandleService mcmHandleService = routeMap.get(requestBaseUrl);
        if (auditCallbackAcceptCode == McmAuditCallbackStatus.ACCEPTED.getCode()) {
            mcmHandleService.mcmAuditAccepted(eventUuid, mcmChangeConfigDTO);
        } else if (auditCallbackAcceptCode == McmAuditCallbackStatus.REJECTED.getCode()) {
            mcmHandleService.mcmAuditRejected(eventUuid, mcmChangeConfigDTO);
        } else if (auditCallbackAcceptCode == McmAuditCallbackStatus.CANCEL.getCode()) {
            mcmHandleService.mcmAuditCancel(eventUuid, mcmChangeConfigDTO);
        }
    }
}