package com.sankuai.meituan.banma.business.oms.controller;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 服务存活检测控制器
 */
@Controller
public class TestAliveController {
    
    /**
     * 检测服务是否存活
     * @return 服务状态信息
     */
    @RequestMapping("/api/monitor/alive")
    @ResponseBody
    public Object isAlive1() {
        return CommonResult.success("I am alive!");
    }

    /**
     * 检测服务是否存活
     * @return 服务状态信息
     */
    @RequestMapping("/monitor/alive")
    @ResponseBody
    public Object isAlive2() {
        return CommonResult.success("I am alive!");
    }
    
    /**
     * 测试接口
     * @return 测试信息
     */
    @RequestMapping("/api/monitor/test")
    @ResponseBody
    public Object test1() {
        return CommonResult.success("I am test!");
    }

    /**
     * 测试接口
     * @return 测试信息
     */
    @RequestMapping("/monitor/test")
    @ResponseBody
    public Object test2() {
        return CommonResult.success("I am test!");
    }

    /**
     * 业务页面
     * @return 页面路径
     */
    @RequestMapping("/businessoms")
    public String log() {
        return "businessoms/test";
    }
} 