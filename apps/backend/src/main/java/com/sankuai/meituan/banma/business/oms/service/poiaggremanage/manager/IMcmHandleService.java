package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager;

import com.sankuai.meituan.banma.business.oms.common.constant.McmTaskStatus;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask;

/**
 * MCM处理服务
 * <AUTHOR>
 */
public interface IMcmHandleService {

    /**
     * 前置校验
     * @param requestUri 请求uri
     * @param mcmChangeConfigDTO 变更配置
     * @return true-校验通过
     */
    boolean preCheck(String requestUri, McmChangeConfigDTO mcmChangeConfigDTO);

    /**
     * 更新状态为审核中
     * @param mcmUrl mcm链接
     * @param eventUuid 事件uuid
     * @param mcmChangeConfigDTO 变更配置
     * @return true-更新成功
     */
    boolean addOperateTaskAndUpdateRecordStatus2Auditing(String mcmUrl, String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO);

    /**
     * 审核通过
     */
    void mcmAuditAccepted(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO);

    /**
     * 审核驳回
     */
    void mcmAuditRejected(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO);

    /**
     * 审核撤销
     */
    void mcmAuditCancel(String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO);

    default McmOperateTask getAuditingMcmOperateTask(String mcmUrl, String eventUuid, McmChangeConfigDTO mcmChangeConfigDTO, Integer taskType) {
        McmOperateTask mcmOperateTask = new McmOperateTask();
        mcmOperateTask.setTaskType(taskType);
        mcmOperateTask.setTaskName(mcmChangeConfigDTO.getChangeDescription());
        mcmOperateTask.setMcmUrl(mcmUrl);
        mcmOperateTask.setMcmEventUuid(eventUuid);
        mcmOperateTask.setStatus(McmTaskStatus.AUDITING.getCode());
        return mcmOperateTask;
    }

    default McmOperateTask getCompletedMcmOperateTask(String eventUuid, Integer status) {
        McmOperateTask mcmOperateTask = new McmOperateTask();
        mcmOperateTask.setMcmEventUuid(eventUuid);
        mcmOperateTask.setStatus(status);
        return mcmOperateTask;
    }
}
