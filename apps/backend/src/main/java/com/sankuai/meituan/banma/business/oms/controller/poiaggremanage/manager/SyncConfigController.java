package com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.manager;

import com.sankuai.mcm.client.sdk.config.annotation.McmHandler;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.common.constant.McmRequest;
import com.sankuai.meituan.banma.business.oms.common.dto.McmChangeConfigDTO;
import com.sankuai.meituan.banma.business.oms.common.dto.PageResultDTO;
import com.sankuai.meituan.banma.business.oms.config.mcm.McmConfig;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.manager.page.SyncConfigPageQueryDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncConfig;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.manager.SyncConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 聚合查询同步配置Controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/poiAggreManage/manager/sync/config")
@Validated
public class SyncConfigController {

    @Resource
    private SyncConfigService syncConfigService;

    /**
     * 新增同步配置
     *
     * @param mcmChangeConfigDTO 变更配置DTO
     * @return 操作结果
     */
    @McmHandler(eventName = McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME)
    @PostMapping(McmRequest.ADD)
    public String add(@RequestBody McmChangeConfigDTO mcmChangeConfigDTO) {
        return mcmChangeConfigDTO.toString();
    }

    /**
     * 更新同步配置
     *
     * @param mcmChangeConfigDTO 变更配置DTO
     * @return 操作结果
     */
    @McmHandler(eventName = McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME)
    @PostMapping(McmRequest.UPDATE)
    public String update(@RequestBody McmChangeConfigDTO mcmChangeConfigDTO) {
        return mcmChangeConfigDTO.toString();
    }

    /**
     * 批量删除同步配置
     *
     * @param mcmChangeConfigDTO 变更配置DTO
     * @return 操作结果
     */
    @McmHandler(eventName = McmConfig.POI_APPGRE_MANAGE_CHANGE_EVENT_NAME)
    @PostMapping(McmRequest.BATCH_DELETE)
    public String batchDeleteByIds(@RequestBody McmChangeConfigDTO mcmChangeConfigDTO) {
        return mcmChangeConfigDTO.toString();
    }

    /**
     * 分页查询同步配置
     *
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    @PostMapping("/page")
    public CommonResult<PageResultDTO<SyncConfig>> page(@RequestBody @Valid SyncConfigPageQueryDTO pageQuery) {
        log.info("分页查询同步配置请求: {}", pageQuery);
        return syncConfigService.page(pageQuery);
    }
} 