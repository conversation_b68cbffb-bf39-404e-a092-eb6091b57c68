package com.sankuai.meituan.banma.business.oms.common.constant;

import lombok.Getter;

/**
 * Mcm审核回调状态
 * <AUTHOR>
 */
@Getter
public enum McmAuditCallbackStatus {
    ACCEPTED(0, "通过"),
    REJECTED(1, "驳回"),
    CANCEL(2, "撤销");

    private final Integer code;

    private final String desc;

    McmAuditCallbackStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static McmAuditCallbackStatus getByCode(int code) {
        for (McmAuditCallbackStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
