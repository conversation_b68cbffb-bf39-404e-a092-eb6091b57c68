package com.sankuai.meituan.banma.business.oms.thrift.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.JsonSchemaDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.ServiceInterfaceDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools.RocketHost;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.tools.ThriftServiceToolService;
import com.sankuai.meituan.banma.business.oms.thrift.RpcInformationIface;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 查询Thrift接口信息
 * <AUTHOR>
 */
@Slf4j
@MdpThriftServer()
public class RpcInformationIfaceImpl implements RpcInformationIface {

    @Resource
    private ThriftServiceToolService thriftServiceToolService;

    @Override
    public String getMethodInfoByAppkeyAndMethodName(String ifaceInfo) {
        JSONObject jsonObject = JSON.parseObject(ifaceInfo);
        Map<String, Object> params = jsonObject.getInnerMap();

        if (!params.containsKey("appkey") || !params.containsKey("serviceName") || !params.containsKey("methodName")) {
            return JSON.toJSONString(CommonResult.failed(400, "Thrift接口信息不全"));
        }
        String appkey = params.get("appkey").toString();
        String serviceName = params.get("serviceName").toString();
        String methodName = params.get("methodName").toString();
        String envStr = MdpContextUtils.getHostEnvStr();
        List<RocketHost> hosts = thriftServiceToolService.getAppkeyHosts(appkey, envStr);

        // 从所有节点中查询service信息
        for (RocketHost host : hosts) {
            List<JsonSchemaDTO.MethodInfo> methods = null;
            try {
                ServiceInterfaceDTO serviceInterfaceDTO = thriftServiceToolService.getServiceInterfaces(host.getIpLan());
                ServiceInterfaceDTO.ServiceInfo serviceInfo = serviceInterfaceDTO.getServiceInfo().get(0);
                // 校验该节点中是否包含service
                boolean serviceExist = false;
                List<ServiceInterfaceDTO.ServiceIfaceInfo> serviceIfaceInfos = serviceInfo.getServiceIfaceInfos();
                for (ServiceInterfaceDTO.ServiceIfaceInfo serviceIfaceInfo : serviceIfaceInfos) {
                    if (serviceIfaceInfo.getServiceName().equals(serviceName)) {
                        serviceExist = true;
                        break;
                    }
                }

                if (!serviceExist) {
                    continue;
                }

                JsonSchemaDTO methodJsonSchema = thriftServiceToolService.getMethodJsonSchema(host.getIpLan(), serviceName, methodName);
                methods = methodJsonSchema.getMethods();
            } catch (Exception e) {
                log.error("当前节点查询Thrift接口信息失败，host: {}", host.getIpLan(), e);
                continue;
            }
            if (methods == null || methods.isEmpty()) {
                continue;
            }
            return JSON.toJSONString(CommonResult.success(methods));
        }
        return JSON.toJSONString(CommonResult.failed("Thrift接口信息查询失败"));
    }
}
