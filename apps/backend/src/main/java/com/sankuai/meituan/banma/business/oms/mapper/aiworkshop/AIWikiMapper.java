package com.sankuai.meituan.banma.business.oms.mapper.aiworkshop;

import com.sankuai.meituan.banma.business.oms.entity.aiworkshop.AIWiki;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI Wiki Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface AIWikiMapper {

    /**
     * 插入Wiki
     * @param wiki Wiki信息
     * @return 影响行数
     */
    int insert(AIWiki wiki);

    /**
     * 更新Wiki
     * @param wiki Wiki信息
     * @return 影响行数
     */
    int update(AIWiki wiki);

    /**
     * 根据ID删除Wiki
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 分页查询Wiki列表
     * @param type wiki类型
     * @param wikiParam 分页查询参数
     * @param offset 偏移量
     * @return Wiki列表
     */
    List<AIWiki> page(@Param("type") Integer type, @Param("wikiParam") String wikiParam, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取总数
     * @param type wiki类型
     * @param wikiParam 分页查询参数
     * @return 总数
     */
    Integer count(@Param("type") Integer type, @Param("wikiParam") String wikiParam);

    /**
     * 根据标题和类型查询
     * @param title 标题
     * @param type wiki类型
     * @return Wiki信息
     */
    AIWiki getByTitleAndType(@Param("title") String title, @Param("type") Integer type);
}