package com.sankuai.meituan.banma.business.oms.controller.poiaggremanage.tools;

import com.sankuai.meituan.banma.business.oms.common.CommonResult;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.JsonSchemaDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.ServiceInterfaceDTO;
import com.sankuai.meituan.banma.business.oms.dto.poiaggremanage.tools.ServiceMethodDTO;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools.RocketHost;
import com.sankuai.meituan.banma.business.oms.service.poiaggremanage.tools.ThriftServiceToolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Thrift服务工具控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/poiAggreManage/thriftServiceTool")
public class ThriftServiceToolController {
    private static final Logger logger = LoggerFactory.getLogger(ThriftServiceToolController.class);

    @Autowired
    private ThriftServiceToolService thriftServiceToolService;


    @GetMapping("/hosts")
    public CommonResult<List<RocketHost>> getAppkeyHosts(@RequestParam String appkey, @RequestParam(defaultValue = "test") String env) {
        logger.info("获取Rocket主机信息");
        try {
            List<RocketHost> rocketHosts = thriftServiceToolService.getAppkeyHosts(appkey, env);
            return CommonResult.success(rocketHosts);
        } catch (Exception e) {
            logger.error("获取Rocket主机信息失败", e);
            return CommonResult.failed("获取Rocket主机信息失败: " + e);
        }
    }
    
    /**
     * 获取服务接口信息
     * @param hostName 主机名称/IP
     * @return 服务接口信息
     */
    @GetMapping("/interfaces")
    public CommonResult<ServiceInterfaceDTO> getServiceInterfaces(@RequestParam String hostName) {
        logger.info("获取服务接口信息, hostName: {}", hostName);
        try {
            ServiceInterfaceDTO serviceInterfaceDTO = thriftServiceToolService.getServiceInterfaces(hostName);
            return CommonResult.success(serviceInterfaceDTO);
        } catch (Exception e) {
            logger.error("获取服务接口信息失败", e);
            return CommonResult.failed("获取服务接口信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务方法信息
     * @param hostName 主机名称/IP
     * @param serviceName 服务名称
     * @return 服务方法信息
     */
    @GetMapping("/methods")
    public CommonResult<ServiceMethodDTO> getServiceMethods(
            @RequestParam String hostName,
            @RequestParam String serviceName) {
        logger.info("获取服务方法信息, hostName: {}, serviceName: {}", hostName, serviceName);
        try {
            ServiceMethodDTO serviceMethodDTO = thriftServiceToolService.getServiceMethods(hostName, serviceName);
            return CommonResult.success(serviceMethodDTO);
        } catch (Exception e) {
            logger.error("获取服务方法信息失败", e);
            return CommonResult.failed("获取服务方法信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取方法参数JSON Schema
     * @param hostName 主机名称/IP
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @return 方法参数JSON Schema
     */
    @GetMapping("/schema")
    public CommonResult<JsonSchemaDTO> getMethodJsonSchema(
            @RequestParam String hostName,
            @RequestParam String serviceName,
            @RequestParam String methodName) {
        logger.info("获取方法参数JSON Schema, hostName: {}, serviceName: {}, methodName: {}", 
                hostName, serviceName, methodName);
        try {
            JsonSchemaDTO jsonSchemaDTO = thriftServiceToolService.getMethodJsonSchema(hostName, serviceName, methodName);
            return CommonResult.success(jsonSchemaDTO);
        } catch (Exception e) {
            logger.error("获取方法参数JSON Schema失败", e);
            return CommonResult.failed("获取方法参数JSON Schema失败: " + e.getMessage());
        }
    }
} 