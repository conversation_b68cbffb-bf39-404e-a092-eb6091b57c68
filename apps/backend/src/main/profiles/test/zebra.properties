# MDP DOCS=https://km.sankuai.com/custom/onecloud/page/1302115131
mdp.zebra[0].jdbcRef=banmapoiaggre_poi_aggre_test
# 对于读写分离必须强制指定类型
mdp.zebra[0].dataSourceType=group
# 包含Mapper接口类的package，如果存在多个，可用逗号隔开
mdp.zebra[0].basePackage=com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage
# 如果sql写在xml中则需要该配置，如果sql写在接口注解中则不需要该配置，如果存在多个，可用逗号隔开
mdp.zebra[0].mapperLocations=classpath*:mappers/poiaggremanage/manager/*.xml
# 连接池初始化连接数，默认取自RDS平台，建议显式配置
mdp.zebra[0].initialPoolSize=3
# 连接池最小连接数，默认取自RDS平台，建议显式配置
mdp.zebra[0].minPoolSize=4
# 连接池最大连接数，默认取自RDS平台，建议显式配置
mdp.zebra[0].maxPoolSize=8
mdp.zebra[0].transactionName=TM0


mdp.zebra[1].jdbcRef=banmallmcorpus_llm_corpus_test
# 对于读写分离必须强制指定类型
mdp.zebra[1].dataSourceType=group
# 包含Mapper接口类的package，如果存在多个，可用逗号隔开
mdp.zebra[1].basePackage=com.sankuai.meituan.banma.business.oms.mapper.aiworkshop
# 如果sql写在xml中则需要该配置，如果sql写在接口注解中则不需要该配置，如果存在多个，可用逗号隔开
mdp.zebra[1].mapperLocations=classpath*:mappers/aiworkshop/*.xml
# 连接池初始化连接数，默认取自RDS平台，建议显式配置
mdp.zebra[1].initialPoolSize=3
# 连接池最小连接数，默认取自RDS平台，建议显式配置
mdp.zebra[1].minPoolSize=4
# 连接池最大连接数，默认取自RDS平台，建议显式配置
mdp.zebra[1].maxPoolSize=8
mdp.zebra[1].transactionName=TM1