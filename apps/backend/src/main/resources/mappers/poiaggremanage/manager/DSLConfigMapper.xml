<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.DSLConfigMapper">
    <!-- 表名 -->
    <sql id="TABLE">bm_poi_aggre_query_dsl${@com.sankuai.meituan.banma.business.oms.common.util.TableNameUtil@getTableNameSuffix()}</sql>

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.DSLConfig">
        <id column="id" property="id"/>
        <result column="dsl_name" property="dslName"/>
        <result column="dsl_description" property="dslDescription"/>
        <result column="dsl" property="dsl"/>
        <result column="field_codes" property="fieldCodes"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler"/>
        <result column="thrown_exception" property="thrownException"/>
        <result column="dsl_pre_heat_param" property="dslPreHeatParam"/>
        <result column="option" property="option"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.MapTypeHandler"/>
        <result column="support_qps" property="supportQps"/>
        <result column="actual_qps" property="actualQps"/>
        <result column="valid" property="valid"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="op_name" property="opName"/>
        <result column="op_mis" property="opMis"/>
        <result column="ctime" property="ctime"/>
        <result column="utime" property="utime"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id
        , dsl_name, dsl_description, dsl, field_codes, thrown_exception, dsl_pre_heat_param, `option`, support_qps, actual_qps, valid, status, op_name, op_mis, ctime, utime
    </sql>

    <!-- 条件查询 -->
    <sql id="Condition_Where_Clause">
        <where>
            <if test="dslName != null and dslName != ''">
                AND dsl_name LIKE CONCAT('%', #{dslName}, '%')
            </if>
            <if test="dslDescription != null and dslDescription != ''">
                AND dsl_description LIKE CONCAT('%', #{dslDescription}, '%')
            </if>
            AND valid = 1
        </where>
    </sql>

    <!-- 插入DSL配置 -->
    <insert id="insert" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.DSLConfig" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (dsl_name, dsl_description, dsl, field_codes, thrown_exception,
        dsl_pre_heat_param, `option`, support_qps, actual_qps, valid, status, op_name, op_mis, ctime, utime)
        VALUES (#{dslName}, #{dslDescription}, #{dsl},
        #{fieldCodes, typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
        #{thrownException}, #{dslPreHeatParam},
        IFNULL(#{option, typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.MapTypeHandler}, '{}'),
        IFNULL(#{supportQps}, 0), IFNULL(#{actualQps}, 0), 1, 0, #{opName}, #{opMis}, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.DSLConfig">
        UPDATE
        <include refid="TABLE"/>
        <set>
            <if test="dslName != null">dsl_name = #{dslName},</if>
            <if test="dslDescription != null">dsl_description = #{dslDescription},</if>
            <if test="dsl != null">dsl = #{dsl},</if>
            <if test="fieldCodes != null">field_codes =
                #{fieldCodes, typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
            </if>
            <if test="thrownException != null">thrown_exception = #{thrownException},</if>
            <if test="dslPreHeatParam != null">dsl_pre_heat_param = #{dslPreHeatParam},</if>
            <if test="option != null">`option` = #{option, typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.MapTypeHandler},</if>
            <if test="supportQps != null">support_qps = #{supportQps},</if>
            <if test="actualQps != null">actual_qps = #{actualQps},</if>
            <if test="status != null">status = #{status},</if>
            <if test="opName != null">op_name = #{opName},</if>
            <if test="opMis != null">op_mis = #{opMis},</if>
            utime = UNIX_TIMESTAMP()
        </set>
        WHERE id = #{id}
        AND valid = 1
    </update>

    <update id="batchUpdateStatus">
        UPDATE
        <include refid="TABLE"/>
        SET status = #{status}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </update>

    <!-- 根据编排名称查询 -->
    <select id="selectByDslName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE dsl_name = #{dslName}
        AND valid = 1
        LIMIT 1
    </select>

    <!-- 根据ID列表查询 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </select>

    <!-- 批量删除 -->
    <update id="batchDeleteByIds" parameterType="java.util.List">
        UPDATE
        <include refid="TABLE"/>
        SET valid = 0, utime = UNIX_TIMESTAMP()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </update>

    <!-- 分页查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        <include refid="Condition_Where_Clause"/>
        ORDER BY utime DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 条件统计 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*) FROM
        <include refid="TABLE"/>
        <include refid="Condition_Where_Clause"/>
    </select>
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE id = #{id}
        AND valid = 1
    </select>
</mapper> 