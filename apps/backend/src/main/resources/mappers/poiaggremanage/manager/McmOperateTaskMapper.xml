<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.changelog.McmOperateTaskMapper">

    <sql id="TABLE">bm_poi_aggre_operate_task${@com.sankuai.meituan.banma.business.oms.common.util.TableNameUtil@getTableNameSuffix()}</sql>

    <resultMap id="BaseResultMap"
               type="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask">
        <id column="id" property="id"/>
        <result column="task_type" property="taskType"/>
        <result column="task_name" property="taskName"/>
        <result column="mcm_url" property="mcmUrl"/>
        <result column="mcm_event_uuid" property="mcmEventUuid"/>
        <result column="status" property="status"/>
        <result column="valid" property="valid"/>
        <result column="op_name" property="opName"/>
        <result column="op_mis" property="opMis"/>
        <result column="ctime" property="ctime"/>
        <result column="utime" property="utime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , task_type, task_name, mcm_url, mcm_event_uuid, status, valid, op_name, op_mis, ctime, utime
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask">
        INSERT INTO
        <include refid="TABLE"/>
        (task_type, task_name, mcm_url, mcm_event_uuid, status, valid, op_name, op_mis, ctime, utime)
        VALUES
        (#{taskType}, #{taskName}, #{mcmUrl}, #{mcmEventUuid}, #{status}, 1, #{opName}, #{opMis}, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
    </insert>

    <update id="updateByEventUuid"
            parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.changelog.McmOperateTask">
        UPDATE
        <include refid="TABLE"/>
        <set>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="mcmUrl != null">mcm_url = #{mcmUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="valid != null">valid = #{valid},</if>
            <if test="opName != null">op_name = #{opName},</if>
            <if test="opMis != null">op_mis = #{opMis},</if>
            utime = UNIX_TIMESTAMP()
        </set>
        WHERE mcm_event_uuid = #{mcmEventUuid}
    </update>

    <select id="page" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        <where>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="opMis != null">
                AND op_mis = #{opMis}
            </if>
            AND valid = 1
        </where>
        ORDER BY ctime DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="count" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="TABLE"/>
        <where>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="opMis != null">
                AND op_mis = #{opMis}
            </if>
            AND valid = 1
        </where>
    </select>

</mapper> 