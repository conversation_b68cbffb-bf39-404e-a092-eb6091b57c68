<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.QueryFieldMetadataMapper">
    <!-- 表名 -->
    <sql id="TABLE">bm_poi_aggre_query_field_metadata${@com.sankuai.meituan.banma.business.oms.common.util.TableNameUtil@getTableNameSuffix()}</sql>

    <resultMap id="BaseResultMap" type="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryFieldMetadata">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="field_code" jdbcType="VARCHAR" property="fieldCode"/>
        <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="field_property" jdbcType="VARCHAR" property="fieldProperty"/>
        <result column="dependent_fields" jdbcType="VARCHAR" property="dependentFields"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler"/>
        <result column="synced_field" jdbcType="BOOLEAN" property="syncedField"/>
        <result column="handler_type" jdbcType="INTEGER" property="handlerType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="op_name" jdbcType="VARCHAR" property="opName"/>
        <result column="op_mis" jdbcType="VARCHAR" property="opMis"/>
        <result column="valid" jdbcType="TINYINT" property="valid"/>
        <result column="ctime" jdbcType="INTEGER" property="ctime"/>
        <result column="utime" jdbcType="INTEGER" property="utime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , field_code, field_name, description, type, default_value, field_property, dependent_fields, synced_field, handler_type, status, op_name, op_mis, valid, ctime, utime
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryFieldMetadata"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (field_code, field_name, description, type, default_value,
        field_property, dependent_fields, synced_field, handler_type, status, op_name, op_mis, valid, ctime,
        utime)
        VALUES (#{fieldCode}, #{fieldName}, #{description}, #{type}, #{defaultValue}, #{fieldProperty},
        #{dependentFields,jdbcType=VARCHAR,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
        #{syncedField},
        IFNULL(#{handlerType}, 1),
        0, #{opName}, #{opMis}, 1,
        UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
    </insert>

    <!-- 批量插入字段元数据 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (field_code, field_name, description, type, default_value,
        field_property, dependent_fields, synced_field, handler_type, status, op_name, op_mis, valid, ctime,
        utime)
        VALUES
        <foreach collection="records" item="item" separator=",">
            (#{item.fieldCode}, #{item.fieldName}, #{item.description}, #{item.type}, #{item.defaultValue},
            #{item.fieldProperty},
            #{item.dependentFields,jdbcType=VARCHAR,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
            #{item.syncedField},
            IFNULL(#{item.handlerType}, 1),
            0, #{item.opName}, #{item.opMis}, 1,
            UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
        </foreach>
    </insert>

    <update id="updateById" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryFieldMetadata">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="fieldCode != null">field_code = #{fieldCode},</if>
            <if test="fieldName != null">field_name = #{fieldName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
            <if test="defaultValue != null">default_value = #{defaultValue},</if>
            <if test="fieldProperty != null">field_property = #{fieldProperty},</if>
            <if test="syncedField != null">synced_field = #{syncedField},</if>
            <if test="dependentFields != null">dependent_fields =
                #{dependentFields,jdbcType=VARCHAR,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
            </if>
            <if test="status != null">status = #{status},</if>
            <if test="opName != null">op_name = #{opName},</if>
            <if test="opMis != null">op_mis = #{opMis},</if>
            utime = UNIX_TIMESTAMP()
        </set>
        WHERE id = #{id}
        AND valid = 1
    </update>
    <update id="batchUpdateStatus">
        UPDATE
        <include refid="TABLE"/>
        SET status = #{status}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </update>

    <update id="batchDeleteByIds">
        UPDATE
        <include refid="TABLE"/>
        SET valid = 0, utime = UNIX_TIMESTAMP()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </update>

    <select id="selectByFieldCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE field_code = #{fieldCode}
        AND valid = 1
    </select>

    <sql id="Page_Where_Clause">
        <where>
            <if test="fieldCode != null and fieldCode != ''">
                AND field_code LIKE CONCAT('%', #{fieldCode}, '%')
            </if>
            <if test="fieldName != null and fieldName != ''">
                AND field_name LIKE CONCAT('%', #{fieldName}, '%')
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="handlerType != null">
                AND handler_type = #{handlerType}
            </if>
            AND valid = 1
        </where>
    </sql>

    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="TABLE"/>
        <include refid="Page_Where_Clause"/>
        ORDER BY utime DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectPageCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="TABLE"/>
        <include refid="Page_Where_Clause"/>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE id = #{id}
        AND valid = 1
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </select>

    <select id="selectByFieldProperty" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE field_property = #{fieldProperty}
        AND valid = 1
    </select>

    <select id="selectByFieldCodeOrFieldProperty" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE (field_code = #{fieldCode}
        or field_property = #{fieldProperty}
        or field_code = #{fieldProperty}
        or field_property = #{fieldCode})
        AND valid = 1
        LIMIT 1 
    </select>
</mapper> 