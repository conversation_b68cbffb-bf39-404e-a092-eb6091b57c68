<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.SyncConfigMapper">
    <!-- 表名 -->
    <sql id="TABLE">bm_poi_aggre_sync_config${@com.sankuai.meituan.banma.business.oms.common.util.TableNameUtil@getTableNameSuffix()}</sql>

    <resultMap id="BaseResultMap" type="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sync_tenant" jdbcType="INTEGER" property="syncTenant"/>
        <result column="sync_scene" jdbcType="INTEGER" property="syncScene"/>
        <result column="sync_name" jdbcType="VARCHAR" property="syncName"/>
        <result column="sync_description" jdbcType="VARCHAR" property="syncDescription"/>
        <result column="sync_field_codes" jdbcType="VARCHAR" property="syncFieldCodes"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="is_poi_outer" jdbcType="TINYINT" property="isPoiOuter"/>
        <result column="total_poi_consistency_check" jdbcType="VARCHAR" property="totalPoiConsistencyCheck"/>
        <result column="mafka_consume_config" jdbcType="VARCHAR" property="mafkaConsumeConfig"/>
        <result column="query_data_config" jdbcType="VARCHAR" property="queryDataConfig"/>
        <result column="query_change_ids_config" jdbcType="VARCHAR" property="queryChangeIdsConfig"/>
        <result column="dts_sync_config" jdbcType="VARCHAR" property="dtsSyncConfig"/>
        <result column="valid" jdbcType="TINYINT" property="valid"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="op_name" jdbcType="VARCHAR" property="opName"/>
        <result column="op_mis" jdbcType="VARCHAR" property="opMis"/>
        <result column="ctime" jdbcType="INTEGER" property="ctime"/>
        <result column="utime" jdbcType="INTEGER" property="utime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        sync_tenant, sync_scene, sync_name, sync_description, sync_field_codes, type, is_poi_outer, total_poi_consistency_check,
        mafka_consume_config, query_data_config, query_change_ids_config, dts_sync_config,
        valid, status, op_name, op_mis, ctime, utime
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncConfig" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (sync_tenant, sync_scene, sync_name, sync_description, sync_field_codes,
        type, is_poi_outer, total_poi_consistency_check, mafka_consume_config, query_data_config,
        query_change_ids_config, dts_sync_config, valid, status, op_name, op_mis, ctime,
        utime)
        VALUES (#{syncTenant}, #{syncScene}, #{syncName}, #{syncDescription},
        #{syncFieldCodes,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
        #{type}, #{isPoiOuter}, #{totalPoiConsistencyCheck},
        #{mafkaConsumeConfig}, #{queryDataConfig}, #{queryChangeIdsConfig}, #{dtsSyncConfig},
        1, 0, #{opName}, #{opMis}, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
    </insert>

    <update id="update" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncConfig">
        UPDATE
        <include refid="TABLE"/>
        <set>
            <if test="syncTenant != null">sync_tenant = #{syncTenant},</if>
            <if test="syncScene != null">sync_scene = #{syncScene},</if>
            <if test="syncName != null">sync_name = #{syncName},</if>
            <if test="syncDescription != null">sync_description = #{syncDescription},</if>
            <if test="syncFieldCodes != null">sync_field_codes =
                #{syncFieldCodes,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
            </if>
            <if test="type != null">type = #{type},</if>
            <if test="isPoiOuter != null">is_poi_outer = #{isPoiOuter},</if>
            <if test="totalPoiConsistencyCheck != null">total_poi_consistency_check = #{totalPoiConsistencyCheck},</if>
            <if test="mafkaConsumeConfig != null">mafka_consume_config = #{mafkaConsumeConfig},</if>
            <if test="queryDataConfig != null">query_data_config = #{queryDataConfig},</if>
            <if test="queryChangeIdsConfig != null">query_change_ids_config = #{queryChangeIdsConfig},</if>
            <if test="dtsSyncConfig != null">dts_sync_config = #{dtsSyncConfig},</if>
            <if test="status != null">status = #{status},</if>
            <if test="opName != null">op_name = #{opName},</if>
            <if test="opMis != null">op_mis = #{opMis},</if>
            utime = UNIX_TIMESTAMP()
        </set>
        WHERE id = #{id}
        AND valid = 1
    </update>

    <update id="batchUpdateStatus">
        UPDATE
        <include refid="TABLE"/>
        SET status = #{status}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </update>

    <update id="deleteByIds">
        UPDATE
        <include refid="TABLE"/>
        SET valid = 0, utime = UNIX_TIMESTAMP()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </update>

    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        <where>
            <if test="syncTenant != null">
                AND sync_tenant = #{syncTenant}
            </if>
            <if test="syncScene != null">
                AND sync_scene = #{syncScene}
            </if>
            <if test="syncName != null and syncName != ''">
                AND sync_name LIKE CONCAT('%', #{syncName}, '%')
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            AND valid = 1
        </where>
        ORDER BY utime DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectPageCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="TABLE"/>
        <where>
            <if test="syncTenant != null">
                AND sync_tenant = #{syncTenant}
            </if>
            <if test="syncScene != null">
                AND sync_scene = #{syncScene}
            </if>
            <if test="syncName != null and syncName != ''">
                AND sync_name LIKE CONCAT('%', #{syncName}, '%')
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            AND valid = 1
        </where>
    </select>

    <!-- 根据同步场景查询同步配置 -->
    <select id="selectBySyncScene" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE sync_scene = #{syncScene}
        AND valid = 1
        LIMIT 1
    </select>

    <!-- 根据ID列表批量查询同步配置 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </select>
    <select id="selectBySyncSceneOrName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE (sync_scene = #{syncScene} or sync_name = #{syncName})
        AND valid = 1
        LIMIT 1
    </select>
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE id = #{id}
        AND valid = 1
    </select>
</mapper> 