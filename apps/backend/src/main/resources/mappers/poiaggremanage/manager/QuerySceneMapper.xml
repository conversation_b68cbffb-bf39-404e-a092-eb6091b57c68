<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.QuerySceneMapper">
    <sql id="TABLE">bm_poi_aggre_query_scene${@com.sankuai.meituan.banma.business.oms.common.util.TableNameUtil@getTableNameSuffix()}</sql>

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryScene">
        <id column="id" property="id"/>
        <result column="appkeys" property="appkeys"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler"/>
        <result column="scene_name" property="sceneName"/>
        <result column="scene_description" property="sceneDescription"/>
        <result column="scene_code" property="sceneCode"/>
        <result column="prd_wikis" property="prdWikis"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler"/>
        <result column="app_type" property="appType"/>
        <result column="administrator" property="administrator"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler"/>
        <result column="scene_level" property="sceneLevel"/>
        <result column="peak_period" property="peakPeriod"/>
        <result column="field_codes" property="fieldCodes"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler"/>
        <result column="dependency_type" property="dependencyType"/>
        <result column="estimate_qps" property="estimateQps"/>
        <result column="actual_qps" property="actualQps"/>
        <result column="valid" property="valid"/>
        <result column="remark" property="remark"/>
        <result column="op_name" property="opName"/>
        <result column="op_mis" property="opMis"/>
        <result column="ctime" property="ctime"/>
        <result column="utime" property="utime"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id
        , appkeys, scene_name, scene_description, scene_code, prd_wikis, app_type,
        administrator, scene_level, peak_period, field_codes, dependency_type, estimate_qps,
        actual_qps, valid, remark, op_name, op_mis, ctime, utime
    </sql>

    <!-- 条件查询 -->
    <sql id="Query_Condition">
        <where>
            <if test="sceneCode != null and sceneCode != ''">
                AND scene_code LIKE CONCAT('%', #{sceneCode}, '%')
            </if>
            <if test="sceneName != null and sceneName != ''">
                AND scene_name LIKE CONCAT('%', #{sceneName}, '%')
            </if>
            <if test="sceneLevel != null">
                AND scene_level = #{sceneLevel}
            </if>
            <if test="valid != null">
                AND valid = #{valid}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="queryScenePage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        <include refid="Query_Condition"/>
        ORDER BY id ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计总数 -->
    <select id="countScene" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="TABLE"/>
        <include refid="Query_Condition"/>
    </select>

    <!-- 获取下一个场景编码 -->
    <select id="getNextSceneCode" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(scene_code), 100) + 1 FROM
        <include refid="TABLE"/>
        FOR UPDATE
    </select>

    <!-- 新增场景 -->
    <insert id="insert" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryScene">
        INSERT INTO
        <include refid="TABLE"/>
        (
        appkeys, scene_name, scene_description, scene_code, prd_wikis, app_type,
        administrator, scene_level, peak_period, field_codes, dependency_type,
        estimate_qps, actual_qps, valid, remark, op_name, op_mis, ctime, utime
        )
        VALUES (
        #{appkeys,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
        #{sceneName},
        #{sceneDescription},
        #{sceneCode},
        #{prdWikis,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
        #{appType},
        #{administrator,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
        #{sceneLevel},
        #{peakPeriod},
        #{fieldCodes,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
        #{dependencyType},
        #{estimateQps},
        #{actualQps},
        #{valid},
        #{remark},
        #{opName},
        #{opMis},
        UNIX_TIMESTAMP(),
        UNIX_TIMESTAMP()
        )
    </insert>

    <!-- 更新场景 -->
    <update id="update" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.QueryScene">
        UPDATE
        <include refid="TABLE"/>
        <set>
            <if test="appkeys != null">appkeys =
                #{appkeys,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
            </if>
            <if test="sceneName != null">scene_name = #{sceneName},</if>
            <if test="sceneDescription != null">scene_description = #{sceneDescription},</if>
            <if test="prdWikis != null">prd_wikis =
                #{prdWikis,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
            </if>
            <if test="appType != null">app_type = #{appType},</if>
            <if test="administrator != null">administrator =
                #{administrator,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
            </if>
            <if test="sceneLevel != null">scene_level = #{sceneLevel},</if>
            <if test="peakPeriod != null">peak_period = #{peakPeriod},</if>
            <if test="fieldCodes != null">field_codes =
                #{fieldCodes,typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},
            </if>
            <if test="dependencyType != null">dependency_type = #{dependencyType},</if>
            <if test="estimateQps != null">estimate_qps = #{estimateQps},</if>
            <if test="actualQps != null">actual_qps = #{actualQps},</if>
            <if test="valid != null">valid = #{valid},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="opName != null">op_name = #{opName},</if>
            <if test="opMis != null">op_mis = #{opMis},</if>
            utime = UNIX_TIMESTAMP()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 批量删除 -->
    <delete id="deleteByIds">
        DELETE FROM
        <include refid="TABLE"/>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据场景名称查询场景（用于查重） -->
    <select id="selectBySceneName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE scene_name = #{sceneName}
        AND valid = 1
        LIMIT 1
    </select>
</mapper> 