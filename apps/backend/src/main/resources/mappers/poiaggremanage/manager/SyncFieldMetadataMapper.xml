<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.banma.business.oms.mapper.poiaggremanage.manager.SyncFieldMetadataMapper">
    <!-- 表名 -->
    <sql id="TABLE">bm_poi_aggre_sync_field_metadata${@com.sankuai.meituan.banma.business.oms.common.util.TableNameUtil@getTableNameSuffix()}</sql>

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncFieldMetadata">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="field_code" jdbcType="VARCHAR" property="fieldCode"/>
        <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="field_property" jdbcType="VARCHAR" property="fieldProperty"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="valid" jdbcType="BIT" property="valid"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="op_name" jdbcType="VARCHAR" property="opName"/>
        <result column="op_mis" jdbcType="VARCHAR" property="opMis"/>
        <result column="ctime" jdbcType="BIGINT" property="ctime"/>
        <result column="utime" jdbcType="BIGINT" property="utime"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id
        , field_code, field_name, description, type, field_property, default_value, valid, status, op_name, op_mis, ctime, utime
    </sql>

    <!-- 插入字段元数据 -->
    <insert id="insert" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncFieldMetadata"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="TABLE"/> (field_code, field_name, description, type, field_property,
                                                      default_value, valid, status, op_name, op_mis, ctime, utime)
        VALUES (#{fieldCode}, #{fieldName}, #{description}, #{type}, #{fieldProperty}, #{defaultValue}, 1, 0,
                #{opName}, #{opMis}, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
    </insert>
    
    <!-- 批量插入字段元数据 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (field_code, field_name, description, type, field_property, 
                                                      default_value, valid, status, op_name, op_mis, ctime, utime)
        VALUES 
        <foreach collection="records" item="item" separator=",">
            (#{item.fieldCode}, #{item.fieldName}, #{item.description}, #{item.type}, #{item.fieldProperty}, 
            #{item.defaultValue}, 1, 0, #{item.opName}, #{item.opMis}, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
        </foreach>
    </insert>

    <!-- 根据ID更新字段元数据 -->
    <update id="updateById" parameterType="com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.manager.SyncFieldMetadata">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="fieldName != null">field_name = #{fieldName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
            <if test="defaultValue != null">default_value = #{defaultValue},</if>
            <if test="status != null">status = #{status},</if>
            <if test="opName != null">op_name = #{opName},</if>
            <if test="opMis != null">op_mis = #{opMis},</if>
            utime = UNIX_TIMESTAMP()
        </set>
        WHERE id = #{id}
        AND valid = 1
    </update>

    <update id="batchUpdateStatus">
        UPDATE
        <include refid="TABLE"/>
        SET status = #{status}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </update>

    <!-- 分页条件查询字段元数据 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="TABLE"/>
        <where>
            <if test="fieldCode != null and fieldCode != ''">
                AND field_code LIKE CONCAT('%', #{fieldCode}, '%')
            </if>
            <if test="fieldName != null and fieldName != ''">
                AND field_name LIKE CONCAT('%', #{fieldName}, '%')
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            AND valid = 1
        </where>
        ORDER BY utime DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 条件查询总数 -->
    <select id="selectPageCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="TABLE"/>
        <where>
            <if test="fieldCode != null and fieldCode != ''">
                AND field_code LIKE CONCAT('%', #{fieldCode}, '%')
            </if>
            <if test="fieldName != null and fieldName != ''">
                AND field_name LIKE CONCAT('%', #{fieldName}, '%')
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            AND valid = 1
        </where>
    </select>

    <!-- 批量删除字段元数据 -->
    <update id="batchDeleteByIds">
        UPDATE
        <include refid="TABLE"/>
        SET valid = 0, utime = UNIX_TIMESTAMP()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </update>

    <!-- 根据字段Code查询字段元数据 -->
    <select id="selectByFieldCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE field_code = #{fieldCode}
        AND valid = 1
    </select>

    <!-- 根据ID查询字段元数据 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE id = #{id}
        AND valid = 1
    </select>

    <!-- 根据ID列表批量查询字段元数据 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND valid = 1
    </select>

    <!-- 根据字段属性查询字段元数据 -->
    <select id="selectByFieldProperty" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        WHERE field_property = #{fieldProperty}
        AND valid = 1
    </select>

    <!-- 根据字段Code或字段属性查询字段元数据 -->
    <select id="selectByFieldCodeOrFieldProperty" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="TABLE"/>
        WHERE (field_code = #{fieldCode}
        or field_property = #{fieldProperty}
        or field_code = #{fieldProperty}
        or field_property = #{fieldCode})
        AND valid = 1
        LIMIT 1
    </select>
</mapper> 