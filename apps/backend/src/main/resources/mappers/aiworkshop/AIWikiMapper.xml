<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.banma.business.oms.mapper.aiworkshop.AIWikiMapper">

    <sql id="TABLE">ai_workshop_wiki</sql>
    
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.banma.business.oms.entity.aiworkshop.AIWiki">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="description" property="description"/>
        <result column="wiki_url" property="wikiUrl"/>
        <result column="options" property="options"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.MapTypeHandler"/>
        <result column="type" property="type"/>
        <result column="star" property="star"/>
        <result column="top" property="top"/>
        <result column="pb_name" property="pbName"/>
        <result column="department" property="department"/>
        <result column="tags" property="tags"
                typeHandler="com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler"/>
        <result column="valid" property="valid"/>
        <result column="pb_mis" property="pbMis"/>
        <result column="op_name" property="opName"/>
        <result column="op_mis" property="opMis"/>
        <result column="ctime" property="ctime"/>
        <result column="utime" property="utime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, title, description, wiki_url, options, type, star, top, pb_name, department, tags, pb_mis, op_name, op_mis, ctime, utime
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.banma.business.oms.entity.aiworkshop.AIWiki" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (
        title, description, wiki_url, options, type, star, top, pb_name, department, tags, valid, pb_mis, op_name, op_mis, ctime,
        utime
        ) VALUES (
        #{title}, #{description}, #{wikiUrl},
        IFNULL(#{options, typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.MapTypeHandler}, '{}'),
        #{type}, 0, 0, #{pbName}, #{department},
        #{tags, typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler}, 1, #{pbMis},
        #{opName},
        #{opMis}, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
        )
    </insert>

    <update id="update" parameterType="com.sankuai.meituan.banma.business.oms.entity.aiworkshop.AIWiki">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="wikiUrl != null">wiki_url = #{wikiUrl},</if>
            <if test="options != null">options = #{options, typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.MapTypeHandler},</if>
            <if test="type != null">type = #{type},</if>
            <if test="star != null">star = #{star},</if>
            <if test="top != null">top = #{top},</if>
            <if test="pbName != null">pb_name = #{pbName},</if>
            <if test="department != null">department = #{department},</if>
            <if test="tags != null">tags = #{tags, typeHandler=com.sankuai.meituan.banma.business.oms.common.handler.StringListTypeHandler},</if>
            <if test="pbMis != null">pb_mis = #{pbMis},</if>
            <if test="opName != null">op_name = #{opName},</if>
            <if test="opMis != null">op_mis = #{opMis},</if>
            utime = UNIX_TIMESTAMP()
        </set>
        WHERE id = #{id}
        AND valid = 1
    </update>

    <update id="deleteById" parameterType="com.sankuai.meituan.banma.business.oms.entity.aiworkshop.AIWiki">
        UPDATE <include refid="TABLE"/>
        SET valid = 0, utime = UNIX_TIMESTAMP()
        WHERE id = #{id}
        AND valid = 1
    </update>

    <select id="page" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="TABLE"/>
        <where>
            type = #{type}
            <if test="wikiParam != null and wikiParam != ''">
                AND (
                    title LIKE CONCAT('%', #{wikiParam}, '%')
                    OR description LIKE CONCAT('%', #{wikiParam}, '%')
                    OR tags LIKE CONCAT('%', #{wikiParam}, '%')
                    OR pb_name LIKE CONCAT('%', #{wikiParam}, '%')
                    OR department LIKE CONCAT('%', #{wikiParam}, '%')
                )
            </if>
            AND valid = 1
        </where>
        ORDER BY top DESC, utime DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="TABLE"/>
        <where>
            type = #{type}
            <if test="wikiParam != null and wikiParam != ''">
                AND (
                title LIKE CONCAT('%', #{wikiParam}, '%')
                OR description LIKE CONCAT('%', #{wikiParam}, '%')
                OR tags LIKE CONCAT('%', #{wikiParam}, '%')
                OR pb_name LIKE CONCAT('%', #{wikiParam}, '%')
                OR department LIKE CONCAT('%', #{wikiParam}, '%')
                )
            </if>
            AND valid = 1
        </where>
    </select>

    <select id="getByTitleAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="TABLE"/>
        <where>
            title = #{title}
            AND type = #{type}
            AND valid = 1
        </where>
        limit 1
    </select>

</mapper> 