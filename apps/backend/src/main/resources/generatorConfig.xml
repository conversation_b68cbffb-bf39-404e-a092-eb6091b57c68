<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="goods" targetRuntime="MyBatis3">
        <!-- 生成的entity实体引入lombok注解 Getter,Setter,Builder -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>
        <!--生成通用sql方法类，包含通用方法。共MdpMapperPlugin、MdpSimpleMapperPlugin、MdpMixedMapperPlugin
        3个插件，根据诉求决定使用哪个插件，具体区别见 https://km.sankuai.com/custom/onecloud/page/424829078 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpSimpleMapperPlugin"/>-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMixedMapperPlugin"/>-->
        <!-- 每次执行插件生成的 xml 时通用的方法会覆盖的 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- 生成批量插入方法插件，默认不需要，需要时配置此插件。使用条件：
        Context targetRuntime="Mybatis3" ; javaClientGenerator type="XMLMAPPER、MIXEDMAPPER"-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.BatchInsertPlugin"/>-->
        <!--分页插件，默认不开启。使用条件：Context targetRuntime="Mybatis3" ; javaClientGenerator
        type="XMLMAPPER、MIXEDMAPPER"-->
        <!--<plugin type="com.meituan.mdp.mybatis.generator.plugins.LimitPlugin"/>-->
        <!-- targetRuntime="Mybatis3"时需要，Example类存储路径 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">
            <property name="targetPackage" value="com.sankuai.meituan.banma.business.oms.dal.example"/>
        </plugin>
        <!-- 从数据库中的字段的comment做为生成entity的属性注释 -->
        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!--使用前替换数据库名,账号密码-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*************************************************************************************************************"
                        userId="user"
                        password="password">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.sankuai.meituan.banma.business.oms.dal.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mappers" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.meituan.banma.business.oms.dal.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--配置需要生成的表-->
        <table tableName="" domainObjectName="">
        </table>

    </context>

</generatorConfiguration>