{"name": "diamondStructure", "description": "菱形依赖结构测试", "timeout": 20, "tasks": [{"alias": "top", "taskType": "Calculate", "description": "顶部节点", "inputs": {"value": "100"}}, {"alias": "left", "taskType": "Calculate", "description": "左侧分支", "inputs": {"result": "$top.value / 2"}}, {"alias": "right", "taskType": "Calculate", "description": "右侧分支", "inputs": {"result": "$top.value * 2"}}, {"alias": "bottom", "taskType": "Calculate", "description": "底部节点", "inputs": {"diff": "$right.result - $left.result", "sum": "$right.result + $left.result"}}], "outputs": {"type": "map", "transform": "$bottom"}}