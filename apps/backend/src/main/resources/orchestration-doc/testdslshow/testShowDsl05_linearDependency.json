{"name": "linearDependency", "description": "线性依赖链 (4个节点)", "timeout": 20, "tasks": [{"alias": "node1", "taskType": "Calculate", "description": "初始数据", "inputs": {"value": "10"}}, {"alias": "node2", "taskType": "Calculate", "description": "加10", "inputs": "$node1.value + 10"}, {"alias": "node3", "taskType": "Calculate", "description": "乘2", "inputs": "$node2 * 2"}, {"alias": "node4", "taskType": "Calculate", "description": "减5", "inputs": {"result": "$node3 - 5"}}], "outputs": {"type": "map", "transform": "seq.map('final', $node4.result, 'original', $node1.value)"}}