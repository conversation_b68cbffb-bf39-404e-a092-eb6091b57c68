{"name": "fanInStructure", "description": "扇入结构测试 (3合1)", "timeout": 20, "tasks": [{"alias": "input1", "taskType": "Calculate", "description": "输入1", "inputs": "10"}, {"alias": "input2", "taskType": "Calculate", "description": "输入2", "inputs": {"value": "20"}}, {"alias": "input3", "taskType": "Calculate", "description": "输入3", "inputs": {"value": "30"}}, {"alias": "collector", "taskType": "Calculate", "description": "汇总节点", "inputs": {"sum": "$input1 + $input2.value + $input3.value", "product": "$input1 * $input2.value * $input3.value"}}], "outputs": {"type": "map", "transform": "$collector"}}