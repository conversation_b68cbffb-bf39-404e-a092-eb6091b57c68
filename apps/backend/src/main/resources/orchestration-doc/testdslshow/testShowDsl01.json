{"name": "testShowDsl01", "description": "测试DSL展示效果（3个节点，简单链式依赖）", "timeout": 20, "tasks": [{"alias": "node1", "taskType": "Calculate", "description": "node1", "inputs": {"sourcePoiIds": "$params.poiIds"}}, {"alias": "node2", "taskType": "Calculate", "description": "node2", "inputs": "count($node1.sourcePoiIds)"}, {"alias": "node3", "taskType": "Calculate", "description": "node3", "inputs": {"size": "$node2"}}], "outputs": {"type": "map", "transform": "seq.map('total', $node3.size, 'data', $node1)"}}