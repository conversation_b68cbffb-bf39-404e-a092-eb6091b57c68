{"name": "fanOutStructure", "description": "扇出结构测试 (1转3)", "timeout": 20, "tasks": [{"alias": "source", "taskType": "Calculate", "description": "数据源", "inputs": {"value": "50", "list": "[1, 2, 3, 4, 5]"}}, {"alias": "branch1", "taskType": "Calculate", "description": "分支1-加法", "inputs": "$source.value + 10"}, {"alias": "branch2", "taskType": "Calculate", "description": "分支2-乘法", "inputs": {"product": "$source.value * 2"}}, {"alias": "branch3", "taskType": "Calculate", "description": "分支3-数组处理", "inputs": {"count": "count($source.list)", "sum": "sum($source.list)"}}], "outputs": {"type": "map", "transform": "seq.map('added', $branch1, 'multiplied', $branch2.product, 'count', $branch3.count, 'sum', $branch3.sum)"}}