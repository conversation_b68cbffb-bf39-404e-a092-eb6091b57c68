{"name": "complexMixedDependency", "description": "复杂混合依赖结构", "timeout": 30, "tasks": [{"alias": "initData", "taskType": "Calculate", "description": "初始数据", "inputs": {"x": "5", "y": "10", "data": "[1, 2, 3, 4, 5]"}}, {"alias": "process1", "taskType": "Calculate", "description": "处理1", "inputs": {"xy": "$initData.x * $initData.y"}}, {"alias": "process2", "taskType": "Calculate", "description": "处理2", "inputs": {"sum": "sum($initData.data)", "count": "count($initData.data)"}}, {"alias": "process3", "taskType": "Calculate", "description": "处理3", "inputs": {"avg": "$process2.sum / $process2.count"}}, {"alias": "branch1", "taskType": "Calculate", "description": "分支1", "inputs": "$process1.xy + $process3.avg"}, {"alias": "branch2", "taskType": "Calculate", "description": "分支2", "inputs": "$process1.xy * $process3.avg"}, {"alias": "final", "taskType": "Calculate", "description": "最终结果", "inputs": {"result": "$branch1 > $branch2 ? 'A更大' : 'B更大'", "valueA": "$branch1", "valueB": "$branch2", "original": "$initData"}}], "outputs": {"type": "map", "transform": "$final"}}