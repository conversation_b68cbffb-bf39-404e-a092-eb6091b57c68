{"name": "multiLevelGridStructure", "description": "多层级网格结构测试", "timeout": 30, "tasks": [{"alias": "layer1_node1", "taskType": "Calculate", "description": "第一层节点1", "inputs": {"value": "10"}}, {"alias": "layer1_node2", "taskType": "Calculate", "description": "第一层节点2", "inputs": {"value": "20"}}, {"alias": "layer2_node1", "taskType": "Calculate", "description": "第二层节点1", "inputs": {"sum": "$layer1_node1.value + $layer1_node2.value"}}, {"alias": "layer2_node2", "taskType": "Calculate", "description": "第二层节点2", "inputs": {"product": "$layer1_node1.value * $layer1_node2.value"}}, {"alias": "layer2_node3", "taskType": "Calculate", "description": "第二层节点3", "inputs": {"diff": "$layer1_node2.value - $layer1_node1.value"}}, {"alias": "layer3_node1", "taskType": "Calculate", "description": "第三层节点1", "inputs": {"combined": "$layer2_node1.sum + $layer2_node2.product"}}, {"alias": "layer3_node2", "taskType": "Calculate", "description": "第三层节点2", "inputs": {"result": "$layer2_node3.diff * 2"}}, {"alias": "final_output", "taskType": "Calculate", "description": "最终输出", "inputs": {"finalValue": "$layer3_node1.combined - $layer3_node2.result", "stats": "seq.map('total', $layer3_node1.combined, 'diff', $layer3_node2.result)"}}], "outputs": {"type": "map", "transform": "$final_output"}}