{"name": "extremeComplexStructure", "description": "极端复杂结构 - 大规模测试", "timeout": 60, "tasks": [{"alias": "input", "taskType": "Calculate", "description": "输入数据", "inputs": {"base": "100", "factor": "2", "list": "[10, 20, 30, 40, 50]"}}, {"alias": "level1_1", "taskType": "Calculate", "description": "一级1", "inputs": "$input.base + 10"}, {"alias": "level1_2", "taskType": "Calculate", "description": "一级2", "inputs": "$input.base * $input.factor"}, {"alias": "level1_3", "taskType": "Calculate", "description": "一级3", "inputs": {"avg": "sum($input.list) / count($input.list)"}}, {"alias": "level2_1", "taskType": "Calculate", "description": "二级1", "inputs": "$level1_1 + $level1_2"}, {"alias": "level2_2", "taskType": "Calculate", "description": "二级2", "inputs": "$level1_2 - $level1_1"}, {"alias": "level2_3", "taskType": "Calculate", "description": "二级3", "inputs": {"value": "$level1_3.avg * $input.factor"}}, {"alias": "level2_4", "taskType": "Calculate", "description": "二级4", "inputs": {"max": "max($input.list)", "min": "min($input.list)"}}, {"alias": "level3_1", "taskType": "Calculate", "description": "三级1", "inputs": "$level2_1 + $level2_3.value"}, {"alias": "level3_2", "taskType": "Calculate", "description": "三级2", "inputs": "$level2_2 * $level2_3.value"}, {"alias": "level3_3", "taskType": "Calculate", "description": "三级3", "inputs": {"range": "$level2_4.max - $level2_4.min"}}, {"alias": "level4_1", "taskType": "Calculate", "description": "四级1", "inputs": {"sum": "$level3_1 + $level3_2", "diff": "$level3_1 - $level3_2"}}, {"alias": "level4_2", "taskType": "Calculate", "description": "四级2", "inputs": {"result": "$level3_3.range * $level2_3.value"}}, {"alias": "output", "taskType": "Calculate", "description": "最终输出", "inputs": {"summary": "seq.map('total', $level4_1.sum, 'difference', $level4_1.diff, 'computed', $level4_2.result)", "status": "$level4_1.sum > 1000 ? '大值' : '小值'"}}], "outputs": {"type": "map", "transform": "$output"}}