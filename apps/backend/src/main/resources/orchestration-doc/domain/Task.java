package com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务实体类
 * <AUTHOR>
 * 注：EqualsAndHashCode只保留基本信息字段，衍生计算的字段和缓存配置不包含
 */
@Data
@EqualsAndHashCode(exclude = {"cacheOption", "level", "dependencyTaskMap"})
public class Task {

    // region 基本信息
    /**
     * 任务别名
     */
    private String alias;
    /**
     * taskType 类型
     */
    private String taskType;
    // endregion


    // region 基本调用参数
    /**
     * 被调服务的url
     */
    private String url;
    /**
     * 请求的方法，如果目标服务需要指定具体方法时使用
     */
    private String method;
    /**
     * 请求的远程服务Appkey
     */
    private String remoteAppkey;
    /**
     * 请求的远程服务端口号
     */
    private String remoteServerPort;
    /**
     * 请求参数
     */
    private String inputs;
    /**
     * 请求参数的额外信息，比如输入的类型信息
     */
    private String inputsExtra;
    // endregion


    // region 服务治理
    /**
     * 请求鉴权信息
     */
    private String secret;
    /**
     * 指定泳道
     */
    private String swimlane;
    /**
     * 指定cell，set化调用
     */
    private String cell;
    /**
     * 指定liteSet字段
     */
    String liteSet;
    /**
     * 指定调用端appkey
     */
    private String appkey;
    /**
     * 指定调用ip端口
     */
    private String ipPorts;
    // endregion


    // region 异常
    /**
     * 是否忽略异常
     */
    boolean ignoreException;
    // endregion


    // region 稳定性
    /**
     * 任务超时时间, ms
     */
    private int timeout = 1000;
    /**
     * 缓存,配置时表示需要缓存执行结果（{task-input, result}），不配置表示不缓存
     */
    private CacheOption cacheOption;
    // endregion


    // region 运行控制
    /**
     * task调用开关表达式
     */
    private String switchExpression;
    // endregion


    // region 依赖
    /**
     * 依赖任务
     */
    private Map<String, Task> dependencyTaskMap = new HashMap<String, Task>();

    /**
     * 层级
     */
    private int level;

}
