package com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.util.Map;

/**
 * 工作流实体类
 * <AUTHOR>
 */
@Data
public class Workflow {

    /**
     * 流程名称
     */
    private String name;

    /**
     * 流程描述
     */
    private String description;

    /**
     * 所有的任务
     */
    private Map<String, Task> taskMap;

    /**
     * 出参信息
     */
    private JsonNode outputs;


    /**
     * 快速失败
     */
    private boolean failFast = true;

    /**
     * 超时时间（整个工作流执行），单位：毫秒
     */
    private int timeout = 1000;

    /**
     * 重试
     */
    private RetryOption retry;
}
