{
  "name": "WmPoiAggreQuery",
  "description": "批量查询外卖主数据（聚合根）数据",
  "timeout": 100,
  "retry": {
    "maxAttempts": 1,
    "delay": 10
  },
  "tasks": [
    {
      "alias": "aggreData",
      "taskType": "ThriftGeneric",
      "description": "批量查询外卖主数据（聚合根）",
      "url": "com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService",
      "method": "mgetWmPoiAggreByWmPoiIdWithSpecificFieldV2",
      "remoteAppkey": "com.sankuai.waimai.poiquery",
      "remoteServerPort": "8499",
      "timeout": 100,
      "inputs": {
        "wmPoiIds": "$params.poiIds",
        "fieldsSet": "
          let map = seq.map('wm_logistics_type', 'wm_logistics_types', 'wm_label_ids', 'label_ids', 'wm_sub_wm_poi_type', 'sub_wm_poi_type', 'wm_owner_type', 'owner_type', 'wm_agent_id', 'agent_id', 'wm_aor_id', 'aor_id', 'wm_logistics_ids', 'wm_logistics_ids', 'wm_brand_type', 'brand_type');
          let values = seq.list();
          for key in $params.fieldsSet { seq.add(values, map[key]); }
          return values;
        "
      },
      "inputsExtra": {
        "wmPoiIds": "java.util.List",
        "fieldsSet": "java.util.Set"
      }
    }
  ],
  "outputs": {
    "type": "map",
    "switch": "$aggreDataIsException == false && $aggreData != nil",
    "variables": {
      "poiAggreMap": "Collections.toMap(JSON.toObject($aggreData), 'wm_poi_id')"
    },
    "transform": "for poiId in Collections.toStringList($params.poiIds) {
      Transform.toMap(poiId,
        'wmLabelIds', Object.getPathValue(poiAggreMap, poiId + '.label_ids'),
        'wmLogisticsTypes', Object.getPathValue(poiAggreMap, poiId + '.wm_logistics_types'),
        'wmSubWmPoiType', Object.getPathValue(poiAggreMap, poiId + '.sub_wm_poi_type'),
        'wmOwnerType', Object.getPathValue(poiAggreMap, poiId + '.owner_type'),
        'wmAgentId', Object.getPathValue(poiAggreMap, poiId + '.agent_id'),
        'wmAorId', Object.getPathValue(poiAggreMap, poiId + '.aor_id'),
        'wmLogisticsIds', Object.getPathValue(poiAggreMap, poiId + '.wm_logistics_ids'),
        'wmBrandType', Object.getPathValue(poiAggreMap, poiId + '.brand_type')
      )
    }"
  }
}

