# 斑马服务编排框架 - 开发文档

## 目录
- [架构设计](#架构设计)
- [核心组件](#核心组件)
- [DSL语法规范](#DSL语法规范)
- [任务类型](#任务类型)
- [表达式引擎](#表达式引擎)
- [调度器说明](#调度器说明)
- [错误处理与重试机制](#错误处理与重试机制) 
- [使用案例详解](#使用案例详解)

## 架构设计

斑马服务编排框架采用分层设计架构，主要包含以下几个层次：

### 1. 接口层 (Facade)
- **Engine类**：提供统一接口，负责DSL解析、工作流执行和结果处理
- 对外暴露简单接口，隐藏内部实现复杂性

### 2. 解析层 (Parser)
- **WorkflowParser**：解析DSL并构建工作流对象
- **TaskParser**：解析任务定义，构建任务对象和依赖关系
- **OutputsParser**：解析输出定义，处理结果转换

### 3. 调度层 (Scheduler)
- **ConcurrentPipelineScheduler**：并发流水线调度器，用于复杂的多任务工作流
- **SingleNodeScheduler**：单节点调度器，用于单一任务工作流
- **SchedulerService**：调度服务，匹配并调用合适的调度器

### 4. 执行层 (Invoker/Executor)
- **任务执行器**：根据任务类型执行具体调用逻辑
- **表达式执行引擎**：执行DSL中的表达式

### 5. 基础设施层 (Infrastructure)
- **缓存管理**：管理工作流和任务结果缓存
- **监控与追踪**：CAT监控集成

## 核心流程

### 1. 工作流执行流程

```
+-------------+     +------------+      +-------------+      +--------------+
|  接收请求    |---->| 解析DSL     |----->| 调度任务执行  |----->| 组装返回结果  |
+-------------+     +------------+      +-------------+      +--------------+
                         |                    |
                         v                    v
                    +------------+      +-------------+
                    | 构建工作流  |      | 执行单个任务  |
                    +------------+      +-------------+
```

1. **接收请求**：Engine入口接收DSL和参数
2. **解析DSL**：WorkflowParser解析DSL，构建Workflow对象
3. **构建工作流**：解析Task信息，建立任务依赖关系
4. **调度任务执行**：Scheduler基于依赖关系调度任务执行
5. **执行单个任务**：Executor执行具体任务
6. **组装返回结果**：根据outputs定义组装返回结果

### 2. 任务依赖解析

TaskParser负责解析任务之间的依赖关系。关键步骤如下：

1. 解析任务属性中的表达式，识别对其他任务的引用
2. 基于引用关系构建依赖图
3. 计算任务的层级，用于调度执行

### 3. 并发执行

ConcurrentPipelineScheduler实现了基于CompletableFuture的并发执行模型。核心逻辑如下：

1. 对每个任务创建对应的CompletableFuture
2. 基于任务依赖关系组装CompletableFuture的依赖链
3. 使用CompletableFuture.allOf等待所有任务完成
4. 处理任务执行异常和超时

## 核心组件

### Workflow (工作流)
工作流是整个服务编排的顶层概念，代表了一个完整的业务流程，包含多个任务及其依赖关系和输出定义。

```java
@Data
public class Workflow {
    private long id;                    // 流程ID
    private String name;                // 流程名称
    private String description;         // 流程描述
    private Map<String, Task> taskMap;  // 所有任务的映射表
    private JsonNode outputs;           // 输出定义
    private boolean failFast = true;    // 是否快速失败
    private int timeout;                // 超时时间
    private RetryOption retry;          // 重试选项
}
```

### Task (任务)
任务是工作流中的基本执行单元，定义了一个具体的服务调用。

```java
@Data
public class Task {
    private String alias;               // 任务别名
    private String taskType;            // 任务类型
    private String description;         // 任务描述
    
    // 调用参数
    private String url;                 // 被调服务URL
    private String method;              // 请求方法
    private String remoteAppkey;        // 远程服务Appkey
    private String remoteServerPort;    // 远程服务端口
    private String inputs;              // 请求参数
    private String inputsExtra;         // 请求参数额外信息
    
    // 服务治理相关
    private String secret;              // 鉴权信息
    private String swimlane;            // 泳道
    private String cell;                // Cell信息
    private String liteSet;             // LiteSet
    private String appkey;              // 调用端Appkey
    private String ipPorts;             // 调用端IP端口
    
    // 异常处理
    private boolean ignoreException;    // 是否忽略异常
    
    // 稳定性相关
    private int timeout;                // 超时时间
    private CacheOption cacheOption;    // 缓存选项
    
    // 执行控制
    private String switchExpression;    // 开关表达式
    
    // 依赖关系
    private Map<String, Task> dependencyTaskMap; // 依赖任务
    private int level;                  // 层级
}
```

### WorkflowContext (工作流上下文)
工作流上下文包含工作流执行过程中的所有状态信息。

```java
@Data
public class WorkflowContext {
    private final String paramJson;     // 入参JSON
    private final Workflow workflow;    // 工作流
    private Option option;              // 配置选项
    
    // 执行状态
    private Map<String, Object> taskResultMap;       // 任务结果映射表
    private Map<String, Throwable> taskExceptionMap; // 任务异常映射表
    private Map<String, Object> envMap;              // 环境变量映射表
    private Map<String, Transaction> transactionMap; // 监控Transaction
}
```

## DSL语法规范

斑马服务编排框架的DSL采用JSON格式，以下是其主要结构和语法规范：

### 1. 工作流定义

```json
{
  "name": "工作流名称",
  "description": "工作流描述",
  "timeout": 1000,           // 整个工作流超时时间（毫秒）
  "retry": {                 // 重试配置（可选）
    "maxAttempts": 3,        // 最大重试次数
    "delay": 100             // 重试间隔（毫秒）
  },
  "tasks": [                 // 任务列表
    // ... 任务定义
  ],
  "outputs": {               // 输出定义
    // ... 输出配置
  }
}
```

| 属性名      | 类型      | 描述                  | 是否必须 |
|------------|----------|----------------------|---------|
| name       | String   | 工作流名称              | 是      |
| description| String   | 工作流描述              | 否      |
| timeout    | Integer  | 整体超时时间（毫秒）      | 否      |
| failFast   | Boolean  | 是否快速失败            | 否      |
| tasks      | Array    | 任务列表               | 是      |
| outputs    | Object   | 输出定义               | 否      |
| retry      | Object   | 重试配置               | 否      |

### 2. 任务定义

```json
{
  "alias": "任务别名",        // 任务标识符，在DSL中唯一
  "taskType": "任务类型",     // 如ThriftGeneric、Calculate等
  "description": "任务描述",  // 任务说明
  "url": "服务URL",          // 对于RPC调用，指定服务路径
  "method": "方法名",        // 对于RPC调用，指定调用方法
  "remoteAppkey": "远程服务Appkey", // 远程服务的appkey
  "remoteServerPort": "远程端口",   // 远程服务端口
  "timeout": 500,            // 任务超时时间（毫秒）
  "ignoreException": false,  // 是否忽略异常
  "switch": "条件表达式",     // 执行开关条件
  "inputs": {                // 输入参数
    "param1": "$params.param1", // 从入参获取
    "param2": "$task1.field"    // 从其他任务结果获取
  },
  "inputsExtra": {           // 输入参数类型定义（可选）
    "param1": "java.lang.String"
  },
  "cacheOption": {           // 缓存配置（可选）
    "expireAfterWriteSeconds": 300,
    "refreshAfterWriteSeconds": 60,
    "maximumSize": 100
  }
}
```

| 属性名           | 类型      | 描述                  | 是否必须 |
|-----------------|----------|----------------------|---------|
| alias           | String   | 任务别名（唯一标识）     | 是      |
| taskType        | String   | 任务类型               | 是      |
| url             | String   | 调用URL               | 否      |
| method          | String   | 调用方法               | 否      |
| remoteAppkey    | String   | 远程服务Appkey         | 否      |
| inputs          | Object   | 输入参数               | 否      |
| timeout         | Integer  | 任务超时时间（毫秒）     | 否      |
| ignoreException | Boolean  | 是否忽略异常           | 否      |
| switchExpression| String   | 条件表达式             | 否      |
| cacheOption     | Object   | 缓存配置               | 否      |

### 3. 输出定义

```json
"outputs": {
  "type": "map",            // 输出类型，支持map、list、object
  "switch": "条件表达式",    // 输出条件（可选）
  "variables": {            // 中间变量定义（可选）
    "var1": "表达式1",
    "var2": "表达式2"
  },
  "transform": "Transform.toMap('result', $task1)" // 转换表达式
}
```

| 属性名           | 类型      | 描述                  | 是否必须 |
|-----------------|----------|----------------------|---------|
| type            | String   | 输出类型，支持map、list、object     | 是      |
| switch          | String   | 输出条件               | 是      |
| variables       | String   | 中间变量定义               | 否      |
| transform       | String   | 转换表达式               | 否      |

## 任务类型

框架支持多种任务类型，每种类型有不同的执行逻辑：

### 1. ThriftGeneric
用于调用Thrift服务，是最常用的任务类型之一。

```json
{
  "alias": "thriftTask",
  "taskType": "ThriftGeneric",
  "description": "查询thrift接口任务示例",
  "url": "com.example.ThriftService",
  "method": "methodName",
  "remoteAppkey": "com.example.service",
  "remoteServerPort": "8080",
  "inputs": {
    "param1": "$params.value"
  }
}
```

### 2. Squirrel
用于查询Squirrel数据，配置连接URL（集群）及参数，当前支持get、mget、hget、hmget、mhmget。

```json
{
  "alias": "squirrelTask",
  "taskType": "Squirrel",
  "description": "查询Squirrel任务示例",
  "url": "redis-banma-poi-aggre-group_qa",
  "method": "mget",
  "timeout": 100,
  "inputs": {
    "category": "'poicenter'",
    "pattern": "'bm.poi.center.poi.base.bo.{0}'",
    "paramsList": "$params.poiIds"
  }
}
```

### 3. Calculate
用于执行计算任务，通过表达式进行数据转换或计算。

```json
{
  "alias": "calculateTask",
  "taskType": "Calculate",
  "description": "将传入的poiId分为外卖和海葵两个list",
  "inputs": {
    "wmPoiIdList": "filter($params.poiIds, lambda(poiId) -> poiId < 10000000000 end)",
    "hkPoiIdList": "filter($params.poiIds, lambda(poiId) -> poiId >= 10000000000 end)"
  }
}
```

Calculate任务类型的目的和使用建议：

- 数据格式转换与计算：数据用于执行轻量级的数据转换和计算逻辑，支持在任务开始前或结束后对数据进行处理
- 业务代码与处理代码隔离：将数据处理逻辑单独抽出来，职责分离，避免正常调用节点任务配置过于繁杂，增加可维护性和健壮性
- 复用性：Calculate任务计算/转换完，也会将该任务节点结果放入到工作流上下文中，workflowContext.taskResultMap,后续节点及结果处理时可以直接使用，避免重复转换

上面的示例展示了使用Calculate任务将poiId列表按照规则拆分为外卖和海葵两个子列表,后续使用时，可以直接使用，$calculateTask.wmPoiIdList来得到外卖商家列表，而不需要重新过滤计算。

### 4. 其他任务类型
根据项目需求，可能还支持HTTP等其他任务类型。

## 表达式引擎

表达式引擎是框架的核心组件之一，用于处理DSL中的动态表达式。主要支持以下功能：

### 1. 变量引用
- `$params` - 引用输入参数(调用整个工作流传入的参数json)
- `$taskName` - 引用任务结果
- `$taskNameIsException` - 检查任务是否发生异常

### 2. 函数支持
表达式支持多种内置函数，如：
- `seq.list()`, `seq.map()` - 创建列表和映射
- `seq.add()` - 添加元素到集合
- `filter()` - 过滤集合
- `Collections.toMap()` - 转换为Map
- `Object.getPathValue()` - 获取对象路径值
- `Transform.toMap()` - 创建结果映射

### 3. 流程控制
- `if...else` 条件语句
- `for` 循环
- `let` 变量声明

### 4. 表达式示例

```
// 条件表达式
$task1IsException == false && $task1 != nil

// 集合处理
filter($params.poiIds, lambda(poiId) -> poiId < 10000000000 end)

// 复杂转换
for poiId in Collections.toStringList($params.poiIds) {
  Transform.toMap(poiId,
    'field1', Object.getPathValue(resultMap, poiId + '.path'),
    'field2', Object.getPathValue(resultMap, poiId + '.otherPath')
  )
}
```

## 调度器说明

框架包含多种调度器，用于不同场景下的任务执行：

### 1. SingleNodeScheduler
单节点调度器，用于执行单个任务的工作流。采用简单直接的调用方式，无需考虑依赖关系。

### 2. ConcurrentPipelineScheduler
并发流水线调度器，是框架的主要调度器，适用于复杂的多任务工作流。其工作流程：

1. **依赖分析**：分析任务间的依赖关系，构建任务依赖图
2. **层级排序**：将任务按依赖关系划分为不同层级
3. **并发执行**：同一层级的任务并发执行
4. **逐层推进**：一层执行完成后，继续执行下一层
5. **结果收集**：收集各任务执行结果，填充工作流上下文

### 3. 调度优化策略
- **快速失败**：某层任务失败时立即终止后续执行
- **条件执行**：根据任务的`switch`表达式决定是否执行
- **动态线程池**：使用Rhino线程池进行任务并发执行

## 错误处理与重试机制

框架提供多级错误处理和重试机制：

### 1. 任务级别
- **ignoreException**：配置任务是否忽略异常
- **任务结果检查**：通过表达式判断任务结果是否正常

### 2. 工作流级别
- **failFast**：是否在首个任务失败后立即终止
- **retry**：工作流级别的重试策略
  - maxAttempts：最大重试次数
  - delay：重试间隔（毫秒）

### 3. 异常分类
框架定义了多种异常类型，用于不同场景的错误处理：
- **DSLParseException**：DSL解析异常
- **ParamJsonParseException**：参数解析异常
- **ExpressEvaluateException**：表达式评估异常
- **TaskExecuteException**：任务执行异常
- **WorkflowExecuteException**：工作流执行异常

## 使用案例详解

以下是一些常见使用场景的详细案例：

### 案例1：单任务调用 - Thrift服务查询

```json
{
  "name": "WmPoiAggreQuery",
  "description": "批量查询外卖主数据（聚合根）数据",
  "timeout": 100,
  "retry": {
    "maxAttempts": 1,
    "delay": 10
  },
  "tasks": [
    {
      "alias": "aggreData",
      "taskType": "ThriftGeneric",
      "description": "批量查询外卖主数据（聚合根）",
      "url": "com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService",
      "method": "mgetWmPoiAggreByWmPoiIdWithSpecificFieldV2",
      "remoteAppkey": "com.sankuai.waimai.poiquery",
      "remoteServerPort": "8499",
      "timeout": 100,
      "inputs": {
        "wmPoiIds": "$params.poiIds",
        "fieldsSet": "
          let map = seq.map('wm_logistics_type', 'wm_logistics_types', 'wm_label_ids', 'label_ids');
          let values = seq.list();
          for key in $params.fieldsSet { seq.add(values, map[key]); }
          return values;
        "
      },
      "inputsExtra": {
        "wmPoiIds": "java.util.List",
        "fieldsSet": "java.util.Set"
      }
    }
  ],
  "outputs": {
    "type": "map",
    "switch": "$aggreDataIsException == false && $aggreData != nil",
    "variables": {
      "poiAggreMap": "Collections.toMap(JSON.toObject($aggreData), 'wm_poi_id')"
    },
    "transform": "for poiId in Collections.toStringList($params.poiIds) {
      Transform.toMap(poiId,
        'wmLabelIds', Object.getPathValue(poiAggreMap, poiId + '.label_ids'),
        'wmLogisticsTypes', Object.getPathValue(poiAggreMap, poiId + '.wm_logistics_types')
      )
    }"
  }
}
```

**说明**：
1. 定义了一个单任务工作流，用于查询外卖门店数据
2. 输入参数中包含了复杂的表达式处理，将请求参数转换为服务所需格式
3. 输出部分使用了transform表达式，将服务返回的结果转换为所需的格式

### 案例2：多任务链式调用

```json
{
  "name": "CheckFetchMealCodeQuery",
  "description": "查询取餐码（OneService）",
  "tasks": [
    {
      "alias": "dateId",
      "taskType": "ThriftGeneric",
      "description": "查询外卖主数据（聚合根）数据",
      "url": "com.sankuai.meituan.banma.oneservice.thrift.iface.BmOneServiceThriftIface",
      "method": "fetchMetric",
      "remoteAppkey": "com.sankuai.banma.data.oneservice",
      "remoteServerPort": "9451",
      "timeout": 1000,
      "ignoreException": true,
      "cacheOption": {
        "expireAfterWriteSeconds": 1800,
        "refreshAfterWriteSeconds": 120,
        "maximumSize": 1
      },
      "inputs": {
        "fetchMetricInput": {
          "allDeliverMode": true,
          "metricOffsetList": [{
            "systemMetric": "'poi_date_qcm_white_list_tag_history'",
            "offsetType": 1
          }]
        }
      },
      "inputsExtra": {
        "fetchMetricInput": "com.sankuai.meituan.banma.oneservice.thrift.vo.FetchMetricInput"
      }
    },
    {
      "alias": "metricData",
      "taskType": "ThriftGeneric",
      "description": "查询OneService指定日期指标数据",
      "switch": "$dateId != nil && $dateId.code == 0 && $dateId.offsetResult != nil && count($dateId.offsetResult) > 0",
      "url": "com.sankuai.meituan.banma.oneservice.thrift.iface.BmOneServiceThriftIface",
      "method": "fetchMetric",
      "remoteAppkey": "com.sankuai.banma.data.oneservice",
      "remoteServerPort": "9451",
      "timeout": 1000,
      "inputs": {
        "fetchMetricInput": {
          "dimensionTypeList": [
            {"value": "'poi'"},
            {"value": "'date'"}
          ],
          "dimensionValueList": "let list = seq.list(); for poiId in $params.poiIds { seq.add(list, seq.list(seq.map('value', poiId), seq.map('value', $dateId.offsetResult[0]))) }; return list;",
          "metricNameList": [
            {"value": "'qcm_white_list_tag_history'"}
          ],
          "selectFields": [
            "'is_white'"
          ],
          "allDeliverMode": true
        }
      },
      "inputsExtra": {
        "fetchMetricInput": "com.sankuai.meituan.banma.oneservice.thrift.vo.FetchMetricInput"
      }
    }
  ],
  "outputs": {
    "type": "map",
    "switch": "$metricDataIsException == false && $metricData != nil && $metricData.code == 0 && $metricData.output != nil && count($metricData.output) == count($params.poiIds)",
    "variables": {
      "metricDataOutputList": "JSON.toObject($metricData.output)"
    },
    "transform": "for index,poiId in Collections.toStringList($params.poiIds) {
      Transform.toMap(poiId,
        'checkFetchMealCode', Object.getPathValue(metricDataOutputList, index + '.0.defaultValue')
      )
    }"
  }
}
```

**说明**：
1. 定义了一个双任务工作流，两个任务有依赖关系
2. 第一个任务查询日期ID，第二个任务使用日期ID查询指标数据
3. 第二个任务使用`switch`表达式，只有在第一个任务成功时才执行
4. 使用缓存机制优化性能：第一个任务的结果会缓存30分钟
5. 输出部分使用复杂转换，将服务返回的多层嵌套结果展平为简单Map

### 案例3：复杂数据处理工作流

```json
{
  "name": "SecondDeliveryCategoryQuery",
  "description": "查询外卖/海葵二级配送品类",
  "timeout": 16000,
  "retry": {
    "maxAttempts": 1,
    "delay": 10
  },
  "tasks": [
    {
      "alias": "prepare",
      "taskType": "Calculate",
      "description": "将传入的poiId分为外卖和海葵两个list",
      "inputs": {
        "wmPoiIdList": "filter($params.poiIds, lambda(poiId) -> poiId < 10000000000 end)",
        "hkPoiIdList": "filter($params.poiIds, lambda(poiId) -> poiId >= 10000000000 end)"
      }
    },
    {
      "alias": "parseHkCategory",
      "taskType": "Calculate",
      "description": "解析获取海葵的二级品类",
      "ignoreException": true,
      "switch": "count($prepare.hkPoiIdList) > 0 && $params.aggreMap != nil && $params.aggreMap != ''",
      "inputs":"
        let hkId2CategoryMap = seq.map();
        for entry in JSON.toObject($params.aggreMap) {
          let poiId = entry.key;
          let categoryId = Object.getPathValue(Object.getPathValue(entry.value, 'bmPoiBaseBo'), 'hkSecondCategory');
          hkId2CategoryMap[poiId] = categoryId
        }
        return hkId2CategoryMap;
      "
    },
    // 更多任务...
  ],
  "outputs": {
    "type": "map",
    "variables": {
      "wmPoiIds": "if $prepare.wmPoiIdList == nil || $prepare.wmPoiIdList == '' {return seq.list();} return JSON.toObject($prepare.wmPoiIdList);",
      "wmId2CategoryMap": "if $parseWmCategory == nil || $parseWmCategory == '' {return seq.map();} return JSON.toObject($parseWmCategory);",
      "wmCategoryId2DeliveryMap": "if $parseWmDelivery == nil || $parseWmDelivery == '' {return seq.map();} return JSON.toObject($parseWmDelivery);",
      "hkPoiIds": "if $prepare.hkPoiIdList == nil || $prepare.hkPoiIdList == '' {return seq.list();} return JSON.toObject($prepare.hkPoiIdList);",
      "hkId2CategoryMap": "if $parseHkCategory == nil || $parseHkCategory == '' {return seq.map();} return JSON.toObject($parseHkCategory);",
      "hkCategoryId2DeliveryMap": "if $parseHkDelivery == nil || $parseHkDelivery == '' {return seq.map();} return JSON.toObject($parseHkDelivery);"
    },
    "transform": "
      let res = seq.map();
      for poiId in hkPoiIds {
        let item = seq.map();
        item['secondDeliveryCategoryId'] = Object.getPathValue(Object.getPathValue(hkCategoryId2DeliveryMap, str(Object.getPathValue(hkId2CategoryMap, str(poiId)))),'categoryId');
        item['secondDeliveryCategoryName'] = Object.getPathValue(Object.getPathValue(hkCategoryId2DeliveryMap, str(Object.getPathValue(hkId2CategoryMap, str(poiId)))),'categoryName');
        res[str(poiId)] = item;
      }
      for poiId in Collections.toStringList(wmPoiIds) {
          let item = seq.map();
          item['secondDeliveryCategoryId'] = Object.getPathValue(Object.getPathValue(wmCategoryId2DeliveryMap, str(Object.getPathValue(wmId2CategoryMap, poiId))),'categoryId');
          item['secondDeliveryCategoryName'] = Object.getPathValue(Object.getPathValue(wmCategoryId2DeliveryMap, str(Object.getPathValue(wmId2CategoryMap, poiId))),'categoryName');
          res[str(poiId)] = item;
      }
      return res;
    "
  }
}
```
**说明**：
1. 这是一个复杂的工作流，处理不同来源的数据（外卖和海葵）
2. 首个任务将输入ID按规则分成两组
3. 后续任务根据ID分组分别处理不同来源的数据
4. 使用条件执行（switch）避免不必要的任务执行
5. 输出部分有复杂的变量处理和转换逻辑，确保空值安全处理
6. 最终合并两部分结果，返回统一格式 
