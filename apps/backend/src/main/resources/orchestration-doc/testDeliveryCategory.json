{
  "name": "SecondDeliveryCategoryQuery",
  "description": "查询外卖/海葵二级配送品类",
  "timeout": 16000,
  "retry": {
    "maxAttempts": 1,
    "delay": 10
  },
  "tasks": [
    {
      "alias": "prepare",
      "taskType": "Calculate",
      "description": "将传入的poiId分为外卖和海葵两个list",
      "inputs": {
        "wmPoiIdList": "filter($params.poiIds, lambda(poiId) -> poiId < 10000000000 end)",
        "hkPoiIdList": "filter($params.poiIds, lambda(poiId) -> poiId >= 10000000000 end)"
      }
    },
    {
      "alias": "parseHkCategory",
      "taskType": "Calculate",
      "description": "解析获取海葵的二级品类",
      "ignoreException": true,
      "switch": "count($prepare.hkPoiIdList) > 0 && $params.aggreMap != nil && $params.aggreMap != ''",
      "inputs":"
        let hkId2CategoryMap = seq.map();
        for entry in JSON.toObject($params.aggreMap) {
          let poiId = entry.key;
          let categoryId = Object.getPathValue(Object.getPathValue(entry.value, 'bmPoiBaseBo'), 'hkSecondCategory');
          hkId2CategoryMap[poiId] = categoryId
        }
        return hkId2CategoryMap;
      "
    },
    {
      "alias": "hkCategory2Delivery",
      "taskType": "ThriftGeneric",
      "description": "将海葵的二级品类转换为二级配送品类",
      "url": "com.sankuai.meituan.banma.category.iface.BmCategoryMappingThriftIface",
      "method": "batchCommonGetCategoryMapping",
      "remoteAppkey": "com.sankuai.deliverycategory.server",
      "ignoreException": true,
      "switch": "$parseHkCategory != nil && $parseHkCategory != ''",
      "timeout": 12000,
      "inputs": {
        "request":"
          let requestMap = seq.map();
          let paramList = seq.list();
          requestMap['categorySource'] = 'HAIKUI_CATEGORY';
          requestMap['outputCategory'] = 'DELIVERY_CATEGORY';
          for entry in JSON.toObject($parseHkCategory) {
            let item = seq.map();
            item['categoryId'] = str(entry.value);
            item['level'] = 2;
            seq.add(paramList, item);
          }
          requestMap['categoryIdAndLevelParams'] = paramList;
          return requestMap;
        "
    }
  },
  {
    "alias": "parseHkDelivery",
    "taskType": "Calculate",
    "description": "解析海葵的二级配送品类结果",
    "ignoreException": true,
    "switch": "$hkCategory2Delivery != nil && $hkCategory2Delivery != ''",
    "inputs":"
      let hkCategoryId2DeliveryMap = seq.map();
      for item in JSON.toObject($hkCategory2Delivery){
        let deliveryInfo = seq.map();
        deliveryInfo['categoryId'] = item.commonCategoryView.categoryId;
        deliveryInfo['categoryName'] = item.commonCategoryView.categoryName;
        hkCategoryId2DeliveryMap[item.sourceCategoryId] = deliveryInfo
      }
      return hkCategoryId2DeliveryMap;
    "
  },
    {
      "alias": "getWmCategoryId",
      "taskType": "ThriftGeneric",
      "description": "获取外卖二级品类id",
      "url": "com.sankuai.meituan.waimai.thrift.service.WmPoiQueryExtendThriftService",
      "method": "mgetWmPoiTagAndTreeMapByWmPoiIdsV2",
      "remoteAppkey": "com.sankuai.waimai.poiquery",
      "remoteServerPort": "8501",
      "ignoreException": true,
      "switch": "count($prepare.wmPoiIdList) > 0",
      "timeout": 12000,
      "inputs": {
        "wmPoiIds": "$prepare.wmPoiIdList"
      },
      "inputsExtra": {
        "wmPoiIds": "java.util.List"
      }
    },
    {
      "alias": "parseWmCategory",
      "taskType": "Calculate",
      "description": "解析外卖的二级品类",
      "ignoreException": true,
      "switch": "$getWmCategoryId != nil && $getWmCategoryId != ''",
      "inputs":"
          let wmId2CategoryMap = seq.map();
          for entry in JSON.toObject($getWmCategoryId) {
            let poiId = entry.key;
            for tree in entry.value {
              if tree.is_primary == 1 {
                for dic in tree.tagTree {
                  if dic.level == 2 {
                    wmId2CategoryMap[poiId] = dic.id
                  }
                }
              }
            }
          }
          return wmId2CategoryMap;
      "
    },
    {
      "alias": "wmCategory2Delivery",
      "taskType": "ThriftGeneric",
      "description": "将外卖的二级品类转换为二级配送品类",
      "url": "com.sankuai.meituan.banma.category.iface.BmCategoryMappingThriftIface",
      "method": "batchGetDeliveryCategoryByWaimai",
      "remoteAppkey": "com.sankuai.deliverycategory.server",
      "ignoreException": true,
      "switch": "$parseWmCategory != nil && $parseWmCategory != ''",
      "timeout": 12000,
      "inputs": {
        "wmCategoryIds":"
          let categoryIdList = seq.list();
          let idSet = seq.set();
          for entry in JSON.toObject($parseWmCategory) {
            seq.add(idSet, entry.value);
          }
          for id in idSet{
            let item = seq.map('categoryId', id, 'level', 2);
            seq.add(categoryIdList, item)
          }
          return categoryIdList;
        "
      }
    },
    {
      "alias": "parseWmDelivery",
      "taskType": "Calculate",
      "description": "解析外卖的二级配送品类",
      "ignoreException": true,
      "switch": "$wmCategory2Delivery != nil && $wmCategory2Delivery != ''",
      "inputs":"
        let wmCategoryId2DeliveryMap = seq.map();
        for item in JSON.toObject($wmCategory2Delivery){
            let deliveryInfo = seq.map();
            deliveryInfo['categoryId'] = item.deliveryCategoryView.categoryId;
            deliveryInfo['categoryName'] = item.deliveryCategoryView.categoryName;
            wmCategoryId2DeliveryMap[item.categoryId] = deliveryInfo
         }
         return wmCategoryId2DeliveryMap;
      "
    }
  ],
  "outputs": {
    "type": "map",
    "variables": {
      "wmPoiIds": "if $prepare.wmPoiIdList == nil || $prepare.wmPoiIdList == '' {return seq.list();} return JSON.toObject($prepare.wmPoiIdList);",
      "wmId2CategoryMap": "if $parseWmCategory == nil || $parseWmCategory == '' {return seq.map();} return JSON.toObject($parseWmCategory);",
      "wmCategoryId2DeliveryMap": "if $parseWmDelivery == nil || $parseWmDelivery == '' {return seq.map();} return JSON.toObject($parseWmDelivery);",
      "hkPoiIds": "if $prepare.hkPoiIdList == nil || $prepare.hkPoiIdList == '' {return seq.list();} return JSON.toObject($prepare.hkPoiIdList);",
      "hkId2CategoryMap": "if $parseHkCategory == nil || $parseHkCategory == '' {return seq.map();} return JSON.toObject($parseHkCategory);",
      "hkCategoryId2DeliveryMap": "if $parseHkDelivery == nil || $parseHkDelivery == '' {return seq.map();} return JSON.toObject($parseHkDelivery);"
    },
    "transform": "
      let res = seq.map();
      for poiId in hkPoiIds {
        let item = seq.map();
        item['secondDeliveryCategoryId'] = Object.getPathValue(Object.getPathValue(hkCategoryId2DeliveryMap, str(Object.getPathValue(hkId2CategoryMap, str(poiId)))),'categoryId');
        item['secondDeliveryCategoryName'] = Object.getPathValue(Object.getPathValue(hkCategoryId2DeliveryMap, str(Object.getPathValue(hkId2CategoryMap, str(poiId)))),'categoryName');
        res[str(poiId)] = item;
      }
      for poiId in Collections.toStringList(wmPoiIds) {
          let item = seq.map();
          item['secondDeliveryCategoryId'] = Object.getPathValue(Object.getPathValue(wmCategoryId2DeliveryMap, str(Object.getPathValue(wmId2CategoryMap, poiId))),'categoryId');
          item['secondDeliveryCategoryName'] = Object.getPathValue(Object.getPathValue(wmCategoryId2DeliveryMap, str(Object.getPathValue(wmId2CategoryMap, poiId))),'categoryName');
          res[str(poiId)] = item;
      }
      return res;
    "
  }
}