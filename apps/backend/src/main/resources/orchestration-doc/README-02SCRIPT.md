# 斑马服务编排框架脚本引擎设计与实践

## 目录
- [1. 背景介绍](#1-背景介绍)
- [2. 脚本引擎架构](#2-脚本引擎架构)
- [3. 表达式语法](#3-表达式语法)
- [4. 变量机制](#4-变量机制)
- [5. 最佳实践](#5-最佳实践)
- [6. 实践案例](#6-实践案例)
- [7. 重要注意事项](#7-重要注意事项)

## 1. 背景介绍

### 1.1 设计目标
- 提供灵活的数据处理能力
- 支持复杂的条件判断和流程控制
- 实现服务间的数据转换和映射
- 支持结果数据的聚合和转换

### 1.2 核心特性
- 混合表达式支持（Aviator + JsonPath）
- 丰富的内置函数库
- 完整的变量生命周期管理
- 灵活的配置化能力

## 2. 脚本引擎架构

### 2.1 整体架构

```
+------------------+
|    DSL解析层     |  ← 解析DSL中的表达式字段
+------------------+
|    表达式引擎    |  ← Aviator引擎 + JsonPath引擎
+------------------+
|    函数扩展层    |  ← 自定义函数库
+------------------+
```

### 2.2 表达式支持范围

1. **工作流级别**
```json
{
  "switch": "表达式",              // 工作流执行条件
  "outputs": {
    "switch": "表达式",           // 输出条件
    "variables": {               // 输出变量定义
      "var1": "表达式",
      "var2": "表达式"
    },
    "transform": "表达式"         // 输出转换逻辑
  }
}
```

2. **任务级别**
```json
{
  "tasks": [{
    "switch": "表达式",           // 任务执行条件
    "inputs": {                 // 任务入参
      "param1": "表达式",
      "param2": "表达式"
    }
  }]
}
```

### 2.3 表达式识别机制

1. **表达式类型**
```java
// 1. JsonPath表达式
$.data.items[0].name          // 标准JsonPath
$taskResult.data.value        // 别名JsonPath

// 2. Aviator表达式
count($list) > 0              // 条件判断
string.contains($name, 'test') // 函数调用

// 3. 混合表达式
Object.getPathValue($result, 'data.items')
if ($task.code == 0) { return $task.data.value; }
```

2. **识别流程**
```java
表达式/脚本属性，取出脚本string
   ↓
1. 完全匹配检查 → 是JsonPath → 执行JsonPath
   ↓
2. 混合表达式检查 → 包含JsonPath
   ↓
3. 提取JsonPath并计算
   ↓
4. 替换回原表达式（将JsonPath字面字符串替换成计算结果值）
   ↓
5. 执行Aviator表达式
   ↓
返回结果
```

## 3. 表达式语法

### 3.1 Aviator原生语法
支持所有Aviator5.0+语法，完整语法请参考[Aviator表达式语法文档](https://km.sankuai.com/collabpage/1902394065)

1. **基础运算**
```java
// 算术运算
1 + 2, 3 * 4, 10 / 2

// 比较运算
==, !=, >, <, >=, <=

// 逻辑运算
&&, ||, !
```

2. **流程控制**
```java
// 条件判断
if (condition) {
    ...
} else {
    ...
}

// 循环遍历
for item in collection {
    ...
}
```

3. **集合操作**
```java
// 1. 集合创建
let list = seq.list()           // 创建List
let map = seq.map()            // 创建Map
let set = seq.set()            // 创建Set

// 2. 元素操作
seq.add(list, item)            // 添加元素
seq.remove(list, item)         // 移除元素
seq.get(list, index)           // 获取元素
seq.contains(list, item)       // 包含判断
seq.count(collection)          // 获取大小

// 3. Map操作
map['key'] = value             // 设置值
map.key = value                // 点号设置值
seq.remove(map, 'key')         // 移除键值对
map.containsKey('key')         // 键存在判断

// 4. 集合遍历
for item in list {             // List遍历
    println(item);
}

for entry in map {             // Map遍历
    println(entry.key + ': ' + entry.value);
}

// 5. 集合转换
seq.toList(array)              // 数组转List
seq.toSet(list)               // List转Set
seq.toMap()                   // 创建新Map

// 6. 集合过滤和映射
filter(list, predicate)        // 过滤元素
map(list, transformer)         // 转换元素
reduce(list, accumulator)      // 归约操作

// 7. 集合工具函数
seq.subList(list, from, to)   // 截取子列表
seq.sort(list)                // 排序
seq.reverse(list)             // 反转
seq.shuffle(list)             // 随机打乱
seq.distinct(list)            // 去重

// 8. 高级操作
// 分组
let groups = seq.map();
for item in list {
    let key = item.type;
    if (!seq.contains(groups, key)) {
        groups[key] = seq.list();
    }
    seq.add(groups[key], item);
}

// 排序
seq.sort(list, lambda(a, b) -> {
    return a.value - b.value;
});

// 分区
let (matched, unmatched) = seq.partition(list, lambda(x) -> {
    return x > 0;
});

// 聚合
let sum = reduce(list, lambda(acc, cur) -> {
    return acc + cur;
}, 0);

// 9. 实用示例
// 列表转Map
let userMap = seq.map();
for user in userList {
    userMap[user.id] = user;
}

// 分组统计
let typeStats = seq.map();
for item in items {
    let type = item.type;
    typeStats[type] = (typeStats[type] || 0) + 1;
}

// 嵌套集合处理
let result = seq.map();
for dept in departments {
    let employees = seq.list();
    for user in users {
        if (user.deptId == dept.id) {
            seq.add(employees, Transform.toMap(
                'name', user.name,
                'role', user.role
            ));
        }
    }
    result[dept.id] = employees;
}
```

4. **Lambda表达式**
```java
// 过滤
filter(list, lambda(x) -> x > 0 end)

// 映射
map(list, lambda(x) -> x * 2 end)
```

### 3.2 JsonPath语法

1. **路径访问**
```java
$.store.book[0].title     // 数组索引访问
$.store.book[*].title     // 数组全部元素
$.store.book[?(@.price < 10)]  // 条件过滤
```

2. **运算符**
```java
$.store.book[(@.length-1)]  // 表达式
$..book[0,1]               // 多个索引
$..book[:2]               // 范围切片
```

### 3.3 混合使用

1. **推荐写法**
```java
// 使用工具函数
Object.getPathValue($obj, 'path.to.field')

// 简单路径直接访问
$task.data.value

// 条件判断
if ($response != nil && Object.getPathValue($response, 'code') == 0) {
    return Object.getPathValue($response, 'data.value');
}
```

2. **不推荐写法**
```java
// 避免复杂路径直接访问
$.complex.path[0].field

// 避免中括号访问
$obj['field']['subfield']
```

## 4. 变量机制

### 4.1 变量生命周期

1. **输入参数**
```json
{
  "params": {
    "poiIds": ["1001", "1002"],
    "fieldsSet": ["wm_logistics_type", "wm_label_ids"]
  }
}
```
- 通过`$params`访问输入参数
- 支持JsonPath方式访问嵌套属性：`$params.poiIds[0]`

2. **任务执行结果**
```json
{
  "tasks": [
    {
      "alias": "aggreData",  // 任务别名
      "taskType": "ThriftGeneric",
      "inputs": {
        "wmPoiIds": "$params.poiIds"  // 使用输入参数
      }
    }
  ],
  "outputs": {
    "variables": {
      "poiMap": "$aggreData"  // 直接使用任务别名引用结果
    }
  }
}
```
- 任务执行完成后，结果会存入工作流上下文
- 通过任务别名（如`$aggreData`）直接访问结果
- 通过`$taskNameIsException`判断任务是否异常

3. **中间变量**
```json
{
  "outputs": {
    "variables": {
      "processedData": "JSON.toObject($aggreData)",  // 定义中间变量
      "filteredIds": "filter($params.poiIds, lambda(id) -> id < 10000000000 end)"
    },
    "transform": "
      let tempMap = seq.map();  // 局部变量
      for id in $filteredIds {
        tempMap[id] = Object.getPathValue($processedData, id + '.data');
      }
      return tempMap;
    "
  }
}
```
- variables中定义的变量可在transform中使用
- let定义的局部变量仅在当前作用域有效

### 4.2 变量访问规则

1. **直接访问**
```java
// 任务结果直接访问
$taskName               // 访问整个结果
$taskName.field        // 访问顶层字段
$taskNameIsException   // 访问任务异常标记

// 输入参数访问
$params.key           // 访问参数
$params.obj.field    // 访问嵌套字段
```

2. **JsonPath访问**
```java
// 深层数据访问
Object.getPathValue($response, 'data.user.name')

// 数组操作
$data.items[*].id    // 获取所有项的id
```

3. **条件访问**
```java
// 空值检查
if ($task != nil && Object.getPathValue($task, 'data') != nil) {
    return Object.getPathValue($task, 'data.value');
}

// 空值+空字符串检查
if ($value == nil || $value == '') {
    // 处理空值或空字符串的情况
    return defaultValue;
}
```

### 4.3 实际案例解析

1. **商户数据查询案例**
```json
{
  "name": "WmPoiAggreQuery",
  "tasks": [
    {
      "alias": "aggreData",
      "taskType": "ThriftGeneric",
      "inputs": {
        "wmPoiIds": "$params.poiIds",
        "fieldsSet": "
          let map = seq.map('wm_logistics_type', 'wm_logistics_types');
          let values = seq.list();
          for key in $params.fieldsSet { 
            seq.add(values, map[key]); 
          }
          return values;
        "
      }
    }
  ],
  "outputs": {
    "type": "map",
    "switch": "$aggreDataIsException == false && $aggreData != nil",
    "variables": {
      "poiAggreMap": "Collections.toMap(JSON.toObject($aggreData), 'wm_poi_id')"
    },
    "transform": "
      for poiId in Collections.toStringList($params.poiIds) {
        Transform.toMap(poiId,
          'wmLogisticsTypes', Object.getPathValue(poiAggreMap, poiId + '.wm_logistics_types'),
          'wmLabelIds', Object.getPathValue(poiAggreMap, poiId + '.label_ids')
        )
      }
    "
  }
}
```

**变量使用说明：**
1. 输入参数：
   - `$params.poiIds`：商户ID列表
   - `$params.fieldsSet`：需要查询的字段集合

2. 任务执行：
   - `aggreData`任务执行完成后，结果存入`$aggreData`
   - 通过`$aggreDataIsException`判断任务是否异常

3. 结果处理：
   - 定义中间变量`poiAggreMap`转换数据结构
   - 在transform中使用中间变量进行结果转换

2. **配送品类查询案例**
```json
{
  "name": "DeliveryCategoryQuery",
  "tasks": [
    {
      "alias": "prepare",
      "taskType": "Calculate",
      "inputs": {
        "wmPoiIdList": "filter($params.poiIds, lambda(poiId) -> poiId < 10000000000 end)",
        "hkPoiIdList": "filter($params.poiIds, lambda(poiId) -> poiId >= 10000000000 end)"
      }
    },
    {
      "alias": "parseHkCategory",
      "taskType": "Calculate",
      "switch": "count($prepare.hkPoiIdList) > 0",
      "inputs": "
        let hkId2CategoryMap = seq.map();
        for entry in JSON.toObject($params.aggreMap) {
          let categoryId = Object.getPathValue(entry.value, 'bmPoiBaseBo.hkSecondCategory');
          hkId2CategoryMap[entry.key] = categoryId;
        }
        return hkId2CategoryMap;
      "
    }
  ],
  "outputs": {
    "variables": {
      "wmPoiIds": "if $prepare.wmPoiIdList == nil {return seq.list();} return JSON.toObject($prepare.wmPoiIdList);",
      "hkCategoryMap": "if $parseHkCategory == nil {return seq.map();} return JSON.toObject($parseHkCategory);"
    },
    "transform": "
      let result = seq.map();
      // 处理海葵商户
      for poiId in $wmPoiIds {
        result[poiId] = Transform.toMap(
          'categoryId', Object.getPathValue($hkCategoryMap, poiId)
        );
      }
      return result;
    "
  }
}
```

**变量使用说明：**
1. 任务间数据传递：
   - `prepare`任务的结果通过`$prepare.wmPoiIdList`访问
   - `parseHkCategory`任务使用`$prepare`的结果作为条件判断

2. 空值处理：
   - 使用`if $prepare.wmPoiIdList == nil`进行空值检查
   - 提供默认空集合作为返回值

3. 结果转换：
   - 使用中间变量简化数据处理逻辑
   - 通过Object.getPathValue安全访问嵌套数据

## 5. 最佳实践

### 数据处理

1. **空值处理**
```java
// 推荐
Optional.orElse($nullable, defaultValue)
if ($value != nil && $value != '') { ... }

// 不推荐
$value ?: defaultValue
```

2. **类型转换**
```java
// 推荐
str($number)                   // 明确的类型转换
Collections.toStringList($list) // 使用工具方法

// 不推荐
$number + ''                   // 隐式转换
```

3. **集合处理**
```java
// 推荐
let map = seq.map()           // 使用seq创建集合
seq.add(list, item)           // 使用seq操作集合

// 不推荐
{'key': 'value'}              // 直接使用字面量
list.add(item)                // 直接调用方法
```

### 性能优化

1. **避免重复解析**
```java
// 推荐
let data = JSON.toObject($json)
for item in data { ... }

// 不推荐
for item in JSON.toObject($json) { ... }
```

2. **使用批量处理**
```java
// 推荐
let results = seq.map()
for id in $idList {
    results[id] = process(id)
}
return results

// 不推荐
for id in $idList {
    process(id)
}
```

## 6. 实践案例

### 数据转换案例
```json
{
  "transform": "
    let result = seq.map();
    for item in JSON.toObject($data) {
      result[item.id] = Transform.toMap(
        'name', item.userName,
        'age', str(item.age),
        'tags', Collections.toStringList(item.tagList)
      );
    }
    return result;
  "
}
```

### 条件处理案例
```json
{
  "switch": "$response != nil && $response.code == 0",
  "transform": "
    if (count($response.data) > 0) {
      return Transform.toMap(
        'success', true,
        'data', $response.data
      );
    }
    return Transform.toMap('success', false);
  "
}
```

## 7. 重要注意事项

### 7.1 数据类型规范

1. **默认数据结构**
```java
// 默认情况下，所有对象都是集合类嵌套基本类型的结构
{
    "key1": [1, 2, 3],                     // List嵌套基本类型
    "key2": {                              // Map嵌套基本类型
        "field1": "value1",
        "field2": 123
    },
    "key3": [                              // List嵌套Map
        {"id": 1, "name": "item1"},
        {"id": 2, "name": "item2"}
    ]
}

// 不支持的复杂对象类型
{
    "key": new CustomObject(),             // ❌ 不支持自定义对象
    "date": new Date(),                    // ❌ 不支持日期对象
    "regex": /pattern/                     // ❌ 不支持正则对象
}
```

### 7.2 任务结果处理

1. **Thrift任务结果**
```java
// Thrift泛化调用结果是JSON字符串
let result = $thriftTask              // ❌ 直接使用结果
let data = JSON.toObject($thriftTask) // ✅ 先转换为对象

// 结果处理最佳实践
"variables": {
    "taskData": "JSON.toObject($thriftTask)",  // 1. 先在variables中转换
    "processedData": "                         // 2. 后续处理使用转换后的变量
        let result = seq.map();
        for item in taskData {
            result[item.id] = item.value;
        }
        return result;
    "
}
```

2. **任务异常处理**
```java
// 任务结果检查
"switch": "$taskIsException == false && $task != nil",  // ✅ 先检查异常和空
"transform": "
    if (Object.getPathValue($taskData, 'code') != 0) {  // ✅ 再检查业务状态
        return Transform.toMap('success', false);
    }
    // 处理正常结果
"
```

### 7.3 表达式使用注意

1. **JsonPath使用**
```java
// JsonPath输入必须是JSON字符串
Object.getPathValue($obj, 'path')     // ❌ 如果$obj是字符串
Object.getPathValue(
    JSON.toObject($obj), 'path'       // ✅ 先转换为对象
)

// 路径访问安全性
$response.data.user.name          // ❌ 可能抛出空指针
Object.getPathValue(
    $response, 'data.user.name'   // ✅ 安全的路径访问
)
```

2. **变量作用域**
```java
// 作用域限制
"transform": "
    let tempVar = 'value';            // 仅在transform块内有效
    for item in list {
        let itemVar = item.value;     // 仅在循环内有效
    }
"

// variables中的变量
"variables": {
    "var1": "expression1",            // ✅ 在后续variables和transform中可用
    "var2": "use($var1) in expression" // ✅ 可以使用之前定义的变量
}
```

### 7.4 性能优化提醒

1. **避免重复转换**
```java
// 场景1：多任务结果处理
// ❌ 每个任务都重复转换
"transform": "
    let dataMap = seq.map();
    seq.put(dataMap, 'name', Object.getPathValue(JSON.toObject($task).'name'));
    seq.put(dataMap, 'age', Object.getPathValue(JSON.toObject($task).'age'));
    return dataMap;
",
// ✅ 统一在variables中转换，后续复用
"variables": {
    "dataMap": "JSON.toObject($task)"
}
"transform": "
    let dataMap = JSON.toObject($task);
    seq.put(dataMap, 'name', Object.getPathValue(dataMap.'name'));
    seq.put(dataMap, 'age', Object.getPathValue(dataMap.'age'));
    return dataMap;
",

// 场景2：条件分支中的重复转换
"transform": "
    // ❌ 在不同分支中重复转换
    if (condition1) {
        let data = JSON.toObject($response);
        return process1(data);
    } else if (condition2) {
        let data = JSON.toObject($response);
        return process2(data);
    }
",
"variables": {
    // ✅ 统一转换一次
    "responseData": "JSON.toObject($response)",
    "result": "
        if (condition1) {
            return process1(responseData);
        } else if (condition2) {
            return process2(responseData);
        }
    "
}
```

### 7.5 其他重要提醒

1. **空值处理**
```java
// 所有外部输入都需要进行空值检查
if ($params != nil && $params.data != '') {  // ✅ 检查null和空字符串
    process($params.data)
}

// 集合空值检查
if (seq.count($list) > 0) {                  // ✅ 检查集合是否为空
    process($list)
}
```

2. **类型一致性**
```java
// 确保集合元素类型一致
let list = seq.list();
seq.add(list, "1");    // ❌ 混合类型
seq.add(list, 2);      // ❌ 混合类型

let numList = seq.list();
seq.add(numList, num("1"));  // ✅ 统一转换为数字
seq.add(numList, 2);
```

3. **调试技巧**
```java
// 使用临时变量调试
"variables": {
    "debug1": "JSON.toObject($task)",     // 检查转换结果
    "debug2": "Object.getPathValue($debug1, 'data')",  // 检查路径访问
    "result": "process($debug2)"          // 使用中间结果
}
``` 
