# 斑马服务编排框架 (Banma Service Orchestration)

## 项目概述
斑马服务编排框架是一个基于DSL（领域特定语言）的服务编排框架，通过声明式JSON配置，实现多服务间的复杂调用流程编排。该框架支持任务依赖关系管理、并发执行、异常处理、重试机制、结果转换等特性，使开发人员能够专注于业务逻辑而非复杂的服务调用流程控制。

## 核心功能
- **声明式工作流定义**：通过JSON格式DSL定义任务流程和依赖关系
- **多种任务类型支持**：支持Thrift、Calculate等多种任务类型
- **自动依赖分析**：根据DSL自动计算任务依赖关系并构建执行图
- **并发调度执行**：基于任务依赖关系的并发调度执行
- **结果转换**：强大的表达式引擎支持复杂的数据转换
- **服务治理**：超时控制、重试机制、异常处理等
- **可缓存性**：支持任务结果缓存，提高性能
- **可观测性**：集成CAT监控，提供完整调用链跟踪

## 快速开始

### 1. 添加依赖
```xml
<dependency>
    <groupId>com.sankuai.meituan.banma.business</groupId>
    <artifactId>banma_service_orchestration</artifactId>
    <version>1.0.3</version>
</dependency>
```

### 2. 编写DSL
创建一个JSON格式的DSL文件，定义工作流和任务：

```json
{
  "name": "ExampleWorkflow",
  "description": "示例工作流",
  "timeout": 1000,
  "tasks": [
    {
      "alias": "task1",
      "taskType": "ThriftGeneric",
      "description": "示例任务1",
      "url": "com.example.service.ExampleService",
      "method": "exampleMethod",
      "remoteAppkey": "com.example.service",
      "inputs": {
        "param1": "$params.param1"
      }
    }
  ],
  "outputs": {
    "type": "map",
    "transform": "Transform.toMap('result', $task1)"
  }
}
```

### 3. 调用服务
```java
String dsl = "..."; // DSL内容
String paramJson = "{\"param1\": \"value1\"}"; // 参数JSON

Response<?> response = Engine.invoke(dsl, paramJson, null);
if (response.isSuccess()) {
    Object result = response.getResult();
    // 处理结果
} else {
    // 处理错误
}
```


## 核心流程

### 1. 工作流执行流程

```
+-------------+     +------------+      +-------------+      +--------------+
|  接收请求    |---->| 解析DSL     |----->| 调度任务执行  |----->| 组装返回结果  |
+-------------+     +------------+      +-------------+      +--------------+
                         |                    |
                         v                    v
                    +------------+      +-------------+
                    | 构建工作流  |      | 执行单个任务  |
                    +------------+      +-------------+
```

1. **接收请求**：Engine入口接收DSL和参数
2. **解析DSL**：WorkflowParser解析DSL，构建Workflow对象
3. **构建工作流**：解析Task信息，建立任务依赖关系
4. **调度任务执行**：Scheduler基于依赖关系调度任务执行
5. **执行单个任务**：Executor执行具体任务
6. **组装返回结果**：根据outputs定义组装返回结果

### 2. 任务依赖解析

TaskParser负责解析任务之间的依赖关系。关键步骤如下：

1. 解析任务属性中的表达式，识别对其他任务的引用
2. 基于引用关系构建依赖图
3. 计算任务的层级，用于调度执行

### 3. 并发执行

ConcurrentPipelineScheduler实现了基于CompletableFuture的并发执行模型。核心逻辑如下：

1. 对每个任务创建对应的CompletableFuture
2. 基于任务依赖关系组装CompletableFuture的依赖链
3. 使用CompletableFuture.allOf等待所有任务完成
4. 处理任务执行异常和超时

## 4. DSL详细说明

班马服务编排系统使用JSON格式的DSL定义工作流。DSL包含以下主要部分：

### 4.1 工作流属性

| 属性名      | 类型      | 描述                  | 是否必须 |
|------------|----------|----------------------|---------|
| name       | String   | 工作流名称              | 是      |
| description| String   | 工作流描述              | 否      |
| timeout    | Integer  | 整体超时时间（毫秒）      | 否      |
| failFast   | Boolean  | 是否快速失败            | 否      |
| tasks      | Array    | 任务列表               | 是      |
| outputs    | Object   | 输出定义               | 否      |
| retry      | Object   | 重试配置               | 否      |

### 4.2 任务属性

| 属性名           | 类型      | 描述                  | 是否必须 |
|-----------------|----------|----------------------|---------|
| alias           | String   | 任务别名（唯一标识）     | 是      |
| taskType        | String   | 任务类型               | 是      |
| url             | String   | 调用URL               | 否      |
| method          | String   | 调用方法               | 否      |
| remoteAppkey    | String   | 远程服务Appkey         | 否      |
| inputs          | Object   | 输入参数               | 否      |
| timeout         | Integer  | 任务超时时间（毫秒）     | 否      |
| ignoreException | Boolean  | 是否忽略异常           | 否      |
| switchExpression| String   | 条件表达式             | 否      |
| cacheOption     | Object   | 缓存配置               | 否      |

## 5. 表达式语法

班马服务编排系统支持在DSL中使用表达式进行动态计算，表达式主要用于以下场景：

1. 任务参数引用：使用 `$task.result.field` 引用其他任务的结果
2. 输入参数引用：使用 `$params.field` 引用输入参数
3. 条件判断：使用 `$task.result.field == value` 进行条件判断

表达式支持使用JSONPath语法访问结果中的字段，也支持Aviator表达式进行计算。


## 更多示例
请参考 `src/test/resources/testDslJsonFiles` 目录下的测试用例获取更多使用示例。 