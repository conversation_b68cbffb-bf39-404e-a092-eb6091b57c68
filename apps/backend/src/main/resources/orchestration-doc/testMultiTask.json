{
  "name": "CheckFetchMealCodeQuery",
  "description": "查询取餐码（OneService）",
  "tasks": [
    {
      "alias": "dateId",
      "taskType": "ThriftGeneric",
      "description": "查询数据就绪的日期",
      "url": "com.sankuai.meituan.banma.oneservice.thrift.iface.BmOneServiceThriftIface",
      "method": "fetchMetric",
      "remoteAppkey": "com.sankuai.banma.data.oneservice",
      "remoteServerPort": "9451",
      "timeout": 1000,
      "ignoreException": true,
      "cacheOption": {
        "expireAfterWriteSeconds": 1800,
        "refreshAfterWriteSeconds": 120,
        "maximumSize": 1
      },
      "inputs": {
        "fetchMetricInput": {
          "allDeliverMode": true,
          "metricOffsetList": [{
            "systemMetric": "'poi_date_qcm_white_list_tag_history'",
            "offsetType": 1
          }]
        }
      },
      "inputsExtra": {
        "fetchMetricInput": "com.sankuai.meituan.banma.oneservice.thrift.vo.FetchMetricInput"
      }
    },
    {
      "alias": "metricData",
      "taskType": "ThriftGeneric",
      "description": "查询OneService指定日期指标数据",
      "switch": "$dateId != nil && $dateId.code == 0 && $dateId.offsetResult != nil && count($dateId.offsetResult) > 0",
      "url": "com.sankuai.meituan.banma.oneservice.thrift.iface.BmOneServiceThriftIface",
      "method": "fetchMetric",
      "remoteAppkey": "com.sankuai.banma.data.oneservice",
      "remoteServerPort": "9451",
      "timeout": 1000,
      "inputs": {
        "fetchMetricInput": {
          "dimensionTypeList": [
            {"value": "'poi'"},
            {"value": "'date'"}
          ],
          "dimensionValueList": "let list = seq.list(); for poiId in $params.poiIds { seq.add(list, seq.list(seq.map('value', poiId), seq.map('value', $dateId.offsetResult[0]))) }; return list;",
          "metricNameList": [
            {"value": "'qcm_white_list_tag_history'"}
          ],
          "selectFields": [
            "'is_white'"
          ],
          "allDeliverMode": true
        }
      },
      "inputsExtra": {
        "fetchMetricInput": "com.sankuai.meituan.banma.oneservice.thrift.vo.FetchMetricInput"
      }
    }
  ],
  "outputs": {
    "type": "map",
    "switch": "$metricDataIsException == false && $metricData != nil && $metricData.code == 0 && $metricData.output != nil && count($metricData.output) == count($params.poiIds)",
    "variables": {
      "metricDataOutputList": "JSON.toObject($metricData.output)"
    },
    "transform": "for index,poiId in Collections.toStringList($params.poiIds) {
      Transform.toMap(poiId,
        'checkFetchMealCode', Object.getPathValue(metricDataOutputList, index + '.0.defaultValue')
      )
    }"
  }
}



