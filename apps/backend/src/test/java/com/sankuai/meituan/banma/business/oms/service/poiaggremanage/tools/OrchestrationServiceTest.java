package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.tools;

import com.sankuai.meituan.banma.service.orchestration.model.Workflow;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.io.File;

public class OrchestrationServiceTest {

    private OrchestrationService orchestrationService;

    @Before
    public void setUp() {
        orchestrationService = new OrchestrationService();
    }

    @Test
    public void testFormattedJson() throws Exception {
        // 读取包含复杂字符串（带换行）的 DSL 文件
        String dsl = FileUtils.readFileToString(new File("src/main/resources/orchestration-doc/testDeliveryCategory.json"), "UTF-8");
        System.out.println("原始 DSL:");
        System.out.println(dsl);
        // 解析为 Workflow 对象
        Workflow workflow = orchestrationService.parseDsl(dsl);
        // 使用新方法序列化
        String newFormatDSL = DSLUtil.toFormattedJson(workflow);
        System.out.println("\n新方法序列化结果:");
        System.out.println(newFormatDSL);
        // 使用 OrchestrationService 的 stringify 方法
        String serviceFormatDSL = orchestrationService.stringify(workflow);
        System.out.println("\nOrchestrationService.stringify 结果:");
        System.out.println(serviceFormatDSL);
        // 验证结果
        Assert.assertNotNull(newFormatDSL);
        Assert.assertNotNull(serviceFormatDSL);

        
        // 将结果写入文件，以便手动检查
        FileUtils.writeStringToFile(new File("target/newFormatDSL.json"), newFormatDSL, "UTF-8");
        FileUtils.writeStringToFile(new File("target/serviceFormatDSL.json"), serviceFormatDSL, "UTF-8");
    }
}