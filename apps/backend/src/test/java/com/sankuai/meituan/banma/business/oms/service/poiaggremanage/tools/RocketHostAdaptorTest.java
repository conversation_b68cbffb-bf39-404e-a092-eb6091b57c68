package com.sankuai.meituan.banma.business.oms.service.poiaggremanage.tools;

import com.sankuai.meituan.banma.business.oms.adaptor.RocketHostAdaptor;
import com.sankuai.meituan.banma.business.oms.common.constant.Env;
import com.sankuai.meituan.banma.business.oms.entity.poiaggremanage.tools.RocketHost;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * 测试类用于验证 getRocketHosts 方法的行为。
 */
public class RocketHostAdaptorTest {
    /**
     * 测试配置文件中存在有效的主机地址列表。
     */
    @Test
    public void testGetRocketHostsWithValidConfig() throws Throwable {
        // arrange

        // act
        List<RocketHost> rocketHosts = RocketHostAdaptor.getAppkeyHosts("com.sankuai.deliverybusiness.oms", Env.PROD.getCode());

        // assert
        System.out.println(rocketHosts);
        assertEquals(true, rocketHosts != null);
    }

}