# 履约平台技术部AI门户后端 (banma_business_oms Backend)

## 项目简介

这是履约平台技术部AI门户的后端部分，基于Spring Boot和MDP框架（美团内部框架）构建。该后端为前端应用提供API服务和业务逻辑处理。

## 技术栈

- Java
- Spring Boot
- MDP框架（美团内部框架）
- Maven

## 项目结构

```
apps/backend/
├── .mvn/                # Maven wrapper 目录
├── deploy/              # 部署相关文件
├── src/                 # 源代码目录
│   ├── main/            # 主要源代码
│   │   ├── java/        # Java 源代码
│   │   │   └── com/sankuai/meituan/banma/business/oms/
│   │   │       ├── controller/ # 控制器层
│   │   │       │   ├── demo/   # 示例模块控制器
│   │   │       │   └── user/   # 用户模块控制器
│   │   │       ├── service/    # 服务层
│   │   │       │   ├── demo/   # 示例模块服务
│   │   │       │   │   └── DemoService   # 示例服务
│   │   │       │   └── user/   # 用户模块服务
│   │   │       ├── adaptor/    # 适配器层（封装外部调用）
│   │   │       │   ├── demo/   # 示例模块适配器
│   │   │       │   └── user/   # 用户模块适配器
│   │   │       ├── mapper/     # 数据访问层
│   │   │       ├── model/      # 数据模型
│   │   │       │   ├── entity/ # 实体类
│   │   │       │   ├── dto/    # 数据传输对象
│   │   │       │   └── vo/     # 视图对象
│   │   │       ├── common/     # 通用工具类
│   │   │       │   ├── constants/ # 常量定义
│   │   │       │   ├── exception/ # 异常处理
│   │   │       │   └── utils/  # 工具类
│   │   │       └── config/     # 配置类
│   │   └── resources/  # 资源文件
│   └── test/           # 测试源代码
├── .gitignore           # Git忽略文件
├── .sdkmanrc            # SDKMAN配置文件
├── banma_business_oms.iml # IntelliJ IDEA 项目文件
├── manifest-offline.yaml # 离线清单文件
├── manifest.yaml        # 项目清单文件
├── mvnw                 # Maven wrapper 脚本
├── pom.xml              # Maven 项目对象模型文件
└── README.md            # 本文件
```

## 快速开始

### 前置条件

- JDK 11 或更高版本
- Maven 3.6 或更高版本

### 设置开发环境

1. 克隆仓库：

```bash
git clone ssh://*******************/bm/banma_business_oms.git
cd banma_business_oms/apps/backend
```

2. 安装依赖：

```bash
./mvnw clean install
```

### 运行应用

```bash
./mvnw spring-boot:run
```

应用将在默认端口（通常是8080）上启动。

## 配置

主要配置文件位于 `src/main/resources/application.properties` 或 `application.yml`。根据需要修改配置。

## API 文档

API 文档通常通过 Swagger 或 Spring Fox 自动生成。启动应用后，可以在以下地址访问API文档：

```
http://localhost:8080/swagger-ui.html
```

## 开发指南

### 架构设计

本项目采用多层架构设计：

1. **控制器层（Controller）**：处理HTTP请求和响应
2. **服务层（Service）**：实现业务逻辑
3. **适配器层（Adaptor）**：封装对外部系统、接口、服务的调用
4. **数据访问层（DAO）**：与数据库交互
5. **模型层（Model）**：包含数据实体、DTO和VO

### 模块化设计规范

项目采用模块化设计，每个功能模块应当遵循以下规范：

#### 目录结构

每个功能模块应有独立的目录结构，包含控制器、服务、适配器和数据访问层：

```
com.sankuai.meituan.banma.business.oms/
├── controller/
│   └── user/                         # 用户模块控制器目录
│       ├── UserController.java       # 用户主控制器
│       └── UserSettingsController.java # 用户设置控制器
├── service/
│   └── user/                         # 用户模块服务目录
│       ├── UserService.java          # 用户服务类（实现类，不要用接口+Impl模式）
├── adaptor/                          # 适配器层
│   └── user/                         # 用户模块适配器目录
│       ├── UserAPIAdaptor.java       # 用户外部API适配器类（实现类，不要用接口+Impl模式）
└── mapper/
    └── user/                         # 用户模块数据访问层
        ├── UserMapper.java           # 用户仓库接口
```

#### 适配器层（Adaptor）设计规范

适配器层负责隔离外部依赖，封装所有对外部系统、接口、服务的调用：

- **职责边界**：
  - 所有与外部系统的交互都应封装在适配器层中
  - 服务层只调用适配器接口，不直接依赖外部系统
  - 适配器实现外部调用的封装、异常处理和数据转换

- **命名规范**：
  - 类命名格式：`{模块名}{功能}Adaptor`，如`UserAPIAdaptor`

- **代码规范**：
  - 适配器应定义清晰的方法签名和异常处理策略
  - 适配器应处理所有外部调用的异常，并转换为系统内部可处理的异常
  - 适配器方法应当做好参数校验和结果转换
  - 适配器方法应该有适当的日志记录，特别是对外部系统调用的请求和响应

- **使用场景**：
  - 调用外部REST API
  - 访问外部微服务
  - 使用第三方SDK
  - 调用遗留系统接口
  - 访问非直接管理的数据源

#### API设计规范

- 模块API应有统一的URL前缀，如`/api/businessoms/user/`
- API命名应符合RESTful规范，使用资源名词而非动词
- 使用适当的HTTP方法：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 统一返回CommonResult封装的响应

#### 代码规范

- 类名使用PascalCase（如UserController）
- 方法名使用camelCase（如getUserList）
- 变量名使用camelCase（如userName）
- 常量使用UPPER_SNAKE_CASE（如MAX_USER_COUNT）
- 遵循单一职责原则，确保代码的内聚性
- 相关的功能应组织在同一个包下，保持目录结构清晰

### 添加新模块

当需要添加新的功能模块时，应按照以下步骤操作：

1. **创建模块目录结构**

   在controller、service、adaptor和dao目录下分别创建新模块的目录：

   ```bash
   mkdir -p src/main/java/com/sankuai/meituan/banma/business/oms/controller/newmodule
   mkdir -p src/main/java/com/sankuai/meituan/banma/business/oms/service/newmodule/
   mkdir -p src/main/java/com/sankuai/meituan/banma/business/oms/adaptor/newmodule/
   mkdir -p src/main/java/com/sankuai/meituan/banma/business/oms/mapper/
   ```

2. **创建控制器类**

   在controller目录下创建控制器类：

   ```java
   package com.sankuai.meituan.banma.business.oms.controller.newmodule;
   
   import org.springframework.web.bind.annotation.*;
   import com.sankuai.meituan.banma.business.oms.common.result.CommonResult;
   
   @RestController
   @RequestMapping("/api/businessoms/newmodule")
   public class NewModuleController {
       
       @GetMapping("/list")
       public CommonResult list() {
           // 实现列表查询逻辑
           return CommonResult.success();
       }
       
       @PostMapping("/create")
       public CommonResult create(@RequestBody NewModuleDTO dto) {
           // 实现创建逻辑
           return CommonResult.success();
       }
   }
   ```

3. **创建服务接口及实现**

   在service目录下创建服务接口：

   ```java
   package com.sankuai.meituan.banma.business.oms.service.newmodule;

   import org.springframework.stereotype.Service;
   import com.sankuai.meituan.banma.business.oms.service.newmodule.NewModuleService;
   import com.sankuai.meituan.banma.business.oms.adaptor.newmodule.NewModuleAdaptor;
   
   public interface NewModuleService {

       @Resource
       private final NewModuleAdaptor newModuleAdaptor;
       
       // 通过适配器调用外部接口
       // 示例：获取外部数据
       public ExternalData getExternalData(String id) {
           return newModuleAdaptor.fetchExternalData(id);
       }
   }
   ```

4. **创建适配器接口及实现**

   在adaptor目录下创建适配器接口：

   ```java
   package com.sankuai.meituan.banma.business.oms.adaptor.newmodule;

   import org.springframework.stereotype.Component;
   import org.springframework.web.client.RestTemplate;
   import com.sankuai.meituan.banma.business.oms.adaptor.newmodule.NewModuleAdaptor;
   import lombok.extern.slf4j.Slf4j;
   
   public interface NewModuleAdaptor {

       private final RestTemplate restTemplate;

       @Autowired
       public NewModuleAdaptor(RestTemplate restTemplate) {
           this.restTemplate = restTemplate;
       }
       /**
        * 从外部系统获取数据
        * @param id 数据ID
        * @return 外部数据对象
        * @throws ExternalSystemException 外部系统异常
        */
       public ExternalData fetchExternalData(String id) {
           try {
               log.info("调用外部系统获取数据，id: {}", id);
               // 调用外部接口示例
               ExternalResponse response = restTemplate.getForObject(
                   "https://external-api.example.com/data/{id}", 
                   ExternalResponse.class, 
                   id
               );
               
               // 处理响应，转换为内部数据格式
               if (response != null && response.isSuccess()) {
                   log.info("外部系统调用成功");
                   return convertToExternalData(response.getData());
               } else {
                   log.error("外部系统调用失败: {}", response);
                   throw new ExternalSystemException("外部系统返回错误");
               }
           } catch (Exception e) {
               log.error("调用外部系统异常", e);
               throw new ExternalSystemException("调用外部系统异常", e);
           }
       }
       
       private ExternalData convertToExternalData(ExternalApiData data) {
           // 数据格式转换
           return new ExternalData();
       }
   }
   ```

5. **创建数据访问层**

   在mapper目录下创建数据访问接口：

   ```java
   package com.sankuai.meituan.banma.business.oms.mapper.newmodule;
   
   public interface NewModuleRepository {
       // 定义数据访问方法
   }
   ```

6. **添加模型类**

   根据业务需求，在model目录下创建相应的实体类、DTO和VO。

### 适配器层使用最佳实践

1. **职责分离**：服务层专注于业务逻辑，适配器层专注于外部系统交互

2. **异常处理**：适配器层应捕获所有外部系统异常，并转换为业务异常
   ```java
   try {
       // 调用外部系统
   } catch (ExternalApiException e) {
       throw new BusinessException("业务错误信息", e);
   } catch (Exception e) {
       throw new SystemException("系统错误信息", e);
   }
   ```

3. **超时与重试**：适配器应设置合理的超时时间，并实现必要的重试逻辑
   ```java
   @Retryable(
       value = {ConnectException.class, TimeoutException.class},
       maxAttempts = 3,
       backoff = @Backoff(delay = 1000, multiplier = 2)
   )
   public ExternalData fetchWithRetry(String id) {
       // 实现外部调用
   }
   ```

5. **日志记录**：详细记录外部调用的请求、响应和性能指标
   ```java
   @Override
   public ExternalData fetchExternalData(String id) {
       long startTime = System.currentTimeMillis();
       try {
           log.info("开始调用外部系统, id: {}", id);
           ExternalData result = doFetchExternalData(id);
           log.info("外部系统调用成功, 耗时: {}ms", System.currentTimeMillis() - startTime);
           return result;
       } catch (Exception e) {
           log.error("外部系统调用失败, 耗时: {}ms, 异常: {}", 
                   System.currentTimeMillis() - startTime, e.getMessage(), e);
           throw e;
       }
   }
   ```

## 测试

运行单元测试：

```bash
./mvnw test
```

## 部署

部署相关的脚本和配置文件位于 `deploy/` 目录。请参考该目录下的文档进行部署。

## 贡献指南

1. 遵循项目的代码规范和提交规范。
2. 在提交代码前，确保所有测试通过。
3. 使用有意义的提交信息。

## 联系方式

如有任何问题或建议，请联系项目维护者。