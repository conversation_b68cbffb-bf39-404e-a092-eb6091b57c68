{"name": "oms", "private": true, "workspaces": ["packages/*", "apps/*"], "scripts": {"frontend:install": "cd apps/frontend && pnpm install", "frontend:dev": "cd apps/frontend && AWP_DEPLOY_ENV=dev pnpm dev --host oms.banma.dev.sankuai.com", "frontend:build": "cd apps/frontend && pnpm build", "frontend": "npm run frontend:dev", "backend:compile": "cd apps/backend && mvn clean compile dependency:copy-dependencies", "backend:start": "cd apps/backend && java -Xms512m -Xmx1024m -Dmdp.sso.enabled=false -DSPRING_PROFILES_ACTIVE=dev -cp \"target/classes:target/dependency/*:src/main/resources:src/main/profiles/dev\" com.sankuai.meituan.banma.business.oms.ApplicationLoader", "backend": "npm run backend:compile && npm run backend:start", "install:all": "pnpm install && npm run frontend:install", "dev": "npm run backend && npm run frontend", "check-api": "node scripts/check-api-consistency.js", "docs": "typedoc --out docs src", "preinstall": "npx only-allow pnpm", "prebuild:frontend": "pnpm check-api"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.5.2", "devDependencies": {"typedoc": "^0.24.0", "typescript": "^5.0.0"}, "dependencies": {"echarts": "^5.6.0"}}